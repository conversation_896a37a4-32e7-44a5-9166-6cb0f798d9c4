/**
 * MongoDB Schema Definitions
 *
 * This file defines the schema structure for all MongoDB collections
 * to ensure consistency across the application.
 */

// Animal Schema
const animalSchema = {
  tagNumber: { type: String, required: true, unique: true }, // Format: [SPECIES_CODE]-[YEAR]-[SEQUENCE]
  name: { type: String, required: true }, // South African names (Sotho, Tswana, Afrikaans)
  type: { type: String, required: true }, // General type (e.g., Cattle, Sheep)
  species: { type: String }, // Same as type, for backward compatibility
  breed: { type: String, required: true }, // Specific breed
  gender: { type: String, required: true }, // Male or Female
  birthDate: { type: String }, // ISO date format
  weight: { type: Number }, // In kg
  status: { type: String, required: true, default: 'Active' }, // Active, Sold, Deceased, Quarantined, Breeding, Retired
  healthStatus: { type: String, required: true, default: 'healthy' }, // healthy, sick, injured, pregnant
  location: { type: String, required: true }, // Farm location
  purchaseDate: { type: String }, // ISO date format
  purchasePrice: { type: Number }, // In ZAR (Rands)
  notes: { type: String }, // Additional information
  parentId: { type: String }, // Reference to parent animal ID (foreign key)
  parentTagNumber: { type: String }, // Reference to parent animal tag (for easier querying)
  sireId: { type: String }, // Father animal ID (foreign key)
  damId: { type: String }, // Mother animal ID (foreign key)
  rfidTag: { type: String }, // RFID tag for tracking
  imageUrl: { type: String }, // Path to animal image
  // Asset Management fields
  breedingCount: { type: Number, default: 0 }, // Number of times the animal has been bred
  retirementDate: { type: String }, // ISO date format - when the animal was retired
  retirementReason: { type: String }, // Age, Breeding Limit, Health Issues, Performance, Other
  retirementNotes: { type: String }, // Additional notes about retirement
  currentValue: { type: Number, default: 0 }, // Current estimated value in ZAR
  depreciationRate: { type: Number, default: 0 }, // Annual depreciation rate (%)
  maintenanceCosts: { type: Number, default: 0 }, // Total maintenance costs in ZAR
  revenueGenerated: { type: Number, default: 0 }, // Total revenue generated in ZAR
  roi: { type: Number, default: 0 }, // Return on investment (%)
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Health Record Schema
const healthRecordSchema = {
  animalId: { type: String, required: true }, // Reference to animal ID
  animalTagNumber: { type: String, required: true }, // Reference to animal tag number
  animalName: { type: String, required: true }, // Animal name for easier querying
  recordType: { type: String, required: true }, // Vaccination, Treatment, Examination, Surgery
  description: { type: String, required: true }, // Description of health record
  date: { type: String, required: true }, // ISO date format
  performedBy: { type: String, required: true }, // Person who performed the health record
  status: { type: String, required: true, default: 'completed' }, // completed, monitoring, scheduled
  notes: { type: String }, // Additional notes
  followUpDate: { type: String }, // ISO date format for follow-up
  medications: { type: Array }, // List of medications used
  cost: { type: Number }, // Cost in ZAR
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Breeding Record Schema
const breedingRecordSchema = {
  femaleId: { type: String, required: true }, // Reference to female animal ID
  femaleTagNumber: { type: String, required: true }, // Reference to female animal tag number
  femaleName: { type: String, required: true }, // Female animal name for easier querying
  maleId: { type: String, required: true }, // Reference to male animal ID
  maleTagNumber: { type: String, required: true }, // Reference to male animal tag number
  maleName: { type: String, required: true }, // Male animal name for easier querying
  date: { type: String, required: true }, // ISO date format of breeding
  status: { type: String, required: true, default: 'pending' }, // pending, confirmed, successful, unsuccessful
  expectedDueDate: { type: String }, // ISO date format of expected due date
  notes: { type: String }, // Additional notes
  method: { type: String }, // Natural, Artificial Insemination
  technician: { type: String }, // Person who performed the breeding
  cost: { type: Number }, // Cost in ZAR
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Birth Record Schema
const birthRecordSchema = {
  breedingRecordId: { type: String }, // Reference to breeding record ID
  motherId: { type: String, required: true }, // Reference to mother animal ID
  motherTagNumber: { type: String, required: true }, // Reference to mother animal tag number
  motherName: { type: String, required: true }, // Mother animal name for easier querying
  fatherId: { type: String }, // Reference to father animal ID
  fatherTagNumber: { type: String }, // Reference to father animal tag number
  fatherName: { type: String }, // Father animal name for easier querying
  birthDate: { type: String, required: true }, // ISO date format
  location: { type: String, required: true }, // Location of birth
  numberOfOffspring: { type: Number, required: true, default: 1 }, // Number of offspring
  offspringDetails: { type: Array }, // Array of offspring details (IDs, tag numbers, etc.)
  complications: { type: String }, // Any complications during birth
  assistanceProvided: { type: Boolean, default: false }, // Whether assistance was provided
  notes: { type: String }, // Additional notes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Feeding Record Schema
const feedingRecordSchema = {
  animalId: { type: String }, // Reference to animal ID (can be null for group feeding)
  animalTagNumber: { type: String }, // Reference to animal tag number
  animalName: { type: String }, // Animal name for easier querying
  groupId: { type: String }, // Reference to group ID (can be null for individual feeding)
  groupName: { type: String }, // Group name for easier querying
  feedType: { type: String, required: true }, // Type of feed
  quantity: { type: Number, required: true }, // Quantity of feed
  unit: { type: String, required: true }, // Unit of measurement (kg, g, etc.)
  feedingDate: { type: String, required: true }, // ISO date format
  feedingTime: { type: String }, // Time of feeding
  location: { type: String }, // Location of feeding
  cost: { type: Number }, // Cost in ZAR
  notes: { type: String }, // Additional notes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Feed Inventory Schema
const feedInventorySchema = {
  name: { type: String, required: true }, // Name of feed
  type: { type: String, required: true }, // Type of feed (hay, grain, supplement, etc.)
  quantity: { type: Number, required: true }, // Quantity of feed
  unit: { type: String, required: true }, // Unit of measurement (kg, g, etc.)
  location: { type: String, required: true }, // Storage location
  purchaseDate: { type: String }, // ISO date format
  purchasePrice: { type: Number }, // Cost in ZAR
  supplier: { type: String }, // Supplier name
  expiryDate: { type: String }, // ISO date format
  nutritionalInfo: { type: Object }, // Nutritional information
  notes: { type: String }, // Additional notes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Commercial/Financial Record Schema
const financialRecordSchema = {
  recordType: { type: String, required: true }, // Income, Expense, Investment
  category: { type: String, required: true }, // Feed, Veterinary, Equipment, Sales, etc.
  amount: { type: Number, required: true }, // Amount in ZAR
  date: { type: String, required: true }, // ISO date format
  description: { type: String, required: true }, // Description of transaction
  paymentMethod: { type: String }, // Cash, Bank Transfer, Credit Card, etc.
  reference: { type: String }, // Reference number or invoice number
  relatedEntityType: { type: String }, // Animal, Feed, Equipment, etc.
  relatedEntityId: { type: String }, // ID of related entity
  relatedEntityName: { type: String }, // Name of related entity for easier querying
  taxDeductible: { type: Boolean, default: false }, // Whether the expense is tax deductible
  receipt: { type: String }, // Path to receipt image or document
  notes: { type: String }, // Additional notes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Inventory Schema
const inventorySchema = {
  category: { type: String, required: true }, // Equipment, Supplies, Medicine, etc.
  name: { type: String, required: true }, // Name of item
  description: { type: String }, // Description of item
  quantity: { type: Number, required: true }, // Quantity of item
  unit: { type: String }, // Unit of measurement (each, box, etc.)
  location: { type: String, required: true }, // Storage location
  purchaseDate: { type: String }, // ISO date format
  purchasePrice: { type: Number }, // Cost in ZAR
  supplier: { type: String }, // Supplier name
  condition: { type: String, default: 'Good' }, // New, Good, Fair, Poor
  lastMaintenanceDate: { type: String }, // ISO date format
  nextMaintenanceDate: { type: String }, // ISO date format
  expirationDate: { type: String }, // ISO date format
  notes: { type: String }, // Additional notes
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// User Schema
const userSchema = {
  username: { type: String, required: true, unique: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  role: { type: String, required: true, default: 'viewer' }, // admin, manager, staff, veterinarian, viewer
  permissions: { type: Array }, // Array of permission objects
  isActive: { type: Boolean, default: true },
  lastLogin: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Export all schemas
module.exports = {
  animalSchema,
  healthRecordSchema,
  breedingRecordSchema,
  birthRecordSchema,
  feedingRecordSchema,
  feedInventorySchema,
  financialRecordSchema,
  inventorySchema,
  userSchema
};
