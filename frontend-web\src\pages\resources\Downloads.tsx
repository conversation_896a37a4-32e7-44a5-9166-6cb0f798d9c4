import React, { useState } from 'react';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab, InputAdornment, Link, Paper, LinearProgress, Tooltip } from '@mui/material';
import {
  Add,
  GetApp,
  Search,
  FilterList,
  Visibility,
  Edit,
  Delete,
  Download,
  CalendarToday,
  LocalOffer,
  Description,
  InsertDriveFile,
  CloudDownload,
  Apps,
  DataObject,
  Code,
  Storage,
  BarChart,
  PictureAsPdf,
  Image
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { useResourcesDataContext } from '../../contexts/DataContext';
// Define DownloadResource type
interface DownloadResource {
  id: string;
  title: string;
  category: string;
  description: string;
  version?: string;
  fileUrl?: string;
  fileSize?: string;
  fileType?: string;
  requirements?: string;
  tags: string[];
  lastUpdated: string;
  downloadCount: number;
  viewCount: number;
  thumbnailUrl?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Downloads: React.FC = () => {
  const theme = useTheme();
  const { downloadResources, loading, error } = useResourcesDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedResource, setSelectedResource] = useState<DownloadResource | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [downloadProgress, setDownloadProgress] = useState<Record<string, number>>({});

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    category: '',
    description: '',
    version: '',
    fileUrl: '',
    fileSize: '',
    fileType: '',
    requirements: '',
    tags: ['']
  });

  // Get unique categories from download resources
  const categories = downloadResources ? Array.from(new Set(downloadResources.map((resource: DownloadResource) => resource.category))) : [];

  // Filter resources based on category and search term
  const filteredResources = downloadResources?.filter((resource: DownloadResource) => {
    const matchesCategory = filterCategory === 'all' || resource.category === filterCategory;
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesCategory && matchesSearch;
  }) || [];

  // Calculate statistics
  const downloadStats = {
    total: downloadResources?.length || 0,
    categories: categories.length,
    totalDownloads: downloadResources?.reduce((sum: number, resource: DownloadResource) => sum + resource.downloadCount, 0) || 0,
    mostPopular: downloadResources?.length
      ? downloadResources.reduce((prev: DownloadResource, current: DownloadResource) => (prev.downloadCount > current.downloadCount) ? prev : current).title
      : 'N/A',
    latestUpdate: downloadResources?.length
      ? downloadResources.reduce((prev: DownloadResource, current: DownloadResource) =>
          new Date(prev.lastUpdated) > new Date(current.lastUpdated) ? prev : current
        ).lastUpdated
      : 'N/A'
  };

  // Handle dialog open/close
  const handleOpenDialog = (resource?: DownloadResource) => {
    if (resource) {
      setSelectedResource(resource);
      setFormData({
        id: resource.id,
        title: resource.title,
        category: resource.category,
        description: resource.description,
        version: resource.version || '',
        fileUrl: resource.fileUrl || '',
        fileSize: resource.fileSize || '',
        fileType: resource.fileType || '',
        requirements: resource.requirements || '',
        tags: resource.tags
      });
    } else {
      setSelectedResource(null);
      setFormData({
        id: `DL-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        title: '',
        category: '',
        description: '',
        version: '',
        fileUrl: '',
        fileSize: '',
        fileType: '',
        requirements: '',
        tags: ['']
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedResource(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (e: any) => {
    setFormData(prev => ({ ...prev, category: e.target.value }));
  };

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags];
    newTags[index] = value;
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  const handleAddTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const handleRemoveTag = (index: number) => {
    const newTags = [...formData.tags];
    newTags.splice(index, 1);
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  // Handle save resource
  const handleSaveResource = () => {
    // In a real app, this would call an API to save the resource
    console.log('Saving download resource:', formData);

    // For now, just close the dialog
    handleCloseDialog();
  };

  // Handle delete resource
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the resource
    console.log('Deleting download resource:', id);
  };

  // Handle view resource
  const handleViewResource = (resource: DownloadResource) => {
    // In a real app, this would open the resource or redirect to a detailed view
    console.log('Viewing download resource:', resource);

    // For now, just open the dialog in view mode
    handleOpenDialog(resource);
  };

  // Handle download resource
  const handleDownloadResource = (resource: DownloadResource) => {
    // In a real app, this would download the resource file
    console.log('Downloading resource:', resource);

    // Simulate download progress
    setDownloadProgress(prev => ({ ...prev, [resource.id]: 0 }));

    const interval = setInterval(() => {
      setDownloadProgress(prev => {
        const currentProgress = prev[resource.id] || 0;
        if (currentProgress >= 100) {
          clearInterval(interval);
          return prev;
        }
        return { ...prev, [resource.id]: currentProgress + 10 };
      });
    }, 300);
  };

  // Get file type icon
  const getFileTypeIcon = (fileType: string) => {
    switch (fileType?.toLowerCase()) {
      case 'pdf':
        return <PictureAsPdf />;
      case 'doc':
      case 'docx':
        return <Description />;
      case 'xls':
      case 'xlsx':
        return <BarChart />;
      case 'zip':
      case 'rar':
        return <Storage />;
      case 'exe':
      case 'msi':
        return <Apps />;
      case 'jpg':
      case 'png':
      case 'gif':
        return <Image />;
      case 'csv':
      case 'json':
        return <DataObject />;
      default:
        return <InsertDriveFile />;
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category?.toLowerCase()) {
      case 'software':
        return <Apps />;
      case 'data':
        return <Storage />;
      case 'templates':
        return <Description />;
      case 'tools':
        return <Code />;
      case 'reports':
        return <BarChart />;
      default:
        return <CloudDownload />;
    }
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    {
      id: 'category',
      label: 'Category',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          icon={getCategoryIcon(value)}
          label={value}
          size="small"
          color="primary"
          variant="outlined"
        />
      )
    },
    { id: 'version', label: 'Version', minWidth: 80 },
    { id: 'fileType', label: 'Type', minWidth: 80 },
    { id: 'fileSize', label: 'Size', minWidth: 80 },
    { id: 'lastUpdated', label: 'Last Updated', minWidth: 120 },
    {
      id: 'downloadCount',
      label: 'Downloads',
      minWidth: 80,
      align: 'right'
    }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Downloads"
        subtitle="Access software, templates, data files, and tools for livestock management"
        module="resources"
        submodule="downloads"
        actionLabel="Add Download"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Downloads"
                  subtitle={downloadStats.total.toString()}
                  module="resources"
                  uniqueId="total-downloads"
                  icon={<CloudDownload />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Categories"
                  subtitle={downloadStats.categories.toString()}
                  module="resources"
                  uniqueId="download-categories"
                  icon={<Apps />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Most Popular"
                  subtitle={downloadStats.mostPopular}
                  module="resources"
                  uniqueId="most-popular-download"
                  icon={<GetApp />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Downloads Count"
                  subtitle={downloadStats.totalDownloads.toString()}
                  module="resources"
                  uniqueId="total-download-count"
                  icon={<Download />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Featured Downloads */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Featured Downloads
          </Typography>
          <Grid container spacing={3} mb={4}>
            {downloadResources?.slice(0, 3).map((resource: DownloadResource) => (
              <Grid item xs={12} md={4} key={resource.id}>
                <motion.div variants={itemVariants}>
                  <Paper
                    elevation={3}
                    sx={{
                      p: 3,
                      borderRadius: 2,
                      background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.dark, 0.1)})`,
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          mr: 2,
                          width: 56,
                          height: 56
                        }}
                      >
                        {getFileTypeIcon(resource.fileType || 'unknown')}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="bold">
                          {resource.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {resource.category} • {resource.fileSize}
                        </Typography>
                      </Box>
                    </Box>

                    <Typography variant="body2" sx={{ mb: 2, flexGrow: 1 }}>
                      {resource.description}
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        {resource.downloadCount} downloads
                      </Typography>
                      <CustomButton
                        variant="contained"
                        color="primary"
                        startIcon={<Download />}
                        onClick={() => handleDownloadResource(resource)}
                        disabled={downloadProgress[resource.id] !== undefined && downloadProgress[resource.id] < 100}
                      >
                        Download
                      </CustomButton>
                    </Box>

                    {downloadProgress[resource.id] !== undefined && downloadProgress[resource.id] < 100 && (
                      <LinearProgress
                        variant="determinate"
                        value={downloadProgress[resource.id]}
                        sx={{ mt: 2 }}
                      />
                    )}
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200, mr: 2 }}>
              <InputLabel id="category-filter-label">Category</InputLabel>
              <Select
                labelId="category-filter-label"
                id="category-filter"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                label="Category"
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem component="li" key={category as React.Key} value={category as string}>{String(category)}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              placeholder="Search downloads..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Tabs
            value={viewMode === 'grid' ? 0 : 1}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : 'table')}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Grid View" />
            <Tab label="Table View" />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              {filteredResources.map((resource: DownloadResource) => (
                <Grid item xs={12} sm={6} md={4} key={resource.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      position: 'relative',
                      overflow: 'hidden',
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <Box
                      sx={{
                        height: 8,
                        width: '100%',
                        background: resource.category === 'Software'
                          ? theme.palette.primary.main
                          : resource.category === 'Data'
                            ? theme.palette.info.main
                            : resource.category === 'Templates'
                              ? theme.palette.success.main
                              : theme.palette.warning.main
                      }}
                    />
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              mr: 1
                            }}
                          >
                            {getFileTypeIcon(resource.fileType || 'unknown')}
                          </Avatar>
                          <Typography variant="h6" component="h2" fontWeight="bold">
                            {resource.title}
                          </Typography>
                        </Box>
                        <Chip
                          icon={getCategoryIcon(resource.category)}
                          label={resource.category}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          Last Updated: {resource.lastUpdated}
                        </Box>
                        {resource.version && (
                          <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Description fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            Version: {resource.version}
                          </Box>
                        )}
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        {resource.description}
                      </Typography>

                      {resource.requirements && (
                        <Box sx={{ mb: 1.5 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Requirements:
                          </Typography>
                          <Typography variant="body2">
                            {resource.requirements}
                          </Typography>
                        </Box>
                      )}

                      <Box sx={{ mb: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Tags:
                          </Typography>
                        </Box>
                        <Box>
                          {resource.tags.map((tag: string, index: number) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Downloads:</strong> {resource.downloadCount}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Size:</strong> {resource.fileSize}
                        </Typography>
                      </Box>
                    </CardContent>

                    <Box sx={{ p: 2, pt: 0 }}>
                      {downloadProgress[resource.id] !== undefined && downloadProgress[resource.id] < 100 ? (
                        <Box sx={{ width: '100%' }}>
                          <LinearProgress
                            variant="determinate"
                            value={downloadProgress[resource.id]}
                            sx={{ mb: 1 }}
                          />
                          <Typography variant="body2" align="center">
                            Downloading... {downloadProgress[resource.id]}%
                          </Typography>
                        </Box>
                      ) : (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <CustomButton
                            variant="outlined"
                            size="small"
                            startIcon={<Visibility />}
                            onClick={() => handleViewResource(resource)}
                          >
                            Details
                          </CustomButton>
                          <CustomButton
                            variant="contained"
                            color="primary"
                            size="small"
                            startIcon={<Download />}
                            onClick={() => handleDownloadResource(resource)}
                          >
                            Download
                          </CustomButton>
                        </Box>
                      )}
                    </Box>
                  </Card>
                </Grid>
              ))}

              {filteredResources.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No downloads found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new download
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                    >
                      Add Download
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EnhancedDataTable
              title="Downloads"
              subtitle="Access software, templates, data files, and tools"
              columns={columns as any}
              data={filteredResources}
              keyField="id"
              loading={loading}
              onView={(row: any) => handleViewResource(row)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['title', 'description', 'tags']}
              emptyMessage="No downloads found"
              accentColor={theme.palette.primary.main}
              actions={[
                {
                  icon: <Download />,
                  title: 'Download',
                  onClick: (row: any) => handleDownloadResource(row)
                }
              ]}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Download Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedResource ? 'Edit Download' : 'Add New Download'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Download ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  labelId="category-label"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  {categories.map((category) => (
                    <MenuItem component="li" key={category as React.Key} value={category as string}>{String(category)}</MenuItem>
                  ))}
                  <MenuItem value="Software">Software</MenuItem>
                  <MenuItem value="Data">Data</MenuItem>
                  <MenuItem value="Templates">Templates</MenuItem>
                  <MenuItem value="Tools">Tools</MenuItem>
                  <MenuItem value="Reports">Reports</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter download title"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Version"
                name="version"
                value={formData.version}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., 1.0, 2.3.1"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="File Type"
                name="fileType"
                value={formData.fileType}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., PDF, EXE, ZIP"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="File Size"
                name="fileSize"
                value={formData.fileSize}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., 2.5 MB"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                placeholder="Enter a detailed description of the download"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Requirements"
                name="requirements"
                value={formData.requirements}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={2}
                placeholder="Enter system requirements or prerequisites"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="File URL"
                name="fileUrl"
                value={formData.fileUrl}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter URL to the downloadable file"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              {formData.tags.map((tag, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={tag}
                    onChange={(e) => handleTagChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Tag ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveTag(index)}
                    disabled={formData.tags.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddTag}
                size="small"
                sx={{ mt: 1 }}
              >
                Add Tag
              </CustomButton>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSaveResource}
            variant="contained"
            color="primary"
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Downloads;
