/**
 * Test script for the notification system
 * This script tests sending notifications via SMS, voice call, and WhatsApp
 */

const notificationService = require('../services/notificationService');

/**
 * Test sending an SMS
 * @param {string} phoneNumber - Phone number to send to
 * @returns {Promise<object>} - Result of the operation
 */
async function testSMS(phoneNumber) {
  console.log(`\n=== Testing SMS to ${phoneNumber} ===`);
  
  const message = `AMPD Livestock SMS Test: This is a test message sent at ${new Date().toLocaleTimeString()}`;
  
  try {
    const result = await notificationService.sendSMS(phoneNumber, message, 'medium');
    console.log('SMS Result:', result);
    return result;
  } catch (error) {
    console.error('Error testing SMS:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test making a voice call
 * @param {string} phoneNumber - Phone number to call
 * @returns {Promise<object>} - Result of the operation
 */
async function testVoiceCall(phoneNumber) {
  console.log(`\n=== Testing Voice Call to ${phoneNumber} ===`);
  
  const message = `This is a test voice call from AMPD Livestock. The current time is ${new Date().toLocaleTimeString()}. This is only a test.`;
  
  try {
    const result = await notificationService.makeVoiceCall(phoneNumber, message, 'high');
    console.log('Voice Call Result:', result);
    return result;
  } catch (error) {
    console.error('Error testing voice call:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test sending a WhatsApp message
 * @param {string} phoneNumber - Phone number to send to
 * @returns {Promise<object>} - Result of the operation
 */
async function testWhatsApp(phoneNumber) {
  console.log(`\n=== Testing WhatsApp to ${phoneNumber} ===`);
  
  const message = `AMPD Livestock WhatsApp Test: This is a test message sent at ${new Date().toLocaleTimeString()}`;
  
  try {
    const result = await notificationService.sendWhatsApp(phoneNumber, message, 'medium');
    console.log('WhatsApp Result:', result);
    return result;
  } catch (error) {
    console.error('Error testing WhatsApp:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Test sending a birth prediction alert
 * @param {string} phoneNumber - Phone number to send to
 * @returns {Promise<object>} - Result of the operation
 */
async function testBirthAlert(phoneNumber) {
  console.log(`\n=== Testing Birth Alert to ${phoneNumber} ===`);
  
  const params = {
    animalId: 'CTL001',
    animalName: 'Bella',
    daysRemaining: 1,
    recipient: {
      name: 'Farm Manager',
      phone: phoneNumber,
      email: '<EMAIL>'
    },
    notificationTypes: ['sms', 'voice', 'whatsapp'],
    priority: 'critical'
  };
  
  try {
    const results = await notificationService.sendBirthPredictionAlert(params);
    console.log('Birth Alert Results:', results);
    return results;
  } catch (error) {
    console.error('Error testing birth alert:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Main function
 */
async function main() {
  // Get phone number from command line arguments
  const phoneNumber = process.argv[2] || '0794484159';
  
  console.log('=== AMPD Livestock Notification System Test ===');
  console.log(`Testing with phone number: ${phoneNumber}`);
  console.log('Current time:', new Date().toLocaleString());
  
  try {
    // Test SMS
    await testSMS(phoneNumber);
    
    // Test Voice Call
    await testVoiceCall(phoneNumber);
    
    // Test WhatsApp
    await testWhatsApp(phoneNumber);
    
    // Test Birth Alert
    await testBirthAlert(phoneNumber);
    
    console.log('\n=== All tests completed ===');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

// Run the main function
main().catch(console.error);
