exports.mockPastures = [
  {
    id: 1,
    name: 'North Pasture',
    size: 25,
    unit: 'acres',
    location: 'North section of property',
    soilType: 'Loam',
    forage: ['Orchard Grass', 'White Clover', 'Ryegrass'],
    waterSource: ['Stream', 'Water trough'],
    fencingType: 'Electric',
    status: 'Active',
    currentAnimals: [1, 4, 5],
    notes: 'Good shade coverage on west side'
  },
  {
    id: 2,
    name: 'South Field',
    size: 30,
    unit: 'acres',
    location: 'South section of property',
    soilType: 'Clay loam',
    forage: ['Alfalfa', 'Timothy', 'Bluegrass'],
    waterSource: ['Pond'],
    fencingType: 'Woven wire',
    status: 'Resting',
    currentAnimals: [],
    notes: 'Resting until May 1st for regrowth'
  },
  {
    id: 3,
    name: 'East Field',
    size: 20,
    unit: 'acres',
    location: 'East section of property',
    soilType: 'Sandy loam',
    forage: ['Fescue', 'Red Clover'],
    waterSource: ['Water trough'],
    fencingType: 'Barbed wire',
    status: 'Active',
    currentAnimals: [6, 7, 14],
    notes: 'Sheep rotation pasture'
  },
  {
    id: 4,
    name: 'West Paddock',
    size: 15,
    unit: 'acres',
    location: 'West section of property',
    soilType: 'Loam',
    forage: ['Mixed grasses', 'Brush'],
    waterSource: ['Stream', 'Water trough'],
    fencingType: 'Goat fencing',
    status: 'Active',
    currentAnimals: [8, 9],
    notes: 'Ideal for browsing goats'
  },
  {
    id: 5,
    name: 'Hay Field',
    size: 40,
    unit: 'acres',
    location: 'Northwest corner of property',
    soilType: 'Silt loam',
    forage: ['Timothy', 'Alfalfa', 'Orchard Grass'],
    waterSource: [],
    fencingType: 'None',
    status: 'Reserved',
    currentAnimals: [],
    notes: 'Reserved for hay production'
  }
];

exports.mockRotationPlans = [
  {
    id: 1,
    name: 'Spring-Summer Cattle Rotation',
    startDate: '2023-04-01',
    endDate: '2023-09-30',
    pastures: [1, 2],
    animalGroups: ['Dairy cows', 'Heifers'],
    rotationSchedule: [
      {
        pastureId: 1,
        startDate: '2023-04-01',
        endDate: '2023-05-15',
        restPeriod: '30 days'
      },
      {
        pastureId: 2,
        startDate: '2023-05-16',
        endDate: '2023-06-30',
        restPeriod: '30 days'
      },
      {
        pastureId: 1,
        startDate: '2023-07-01',
        endDate: '2023-08-15',
        restPeriod: '30 days'
      },
      {
        pastureId: 2,
        startDate: '2023-08-16',
        endDate: '2023-09-30',
        restPeriod: '30 days'
      }
    ],
    notes: 'Adjust rotation based on forage availability and weather conditions'
  },
  {
    id: 2,
    name: 'Sheep Grazing Plan',
    startDate: '2023-04-15',
    endDate: '2023-10-15',
    pastures: [3, 4],
    animalGroups: ['Ewes with lambs'],
    rotationSchedule: [
      {
        pastureId: 3,
        startDate: '2023-04-15',
        endDate: '2023-05-31',
        restPeriod: '21 days'
      },
      {
        pastureId: 4,
        startDate: '2023-06-01',
        endDate: '2023-07-15',
        restPeriod: '21 days'
      },
      {
        pastureId: 3,
        startDate: '2023-07-16',
        endDate: '2023-08-31',
        restPeriod: '21 days'
      },
      {
        pastureId: 4,
        startDate: '2023-09-01',
        endDate: '2023-10-15',
        restPeriod: '21 days'
      }
    ],
    notes: 'Move sheep when forage height reaches 3-4 inches'
  }
];

exports.mockPastureMaintenanceRecords = [
  {
    id: 1,
    pastureId: 1,
    date: '2023-03-15',
    activityType: 'Fertilization',
    description: 'Spring nitrogen application',
    materials: 'Urea (46-0-0)',
    quantity: 500,
    unit: 'lbs',
    cost: 350,
    performedBy: 'John Smith',
    notes: 'Applied at 50 lbs N/acre'
  },
  {
    id: 2,
    pastureId: 3,
    date: '2023-03-20',
    activityType: 'Weed Control',
    description: 'Spot treatment of thistles',
    materials: 'Herbicide - 2,4-D',
    quantity: 2,
    unit: 'gallons',
    cost: 120,
    performedBy: 'Michael Brown',
    notes: 'Targeted application to minimize clover damage'
  },
  {
    id: 3,
    pastureId: 2,
    date: '2023-02-28',
    activityType: 'Soil Testing',
    description: 'Annual soil analysis',
    materials: null,
    quantity: null,
    unit: null,
    cost: 175,
    performedBy: 'Agricultural Testing Services',
    notes: 'Results: pH 6.2, P: Medium, K: High'
  },
  {
    id: 4,
    pastureId: 5,
    date: '2023-03-25',
    activityType: 'Overseeding',
    description: 'Patchy areas reinforcement',
    materials: 'Orchard grass and red clover mix',
    quantity: 50,
    unit: 'lbs',
    cost: 225,
    performedBy: 'John Smith',
    notes: 'Broadcast seeding followed by cultipacking'
  },
  {
    id: 5,
    pastureId: 4,
    date: '2023-03-10',
    activityType: 'Fence Repair',
    description: 'Repair damaged section on north side',
    materials: 'Goat fencing, posts',
    quantity: null,
    unit: null,
    cost: null,
    performedBy: 'John Doe',
    notes: 'Repaired 100 feet of fence'
  }
];
