import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { motion } from 'framer-motion';
import {
  Box,
  useTheme,
  useMediaQuery
} from '@mui/material';
// Import local images
import { loginImages } from '../utils/localImages';
// Import custom components
import AnimatedLoginBackground from '../components/login/AnimatedLoginBackground';
import EnhancedLoginForm from '../components/login/EnhancedLoginForm';
import LoginStats from '../components/login/LoginStats';

const Login: React.FC = () => {
  const [error, setError] = useState('');
  
  const { login, isLoading, error: authError } = useAuth();
  const { translate } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Get the redirect path from location state or default to dashboard
  const from = location.state?.from?.pathname || '/dashboard';
  
  // Set error from auth context if available
  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);
  
  // Handle login submission
  const handleLogin = async (username: string, password: string) => {
    try {
      await login(username, password);
      // Navigate to the page user was trying to access or dashboard
      navigate(from, { replace: true });
    } catch (err) {
      // Error is handled in the auth context and displayed via the authError effect
      console.error('Login failed:', err);
    }
  };
  
  return (
    <Box 
      className="colorful-background"
      sx={{
        display: 'flex',
        minHeight: '100vh',
        overflow: 'hidden',
        background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
        backgroundSize: '400% 400%',
        animation: 'gradientAnimation 15s ease infinite',
      }}
    >
      {/* Main Container */}
      <Box sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        width: '100%',
        maxWidth: '1600px',
        height: '100vh',
        margin: '0 auto',
        p: { xs: 2, md: 4 }
      }}>
        {/* Left Side - Login Form */}
        <Box
          component={motion.div}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7 }}
          sx={{
            width: isMobile ? '100%' : '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: { xs: 2, md: 4 },
            order: { xs: 2, md: 1 }
          }}
        >
          <EnhancedLoginForm
            onSubmit={handleLogin}
            isLoading={isLoading}
            error={error}
          />
        </Box>
        
        {/* Right Side - Background with Stats */}
        <Box
          component={motion.div}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          sx={{
            width: isMobile ? '100%' : '50%',
            height: isMobile ? '40vh' : '100%',
            position: 'relative',
            borderRadius: { xs: 2, md: 3 },
            overflow: 'hidden',
            order: { xs: 1, md: 2 },
            mb: { xs: 3, md: 0 }
          }}
        >
          <AnimatedLoginBackground
            images={[
              '/images/modules/animals/cattle-1.jpeg',
              '/images/modules/animals/cattle-2.avif',
              '/images/modules/animals/cattle-3.jpeg',
              '/images/modules/animals/cattle-4.jpeg',
              '/images/modules/animals/cattle-5.avif'
            ]}
            interval={10000}
          >
            <LoginStats
              stats={[
                { value: '245+', label: translate('animals.tracked') },
                { value: '8+', label: translate('dashboard.modules') },
                { value: '24/7', label: translate('dashboard.support') },
                { value: '100%', label: translate('dashboard.satisfaction') }
              ]}
            />
          </AnimatedLoginBackground>
        </Box>
      </Box>
    </Box>
  );
};

export default Login;
