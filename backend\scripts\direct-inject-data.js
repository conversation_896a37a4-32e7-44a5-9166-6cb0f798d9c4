/**
 * Direct Inject Data Script
 * 
 * This script directly injects sample data into MongoDB for all modules
 */

const { MongoClient, ObjectId } = require('mongodb');

// MongoDB connection string
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

// Create sample data for all modules
const createSampleData = () => {
  // Generate IDs for animals
  const animalIds = {
    bull: new ObjectId(),
    cow: new ObjectId(),
    ram: new ObjectId(),
    ewe: new ObjectId(),
    buck: new ObjectId()
  };

  // Animal data
  const animals = [
    {
      _id: animalIds.bull,
      tagNumber: 'CTL-2024-001',
      name: 'Thabo',
      species: 'Cattle',
      breed: 'Nguni',
      gender: 'male',
      birthDate: new Date('2022-03-15'),
      acquisitionDate: new Date('2022-03-15'),
      weight: 450,
      status: 'active',
      healthStatus: 'healthy',
      location: 'North Paddock',
      rfidTag: 'RF00123456',
      notes: 'Prime breeding bull with excellent conformation',
      purchasePrice: 15000,
      estimatedValue: 25000,
      images: [
        {
          url: '/images/animals/cattle-1.jpeg',
          caption: 'Thabo - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: animalIds.cow,
      tagNumber: 'CTL-2024-002',
      name: 'Lesedi',
      species: 'Cattle',
      breed: 'Bonsmara',
      gender: 'female',
      birthDate: new Date('2021-06-10'),
      acquisitionDate: new Date('2021-06-10'),
      weight: 380,
      status: 'active',
      healthStatus: 'healthy',
      location: 'South Paddock',
      rfidTag: 'RF00123457',
      notes: 'Excellent milk producer',
      purchasePrice: 12000,
      estimatedValue: 18000,
      images: [
        {
          url: '/images/animals/cattle-2.avif',
          caption: 'Lesedi - Side View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: animalIds.ram,
      tagNumber: 'SHP-2024-001',
      name: 'Kgosi',
      species: 'Sheep',
      breed: 'Dorper',
      gender: 'male',
      birthDate: new Date('2023-01-20'),
      acquisitionDate: new Date('2023-01-20'),
      weight: 65,
      status: 'active',
      healthStatus: 'healthy',
      location: 'East Paddock',
      rfidTag: 'RF00123458',
      notes: 'Breeding ram with excellent wool quality',
      purchasePrice: 3500,
      estimatedValue: 5000,
      images: [
        {
          url: '/images/animals/sheep-1.jpeg',
          caption: 'Kgosi - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: animalIds.ewe,
      tagNumber: 'SHP-2024-002',
      name: 'Naledi',
      species: 'Sheep',
      breed: 'Merino',
      gender: 'female',
      birthDate: new Date('2022-11-05'),
      acquisitionDate: new Date('2022-11-05'),
      weight: 45,
      status: 'active',
      healthStatus: 'healthy',
      location: 'East Paddock',
      rfidTag: 'RF00123459',
      notes: 'High wool producer',
      purchasePrice: 2800,
      estimatedValue: 4000,
      images: [
        {
          url: '/images/animals/sheep-2.jpeg',
          caption: 'Naledi - Side View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: animalIds.buck,
      tagNumber: 'GOT-2024-001',
      name: 'Tumelo',
      species: 'Goat',
      breed: 'Boer Goat',
      gender: 'male',
      birthDate: new Date('2023-02-15'),
      acquisitionDate: new Date('2023-02-15'),
      weight: 70,
      status: 'active',
      healthStatus: 'healthy',
      location: 'West Paddock',
      rfidTag: 'RF00123460',
      notes: 'Breeding buck with excellent meat conformation',
      purchasePrice: 4000,
      estimatedValue: 6000,
      images: [
        {
          url: '/images/animals/goat-1.jpeg',
          caption: 'Tumelo - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Health records
  const healthRecords = [
    {
      _id: new ObjectId(),
      animal: animalIds.bull,
      recordType: 'examination',
      date: new Date('2024-01-15'),
      performer: new ObjectId(),
      veterinarian: {
        name: 'Dr. Sipho Mkhize',
        contact: '+27 82 123 4567',
        clinic: 'Farmland Veterinary Clinic'
      },
      diagnosis: 'Healthy, routine checkup',
      symptoms: [],
      temperature: {
        value: 38.5,
        unit: 'C'
      },
      weight: {
        value: 450,
        unit: 'kg'
      },
      medications: [],
      procedures: [
        {
          name: 'General health examination',
          description: 'Routine health check',
          cost: 500
        }
      ],
      cost: 500,
      notes: 'Animal is in excellent health',
      status: 'resolved',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      animal: animalIds.cow,
      recordType: 'vaccination',
      date: new Date('2024-02-10'),
      performer: new ObjectId(),
      veterinarian: {
        name: 'Dr. Sipho Mkhize',
        contact: '+27 82 123 4567',
        clinic: 'Farmland Veterinary Clinic'
      },
      diagnosis: '',
      symptoms: [],
      temperature: {
        value: 38.7,
        unit: 'C'
      },
      weight: {
        value: 380,
        unit: 'kg'
      },
      medications: [
        {
          name: 'Bovine Viral Diarrhea Vaccine',
          dosage: '5',
          unit: 'ml',
          route: 'intramuscular',
          frequency: 'once',
          startDate: new Date('2024-02-10'),
          endDate: new Date('2024-02-10'),
          notes: 'Annual vaccination'
        }
      ],
      procedures: [
        {
          name: 'Vaccination',
          description: 'Annual BVD vaccination',
          cost: 350
        }
      ],
      cost: 350,
      notes: 'Annual vaccination completed',
      status: 'resolved',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Breeding records
  const breedingRecordId = new ObjectId();
  const breedingRecords = [
    {
      _id: breedingRecordId,
      maleAnimal: animalIds.bull,
      femaleAnimal: animalIds.cow,
      breedingDate: new Date('2023-06-15'),
      method: 'natural',
      location: 'South Paddock',
      status: 'successful',
      confirmedPregnant: true,
      confirmationDate: new Date('2023-07-20'),
      confirmationMethod: 'veterinary examination',
      expectedDueDate: new Date('2024-03-25'),
      notes: 'Successful breeding, pregnancy confirmed',
      performer: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Birth records
  const birthRecords = [
    {
      _id: new ObjectId(),
      mother: animalIds.cow,
      father: animalIds.bull,
      birthDate: new Date('2024-03-27'),
      location: 'South Paddock',
      birthType: 'natural',
      assistance: 'none',
      complications: 'none',
      breedingRecord: breedingRecordId,
      offspring: [
        {
          tagNumber: 'CTL-2024-003',
          gender: 'female',
          weight: 35,
          status: 'alive',
          notes: 'Healthy female calf'
        }
      ],
      totalOffspring: 1,
      aliveOffspring: 1,
      notes: 'Normal birth, healthy calf',
      performer: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Financial records
  const financialRecords = [
    {
      _id: new ObjectId(),
      transactionDate: new Date('2024-01-15'),
      type: 'expense',
      category: 'feed',
      subcategory: 'hay',
      amount: 5000,
      currency: 'ZAR',
      description: 'Purchase of alfalfa hay',
      paymentMethod: 'bank transfer',
      reference: 'INV-2024-001',
      vendor: 'Farm Feed Suppliers',
      status: 'completed',
      relatedTo: {
        type: 'feeding',
        id: null
      },
      notes: 'Monthly hay purchase',
      createdBy: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      transactionDate: new Date('2024-04-20'),
      type: 'income',
      category: 'sales',
      subcategory: 'livestock',
      amount: 15000,
      currency: 'ZAR',
      description: 'Sale of cattle',
      paymentMethod: 'bank transfer',
      reference: 'SALE-2024-008',
      vendor: 'Local Livestock Auction',
      status: 'completed',
      relatedTo: {
        type: 'animal',
        id: null
      },
      notes: 'Sale of 2 cattle at auction',
      createdBy: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Business KPIs
  const businessKpis = [
    {
      _id: new ObjectId(),
      name: 'Average Daily Milk Production',
      value: 18.5,
      target: 20,
      unit: 'liters/cow',
      status: 'warning',
      trend: 5, // 5% increase from previous period
      category: 'operational',
      description: 'Average daily milk production per cow',
      period: 'monthly',
      date: new Date('2024-04-30'),
      createdBy: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      name: 'Feed Cost per Animal',
      value: 45,
      target: 40,
      unit: 'ZAR/day',
      status: 'danger',
      trend: 8, // 8% increase from previous period
      category: 'financial',
      description: 'Average daily feed cost per animal',
      period: 'monthly',
      date: new Date('2024-04-30'),
      createdBy: new ObjectId(),
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  return {
    animals,
    healthRecords,
    breedingRecords,
    birthRecords,
    financialRecords,
    businessKpis
  };
};

// Function to inject data into MongoDB
async function injectData() {
  let client;

  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    
    // Get sample data
    const data = createSampleData();
    
    // Insert animals
    console.log('Inserting animal data...');
    await db.collection('animals').deleteMany({});
    await db.collection('animals').insertMany(data.animals);
    
    // Insert health records
    console.log('Inserting health records...');
    await db.collection('healthrecords').deleteMany({});
    await db.collection('healthrecords').insertMany(data.healthRecords);
    
    // Insert breeding records
    console.log('Inserting breeding records...');
    await db.collection('breedingrecords').deleteMany({});
    await db.collection('breedingrecords').insertMany(data.breedingRecords);
    
    // Insert birth records
    console.log('Inserting birth records...');
    await db.collection('birthrecords').deleteMany({});
    await db.collection('birthrecords').insertMany(data.birthRecords);
    
    // Insert financial records
    console.log('Inserting financial records...');
    await db.collection('transactions').deleteMany({});
    await db.collection('transactions').insertMany(data.financialRecords);
    
    // Insert business KPIs
    console.log('Inserting business KPIs...');
    await db.collection('businesskpis').deleteMany({});
    await db.collection('businesskpis').insertMany(data.businessKpis);
    
    console.log('Data injection completed successfully');
  } catch (error) {
    console.error('Error injecting data:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectData()
  .then(() => {
    console.log('Data injection process completed');
  })
  .catch((error) => {
    console.error('Error in data injection process:', error);
  });
