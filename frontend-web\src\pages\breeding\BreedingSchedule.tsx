import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { motion } from 'framer-motion';
import { format, addDays, isBefore, isAfter, parseISO } from 'date-fns';
import { Box, Typography, Grid, Card, CardContent, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, Tabs, Tab, Divider, Avatar, LinearProgress } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CheckCircle,
  Cancel,
  CalendarToday,
  Pets,
  Male,
  Female,
  Science,
  FilterList,
  Search,
  Visibility
 } from '../../utils/iconImports';
import { mockBreedingRecords } from '../../mocks/breedingData';
import { mockAnimals } from '../../mocks/animalData';

interface BreedingEvent {
  id: string;
  animalId: string;
  animalName: string;
  breedingDate: Date;
  method: 'natural' | 'artificial';
  sireId: string;
  sireName: string;
  status: 'scheduled' | 'completed' | 'failed';
  expectedDueDate?: Date;
  veterinarian?: string;
  notes: string;
  followUpDates?: FollowUpDate[];
}

interface FollowUpDate {
  date: Date;
  type: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  notes: string;
}

interface Animal {
  id: string;
  name: string;
  gender: 'male' | 'female';
  breed: string;
  age: number;
}

const BreedingSchedule: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<BreedingEvent | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Convert mock data to our format
  const convertedRecords: BreedingEvent[] = mockBreedingRecords.map(record => {
    // Find female animal
    const femaleAnimal = mockAnimals.find((animal: { id: string }) => animal.id === record.femaleId.toString()) || {
      id: record.femaleId,
      name: `Animal ${record.femaleId}`,
      gender: 'female',
      breed: 'Unknown',
      age: 0
    };

    // Find male animal
    const maleAnimal = mockAnimals.find((animal: { id: string }) => animal.id === record.maleId.toString()) || {
      id: record.maleId,
      name: `Animal ${record.maleId}`,
      gender: 'male',
      breed: 'Unknown',
      age: 0
    };

    return {
      id: record.id.toString(),
      animalId: record.femaleId.toString(),
      animalName: femaleAnimal.name,
      breedingDate: parseISO(record.date),
      method: record.type === 'artificial' ? 'artificial' : 'natural',
      sireId: record.maleId.toString(),
      sireName: maleAnimal.name,
      status: record.status.toLowerCase() === 'pending' ? 'scheduled' :
              record.status.toLowerCase() === 'confirmed' ? 'completed' : 'failed',
      expectedDueDate: record.expectedDueDate ? parseISO(record.expectedDueDate) : undefined,
      veterinarian: record.technician,
      notes: record.notes,
      followUpDates: record.followUpDates?.map((followUp: { date: string, type: string, status: string, notes: string }) => ({
        date: parseISO(followUp.date),
        type: followUp.type,
        status: followUp.status as 'completed' | 'scheduled' | 'cancelled',
        notes: followUp.notes
      }))
    };
  });

  const [schedules, setSchedules] = useState<BreedingEvent[]>(convertedRecords);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenDialog = (event?: BreedingEvent) => {
    setSelectedEvent(event || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedEvent(null);
  };

  const handleSaveEvent = () => {
    // In a real app, this would save to the backend
    // For now, we'll just close the dialog
    handleCloseDialog();
  };

  // Filter schedules based on tab, search term, and status filter
  const filteredSchedules = schedules.filter(schedule => {
    // Filter by tab
    if (tabValue === 1 && schedule.status !== 'scheduled') return false;
    if (tabValue === 2 && schedule.status !== 'completed') return false;
    if (tabValue === 3 && schedule.status !== 'failed') return false;

    // Filter by search term
    if (searchTerm && !(
      schedule.animalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.animalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      schedule.sireName.toLowerCase().includes(searchTerm.toLowerCase())
    )) return false;

    // Filter by status
    if (filterStatus !== 'all' && schedule.status !== filterStatus) return false;

    return true;
  });

  // Get upcoming schedules (next 7 days)
  const today = new Date();
  const nextWeek = addDays(today, 7);
  const upcomingSchedules = schedules.filter(schedule =>
    schedule.status === 'scheduled' &&
    isAfter(schedule.breedingDate, today) &&
    isBefore(schedule.breedingDate, nextWeek)
  );

  // Get today's schedules
  const todaySchedules = schedules.filter(schedule =>
    schedule.status === 'scheduled' &&
    format(schedule.breedingDate, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')
  );

  // Calculate statistics
  const totalScheduled = schedules.filter(s => s.status === 'scheduled').length;
  const totalCompleted = schedules.filter(s => s.status === 'completed').length;
  const totalFailed = schedules.filter(s => s.status === 'failed').length;
  const successRate = totalCompleted > 0 ?
    Math.round((totalCompleted / (totalCompleted + totalFailed)) * 100) : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'primary';
      case 'completed': return 'success';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <CalendarToday fontSize="small" />;
      case 'completed': return <CheckCircle fontSize="small" />;
      case 'failed': return <Cancel fontSize="small" />;
      default: return undefined;
    }
  };

  return (
    <Box sx={{ p: 3, bgcolor: '#f5f5f5', minHeight: '100vh' }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" fontWeight="bold">Breeding Schedule</Typography>
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            Add Breeding Event
          </CustomButton>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>Total Scheduled</Typography>
                <Typography variant="h4">{totalScheduled}</Typography>
                <LinearProgress
                  variant="determinate"
                  value={(totalScheduled / schedules.length) * 100}
                  color="primary"
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>Completed</Typography>
                <Typography variant="h4">{totalCompleted}</Typography>
                <LinearProgress
                  variant="determinate"
                  value={(totalCompleted / schedules.length) * 100}
                  color="success"
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>Failed</Typography>
                <Typography variant="h4">{totalFailed}</Typography>
                <LinearProgress
                  variant="determinate"
                  value={(totalFailed / schedules.length) * 100}
                  color="error"
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>Success Rate</Typography>
                <Typography variant="h4">{successRate}%</Typography>
                <LinearProgress
                  variant="determinate"
                  value={successRate}
                  color={successRate > 75 ? 'success' : successRate > 50 ? 'warning' : 'error'}
                  sx={{ mt: 2, height: 8, borderRadius: 4 }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Today's and Upcoming Schedules */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={6}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Today's Breeding Events
                </Typography>
                {todaySchedules.length > 0 ? (
                  <Box>
                    {todaySchedules.map((schedule) => (
                      <Box key={schedule.id} sx={{ mb: 2, p: 2, bgcolor: '#f9f9f9', borderRadius: 1 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                              <Female />
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {schedule.animalName}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                ID: {schedule.animalId}
                              </Typography>
                            </Box>
                          </Box>
                          <Chip
                            label={schedule.method.toUpperCase()}
                            color={schedule.method === 'artificial' ? 'secondary' : 'primary'}
                            size="small"
                            icon={schedule.method === 'artificial' ? <Science /> : <Pets />}
                          />
                        </Box>
                        <Divider sx={{ my: 1 }} />
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="body2">
                            <strong>Time:</strong> {format(schedule.breedingDate, 'h:mm a')}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Sire:</strong> {schedule.sireName}
                          </Typography>
                          <CustomButton
                            size="small"
                            variant="outlined"
                            color="primary"
                            onClick={() => handleOpenDialog(schedule)}
                          >
                            View Details
                          </CustomButton>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body1" color="textSecondary" sx={{ py: 2 }}>
                    No breeding events scheduled for today.
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Upcoming Breeding Events (Next 7 Days)
                </Typography>
                {upcomingSchedules.length > 0 ? (
                  <Box>
                    {upcomingSchedules.map((schedule) => (
                      <Box key={schedule.id} sx={{ mb: 2, p: 2, bgcolor: '#f9f9f9', borderRadius: 1 }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Box display="flex" alignItems="center">
                            <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                              <Female />
                            </Avatar>
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {schedule.animalName}
                              </Typography>
                              <Typography variant="body2" color="textSecondary">
                                ID: {schedule.animalId}
                              </Typography>
                            </Box>
                          </Box>
                          <Chip
                            label={format(schedule.breedingDate, 'MMM dd')}
                            color="primary"
                            size="small"
                            icon={<CalendarToday fontSize="small" />}
                          />
                        </Box>
                        <Divider sx={{ my: 1 }} />
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="body2">
                            <strong>Method:</strong> {schedule.method}
                          </Typography>
                          <Typography variant="body2">
                            <strong>Sire:</strong> {schedule.sireName}
                          </Typography>
                          <CustomButton
                            size="small"
                            variant="outlined"
                            color="primary"
                            onClick={() => handleOpenDialog(schedule)}
                          >
                            View Details
                          </CustomButton>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Typography variant="body1" color="textSecondary" sx={{ py: 2 }}>
                    No upcoming breeding events in the next 7 days.
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Main Schedule Table */}
        <Card sx={{ borderRadius: 2, boxShadow: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="All Events" />
                <Tab label="Scheduled" />
                <Tab label="Completed" />
                <Tab label="Failed" />
              </Tabs>
              <Box display="flex" alignItems="center">
                <TextField
                  size="small"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  sx={{ mr: 2 }}
                />
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filterStatus}
                    label="Status"
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <MenuItem value="all">All</MenuItem>
                    <MenuItem value="scheduled">Scheduled</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                  </Select>
                </FormControl>
                <IconButton>
                  <FilterList />
                </IconButton>
              </Box>
            </Box>
          </Box>

          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table>
              <TableHead sx={{ bgcolor: '#f5f5f5' }}>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Animal</TableCell>
                  <TableCell>Breeding Date</TableCell>
                  <TableCell>Method</TableCell>
                  <TableCell>Sire</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Expected Due Date</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredSchedules.map((schedule) => (
                  <TableRow key={schedule.id} hover>
                    <TableCell>{schedule.id}</TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'primary.light', width: 30, height: 30, mr: 1 }}>
                          <Female fontSize="small" />
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">{schedule.animalName}</Typography>
                          <Typography variant="caption" color="textSecondary">ID: {schedule.animalId}</Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{format(schedule.breedingDate, 'MMM dd, yyyy')}</TableCell>
                    <TableCell>
                      <Chip
                        label={schedule.method}
                        size="small"
                        color={schedule.method === 'artificial' ? 'secondary' : 'primary'}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ bgcolor: 'secondary.light', width: 30, height: 30, mr: 1 }}>
                          <Male fontSize="small" />
                        </Avatar>
                        <Typography variant="body2">{schedule.sireName}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(schedule.status)}
                        label={schedule.status}
                        color={getStatusColor(schedule.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {schedule.expectedDueDate ? (
                        format(schedule.expectedDueDate, 'MMM dd, yyyy')
                      ) : (
                        <Typography variant="caption" color="textSecondary">Not set</Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary" onClick={() => handleOpenDialog(schedule)}>
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="primary">
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </motion.div>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedEvent ? 'Edit Breeding Event' : 'Add New Breeding Event'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Female Animal"
                select
                fullWidth
                margin="normal"
                value={selectedEvent?.animalId || ''}
              >
                {mockAnimals
                  .filter((animal: { gender: string }) => animal.gender.toLowerCase() === 'female')
                  .map((animal: { id: string, name: string }) => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} (ID: {animal.id})
                    </MenuItem>
                  ))
                }
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Male Animal (Sire)"
                select
                fullWidth
                margin="normal"
                value={selectedEvent?.sireId || ''}
              >
                {mockAnimals
                  .filter((animal: { gender: string }) => animal.gender.toLowerCase() === 'male')
                  .map((animal: { id: string, name: string }) => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} (ID: {animal.id})
                    </MenuItem>
                  ))
                }
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Breeding Date"
                type="date"
                fullWidth
                margin="normal"
                value={selectedEvent ? format(selectedEvent.breedingDate, 'yyyy-MM-dd') : format(new Date(), 'yyyy-MM-dd')}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Method"
                select
                fullWidth
                margin="normal"
                value={selectedEvent?.method || 'natural'}
              >
                <MenuItem value="natural">Natural</MenuItem>
                <MenuItem value="artificial">Artificial Insemination</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Status"
                select
                fullWidth
                margin="normal"
                value={selectedEvent?.status || 'scheduled'}
              >
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="failed">Failed</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Veterinarian/Technician"
                fullWidth
                margin="normal"
                value={selectedEvent?.veterinarian || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Notes"
                fullWidth
                multiline
                rows={4}
                margin="normal"
                value={selectedEvent?.notes || ''}
              />
            </Grid>
          </Grid>

          {selectedEvent && selectedEvent.followUpDates && selectedEvent.followUpDates.length > 0 && (
            <Box mt={3}>
              <Typography variant="h6" gutterBottom>Follow-up Dates</Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {selectedEvent.followUpDates.map((followUp, index) => (
                      <TableRow key={index}>
                        <TableCell>{format(followUp.date, 'MMM dd, yyyy')}</TableCell>
                        <TableCell>{followUp.type}</TableCell>
                        <TableCell>
                          <Chip
                            label={followUp.status}
                            size="small"
                            color={
                              followUp.status === 'completed' ? 'success' :
                              followUp.status === 'scheduled' ? 'primary' : 'default'
                            }
                          />
                        </TableCell>
                        <TableCell>{followUp.notes}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton onClick={handleSaveEvent} variant="contained" color="primary">
            {selectedEvent ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BreedingSchedule;
