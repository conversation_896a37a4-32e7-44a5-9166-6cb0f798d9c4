describe('Login Flow', () => {
  beforeEach(() => {
    cy.visit('/login');
  });

  it('should display the login form', () => {
    cy.get('[data-testid=login-form]').should('be.visible');
    cy.get('[data-testid=username-input]').should('be.visible');
    cy.get('[data-testid=password-input]').should('be.visible');
    cy.get('[data-testid=login-button]').should('be.visible');
  });

  it('should show validation errors for empty fields', () => {
    cy.get('[data-testid=login-button]').click();
    cy.get('[data-testid=username-error]').should('be.visible');
    cy.get('[data-testid=password-error]').should('be.visible');
  });

  it('should login successfully with valid credentials', () => {
    cy.get('[data-testid=username-input]').type('admin');
    cy.get('[data-testid=password-input]').type('password');
    cy.get('[data-testid=login-button]').click();
    
    // Should redirect to dashboard after successful login
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid=dashboard-welcome]').should('be.visible');
  });

  it('should show error message with invalid credentials', () => {
    cy.get('[data-testid=username-input]').type('invalid');
    cy.get('[data-testid=password-input]').type('invalid');
    cy.get('[data-testid=login-button]').click();
    
    // Should show error message
    cy.get('[data-testid=login-error]').should('be.visible');
    cy.url().should('include', '/login');
  });

  it('should be responsive on mobile devices', () => {
    // Test on mobile viewport
    cy.viewport('iphone-x');
    
    // Login form should still be visible and usable
    cy.get('[data-testid=login-form]').should('be.visible');
    cy.get('[data-testid=username-input]').should('be.visible').type('admin');
    cy.get('[data-testid=password-input]').should('be.visible').type('password');
    cy.get('[data-testid=login-button]').should('be.visible').click();
    
    // Should redirect to dashboard after successful login
    cy.url().should('include', '/dashboard');
  });
});
