// Test script to verify auth middleware

const { protect, authorize } = require('./src/middleware/auth');

// Mock request object
const req = {
  user: {
    id: 1,
    username: 'AM<PERSON>',
    role: 'admin'
  }
};

// Mock response object
const res = {
  status: (code) => {
    console.log(`Status code: ${code}`);
    return {
      json: (data) => {
        console.log('Response data:', data);
      }
    };
  }
};

// Mock next function
const next = () => {
  console.log('Next function called');
};

// Test protect middleware
console.log('Testing protect middleware:');
try {
  protect(req, res, next);
  console.log('Protect middleware test passed');
} catch (error) {
  console.error('Protect middleware test failed:', error);
}

// Test authorize middleware
console.log('\nTesting authorize middleware with admin role:');
try {
  const adminAuthorize = authorize('admin');
  adminAuthorize(req, res, next);
  console.log('Authorize middleware test passed for admin role');
} catch (error) {
  console.error('Authorize middleware test failed for admin role:', error);
}

console.log('\nTesting authorize middleware with user role:');
try {
  const userAuthorize = authorize('user');
  userAuthorize(req, res, next);
  console.log('Authorize middleware test passed for user role');
} catch (error) {
  console.error('Authorize middleware test failed for user role:', error);
}

console.log('\nAll tests completed');
