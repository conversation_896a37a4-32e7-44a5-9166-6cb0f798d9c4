/**
 * Translation Hook
 * 
 * This hook provides translation functionality using the LanguageContext.
 * It's a wrapper around the useLanguage hook to provide a more standardized interface.
 */

import { useLanguage } from '../contexts/LanguageContext';

/**
 * Hook for accessing translation functionality
 * @returns Translation functions and language state
 */
export const useTranslation = () => {
  const { 
    language, 
    setLanguage, 
    translate, 
    translateSubModule, 
    translateModuleField, 
    isLoading 
  } = useLanguage();

  return {
    language,
    setLanguage,
    t: translate, // Alias for translate
    translate,
    translateSubModule,
    translateModuleField,
    isLoading
  };
};

export default useTranslation;
