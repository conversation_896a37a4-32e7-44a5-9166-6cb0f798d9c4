import { MongoDbService } from './mongoDbService';
import { User } from '../types/user';
import axios from 'axios';

// API URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

/**
 * MongoDB User Service
 * Handles user-related operations with MongoDB
 */
export class UserMongoService extends MongoDbService<User> {
  constructor() {
    super('users');
  }

  /**
   * Get all users from MongoDB
   */
  async getAllUsers(): Promise<User[]> {
    try {
      console.log('Fetching users from API');
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await axios.get(`${API_URL}/users`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error('Error fetching users from API:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param id User ID
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      console.log(`Fetching user with ID ${id} from API`);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await axios.get(`${API_URL}/users/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching user with ID ${id} from API:`, error);
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Get user by username
   * @param username Username
   */
  async getUserByUsername(username: string): Promise<User | null> {
    try {
      console.log(`Fetching user with username ${username} from API`);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await axios.get(`${API_URL}/users/username/${username}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error(`Error fetching user with username ${username} from API:`, error);
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Create a new user
   * @param userData User data
   */
  async createUser(userData: Partial<User>): Promise<User> {
    try {
      console.log('Creating user via API:', userData);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Prepare user data
      const newUser = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: (userData.role as 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer') || 'viewer',
        status: (userData.status as 'active' | 'inactive' | 'suspended') || 'active',
        permissions: userData.permissions || []
      };

      const response = await axios.post(`${API_URL}/users`, newUser, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error('Error creating user via API:', error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to create user');
      }
      throw error;
    }
  }

  /**
   * Update a user
   * @param id User ID
   * @param userData User data to update
   */
  async updateUser(id: string, userData: Partial<User>): Promise<User> {
    try {
      console.log(`Updating user with ID ${id} via API:`, userData);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      // Prepare update data
      const updateData: any = { ...userData };

      // Remove id from update data
      delete updateData.id;

      const response = await axios.put(`${API_URL}/users/${id}`, updateData, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return response.data;
    } catch (error) {
      console.error(`Error updating user with ID ${id} via API:`, error);
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to update user');
      }
      throw error;
    }
  }

  /**
   * Delete a user
   * @param id User ID
   */
  async deleteUser(id: string): Promise<boolean> {
    try {
      console.log(`Deleting user with ID ${id} via API`);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication required');
      }

      await axios.delete(`${API_URL}/users/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      return true;
    } catch (error) {
      console.error(`Error deleting user with ID ${id} via API:`, error);
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        return false;
      }
      if (axios.isAxiosError(error) && error.response) {
        throw new Error(error.response.data.error || 'Failed to delete user');
      }
      throw error;
    }
  }


}

// Create and export an instance of the service
export const userMongoService = new UserMongoService();
