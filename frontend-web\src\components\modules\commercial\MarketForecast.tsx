import React, { useState } from 'react';
import CustomButton from '../../common/CustomButton';
import { Box, Typography, Card, CardContent, Grid, Chip, FormControl, InputLabel, Select, MenuItem, useTheme, alpha, Tooltip, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Info,
  Download,
  FilterList,
  Event as CalendarMonth
} from '../../../utils/iconImports';
import AnimatedBackgroundCard from '../../common/AnimatedBackgroundCard';

// Mock data for price forecasts
const mockPriceForecastData = [
  { month: 'Jan', beef: 42.50, mutton: 65.75, pork: 38.20, forecast: true },
  { month: 'Feb', beef: 43.25, mutton: 66.50, pork: 37.80, forecast: true },
  { month: 'Mar', beef: 44.00, mutton: 67.25, pork: 38.50, forecast: true },
  { month: 'Apr', beef: 45.75, mutton: 68.00, pork: 39.25, forecast: true },
  { month: 'May', beef: 46.50, mutton: 69.75, pork: 40.00, forecast: true },
  { month: 'Jun', beef: 47.25, mutton: 70.50, pork: 41.75, forecast: true },
  { month: 'Jul', beef: 48.00, mutton: 71.25, pork: 42.50, forecast: false },
  { month: 'Aug', beef: 47.50, mutton: 70.75, pork: 41.25, forecast: false },
  { month: 'Sep', beef: 46.75, mutton: 69.50, pork: 40.75, forecast: false },
  { month: 'Oct', beef: 45.50, mutton: 68.25, pork: 39.50, forecast: false },
  { month: 'Nov', beef: 44.25, mutton: 67.00, pork: 38.75, forecast: false },
  { month: 'Dec', beef: 43.75, mutton: 66.25, pork: 38.00, forecast: false }
];

// Mock data for market trends
const mockMarketTrends = [
  { id: 'TR001', category: 'Beef', currentPrice: 'R45.50/kg', change: '+2.5%', forecast: 'Increasing', demand: 'High', confidence: 85 },
  { id: 'TR002', category: 'Mutton', currentPrice: 'R68.25/kg', change: '-1.2%', forecast: 'Stable', demand: 'Medium', confidence: 72 },
  { id: 'TR003', category: 'Pork', currentPrice: 'R39.50/kg', change: '+3.8%', forecast: 'Increasing', demand: 'High', confidence: 78 },
  { id: 'TR004', category: 'Dairy', currentPrice: 'R12.75/L', change: '+0.8%', forecast: 'Stable', demand: 'Medium', confidence: 65 },
  { id: 'TR005', category: 'Wool', currentPrice: 'R85.00/kg', change: '-2.5%', forecast: 'Decreasing', demand: 'Low', confidence: 70 },
  { id: 'TR006', category: 'Feed', currentPrice: 'R3,200/ton', change: '+4.2%', forecast: 'Increasing', demand: 'High', confidence: 82 },
  { id: 'TR007', category: 'Vaccines', currentPrice: 'R450/unit', change: '+1.5%', forecast: 'Stable', demand: 'Medium', confidence: 68 }
];

// Mock data for supply forecast
const mockSupplyForecast = [
  { month: 'Jul', beef: 85, mutton: 78, pork: 92 },
  { month: 'Aug', beef: 82, mutton: 75, pork: 88 },
  { month: 'Sep', beef: 78, mutton: 72, pork: 85 },
  { month: 'Oct', beef: 75, mutton: 70, pork: 80 },
  { month: 'Nov', beef: 80, mutton: 74, pork: 83 },
  { month: 'Dec', beef: 83, mutton: 77, pork: 87 }
];

// Mock data for seasonal price variations
const mockSeasonalVariations = [
  { season: 'Summer', beef: +5, mutton: -2, pork: +3, feed: +8 },
  { season: 'Autumn', beef: +2, mutton: +4, pork: +1, feed: -3 },
  { season: 'Winter', beef: -3, mutton: +8, pork: -2, feed: -5 },
  { season: 'Spring', beef: -4, mutton: -5, pork: +4, feed: +2 }
];

const MarketForecast: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState<string>('6months');
  const [productCategory, setProductCategory] = useState<string>('all');
  const [selectedTrend, setSelectedTrend] = useState<string | null>(null);

  // Filter data based on selected time range
  const getFilteredForecastData = () => {
    if (timeRange === '3months') {
      return mockPriceForecastData.slice(0, 3);
    } else if (timeRange === '6months') {
      return mockPriceForecastData.slice(0, 6);
    } else {
      return mockPriceForecastData;
    }
  };

  // Filter trends based on selected category
  const getFilteredTrends = () => {
    if (productCategory === 'all') {
      return mockMarketTrends;
    } else {
      return mockMarketTrends.filter(trend =>
        trend.category.toLowerCase() === productCategory.toLowerCase()
      );
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <Box sx={{ px: 3, pb: 5 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Filters */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 4, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
              >
                <MenuItem value="3months">3 Months</MenuItem>
                <MenuItem value="6months">6 Months</MenuItem>
                <MenuItem value="12months">12 Months</MenuItem>
              </Select>
            </FormControl>

            <FormControl size="small" sx={{ minWidth: 150 }}>
              <InputLabel>Category</InputLabel>
              <Select
                value={productCategory}
                label="Category"
                onChange={(e) => setProductCategory(e.target.value)}
              >
                <MenuItem value="all">All Categories</MenuItem>
                <MenuItem value="beef">Beef</MenuItem>
                <MenuItem value="mutton">Mutton</MenuItem>
                <MenuItem value="pork">Pork</MenuItem>
                <MenuItem value="dairy">Dairy</MenuItem>
                <MenuItem value="feed">Feed</MenuItem>
              </Select>
            </FormControl>

            <CustomButton
              variant="outlined"
              startIcon={<Download />}
              size="small"
              sx={{ ml: 'auto' }}
            >
              Export Forecast
            </CustomButton>
          </Box>
        </motion.div>

        {/* Price Forecast Chart */}
        <motion.div variants={itemVariants}>
          <AnimatedBackgroundCard
            title="Price Forecast Trends"
            subtitle="Projected market prices for key livestock products (R/kg)"
            module="commercial"
            uniqueId="price-forecast"
            icon={<TrendingUp />}
            accentColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            height={400}
            delay={0.1}
          >
            <Box sx={{ height: 320, mt: 2 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={getFilteredForecastData()}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="month" />
                  <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
                  <RechartsTooltip
                    formatter={(value: number) => [`R${value.toFixed(2)}/kg`, '']}
                    labelFormatter={(label) => `Month: ${label}`}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="beef"
                    name="Beef"
                    stroke="#D32F2F"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 8 }}
                    strokeDasharray="3 3"
                  />
                  <Line
                    type="monotone"
                    dataKey="mutton"
                    name="Mutton"
                    stroke="#7B1FA2"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 8 }}
                    strokeDasharray="3 3"
                  />
                  <Line
                    type="monotone"
                    dataKey="pork"
                    name="Pork"
                    stroke="#1976D2"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 8 }}
                    strokeDasharray="3 3"
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>
                <Box sx={{ width: 30, height: 2, backgroundColor: '#000', mr: 1 }} />
                <Typography variant="caption">Historical</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>
                <Box sx={{ width: 30, height: 2, backgroundColor: '#000', mr: 1, borderStyle: 'dashed', borderWidth: 1 }} />
                <Typography variant="caption">Forecast</Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </motion.div>

        {/* Market Trends and Supply Forecast */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Market Trends Table */}
          <Grid item xs={12} md={7}>
            <motion.div variants={itemVariants}>
              <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Market Trends & Forecasts
                  </Typography>
                  <TableContainer component={Paper} sx={{ boxShadow: 'none', maxHeight: 350 }}>
                    <Table size="small" stickyHeader>
                      <TableHead>
                        <TableRow>
                          <TableCell>Category</TableCell>
                          <TableCell>Current Price</TableCell>
                          <TableCell>Change</TableCell>
                          <TableCell>Forecast</TableCell>
                          <TableCell>Demand</TableCell>
                          <TableCell>Confidence</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {getFilteredTrends().map((trend) => (
                          <TableRow
                            key={trend.id}
                            hover
                            onClick={() => setSelectedTrend(trend.id)}
                            selected={selectedTrend === trend.id}
                            sx={{
                              cursor: 'pointer',
                              '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.05) }
                            }}
                          >
                            <TableCell>{trend.category}</TableCell>
                            <TableCell>{trend.currentPrice}</TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center', color: trend.change.startsWith('+') ? 'success.main' : 'error.main' }}>
                                {trend.change.startsWith('+') ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                                {trend.change}
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={trend.forecast}
                                color={
                                  trend.forecast === 'Increasing' ? 'success' :
                                  trend.forecast === 'Decreasing' ? 'error' : 'info'
                                }
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={trend.demand}
                                color={
                                  trend.demand === 'High' ? 'success' :
                                  trend.demand === 'Low' ? 'error' : 'warning'
                                }
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Box
                                  sx={{
                                    width: 50,
                                    height: 6,
                                    borderRadius: 3,
                                    backgroundColor: alpha(theme.palette.grey[300], 0.5),
                                    mr: 1,
                                    position: 'relative',
                                    overflow: 'hidden'
                                  }}
                                >
                                  <Box
                                    sx={{
                                      position: 'absolute',
                                      left: 0,
                                      top: 0,
                                      bottom: 0,
                                      width: `${trend.confidence}%`,
                                      backgroundColor:
                                        trend.confidence > 80 ? theme.palette.success.main :
                                        trend.confidence > 70 ? theme.palette.warning.main :
                                        theme.palette.error.main
                                    }}
                                  />
                                </Box>
                                <Typography variant="caption">{trend.confidence}%</Typography>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          {/* Supply Forecast */}
          <Grid item xs={12} md={5}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Supply Availability Forecast"
                subtitle="Projected supply levels for the next 6 months (%)"
                module="commercial"
                uniqueId="supply-forecast"
                icon={<CalendarMonth />}
                accentColor={theme.palette.secondary.main}
                secondaryColor={theme.palette.secondary.dark}
                height={400}
                delay={0.2}
              >
                <Box sx={{ height: 320, mt: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={mockSupplyForecast}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="month" />
                      <YAxis domain={[50, 100]} />
                      <RechartsTooltip
                        formatter={(value: number) => [`${value}%`, '']}
                        labelFormatter={(label) => `Month: ${label}`}
                      />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="beef"
                        name="Beef"
                        stroke="#D32F2F"
                        fill={alpha('#D32F2F', 0.2)}
                        activeDot={{ r: 8 }}
                      />
                      <Area
                        type="monotone"
                        dataKey="mutton"
                        name="Mutton"
                        stroke="#7B1FA2"
                        fill={alpha('#7B1FA2', 0.2)}
                        activeDot={{ r: 8 }}
                      />
                      <Area
                        type="monotone"
                        dataKey="pork"
                        name="Pork"
                        stroke="#1976D2"
                        fill={alpha('#1976D2', 0.2)}
                        activeDot={{ r: 8 }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>
        </Grid>

        {/* Seasonal Variations */}
        <motion.div variants={itemVariants} style={{ marginTop: '24px' }}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" fontWeight="bold">
                  Seasonal Price Variations
                </Typography>
                <Tooltip title="Percentage change in prices based on seasonal factors">
                  <IconButton size="small">
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Season</TableCell>
                      <TableCell>Beef</TableCell>
                      <TableCell>Mutton</TableCell>
                      <TableCell>Pork</TableCell>
                      <TableCell>Feed</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockSeasonalVariations.map((season) => (
                      <TableRow key={season.season}>
                        <TableCell><strong>{season.season}</strong></TableCell>
                        <TableCell>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            color: season.beef > 0 ? 'success.main' : 'error.main'
                          }}>
                            {season.beef > 0 ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                            {season.beef > 0 ? '+' : ''}{season.beef}%
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            color: season.mutton > 0 ? 'success.main' : 'error.main'
                          }}>
                            {season.mutton > 0 ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                            {season.mutton > 0 ? '+' : ''}{season.mutton}%
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            color: season.pork > 0 ? 'success.main' : 'error.main'
                          }}>
                            {season.pork > 0 ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                            {season.pork > 0 ? '+' : ''}{season.pork}%
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            color: season.feed > 0 ? 'success.main' : 'error.main'
                          }}>
                            {season.feed > 0 ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                            {season.feed > 0 ? '+' : ''}{season.feed}%
                          </Box>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontSize: '0.875rem' }}>
                <strong>Note:</strong> Seasonal variations are based on historical data and may be affected by climate conditions,
                market demand, and other external factors. Current season is highlighted.
              </Typography>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </Box>
  );
};

export default MarketForecast;
