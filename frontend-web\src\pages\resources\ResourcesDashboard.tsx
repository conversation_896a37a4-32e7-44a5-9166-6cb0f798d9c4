import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, Box, Chip, IconButton, Avatar, LinearProgress, useTheme, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,  } from '@mui/material';
import { 
  Add,
  Inventory,
  Build,
  Search,
  FilterList,
  MoreVert,
  ArrowForward,
  CheckCircle,
  Agriculture,
 } from '../../utils/iconImports';
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';
import { EnhancedPieLabelRenderProps } from '../../types/recharts';

// Mock resource data
const mockResourceStats = {
  totalResources: 125,
  availableResources: 98,
  inUseResources: 27,
  maintenanceNeeded: 12,
  resourceUtilization: 78,
  landUsage: {
    veld: 850,
    crops: 250,
    facilities: 100,
    unused: 300
  },
  resourcesByType: {
    equipment: 45,
    vehicles: 15,
    buildings: 25,
    land: 40
  },
  resourcesByStatus: {
    available: 98,
    inUse: 27,
    maintenance: 12,
    retired: 5
  }
};

// Mock resource utilization data
const resourceUtilizationData = [
  { month: 'Jan', equipment: 65, vehicles: 45, buildings: 85, land: 90 },
  { month: 'Feb', equipment: 70, vehicles: 50, buildings: 85, land: 90 },
  { month: 'Mar', equipment: 75, vehicles: 55, buildings: 85, land: 90 },
  { month: 'Apr', equipment: 80, vehicles: 60, buildings: 85, land: 90 },
  { month: 'May', equipment: 85, vehicles: 65, buildings: 85, land: 90 },
  { month: 'Jun', equipment: 90, vehicles: 70, buildings: 85, land: 90 },
];

// Mock resource efficiency data for radar chart
const resourceEfficiencyData = [
  { subject: 'Utilization', A: 85, fullMark: 100 },
  { subject: 'Maintenance', A: 75, fullMark: 100 },
  { subject: 'Cost Efficiency', A: 80, fullMark: 100 },
  { subject: 'Productivity', A: 88, fullMark: 100 },
  { subject: 'Reliability', A: 82, fullMark: 100 },
  { subject: 'Sustainability', A: 70, fullMark: 100 },
];

// Mock recent resource activities
const mockResourceActivities = [
  { id: 'RA001', date: '2023-06-15', resource: 'Tractor #3', activity: 'Maintenance', status: 'completed' },
  { id: 'RA002', date: '2023-06-14', resource: 'North Pasture', activity: 'Rotation', status: 'completed' },
  { id: 'RA003', date: '2023-06-12', resource: 'Storage Barn', activity: 'Inspection', status: 'scheduled' },
  { id: 'RA004', date: '2023-06-10', resource: 'Feed Mixer', activity: 'Repair', status: 'in-progress' },
  { id: 'RA005', date: '2023-06-08', resource: 'Water System', activity: 'Upgrade', status: 'scheduled' },
];

const ResourcesDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  // State for chart interactions
  const [selectedResource, setSelectedResource] = useState<string | null>(null);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // Prepare data for resource type chart
  const resourceTypeData = Object.entries(mockResourceStats.resourcesByType).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  }));

  // Prepare data for land usage chart
  const landUsageData = Object.entries(mockResourceStats.landUsage).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  }));

  return (
    <div className="p-6" style={{ background: 'linear-gradient(to right, #e6f7ff, #d6eeff)' }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
            Resource Management
          </Typography>
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<Add />}
            onClick={() => navigate('/resources/new')}
            sx={{ borderRadius: '8px', boxShadow: '0 4px 10px rgba(0,0,0,0.1)' }}
          >
            Add Resource
          </CustomButton>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <Inventory />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Total Resources
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {mockResourceStats.totalResources}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={100}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <CheckCircle />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Available Resources
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {mockResourceStats.availableResources}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={(mockResourceStats.availableResources / mockResourceStats.totalResources) * 100}
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Build />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Maintenance Needed
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {mockResourceStats.maintenanceNeeded}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={(mockResourceStats.maintenanceNeeded / mockResourceStats.totalResources) * 100}
                    color="warning"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <Agriculture />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Resource Utilization
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {mockResourceStats.resourceUtilization}%
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={mockResourceStats.resourceUtilization}
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Charts and Lists Section */}
      <Grid container spacing={3} mb={4} sx={{ '& .recharts-responsive-container': { minHeight: { xs: 200, sm: 250, md: 300 } } }}>
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" mb={2}>
                  Resources by Type
                </Typography>
                <Box sx={{ height: { xs: 200, sm: 250, md: 300 } }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={resourceTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={(props: EnhancedPieLabelRenderProps) => `${props.name}: ${(props.percent ? (props.percent * 100).toFixed(0) : 0)}%`}
                        onClick={(data) => {
                          setSelectedResource(data.name);
                          setSelectedChart('resourceType');
                          setChartTooltip({
                            show: true,
                            content: `${data.name}: ${data.value} resources (${(data.percent ? (data.percent * 100).toFixed(1) : 0)}%)`
                          });
                        }}
                      >
                        {resourceTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" mb={2}>
                  Land Usage (acres)
                </Typography>
                <Box sx={{ height: { xs: 200, sm: 250, md: 300 } }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={landUsageData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={(props: EnhancedPieLabelRenderProps) => `${props.name}: ${(props.percent ? (props.percent * 100).toFixed(0) : 0)}%`}
                        onClick={(data) => {
                          setSelectedResource(data.name);
                          setSelectedChart('landUsage');
                          setChartTooltip({
                            show: true,
                            content: `${data.name}: ${data.value} acres (${(data.percent ? (data.percent * 100).toFixed(1) : 0)}%)`
                          });
                        }}
                      >
                        {landUsageData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Resource Utilization Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden' }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" mb={2}>
              Resource Utilization Trends
            </Typography>
            <Box sx={{ height: { xs: 250, sm: 300, md: 350 } }}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={resourceUtilizationData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  onClick={(data) => {
                    if (data && data.activePayload && data.activePayload.length > 0) {
                      const payload = data.activePayload[0].payload;
                      setSelectedChart('utilization');
                      setChartTooltip({
                        show: true,
                        content: `${payload.month}: Equipment: ${payload.equipment}%, Vehicles: ${payload.vehicles}%, Buildings: ${payload.buildings}%, Land: ${payload.land}%`
                      });
                    }
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend onClick={(data) => {
                    if (data && data.dataKey) {
                      setSelectedResource(data.dataKey as string);
                      setChartTooltip({
                        show: true,
                        content: `Selected resource type: ${data.dataKey}`
                      });
                    }
                  }} />
                  <Bar dataKey="equipment" fill={theme.palette.primary.main} />
                  <Bar dataKey="vehicles" fill={theme.palette.secondary.main} />
                  <Bar dataKey="buildings" fill={theme.palette.success.main} />
                  <Bar dataKey="land" fill={theme.palette.warning.main} />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      </motion.div>

      {/* Resource Efficiency Radar Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden' }}>
          <CardContent>
            <Typography variant="h6" fontWeight="bold" mb={2}>
              Resource Efficiency Metrics
            </Typography>
            <Box sx={{ height: { xs: 250, sm: 300, md: 350 } }}>
              <ResponsiveContainer width="100%" height="100%">
                <RadarChart
                  cx="50%"
                  cy="50%"
                  outerRadius="80%"
                  data={resourceEfficiencyData}
                  onClick={(data) => {
                    if (data && data.activePayload && data.activePayload.length > 0) {
                      const payload = data.activePayload[0].payload;
                      setSelectedChart('efficiency');
                      setChartTooltip({
                        show: true,
                        content: `${payload.subject}: ${payload.A}% (Target: ${payload.fullMark}%)`
                      });
                    }
                  }}
                >
                  <PolarGrid />
                  <PolarAngleAxis dataKey="subject" />
                  <PolarRadiusAxis angle={30} domain={[0, 100]} />
                  <Radar
                    name="Efficiency"
                    dataKey="A"
                    stroke={theme.palette.primary.main}
                    fill={theme.palette.primary.main}
                    fillOpacity={0.6}
                    activeDot={{
                      r: 8,
                      onClick: (data: any) => {
                        if (data && data.payload) {
                          setSelectedResource(data.payload.subject);
                          setChartTooltip({
                            show: true,
                            content: `${data.payload.subject}: ${data.payload.A}% (Target: ${data.payload.fullMark}%)`
                          });
                        }
                      }
                    }}
                  />
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </Box>
          </CardContent>
        </Card>
      </motion.div>

      {/* Chart Interaction Feedback */}
      {chartTooltip.show && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden', bgcolor: 'info.light' }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" fontWeight="bold">
                  {selectedChart === 'resourceType' ? 'Resource Type Details' :
                   selectedChart === 'landUsage' ? 'Land Usage Details' :
                   selectedChart === 'utilization' ? 'Utilization Details' :
                   'Efficiency Details'}
                </Typography>
                <IconButton size="small" onClick={() => setChartTooltip({ show: false, content: '' })}>
                  <MoreVert />
                </IconButton>
              </Box>
              <Typography variant="body1" mt={1}>
                {chartTooltip.content}
              </Typography>
              {selectedResource && (
                <Box mt={2}>
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    size="small"
                    onClick={() => navigate(`/resources/${selectedChart === 'landUsage' ? 'land' : 'list'}?type=${selectedResource}`)}
                  >
                    View {selectedResource} Details
                  </CustomButton>
                  <Typography variant="body2" color="text.secondary" mt={1}>
                    {selectedChart === 'resourceType' && 'Analyze resource distribution to optimize allocation.'}
                    {selectedChart === 'landUsage' && 'Land usage analysis helps maximize productivity of available acreage.'}
                    {selectedChart === 'utilization' && 'Utilization trends identify opportunities for improved resource management.'}
                    {selectedChart === 'efficiency' && 'Efficiency metrics highlight areas for operational improvements.'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Recent Resource Activities */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.9 }}
      >
        <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden' }}>
          <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              Recent Resource Activities
            </Typography>
            <Box>
              <IconButton color="inherit" size="small">
                <Search />
              </IconButton>
              <IconButton color="inherit" size="small">
                <FilterList />
              </IconButton>
              <IconButton color="inherit" size="small">
                <MoreVert />
              </IconButton>
            </Box>
          </Box>
          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table>
              <TableHead sx={{ bgcolor: 'primary.light' }}>
                <TableRow>
                  <TableCell>ID</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Resource</TableCell>
                  <TableCell>Activity</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockResourceActivities.map((activity) => (
                  <TableRow key={activity.id} hover>
                    <TableCell>{activity.id}</TableCell>
                    <TableCell>{new Date(activity.date).toLocaleDateString()}</TableCell>
                    <TableCell>{activity.resource}</TableCell>
                    <TableCell>{activity.activity}</TableCell>
                    <TableCell>
                      <Chip
                        label={activity.status.charAt(0).toUpperCase() + activity.status.slice(1)}
                        color={
                          activity.status === 'completed' ? 'success' :
                          activity.status === 'in-progress' ? 'warning' :
                          'info'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" onClick={() => navigate(`/resources/activities/${activity.id}`)}>
                        <ArrowForward fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </motion.div>
    </div>
  );
};

export default ResourcesDashboard;
