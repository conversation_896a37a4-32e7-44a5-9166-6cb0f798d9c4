/* AgriIntel Tabbed Landing Page Styles */

/* Tab Panel Background Images - HIGH PRIORITY */
.Mu<PERSON><PERSON><PERSON>-root[role="tabpanel"] {
  position: relative !important;
  min-height: 80vh !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important; /* Changed from fixed for better mobile support */
  background-color: #f5f5f5 !important; /* Light fallback color */
  display: block !important; /* Ensure visibility */
  opacity: 1 !important; /* Ensure visibility */
}

/* Individual Tab Backgrounds - Professional Farming Images - MAXIMUM PRIORITY */
div[role="tabpanel"]:nth-of-type(1),
.Mui<PERSON>ox-root[role="tabpanel"]:nth-of-type(1),
div[id*="tabpanel-0"] {
  /* Home Tab - Cattle Farm */
  background-image: url('../assets/images/landing-backgrounds/home-cattle-farm.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(2),
.Mui<PERSON>ox-root[role="tabpanel"]:nth-of-type(2),
div[id*="tabpanel-1"] {
  /* Features Tab - Smart Farming Technology */
  background-image: url('../assets/images/landing-backgrounds/features-smart-farming.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(3),
.MuiBox-root[role="tabpanel"]:nth-of-type(3),
div[id*="tabpanel-2"] {
  /* Testimonials Tab - Livestock Management */
  background-image: url('../assets/images/landing-backgrounds/testimonials-livestock.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(4),
.MuiBox-root[role="tabpanel"]:nth-of-type(4),
div[id*="tabpanel-3"] {
  /* Pricing Tab - Modern Agriculture */
  background-image: url('../assets/images/landing-backgrounds/pricing-agriculture.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(5),
.MuiBox-root[role="tabpanel"]:nth-of-type(5),
div[id*="tabpanel-4"] {
  /* Trust & Security Tab - Secure Farm Operations */
  background-image: url('../assets/images/landing-backgrounds/trust-security-farm.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(6),
.MuiBox-root[role="tabpanel"]:nth-of-type(6),
div[id*="tabpanel-5"] {
  /* FAQ Tab - Modern Farm Technology */
  background-image: url('../assets/images/landing-backgrounds/faq-modern-farm.jpg') !important;
}

div[role="tabpanel"]:nth-of-type(7),
.MuiBox-root[role="tabpanel"]:nth-of-type(7),
div[id*="tabpanel-6"] {
  /* Contact Tab - Agricultural Innovation */
  background-image: url('../assets/images/landing-backgrounds/contact-agriculture.jpg') !important;
}

/* Background Overlay for Text Readability - VERY LIGHT TO SHOW IMAGES */
.MuiBox-root[role="tabpanel"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(21, 101, 192, 0.1) 0%,
    rgba(46, 125, 50, 0.08) 25%,
    rgba(76, 175, 80, 0.05) 50%,
    rgba(245, 124, 0, 0.08) 75%,
    rgba(46, 125, 50, 0.1) 100%
  );
  background-blend-mode: soft-light;
  z-index: 1;
  pointer-events: none;
}

/* Content Container for proper layering */
.MuiBox-root[role="tabpanel"] > * {
  position: relative;
  z-index: 2;
}

/* Tab panels should be clean without debug styles */

/* Force background images to show - MAXIMUM PRIORITY */
div[role="tabpanel"],
.MuiBox-root[role="tabpanel"],
[role="tabpanel"] {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  min-height: 80vh !important;
  background-color: transparent !important;
}

/* Override any Material-UI background styles */
.MuiBox-root[role="tabpanel"] * {
  background: transparent !important;
}

/* Specific tab panel targeting with IDs */
#agriintel-tabpanel-0 {
  background-image: url('../assets/images/landing-backgrounds/home-cattle-farm.jpg') !important;
}

#agriintel-tabpanel-1 {
  background-image: url('../assets/images/landing-backgrounds/features-smart-farming.jpg') !important;
}

#agriintel-tabpanel-2 {
  background-image: url('../assets/images/landing-backgrounds/testimonials-livestock.jpg') !important;
}

#agriintel-tabpanel-3 {
  background-image: url('../assets/images/landing-backgrounds/pricing-agriculture.jpg') !important;
}

#agriintel-tabpanel-4 {
  background-image: url('../assets/images/landing-backgrounds/trust-security-farm.jpg') !important;
}

#agriintel-tabpanel-5 {
  background-image: url('../assets/images/landing-backgrounds/faq-modern-farm.jpg') !important;
}

#agriintel-tabpanel-6 {
  background-image: url('../assets/images/landing-backgrounds/contact-agriculture.jpg') !important;
}

/* Image Loading Optimization */
.MuiBox-root[role="tabpanel"] {
  background-color: #f5f5f5; /* Fallback color while image loads */
  transition: background-image 0.5s ease-in-out;
}

/* High contrast text for better readability over images */
.MuiBox-root[role="tabpanel"] .agri-hero-title,
.MuiBox-root[role="tabpanel"] .agri-section-title {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  color: #ffffff !important;
}

.MuiBox-root[role="tabpanel"] .agri-hero-subtitle,
.MuiBox-root[role="tabpanel"] .agri-section-subtitle {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.95) !important;
}

/* FIXED Header Styles with Proper Logo Size */
.agri-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 1001 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  backdrop-filter: blur(15px) !important;
  border-bottom: 2px solid rgba(46, 125, 50, 0.3) !important;
  padding: 1rem 0 !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

.agri-header-container {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 0.5rem 0 !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.agri-header-brand {
  flex: 0 0 auto !important;
  /* Remove the problematic transform scale */
}

.agri-header-center {
  flex: 1 !important;
  text-align: center !important;
}

.agri-header-actions {
  flex: 0 0 auto !important;
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
}

/* Enhanced Typography - Larger Tagline */
.agri-tagline {
  color: #2E7D32 !important;
  font-weight: 700 !important;
  font-size: 1.6rem !important;
  text-shadow: 0 2px 4px rgba(46, 125, 50, 0.2) !important;
  letter-spacing: 0.5px !important;
}

.agri-status-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  font-weight: 600 !important;
}

/* Tab Panel Transitions */
.MuiBox-root[role="tabpanel"][hidden] {
  opacity: 0;
  transform: translateY(20px);
}

.MuiBox-root[role="tabpanel"]:not([hidden]) {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease-in-out;
}

/* Tab Navigation Styling */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.MuiTab-root {
  min-width: 140px !important; /* Increased from 120px */
  font-weight: 700 !important;
  font-size: 1.2rem !important; /* Increased from 1rem */
  text-transform: none !important;
  color: #666 !important;
  transition: all 0.3s ease !important;
  padding: 16px 24px !important; /* Increased padding */
  letter-spacing: 0.3px;
}

.MuiTab-root:hover {
  color: #2E7D32 !important;
  background: rgba(46, 125, 50, 0.05);
}

.MuiTab-root.Mui-selected {
  color: #2E7D32 !important;
  font-weight: 700 !important;
}

.MuiTabs-indicator {
  background-color: #4CAF50 !important;
  height: 3px !important;
  border-radius: 2px !important;
}

/* Enhanced Hero Content with Larger Typography */
.agri-hero-content {
  text-align: center;
  padding: 5rem 0;
  max-width: 1200px;
  margin: 0 auto;
}

.agri-hero-title {
  font-size: 4.5rem !important; /* Increased from 3.5rem */
  font-weight: 900 !important;
  color: #ffffff !important; /* White for better contrast over images */
  margin-bottom: 2rem !important;
  line-height: 1.1 !important;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  letter-spacing: -1px;
}

.agri-hero-accent {
  background: linear-gradient(135deg, #4CAF50 0%, #FF9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.8));
}

.agri-hero-subtitle {
  font-size: 1.6rem !important; /* Increased from 1.25rem */
  color: rgba(255, 255, 255, 0.95) !important; /* White for better contrast */
  margin-bottom: 3rem !important;
  line-height: 1.5 !important;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
  font-weight: 500;
}

.agri-hero-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.agri-cta-primary {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.agri-cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
}

.agri-cta-secondary {
  border: 2px solid #4CAF50 !important;
  color: #4CAF50 !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.agri-cta-secondary:hover {
  background: #4CAF50 !important;
  color: white !important;
  transform: translateY(-2px);
}

.agri-hero-trust {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.agri-trust-text {
  color: #666 !important;
  font-weight: 600 !important;
}

/* Enhanced Features Section Typography */
.agri-section-header {
  text-align: center;
  margin-bottom: 5rem;
}

.agri-section-title {
  font-size: 3.2rem !important; /* Increased from 2.5rem */
  font-weight: 800 !important;
  color: #ffffff !important; /* White for better contrast over images */
  margin-bottom: 1.5rem !important;
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
  letter-spacing: -0.5px;
}

.agri-section-subtitle {
  font-size: 1.5rem !important; /* Increased from 1.2rem */
  color: rgba(255, 255, 255, 0.9) !important; /* White for better contrast */
  max-width: 700px;
  margin: 0 auto;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  font-weight: 500;
  line-height: 1.4;
}

.agri-features-grid {
  margin-top: 3rem;
}

.agri-feature-icon {
  text-align: center;
  margin-bottom: 1.5rem;
}

.agri-feature-title {
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  text-align: center;
}

.agri-feature-description {
  color: rgba(255, 255, 255, 0.8) !important;
  text-align: center;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .agri-header-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .agri-header-center {
    order: -1;
  }

  .agri-hero-title {
    font-size: 2.5rem !important;
  }

  .agri-hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .agri-cta-primary,
  .agri-cta-secondary {
    width: 100%;
    max-width: 300px;
  }

  .MuiTabs-root {
    .MuiTab-root {
      min-width: 80px !important;
      font-size: 0.9rem !important;
    }
  }

  /* Mobile Background Adjustments */
  .MuiBox-root[role="tabpanel"] {
    background-attachment: scroll;
    background-size: cover;
    background-position: center center;
  }

  .MuiBox-root[role="tabpanel"]::before {
    background: linear-gradient(
      135deg,
      rgba(21, 101, 192, 0.9) 0%,
      rgba(46, 125, 50, 0.85) 25%,
      rgba(76, 175, 80, 0.8) 50%,
      rgba(245, 124, 0, 0.85) 75%,
      rgba(46, 125, 50, 0.9) 100%
    );
  }
}

@media (max-width: 480px) {
  .agri-hero-title {
    font-size: 2rem !important;
  }

  .agri-hero-subtitle {
    font-size: 1.1rem !important;
  }

  .agri-section-title {
    font-size: 2rem !important;
  }

  /* Extra small screen background adjustments */
  .MuiBox-root[role="tabpanel"] {
    min-height: 50vh;
  }

  .MuiBox-root[role="tabpanel"]::before {
    background: linear-gradient(
      135deg,
      rgba(21, 101, 192, 0.95) 0%,
      rgba(46, 125, 50, 0.9) 25%,
      rgba(76, 175, 80, 0.85) 50%,
      rgba(245, 124, 0, 0.9) 75%,
      rgba(46, 125, 50, 0.95) 100%
    );
  }
}

/* Enhanced Glassmorphism Cards for All Tabs */
.contact-tab-content {
  padding: 3rem 0;
}

.contact-card,
.MuiCard-root {
  background: rgba(255, 255, 255, 0.15) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 16px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

.contact-card:hover,
.MuiCard-root:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.25) !important;
}

/* 2026-Style Interactive Elements */
.agri-cta-primary,
.agri-cta-secondary,
.MuiButton-root {
  position: relative;
  overflow: hidden;
  border-radius: 12px !important;
  font-weight: 700 !important;
  text-transform: none !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.agri-cta-primary::before,
.agri-cta-secondary::before,
.MuiButton-contained::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.agri-cta-primary:hover::before,
.agri-cta-secondary:hover::before,
.MuiButton-contained:hover::before {
  left: 100%;
}

/* Enhanced Feature Cards */
.agri-feature-icon {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.agri-feature-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
}

/* Pricing Cards Enhancement */
.pricing-card {
  background: rgba(255, 255, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 20px !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #1565C0, #4CAF50, #FF9800);
  z-index: 1;
}

.pricing-card:hover {
  transform: translateY(-12px) rotateY(5deg);
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.beta-card {
  border-color: rgba(255, 152, 0, 0.4) !important;
}

.beta-card::before {
  background: linear-gradient(90deg, #FF9800, #F57C00) !important;
}

.professional-card {
  border-color: rgba(76, 175, 80, 0.4) !important;
}

.professional-card::before {
  background: linear-gradient(90deg, #4CAF50, #2E7D32) !important;
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.MuiTab-root:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

.agri-cta-primary:focus,
.agri-cta-secondary:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

/* Modern 2025 Footer Styling */
.landing-footer {
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.95) 0%,
    rgba(46, 125, 50, 0.95) 50%,
    rgba(245, 124, 0, 0.95) 100%);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 4rem 0 2rem;
  margin-top: 4rem;
  position: relative;
  overflow: hidden;
}

.landing-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.3) 100%);
  z-index: 1;
}

.landing-footer .MuiContainer-root {
  position: relative;
  z-index: 2;
}

.footer-brand-title {
  color: #ffffff !important;
  font-weight: 800 !important;
  font-size: 2rem !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 1rem !important;
}

.footer-description {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 1.1rem !important;
  line-height: 1.6;
  margin-bottom: 2rem !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.footer-contact .contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.95) !important;
  font-size: 1rem !important;
}

.footer-title {
  color: #ffffff !important;
  font-weight: 700 !important;
  font-size: 1.3rem !important;
  margin-bottom: 1.5rem !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8) !important;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  display: inline-block;
}

.footer-links a:hover {
  color: #ffffff !important;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  transform: translateX(4px);
}

.footer-divider {
  background: rgba(255, 255, 255, 0.2) !important;
  margin: 3rem 0 2rem !important;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.footer-badges {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.footer-cta-button {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 0.75rem 1.5rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
  text-transform: none !important;
}

.footer-cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
}

/* Stunning 2026 Animations and Effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.3); }
  50% { box-shadow: 0 0 40px rgba(76, 175, 80, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
    transform: scale(1.02);
  }
}

/* Apply animations to interactive elements */
.agri-hero-title {
  animation: float 6s ease-in-out infinite;
}

.agri-feature-icon {
  animation: pulse-glow 4s ease-in-out infinite;
}

.pricing-card {
  animation: float 8s ease-in-out infinite;
}

.pricing-card:nth-child(2) {
  animation-delay: -2s;
}

/* Enhanced Tab Indicators */
.MuiTabs-indicator {
  background: linear-gradient(90deg, #1565C0, #4CAF50, #FF9800) !important;
  height: 4px !important;
  border-radius: 2px !important;
  animation: shimmer 3s ease-in-out infinite;
  background-size: 200% 100%;
}

/* Glassmorphism Navigation */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Enhanced Trust Indicators */
.agri-hero-trust {
  animation: glow 3s ease-in-out infinite;
  padding: 1.5rem;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .footer-badges {
    justify-content: center;
  }

  .footer-brand-title {
    font-size: 1.6rem !important;
  }

  .footer-description {
    font-size: 1rem !important;
  }

  /* Reduce animations on mobile for performance */
  .agri-hero-title,
  .agri-feature-icon,
  .pricing-card {
    animation: none;
  }
}

/* EMERGENCY FIX - COMPLETE OVERRIDE FOR WORKING TABS AND BACKGROUNDS */

/* Fix the main landing container and scaling - TRANSPARENT FOR TAB BACKGROUNDS */
.agri-landing-main {
  background: transparent !important; /* Remove solid gradient to show tab backgrounds */
  min-height: 100vh !important;
  position: relative !important;
  width: 100% !important;
  overflow-x: hidden !important;
  /* Fix scaling issues */
  zoom: 1 !important;
  transform: scale(1) !important;
  transform-origin: top left !important;
}

/* Ensure proper viewport scaling */
html {
  font-size: 16px !important;
  zoom: 1 !important;
}

body {
  zoom: 1 !important;
  transform: scale(1) !important;
  width: 100% !important;
  overflow-x: hidden !important;
}

/* Make tabs visible and functional */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.95) !important;
  border-bottom: 2px solid #4CAF50 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
}

.MuiTab-root {
  min-width: 140px !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  color: #2E7D32 !important;
  text-transform: none !important;
  padding: 16px 24px !important;
}

.MuiTab-root.Mui-selected {
  color: #1565C0 !important;
  background: rgba(76, 175, 80, 0.1) !important;
}

.MuiTabs-indicator {
  background: linear-gradient(90deg, #1565C0, #4CAF50, #FF9800) !important;
  height: 4px !important;
}

/* Fix tab panels with working backgrounds */
.MuiBox-root[role="tabpanel"] {
  min-height: 80vh !important;
  position: relative !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important;
}

/* Individual tab backgrounds - WORKING VERSION */
div[role="tabpanel"][id="agriintel-tabpanel-0"] {
  background-image: url('../assets/images/landing-backgrounds/home-cattle-farm.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-1"] {
  background-image: url('../assets/images/landing-backgrounds/features-smart-farming.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-2"] {
  background-image: url('../assets/images/landing-backgrounds/testimonials-livestock.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-3"] {
  background-image: url('../assets/images/landing-backgrounds/pricing-agriculture.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-4"] {
  background-image: url('../assets/images/landing-backgrounds/trust-security-farm.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-5"] {
  background-image: url('../assets/images/landing-backgrounds/faq-modern-farm.jpg') !important;
}

div[role="tabpanel"][id="agriintel-tabpanel-6"] {
  background-image: url('../assets/images/landing-backgrounds/contact-agriculture.jpg') !important;
}

/* Alternative selectors for better targeting */
.MuiBox-root[role="tabpanel"]:nth-of-type(1),
div[id="agriintel-tabpanel-0"] {
  background-image: url('../assets/images/landing-backgrounds/home-cattle-farm.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(2),
div[id="agriintel-tabpanel-1"] {
  background-image: url('../assets/images/landing-backgrounds/features-smart-farming.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(3),
div[id="agriintel-tabpanel-2"] {
  background-image: url('../assets/images/landing-backgrounds/testimonials-livestock.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(4),
div[id="agriintel-tabpanel-3"] {
  background-image: url('../assets/images/landing-backgrounds/pricing-agriculture.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(5),
div[id="agriintel-tabpanel-4"] {
  background-image: url('../assets/images/landing-backgrounds/trust-security-farm.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(6),
div[id="agriintel-tabpanel-5"] {
  background-image: url('../assets/images/landing-backgrounds/faq-modern-farm.jpg') !important;
}

.MuiBox-root[role="tabpanel"]:nth-of-type(7),
div[id="agriintel-tabpanel-6"] {
  background-image: url('../assets/images/landing-backgrounds/contact-agriculture.jpg') !important;
}

/* Subtle overlay for text readability */
.MuiBox-root[role="tabpanel"]::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.3) !important;
  z-index: 1 !important;
  pointer-events: none !important;
}

/* Ensure content is above overlay */
.MuiBox-root[role="tabpanel"] > * {
  position: relative !important;
  z-index: 2 !important;
}

/* Fix text colors for visibility */
.MuiBox-root[role="tabpanel"] .agri-hero-title,
.MuiBox-root[role="tabpanel"] .agri-section-title {
  color: #ffffff !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

.MuiBox-root[role="tabpanel"] .agri-hero-subtitle,
.MuiBox-root[role="tabpanel"] .agri-section-subtitle {
  color: rgba(255, 255, 255, 0.9) !important;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7) !important;
}
