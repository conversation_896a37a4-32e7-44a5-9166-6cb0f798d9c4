/* AgriIntel Tabbed Landing Page Styles */

/* Header Styles */
.agri-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.agri-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.agri-header-brand {
  flex: 0 0 auto;
}

.agri-header-center {
  flex: 1;
  text-align: center;
}

.agri-header-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.agri-tagline {
  color: #2E7D32 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
}

.agri-status-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  font-weight: 600 !important;
}

/* Tab Panel Transitions */
.MuiBox-root[role="tabpanel"] {
  transition: all 0.3s ease-in-out;
  min-height: 60vh;
}

.MuiBox-root[role="tabpanel"][hidden] {
  opacity: 0;
  transform: translateY(20px);
}

.MuiBox-root[role="tabpanel"]:not([hidden]) {
  opacity: 1;
  transform: translateY(0);
}

/* Tab Navigation Styling */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.MuiTab-root {
  min-width: 120px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-transform: none !important;
  color: #666 !important;
  transition: all 0.3s ease !important;
}

.MuiTab-root:hover {
  color: #2E7D32 !important;
  background: rgba(46, 125, 50, 0.05);
}

.MuiTab-root.Mui-selected {
  color: #2E7D32 !important;
  font-weight: 700 !important;
}

.MuiTabs-indicator {
  background-color: #4CAF50 !important;
  height: 3px !important;
  border-radius: 2px !important;
}

/* Hero Content Styling */
.agri-hero-content {
  text-align: center;
  padding: 4rem 0;
  max-width: 1000px;
  margin: 0 auto;
}

.agri-hero-title {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  color: #2E7D32 !important;
  margin-bottom: 1.5rem !important;
  line-height: 1.2 !important;
}

.agri-hero-accent {
  background: linear-gradient(135deg, #4CAF50 0%, #FF9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.agri-hero-subtitle {
  font-size: 1.25rem !important;
  color: #666 !important;
  margin-bottom: 3rem !important;
  line-height: 1.6 !important;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.agri-hero-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.agri-cta-primary {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.agri-cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
}

.agri-cta-secondary {
  border: 2px solid #4CAF50 !important;
  color: #4CAF50 !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.agri-cta-secondary:hover {
  background: #4CAF50 !important;
  color: white !important;
  transform: translateY(-2px);
}

.agri-hero-trust {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.agri-trust-text {
  color: #666 !important;
  font-weight: 600 !important;
}

/* Features Section */
.agri-section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.agri-section-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #2E7D32 !important;
  margin-bottom: 1rem !important;
}

.agri-section-subtitle {
  font-size: 1.2rem !important;
  color: #666 !important;
  max-width: 600px;
  margin: 0 auto;
}

.agri-features-grid {
  margin-top: 3rem;
}

.agri-feature-icon {
  text-align: center;
  margin-bottom: 1.5rem;
}

.agri-feature-title {
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  text-align: center;
}

.agri-feature-description {
  color: rgba(255, 255, 255, 0.8) !important;
  text-align: center;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .agri-header-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .agri-header-center {
    order: -1;
  }
  
  .agri-hero-title {
    font-size: 2.5rem !important;
  }
  
  .agri-hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .agri-cta-primary,
  .agri-cta-secondary {
    width: 100%;
    max-width: 300px;
  }
  
  .MuiTabs-root {
    .MuiTab-root {
      min-width: 80px !important;
      font-size: 0.9rem !important;
    }
  }
}

@media (max-width: 480px) {
  .agri-hero-title {
    font-size: 2rem !important;
  }
  
  .agri-hero-subtitle {
    font-size: 1.1rem !important;
  }
  
  .agri-section-title {
    font-size: 2rem !important;
  }
}

/* Contact Tab Specific Styles */
.contact-tab-content {
  padding: 2rem 0;
}

.contact-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(46, 125, 50, 0.1) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.15) !important;
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.MuiTab-root:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

.agri-cta-primary:focus,
.agri-cta-secondary:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}
