/* AgriIntel Tabbed Landing Page Styles */

/* Tab Panel Background Images */
.Mui<PERSON>ox-root[role="tabpanel"] {
  position: relative;
  min-height: 60vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Individual Tab Backgrounds */
.Mui<PERSON>ox-root[role="tabpanel"]:nth-of-type(1) {
  /* Home Tab - High-tech pasture system */
  background-image: url('../assets/images/landing-backgrounds/high-tech-pasture.jpg');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(2) {
  /* Features Tab - Agricultural technology */
  background-image: url('../assets/images/landing-backgrounds/agricultural-technology.jpg');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(3) {
  /* Testimonials Tab - IoT Agriculture */
  background-image: url('../assets/images/landing-backgrounds/iot-agriculture.png');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(4) {
  /* Pricing Tab - Smart farming technology */
  background-image: url('../assets/images/landing-backgrounds/smart-farming-tech.png');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(5) {
  /* Trust & Security Tab - Environmental assessment */
  background-image: url('../assets/images/landing-backgrounds/environmental-assessment.jpg');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(6) {
  /* FAQ Tab - Smart farming infographics */
  background-image: url('../assets/images/landing-backgrounds/smart-farming-infographics.jpg');
}

.MuiBox-root[role="tabpanel"]:nth-of-type(7) {
  /* Contact Tab - IoT in Agriculture */
  background-image: url('../assets/images/landing-backgrounds/iot-in-agriculture.png');
}

/* Background Overlay for Text Readability */
.MuiBox-root[role="tabpanel"]::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(21, 101, 192, 0.85) 0%,
    rgba(46, 125, 50, 0.8) 25%,
    rgba(76, 175, 80, 0.75) 50%,
    rgba(245, 124, 0, 0.8) 75%,
    rgba(46, 125, 50, 0.85) 100%
  );
  background-blend-mode: multiply;
  z-index: 1;
  pointer-events: none;
}

/* Content Container for proper layering */
.MuiBox-root[role="tabpanel"] > * {
  position: relative;
  z-index: 2;
}

/* Image Loading Optimization */
.MuiBox-root[role="tabpanel"] {
  background-color: #f5f5f5; /* Fallback color while image loads */
  transition: background-image 0.5s ease-in-out;
}

/* High contrast text for better readability over images */
.MuiBox-root[role="tabpanel"] .agri-hero-title,
.MuiBox-root[role="tabpanel"] .agri-section-title {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  color: #ffffff !important;
}

.MuiBox-root[role="tabpanel"] .agri-hero-subtitle,
.MuiBox-root[role="tabpanel"] .agri-section-subtitle {
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Header Styles */
.agri-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.agri-header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.agri-header-brand {
  flex: 0 0 auto;
}

.agri-header-center {
  flex: 1;
  text-align: center;
}

.agri-header-actions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.agri-tagline {
  color: #2E7D32 !important;
  font-weight: 600 !important;
  font-size: 1.1rem !important;
}

.agri-status-badge {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%) !important;
  color: white !important;
  font-weight: 600 !important;
}

/* Tab Panel Transitions */
.MuiBox-root[role="tabpanel"][hidden] {
  opacity: 0;
  transform: translateY(20px);
}

.MuiBox-root[role="tabpanel"]:not([hidden]) {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease-in-out;
}

/* Tab Navigation Styling */
.MuiTabs-root {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.MuiTab-root {
  min-width: 120px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-transform: none !important;
  color: #666 !important;
  transition: all 0.3s ease !important;
}

.MuiTab-root:hover {
  color: #2E7D32 !important;
  background: rgba(46, 125, 50, 0.05);
}

.MuiTab-root.Mui-selected {
  color: #2E7D32 !important;
  font-weight: 700 !important;
}

.MuiTabs-indicator {
  background-color: #4CAF50 !important;
  height: 3px !important;
  border-radius: 2px !important;
}

/* Hero Content Styling */
.agri-hero-content {
  text-align: center;
  padding: 4rem 0;
  max-width: 1000px;
  margin: 0 auto;
}

.agri-hero-title {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  color: #2E7D32 !important;
  margin-bottom: 1.5rem !important;
  line-height: 1.2 !important;
}

.agri-hero-accent {
  background: linear-gradient(135deg, #4CAF50 0%, #FF9800 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.agri-hero-subtitle {
  font-size: 1.25rem !important;
  color: #666 !important;
  margin-bottom: 3rem !important;
  line-height: 1.6 !important;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.agri-hero-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.agri-cta-primary {
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%) !important;
  color: white !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.agri-cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
}

.agri-cta-secondary {
  border: 2px solid #4CAF50 !important;
  color: #4CAF50 !important;
  font-weight: 700 !important;
  padding: 1rem 2rem !important;
  font-size: 1.1rem !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.agri-cta-secondary:hover {
  background: #4CAF50 !important;
  color: white !important;
  transform: translateY(-2px);
}

.agri-hero-trust {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.agri-trust-text {
  color: #666 !important;
  font-weight: 600 !important;
}

/* Features Section */
.agri-section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.agri-section-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #2E7D32 !important;
  margin-bottom: 1rem !important;
}

.agri-section-subtitle {
  font-size: 1.2rem !important;
  color: #666 !important;
  max-width: 600px;
  margin: 0 auto;
}

.agri-features-grid {
  margin-top: 3rem;
}

.agri-feature-icon {
  text-align: center;
  margin-bottom: 1.5rem;
}

.agri-feature-title {
  color: white !important;
  font-weight: 700 !important;
  margin-bottom: 1rem !important;
  text-align: center;
}

.agri-feature-description {
  color: rgba(255, 255, 255, 0.8) !important;
  text-align: center;
  line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
  .agri-header-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .agri-header-center {
    order: -1;
  }

  .agri-hero-title {
    font-size: 2.5rem !important;
  }

  .agri-hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .agri-cta-primary,
  .agri-cta-secondary {
    width: 100%;
    max-width: 300px;
  }

  .MuiTabs-root {
    .MuiTab-root {
      min-width: 80px !important;
      font-size: 0.9rem !important;
    }
  }

  /* Mobile Background Adjustments */
  .MuiBox-root[role="tabpanel"] {
    background-attachment: scroll;
    background-size: cover;
    background-position: center center;
  }

  .MuiBox-root[role="tabpanel"]::before {
    background: linear-gradient(
      135deg,
      rgba(21, 101, 192, 0.9) 0%,
      rgba(46, 125, 50, 0.85) 25%,
      rgba(76, 175, 80, 0.8) 50%,
      rgba(245, 124, 0, 0.85) 75%,
      rgba(46, 125, 50, 0.9) 100%
    );
  }
}

@media (max-width: 480px) {
  .agri-hero-title {
    font-size: 2rem !important;
  }

  .agri-hero-subtitle {
    font-size: 1.1rem !important;
  }

  .agri-section-title {
    font-size: 2rem !important;
  }

  /* Extra small screen background adjustments */
  .MuiBox-root[role="tabpanel"] {
    min-height: 50vh;
  }

  .MuiBox-root[role="tabpanel"]::before {
    background: linear-gradient(
      135deg,
      rgba(21, 101, 192, 0.95) 0%,
      rgba(46, 125, 50, 0.9) 25%,
      rgba(76, 175, 80, 0.85) 50%,
      rgba(245, 124, 0, 0.9) 75%,
      rgba(46, 125, 50, 0.95) 100%
    );
  }
}

/* Contact Tab Specific Styles */
.contact-tab-content {
  padding: 2rem 0;
}

.contact-card {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(46, 125, 50, 0.1) !important;
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
}

.contact-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 30px rgba(46, 125, 50, 0.15) !important;
}

/* Accessibility Improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.MuiTab-root:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

.agri-cta-primary:focus,
.agri-cta-secondary:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}
