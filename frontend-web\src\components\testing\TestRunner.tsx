/**
 * Test Runner Component
 * Comprehensive testing interface for Beta version quality assurance
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert,
  Grid,
  Paper,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Refresh,
  CheckCircle,
  Error,
  Warning,
  ExpandMore,
  BugReport,
  Speed,
  Security,
  Navigation,
  Web,
  Assessment,
  Download
} from '@mui/icons-material';
import {
  runAllTests,
  runAuthenticationTests,
  runAPITests,
  runNavigationTests,
  runFormTests,
  runUITests,
  runPerformanceTests,
  testFramework,
  type TestSuite,
  type TestResult
} from '../../utils/testingFramework';

const TestRunner: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestSuite[]>([]);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [progress, setProgress] = useState(0);

  const testSuites = [
    { name: 'Authentication', icon: <Security />, runner: runAuthenticationTests },
    { name: 'API Endpoints', icon: <Web />, runner: runAPITests },
    { name: 'Navigation', icon: <Navigation />, runner: runNavigationTests },
    { name: 'Forms', icon: <BugReport />, runner: runFormTests },
    { name: 'UI/UX', icon: <Assessment />, runner: runUITests },
    { name: 'Performance', icon: <Speed />, runner: runPerformanceTests }
  ];

  const runAllTestSuites = async () => {
    setIsRunning(true);
    setProgress(0);
    setResults([]);

    try {
      const allResults = await runAllTests();
      setResults(allResults);
    } catch (error) {
      console.error('Error running tests:', error);
    } finally {
      setIsRunning(false);
      setProgress(100);
      setCurrentTest('');
    }
  };

  const runSingleSuite = async (suiteName: string, runner: () => Promise<TestSuite> | TestSuite) => {
    setIsRunning(true);
    setCurrentTest(suiteName);
    
    try {
      const result = await runner();
      setResults(prev => {
        const filtered = prev.filter(r => r.name !== suiteName);
        return [...filtered, result];
      });
    } catch (error) {
      console.error(`Error running ${suiteName} tests:`, error);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle color="success" />;
      case 'fail': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      default: return <Warning />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'success';
      case 'fail': return 'error';
      case 'warning': return 'warning';
      default: return 'default';
    }
  };

  const getSummary = () => {
    if (results.length === 0) return null;
    
    return testFramework.generateSummary();
  };

  const exportResults = () => {
    const summary = getSummary();
    const exportData = {
      timestamp: new Date().toISOString(),
      summary,
      results
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agriintel-test-results-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const summary = getSummary();

  return (
    <Box sx={{ p: 3 }}>
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title="AgriIntel Beta Testing Suite"
          subheader="Comprehensive quality assurance testing for production readiness"
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Export Results">
                <IconButton onClick={exportResults} disabled={results.length === 0}>
                  <Download />
                </IconButton>
              </Tooltip>
              <Button
                variant="contained"
                startIcon={isRunning ? <Stop /> : <PlayArrow />}
                onClick={runAllTestSuites}
                disabled={isRunning}
              >
                {isRunning ? 'Running...' : 'Run All Tests'}
              </Button>
            </Box>
          }
        />
        <CardContent>
          {isRunning && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                {currentTest ? `Running: ${currentTest}` : 'Initializing tests...'}
              </Typography>
              <LinearProgress />
            </Box>
          )}

          {summary && (
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="primary">
                    {summary.totalTests}
                  </Typography>
                  <Typography variant="caption">Total Tests</Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="success.main">
                    {summary.totalPassed}
                  </Typography>
                  <Typography variant="caption">Passed</Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="error.main">
                    {summary.totalFailed}
                  </Typography>
                  <Typography variant="caption">Failed</Typography>
                </Paper>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="warning.main">
                    {summary.totalWarnings}
                  </Typography>
                  <Typography variant="caption">Warnings</Typography>
                </Paper>
              </Grid>
            </Grid>
          )}

          {summary && (
            <Alert 
              severity={summary.passRate >= 90 ? 'success' : summary.passRate >= 70 ? 'warning' : 'error'}
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle2">
                Overall Pass Rate: {summary.passRate}% 
                {summary.passRate >= 90 && ' - Excellent! Ready for production.'}
                {summary.passRate >= 70 && summary.passRate < 90 && ' - Good, but some improvements needed.'}
                {summary.passRate < 70 && ' - Needs significant improvements before production.'}
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        {testSuites.map((suite) => {
          const suiteResult = results.find(r => r.name.includes(suite.name));
          return (
            <Grid item xs={12} sm={6} md={4} key={suite.name}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {suite.icon}
                    <Typography variant="h6" sx={{ ml: 1, flexGrow: 1 }}>
                      {suite.name}
                    </Typography>
                    {suiteResult && (
                      <Chip
                        size="small"
                        label={`${suiteResult.passed}/${suiteResult.tests.length}`}
                        color={suiteResult.failed === 0 ? 'success' : 'error'}
                      />
                    )}
                  </Box>
                  
                  <Button
                    fullWidth
                    variant="outlined"
                    onClick={() => runSingleSuite(suite.name, suite.runner)}
                    disabled={isRunning}
                    startIcon={<PlayArrow />}
                  >
                    Run {suite.name} Tests
                  </Button>
                  
                  {suiteResult && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="caption" color="text.secondary">
                        Duration: {suiteResult.duration}ms
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {results.length > 0 && (
        <Card>
          <CardHeader title="Test Results" />
          <CardContent>
            {results.map((suite, index) => (
              <Accordion key={index}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Typography variant="h6" sx={{ flexGrow: 1 }}>
                      {suite.name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mr: 2 }}>
                      <Chip
                        size="small"
                        label={`${suite.passed} passed`}
                        color="success"
                        variant="outlined"
                      />
                      {suite.failed > 0 && (
                        <Chip
                          size="small"
                          label={`${suite.failed} failed`}
                          color="error"
                          variant="outlined"
                        />
                      )}
                      {suite.warnings > 0 && (
                        <Chip
                          size="small"
                          label={`${suite.warnings} warnings`}
                          color="warning"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </Box>
                </AccordionSummary>
                <AccordionDetails>
                  <List dense>
                    {suite.tests.map((test, testIndex) => (
                      <ListItem key={testIndex}>
                        <ListItemIcon>
                          {getStatusIcon(test.status)}
                        </ListItemIcon>
                        <ListItemText
                          primary={test.testName}
                          secondary={test.message}
                        />
                        <Chip
                          size="small"
                          label={test.status}
                          color={getStatusColor(test.status) as any}
                          variant="outlined"
                        />
                      </ListItem>
                    ))}
                  </List>
                </AccordionDetails>
              </Accordion>
            ))}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default TestRunner;
