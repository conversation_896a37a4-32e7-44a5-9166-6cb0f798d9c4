import { useRef, useEffect } from 'react';
import { applyTabSelectionFixesToElement } from '../utils/applyTabSelectionFixes';

/**
 * Custom hook to make report items selectable
 * This hook applies selection fixes to the element
 * @returns A ref to attach to the element
 */
export const useReportItemSelection = () => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      // Apply selection fixes to the element
      applyTabSelectionFixesToElement(elementRef.current);

      // Apply fixes again after a delay to ensure all styles are applied
      const timer = setTimeout(() => {
        if (elementRef.current) {
          applyTabSelectionFixesToElement(elementRef.current);
        }
      }, 100);

      // Apply fixes one more time after a longer delay
      const timer2 = setTimeout(() => {
        if (elementRef.current) {
          applyTabSelectionFixesToElement(elementRef.current);

          // Make sure all child elements are selectable
          const clickableElements = elementRef.current.querySelectorAll('button, a, [role="button"]');
          clickableElements.forEach(element => {
            (element as HTMLElement).style.position = 'relative';
            (element as HTMLElement).style.zIndex = '10';
            (element as HTMLElement).style.pointerEvents = 'auto';
            (element as HTMLElement).style.cursor = 'pointer';
          });
        }
      }, 500);

      return () => {
        clearTimeout(timer);
        clearTimeout(timer2);
      };
    }
  }, []);

  return elementRef;
};

export default useReportItemSelection;
