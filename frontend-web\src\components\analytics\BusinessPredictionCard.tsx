import React from 'react';
import { Box, Typography, Grid, useTheme, alpha, LinearProgress } from '@mui/material';
import { motion } from 'framer-motion';
import BusinessAnalyticsCard from './BusinessAnalyticsCard';
import BusinessAnalyticsButton from './BusinessAnalyticsButton';

interface BusinessPredictionProps {
  id: string;
  name: string;
  description: string;
  timeframe: string;
  currentValue: string | number;
  predictedValue: string | number;
  confidenceLevel: number;
  category: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  suggestedAction?: string;
}

interface BusinessPredictionCardProps {
  prediction: BusinessPredictionProps;
  onViewDetails?: (prediction: BusinessPredictionProps) => void;
  loading?: boolean;
  delay?: number;
  icon?: React.ReactNode;
}

/**
 * A card component for displaying business predictions
 */
const BusinessPredictionCard: React.FC<BusinessPredictionCardProps> = ({
  prediction,
  onViewDetails,
  loading = false,
  delay = 0,
  icon
}) => {
  const theme = useTheme();

  const getImpactColor = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.7) return theme.palette.success.main;
    if (confidence >= 0.4) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const impactColor = getImpactColor(prediction.impact);
  const confidenceColor = getConfidenceColor(prediction.confidenceLevel);

  return (
    <BusinessAnalyticsCard
      title={prediction.name}
      subtitle={`Prediction timeframe: ${prediction.timeframe}`}
      icon={icon}
      loading={loading}
      delay={delay}
      actionLabel="View Details"
      onAction={() => onViewDetails && onViewDetails(prediction)}
    >
      <Box sx={{ mt: 2 }}>
        <Typography variant="body1" sx={{ mb: 3 }}>
          {prediction.description}
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Current Value
              </Typography>
              <Typography variant="h5" fontWeight="bold" color="primary">
                {prediction.currentValue}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Predicted Value
              </Typography>
              <Typography variant="h5" fontWeight="bold" color={impactColor}>
                {prediction.predictedValue}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Confidence Level
            </Typography>
            <Typography variant="body2" fontWeight="medium" color={confidenceColor}>
              {(prediction.confidenceLevel * 100).toFixed(0)}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={prediction.confidenceLevel * 100}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: alpha(confidenceColor, 0.2),
              '& .MuiLinearProgress-bar': {
                backgroundColor: confidenceColor,
                borderRadius: 4
              }
            }}
          />
        </Box>

        {prediction.actionable && prediction.suggestedAction && (
          <Box
            sx={{
              mt: 3,
              p: 2,
              borderRadius: 2,
              backgroundColor: alpha(theme.palette.info.main, 0.1),
              border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
            }}
          >
            <Typography variant="body2" fontWeight="bold" color="info.main" gutterBottom>
              Suggested Action:
            </Typography>
            <Typography variant="body2">
              {prediction.suggestedAction}
            </Typography>
          </Box>
        )}
      </Box>
    </BusinessAnalyticsCard>
  );
};

export default BusinessPredictionCard;
