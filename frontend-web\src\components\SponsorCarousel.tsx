import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';

interface SponsorCarouselProps {
  interval?: number; // Time in milliseconds between slides
  title?: string;
}

// List of sponsor images from the manually added UXUI LiveStock folder
const sponsorImages = [
  '/images/sponsors/landbank.png',        // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/agri-research-council.png', // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/deeds-department.png',  // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/namc.png',             // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/ppecb.png',            // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/sponsor1.jpg',         // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/sponsor2.jpg',         // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/sponsor3.jpg',         // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/sponsor4.jpg',         // Using manually added image from UXUI LiveStock folder
  '/images/sponsors/sponsor5.jpg',         // Using manually added image from UXUI LiveStock folder
];

// Sponsor links will be created in the component to use translations

const SponsorCarousel: React.FC<SponsorCarouselProps> = ({
  interval = 10000, // Default to 10 seconds
  title
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoaded, setIsLoaded] = useState<boolean[]>(Array(sponsorImages.length).fill(false));
  const theme = useTheme();
  const { translate } = useLanguage();

  // Create sponsor links with translations
  const sponsorLinks = [
    { name: translate('resources.landbank'), url: 'https://www.landbank.co.za/' },
    { name: translate('resources.arc'), url: 'https://www.arc.agric.za/' },
    { name: translate('resources.deeds'), url: 'http://www.deeds.gov.za/' },
    { name: translate('resources.namc'), url: 'https://www.namc.co.za/' },
    { name: translate('resources.ppecb'), url: 'https://ppecb.com/' },
    { name: translate('dashboard.sponsor', { number: 1 }), url: '#' },
    { name: translate('dashboard.sponsor', { number: 2 }), url: '#' },
    { name: translate('dashboard.sponsor', { number: 3 }), url: '#' },
    { name: translate('dashboard.sponsor', { number: 4 }), url: '#' },
    { name: translate('dashboard.sponsor', { number: 5 }), url: '#' },
  ];

  // Function to handle image load events
  const handleImageLoad = (index: number) => {
    const newLoadedState = [...isLoaded];
    newLoadedState[index] = true;
    setIsLoaded(newLoadedState);
  };

  // Auto-rotate through sponsors
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsorImages.length);
    }, interval);

    return () => clearInterval(timer);
  }, [interval]);

  // Preload all images
  useEffect(() => {
    sponsorImages.forEach((src, index) => {
      const img = new Image();
      img.src = src;
      img.onload = () => handleImageLoad(index);
    });
  }, []);

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        mb: 3,
        borderRadius: 2,
        backgroundColor: alpha(theme.palette.primary.main, 0.05),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        overflow: 'hidden'
      }}
    >
      <Typography
        variant="h6"
        sx={{
          mb: 2,
          textAlign: 'center',
          color: theme.palette.primary.main,
          fontWeight: 'medium'
        }}
      >
        {title}
      </Typography>

      <Box sx={{ position: 'relative', height: 120, display: 'flex', justifyContent: 'center' }}>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <a
              href={sponsorLinks[currentIndex % sponsorLinks.length].url}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textDecoration: 'none'
              }}
            >
              <Box
                sx={{
                  height: 80,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1
                }}
              >
                <img
                  src={sponsorImages[currentIndex]}
                  alt={translate('dashboard.sponsor_alt', { number: currentIndex + 1 })}
                  style={{
                    maxHeight: '100%',
                    maxWidth: '100%',
                    objectFit: 'contain',
                    filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.1))'
                  }}
                  onError={(e) => {
                    // Fallback if image doesn't load
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/sponsors/placeholder.png'; // Using manually added image from UXUI LiveStock folder
                  }}
                />
              </Box>
              <Typography
                variant="body2"
                sx={{
                  color: theme.palette.text.secondary,
                  textAlign: 'center',
                  fontWeight: 'medium'
                }}
              >
                {sponsorLinks[currentIndex % sponsorLinks.length].name}
              </Typography>
            </a>
          </motion.div>
        </AnimatePresence>
      </Box>

      {/* Indicator dots */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
        {sponsorImages.map((_, index) => (
          <Box
            key={index}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              mx: 0.5,
              backgroundColor: index === currentIndex
                ? theme.palette.primary.main
                : alpha(theme.palette.primary.main, 0.3),
              transition: 'background-color 0.3s ease',
              cursor: 'pointer'
            }}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </Box>
    </Paper>
  );
};

export default SponsorCarousel;
