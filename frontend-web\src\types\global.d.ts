// Global type definitions

// Declare process/browser module
declare module 'process/browser' {
  const process: NodeJS.Process;
  export default process;
}

// Extend Window interface
interface Window {
  fs: any;
  net: any;
  tls: any;
  dns: any;
  child_process: any;
  [key: string]: any;
}

// Resource Link type
interface ResourceLink {
  id: string;
  name: string;
  url: string;
  description: string;
  category: 'financial' | 'education' | 'government' | 'regulatory' | string;
  logoUrl?: string;
}

// User type with strict role definition
interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  password?: string;
  confirmPassword?: string;
  role: 'admin' | 'manager' | 'viewer' | 'staff' | 'user' | string;
  status: 'active' | 'inactive' | 'pending' | string;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
  permissions?: string[];
}

// Feeding Record type
interface FeedingRecord {
  id: string;
  animalId: string;
  animalTagNumber: string;
  animalName: string;
  animalGroupId?: string;
  animalGroup?: string;
  feedType: string;
  quantity: number;
  unit: string;
  feedingDate: string;
  feedingTime: string;
  location: string;
  cost: number;
  notes: string;
  animals: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Health Record type
interface HealthRecord {
  id: string;
  animalId: string;
  animalTagNumber: string;
  animalName: string;
  recordType: string;
  description: string;
  date: string;
  performedBy: string;
  status: string;
  cost: number;
  notes: string;
  followUpDate: string;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date;
  updatedAt: Date;
}

// Breeding Record type
interface BreedingRecord {
  id: string;
  femaleId: string;
  femaleTagNumber: string;
  femaleName: string;
  maleId: string;
  maleTagNumber: string;
  maleName: string;
  date: string;
  status: string;
  method: 'natural' | 'artificial';
  location: string;
  notes: string;
  expectedBirthDate: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
}

// Animal type
interface Animal {
  id: string;
  tagNumber: string;
  name: string;
  type: string;
  species: string;
  breed: string;
  gender: string;
  birthDate: string;
  weight: number;
  status: string;
  healthStatus: string;
  location: string;
  purchaseDate: string | null;
  purchasePrice: number | null;
  notes: string;
  rfidTag?: string;
  imageUrl?: string;
  parentId?: string;
  parentTagNumber?: string;
  sireId?: string;
  damId?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Module Header Props
interface ModuleHeaderProps {
  title: string;
  subtitle: string;
  module: string;
  submodule: string;
  actionLabel?: string;
  actionIcon?: React.ReactNode;
  onAction?: () => void | Promise<void>;
  disabled?: boolean;
}
