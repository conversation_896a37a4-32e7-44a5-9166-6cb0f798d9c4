export interface Transaction {
  id: string;
  date: Date;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  description: string;
  paymentMethod: 'cash' | 'bank' | 'credit';
  status: 'pending' | 'completed' | 'cancelled';
  relatedEntity?: {
    type: 'animal' | 'feed' | 'equipment' | 'labor' | 'other';
    id: string;
  };
  attachments?: string[]; // URLs to receipts/invoices
  notes: string;
}

export interface Budget {
  id: string;
  period: {
    start: Date;
    end: Date;
  };
  category: string;
  plannedAmount: number;
  actualAmount: number;
  variance: number;
  status: 'active' | 'closed';
}

export interface SalesRecord {
  id: string;
  date: Date;
  customer: Customer;
  items: SaleItem[];
  totalAmount: number;
  paymentStatus: 'pending' | 'partial' | 'completed';
  deliveryStatus: 'pending' | 'in-transit' | 'delivered';
  invoice: string; // Invoice number
}

export interface Customer {
  id: string;
  name: string;
  type: 'individual' | 'business';
  contact: {
    phone: string;
    email: string;
    address: string;
  };
  paymentTerms: string;
  creditLimit?: number;
}

export interface SaleItem {
  id: string;
  type: 'livestock' | 'product';
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export const mockTransactions: Transaction[] = [
  {
    id: 'TR001',
    date: new Date('2024-01-15'),
    type: 'expense',
    category: 'Feed',
    amount: 25000,
    description: 'Monthly lucerne and feed purchase',
    paymentMethod: 'bank',
    status: 'completed',
    relatedEntity: {
      type: 'feed',
      id: 'FD001'
    },
    notes: 'Regular monthly feed order from Afgri'
  },
  {
    id: 'TR002',
    date: new Date('2024-02-05'),
    type: 'expense',
    category: 'Veterinary',
    amount: 8500,
    description: 'Quarterly herd health check',
    paymentMethod: 'bank',
    status: 'completed',
    relatedEntity: {
      type: 'other',
      id: 'VET001'
    },
    notes: 'Dr. van Niekerk - vaccination and health checks'
  },
  {
    id: 'TR003',
    date: new Date('2024-02-20'),
    type: 'income',
    category: 'Livestock Sale',
    amount: 85000,
    description: 'Sale of 5 Bonsmara cattle',
    paymentMethod: 'bank',
    status: 'completed',
    relatedEntity: {
      type: 'animal',
      id: 'ANM045'
    },
    notes: 'Sold to Vryburg Abattoir'
  },
  {
    id: 'TR004',
    date: new Date('2024-03-01'),
    type: 'expense',
    category: 'Equipment',
    amount: 12500,
    description: 'New dipping equipment',
    paymentMethod: 'bank',
    status: 'completed',
    relatedEntity: {
      type: 'equipment',
      id: 'EQ008'
    },
    notes: 'Purchased from Kaap Agri'
  }
];

export const mockFinancialRecords = mockTransactions;

export const mockFinancialStats = {
  totalIncome: 359000,
  totalExpenses: 46000,
  netProfit: 313000,
  cashOnHand: 425000,
  accountsReceivable: 150000,
  accountsPayable: 35000,
  assetValue: 2500000,
  liabilities: 750000,
  equity: 1750000,
  profitMargin: 87.2,
  returnOnInvestment: 12.5,
  debtToEquityRatio: 0.43,
  currentRatio: 2.8,
  quickRatio: 1.9,
  inventoryTurnover: 4.2,
  monthlyRevenue: [120000, 85000, 154000, 135000, 98000, 112000, 145000, 132000, 128000, 142000, 156000, 168000],
  monthlyExpenses: [45000, 38000, 42000, 40000, 44000, 39000, 41000, 43000, 40000, 42000, 45000, 48000],
  categoryBreakdown: {
    income: {
      'Livestock Sales': 65,
      'Dairy Products': 20,
      'Crop Sales': 10,
      'Other': 5
    },
    expenses: {
      'Feed': 35,
      'Labor': 25,
      'Veterinary': 15,
      'Equipment': 10,
      'Utilities': 8,
      'Other': 7
    }
  },
  recentTransactions: [
    { id: 'TR003', date: '2024-02-20', description: 'Sale of 5 Bonsmara cattle', amount: 85000, type: 'income' },
    { id: 'TR004', date: '2024-03-01', description: 'New dipping equipment', amount: -12500, type: 'expense' },
    { id: 'TR002', date: '2024-02-05', description: 'Quarterly herd health check', amount: -8500, type: 'expense' },
    { id: 'TR001', date: '2024-01-15', description: 'Monthly lucerne and feed purchase', amount: -25000, type: 'expense' }
  ]
};

export const mockSalesRecords: SalesRecord[] = [
  {
    id: 'SL001',
    date: new Date('2024-01-20'),
    customer: {
      id: 'CUST001',
      name: 'Beefmaster Kimberley',
      type: 'business',
      contact: {
        phone: '************',
        email: '<EMAIL>',
        address: '10 Industrial Rd, Kimberley'
      },
      paymentTerms: 'Net 30'
    },
    items: [
      {
        id: 'SI001',
        type: 'livestock',
        description: 'Premium Bonsmara cattle',
        quantity: 5,
        unitPrice: 17000,
        total: 85000
      }
    ],
    totalAmount: 85000,
    paymentStatus: 'completed',
    deliveryStatus: 'delivered',
    invoice: 'INV2024001'
  },
  {
    id: 'SL002',
    date: new Date('2024-02-15'),
    customer: {
      id: 'CUST002',
      name: 'Cavalier Abattoir',
      type: 'business',
      contact: {
        phone: '************',
        email: '<EMAIL>',
        address: '25 Slaughter Rd, Cullinan'
      },
      paymentTerms: 'Net 15'
    },
    items: [
      {
        id: 'SI002',
        type: 'livestock',
        description: 'A-grade Nguni cattle',
        quantity: 8,
        unitPrice: 15500,
        total: 124000
      }
    ],
    totalAmount: 124000,
    paymentStatus: 'completed',
    deliveryStatus: 'delivered',
    invoice: 'INV2024002'
  },
  {
    id: 'SL003',
    date: new Date('2024-03-05'),
    customer: {
      id: 'CUST003',
      name: 'Karan Beef',
      type: 'business',
      contact: {
        phone: '************',
        email: '<EMAIL>',
        address: 'Farm Knoppiesfontein, Heidelberg'
      },
      paymentTerms: 'Net 30'
    },
    items: [
      {
        id: 'SI003',
        type: 'livestock',
        description: 'Feedlot-ready Simbra calves',
        quantity: 12,
        unitPrice: 12500,
        total: 150000
      }
    ],
    totalAmount: 150000,
    paymentStatus: 'pending',
    deliveryStatus: 'in-transit',
    invoice: 'INV2024003'
  }
];