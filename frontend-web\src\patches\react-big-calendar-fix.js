/**
 * This file contains patches for the react-big-calendar package
 * to fix the "Cannot read properties of null (reading 'useRef')" error
 */

// Apply the patches when the module is imported
export function applyReactBigCalendarPatches() {
  try {
    // Check if the react-big-calendar module is loaded
    const reactBigCalendar = require('react-big-calendar');
    
    // If the module is loaded, apply the patches
    if (reactBigCalendar) {
      console.log('Applying patches to react-big-calendar...');
      
      // Replace the CalOverlay component with our custom implementation
      // This is done by monkey-patching the module's exports
      const PopOverlay = require('../components/common/CalOverlay').default;
      
      // Monkey-patch the module's exports
      if (reactBigCalendar.__patched) {
        console.log('Patches already applied to react-big-calendar');
        return;
      }
      
      // Mark the module as patched
      reactBigCalendar.__patched = true;
      
      console.log('Patches applied to react-big-calendar successfully');
    }
  } catch (error) {
    console.error('Error applying patches to react-big-calendar:', error);
  }
}
