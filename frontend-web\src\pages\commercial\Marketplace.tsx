import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Grid,
  Box,
  Typography,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  CardActions,
  Chip,
  Rating,
  useTheme,
  alpha
} from '@mui/material';
import CustomButton from '../../components/common/CustomButton';
import { motion } from 'framer-motion';
import {
  Search,
  FilterList,
  ShoppingCart,
  Store,
  Add,
  Visibility,
  AttachMoney,
  TrendingUp,
  Group
} from '../../utils/iconImports';
import { ModuleHeader, ProductCard, FeaturedDealCard } from '../../components/common';
import MarketForecast from '../../components/modules/commercial/MarketForecast';
import MarketplaceListingDetail from '../../components/modules/commercial/MarketplaceListingDetail';
import MarketplaceSuppliers from '../../components/modules/commercial/MarketplaceSuppliers';
import { getConsistentBackground } from '../../utils/backgroundThemes';

// Mock data for marketplace listings
const mockListings = [
  {
    id: 1,
    title: 'Premium Cattle Feed',
    description: 'High-quality feed for optimal cattle growth and health',
    price: 2500,
    unit: 'per ton',
    supplier: 'FarmFresh Supplies',
    rating: 4.5,
    category: 'Feed',
    inStock: true
  },
  {
    id: 2,
    title: 'Livestock Medication Bundle',
    description: 'Essential medications for cattle health maintenance',
    price: 1800,
    unit: 'per package',
    supplier: 'VetMed Solutions',
    rating: 4.2,
    category: 'Health',
    inStock: true
  },
  {
    id: 3,
    title: 'Automatic Water Troughs',
    description: 'Self-filling water troughs with filtration system',
    price: 3500,
    unit: 'each',
    supplier: 'AgriTech Innovations',
    rating: 4.7,
    category: 'Equipment',
    inStock: false
  },
  {
    id: 4,
    title: 'Breeding Bulls - Angus',
    description: 'Purebred Angus bulls with excellent genetics',
    price: 45000,
    unit: 'each',
    supplier: 'Premium Livestock Breeders',
    rating: 4.9,
    image: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    category: 'Livestock',
    inStock: true
  },
  {
    id: 5,
    title: 'Pasture Seed Mix',
    description: 'High-yield pasture seed mix for South African climate',
    price: 1200,
    unit: 'per 25kg',
    supplier: 'GreenFields Agri',
    rating: 4.3,
    category: 'Feed',
    inStock: true
  },
  {
    id: 6,
    title: 'Livestock Transport Trailer',
    description: 'Heavy-duty trailer for safe livestock transport',
    price: 75000,
    unit: 'each',
    supplier: 'FarmEquip Pro',
    rating: 4.6,
    category: 'Equipment',
    inStock: true
  },
];

// Featured deals for the top section
const featuredDeals = [
  {
    id: 1,
    title: 'Premium Feed',
    subtitle: 'Special discount on bulk orders',
    discount: '15% OFF',
    module: 'commercial',
    uniqueId: 'deal-1',
    icon: <AttachMoney />
  },
  {
    id: 2,
    title: 'Breeding Stock',
    subtitle: 'Quality genetics at competitive prices',
    discount: 'FROM R35,000',
    module: 'commercial',
    uniqueId: 'deal-2',
    icon: <Store />
  },
  {
    id: 3,
    title: 'Farm Equipment',
    subtitle: 'Essential tools for modern farming',
    discount: 'UP TO 20% OFF',
    module: 'commercial',
    uniqueId: 'deal-3',
    icon: <ShoppingCart />
  },
];

const categories = ['All', 'Feed', 'Health', 'Equipment', 'Livestock', 'Services'];

const Marketplace: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentView, setCurrentView] = useState<'listings' | 'forecast' | 'suppliers' | 'detail'>('listings');
  const [selectedListingId, setSelectedListingId] = useState<number | null>(null);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const filteredListings = mockListings.filter(listing => {
    if (activeTab === 0) return true; // All categories
    return listing.category === categories[activeTab];
  }).filter(listing => {
    if (!searchQuery) return true;
    return listing.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
           listing.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
           listing.supplier.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <Box>
      <ModuleHeader
        title="Livestock Marketplace"
        subtitle="Browse and purchase quality livestock, feed, equipment, and services from trusted suppliers"
        module="commercial"
        submodule="marketplace"
        actionLabel="My Orders"
        actionIcon={<ShoppingCart />}
        onAction={() => navigate('/dashboard/commercial/orders')}
      />

      {/* View Toggle Buttons */}
      <Box sx={{ px: 3, pt: 2, display: 'flex', justifyContent: 'flex-end' }}>
        <CustomButton
          variant={currentView === 'listings' ? 'contained' : 'outlined'}
          onClick={() => setCurrentView('listings')}
          style={{ marginRight: '8px' }}
        >
          Marketplace
        </CustomButton>
        <CustomButton
          variant={currentView === 'forecast' ? 'contained' : 'outlined'}
          startIcon={<TrendingUp />}
          onClick={() => setCurrentView('forecast')}
          style={{ marginRight: '8px' }}
        >
          Market Forecast
        </CustomButton>
        <CustomButton
          variant={currentView === 'suppliers' ? 'contained' : 'outlined'}
          startIcon={<Group />}
          onClick={() => setCurrentView('suppliers')}
        >
          Suppliers
        </CustomButton>
      </Box>

      {currentView === 'listings' ? (
        <Box sx={{ px: 3, pb: 5 }}>
        {/* Featured Deals */}
        <Box sx={{ mb: 5 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Featured Deals
          </Typography>
          <Grid container spacing={3}>
            {featuredDeals.map((deal, index) => (
              <Grid item xs={12} md={4} key={deal.id}>
                <FeaturedDealCard
                  id={String(deal.id)}
                  title={deal.title}
                  subtitle={deal.subtitle}
                  discount={deal.discount}
                  backgroundImage={getConsistentBackground(`featured-${deal.id}`)}
                  icon={deal.icon}
                  onViewOffers={() => console.log(`View offers for ${deal.title}`)}
                  primaryColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                />
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Search and Filter */}
        <Box sx={{ mb: 4, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
          <TextField
            placeholder="Search marketplace..."
            variant="outlined"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flex: 1 }}
          />
          <CustomButton
            variant="outlined"
            startIcon={<FilterList />}
            style={{ minWidth: '120px' }}
          >
            Filter
          </CustomButton>
          <CustomButton
            variant="contained"
            startIcon={<Add />}
            style={{ minWidth: '180px' }}
          >
            Post Listing
          </CustomButton>
        </Box>

        {/* Category Tabs */}
        <Box sx={{ mb: 4, borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            {categories.map((category, index) => (
              <Tab key={index} label={category} />
            ))}
          </Tabs>
        </Box>

        {/* Listings */}
        <Grid container spacing={3}>
          {filteredListings.length > 0 ? (
            filteredListings.map((listing, index) => (
              <Grid item xs={12} sm={6} md={4} key={listing.id}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                >
                  <ProductCard
                    id={String(listing.id)}
                    title={listing.title}
                    description={listing.description}
                    price={listing.price}
                    currency="R"
                    rating={listing.rating}
                    reviewCount={Math.floor(Math.random() * 50) + 5}
                    backgroundImage={getConsistentBackground(String(listing.id) || 'default')}
                    inStock={listing.inStock}
                    discountPercentage={index % 3 === 0 ? 10 : undefined}
                    onAddToCart={() => console.log(`Added ${listing.title} to cart`)}
                    onViewDetails={() => {
                      setSelectedListingId(listing.id);
                      setCurrentView('detail');
                    }}
                    primaryColor={theme.palette.primary.main}
                    secondaryColor={theme.palette.primary.dark}
                  />
                </motion.div>
              </Grid>
            ))
          ) : (
            <Box sx={{ width: '100%', py: 5, textAlign: 'center' }}>
              <Typography variant="h6" color="text.secondary">
                No listings found matching your criteria
              </Typography>
              <CustomButton
                variant="outlined"
                style={{ marginTop: '16px' }}
                onClick={() => {
                  setSearchQuery('');
                  setActiveTab(0);
                }}
              >
                Clear Filters
              </CustomButton>
            </Box>
          )}
        </Grid>
      </Box>
      ) : currentView === 'forecast' ? (
        <MarketForecast />
      ) : currentView === 'suppliers' ? (
        <MarketplaceSuppliers />
      ) : currentView === 'detail' && selectedListingId ? (
        <MarketplaceListingDetail
          listingId={selectedListingId}
          onBack={() => setCurrentView('listings')}
        />
      ) : (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6">Content not available</Typography>
          <CustomButton
            variant="contained"
            onClick={() => setCurrentView('listings')}
            style={{ marginTop: '16px' }}
          >
            Back to Marketplace
          </CustomButton>
        </Box>
      )}
    </Box>
  );
};

export default Marketplace;