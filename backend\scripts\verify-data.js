/**
 * Verify Data Script
 * 
 * This script checks if data exists in the MongoDB database
 */

const { MongoClient } = require('mongodb');

// MongoDB connection string
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

// Function to verify data in MongoDB
async function verifyData() {
  let client;

  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    
    // Check collections
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections in database:`);
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    // Check data in each collection
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      console.log(`Collection ${collection.name} has ${count} documents`);
      
      if (count > 0) {
        // Show a sample document
        const sample = await db.collection(collection.name).findOne();
        console.log(`Sample document from ${collection.name}:`);
        console.log(JSON.stringify(sample, null, 2).substring(0, 500) + '...');
      }
    }
    
    console.log('Data verification completed');
  } catch (error) {
    console.error('Error verifying data:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the verification function
verifyData()
  .then(() => {
    console.log('Data verification process completed');
  })
  .catch((error) => {
    console.error('Error in data verification process:', error);
  });
