import React from 'react';
import { Box, Chip, CircularProgress, Tooltip } from '@mui/material';
import { CheckCircle, Error, Warning } from '@mui/icons-material';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';

/**
 * Component to display MongoDB connection status
 */
const MongoDbStatus: React.FC = () => {
  const { isConnected, isLoading, error } = useMongoDb();

  if (isLoading) {
    return (
      <Tooltip title="Connecting to MongoDB...">
        <Chip
          icon={<CircularProgress size={16} color="inherit" />}
          label="Connecting to DB"
          color="default"
          size="small"
        />
      </Tooltip>
    );
  }

  if (error) {
    return (
      <Tooltip title={`MongoDB Error: ${error.message}`}>
        <Chip
          icon={<Error fontSize="small" />}
          label="DB Error"
          color="error"
          size="small"
        />
      </Tooltip>
    );
  }

  if (isConnected) {
    return (
      <Tooltip title="Connected to MongoDB">
        <Chip
          icon={<CheckCircle fontSize="small" />}
          label="DB Connected"
          color="success"
          size="small"
        />
      </Tooltip>
    );
  }

  return (
    <Tooltip title="MongoDB connection issue">
      <Chip
        icon={<Warning fontSize="small" />}
        label="DB Disconnected"
        color="warning"
        size="small"
      />
    </Tooltip>
  );
};

export default MongoDbStatus;
