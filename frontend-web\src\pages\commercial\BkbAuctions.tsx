import React from 'react';
import { Box, Typography, Paper, Container } from '@mui/material';
import { BkbAuctionProvider } from '../../contexts/commercial/BkbAuctionContext';
import BkbAuctionCalendar from '../../components/commercial/BkbAuctionCalendar';
import { ModuleContainer } from '../../components/common';
import { useLanguage } from '../../contexts/LanguageContext';

const BkbAuctions: React.FC = () => {
  const { translate } = useLanguage();

  return (
    <ModuleContainer module="commercial">
      <Container maxWidth="xl">
        <Box sx={{ py: 3 }}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="text.secondary" paragraph>
                {translate ? translate('commercial.bkb.description', {
                  fallback: 'BKB is a leading agricultural services company in South Africa that hosts regular livestock auctions. View upcoming auctions and plan your participation.'
                }) : 'BKB is a leading agricultural services company in South Africa that hosts regular livestock auctions. View upcoming auctions and plan your participation.'}
              </Typography>
            </Box>

            <BkbAuctionProvider>
              <BkbAuctionCalendar />
            </BkbAuctionProvider>
          </Paper>
        </Box>
      </Container>
    </ModuleContainer>
  );
};

export default BkbAuctions;
