/**
 * This CSS file provides a direct fix for the MUI Button component in the Reports module.
 * It overrides the MUI Button styles to prevent the "Cannot read properties of undefined (reading 'dark')" error.
 */

/* Override the MUI Button styles in the Reports module */
.reports-module .MuiButton-root {
  /* Base styles */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  box-sizing: border-box !important;
  outline: 0 !important;
  border: 0 !important;
  margin: 0 !important;
  cursor: pointer !important;
  user-select: none !important;
  vertical-align: middle !important;
  text-decoration: none !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  line-height: 1.75 !important;
  letter-spacing: 0.02857em !important;
  text-transform: uppercase !important;
  min-width: 64px !important;
  padding: 6px 16px !important;
  border-radius: 4px !important;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms !important;
  color: rgba(0, 0, 0, 0.87) !important;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif !important;
}

/* Contained button styles */
.reports-module .MuiButton-contained {
  color: #fff !important;
  background-color: #3AA99F !important;
  box-shadow: 0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12) !important;
}

.reports-module .MuiButton-contained:hover {
  background-color: #2A8A82 !important;
  box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12) !important;
}

.reports-module .MuiButton-containedPrimary {
  color: #fff !important;
  background-color: #3AA99F !important;
}

.reports-module .MuiButton-containedPrimary:hover {
  background-color: #2A8A82 !important;
}

.reports-module .MuiButton-containedSecondary {
  color: #fff !important;
  background-color: #38B2AC !important;
}

.reports-module .MuiButton-containedSecondary:hover {
  background-color: #2C8A84 !important;
}

/* Outlined button styles */
.reports-module .MuiButton-outlined {
  border: 1px solid rgba(0, 0, 0, 0.23) !important;
  padding: 5px 15px !important;
  color: #3AA99F !important;
  background-color: transparent !important;
}

.reports-module .MuiButton-outlined:hover {
  background-color: rgba(58, 169, 159, 0.04) !important;
  border: 1px solid #3AA99F !important;
}

.reports-module .MuiButton-outlinedPrimary {
  color: #3AA99F !important;
  border: 1px solid rgba(58, 169, 159, 0.5) !important;
}

.reports-module .MuiButton-outlinedPrimary:hover {
  border: 1px solid #3AA99F !important;
  background-color: rgba(58, 169, 159, 0.04) !important;
}

.reports-module .MuiButton-outlinedSecondary {
  color: #38B2AC !important;
  border: 1px solid rgba(56, 178, 172, 0.5) !important;
}

.reports-module .MuiButton-outlinedSecondary:hover {
  border: 1px solid #38B2AC !important;
  background-color: rgba(56, 178, 172, 0.04) !important;
}

/* Text button styles */
.reports-module .MuiButton-text {
  padding: 6px 8px !important;
  color: #3AA99F !important;
  background-color: transparent !important;
}

.reports-module .MuiButton-text:hover {
  background-color: rgba(58, 169, 159, 0.04) !important;
}

.reports-module .MuiButton-textPrimary {
  color: #3AA99F !important;
}

.reports-module .MuiButton-textPrimary:hover {
  background-color: rgba(58, 169, 159, 0.04) !important;
}

.reports-module .MuiButton-textSecondary {
  color: #38B2AC !important;
}

.reports-module .MuiButton-textSecondary:hover {
  background-color: rgba(56, 178, 172, 0.04) !important;
}

/* Disabled button styles */
.reports-module .MuiButton-root.Mui-disabled {
  color: rgba(0, 0, 0, 0.26) !important;
  cursor: default !important;
  pointer-events: none !important;
}

.reports-module .MuiButton-contained.Mui-disabled {
  color: rgba(0, 0, 0, 0.26) !important;
  background-color: rgba(0, 0, 0, 0.12) !important;
  box-shadow: none !important;
}

.reports-module .MuiButton-outlined.Mui-disabled {
  border: 1px solid rgba(0, 0, 0, 0.12) !important;
}

/* Size variants */
.reports-module .MuiButton-sizeSmall {
  padding: 4px 10px !important;
  font-size: 0.8125rem !important;
}

.reports-module .MuiButton-sizeLarge {
  padding: 8px 22px !important;
  font-size: 0.9375rem !important;
}

/* Full width */
.reports-module .MuiButton-fullWidth {
  width: 100% !important;
}

/* Button label */
.reports-module .MuiButton-label {
  width: 100% !important;
  display: inherit !important;
  align-items: inherit !important;
  justify-content: inherit !important;
}

/* Start and end icons */
.reports-module .MuiButton-startIcon {
  display: inherit !important;
  margin-right: 8px !important;
  margin-left: -4px !important;
}

.reports-module .MuiButton-startIcon.MuiButton-iconSizeSmall {
  margin-left: -2px !important;
}

.reports-module .MuiButton-endIcon {
  display: inherit !important;
  margin-right: -4px !important;
  margin-left: 8px !important;
}

.reports-module .MuiButton-endIcon.MuiButton-iconSizeSmall {
  margin-right: -2px !important;
}

.reports-module .MuiButton-iconSizeSmall > *:first-child {
  font-size: 18px !important;
}

.reports-module .MuiButton-iconSizeMedium > *:first-child {
  font-size: 20px !important;
}

.reports-module .MuiButton-iconSizeLarge > *:first-child {
  font-size: 22px !important;
}
