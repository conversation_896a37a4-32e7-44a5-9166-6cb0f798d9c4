<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AgriIntel Image Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #1A1F3A 0%, #0A0E27 50%, #2D1B69 100%);
            color: white;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .image-card img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .image-card h3 {
            margin: 0 0 10px 0;
            color: #4FBEB4;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background: #10B981; }
        .error { background: #EF4444; }
        .loading { background: #F59E0B; }
    </style>
</head>
<body>
    <h1>🌾 AgriIntel Image Loading Test</h1>
    <p>Testing all image assets used in the application...</p>
    
    <div class="image-grid" id="imageGrid">
        <!-- Images will be loaded here -->
    </div>

    <script>
        const images = [
            // Dashboard
            { path: '/images/dashboard/main-dashboard.jpg', name: 'Main Dashboard' },
            { path: '/images/dashboard/Maindash.jpg', name: 'Dashboard Alt' },
            
            // Animals
            { path: '/images/modules/animals/cattle-1.jpeg', name: 'Cattle 1' },
            { path: '/images/modules/animals/cattle-2.avif', name: 'Cattle 2' },
            { path: '/images/modules/animals/cattle-3.jpeg', name: 'Cattle 3' },
            { path: '/images/modules/animals/cattle-4.jpeg', name: 'Cattle 4' },
            { path: '/images/modules/animals/cattle-5.avif', name: 'Cattle 5' },
            
            // Health
            { path: '/images/modules/health/health-main.png', name: 'Health Main' },
            { path: '/images/modules/health/veterinary-1.jpg', name: 'Veterinary 1' },
            { path: '/images/modules/health/veterinary-2.jpg', name: 'Veterinary 2' },
            { path: '/images/modules/health/veterinary-3.jpg', name: 'Veterinary 3' },
            
            // Breeding
            { path: '/images/modules/breeding/breeding-main.png', name: 'Breeding Main' },
            
            // Feeding
            { path: '/images/modules/feeding/feed-main.jpeg', name: 'Feed Main' },
            { path: '/images/modules/feeding/feed-2.jpeg', name: 'Feed 2' },
            { path: '/images/modules/feeding/feed-sheep.jpeg', name: 'Feed Sheep' },
            
            // Commercial
            { path: '/images/modules/commercial/commercial-1.jpeg', name: 'Commercial 1' },
            { path: '/images/modules/commercial/commercial-2.jpeg', name: 'Commercial 2' },
            { path: '/images/modules/commercial/commercial-3.jpeg', name: 'Commercial 3' },
            
            // RFID
            { path: '/images/modules/rfid/rfid-1.webp', name: 'RFID 1' },
            { path: '/images/modules/rfid/rfid-2.jpg', name: 'RFID 2' },
            { path: '/images/modules/rfid/rfid-3.jpg', name: 'RFID 3' },
            { path: '/images/modules/rfid/rfid-4.jpg', name: 'RFID 4' },
            
            // Login
            { path: '/images/login/login-bg.jpeg', name: 'Login Background' }
        ];

        function createImageCard(imageInfo) {
            const card = document.createElement('div');
            card.className = 'image-card';
            
            const img = document.createElement('img');
            const title = document.createElement('h3');
            const status = document.createElement('div');
            const path = document.createElement('p');
            
            title.textContent = imageInfo.name;
            path.textContent = imageInfo.path;
            path.style.fontSize = '12px';
            path.style.color = '#ccc';
            
            status.className = 'status loading';
            status.textContent = 'Loading...';
            
            img.onload = function() {
                status.className = 'status success';
                status.textContent = '✅ Loaded';
            };
            
            img.onerror = function() {
                status.className = 'status error';
                status.textContent = '❌ Failed';
                img.style.display = 'none';
            };
            
            img.src = imageInfo.path;
            img.alt = imageInfo.name;
            
            card.appendChild(img);
            card.appendChild(title);
            card.appendChild(status);
            card.appendChild(path);
            
            return card;
        }

        // Load all images
        const grid = document.getElementById('imageGrid');
        images.forEach(imageInfo => {
            grid.appendChild(createImageCard(imageInfo));
        });
    </script>
</body>
</html>
