import { notificationService, NotificationType, NotificationPriority } from './notificationService';
import { notificationHistoryService } from './notificationHistoryService';
import { format, addMinutes } from 'date-fns';

/**
 * Test service for scheduling and triggering real notifications
 */
export const testNotificationService = {
  /**
   * Schedule a test notification to be sent at a specific time
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param birthDateTime Expected birth date and time
   * @param notificationTime Time to send the notification
   * @param recipient Recipient information
   * @param notificationTypes Types of notifications to send
   * @returns Promise that resolves when the notification is scheduled
   */
  scheduleTestNotification: async (
    animalId: string,
    animalName: string,
    birthDateTime: Date,
    notificationTime: Date,
    recipient: {
      name: string;
      phone?: string;
      email?: string;
    },
    notificationTypes: NotificationType[]
  ): Promise<{ success: boolean; scheduledTime: Date; message: string }> => {
    try {
      // Calculate time until notification should be sent
      const now = new Date();
      const timeUntilNotification = notificationTime.getTime() - now.getTime();
      
      // If notification time is in the past, send immediately
      if (timeUntilNotification <= 0) {
        console.log('Notification time is in the past, sending immediately');
        await testNotificationService.sendTestNotification(
          animalId,
          animalName,
          birthDateTime,
          recipient,
          notificationTypes
        );
        return {
          success: true,
          scheduledTime: now,
          message: `Notification for ${animalName} sent immediately`
        };
      }
      
      // Schedule the notification
      console.log(`Scheduling notification for ${format(notificationTime, 'yyyy-MM-dd HH:mm:ss')}`);
      console.log(`Time until notification: ${Math.floor(timeUntilNotification / 1000 / 60)} minutes`);
      
      // Set a timeout to send the notification at the specified time
      setTimeout(async () => {
        await testNotificationService.sendTestNotification(
          animalId,
          animalName,
          birthDateTime,
          recipient,
          notificationTypes
        );
      }, timeUntilNotification);
      
      return {
        success: true,
        scheduledTime: notificationTime,
        message: `Notification for ${animalName} scheduled for ${format(notificationTime, 'HH:mm')}`
      };
    } catch (error) {
      console.error('Error scheduling test notification:', error);
      return {
        success: false,
        scheduledTime: new Date(),
        message: `Failed to schedule notification: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  },
  
  /**
   * Send a test notification immediately
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param birthDateTime Expected birth date and time
   * @param recipient Recipient information
   * @param notificationTypes Types of notifications to send
   * @returns Promise that resolves when all notifications are sent
   */
  sendTestNotification: async (
    animalId: string,
    animalName: string,
    birthDateTime: Date,
    recipient: {
      name: string;
      phone?: string;
      email?: string;
    },
    notificationTypes: NotificationType[]
  ): Promise<Array<{ type: NotificationType; result: { success: boolean; notificationId?: string; error?: string } }>> => {
    // Calculate hours until birth
    const now = new Date();
    const hoursUntilBirth = (birthDateTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    const daysRemaining = Math.ceil(hoursUntilBirth / 24);
    
    // Determine priority based on hours remaining
    let priority: NotificationPriority = 'low';
    if (hoursUntilBirth <= 12) {
      priority = 'critical';
    } else if (hoursUntilBirth <= 48) {
      priority = 'high';
    } else if (hoursUntilBirth <= 72) {
      priority = 'medium';
    }
    
    // Create the message
    const birthTimeStr = format(birthDateTime, 'HH:mm');
    const birthDateStr = format(birthDateTime, 'dd/MM/yyyy');
    
    let message = `URGENT BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth TOMORROW at ${birthTimeStr}. This is the FINAL REMINDER. Please prepare immediately.`;
    
    if (hoursUntilBirth <= 12) {
      message = `CRITICAL BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth TODAY at ${birthTimeStr}. IMMEDIATE ATTENTION REQUIRED.`;
    } else if (hoursUntilBirth <= 24) {
      message = `URGENT BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth TOMORROW at ${birthTimeStr}. This is the FINAL REMINDER. Please prepare immediately.`;
    } else if (hoursUntilBirth <= 48) {
      message = `IMPORTANT BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in 2 days (${birthDateStr} at ${birthTimeStr}). Please begin preparations.`;
    }
    
    // Create email subject
    const subject = `${priority === 'critical' ? 'CRITICAL: ' : priority === 'high' ? 'URGENT: ' : ''}Birth Alert for ${animalName} - Expected ${format(birthDateTime, 'dd/MM/yyyy HH:mm')}`;
    
    // Send notifications using each specified method
    const results: Array<{ type: NotificationType; result: { success: boolean; notificationId?: string; error?: string } }> = [];
    
    for (const type of notificationTypes) {
      let result;
      
      switch (type) {
        case 'sms':
          if (recipient.phone) {
            console.log(`Sending SMS to ${recipient.phone} with priority ${priority}:`, message);
            result = await notificationService.sendSMS(recipient.phone, message, priority);
            results.push({ type, result });
            
            // Add to notification history
            notificationHistoryService.addEntry({
              timestamp: new Date(),
              animalId,
              animalName,
              notificationType: type,
              recipient: {
                name: recipient.name,
                contact: recipient.phone
              },
              message,
              priority,
              status: result.success ? 'success' : 'failed',
              error: result.success ? undefined : result.error
            });
          }
          break;
        case 'voice':
          if (recipient.phone) {
            console.log(`Initiating voice call to ${recipient.phone} with priority ${priority}:`, message);
            result = await notificationService.sendVoiceCall(recipient.phone, message, priority);
            results.push({ type, result });
            
            // Add to notification history
            notificationHistoryService.addEntry({
              timestamp: new Date(),
              animalId,
              animalName,
              notificationType: type,
              recipient: {
                name: recipient.name,
                contact: recipient.phone
              },
              message,
              priority,
              status: result.success ? 'success' : 'failed',
              error: result.success ? undefined : result.error
            });
          }
          break;
        case 'whatsapp':
          if (recipient.phone) {
            console.log(`Sending WhatsApp message to ${recipient.phone} with priority ${priority}:`, message);
            result = await notificationService.sendWhatsApp(recipient.phone, message, priority);
            results.push({ type, result });
            
            // Add to notification history
            notificationHistoryService.addEntry({
              timestamp: new Date(),
              animalId,
              animalName,
              notificationType: type,
              recipient: {
                name: recipient.name,
                contact: recipient.phone
              },
              message,
              priority,
              status: result.success ? 'success' : 'failed',
              error: result.success ? undefined : result.error
            });
          }
          break;
        case 'email':
          if (recipient.email) {
            console.log(`Sending email to ${recipient.email} with priority ${priority}:`, subject, message);
            result = await notificationService.sendEmail(recipient.email, subject, message, priority);
            results.push({ type, result });
            
            // Add to notification history
            notificationHistoryService.addEntry({
              timestamp: new Date(),
              animalId,
              animalName,
              notificationType: type,
              recipient: {
                name: recipient.name,
                contact: recipient.email
              },
              message,
              priority,
              status: result.success ? 'success' : 'failed',
              error: result.success ? undefined : result.error
            });
          }
          break;
        case 'app':
          console.log(`Sending in-app notification to ${recipient.name} with priority ${priority}:`, message);
          result = await notificationService.sendAppNotification(recipient.name, message, priority);
          results.push({ type, result });
          
          // Add to notification history
          notificationHistoryService.addEntry({
            timestamp: new Date(),
            animalId,
            animalName,
            notificationType: type,
            recipient: {
              name: recipient.name,
              contact: 'In-App'
            },
            message,
            priority,
            status: result.success ? 'success' : 'failed',
            error: result.success ? undefined : result.error
          });
          break;
      }
    }
    
    return results;
  }
};

export default testNotificationService;
