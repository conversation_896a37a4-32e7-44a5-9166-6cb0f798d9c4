/**
 * Business Analysis Schema Definitions
 *
 * This file defines the schema structure for Business Analysis MongoDB collections
 * to ensure consistency across the application.
 */

// Business KPI Schema
const businessKpiSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  value: { type: Number, required: true },
  target: { type: Number, required: true },
  unit: { type: String, required: true },
  status: { type: String, required: true, enum: ['success', 'warning', 'danger'] },
  trend: { type: Number, required: true },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Business Prediction Schema
const businessPredictionSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  currentValue: { type: Number, required: true },
  predictedValue: { type: Number, required: true },
  confidence: { type: Number, required: true },
  timeframe: { type: String, required: true },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Business Insight Schema
const businessInsightSchema = {
  id: { type: String, required: true, unique: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  impact: { type: String, required: true, enum: ['high', 'medium', 'low'] },
  actionable: { type: Boolean, required: true },
  suggestedAction: { type: String },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Business Risk Schema
const businessRiskSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  probability: { type: Number, required: true },
  impact: { type: Number, required: true },
  severity: { type: Number, required: true },
  mitigationStrategy: { type: String, required: true },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Business Opportunity Schema
const businessOpportunitySchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  potentialValue: { type: Number, required: true },
  feasibility: { type: Number, required: true },
  opportunityScore: { type: Number, required: true },
  implementationStrategy: { type: String, required: true },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Strategic Goal Schema
const strategicGoalSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  targetDate: { type: Date, required: true },
  progress: { type: Number, required: true },
  status: { type: String, required: true, enum: ['not_started', 'in_progress', 'completed', 'delayed'] },
  kpis: { type: Array },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Strategic Initiative Schema
const strategicInitiativeSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  budget: { type: Number, required: true },
  status: { type: String, required: true, enum: ['not_started', 'in_progress', 'completed', 'delayed'] },
  progress: { type: Number, required: true },
  owner: { type: String, required: true },
  relatedGoalIds: { type: Array },
  category: { type: String, required: true, enum: ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'] },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// SWOT Analysis Schema
const swotAnalysisSchema = {
  id: { type: String, required: true, unique: true },
  strengths: { type: Array, required: true },
  weaknesses: { type: Array, required: true },
  opportunities: { type: Array, required: true },
  threats: { type: Array, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Strategic Scenario Schema
const strategicScenarioSchema = {
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  probability: { type: Number, required: true },
  impact: { type: Number, required: true },
  financialImpact: { type: Number, required: true },
  responseStrategy: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
};

// Export all schemas
module.exports = {
  businessKpiSchema,
  businessPredictionSchema,
  businessInsightSchema,
  businessRiskSchema,
  businessOpportunitySchema,
  strategicGoalSchema,
  strategicInitiativeSchema,
  swotAnalysisSchema,
  strategicScenarioSchema
};
