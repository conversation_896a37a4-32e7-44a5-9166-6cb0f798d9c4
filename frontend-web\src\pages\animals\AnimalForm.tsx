import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Divider,
  FormHelperText,
  Alert,
  SelectChangeEvent,
  Card,
  CardContent,
  CircularProgress,
  useTheme,
  alpha
} from '@mui/material';
import CustomButton from '../../components/common/CustomButton';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { ArrowBack, Save, Delete, Pets } from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { mockAnimals } from '../../mocks/animalData';
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import { ModuleContainer, ModuleHeader } from '../../components/common';

const AnimalForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = id !== 'new';

  const [formData, setFormData] = useState({
    name: '',
    tag: '',
    species: '',
    breed: '',
    gender: '',
    dateOfBirth: null as dayjs.Dayjs | null,
    status: 'Active',
    location: '',
    weight: '',
    purchaseDate: null as dayjs.Dayjs | null,
    purchasePrice: '',
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(isEditMode);
  const [submitError, setSubmitError] = useState('');

  // Use MongoDB if connected, otherwise use mock data
  const { isConnected } = useMongoDb();
  const { getAnimalById } = useMongoAnimalData();

  useEffect(() => {
    if (isEditMode) {
      const fetchAnimal = async () => {
        try {
          let animal;

          if (isConnected) {
            // Try to fetch from MongoDB
            animal = await getAnimalById(id || '');
          }

          // Fall back to mock data if MongoDB fetch fails or not connected
          if (!animal) {
            animal = mockAnimals.find(a => a.id.toString() === id);
          }

          if (animal) {
            setFormData({
              name: animal.name,
              tag: animal.tagNumber,
              species: animal.species || animal.type || '',
              breed: animal.breed,
              gender: animal.gender,
              dateOfBirth: animal.birthDate ? dayjs(animal.birthDate) : null,
              status: animal.status,
              location: animal.location,
              weight: animal.weight ? animal.weight.toString() : '',
              purchaseDate: animal.purchaseDate ? dayjs(animal.purchaseDate) : null,
              purchasePrice: animal.purchasePrice ? animal.purchasePrice.toString() : '',
              notes: animal.notes || ''
            });
          }
        } catch (error) {
          console.error('Error fetching animal data:', error);
          setSubmitError('Failed to load animal data');
        } finally {
          setLoading(false);
        }
      };

      fetchAnimal();
    }
  }, [id, isEditMode, isConnected, getAnimalById]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Clear error when field is edited
      if (errors[name]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  };

  const handleDateChange = (field: string) => (date: dayjs.Dayjs | null) => {
    setFormData(prev => ({ ...prev, [field]: date }));

    // Clear error when field is edited
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.tag.trim()) newErrors.tag = 'Tag number is required';
    if (!formData.species) newErrors.species = 'Species is required';
    if (!formData.breed.trim()) newErrors.breed = 'Breed is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.dateOfBirth) newErrors.dateOfBirth = 'Date of birth is required';
    if (!formData.status) newErrors.status = 'Status is required';
    if (!formData.location.trim()) newErrors.location = 'Location is required';

    if (formData.weight && isNaN(Number(formData.weight))) {
      newErrors.weight = 'Weight must be a number';
    }

    if (formData.purchasePrice && isNaN(Number(formData.purchasePrice))) {
      newErrors.purchasePrice = 'Purchase price must be a number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const { createAnimal, updateAnimal } = useMongoAnimalData();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setSubmitError('');

    try {
      // Prepare animal data
      // Convert Dayjs objects to ISO strings for API compatibility
      const animalData = {
        name: formData.name,
        tagNumber: formData.tag,
        species: formData.species,
        type: formData.species,
        breed: formData.breed,
        gender: formData.gender,
        birthDate: formData.dateOfBirth ? formData.dateOfBirth.toISOString() : undefined,
        status: formData.status,
        location: formData.location,
        weight: parseFloat(formData.weight || '0'),
        purchaseDate: formData.purchaseDate ? formData.purchaseDate.toISOString() : undefined,
        purchasePrice: parseFloat(formData.purchasePrice || '0'),
        notes: formData.notes,
        healthStatus: 'healthy' as const // Default health status
      };

      if (isConnected) {
        // Use MongoDB
        if (isEditMode && id) {
          await updateAnimal(id, animalData);
        } else {
          await createAnimal(animalData);
        }
      } else {
        // Simulate API call with mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Navigate back to animals list
      navigate('/dashboard/animals');
    } catch (error) {
      console.error('Error saving animal:', error);
      setSubmitError('Failed to save animal data. Please try again.');
      setLoading(false);
    }
  };

  const theme = useTheme();

  if (loading && isEditMode) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <ModuleContainer module="animals">
      <ModuleHeader
        title={isEditMode ? 'Edit Animal' : 'Add New Animal'}
        subtitle={isEditMode ? 'Update animal information' : 'Register a new animal in your inventory'}
        module="animals"
        submodule="form"
        icon={<Pets />}
        actionLabel="Back to Animals"
        actionIcon={<ArrowBack />}
        onAction={() => navigate('/dashboard/animals')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            backgroundColor: alpha(theme.palette.primary.main, 0.03)
          }}>
            <CardContent>
              <Typography variant="h5" component="h1" gutterBottom>
                {isEditMode ? 'Edit Animal Details' : 'New Animal Registration'}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              {submitError && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {submitError}
                </Alert>
              )}

          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  error={!!errors.name}
                  helperText={errors.name}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Tag Number"
                  name="tag"
                  value={formData.tag}
                  onChange={handleChange}
                  error={!!errors.tag}
                  helperText={errors.tag}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.species} required>
                  <InputLabel>Species</InputLabel>
                  <Select
                    name="species"
                    value={formData.species}
                    onChange={handleChange}
                    label="Species"
                  >
                    <MenuItem value="Cattle">Cattle</MenuItem>
                    <MenuItem value="Sheep">Sheep</MenuItem>
                    <MenuItem value="Goat">Goat</MenuItem>
                    <MenuItem value="Pig">Pig</MenuItem>
                    <MenuItem value="Horse">Horse</MenuItem>
                  </Select>
                  {errors.species && <FormHelperText>{errors.species}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Breed"
                  name="breed"
                  value={formData.breed}
                  onChange={handleChange}
                  error={!!errors.breed}
                  helperText={errors.breed}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.gender} required>
                  <InputLabel>Gender</InputLabel>
                  <Select
                    name="gender"
                    value={formData.gender}
                    onChange={handleChange}
                    label="Gender"
                  >
                    <MenuItem value="Male">Male</MenuItem>
                    <MenuItem value="Female">Female</MenuItem>
                  </Select>
                  {errors.gender && <FormHelperText>{errors.gender}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Date of Birth"
                    value={formData.dateOfBirth}
                    onChange={handleDateChange('dateOfBirth')}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        required: true,
                        error: !!errors.dateOfBirth,
                        helperText: errors.dateOfBirth
                      }
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth error={!!errors.status} required>
                  <InputLabel>Status</InputLabel>
                  <Select
                    name="status"
                    value={formData.status}
                    onChange={handleChange}
                    label="Status"
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Sold">Sold</MenuItem>
                    <MenuItem value="Deceased">Deceased</MenuItem>
                    <MenuItem value="Quarantined">Quarantined</MenuItem>
                    <MenuItem value="Breeding">Breeding</MenuItem>
                  </Select>
                  {errors.status && <FormHelperText>{errors.status}</FormHelperText>}
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  error={!!errors.location}
                  helperText={errors.location}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Weight (kg)"
                  name="weight"
                  type="number"
                  value={formData.weight}
                  onChange={handleChange}
                  error={!!errors.weight}
                  helperText={errors.weight}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DatePicker
                    label="Purchase Date"
                    value={formData.purchaseDate}
                    onChange={handleDateChange('purchaseDate')}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        error: !!errors.purchaseDate,
                        helperText: errors.purchaseDate
                      }
                    }}
                  />
                </LocalizationProvider>
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Purchase Price"
                  name="purchasePrice"
                  type="number"
                  value={formData.purchasePrice}
                  onChange={handleChange}
                  error={!!errors.purchasePrice}
                  helperText={errors.purchasePrice}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  multiline
                  rows={4}
                />
              </Grid>

              <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box>
                  {isEditMode && (
                    <CustomButton
                      variant="outlined"
                      color="error"
                      startIcon={<Delete />}
                      style={{ marginRight: '16px' }}
                    >
                      Delete
                    </CustomButton>
                  )}
                </Box>

                <Box>
                  <CustomButton
                    variant="outlined"
                    onClick={() => navigate('/dashboard/animals')}
                    style={{ marginRight: '16px' }}
                  >
                    Cancel
                  </CustomButton>

                  <CustomButton
                    type="submit"
                    variant="contained"
                    startIcon={<Save />}
                    disabled={loading}
                    loading={loading}
                  >
                    {loading ? 'Saving...' : 'Save'}
                  </CustomButton>
                </Box>
              </Grid>
            </Grid>
          </form>
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </ModuleContainer>
  );
};

export default AnimalForm;
