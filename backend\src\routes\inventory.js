/**
 * inventory Routes
 * 
 * API routes for inventory module
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const inventoryController = require('../controllers/inventoryController');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * GET /api/inventory
 * 
 * @route GET /
 * @group inventory - inventory operations
 * @returns {object} 200 - Success response
 */
router.get('/', inventoryController.getAll);

/**
 * GET /api/inventory/:id
 * 
 * @route GET /:id
 * @group inventory - inventory operations
 * @returns {object} 200 - Success response
 */
router.get('/:id', inventoryController.getById);

/**
 * POST /api/inventory
 * 
 * @route POST /
 * @group inventory - inventory operations
 * @returns {object} 201 - Created response
 */
router.post('/', inventoryController.create);

/**
 * PUT /api/inventory/:id
 * 
 * @route PUT /:id
 * @group inventory - inventory operations
 * @returns {object} 200 - Success response
 */
router.put('/:id', inventoryController.update);

/**
 * DELETE /api/inventory/:id
 * 
 * @route DELETE /:id
 * @group inventory - inventory operations
 * @returns {object} 200 - Success response
 */
router.delete('/:id', inventoryController.delete);

module.exports = router;
