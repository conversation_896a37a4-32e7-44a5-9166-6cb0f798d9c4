import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import ModuleDashboard, { ModuleMetric, QuickAction } from '../../ModuleDashboard';

const HealthDashboard: React.FC = () => {
  const metrics: ModuleMetric[] = [
    { label: 'Health Alerts', value: '3', trend: '-2', status: 'positive' },
    { label: 'Vaccinations Due', value: '12', trend: '+5', status: 'warning' },
    { label: 'Treatment Success', value: '94%', trend: '+2%', status: 'positive' },
    { label: 'Disease Incidents', value: '2', trend: '-1', status: 'positive' },
  ];

  const quickActions: QuickAction[] = [
    { name: 'Record Treatment', icon: '💉', path: `${ROUTES.HEALTH}/treatment` },
    { name: 'Schedule Checkup', icon: '🩺', path: `${ROUTES.HEALTH}/checkup` },
    { name: 'Vaccination', icon: '💊', path: `${ROUTES.HEALTH}/vaccination` },
    { name: 'Health Records', icon: '📋', path: `${ROUTES.HEALTH}/records` },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Health Dashboard</h1>
          <p className="text-gray-600">Monitor and manage animal health</p>
        </div>
      </div>
      
      {/* Add your dashboard content here */}
      <ModuleDashboard
        title="Health Management"
        description="Monitor and manage animal health"
        metrics={metrics}
        quickActions={quickActions}
      />
    </div>
  );
};

export default HealthDashboard;

