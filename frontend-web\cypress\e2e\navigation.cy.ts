describe('Navigation and Responsive Design', () => {
  beforeEach(() => {
    // Login before each test
    cy.login('admin', 'password');
  });

  it('should navigate between modules using sidebar', () => {
    // Navigate to Animals module
    cy.navigateToModule('animals');
    cy.get('[data-testid=animals-header]').should('be.visible');
    
    // Navigate to Breeding module
    cy.navigateToModule('breeding');
    cy.get('[data-testid=breeding-header]').should('be.visible');
    
    // Navigate to Health module
    cy.navigateToModule('health');
    cy.get('[data-testid=health-header]').should('be.visible');
    
    // Navigate to Feed Management module
    cy.navigateToModule('feed-management');
    cy.get('[data-testid=feed-management-header]').should('be.visible');
  });

  it('should collapse sidebar on mobile devices', () => {
    // Test on mobile viewport
    cy.viewport('iphone-x');
    
    // Sidebar should be collapsed by default
    cy.get('[data-testid=sidebar]').should('have.class', 'collapsed');
    
    // Open sidebar
    cy.get('[data-testid=sidebar-toggle]').click();
    cy.get('[data-testid=sidebar]').should('not.have.class', 'collapsed');
    
    // Navigate to a module
    cy.get('[data-testid=nav-animals]').click();
    
    // Sidebar should auto-collapse after navigation on mobile
    cy.get('[data-testid=sidebar]').should('have.class', 'collapsed');
  });

  it('should adapt layout on different screen sizes', () => {
    // Test on desktop
    cy.viewport(1280, 720);
    cy.navigateToModule('dashboard');
    
    // Dashboard cards should be in a row
    cy.get('[data-testid=dashboard-metrics]').find('.metric-card').should('have.length.at.least', 3);
    
    // Test on tablet
    cy.viewport('ipad-2');
    // Cards should adjust to fewer columns
    
    // Test on mobile
    cy.viewport('iphone-x');
    // Cards should stack vertically
    
    // Check if content is visible without horizontal scrolling
    cy.get('body').then($body => {
      const bodyWidth = $body[0].scrollWidth;
      const viewportWidth = Cypress.config('viewportWidth');
      
      // Body width should not exceed viewport width (no horizontal scrolling)
      expect(bodyWidth).to.be.at.most(viewportWidth);
    });
  });

  it('should have touch-friendly interactions on mobile', () => {
    // Test on mobile viewport
    cy.viewport('iphone-x');
    
    // Navigate to a module with interactive elements
    cy.navigateToModule('animals');
    
    // Buttons and interactive elements should have adequate size for touch
    cy.get('[data-testid=add-animal-button]').then($button => {
      const rect = $button[0].getBoundingClientRect();
      
      // Touch targets should be at least 44x44 pixels
      expect(rect.width).to.be.at.least(44);
      expect(rect.height).to.be.at.least(44);
    });
    
    // Test swipe gestures if applicable
    // cy.get('[data-testid=swipeable-card]').swipe('left');
  });

  it('should maintain usability across different screen orientations', () => {
    // Test in landscape orientation
    cy.viewport('iphone-x', 'landscape');
    cy.navigateToModule('dashboard');
    
    // Important elements should be visible
    cy.get('[data-testid=dashboard-welcome]').should('be.visible');
    
    // Test in portrait orientation
    cy.viewport('iphone-x', 'portrait');
    
    // Important elements should still be visible
    cy.get('[data-testid=dashboard-welcome]').should('be.visible');
  });
});
