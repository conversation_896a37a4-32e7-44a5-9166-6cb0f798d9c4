import React, { useState, useEffect } from 'react';
import { Box, Grid, Typography, Card, CardContent, Chip, IconButton, useTheme, alpha, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TextField, InputAdornment, Menu, MenuItem, ListItemIcon, ListItemText, Dialog, DialogTitle, DialogContent, DialogActions, Rating, Avatar, Divider, FormControl, InputLabel, Select, SelectChangeEvent, CircularProgress, Tabs, Tab, Link } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Visibility,
  Edit,
  Delete,
  Phone,
  Email,
  Store,
  LocalShipping,
  AttachMoney,
  Inventory,
  Star,
  Business,
  Close,
  Language,
  LocationOn,
  CalendarToday
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { SupplierProvider, useSuppliers } from '../../contexts/SupplierContext';
import { Supplier, SupplierProductCategory } from '../../types/commercial/suppliers';

// Tab interface
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`supplier-tabpanel-${index}`}
      aria-labelledby={`supplier-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `supplier-tab-${index}`,
    'aria-controls': `supplier-tabpanel-${index}`,
  };
}

// Category colors
const getCategoryColor = (category: SupplierProductCategory | string, theme: any) => {
  switch (category) {
    case 'livestock-feed':
      return theme.palette.success.main;
    case 'animal-health':
      return theme.palette.error.main;
    case 'equipment':
      return theme.palette.info.main;
    case 'irrigation':
      return theme.palette.warning.main;
    case 'fertilizer':
      return theme.palette.secondary.main;
    case 'chemicals':
      return theme.palette.error.dark;
    case 'financial':
      return theme.palette.primary.dark;
    case 'insurance':
      return theme.palette.info.dark;
    default:
      return theme.palette.primary.main;
  }
};

// Category display names
const getCategoryDisplayName = (category: SupplierProductCategory | string) => {
  switch (category) {
    case 'livestock-feed':
      return 'Livestock Feed';
    case 'animal-health':
      return 'Animal Health';
    case 'equipment':
      return 'Equipment';
    case 'irrigation':
      return 'Irrigation';
    case 'fertilizer':
      return 'Fertilizer';
    case 'chemicals':
      return 'Chemicals';
    case 'financial':
      return 'Financial';
    case 'insurance':
      return 'Insurance';
    case 'retail':
      return 'Retail';
    case 'seeds':
      return 'Seeds';
    case 'fuel':
      return 'Fuel';
    case 'other':
      return 'Other';
    default:
      return category;
  }
};

// Supplier Directory Component
const SupplierDirectory: React.FC = () => {
  const theme = useTheme();
  const {
    suppliers,
    loading,
    error,
    filter,
    setFilter,
    selectedSupplier,
    setSelectedSupplier,
    getSupplierById
  } = useSuppliers();

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [supplierDetails, setSupplierDetails] = useState<Supplier | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');

  // Supplier statistics
  const totalSuppliers = suppliers.length;
  const activeSuppliers = suppliers.length; // All suppliers are considered active
  const totalSpent = 0; // We don't have this data yet

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, supplierId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedSupplier(supplierId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleViewSupplier = async () => {
    if (selectedSupplier) {
      try {
        const supplier = await getSupplierById(selectedSupplier);
        setSupplierDetails(supplier);
        setOpenDialog(true);
      } catch (error) {
        console.error('Error fetching supplier details:', error);
      }
    }
    handleMenuClose();
  };

  const handleCategoryFilterChange = (event: SelectChangeEvent) => {
    setCategoryFilter(event.target.value);
    setFilter({
      ...filter,
      categories: event.target.value === 'all' ? undefined : [event.target.value as SupplierProductCategory]
    });
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilter({
      ...filter,
      search: event.target.value
    });
  };

  // Get primary category for display
  const getPrimaryCategory = (supplier: Supplier) => {
    return supplier.categories && supplier.categories.length > 0
      ? supplier.categories[0]
      : 'other';
  };

  // Get primary contact
  const getPrimaryContact = (supplier: Supplier, type: 'phone' | 'email') => {
    const contact = supplier.contacts?.find(c => c.type === type);
    return contact ? contact.value : '';
  };

  // Get primary location
  const getPrimaryLocation = (supplier: Supplier) => {
    return supplier.locations && supplier.locations.length > 0
      ? supplier.locations[0].address
      : '';
  };

  // Filter suppliers based on search query and category
  const filteredSuppliers = suppliers.filter(supplier => {
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (
        !supplier.name.toLowerCase().includes(query) &&
        !supplier.description.toLowerCase().includes(query) &&
        !(supplier.contactPerson && supplier.contactPerson.toLowerCase().includes(query))
      ) {
        return false;
      }
    }

    // Apply category filter
    if (categoryFilter !== 'all' && supplier.categories) {
      return supplier.categories.some(category =>
        category.toLowerCase() === categoryFilter.toLowerCase()
      );
    }

    return true;
  });

  return (
    <Box>
      <ModuleHeader
        title="Supplier Management"
        subtitle="Manage your livestock suppliers, track orders, and maintain supplier relationships"
        module="commercial"
        submodule="suppliers"
        actionLabel="Add Supplier"
        actionIcon={<Add />}
        onAction={() => console.log('Add new supplier')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Total Suppliers"
              subtitle={`${totalSuppliers} registered suppliers`}
              backgroundImage="https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg"
              icon={<Business />}
              accentColor={theme.palette.primary.main}
              delay={0.1}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {totalSuppliers}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Active Suppliers"
              subtitle="Currently active partnerships"
              backgroundImage="https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg"
              icon={<Store />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.2}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {activeSuppliers}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Total Spent"
              subtitle="Lifetime supplier expenditure"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<AttachMoney />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.3}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                R{totalSpent.toLocaleString()}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
          <TextField
            placeholder="Search suppliers..."
            variant="outlined"
            fullWidth
            size="small"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flex: 1 }}
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="category-filter-label">Category</InputLabel>
            <Select
              labelId="category-filter-label"
              value={categoryFilter}
              label="Category"
              onChange={handleCategoryFilterChange}
            >
              <MenuItem value="all">All Categories</MenuItem>
              <MenuItem value="Feed">Feed</MenuItem>
              <MenuItem value="Health">Health</MenuItem>
              <MenuItem value="Equipment">Equipment</MenuItem>
              <MenuItem value="Livestock">Livestock</MenuItem>
            </Select>
          </FormControl>

          <CustomButton
            variant="contained"
            startIcon={<Add />}
            sx={{ minWidth: 180 }}
          >
            Add Supplier
          </CustomButton>
        </Box>

        {/* Suppliers Table */}
        <Paper sx={{
          width: '100%',
          overflow: 'hidden',
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
        }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Supplier</TableCell>
                  <TableCell>Category</TableCell>
                  <TableCell>Contact Person</TableCell>
                  <TableCell>Contact Info</TableCell>
                  <TableCell>Rating</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Order</TableCell>
                  <TableCell align="right">Total Orders</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredSuppliers
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((supplier, index) => (
                    <TableRow
                      key={supplier.id}
                      sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
                      component={motion.tr}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.05 }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(getCategoryColor(supplier.category, theme), 0.2),
                              color: getCategoryColor(supplier.category, theme),
                              mr: 2
                            }}
                          >
                            {supplier.name.charAt(0)}
                          </Avatar>
                          <Typography variant="body2" fontWeight="medium">{supplier.name}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={supplier.category}
                          size="small"
                          sx={{
                            bgcolor: alpha(getCategoryColor(supplier.category, theme), 0.1),
                            color: getCategoryColor(supplier.category, theme),
                            fontWeight: 'medium'
                          }}
                        />
                      </TableCell>
                      <TableCell>{supplier.contactPerson}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Email sx={{ fontSize: 16, mr: 0.5, color: theme.palette.text.secondary }} />
                            <Typography variant="body2">{supplier.email}</Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Phone sx={{ fontSize: 16, mr: 0.5, color: theme.palette.text.secondary }} />
                            <Typography variant="body2">{supplier.phone}</Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Rating value={supplier.rating} precision={0.5} size="small" readOnly />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {supplier.rating}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={supplier.status === 'active' ? 'Active' : 'Inactive'}
                          size="small"
                          color={supplier.status === 'active' ? 'success' : 'default'}
                        />
                      </TableCell>
                      <TableCell>{new Date(supplier.lastOrder).toLocaleDateString('en-ZA')}</TableCell>
                      <TableCell align="right">{supplier.totalOrders}</TableCell>
                      <TableCell align="right">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, supplier.id)}
                        >
                          <MoreVert fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                }
                {filteredSuppliers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={9} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        No suppliers found matching your criteria
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredSuppliers.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleViewSupplier}>
          <ListItemIcon>
            <Visibility fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <Edit fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Supplier</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <LocalShipping fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Orders</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <Delete fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText sx={{ color: theme.palette.error.main }}>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Supplier Details Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Supplier Details</Typography>
            <IconButton onClick={() => setOpenDialog(false)} size="small">
              <Close fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {selectedSupplier && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <Avatar
                      sx={{
                        width: 64,
                        height: 64,
                        bgcolor: alpha(getCategoryColor(suppliers.find(s => s.id === selectedSupplier)?.category || '', theme), 0.2),
                        color: getCategoryColor(suppliers.find(s => s.id === selectedSupplier)?.category || '', theme),
                        mr: 2
                      }}
                    >
                      {suppliers.find(s => s.id === selectedSupplier)?.name.charAt(0)}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="bold">
                        {suppliers.find(s => s.id === selectedSupplier)?.name}
                      </Typography>
                      <Chip
                        label={suppliers.find(s => s.id === selectedSupplier)?.category}
                        size="small"
                        sx={{
                          bgcolor: alpha(getCategoryColor(suppliers.find(s => s.id === selectedSupplier)?.category || '', theme), 0.1),
                          color: getCategoryColor(suppliers.find(s => s.id === selectedSupplier)?.category || '', theme),
                          fontWeight: 'medium',
                          mt: 0.5
                        }}
                      />
                    </Box>
                  </Box>

                  <Typography variant="subtitle2" color="text.secondary">Contact Person</Typography>
                  <Typography variant="body1" gutterBottom>
                    {suppliers.find(s => s.id === selectedSupplier)?.contactPerson}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary" mt={2}>Email</Typography>
                  <Typography variant="body1" gutterBottom>
                    {suppliers.find(s => s.id === selectedSupplier)?.email}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary" mt={2}>Phone</Typography>
                  <Typography variant="body1" gutterBottom>
                    {suppliers.find(s => s.id === selectedSupplier)?.phone}
                  </Typography>

                  <Typography variant="subtitle2" color="text.secondary" mt={2}>Address</Typography>
                  <Typography variant="body1" gutterBottom>
                    {suppliers.find(s => s.id === selectedSupplier)?.address}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      borderRadius: 2,
                      mb: 3
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Supplier Statistics
                    </Typography>

                    <Grid container spacing={2} mt={1}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Status</Typography>
                        <Chip
                          label={suppliers.find(s => s.id === selectedSupplier)?.status === 'active' ? 'Active' : 'Inactive'}
                          size="small"
                          color={suppliers.find(s => s.id === selectedSupplier)?.status === 'active' ? 'success' : 'default'}
                          sx={{ mt: 0.5 }}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Rating</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          <Rating
                            value={suppliers.find(s => s.id === selectedSupplier)?.rating || 0}
                            precision={0.5}
                            size="small"
                            readOnly
                          />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {suppliers.find(s => s.id === selectedSupplier)?.rating}
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Total Orders</Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {suppliers.find(s => s.id === selectedSupplier)?.totalOrders}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Total Spent</Typography>
                        <Typography variant="h6" fontWeight="medium">
                          R{suppliers.find(s => s.id === selectedSupplier)?.totalSpent.toLocaleString()}
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Last Order Date</Typography>
                        <Typography variant="body1">
                          {new Date(suppliers.find(s => s.id === selectedSupplier)?.lastOrder || '').toLocaleDateString('en-ZA')}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Paper>

                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Recent Orders
                  </Typography>

                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      Order history will be displayed here
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setOpenDialog(false)}>Close</CustomButton>
          <CustomButton variant="outlined" startIcon={<LocalShipping />}>View Orders</CustomButton>
          <CustomButton variant="contained" startIcon={<Edit />}>Edit Supplier</CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Wrap the component with SupplierProvider
const Suppliers = () => {
  return (
    <SupplierProvider>
      <SupplierDirectory />
    </SupplierProvider>
  );
};

export default Suppliers;
