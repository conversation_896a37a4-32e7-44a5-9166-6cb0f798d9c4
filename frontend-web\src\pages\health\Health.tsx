import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Box, CircularProgress } from '@mui/material';

import HealthDashboard from './HealthDashboard';
import Vaccinations from './Vaccinations';
import Diseases from './Diseases';
import Treatments from './Treatments';
import Appointments from './Appointments';
import Records from './Records';

const Health: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Routes>
        <Route path="/" element={<HealthDashboard />} />
        <Route path="/vaccinations" element={<Vaccinations />} />
        <Route path="/diseases" element={<Diseases />} />
        <Route path="/treatments" element={<Treatments />} />
        <Route path="/appointments" element={<Appointments />} />
        <Route path="/records" element={<Records />} />
      </Routes>
    </motion.div>
  );
};

export default Health;