import { useLocation } from 'react-router-dom';
import { moduleBackgrounds } from '../theme/theme';

/**
 * Custom hook to get the appropriate background gradient based on the current route
 * @returns The background gradient CSS string
 */
export const useModuleBackground = (): string => {
  const location = useLocation();
  const path = location.pathname;

  // Determine which module we're in based on the path
  if (path === '/') {
    return moduleBackgrounds.dashboard;
  } else if (path.startsWith('/animals')) {
    return moduleBackgrounds.animals;
  } else if (path.startsWith('/health')) {
    return moduleBackgrounds.health;
  } else if (path.startsWith('/breeding')) {
    return moduleBackgrounds.breeding;
  } else if (path.startsWith('/feed-management') || path.startsWith('/feeding')) {
    return moduleBackgrounds.feed;
  } else if (path.startsWith('/financial')) {
    return moduleBackgrounds.financial;
  } else if (path.startsWith('/inventory')) {
    return moduleBackgrounds.financial;
  } else if (path.startsWith('/commercial')) {
    return moduleBackgrounds.commercial;
  } else if (path.startsWith('/compliance')) {
    return moduleBackgrounds.compliance;
  } else if (path.startsWith('/resources')) {
    return moduleBackgrounds.resources;
  }

  // Default background
  return moduleBackgrounds.dashboard;
};

export default useModuleBackground;
