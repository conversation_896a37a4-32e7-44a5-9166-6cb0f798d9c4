/**
 * Background Image Optimization for AgriIntel
 * 
 * Ensures high-quality livestock/farm images are properly optimized
 * and maintain quality across different screen sizes and devices.
 */

/* High-quality background image optimization */
.unified-background-image {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: -webkit-crisp-edges;
  image-rendering: crisp-edges;
  image-rendering: optimize-quality;
  
  /* Ensure smooth scaling */
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
}

/* Responsive background images for different screen sizes */
@media (max-width: 640px) {
  .unified-background-image {
    background-attachment: scroll; /* Better performance on mobile */
    background-size: cover;
    background-position: center center;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .unified-background-image {
    background-size: cover;
    background-position: center center;
  }
}

@media (min-width: 1025px) {
  .unified-background-image {
    background-size: cover;
    background-position: center center;
    background-attachment: fixed;
  }
}

/* High-DPI display optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .unified-background-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -webkit-crisp-edges;
    image-rendering: crisp-edges;
  }
}

/* Fallback gradients for livestock themes */
.livestock-background-cattle {
  background: linear-gradient(135deg, rgba(101, 67, 33, 0.8), rgba(139, 69, 19, 0.6));
}

.livestock-background-sheep {
  background: linear-gradient(135deg, rgba(245, 245, 220, 0.8), rgba(255, 248, 220, 0.6));
}

.livestock-background-goats {
  background: linear-gradient(135deg, rgba(160, 82, 45, 0.8), rgba(210, 180, 140, 0.6));
}

.livestock-background-farm {
  background: linear-gradient(135deg, rgba(34, 139, 34, 0.8), rgba(107, 142, 35, 0.6));
}

/* Agricultural theme gradients */
.agri-theme-primary {
  background: linear-gradient(135deg, rgba(15, 118, 110, 0.9), rgba(5, 150, 105, 0.7));
}

.agri-theme-secondary {
  background: linear-gradient(135deg, rgba(55, 65, 81, 0.9), rgba(75, 85, 99, 0.7));
}

.agri-theme-nature {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.6));
}

/* Overlay patterns for better text readability */
.background-overlay-light {
  position: relative;
}

.background-overlay-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.85);
  z-index: 1;
}

.background-overlay-dark {
  position: relative;
}

.background-overlay-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.background-overlay-gradient {
  position: relative;
}

.background-overlay-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.02) 0%,
    rgba(255, 255, 255, 0.01) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  z-index: -1;
  pointer-events: none;
}

/* Content positioning over backgrounds */
.background-content {
  position: relative;
  z-index: 2;
}

/* Blur effects for glassmorphism */
.background-blur {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.background-blur-light {
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.background-blur-dark {
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.2);
}

/* Performance optimizations */
.background-optimized {
  will-change: transform;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Preload critical background images - to be configured per implementation */
.preload-backgrounds::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
  z-index: -1;
  /* Background images should be added here based on actual available images */
}

/* Smooth transitions between background changes */
.background-transition {
  transition: background-image 0.5s ease-in-out;
}

/* Print styles - remove backgrounds for better printing */
@media print {
  .unified-background-image,
  .livestock-background-cattle,
  .livestock-background-sheep,
  .livestock-background-goats,
  .livestock-background-farm {
    background: none !important;
    background-image: none !important;
  }
  
  .background-overlay-light::before,
  .background-overlay-dark::before,
  .background-overlay-gradient::before {
    display: none !important;
  }
}

/* Accessibility - respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
  .unified-background-image {
    background-attachment: scroll;
  }
  
  .background-transition {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .background-overlay-light::before {
    background: rgba(0, 0, 0, 0.7);
  }
  
  .background-overlay-gradient::before {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0.6) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}

/* Loading states for background images */
.background-loading {
  background: linear-gradient(
    135deg,
    #f3f4f6 0%,
    #e5e7eb 50%,
    #f3f4f6 100%
  );
  animation: backgroundPulse 2s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Error states for failed background loads */
.background-error {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.1) 0%,
    rgba(220, 38, 38, 0.05) 50%,
    rgba(239, 68, 68, 0.1) 100%
  );
}

/* Success states for loaded backgrounds */
.background-loaded {
  animation: backgroundFadeIn 0.5s ease-in-out;
}

@keyframes backgroundFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
