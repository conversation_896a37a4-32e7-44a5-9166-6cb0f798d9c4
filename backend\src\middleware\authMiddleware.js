/**
 * Authentication Middleware
 *
 * This middleware handles authentication and authorization for API routes.
 */

const { verifyToken } = require('../services/authService');
const logger = require('../utils/logger');

/**
 * Authenticate a request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function authenticate(req, res, next) {
  try {
    // Skip authentication for certain endpoints
    const skipAuthPaths = [
      '/api/status',
      '/api/db-status',
      '/health',
      '/api/auth/login',
      '/api/auth/register'
    ];

    // Check if the current path should skip authentication
    const currentPath = req.path;
    if (skipAuthPaths.some(path => currentPath.startsWith(path) || currentPath === path)) {
      logger.info(`Skipping authentication for public endpoint: ${currentPath}`);
      return next();
    }

    // Get the token from the request
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn(`No token provided or invalid token format for ${req.method} ${req.originalUrl}`);
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        details: 'No valid authorization header found'
      });
    }

    // Extract the token
    const token = authHeader.split(' ')[1];

    // Verify the token
    const decoded = verifyToken(token);

    if (!decoded) {
      logger.warn(`Invalid token for ${req.method} ${req.originalUrl}`);
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired token',
        details: 'The provided token is invalid or has expired'
      });
    }

    // Add the user to the request
    req.user = decoded;

    // Log successful authentication
    logger.info(`User ${decoded.username} (${decoded.role}) authenticated for ${req.method} ${req.originalUrl}`);

    // Continue to the next middleware or route handler
    next();
  } catch (error) {
    logger.error(`Authentication error for ${req.method} ${req.originalUrl}:`, error);

    // Determine the specific error type
    let errorMessage = 'Authentication failed';
    let errorDetails = 'An unexpected error occurred during authentication';

    if (error.name === 'TokenExpiredError') {
      errorMessage = 'Token expired';
      errorDetails = 'Your session has expired. Please log in again.';
    } else if (error.name === 'JsonWebTokenError') {
      errorMessage = 'Invalid token';
      errorDetails = 'The provided token is malformed or invalid.';
    }

    return res.status(401).json({
      success: false,
      message: errorMessage,
      details: errorDetails
    });
  }
}

/**
 * Authorize a request based on role
 * @param {string[]} roles - Allowed roles
 * @returns {Function} - Express middleware
 */
function authorize(roles = []) {
  return (req, res, next) => {
    try {
      // Check if the user is authenticated
      if (!req.user) {
        logger.warn('User not authenticated');
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // If roles is a string, convert it to an array
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      // If no roles are specified, allow all authenticated users
      if (allowedRoles.length === 0) {
        return next();
      }

      // Check if the user has one of the allowed roles
      if (!allowedRoles.includes(req.user.role)) {
        logger.warn(`User ${req.user.username} does not have required role. Required: ${allowedRoles.join(', ')}, Actual: ${req.user.role}`);
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          requiredRoles: allowedRoles,
          userRole: req.user.role
        });
      }

      // Log successful authorization
      logger.info(`User ${req.user.username} authorized with role ${req.user.role} for ${req.method} ${req.originalUrl}`);

      // Continue to the next middleware or route handler
      next();
    } catch (error) {
      logger.error('Authorization error:', error);
      return res.status(403).json({
        success: false,
        message: 'Authorization failed'
      });
    }
  };
}

/**
 * Authorize a request based on permission
 * @param {string[]} permissions - Required permissions
 * @returns {Function} - Express middleware
 */
function requirePermission(permissions = []) {
  return (req, res, next) => {
    try {
      // Check if the user is authenticated
      if (!req.user) {
        logger.warn('User not authenticated');
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      // If permissions is a string, convert it to an array
      const requiredPermissions = Array.isArray(permissions) ? permissions : [permissions];

      // If no permissions are specified, allow all authenticated users
      if (requiredPermissions.length === 0) {
        return next();
      }

      // Admin role has all permissions
      if (req.user.role === 'admin') {
        logger.info(`User ${req.user.username} granted access as admin for ${req.method} ${req.originalUrl}`);
        return next();
      }

      // Role-based permissions mapping
      const rolePermissions = {
        'manager': [
          'view_animals', 'create_animal', 'update_animal',
          'view_health_records', 'create_health_record', 'update_health_record',
          'view_breeding_records', 'create_breeding_record', 'update_breeding_record',
          'view_transactions', 'create_transaction',
          'view_inventory', 'create_inventory', 'update_inventory',
          'view_reports', 'create_reports', 'export_reports'
        ],
        'super_user': [
          'view_animals', 'create_animal', 'update_animal',
          'view_health_records', 'create_health_record',
          'view_breeding_records', 'create_breeding_record',
          'view_transactions',
          'view_inventory',
          'view_reports', 'export_reports'
        ],
        'staff': [
          'view_animals',
          'view_health_records',
          'view_breeding_records',
          'view_inventory',
          'view_reports'
        ],
        'veterinarian': [
          'view_animals', 'update_animal',
          'view_health_records', 'create_health_record', 'update_health_record',
          'view_breeding_records'
        ],
        'viewer': [
          'view_animals',
          'view_health_records',
          'view_breeding_records',
          'view_inventory',
          'view_reports'
        ]
      };

      // Get permissions for the user's role
      const roleBasedPermissions = rolePermissions[req.user.role] || [];

      // Check if the user has all required permissions based on their role
      const hasRolePermissions = requiredPermissions.every(permission =>
        roleBasedPermissions.includes(permission)
      );

      if (hasRolePermissions) {
        logger.info(`User ${req.user.username} granted access based on role ${req.user.role} for ${req.method} ${req.originalUrl}`);
        return next();
      }

      // Check if the user has all required permissions from their explicit permissions
      const userPermissions = req.user.permissions || [];

      // Check if the user has the 'all' permission
      if (userPermissions.includes('all')) {
        logger.info(`User ${req.user.username} granted access with 'all' permission for ${req.method} ${req.originalUrl}`);
        return next();
      }

      // Check if the user has all required permissions
      const hasAllPermissions = requiredPermissions.every(permission =>
        userPermissions.includes(permission)
      );

      if (hasAllPermissions) {
        logger.info(`User ${req.user.username} granted access with explicit permissions for ${req.method} ${req.originalUrl}`);
        return next();
      }

      // User doesn't have required permissions
      logger.warn(`User ${req.user.username} does not have required permissions. Required: ${requiredPermissions.join(', ')}, Role: ${req.user.role}, Explicit: ${userPermissions.join(', ')}`);
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        requiredPermissions: requiredPermissions,
        userRole: req.user.role,
        userPermissions: userPermissions
      });
    } catch (error) {
      logger.error('Permission check error:', error);
      return res.status(403).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
}

/**
 * Log API requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function logRequest(req, res, next) {
  // Get the current timestamp
  const timestamp = new Date().toISOString();

  // Get the request method and path
  const { method, originalUrl, ip } = req;

  // Get the user if authenticated
  const user = req.user ? `${req.user.username} (${req.user.role})` : 'unauthenticated';

  // Log the request
  logger.info(`[${timestamp}] ${method} ${originalUrl} - User: ${user}, IP: ${ip}`);

  // Continue to the next middleware or route handler
  next();
}

module.exports = {
  authenticate,
  authorize,
  requirePermission,
  logRequest
};
