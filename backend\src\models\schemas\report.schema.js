/**
 * Report Schema
 * 
 * Defines the MongoDB schema for the reports collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Report Schema
const reportSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['animal', 'breeding', 'health', 'feeding', 'financial', 'inventory', 'custom'],
    required: true
  },
  subtype: {
    type: String,
    trim: true
  },
  parameters: {
    type: Map,
    of: Schema.Types.Mixed
  },
  dateRange: {
    start: {
      type: Date
    },
    end: {
      type: Date
    }
  },
  filters: [{
    field: {
      type: String,
      required: true
    },
    operator: {
      type: String,
      enum: ['equals', 'notEquals', 'contains', 'notContains', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual', 'between', 'in', 'notIn'],
      required: true
    },
    value: {
      type: Schema.Types.Mixed,
      required: true
    }
  }],
  sortBy: {
    field: {
      type: String
    },
    direction: {
      type: String,
      enum: ['asc', 'desc'],
      default: 'asc'
    }
  },
  groupBy: {
    type: String
  },
  columns: [{
    field: {
      type: String,
      required: true
    },
    label: {
      type: String
    },
    width: {
      type: Number
    },
    format: {
      type: String
    },
    visible: {
      type: Boolean,
      default: true
    }
  }],
  charts: [{
    type: {
      type: String,
      enum: ['bar', 'line', 'pie', 'doughnut', 'area', 'scatter', 'radar', 'none'],
      default: 'none'
    },
    title: {
      type: String
    },
    xAxis: {
      field: {
        type: String
      },
      label: {
        type: String
      }
    },
    yAxis: {
      field: {
        type: String
      },
      label: {
        type: String
      }
    },
    options: {
      type: Map,
      of: Schema.Types.Mixed
    }
  }],
  schedule: {
    enabled: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'custom'],
      default: 'monthly'
    },
    dayOfWeek: {
      type: Number, // 0 = Sunday, 1 = Monday, etc.
      min: 0,
      max: 6
    },
    dayOfMonth: {
      type: Number,
      min: 1,
      max: 31
    },
    time: {
      type: String // Format: HH:MM
    },
    recipients: [{
      email: {
        type: String,
        required: true
      },
      name: {
        type: String
      }
    }],
    format: {
      type: String,
      enum: ['pdf', 'excel', 'csv', 'html'],
      default: 'pdf'
    },
    lastRun: {
      type: Date
    },
    nextRun: {
      type: Date
    }
  },
  isTemplate: {
    type: Boolean,
    default: false
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Report Result Schema
const reportResultSchema = new Schema({
  report: {
    type: Schema.Types.ObjectId,
    ref: 'Report',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  generatedDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  parameters: {
    type: Map,
    of: Schema.Types.Mixed
  },
  data: {
    type: Schema.Types.Mixed
  },
  summary: {
    type: Map,
    of: Schema.Types.Mixed
  },
  format: {
    type: String,
    enum: ['json', 'pdf', 'excel', 'csv', 'html'],
    default: 'json'
  },
  fileUrl: {
    type: String
  },
  fileSize: {
    type: Number,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'error'],
    default: 'pending'
  },
  error: {
    message: {
      type: String
    },
    stack: {
      type: String
    }
  },
  executionTime: {
    type: Number,
    min: 0
  },
  rowCount: {
    type: Number,
    min: 0
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
reportSchema.index({ type: 1 });
reportSchema.index({ 'schedule.enabled': 1, 'schedule.nextRun': 1 });
reportSchema.index({ isTemplate: 1 });
reportSchema.index({ isPublic: 1 });
reportSchema.index({ createdBy: 1 });

reportResultSchema.index({ report: 1 });
reportResultSchema.index({ generatedDate: -1 });
reportResultSchema.index({ status: 1 });
reportResultSchema.index({ createdBy: 1 });

// Pre-save hook to update timestamps
reportSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

reportResultSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const Report = mongoose.model('Report', reportSchema);
const ReportResult = mongoose.model('ReportResult', reportResultSchema);

module.exports = {
  Report,
  ReportResult
};
