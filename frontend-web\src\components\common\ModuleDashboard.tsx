/**
 * Standardized Module Dashboard Component
 * 
 * This component provides a consistent dashboard layout for all modules
 * with integrated statistics and data visualization.
 */

import React from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, Grid, Card, CardContent, CardHeader, Divider, CircularProgress, useTheme, alpha } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from '../../hooks/useTranslation';
import { BaseEntity } from '../../services/moduleService';
import { ModuleContainer, ModuleHeader } from './index';
import DataTable, { Column } from './DataTable';

// Stat card props
interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  color?: string;
  onClick?: () => void;
}

// Stat card component
const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  onClick
}) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': onClick ? {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8]
        } : {},
        bgcolor: alpha(theme.palette.background.paper, 0.8),
        borderRadius: 2
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h4" component="div" sx={{ fontWeight: 'bold', color: color || 'inherit' }}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          {icon && (
            <Box 
              sx={{ 
                p: 1.5, 
                borderRadius: '50%', 
                bgcolor: color ? alpha(color, 0.1) : alpha(theme.palette.primary.main, 0.1),
                color: color || theme.palette.primary.main
              }}
            >
              {icon}
            </Box>
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

// Chart card props
interface ChartCardProps {
  title: string;
  subtitle?: string;
  chart: React.ReactNode;
  height?: number | string;
  actions?: React.ReactNode;
}

// Chart card component
const ChartCard: React.FC<ChartCardProps> = ({
  title,
  subtitle,
  chart,
  height = 300,
  actions
}) => {
  const theme = useTheme();
  
  return (
    <Card 
      sx={{ 
        height: '100%',
        bgcolor: alpha(theme.palette.background.paper, 0.8),
        borderRadius: 2
      }}
    >
      <CardHeader
        title={
          <Typography variant="h6">
            {title}
          </Typography>
        }
        subheader={subtitle}
        action={actions}
      />
      <Divider />
      <CardContent sx={{ height }}>
        {chart}
      </CardContent>
    </Card>
  );
};

// Module dashboard props
interface ModuleDashboardProps<T extends BaseEntity> {
  title: string;
  subtitle?: string;
  module: string;
  stats: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon?: React.ReactNode;
    color?: string;
    onClick?: () => void;
  }[];
  charts: {
    title: string;
    subtitle?: string;
    chart: React.ReactNode;
    gridSize?: {
      xs?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };
    height?: number | string;
    actions?: React.ReactNode;
  }[];
  recentData?: {
    title: string;
    data: T[];
    columns: Column<T>[];
    loading: boolean;
    error: string | null;
    onView?: (item: T) => void;
    viewAllLink?: string;
    emptyMessage?: string;
    maxItems?: number;
  };
  loading?: boolean;
  error?: string | null;
  actionButtons?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
    color?: 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
    variant?: 'text' | 'outlined' | 'contained';
  }[];
}

function ModuleDashboard<T extends BaseEntity>({
  title,
  subtitle,
  module,
  stats,
  charts,
  recentData,
  loading = false,
  error = null,
  actionButtons = []
}: ModuleDashboardProps<T>) {
  const theme = useTheme();
  const { translate } = useTranslation();
  const navigate = useNavigate();
  
  return (
    <ModuleContainer>
      <ModuleHeader
        title={title}
        subtitle={subtitle}
        module={module}
      />
      
      {/* Error message */}
      {error && (
        <Box sx={{ mb: 3, px: 3 }}>
          <Paper 
            sx={{ 
              p: 2, 
              bgcolor: alpha(theme.palette.error.main, 0.1),
              color: theme.palette.error.main,
              borderRadius: 2
            }}
          >
            <Typography variant="body1">{error}</Typography>
          </Paper>
        </Box>
      )}
      
      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}
      
      {/* Action buttons */}
      {actionButtons.length > 0 && (
        <Box sx={{ display: 'flex', gap: 2, mb: 4, px: 3 }}>
          {actionButtons.map((button, index) => (
            <CustomButton
              key={index}
              variant={button.variant || 'contained'}
              color={button.color || 'primary'}
              startIcon={button.icon}
              onClick={button.onClick}
            >
              {button.label}
            </CustomButton>
          ))}
        </Box>
      )}
      
      {/* Stats cards */}
      <Box sx={{ mb: 4, px: 3 }}>
        <Grid container spacing={3}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
              <StatCard
                title={stat.title}
                value={stat.value}
                subtitle={stat.subtitle}
                icon={stat.icon}
                color={stat.color}
                onClick={stat.onClick}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
      
      {/* Charts */}
      <Box sx={{ mb: 4, px: 3 }}>
        <Grid container spacing={3}>
          {charts.map((chart, index) => (
            <Grid 
              item 
              xs={12} 
              sm={chart.gridSize?.sm || 12} 
              md={chart.gridSize?.md || 6} 
              lg={chart.gridSize?.lg || 6} 
              xl={chart.gridSize?.xl || 4}
              key={index}
            >
              <ChartCard
                title={chart.title}
                subtitle={chart.subtitle}
                chart={chart.chart}
                height={chart.height}
                actions={chart.actions}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
      
      {/* Recent data */}
      {recentData && (
        <Box sx={{ mb: 4, px: 3 }}>
          <Card 
            sx={{ 
              bgcolor: alpha(theme.palette.background.paper, 0.8),
              borderRadius: 2
            }}
          >
            <CardHeader
              title={
                <Typography variant="h6">
                  {recentData.title}
                </Typography>
              }
              action={
                recentData.viewAllLink && (
                  <CustomButton 
                    variant="text" 
                    onClick={() => navigate(recentData.viewAllLink!)}
                  >
                    {translate ? translate('common.view_all', { fallback: 'View All' }) : 'View All'}
                  </CustomButton>
                )
              }
            />
            <Divider />
            <CardContent>
              <DataTable
                columns={recentData.columns}
                rows={recentData.data.slice(0, recentData.maxItems || 5)}
                loading={recentData.loading}
                error={recentData.error}
                onView={recentData.onView}
                pagination={false}
                emptyMessage={recentData.emptyMessage || 'No recent data available'}
                dense
              />
            </CardContent>
          </Card>
        </Box>
      )}
    </ModuleContainer>
  );
}

export default ModuleDashboard;
