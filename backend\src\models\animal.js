const { executeQuery } = require('../config/db');
const logger = require('../utils/logger');

/**
 * Animal model for livestock management
 */
class Animal {
  /**
   * Create a new animal
   * @param {Object} animalData - Animal data
   * @returns {Promise<Object>} - Created animal
   */
  static async create(animalData) {
    try {
      // SQL query to insert animal
      const query = `
        INSERT INTO Animals (
          tag_number, 
          animal_type, 
          breed, 
          gender, 
          birth_date, 
          weight, 
          status, 
          location, 
          parent_tag_number, 
          purchase_date, 
          purchase_price,
          notes,
          created_by,
          created_at
        )
        OUTPUT INSERTED.id, INSERTED.tag_number, INSERTED.animal_type, INSERTED.breed, 
               INSERTED.gender, INSERTED.birth_date, INSERTED.weight, INSERTED.status, 
               INSERTED.location, INSERTED.parent_tag_number, INSERTED.purchase_date, 
               INSERTED.purchase_price, INSERTED.notes, INSERTED.created_by, 
               INSERTED.created_at, INSERTED.updated_at
        VALUES (
          @tagNumber, 
          @animalType, 
          @breed, 
          @gender, 
          @birthDate, 
          @weight, 
          @status, 
          @location, 
          @parentTagNumber, 
          @purchaseDate, 
          @purchasePrice,
          @notes,
          @createdBy,
          GETDATE()
        )
      `;

      // Query parameters
      const params = {
        tagNumber: animalData.tagNumber,
        animalType: animalData.animalType,
        breed: animalData.breed,
        gender: animalData.gender,
        birthDate: animalData.birthDate,
        weight: animalData.weight,
        status: animalData.status || 'active',
        location: animalData.location,
        parentTagNumber: animalData.parentTagNumber,
        purchaseDate: animalData.purchaseDate,
        purchasePrice: animalData.purchasePrice,
        notes: animalData.notes,
        createdBy: animalData.createdBy
      };

      // Execute query
      const result = await executeQuery(query, params);
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      throw new Error('Failed to create animal');
    } catch (error) {
      logger.error('Error creating animal:', error);
      throw error;
    }
  }

  /**
   * Find animal by ID
   * @param {number} id - Animal ID
   * @returns {Promise<Object>} - Animal object
   */
  static async findById(id) {
    try {
      const query = `
        SELECT a.*, 
               u1.username as created_by_username,
               u2.username as updated_by_username
        FROM Animals a
        LEFT JOIN Users u1 ON a.created_by = u1.id
        LEFT JOIN Users u2 ON a.updated_by = u2.id
        WHERE a.id = @id
      `;
      
      const result = await executeQuery(query, { id });
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      logger.error('Error finding animal by ID:', error);
      throw error;
    }
  }

  /**
   * Find animal by tag number
   * @param {string} tagNumber - Animal tag number
   * @returns {Promise<Object>} - Animal object
   */
  static async findByTagNumber(tagNumber) {
    try {
      const query = `
        SELECT a.*, 
               u1.username as created_by_username,
               u2.username as updated_by_username
        FROM Animals a
        LEFT JOIN Users u1 ON a.created_by = u1.id
        LEFT JOIN Users u2 ON a.updated_by = u2.id
        WHERE a.tag_number = @tagNumber
      `;
      
      const result = await executeQuery(query, { tagNumber });
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      logger.error('Error finding animal by tag number:', error);
      throw error;
    }
  }

  /**
   * Update animal information
   * @param {number} id - Animal ID
   * @param {Object} animalData - Animal data to update
   * @returns {Promise<Object>} - Updated animal
   */
  static async update(id, animalData) {
    try {
      // Build update query dynamically based on provided fields
      let updateFields = [];
      const params = { id };

      if (animalData.tagNumber !== undefined) {
        updateFields.push('tag_number = @tagNumber');
        params.tagNumber = animalData.tagNumber;
      }

      if (animalData.animalType !== undefined) {
        updateFields.push('animal_type = @animalType');
        params.animalType = animalData.animalType;
      }

      if (animalData.breed !== undefined) {
        updateFields.push('breed = @breed');
        params.breed = animalData.breed;
      }

      if (animalData.gender !== undefined) {
        updateFields.push('gender = @gender');
        params.gender = animalData.gender;
      }

      if (animalData.birthDate !== undefined) {
        updateFields.push('birth_date = @birthDate');
        params.birthDate = animalData.birthDate;
      }

      if (animalData.weight !== undefined) {
        updateFields.push('weight = @weight');
        params.weight = animalData.weight;
      }

      if (animalData.status !== undefined) {
        updateFields.push('status = @status');
        params.status = animalData.status;
      }

      if (animalData.location !== undefined) {
        updateFields.push('location = @location');
        params.location = animalData.location;
      }

      if (animalData.parentTagNumber !== undefined) {
        updateFields.push('parent_tag_number = @parentTagNumber');
        params.parentTagNumber = animalData.parentTagNumber;
      }

      if (animalData.purchaseDate !== undefined) {
        updateFields.push('purchase_date = @purchaseDate');
        params.purchaseDate = animalData.purchaseDate;
      }

      if (animalData.purchasePrice !== undefined) {
        updateFields.push('purchase_price = @purchasePrice');
        params.purchasePrice = animalData.purchasePrice;
      }

      if (animalData.notes !== undefined) {
        updateFields.push('notes = @notes');
        params.notes = animalData.notes;
      }

      // Add updated_by and updated_at
      if (animalData.updatedBy !== undefined) {
        updateFields.push('updated_by = @updatedBy');
        params.updatedBy = animalData.updatedBy;
      }
      
      updateFields.push('updated_at = GETDATE()');

      // If no fields to update, return animal
      if (updateFields.length === 0) {
        return this.findById(id);
      }

      // Create update query
      const query = `
        UPDATE Animals
        SET ${updateFields.join(', ')}
        OUTPUT INSERTED.id, INSERTED.tag_number, INSERTED.animal_type, INSERTED.breed, 
               INSERTED.gender, INSERTED.birth_date, INSERTED.weight, INSERTED.status, 
               INSERTED.location, INSERTED.parent_tag_number, INSERTED.purchase_date, 
               INSERTED.purchase_price, INSERTED.notes, INSERTED.created_by, 
               INSERTED.created_at, INSERTED.updated_by, INSERTED.updated_at
        WHERE id = @id
      `;

      // Execute query
      const result = await executeQuery(query, params);
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      throw new Error('Failed to update animal');
    } catch (error) {
      logger.error('Error updating animal:', error);
      throw error;
    }
  }

  /**
   * Delete animal
   * @param {number} id - Animal ID
   * @returns {Promise<boolean>} - Success status
   */
  static async delete(id) {
    try {
      const query = `
        DELETE FROM Animals
        WHERE id = @id
      `;
      
      const result = await executeQuery(query, { id });
      
      return result.rowsAffected && result.rowsAffected[0] > 0;
    } catch (error) {
      logger.error('Error deleting animal:', error);
      throw error;
    }
  }

  /**
   * List all animals with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Paginated animals
   */
  static async findAll(options = {}) {
    try {
      const page = options.page || 1;
      const limit = options.limit || 10;
      const offset = (page - 1) * limit;
      
      // Build where clause based on filters
      let whereClause = '1=1'; // Always true condition as a starting point
      const params = { offset, limit };
      
      if (options.animalType) {
        whereClause += ' AND animal_type = @animalType';
        params.animalType = options.animalType;
      }
      
      if (options.status) {
        whereClause += ' AND status = @status';
        params.status = options.status;
      }
      
      if (options.location) {
        whereClause += ' AND location = @location';
        params.location = options.location;
      }
      
      if (options.gender) {
        whereClause += ' AND gender = @gender';
        params.gender = options.gender;
      }
      
      if (options.minWeight) {
        whereClause += ' AND weight >= @minWeight';
        params.minWeight = options.minWeight;
      }
      
      if (options.maxWeight) {
        whereClause += ' AND weight <= @maxWeight';
        params.maxWeight = options.maxWeight;
      }
      
      if (options.search) {
        whereClause += ' AND (tag_number LIKE @search OR breed LIKE @search OR notes LIKE @search)';
        params.search = `%${options.search}%`;
      }
      
      // Build order by clause
      const orderBy = options.sortBy ? `${options.sortBy} ${options.sortOrder || 'ASC'}` : 'id ASC';
      
      const query = `
        SELECT a.*, 
               u1.username as created_by_username,
               u2.username as updated_by_username
        FROM Animals a
        LEFT JOIN Users u1 ON a.created_by = u1.id
        LEFT JOIN Users u2 ON a.updated_by = u2.id
        WHERE ${whereClause}
        ORDER BY ${orderBy}
        OFFSET @offset ROWS
        FETCH NEXT @limit ROWS ONLY;

        SELECT COUNT(*) as total FROM Animals WHERE ${whereClause};
      `;
      
      const result = await executeQuery(query, params);
      
      if (result.recordsets && result.recordsets.length === 2) {
        const animals = result.recordsets[0];
        const total = result.recordsets[1][0].total;
        
        return {
          animals,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
          }
        };
      }
      
      throw new Error('Failed to retrieve animals');
    } catch (error) {
      logger.error('Error finding all animals:', error);
      throw error;
    }
  }

  /**
   * Get animal statistics
   * @returns {Promise<Object>} - Animal statistics
   */
  static async getStatistics() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_animals,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_animals,
          SUM(CASE WHEN status = 'sick' THEN 1 ELSE 0 END) as sick_animals,
          SUM(CASE WHEN status = 'pregnant' THEN 1 ELSE 0 END) as pregnant_animals,
          SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold_animals,
          SUM(CASE WHEN status = 'deceased' THEN 1 ELSE 0 END) as deceased_animals,
          SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_animals,
          SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_animals,
          AVG(weight) as average_weight
        FROM Animals;

        SELECT animal_type, COUNT(*) as count
        FROM Animals
        GROUP BY animal_type
        ORDER BY count DESC;

        SELECT location, COUNT(*) as count
        FROM Animals
        GROUP BY location
        ORDER BY count DESC;
      `;
      
      const result = await executeQuery(query);
      
      if (result.recordsets && result.recordsets.length === 3) {
        return {
          summary: result.recordsets[0][0],
          byType: result.recordsets[1],
          byLocation: result.recordsets[2]
        };
      }
      
      throw new Error('Failed to retrieve animal statistics');
    } catch (error) {
      logger.error('Error getting animal statistics:', error);
      throw error;
    }
  }
}

module.exports = Animal;