export interface Order {
  id: string;
  orderNumber: string;
  orderDate: Date;
  customerInfo: {
    id: string;
    name: string;
    contact: string;
    address: string;
  };
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'refunded';
  paymentMethod: string;
  shippingMethod: string;
  notes: string;
}

export interface OrderItem {
  id: string;
  productType: 'livestock' | 'meat' | 'dairy' | 'other';
  productId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export const mockOrders: Order[] = [
  {
    id: 'ORD001',
    orderNumber: 'ORD-2024-001',
    orderDate: new Date('2024-02-20'),
    customerInfo: {
      id: 'CUST001',
      name: '<PERSON>',
      contact: '******-0123',
      address: '123 Farm Road, Rural City, ST 12345'
    },
    items: [
      {
        id: 'ITEM001',
        productType: 'livestock',
        productId: 'ANM001',
        description: 'Holstein Heifer',
        quantity: 1,
        unitPrice: 2500,
        totalPrice: 2500
      }
    ],
    totalAmount: 2500,
    status: 'confirmed',
    paymentStatus: 'paid',
    paymentMethod: 'bank transfer',
    shippingMethod: 'pickup',
    notes: 'Pickup scheduled for March 1st'
  }
];