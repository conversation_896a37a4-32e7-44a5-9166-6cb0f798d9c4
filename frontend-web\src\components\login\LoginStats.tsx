import React from 'react';
import { Box, Typography, useTheme, alpha, Divider } from '@mui/material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { useThemeContext } from '../../contexts/ThemeContext';

interface Stat {
  value: string;
  label: string;
}

interface LoginStatsProps {
  stats: Stat[];
}

/**
 * LoginStats - A component to display statistics on the login page
 * Features animated cards with hover effects
 */
const LoginStats: React.FC<LoginStatsProps> = ({ stats }) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'flex-start',
        width: '100%',
        p: { xs: 2, md: 4 },
        pt: { xs: 4, md: 6 },
        color: 'white'
      }}
    >
      {/* Header */}
      <motion.div
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{
          type: "spring",
          stiffness: 100,
          damping: 15,
          delay: 0.5
        }}
        style={{ maxWidth: '500px', textAlign: 'center', marginBottom: '2rem' }}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <Typography variant="h2" sx={{
            fontWeight: 'bold',
            mb: 2,
            textShadow: '0 2px 10px rgba(0,0,0,0.3)',
            letterSpacing: '1px',
            color: '#ffffff'
          }}>
            MayCaiphus
          </Typography>
        </motion.div>

        <motion.div
          initial={{ width: 0 }}
          animate={{ width: '80px' }}
          transition={{ delay: 1.1, duration: 0.5 }}
          style={{ margin: '16px auto' }}
        >
          <Divider sx={{
            borderColor: theme.palette.primary.main,
            borderWidth: 2
          }} />
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.3, duration: 0.5 }}
        >
          <Typography variant="h6" sx={{
            mb: 3,
            fontWeight: 'light',
            textShadow: '0 1px 8px rgba(0,0,0,0.2)',
            letterSpacing: '0.5px',
            opacity: 0.9
          }}>
            {translate('dashboard.subtitle')}
          </Typography>
        </motion.div>
      </motion.div>

      {/* Stats */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Box sx={{
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'center',
          gap: 3
        }}>
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{
                y: -5,
                scale: 1.05,
                boxShadow: '0 12px 30px rgba(0,0,0,0.2)',
                rotate: [0, 1, -1, 0]
              }}
              transition={{
                rotate: { duration: 0.3, ease: "easeInOut" }
              }}
            >
              <Box sx={{
                backgroundColor: alpha(theme.palette.background.paper, 0.05),
                backdropFilter: 'blur(5px)',
                p: 3,
                borderRadius: 0,
                border: 'none',
                boxShadow: 'none',
                transition: 'all 0.3s ease',
                width: 140,
                height: 120,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                textAlign: 'center',
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  right: 0,
                  height: '1px',
                  background: `linear-gradient(90deg, transparent, ${alpha(theme.palette.primary.light, 0.5)}, transparent)`,
                  animation: 'shimmer 2s infinite'
                }
              }}>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 260,
                    damping: 20,
                    delay: 1.5 + (index * 0.1)
                  }}
                >
                  <Typography variant="h3" sx={{
                    fontWeight: 'bold',
                    mb: 1,
                    color: theme.palette.primary.light
                  }}>
                    {stat.value}
                  </Typography>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.8 + (index * 0.1), duration: 0.5 }}
                >
                  <Typography variant="body2" sx={{
                    color: 'white',
                    fontWeight: 'medium',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    fontSize: '0.75rem'
                  }}>
                    {stat.label}
                  </Typography>
                </motion.div>
              </Box>
            </motion.div>
          ))}
        </Box>
      </motion.div>
    </Box>
  );
};

export default LoginStats;
