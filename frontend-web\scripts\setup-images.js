/**
 * <PERSON><PERSON><PERSON> to set up images for the AMPD Livestock Management application
 * This script creates the necessary directories and copies images to the public folder
 */
const fs = require('fs');
const path = require('path');

// Define paths
const publicDir = path.resolve(__dirname, '../public');
const imagesDir = path.resolve(publicDir, 'images');

// Create directories if they don't exist
const directories = [
  'animals',
  'modules/animals',
  'modules/health',
  'modules/breeding',
  'modules/feeding',
  'modules/commercial',
  'modules/financial',
  'modules/compliance',
  'modules/resources',
  'modules/rfid',
  'dashboard',
  'sponsors'
];

// Create directories
directories.forEach(dir => {
  const fullPath = path.resolve(imagesDir, dir);
  if (!fs.existsSync(fullPath)) {
    console.log(`Creating directory: ${fullPath}`);
    fs.mkdirSync(fullPath, { recursive: true });
  }
});

// Create placeholder images if they don't exist
const placeholderImages = [
  // Animal images
  { path: 'animals/cattle-1.jpeg', width: 800, height: 600, color: '#8B4513' },
  { path: 'animals/cattle-2.avif', width: 800, height: 600, color: '#A0522D' },
  { path: 'animals/cattle-3.jpeg', width: 800, height: 600, color: '#CD853F' },
  { path: 'animals/cattle-4.jpeg', width: 800, height: 600, color: '#D2691E' },
  { path: 'animals/cattle-5.avif', width: 800, height: 600, color: '#B8860B' },
  { path: 'animals/cattle-6.jpeg', width: 800, height: 600, color: '#DAA520' },
  { path: 'animals/sheep-1.jpeg', width: 800, height: 600, color: '#F5DEB3' },
  { path: 'animals/sheep-2.jpeg', width: 800, height: 600, color: '#FFE4B5' },
  { path: 'animals/goat-1.jpeg', width: 800, height: 600, color: '#FFDEAD' },
  { path: 'animals/goat-2.jpeg', width: 800, height: 600, color: '#F0E68C' },

  // Module background images
  { path: 'modules/animals/cattle-1.jpeg', width: 1200, height: 800, color: '#556B2F' },
  { path: 'modules/animals/cattle-2.avif', width: 1200, height: 800, color: '#6B8E23' },
  { path: 'modules/animals/cattle-3.jpeg', width: 1200, height: 800, color: '#808000' },
  { path: 'modules/animals/cattle-4.jpeg', width: 1200, height: 800, color: '#556B2F' },
  { path: 'modules/animals/cattle-5.avif', width: 1200, height: 800, color: '#6B8E23' },

  { path: 'modules/health/health-main.png', width: 1200, height: 800, color: '#4682B4' },
  { path: 'modules/health/veterinary-1.jpg', width: 1200, height: 800, color: '#5F9EA0' },
  { path: 'modules/health/veterinary-2.jpg', width: 1200, height: 800, color: '#4169E1' },
  { path: 'modules/health/veterinary-3.jpg', width: 1200, height: 800, color: '#6495ED' },
  { path: 'modules/health/veterinary-main.jpg', width: 1200, height: 800, color: '#1E90FF' },

  { path: 'modules/breeding/breeding-main.png', width: 1200, height: 800, color: '#FF69B4' },

  { path: 'modules/feeding/feed-main.jpeg', width: 1200, height: 800, color: '#BDB76B' },
  { path: 'modules/feeding/feed-2.jpeg', width: 1200, height: 800, color: '#DAA520' },
  { path: 'modules/feeding/feed-sheep.jpeg', width: 1200, height: 800, color: '#F4A460' },

  { path: 'modules/commercial/commercial-main.webp', width: 1200, height: 800, color: '#2E8B57' },
  { path: 'modules/commercial/commercial-1.jpeg', width: 1200, height: 800, color: '#3CB371' },
  { path: 'modules/commercial/commercial-2.jpeg', width: 1200, height: 800, color: '#20B2AA' },
  { path: 'modules/commercial/commercial-3.jpeg', width: 1200, height: 800, color: '#66CDAA' },

  { path: 'modules/rfid/rfid-1.webp', width: 1200, height: 800, color: '#708090' },
  { path: 'modules/rfid/rfid-2.jpg', width: 1200, height: 800, color: '#778899' },
  { path: 'modules/rfid/rfid-3.jpg', width: 1200, height: 800, color: '#B0C4DE' },
  { path: 'modules/rfid/rfid-4.jpg', width: 1200, height: 800, color: '#4682B4' },

  { path: 'dashboard/main-dashboard.jpg', width: 1200, height: 800, color: '#0F766E' },

  // Sponsor images
  { path: 'sponsors/land bank.png', width: 400, height: 200, color: '#FFFFFF' },
  { path: 'sponsors/Agricultural research council.png', width: 400, height: 200, color: '#FFFFFF' },
  { path: 'sponsors/Agriculture , land reform & rural development.png', width: 400, height: 200, color: '#FFFFFF' },
  { path: 'sponsors/National Agricultural Marketing Council.png', width: 400, height: 200, color: '#FFFFFF' },
  { path: 'sponsors/Perishable Productst Control Board.png', width: 400, height: 200, color: '#FFFFFF' }
];

// Create HTML placeholder images
placeholderImages.forEach(img => {
  const fullPath = path.resolve(imagesDir, img.path);

  // Skip if file already exists
  if (fs.existsSync(fullPath)) {
    console.log(`Image already exists: ${fullPath}`);
    return;
  }

  // Create directory if it doesn't exist
  const dir = path.dirname(fullPath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }

  // Create a simple HTML file with a colored div as a placeholder
  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Placeholder Image</title>
  <style>
    body, html {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: ${img.color};
      color: white;
      font-family: Arial, sans-serif;
      text-align: center;
    }
    .placeholder {
      width: ${img.width}px;
      height: ${img.height}px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: ${img.color};
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    h1 {
      margin: 0;
      padding: 0;
      font-size: 24px;
    }
    p {
      margin: 10px 0 0 0;
      padding: 0;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div class="placeholder">
    <h1>AMPD Livestock</h1>
    <p>${path.basename(img.path, path.extname(img.path))}</p>
  </div>
</body>
</html>
  `;

  // Write the HTML file
  console.log(`Creating placeholder image: ${fullPath}`);
  fs.writeFileSync(fullPath, html);
});

console.log('Image setup completed successfully!');
