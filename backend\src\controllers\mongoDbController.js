/**
 * MongoDB Controller
 *
 * This controller handles MongoDB operations for the API.
 */

const { getDB, getCollection } = require('../config/db');
const logger = require('../utils/logger');

const mongoDbController = {
  // Animal Management
  getAllAnimals: async (req, res) => {
    try {
      const collection = await getCollection('animals');
      const animals = await collection.find({}).toArray();
      res.json(animals);
    } catch (error) {
      logger.error('Error fetching animals from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getAnimalById: async (req, res) => {
    try {
      const id = req.params.id;
      const collection = await getCollection('animals');
      const animal = await collection.findOne({ id: parseInt(id) });

      if (!animal) {
        return res.status(404).json({ error: 'Animal not found' });
      }

      res.json(animal);
    } catch (error) {
      logger.error('Error fetching animal from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createAnimal: async (req, res) => {
    try {
      const animalData = req.body;
      const collection = await getCollection('animals');

      // Generate a new ID
      const lastAnimal = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastAnimal.length > 0 ? lastAnimal[0].id + 1 : 1;

      const result = await collection.insertOne({ ...animalData, id: newId });

      res.status(201).json({
        id: newId,
        ...animalData
      });
    } catch (error) {
      logger.error('Error creating animal in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  updateAnimal: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const animalData = req.body;
      const collection = await getCollection('animals');

      const result = await collection.updateOne(
        { id },
        { $set: animalData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Animal not found' });
      }

      res.json({ id, ...animalData });
    } catch (error) {
      logger.error('Error updating animal in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  deleteAnimal: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const collection = await getCollection('animals');

      const result = await collection.deleteOne({ id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Animal not found' });
      }

      res.json({ message: 'Animal deleted successfully' });
    } catch (error) {
      logger.error('Error deleting animal from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Health Management
  getAllHealthRecords: async (req, res) => {
    try {
      const collection = await getCollection('health_records');
      const records = await collection.find({}).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching health records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getHealthRecordById: async (req, res) => {
    try {
      const id = req.params.id;
      const collection = await getCollection('health_records');
      const record = await collection.findOne({ id });

      if (!record) {
        return res.status(404).json({ error: 'Health record not found' });
      }

      res.json(record);
    } catch (error) {
      logger.error('Error fetching health record from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // These methods are referenced in healthRoutes.js
  getHealthRecords: async (req, res) => {
    try {
      const { animalId, type, fromDate, toDate } = req.query;
      const collection = await getCollection('health_records');

      let query = {};
      if (animalId) query.animalId = parseInt(animalId);
      if (type) query.type = type;
      if (fromDate || toDate) {
        query.date = {};
        if (fromDate) query.date.$gte = new Date(fromDate);
        if (toDate) query.date.$lte = new Date(toDate);
      }

      const records = await collection.find(query).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching health records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getVaccinations: async (req, res) => {
    try {
      const { animalId, vaccineType } = req.query;
      const collection = await getCollection('vaccinations');

      let query = {};
      if (animalId) query.animalId = parseInt(animalId);
      if (vaccineType) query.vaccineType = vaccineType;

      const vaccinations = await collection.find(query).toArray();
      res.json(vaccinations);
    } catch (error) {
      logger.error('Error fetching vaccinations from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createHealthRecord: async (req, res) => {
    try {
      const recordData = req.body;
      const collection = await getCollection('health_records');

      // Generate a new ID
      const lastRecord = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastRecord.length > 0 ? lastRecord[0].id + 1 : 1;

      const result = await collection.insertOne({ ...recordData, id: newId });

      res.status(201).json({
        id: newId,
        ...recordData
      });
    } catch (error) {
      logger.error('Error creating health record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  updateHealthRecord: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const recordData = req.body;
      const collection = await getCollection('health_records');

      const result = await collection.updateOne(
        { id },
        { $set: recordData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Health record not found' });
      }

      res.json({ id, ...recordData });
    } catch (error) {
      logger.error('Error updating health record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  deleteHealthRecord: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const collection = await getCollection('health_records');

      const result = await collection.deleteOne({ id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Health record not found' });
      }

      res.json({ message: 'Health record deleted successfully' });
    } catch (error) {
      logger.error('Error deleting health record from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Breeding Management
  getAllBreedingRecords: async (req, res) => {
    try {
      const collection = await getCollection('breeding_records');
      const records = await collection.find({}).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching breeding records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // These methods are referenced in breedingRoutes.js
  getBreedingRecords: async (req, res) => {
    try {
      const { femaleId, maleId, status } = req.query;
      const collection = await getCollection('breeding_records');

      let query = {};
      if (femaleId) query.femaleId = parseInt(femaleId);
      if (maleId) query.maleId = parseInt(maleId);
      if (status) query.status = status;

      const records = await collection.find(query).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching breeding records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getCalvingRecords: async (req, res) => {
    try {
      const { breedingRecordId } = req.query;
      const collection = await getCollection('calving_records');

      let query = {};
      if (breedingRecordId) query.breedingRecordId = parseInt(breedingRecordId);

      const records = await collection.find(query).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching calving records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createBreedingRecord: async (req, res) => {
    try {
      const recordData = req.body;
      const collection = await getCollection('breeding_records');

      // Generate a new ID
      const lastRecord = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastRecord.length > 0 ? lastRecord[0].id + 1 : 1;

      const result = await collection.insertOne({ ...recordData, id: newId });

      res.status(201).json({
        id: newId,
        ...recordData
      });
    } catch (error) {
      logger.error('Error creating breeding record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  updateBreedingRecord: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const recordData = req.body;
      const collection = await getCollection('breeding_records');

      const result = await collection.updateOne(
        { id },
        { $set: recordData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Breeding record not found' });
      }

      res.json({ id, ...recordData });
    } catch (error) {
      logger.error('Error updating breeding record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  deleteBreedingRecord: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const collection = await getCollection('breeding_records');

      const result = await collection.deleteOne({ id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Breeding record not found' });
      }

      res.json({ message: 'Breeding record deleted successfully' });
    } catch (error) {
      logger.error('Error deleting breeding record from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createCalvingRecord: async (req, res) => {
    try {
      const recordData = req.body;
      const collection = await getCollection('calving_records');

      // Generate a new ID
      const lastRecord = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastRecord.length > 0 ? lastRecord[0].id + 1 : 1;

      const result = await collection.insertOne({ ...recordData, id: newId });

      res.status(201).json({
        id: newId,
        ...recordData
      });
    } catch (error) {
      logger.error('Error creating calving record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Feeding Management
  getAllFeedingRecords: async (req, res) => {
    try {
      const collection = await getCollection('feeding_records');
      const records = await collection.find({}).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching feeding records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // These methods are referenced in feedingRoutes.js
  getFeedingRecords: async (req, res) => {
    try {
      const { feedType, location, fromDate, toDate } = req.query;
      const collection = await getCollection('feeding_records');

      let query = {};
      if (feedType) query.feedType = feedType;
      if (location) query.location = location;
      if (fromDate || toDate) {
        query.date = {};
        if (fromDate) query.date.$gte = new Date(fromDate);
        if (toDate) query.date.$lte = new Date(toDate);
      }

      const records = await collection.find(query).toArray();
      res.json(records);
    } catch (error) {
      logger.error('Error fetching feeding records from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getFeedInventory: async (req, res) => {
    try {
      const { feedType, location } = req.query;
      const collection = await getCollection('feed_inventory');

      let query = {};
      if (feedType) query.feedType = feedType;
      if (location) query.location = location;

      const inventory = await collection.find(query).toArray();
      res.json(inventory);
    } catch (error) {
      logger.error('Error fetching feed inventory from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createFeedingRecord: async (req, res) => {
    try {
      const recordData = req.body;
      const collection = await getCollection('feeding_records');

      // Generate a new ID
      const lastRecord = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastRecord.length > 0 ? lastRecord[0].id + 1 : 1;

      const result = await collection.insertOne({ ...recordData, id: newId });

      res.status(201).json({
        id: newId,
        ...recordData
      });
    } catch (error) {
      logger.error('Error creating feeding record in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  addFeedInventory: async (req, res) => {
    try {
      const inventoryData = req.body;
      const collection = await getCollection('feed_inventory');

      // Generate a new ID
      const lastItem = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastItem.length > 0 ? lastItem[0].id + 1 : 1;

      const result = await collection.insertOne({ ...inventoryData, id: newId });

      res.status(201).json({
        id: newId,
        ...inventoryData
      });
    } catch (error) {
      logger.error('Error adding feed inventory in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  updateFeedInventory: async (req, res) => {
    try {
      const id = req.params.id;
      const inventoryData = req.body;
      const collection = await getCollection('feed_inventory');

      const result = await collection.updateOne(
        { id: parseInt(id) },
        { $set: inventoryData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Feed inventory item not found' });
      }

      res.json({ id, ...inventoryData });
    } catch (error) {
      logger.error('Error updating feed inventory in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Inventory Management
  getAllInventory: async (req, res) => {
    try {
      const collection = await getCollection('inventory');
      const inventory = await collection.find({}).toArray();
      res.json(inventory);
    } catch (error) {
      logger.error('Error fetching inventory from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // These methods are referenced in inventoryRoutes.js
  getInventory: async (req, res) => {
    try {
      const { category, condition } = req.query;
      const collection = await getCollection('inventory');

      let query = {};
      if (category) query.category = category;
      if (condition) query.condition = condition;

      const inventory = await collection.find(query).toArray();
      res.json(inventory);
    } catch (error) {
      logger.error('Error fetching inventory from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  getInventoryById: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const collection = await getCollection('inventory');
      const item = await collection.findOne({ id });

      if (!item) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      res.json(item);
    } catch (error) {
      logger.error('Error fetching inventory item from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  createInventoryItem: async (req, res) => {
    try {
      const itemData = req.body;
      const collection = await getCollection('inventory');

      // Generate a new ID
      const lastItem = await collection.find().sort({ id: -1 }).limit(1).toArray();
      const newId = lastItem.length > 0 ? lastItem[0].id + 1 : 1;

      const result = await collection.insertOne({ ...itemData, id: newId });

      res.status(201).json({
        id: newId,
        ...itemData
      });
    } catch (error) {
      logger.error('Error creating inventory item in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  updateInventoryItem: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const itemData = req.body;
      const collection = await getCollection('inventory');

      const result = await collection.updateOne(
        { id },
        { $set: itemData }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      res.json({ id, ...itemData });
    } catch (error) {
      logger.error('Error updating inventory item in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  deleteInventoryItem: async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const collection = await getCollection('inventory');

      const result = await collection.deleteOne({ id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Inventory item not found' });
      }

      res.json({ message: 'Inventory item deleted successfully' });
    } catch (error) {
      logger.error('Error deleting inventory item from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Pasture Management
  getAllPastures: async (req, res) => {
    try {
      const collection = await getCollection('pastures');
      const pastures = await collection.find({}).toArray();
      res.json(pastures);
    } catch (error) {
      logger.error('Error fetching pastures from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  // Financial Management
  getAllExpenses: async (req, res) => {
    try {
      const collection = await getCollection('expenses');
      const expenses = await collection.find({}).toArray();
      res.json(expenses);
    } catch (error) {
      logger.error('Error fetching expenses from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = mongoDbController;
