import React from 'react';
import { render, screen } from '../../utils/testUtils';
import LazyImage from './LazyImage';

// Mock IntersectionObserver
class MockIntersectionObserver implements IntersectionObserver {
  readonly root: Element | Document | null;
  readonly rootMargin: string;
  readonly thresholds: ReadonlyArray<number>;

  constructor(callback: IntersectionObserverCallback, options?: IntersectionObserverInit) {
    this.root = options?.root ?? null;
    this.rootMargin = options?.rootMargin ?? '0px';
    this.thresholds = Array.isArray(options?.threshold) ? options.threshold : [options?.threshold ?? 0];

    // Immediately trigger the callback to simulate element in view
    setTimeout(() => {
      callback([{
        boundingClientRect: {} as DOMRectReadOnly,
        intersectionRatio: 1,
        intersectionRect: {} as DOMRectReadOnly,
        isIntersecting: true,
        rootBounds: null,
        target: {} as Element,
        time: Date.now()
      }], this as unknown as IntersectionObserver);
    }, 0);
  }

  observe() { /* Mock implementation */ }
  unobserve() { /* Mock implementation */ }
  disconnect() { /* Mock implementation */ }
  takeRecords(): IntersectionObserverEntry[] { return []; }
}

// Set up the mock
global.IntersectionObserver = MockIntersectionObserver as unknown as typeof IntersectionObserver;

describe('LazyImage Component', () => {
  it('renders with correct alt text', () => {
    render(
      <LazyImage
        src="https://example.com/image.jpg"
        alt="Test image"
        width={200}
        height={200}
      />
    );

    // The image should be in the document with the correct alt text
    const image = screen.getByAltText('Test image');
    expect(image).toBeInTheDocument();
  });

  it('applies custom styles', () => {
    render(
      <LazyImage
        src="https://example.com/image.jpg"
        alt="Styled image"
        width={300}
        height={200}
        sx={{ borderRadius: '8px' }}
      />
    );

    const image = screen.getByAltText('Styled image');
    expect(image).toBeInTheDocument();
    // In a real test, we would check for the applied styles
  });

  it('handles loading state correctly', () => {
    const handleLoad = jest.fn();

    render(
      <LazyImage
        src="https://example.com/image.jpg"
        alt="Loading image"
        onLoad={handleLoad}
        loadingEffect="skeleton"
      />
    );

    // In a real test, we would trigger the load event and check if the callback was called
    // and if the skeleton was shown during loading
  });

  it('handles error state correctly', () => {
    const handleError = jest.fn();

    render(
      <LazyImage
        src="https://example.com/nonexistent.jpg"
        alt="Error image"
        onError={handleError}
      />
    );

    // In a real test, we would trigger the error event and check if the callback was called
    // and if the error fallback was shown
  });
});
