import { v4 as uuidv4 } from 'uuid';
import { BaseService, ServiceResult } from '../core/BaseService';
import { BkbAuctionCalendarMonth, BkbAuctionType, BkbAuctionCategory, BkbAuction } from '../../types/commercial/bkbAuction';
import ServiceRegistry from '../core/ServiceRegistry';

/**
 * Service for BKB Livestock Auction data with improved error handling
 */
export class BkbAuctionService extends BaseService {
  private baseUrl = 'https://events.bkb.co.za';
  private apiEndpoint = '/api/events'; // Hypothetical endpoint
  private useMockData = true; // Set to false when real API is available
  
  constructor() {
    super('BkbAuctionService');
  }
  
  /**
   * Fetch auctions for a specific month with proper error handling
   * @param year The year
   * @param month The month (0-11)
   * @returns Promise with auction calendar data
   */
  async fetchMonthlyAuctions(year: number, month: number): Promise<ServiceResult<BkbAuctionCalendarMonth>> {
    this.logInfo(`Fetching auctions for ${year}-${month + 1}`);
    
    if (this.useMockData) {
      return this.executeOperation(
        async () => this.getMockData(year, month),
        this.getEmptyCalendarData(year, month),
        'Failed to generate mock auction data'
      );
    }
    
    return this.executeOperation(
      async () => {
        const response = await fetch(`${this.baseUrl}${this.apiEndpoint}?year=${year}&month=${month + 1}`);
        
        if (!response.ok) {
          throw new Error(`API returned status ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        return this.parseApiResponse(data, year, month);
      },
      this.getMockData(year, month), // Fallback to mock data if API fails
      `Failed to fetch BKB auctions for ${year}-${month + 1}`
    );
  }
  
  /**
   * Get calendar subscription URL
   * @returns The subscription URL
   */
  getCalendarSubscriptionUrl(): string {
    return `${this.baseUrl}/subscribe`;
  }
  
  /**
   * Parse API response into our calendar data format
   * @param apiData The raw API data
   * @param year The year
   * @param month The month (0-11)
   * @returns Formatted calendar data
   */
  private parseApiResponse(apiData: any, year: number, month: number): BkbAuctionCalendarMonth {
    try {
      // This would be implemented based on the actual API response format
      // For now, we'll just return mock data
      return this.getMockData(year, month);
    } catch (error) {
      this.logError('Error parsing API response', error);
      throw new Error('Failed to parse auction data from API');
    }
  }
  
  /**
   * Generate empty calendar data as a fallback
   * @param year The year
   * @param month The month (0-11)
   * @returns Empty calendar data
   */
  private getEmptyCalendarData(year: number, month: number): BkbAuctionCalendarMonth {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const days = Array.from({ length: daysInMonth }, (_, i) => ({
      date: new Date(year, month, i + 1),
      auctions: []
    }));
    
    return { year, month, days };
  }
  
  /**
   * Generate mock auction data
   * @param year The year
   * @param month The month (0-11)
   * @returns Mock calendar data
   */
  private getMockData(year: number, month: number): BkbAuctionCalendarMonth {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const days = Array.from({ length: daysInMonth }, (_, i) => {
      const date = new Date(year, month, i + 1);
      return {
        date,
        auctions: this.generateMockAuctions(date)
      };
    });

    return {
      year,
      month,
      days
    };
  }

  /**
   * Generate mock auctions for a specific date
   * @param date The date
   * @returns Array of mock auctions
   */
  private generateMockAuctions(date: Date): BkbAuction[] {
    // Skip weekends and generate 0-2 auctions for weekdays
    const day = date.getDay();
    if (day === 0 || day === 6) return []; // Weekend

    const numAuctions = Math.floor(Math.random() * 3); // 0-2 auctions
    if (numAuctions === 0) return [];

    return Array.from({ length: numAuctions }, (_, i) => this.createMockAuction(date, i));
  }

  /**
   * Create a single mock auction
   * @param date The auction date
   * @param index Index for uniqueness
   * @returns A mock auction
   */
  private createMockAuction(date: Date, index: number): BkbAuction {
    const locations = [
      'BADPLAAS', 'NEWCASTLE', 'BULTFONTEIN', 'ERMELO',
      'KAREEDOUW', 'DUNDEE', 'DEVON', 'WITBANK',
      'BETHLEHEM', 'FOURIESBURG', 'KAALAAGTE', 'POTCHEFSTROOM'
    ];
    
    const types: BkbAuctionType[] = ['weekly', 'monthly', 'fortnightly', 'special', 'prime'];
    const categories: BkbAuctionCategory[] = ['livestock', 'fibre', 'irrigation', 'grazing'];
    
    const location = locations[Math.floor(Math.random() * locations.length)];
    const type = types[Math.floor(Math.random() * types.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    
    // Generate start time between 9am and 2pm
    const startHour = 9 + Math.floor(Math.random() * 5);
    const startTime = `${startHour}:00 am`;
    
    // End time is 2-4 hours after start
    const endHour = startHour + 2 + Math.floor(Math.random() * 3);
    const endTime = `${endHour > 12 ? endHour - 12 : endHour}:00 ${endHour >= 12 ? 'pm' : 'am'}`;
    
    return {
      id: uuidv4(),
      title: `${location} ${type.toUpperCase()} ${category === 'livestock' ? 'AUCTION' : category.toUpperCase()}`,
      location,
      date: new Date(date),
      startTime,
      endTime,
      type,
      category,
      region: this.getRandomRegion(),
      province: this.getRandomProvince(),
      description: `${location} ${type} ${category} auction. Contact BKB for more details.`,
      url: `${this.baseUrl}/event/${location.toLowerCase()}-${type}-${category}`
    };
  }
  
  /**
   * Get a random region
   * @returns Random region name
   */
  private getRandomRegion(): string {
    const regions = ['Northern', 'Southern', 'Eastern', 'Western', 'Central'];
    return regions[Math.floor(Math.random() * regions.length)];
  }
  
  /**
   * Get a random province
   * @returns Random province name
   */
  private getRandomProvince(): string {
    const provinces = [
      'Eastern Cape', 'Free State', 'Gauteng', 'KwaZulu-Natal',
      'Limpopo', 'Mpumalanga', 'North West', 'Northern Cape', 'Western Cape'
    ];
    return provinces[Math.floor(Math.random() * provinces.length)];
  }
}

// Register the service with the registry
const bkbAuctionService = ServiceRegistry.registerClass('bkbAuctionService', BkbAuctionService);

export default bkbAuctionService;
