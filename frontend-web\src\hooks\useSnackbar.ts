import { useContext } from 'react';
import { useSnackbar as useSnackbarContext } from '../contexts/SnackbarContext';
import { AlertColor } from '@mui/material';

/**
 * Hook to use the snackbar functionality
 * @returns Snackbar context functions
 */
export const useSnackbar = () => {
  const context = useSnackbarContext();

  if (!context) {
    throw new Error('useSnackbar must be used within a SnackbarProvider');
  }

  return {
    showSnackbar: (message: string, severity: AlertColor = 'info') => {
      context.showSnackbar(message, severity);
    }
  };
};

export default useSnackbar;
