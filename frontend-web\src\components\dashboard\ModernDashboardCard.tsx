import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  IconButton,
  useTheme,
  alpha,
  Chip,
  LinearProgress,
} from '@mui/material';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  MoreVert,
  ArrowForward 
} from '@mui/icons-material';

interface ModernDashboardCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ElementType;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  progress?: {
    value: number;
    max: number;
    label?: string;
  };
  gradient: string;
  onClick?: () => void;
  actionLabel?: string;
  delay?: number;
  size?: 'small' | 'medium' | 'large';
}

const ModernDashboardCard: React.FC<ModernDashboardCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  progress,
  gradient,
  onClick,
  actionLabel,
  delay = 0,
  size = 'medium',
}) => {
  const theme = useTheme();

  const cardHeight = {
    small: 140,
    medium: 180,
    large: 220,
  };

  const iconSize = {
    small: 32,
    medium: 40,
    large: 48,
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay,
        ease: [0.4, 0, 0.2, 1] 
      }}
      whileHover={{ 
        y: -8,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
    >
      <Card
        onClick={onClick}
        sx={{
          height: cardHeight[size],
          cursor: onClick ? 'pointer' : 'default',
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          background: `
            linear-gradient(135deg, 
              ${alpha(theme.palette.background.paper, 0.9)} 0%, 
              ${alpha(theme.palette.background.paper, 0.95)} 100%
            )
          `,
          backdropFilter: 'blur(20px)',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            boxShadow: `0 20px 60px ${alpha(theme.palette.primary.main, 0.15)}`,
            '& .card-icon': {
              transform: 'scale(1.1) rotate(5deg)',
            },
            '& .card-action': {
              opacity: 1,
              transform: 'translateX(0)',
            },
            '& .gradient-overlay': {
              opacity: 0.1,
            },
          },
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: gradient,
            borderRadius: '12px 12px 0 0',
          },
        }}
      >
        {/* Gradient Overlay */}
        <Box
          className="gradient-overlay"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: gradient,
            opacity: 0.03,
            transition: 'opacity 0.3s ease',
            borderRadius: 3,
          }}
        />

        {/* Floating Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 70%)`,
          }}
        />

        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          position: 'relative',
          zIndex: 1,
          p: 3,
        }}>
          {/* Header */}
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Box display="flex" alignItems="center" gap={1.5}>
              <Box
                className="card-icon"
                sx={{
                  width: iconSize[size],
                  height: iconSize[size],
                  borderRadius: 2,
                  background: gradient,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  transition: 'all 0.3s ease',
                  boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
                }}
              >
                <Icon sx={{ 
                  fontSize: iconSize[size] * 0.6, 
                  color: 'white',
                }} />
              </Box>
              
              <Box>
                <Typography 
                  variant={size === 'small' ? 'caption' : 'body2'} 
                  sx={{ 
                    color: theme.palette.text.secondary,
                    fontWeight: 500,
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                  }}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: theme.palette.text.disabled,
                      display: 'block',
                    }}
                  >
                    {subtitle}
                  </Typography>
                )}
              </Box>
            </Box>

            <IconButton 
              size="small" 
              sx={{ 
                color: theme.palette.text.secondary,
                opacity: 0.7,
                '&:hover': { opacity: 1 }
              }}
            >
              <MoreVert fontSize="small" />
            </IconButton>
          </Box>

          {/* Value */}
          <Box flex={1} display="flex" flexDirection="column" justifyContent="center">
            <Typography 
              variant={size === 'large' ? 'h3' : size === 'medium' ? 'h4' : 'h5'}
              sx={{ 
                fontWeight: 800,
                color: theme.palette.text.primary,
                lineHeight: 1.2,
                mb: 0.5,
              }}
            >
              {value}
            </Typography>

            {/* Trend */}
            {trend && (
              <Box display="flex" alignItems="center" gap={0.5} mb={1}>
                <Chip
                  icon={trend.isPositive ? <TrendingUp /> : <TrendingDown />}
                  label={`${trend.isPositive ? '+' : ''}${trend.value}%`}
                  size="small"
                  sx={{
                    height: 24,
                    fontSize: '0.75rem',
                    fontWeight: 600,
                    backgroundColor: trend.isPositive 
                      ? alpha(theme.palette.success.main, 0.1)
                      : alpha(theme.palette.error.main, 0.1),
                    color: trend.isPositive 
                      ? theme.palette.success.main
                      : theme.palette.error.main,
                    border: `1px solid ${trend.isPositive 
                      ? alpha(theme.palette.success.main, 0.2)
                      : alpha(theme.palette.error.main, 0.2)
                    }`,
                    '& .MuiChip-icon': {
                      fontSize: 16,
                    },
                  }}
                />
                {trend.label && (
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: theme.palette.text.secondary,
                      ml: 0.5,
                    }}
                  >
                    {trend.label}
                  </Typography>
                )}
              </Box>
            )}

            {/* Progress */}
            {progress && (
              <Box mb={1}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: theme.palette.text.secondary,
                      fontWeight: 500,
                    }}
                  >
                    {progress.label || 'Progress'}
                  </Typography>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: theme.palette.text.primary,
                      fontWeight: 600,
                    }}
                  >
                    {progress.value}/{progress.max}
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(progress.value / progress.max) * 100}
                  sx={{
                    height: 6,
                    borderRadius: 3,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      background: gradient,
                    },
                  }}
                />
              </Box>
            )}
          </Box>

          {/* Action */}
          {onClick && actionLabel && (
            <Box 
              className="card-action"
              display="flex" 
              alignItems="center" 
              justifyContent="space-between"
              sx={{
                opacity: 0.7,
                transform: 'translateX(-10px)',
                transition: 'all 0.3s ease',
              }}
            >
              <Typography 
                variant="body2" 
                sx={{ 
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                }}
              >
                {actionLabel}
              </Typography>
              <ArrowForward 
                sx={{ 
                  fontSize: 16,
                  color: theme.palette.primary.main,
                }} 
              />
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ModernDashboardCard;
