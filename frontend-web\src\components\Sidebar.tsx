import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Divider,
  useTheme,
  alpha,
  Tooltip,
  IconButton
} from '@mui/material';
import UpgradePrompt from './UpgradePrompt';
import {
  Dashboard as DashboardIcon,
  Pets as AnimalsIcon,
  LocalHospital as HealthIcon,
  Favorite as BreedingIcon,
  Restaurant as FeedingIcon,
  AttachMoney as FinancialIcon,
  Inventory as InventoryIcon,
  Store as CommercialIcon,
  Assessment as ReportsIcon,
  MenuBook as ResourcesIcon,
  Settings as SettingsIcon,
  Security as ComplianceIcon,
  Analytics as AnalyticsIcon,
  ExpandLess,
  ExpandMore,
  ChevronLeft,
  ChevronRight,
  Lock
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useSubscription } from '../contexts/SubscriptionContext';
import { useAuth } from '../contexts/AuthContext';
import { ROUTES } from '../constants/routes';
import BetaFeatureGate from './beta/BetaFeatureGate';
import BetaUpgradeButton from './beta/BetaUpgradeButton';
import AgriIntelLogo from './common/AgriIntelLogo';
import {
  canAccessModule
} from '../utils/unifiedAccessControl';
import './SidebarFix.css';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  isMobile: boolean;
}

interface SubModule {
  name: string;
  path: string;
  icon?: React.ReactNode;
  premiumOnly?: boolean; // Whether this sub-module requires premium subscription
}

interface ModuleItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  subModules?: SubModule[];
  betaAccess?: boolean; // Whether this module is available in beta
  premiumOnly?: boolean; // Whether this module requires premium subscription
  feature?: string; // Feature name for subscription checking
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle, isMobile }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const { translate } = useLanguage();
  const { subscription, checkFeatureAccess } = useSubscription();
  const { user } = useAuth();
  const [expandedModules, setExpandedModules] = useState<string[]>([]);
  const [upgradePrompt, setUpgradePrompt] = useState({
    open: false,
    feature: '',
    module: ''
  });

  const themeColor = availableColors[currentColor];

  const handleModuleClick = (module: ModuleItem, event?: React.MouseEvent) => {
    // Prevent event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    console.log('Module clicked:', module.name, 'Has subModules:', module.subModules?.length);
    console.log('Module details:', {
      name: module.name,
      hasSubModules: !!module.subModules,
      subModulesCount: module.subModules?.length,
      premiumOnly: module.premiumOnly,
      feature: module.feature,
      betaAccess: module.betaAccess
    });

    // For modules with sub-modules, always allow expansion regardless of subscription
    if (module.subModules && module.subModules.length > 0) {
      console.log('Toggling expansion for:', module.name);
      // Toggle expansion - simplified logic for debugging
      setExpandedModules(prev => {
        const isCurrentlyExpanded = prev.includes(module.name);
        const newExpanded = isCurrentlyExpanded
          ? prev.filter(name => name !== module.name)
          : [...prev, module.name];
        console.log('Expansion state changed:', {
          module: module.name,
          wasExpanded: isCurrentlyExpanded,
          nowExpanded: !isCurrentlyExpanded,
          allExpanded: newExpanded
        });
        return newExpanded;
      });
      return; // Don't navigate, just expand/collapse
    }

    // Check subscription access only for navigation
    if (module.premiumOnly && subscription?.plan?.name === 'beta') {
      console.log('Premium module blocked for beta user');
      // Show upgrade prompt for premium-only modules
      setUpgradePrompt({
        open: true,
        feature: module.name,
        module: module.name
      });
      return;
    }

    if (module.feature && !checkFeatureAccess(module.feature)) {
      console.log('Feature access denied:', module.feature);
      // Feature not accessible in current plan
      return;
    }

    // Navigate to module
    console.log('Navigating to:', module.path);
    navigate(module.path);
    if (isMobile) {
      onToggle();
    }
  };

  const handleSubModuleClick = (subModule: SubModule, parentModule?: ModuleItem) => {
    console.log('Sub-module clicked:', subModule.name, 'Path:', subModule.path);

    // Check if sub-module has premium restrictions
    if (parentModule?.premiumOnly && subscription?.plan?.name === 'beta') {
      console.log('Premium sub-module blocked for beta user');
      return;
    }

    navigate(subModule.path);
    if (isMobile) {
      onToggle();
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Check if we're in beta mode
  const isBetaMode = location.pathname.startsWith('/beta-dashboard');

  // Define all modules
  const allModuleItems: ModuleItem[] = [
    {
      name: translate('nav.dashboard'),
      path: isBetaMode ? '/beta-dashboard' : ROUTES.DASHBOARD,
      icon: <DashboardIcon />,
      betaAccess: true
    },
    {
      name: translate('nav.animals'),
      path: isBetaMode ? '/beta-dashboard/animals' : ROUTES.ANIMALS,
      icon: <AnimalsIcon />,
      betaAccess: true,
      feature: 'animal_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/animals' : `${ROUTES.ANIMALS}` },
        { name: 'Animal Tracking', path: isBetaMode ? '/beta-dashboard/animals/tracking' : `${ROUTES.ANIMALS}/tracking` },
        { name: 'Animal Profiles', path: isBetaMode ? '/beta-dashboard/animals/profiles' : `${ROUTES.ANIMALS}/profiles` },
        { name: 'Animal Records', path: isBetaMode ? '/beta-dashboard/animals/records' : `${ROUTES.ANIMALS}/records` },
        { name: 'Growth Tracking', path: isBetaMode ? '/beta-dashboard/animals/growth' : `${ROUTES.ANIMALS}/growth` },
        { name: 'RFID Tracking', path: isBetaMode ? '/beta-dashboard/animals/rfid' : `${ROUTES.ANIMALS}/rfid` },
        { name: 'Asset Management', path: isBetaMode ? '/beta-dashboard/animals/asset-management' : `${ROUTES.ANIMALS}/asset-management` },
        { name: 'Genealogy', path: isBetaMode ? '/beta-dashboard/animals/genealogy' : `${ROUTES.ANIMALS}/genealogy` },
        { name: 'Health Prediction', path: isBetaMode ? '/beta-dashboard/animals/health-prediction' : `${ROUTES.ANIMALS}/health-prediction` },
        { name: 'Retirement Tracking', path: isBetaMode ? '/beta-dashboard/animals/retirement' : `${ROUTES.ANIMALS}/retirement` }
      ]
    },
    {
      name: translate('nav.health'),
      path: isBetaMode ? '/beta-dashboard/health' : ROUTES.HEALTH,
      icon: <HealthIcon />,
      betaAccess: true,
      feature: 'health_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/health' : `${ROUTES.HEALTH}` },
        { name: 'Health Records', path: isBetaMode ? '/beta-dashboard/health/records' : `${ROUTES.HEALTH}/records` },
        { name: 'Treatments', path: isBetaMode ? '/beta-dashboard/health/treatments' : `${ROUTES.HEALTH}/treatments` },
        { name: 'Vaccinations', path: isBetaMode ? '/beta-dashboard/health/vaccinations' : `${ROUTES.HEALTH}/vaccinations` },
        { name: 'Appointments', path: isBetaMode ? '/beta-dashboard/health/appointments' : `${ROUTES.HEALTH}/appointments` },
        { name: 'Diseases', path: isBetaMode ? '/beta-dashboard/health/diseases' : `${ROUTES.HEALTH}/diseases` }
      ]
    },
    {
      name: translate('nav.resources'),
      path: isBetaMode ? '/beta-dashboard/resources' : ROUTES.RESOURCES,
      icon: <ResourcesIcon />,
      betaAccess: true,
      feature: 'resources',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/resources' : `${ROUTES.RESOURCES}` },
        { name: 'Documentation', path: isBetaMode ? '/beta-dashboard/resources/documentation' : `${ROUTES.RESOURCES}/documentation` },
        { name: 'Training', path: isBetaMode ? '/beta-dashboard/resources/training' : `${ROUTES.RESOURCES}/training` },
        { name: 'Support', path: isBetaMode ? '/beta-dashboard/resources/support' : `${ROUTES.RESOURCES}/support` },
        { name: 'Guidelines', path: isBetaMode ? '/beta-dashboard/resources/guidelines' : `${ROUTES.RESOURCES}/guidelines` },
        { name: 'Downloads', path: isBetaMode ? '/beta-dashboard/resources/downloads' : `${ROUTES.RESOURCES}/downloads` },
        { name: 'Government Resources', path: isBetaMode ? '/beta-dashboard/resources/government' : `${ROUTES.RESOURCES}/government` },
        { name: 'Maintenance', path: isBetaMode ? '/beta-dashboard/resources/maintenance' : `${ROUTES.RESOURCES}/maintenance` },
        { name: 'Utilization', path: isBetaMode ? '/beta-dashboard/resources/utilization' : `${ROUTES.RESOURCES}/utilization` }
      ]
    },
    {
      name: translate('nav.settings'),
      path: isBetaMode ? '/beta-dashboard/settings' : ROUTES.SETTINGS,
      icon: <SettingsIcon />,
      betaAccess: true,
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/settings' : `${ROUTES.SETTINGS}` },
        { name: 'Theme Settings', path: isBetaMode ? '/beta-dashboard/settings/theme' : `${ROUTES.SETTINGS}/theme` },
        { name: 'User Management', path: isBetaMode ? '/beta-dashboard/settings/users' : `${ROUTES.SETTINGS}/users` },
        { name: 'Database Settings', path: isBetaMode ? '/beta-dashboard/settings/database' : `${ROUTES.SETTINGS}/database` },
        { name: 'Backup Settings', path: isBetaMode ? '/beta-dashboard/settings/backup' : `${ROUTES.SETTINGS}/backup` }
      ]
    },
    // Premium modules (shown to all users with access control)
    {
      name: translate('nav.breeding'),
      path: isBetaMode ? '/beta-dashboard/breeding' : ROUTES.BREEDING,
      icon: <BreedingIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'breeding_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/breeding' : `${ROUTES.BREEDING}` },
        { name: 'Breeding Records', path: isBetaMode ? '/beta-dashboard/breeding/records' : `${ROUTES.BREEDING}/records` },
        { name: 'Breeding Schedule', path: isBetaMode ? '/beta-dashboard/breeding/schedule' : `${ROUTES.BREEDING}/schedule` },
        { name: 'Pregnancy Tracking', path: isBetaMode ? '/beta-dashboard/breeding/pregnancy' : `${ROUTES.BREEDING}/pregnancy` },
        { name: 'Birth Records', path: isBetaMode ? '/beta-dashboard/breeding/births' : `${ROUTES.BREEDING}/births` },
        { name: 'Birth Predictions', path: isBetaMode ? '/beta-dashboard/breeding/predictions' : `${ROUTES.BREEDING}/predictions` },
        { name: 'Heat Calendar', path: isBetaMode ? '/beta-dashboard/breeding/calendar' : `${ROUTES.BREEDING}/calendar` }
      ]
    },
    {
      name: translate('nav.feeding'),
      path: isBetaMode ? '/beta-dashboard/feeding' : ROUTES.FEED,
      icon: <FeedingIcon />,
      betaAccess: true,
      feature: 'feeding_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/feeding' : `${ROUTES.FEED}` },
        { name: 'Feeding Records', path: isBetaMode ? '/beta-dashboard/feeding/records' : `${ROUTES.FEED}/records` },
        { name: 'Feeding Schedules', path: isBetaMode ? '/beta-dashboard/feeding/schedules' : `${ROUTES.FEED}/schedules` },
        { name: 'Feeding Plans', path: isBetaMode ? '/beta-dashboard/feeding/plans' : `${ROUTES.FEED}/plans` },
        { name: 'Nutrition', path: isBetaMode ? '/beta-dashboard/feeding/nutrition' : `${ROUTES.FEED}/nutrition` },
        { name: 'Inventory', path: isBetaMode ? '/beta-dashboard/feeding/inventory' : `${ROUTES.FEED}/inventory` },
        { name: 'Suppliers', path: isBetaMode ? '/beta-dashboard/feeding/suppliers' : `${ROUTES.FEED}/suppliers` }
      ]
    },
    {
      name: translate('nav.financial'),
      path: isBetaMode ? '/beta-dashboard/financial' : ROUTES.FINANCIAL,
      icon: <FinancialIcon />,
      betaAccess: true, // Now available in BETA with basic features
      feature: 'financial_basic',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/financial' : `${ROUTES.FINANCIAL}` },
        { name: 'Overview', path: isBetaMode ? '/beta-dashboard/financial/overview' : `${ROUTES.FINANCIAL}/overview` },
        { name: 'Transactions', path: isBetaMode ? '/beta-dashboard/financial/transactions' : `${ROUTES.FINANCIAL}/transactions` },
        { name: 'Reports', path: isBetaMode ? '/beta-dashboard/financial/reports' : `${ROUTES.FINANCIAL}/reports` },
        { name: 'Budgets', path: isBetaMode ? '/beta-dashboard/financial/budgets' : `${ROUTES.FINANCIAL}/budgets`, premiumOnly: true },
        { name: 'Forecasting', path: isBetaMode ? '/beta-dashboard/financial/forecast' : `${ROUTES.FINANCIAL}/forecast`, premiumOnly: true },
        { name: 'ROI Analysis', path: isBetaMode ? '/beta-dashboard/financial/roi' : `${ROUTES.FINANCIAL}/roi`, premiumOnly: true },
        { name: 'Invoices', path: isBetaMode ? '/beta-dashboard/financial/invoices' : `${ROUTES.FINANCIAL}/invoices`, premiumOnly: true }
      ]
    },
    {
      name: translate('nav.inventory'),
      path: isBetaMode ? '/beta-dashboard/inventory' : ROUTES.INVENTORY,
      icon: <InventoryIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'inventory_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/inventory' : `${ROUTES.INVENTORY}` }
      ]
    },
    {
      name: translate('nav.commercial'),
      path: isBetaMode ? '/beta-dashboard/commercial' : ROUTES.COMMERCIAL,
      icon: <CommercialIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'commercial_operations',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/commercial' : `${ROUTES.COMMERCIAL}` },
        { name: 'Marketplace', path: isBetaMode ? '/beta-dashboard/commercial/marketplace' : `${ROUTES.COMMERCIAL}/marketplace` },
        { name: 'Auctions', path: isBetaMode ? '/beta-dashboard/commercial/auctions' : `${ROUTES.COMMERCIAL}/auctions` },
        { name: 'BKB Auctions', path: isBetaMode ? '/beta-dashboard/commercial/bkb-auctions' : `${ROUTES.COMMERCIAL}/bkb-auctions` },
        { name: 'Orders', path: isBetaMode ? '/beta-dashboard/commercial/orders' : `${ROUTES.COMMERCIAL}/orders` },
        { name: 'Suppliers', path: isBetaMode ? '/beta-dashboard/commercial/suppliers' : `${ROUTES.COMMERCIAL}/suppliers` },
        { name: 'Pricing', path: isBetaMode ? '/beta-dashboard/commercial/pricing' : `${ROUTES.COMMERCIAL}/pricing` },
        { name: 'Price Updates', path: `${ROUTES.COMMERCIAL}/price-updates` }
      ]
    },
    {
      name: translate('nav.reports'),
      path: isBetaMode ? '/beta-dashboard/reports' : ROUTES.REPORTS,
      icon: <ReportsIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'basic_reports',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/reports' : `${ROUTES.REPORTS}` },
        { name: 'Analysis', path: isBetaMode ? '/beta-dashboard/reports/analysis' : `${ROUTES.REPORTS}/analysis` },
        { name: 'Performance', path: isBetaMode ? '/beta-dashboard/reports/performance' : `${ROUTES.REPORTS}/performance` },
        { name: 'Health Reports', path: isBetaMode ? '/beta-dashboard/reports/health' : `${ROUTES.REPORTS}/health` },
        { name: 'Market Reports', path: isBetaMode ? '/beta-dashboard/reports/market' : `${ROUTES.REPORTS}/market` },
        { name: 'Financial Reports', path: isBetaMode ? '/beta-dashboard/reports/financial' : `${ROUTES.REPORTS}/financial` },
        { name: 'Feeding Reports', path: isBetaMode ? '/beta-dashboard/reports/feeding' : `${ROUTES.REPORTS}/feeding` },
        { name: 'Breeding Reports', path: isBetaMode ? '/beta-dashboard/reports/breeding' : `${ROUTES.REPORTS}/breeding` },
        { name: 'Predictive Analysis', path: isBetaMode ? '/beta-dashboard/reports/predictive' : `${ROUTES.REPORTS}/predictive`, premiumOnly: true },
        { name: 'Custom Reports', path: isBetaMode ? '/beta-dashboard/reports/custom' : `${ROUTES.REPORTS}/custom`, premiumOnly: true }
      ]
    },
    {
      name: translate('nav.analytics'),
      path: isBetaMode ? '/beta-dashboard/analytics' : ROUTES.ANALYTICS,
      icon: <AnalyticsIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'advanced_analytics',
      subModules: [
        { name: 'Business Analysis', path: isBetaMode ? '/beta-dashboard/analytics' : `${ROUTES.ANALYTICS}` },
        { name: 'Business Strategy', path: isBetaMode ? '/beta-dashboard/analytics/strategy' : `${ROUTES.ANALYTICS}/strategy` },
        { name: 'Business Predictions', path: isBetaMode ? '/beta-dashboard/analytics/predictions' : `${ROUTES.ANALYTICS}/predictions` }
      ]
    },
    {
      name: translate('nav.compliance'),
      path: isBetaMode ? '/beta-dashboard/compliance' : ROUTES.COMPLIANCE,
      icon: <ComplianceIcon />,
      betaAccess: true,
      premiumOnly: true,
      feature: 'compliance_management',
      subModules: [
        { name: 'Dashboard', path: isBetaMode ? '/beta-dashboard/compliance' : `${ROUTES.COMPLIANCE}` },
        { name: 'Certifications', path: isBetaMode ? '/beta-dashboard/compliance/certifications' : `${ROUTES.COMPLIANCE}/certifications` },
        { name: 'Inspections', path: isBetaMode ? '/beta-dashboard/compliance/inspections' : `${ROUTES.COMPLIANCE}/inspections` },
        { name: 'Documents', path: isBetaMode ? '/beta-dashboard/compliance/documents' : `${ROUTES.COMPLIANCE}/documents` }
      ]
    }
  ];

  // Filter modules based on user access using unified access control
  const moduleItems = allModuleItems.filter(module => {
    // If user is not loaded yet, show all modules to prevent empty sidebar
    if (!user) return true;

    // Extract module ID from path - handle both beta and regular paths
    let moduleId = module.path.split('/').pop() || 'dashboard';

    // Handle beta dashboard paths
    if (module.path.includes('/beta-dashboard')) {
      const pathParts = module.path.split('/');
      moduleId = pathParts[pathParts.length - 1] || 'dashboard';
      if (moduleId === 'beta-dashboard') moduleId = 'dashboard';
    }

    // Handle regular dashboard paths
    if (module.path.includes('/dashboard') && !module.path.includes('/beta-dashboard')) {
      const pathParts = module.path.split('/dashboard/');
      moduleId = pathParts.length > 1 ? pathParts[1] : 'dashboard';
    }

    console.log('Checking access for module:', moduleId, 'Path:', module.path, 'User:', user?.username);

    // Use unified access control to check if user can access this module
    const hasAccess = canAccessModule(user, moduleId);
    console.log('Access result:', hasAccess);

    return hasAccess;
  });

  // Auto-expand current module
  useEffect(() => {
    const currentPath = location.pathname;
    const currentModule = moduleItems.find(module =>
      currentPath.startsWith(module.path) ||
      module.subModules?.some(sub => currentPath.startsWith(sub.path))
    );

    if (currentModule && !expandedModules.includes(currentModule.name)) {
      setExpandedModules(prev => [...prev, currentModule.name]);
    }
  }, [location.pathname, moduleItems, expandedModules]);

  const drawerWidth = isOpen ? 280 : 64;

  const listItemVariants = {
    open: { opacity: 1, x: 0, transition: { duration: 0.2 } },
    closed: { opacity: 0, x: -20, transition: { duration: 0.2 } }
  };

  return (
    <>
    <Drawer
      variant={isMobile ? "temporary" : "permanent"}
      open={isMobile ? isOpen : true}
      onClose={isMobile ? onToggle : undefined}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: `linear-gradient(180deg, ${themeColor.primary}, ${themeColor.secondary})`,
          borderRight: 'none',
          transition: 'width 0.3s ease-in-out',
          overflowX: 'hidden',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: alpha(theme.palette.common.white, 0.1),
          },
          '&::-webkit-scrollbar-thumb': {
            background: alpha(theme.palette.common.white, 0.3),
            borderRadius: '3px',
            '&:hover': {
              background: alpha(theme.palette.common.white, 0.5),
            },
          },
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: isOpen ? 'space-between' : 'center',
          p: 2,
          minHeight: 64,
          borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}`,
        }}
      >
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box>
              <AgriIntelLogo
                variant="compact"
                size="small"
                theme="white"
                showTagline={false}
                orientation="vertical"
              />
              {subscription?.plan?.name === 'beta' && (
                <Typography
                  variant="caption"
                  sx={{
                    color: alpha(theme.palette.common.white, 0.8),
                    fontSize: '0.7rem',
                    display: 'block',
                    textAlign: 'center',
                    mt: 0.5
                  }}
                >
                  Beta Version
                </Typography>
              )}
            </Box>
          </motion.div>
        )}

        {!isMobile && (
          <IconButton
            onClick={onToggle}
            sx={{
              color: 'white',
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              },
            }}
          >
            {isOpen ? <ChevronLeft /> : <ChevronRight />}
          </IconButton>
        )}
      </Box>

      {/* Navigation List */}
      <List sx={{ pt: 1, pb: 1 }}>
        {moduleItems.map((module, index) => {
          const isLocked = module.premiumOnly && subscription?.plan?.name === 'beta';

          return (
            <React.Fragment key={module.name}>
              <ListItem disablePadding>
                {isLocked ? (
                  <BetaFeatureGate
                    feature={module.feature || 'premium_module'}
                    showPreview={false}
                    upgradeMessage={`Upgrade to access ${module.name}`}
                  >
                    <Tooltip
                      title={!isOpen ? `${module.name} (Premium)` : ''}
                      placement="right"
                      arrow
                    >
                      <ListItemButton
                        disabled
                        sx={{
                          minHeight: 48,
                          px: 2.5,
                          py: 1,
                          mx: 1,
                          mb: 0.5,
                          borderRadius: 2,
                          color: alpha(theme.palette.common.white, 0.5),
                          backgroundColor: 'transparent',
                          opacity: 0.6,
                          cursor: 'not-allowed',
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.common.white, 0.05),
                          },
                          transition: 'all 0.2s ease-in-out',
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 0,
                            mr: isOpen ? 2 : 'auto',
                            justifyContent: 'center',
                            color: alpha(theme.palette.common.white, 0.5),
                          }}
                        >
                          {module.icon}
                        </ListItemIcon>

                        {isOpen && (
                          <motion.div
                            variants={listItemVariants}
                            initial="closed"
                            animate="open"
                            style={{ display: 'flex', alignItems: 'center', width: '100%' }}
                          >
                            <ListItemText
                              primary={module.name}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.9rem',
                                  fontWeight: 500,
                                },
                              }}
                            />
                            <Lock sx={{ fontSize: 16, ml: 1 }} />
                          </motion.div>
                        )}
                      </ListItemButton>
                    </Tooltip>
                  </BetaFeatureGate>
                ) : (
                  <Tooltip
                    title={!isOpen ? module.name : ''}
                    placement="right"
                    arrow
                  >
                    <ListItemButton
                      onClick={(event) => handleModuleClick(module, event)}
                      sx={{
                        minHeight: 48,
                        px: 2.5,
                        py: 1,
                        mx: 1,
                        mb: 0.5,
                        borderRadius: 2,
                        color: 'white',
                        backgroundColor: isActive(module.path)
                          ? alpha(theme.palette.common.white, 0.2)
                          : 'transparent',
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.common.white, 0.15),
                          transform: 'translateX(4px)',
                        },
                        transition: 'all 0.2s ease-in-out',
                      }}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 0,
                          mr: isOpen ? 2 : 'auto',
                          justifyContent: 'center',
                          color: 'white',
                        }}
                      >
                        {module.icon}
                      </ListItemIcon>

                      {isOpen && (
                        <motion.div
                          variants={listItemVariants}
                          initial="closed"
                          animate="open"
                          style={{ display: 'flex', alignItems: 'center', width: '100%' }}
                        >
                          <ListItemText
                            primary={module.name}
                            sx={{
                              '& .MuiListItemText-primary': {
                                fontSize: '0.9rem',
                                fontWeight: 500,
                              },
                            }}
                          />
                          {module.subModules && module.subModules.length > 0 && (
                            expandedModules.includes(module.name) ? <ExpandLess /> : <ExpandMore />
                          )}
                        </motion.div>
                      )}
                    </ListItemButton>
                  </Tooltip>
                )}
              </ListItem>

            {/* Sub-modules - Fixed expansion issue */}
            {module.subModules && module.subModules.length > 0 && isOpen && (
              <AnimatePresence>
                {expandedModules.includes(module.name) && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    style={{ overflow: 'hidden' }}
                  >
                    <List component="div" disablePadding>
                      {module.subModules.map((subModule) => (
                        <ListItem key={subModule.name} disablePadding>
                          <ListItemButton
                            onClick={() => handleSubModuleClick(subModule, module)}
                            sx={{
                              pl: 6,
                              pr: 2.5,
                              py: 0.8,
                              mx: 1,
                              mb: 0.3,
                              borderRadius: 1.5,
                              color: alpha(theme.palette.common.white, 0.9),
                              backgroundColor: isActive(subModule.path)
                                ? alpha(theme.palette.common.white, 0.15)
                                : 'transparent',
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.common.white, 0.1),
                                transform: 'translateX(4px)',
                              },
                              transition: 'all 0.2s ease-in-out',
                            }}
                          >
                            <ListItemText
                              primary={subModule.name}
                              sx={{
                                '& .MuiListItemText-primary': {
                                  fontSize: '0.8rem',
                                  fontWeight: isActive(subModule.path) ? 600 : 400,
                                },
                              }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                    </List>
                  </motion.div>
                )}
              </AnimatePresence>
            )}

              {/* Add divider after certain modules for better organization */}
              {(index === 0 || index === 4 || index === 7 || index === 10) && (
                <Divider
                  sx={{
                    my: 1,
                    mx: 2,
                    borderColor: alpha(theme.palette.common.white, 0.1)
                  }}
                />
              )}
            </React.Fragment>
          );
        })}
      </List>

      {/* Beta Upgrade Button */}
      {subscription?.plan?.name === 'beta' && isOpen && (
        <Box sx={{ p: 2, mt: 'auto' }}>
          <BetaUpgradeButton variant="sidebar" size="medium" />
        </Box>
      )}
    </Drawer>

    {/* Upgrade Prompt Dialog */}
    <UpgradePrompt
      open={upgradePrompt.open}
      onClose={() => setUpgradePrompt({ open: false, feature: '', module: '' })}
      feature={upgradePrompt.feature}
      module={upgradePrompt.module}
    />
  </>
  );
};

export default Sidebar;