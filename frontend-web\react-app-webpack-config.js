// This file is used to customize webpack configuration for react-scripts
const path = require('path');
const webpack = require('webpack');

module.exports = {
  webpack: {
    configure: (webpackConfig) => {
      // Add fallbacks for node modules
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        // Use actual polyfill packages for Node.js modules
        "crypto": require.resolve('crypto-browserify'),
        "stream": require.resolve('stream-browserify'),
        "timers": require.resolve('timers-browserify'),
        "os": require.resolve('os-browserify/browser'),
        "zlib": require.resolve('browserify-zlib'),
        "http": require.resolve('stream-http'),
        "https": require.resolve('https-browserify'),
        "querystring": require.resolve('querystring-es3'),
        "buffer": require.resolve('buffer/'),
        "process": require.resolve('process/browser'),
        
        // Use our mock implementations for modules without browser versions
        "fs": path.resolve(__dirname, 'src/polyfills.js'),
        "net": path.resolve(__dirname, 'src/polyfills.js'),
        "tls": path.resolve(__dirname, 'src/polyfills.js'),
        "dns": path.resolve(__dirname, 'src/polyfills.js'),
        "child_process": path.resolve(__dirname, 'src/polyfills.js'),
        "fs/promises": path.resolve(__dirname, 'src/polyfills.js'),
        "timers/promises": path.resolve(__dirname, 'src/polyfills.js'),
        "path": false
      };

      // Add plugins for polyfills
      webpackConfig.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer']
        })
      );
      
      return webpackConfig;
    }
  }
};
