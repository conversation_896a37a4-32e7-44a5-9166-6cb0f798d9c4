/**
 * A simple in-memory cache utility for database queries
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

interface CacheOptions {
  /** Time to live in milliseconds */
  ttl?: number;
  /** Whether to refresh the TTL on access */
  refreshOnAccess?: boolean;
}

class QueryCache {
  private cache: Map<string, CacheItem<any>> = new Map();
  private defaultTTL: number = 5 * 60 * 1000; // 5 minutes default TTL

  /**
   * Get an item from the cache
   * @param key The cache key
   * @param options Cache options
   * @returns The cached data or undefined if not found or expired
   */
  get<T>(key: string, options?: CacheOptions): T | undefined {
    const item = this.cache.get(key);
    
    if (!item) {
      return undefined;
    }
    
    const now = Date.now();
    
    // Check if item is expired
    if (item.expiresAt < now) {
      this.cache.delete(key);
      return undefined;
    }
    
    // Refresh TTL if option is set
    if (options?.refreshOnAccess) {
      const ttl = options.ttl || this.defaultTTL;
      item.expiresAt = now + ttl;
    }
    
    return item.data;
  }

  /**
   * Set an item in the cache
   * @param key The cache key
   * @param data The data to cache
   * @param options Cache options
   */
  set<T>(key: string, data: T, options?: CacheOptions): void {
    const now = Date.now();
    const ttl = options?.ttl || this.defaultTTL;
    
    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt: now + ttl
    });
  }

  /**
   * Remove an item from the cache
   * @param key The cache key
   * @returns True if the item was removed, false if it didn't exist
   */
  remove(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get or set an item in the cache
   * If the item doesn't exist or is expired, the getter function is called
   * @param key The cache key
   * @param getter Function to get the data if not in cache
   * @param options Cache options
   * @returns The cached or newly fetched data
   */
  async getOrSet<T>(key: string, getter: () => Promise<T>, options?: CacheOptions): Promise<T> {
    const cachedData = this.get<T>(key, options);
    
    if (cachedData !== undefined) {
      return cachedData;
    }
    
    const data = await getter();
    this.set(key, data, options);
    return data;
  }

  /**
   * Check if an item exists in the cache and is not expired
   * @param key The cache key
   * @returns True if the item exists and is not expired
   */
  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }
    
    return item.expiresAt >= Date.now();
  }

  /**
   * Get all keys in the cache
   * @param includeExpired Whether to include expired items
   * @returns Array of cache keys
   */
  keys(includeExpired: boolean = false): string[] {
    if (includeExpired) {
      return Array.from(this.cache.keys());
    }
    
    const now = Date.now();
    return Array.from(this.cache.entries())
      .filter(([_, item]) => item.expiresAt >= now)
      .map(([key]) => key);
  }

  /**
   * Get the number of items in the cache
   * @param includeExpired Whether to include expired items
   * @returns Number of items in the cache
   */
  size(includeExpired: boolean = false): number {
    if (includeExpired) {
      return this.cache.size;
    }
    
    return this.keys(false).length;
  }
}

// Export a singleton instance
export const queryCache = new QueryCache();

/**
 * Create a cached version of a function
 * @param fn The function to cache
 * @param keyFn Function to generate a cache key from the arguments
 * @param options Cache options
 * @returns A cached version of the function
 */
export function createCachedFunction<T, Args extends any[]>(
  fn: (...args: Args) => Promise<T>,
  keyFn: (...args: Args) => string,
  options?: CacheOptions
): (...args: Args) => Promise<T> {
  return async (...args: Args): Promise<T> => {
    const key = keyFn(...args);
    return queryCache.getOrSet(key, () => fn(...args), options);
  };
}

export default queryCache;
