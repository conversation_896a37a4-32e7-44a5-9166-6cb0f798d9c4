import React, { useState, useEffect } from 'react';
import { Box, Paper, Typography, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';

interface SponsorCarouselProps {
  interval?: number;
}

/**
 * Component to display rotating sponsor images
 * @param interval - The interval in milliseconds to rotate images
 */
const SponsorCarousel: React.FC<SponsorCarouselProps> = ({ interval = 10000 }) => {
  const theme = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  
  // Sponsor data
  const sponsors = [
    {
      name: 'Land Bank',
      image: '/images/sponsors/land bank.png',
      url: 'https://www.landbank.co.za/'
    },
    {
      name: 'Agricultural Research Council',
      image: '/images/sponsors/Agricultural research council.png',
      url: 'https://www.arc.agric.za/'
    },
    {
      name: 'Department of Agriculture, Land Reform & Rural Development',
      image: '/images/sponsors/Agriculture , land reform & rural development.png',
      url: 'https://www.dalrrd.gov.za/'
    },
    {
      name: 'National Agricultural Marketing Council',
      image: '/images/sponsors/National Agricultural Marketing Council.png',
      url: 'https://www.namc.co.za/'
    },
    {
      name: 'Perishable Products Export Control Board',
      image: '/images/sponsors/Perishable Productst Control Board.png',
      url: 'https://ppecb.com/'
    }
  ];

  // Rotate sponsors
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsors.length);
    }, interval);
    
    return () => clearInterval(timer);
  }, [interval, sponsors.length]);

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        backgroundColor: alpha(theme.palette.background.paper, 0.7),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
      }}
    >
      <Typography
        variant="h6"
        sx={{
          mb: 2,
          fontWeight: 600,
          color: theme.palette.text.primary,
          textAlign: 'center'
        }}
      >
        Agricultural Resources
      </Typography>
      
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          position: 'relative'
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              width: '100%',
              height: '100%',
              padding: '16px'
            }}
          >
            <Box
              component="a"
              href={sponsors[currentIndex].url}
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textDecoration: 'none',
                color: 'inherit',
                transition: 'transform 0.3s ease',
                '&:hover': {
                  transform: 'scale(1.05)'
                }
              }}
            >
              <Box
                component="img"
                src={sponsors[currentIndex].image}
                alt={sponsors[currentIndex].name}
                sx={{
                  maxWidth: '100%',
                  maxHeight: 120,
                  objectFit: 'contain',
                  mb: 2,
                  borderRadius: 1,
                  backgroundColor: 'transparent'
                }}
              />
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 500,
                  textAlign: 'center',
                  color: theme.palette.text.primary
                }}
              >
                {sponsors[currentIndex].name}
              </Typography>
            </Box>
          </motion.div>
        </AnimatePresence>
      </Box>
      
      {/* Dots indicator */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 2
        }}
      >
        {sponsors.map((_, index) => (
          <Box
            key={index}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              mx: 0.5,
              backgroundColor: index === currentIndex
                ? theme.palette.primary.main
                : alpha(theme.palette.primary.main, 0.3),
              transition: 'background-color 0.3s ease'
            }}
          />
        ))}
      </Box>
    </Paper>
  );
};

export default SponsorCarousel;
