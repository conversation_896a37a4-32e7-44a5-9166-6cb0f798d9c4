import React, { useState } from 'react';
import { Box, FormControl, InputLabel, MenuItem, Select, Chip, Typography } from '@mui/material';
import { Add } from '@mui/icons-material';
import { Mo<PERSON>leHeader, ModuleContainer, StyledTable } from '../../components/common';
import { useThemeContext } from '../../contexts/ThemeContext';

interface AnimalRecord {
  id: string;
  animalId: string;
  animalName: string;
  recordType: 'Health' | 'Weight' | 'Vaccination' | 'Treatment' | 'Feed';
  date: string;
  description: string;
  performedBy: string;
  status: 'Completed' | 'Pending' | 'Scheduled';
}

const mockRecords: AnimalRecord[] = [
  {
    id: 'REC001',
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    recordType: 'Vaccination',
    date: '2024-02-15',
    description: 'Vaccination against Brucellosis',
    performedBy: '<PERSON><PERSON> <PERSON>',
    status: 'Completed'
  },
  {
    id: 'REC002',
    animalId: 'ANM002',
    animalName: 'Kgosi',
    recordType: 'Weight',
    date: '2024-02-14',
    description: 'Monthly weight check',
    performedBy: 'Sipho Ndlovu',
    status: 'Completed'
  },
  {
    id: 'REC003',
    animalId: 'ANM003',
    animalName: 'Lesedi',
    recordType: 'Health',
    date: '2024-02-20',
    description: 'Pregnancy check - confirmed',
    performedBy: 'Dr. Botha',
    status: 'Completed'
  },
  {
    id: 'REC004',
    animalId: 'ANM004',
    animalName: 'Pule',
    recordType: 'Treatment',
    date: '2024-02-25',
    description: 'Dipping for tick control',
    performedBy: 'Thabo Molefe',
    status: 'Completed'
  },
  {
    id: 'REC005',
    animalId: 'ANM005',
    animalName: 'Lerato',
    recordType: 'Feed',
    date: '2024-03-01',
    description: 'Special supplement for breeding season',
    performedBy: 'Sipho Ndlovu',
    status: 'Completed'
  },
  {
    id: 'REC006',
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    recordType: 'Vaccination',
    date: '2024-03-10',
    description: 'Scheduled vaccination for Anthrax',
    performedBy: 'Dr. van Niekerk',
    status: 'Scheduled'
  }
];

const AnimalRecords: React.FC = () => {
  const [filterType, setFilterType] = useState('all');
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Filter records based on selected type
  const filteredRecords = filterType === 'all'
    ? mockRecords
    : mockRecords.filter(record => record.recordType === filterType);

  // Define columns for the styled table
  const columns = [
    { id: 'date', label: 'Date' },
    { id: 'animalName', label: 'Animal' },
    { id: 'recordType', label: 'Type' },
    { id: 'description', label: 'Description' },
    {
      id: 'status',
      label: 'Status',
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          sx={{
            bgcolor:
              value === 'Completed' ? 'success.light' :
              value === 'Pending' ? 'warning.light' :
              'info.light',
            color:
              value === 'Completed' ? 'success.dark' :
              value === 'Pending' ? 'warning.dark' :
              'info.dark',
            fontWeight: 'medium',
            fontSize: '0.75rem'
          }}
        />
      )
    }
  ];

  return (
    <ModuleContainer module="animals">
      <ModuleHeader
        title="Animal Records"
        subtitle="Track and manage all animal-related records and activities"
        module="animals"
        submodule="records"
        actionLabel="Add New Record"
        actionIcon={<Add />}
        onAction={() => console.log('Add new record')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" fontWeight="bold">Animal Records</Typography>

          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="record-type-label">Filter by Type</InputLabel>
            <Select
              labelId="record-type-label"
              value={filterType}
              label="Filter by Type"
              onChange={(e) => setFilterType(e.target.value)}
              sx={{ bgcolor: 'background.paper' }}
            >
              <MenuItem value="all">All Types</MenuItem>
              <MenuItem value="Health">Health</MenuItem>
              <MenuItem value="Weight">Weight</MenuItem>
              <MenuItem value="Vaccination">Vaccination</MenuItem>
              <MenuItem value="Treatment">Treatment</MenuItem>
              <MenuItem value="Feed">Feed</MenuItem>
            </Select>
          </FormControl>
        </Box>

        <StyledTable
          columns={columns}
          rows={filteredRecords}
          keyField="id"
          onRowClick={(row) => console.log('Clicked row:', row)}
          emptyMessage="No records found"
        />
      </Box>
    </ModuleContainer>
  );
};

export default AnimalRecords;