import { AxiosError } from 'axios';

/**
 * Error types for categorizing different kinds of errors
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  NOT_FOUND = 'NOT_FOUND',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Structured error object with additional context
 */
export interface AppError {
  type: ErrorType;
  message: string;
  originalError?: any;
  statusCode?: number;
  timestamp: string;
  context?: Record<string, any>;
}

/**
 * Create a standardized error object from any error
 * @param error The original error
 * @param context Additional context for the error
 * @returns A standardized AppError object
 */
export function createAppError(error: any, context?: Record<string, any>): AppError {
  // Default error
  const appError: AppError = {
    type: ErrorType.UNKNOWN,
    message: 'An unknown error occurred',
    originalError: error,
    timestamp: new Date().toISOString(),
    context
  };

  // Handle Axios errors
  if (error?.isAxiosError) {
    const axiosError = error as AxiosError;
    appError.statusCode = axiosError.response?.status;

    // Categorize based on status code
    if (axiosError.code === 'ECONNABORTED') {
      appError.type = ErrorType.TIMEOUT;
      appError.message = 'Request timed out. Please try again.';
    } else if (!axiosError.response) {
      appError.type = ErrorType.NETWORK;
      appError.message = 'Network error. Please check your connection.';
    } else {
      switch (axiosError.response.status) {
        case 401:
          appError.type = ErrorType.AUTHENTICATION;
          appError.message = 'Authentication failed. Please log in again.';
          break;
        case 403:
          appError.type = ErrorType.AUTHORIZATION;
          appError.message = 'You do not have permission to perform this action.';
          break;
        case 404:
          appError.type = ErrorType.NOT_FOUND;
          appError.message = 'The requested resource was not found.';
          break;
        case 422:
          appError.type = ErrorType.VALIDATION;
          appError.message = 'Validation failed. Please check your input.';
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          appError.type = ErrorType.SERVER;
          appError.message = 'Server error. Please try again later.';
          break;
        default:
          appError.message = axiosError.response?.data && 'message' in (axiosError.response.data as any)
            ? (axiosError.response.data as any).message
            : 'An error occurred';
      }
    }
  } else if (error instanceof Error) {
    appError.message = error.message;
  } else if (typeof error === 'string') {
    appError.message = error;
  }

  // Log the error
  logError(appError);

  return appError;
}

/**
 * Log an error to the console and potentially to a monitoring service
 * @param error The error to log
 */
export function logError(error: AppError): void {
  // Log to console
  console.error(`[${error.type}] ${error.message}`, {
    statusCode: error.statusCode,
    timestamp: error.timestamp,
    context: error.context,
    originalError: error.originalError
  });

  // TODO: Add integration with error monitoring service like Sentry
  // if (process.env.NODE_ENV === 'production') {
  //   // Send to monitoring service
  // }
}

/**
 * Get a user-friendly error message based on the error type
 * @param error The AppError object
 * @returns A user-friendly error message
 */
export function getUserFriendlyErrorMessage(error: AppError): string {
  switch (error.type) {
    case ErrorType.NETWORK:
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    case ErrorType.SERVER:
      return 'The server encountered an error. Our team has been notified and is working to fix the issue.';
    case ErrorType.AUTHENTICATION:
      return 'Your session has expired. Please log in again to continue.';
    case ErrorType.AUTHORIZATION:
      return 'You don\'t have permission to access this resource.';
    case ErrorType.VALIDATION:
      return 'Please check the information you provided and try again.';
    case ErrorType.NOT_FOUND:
      return 'The requested information could not be found.';
    case ErrorType.TIMEOUT:
      return 'The request took too long to complete. Please try again.';
    default:
      return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
  }
}

/**
 * Try to extract validation errors from the error response
 * @param error The AppError object
 * @returns An object mapping field names to error messages, or null if no validation errors are found
 */
export function extractValidationErrors(error: AppError): Record<string, string> | null {
  if (error.type !== ErrorType.VALIDATION) {
    return null;
  }

  try {
    const axiosError = error.originalError as AxiosError;
    const data = axiosError.response?.data as any;

    if (data?.errors && typeof data.errors === 'object') {
      return data.errors;
    }

    return null;
  } catch (e) {
    return null;
  }
}

export default {
  ErrorType,
  createAppError,
  logError,
  getUserFriendlyErrorMessage,
  extractValidationErrors
};
