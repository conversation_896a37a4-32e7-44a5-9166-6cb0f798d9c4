/**
 * Enhanced ResizeObserver patch
 * 
 * This patch provides additional fixes for ResizeObserver loop errors
 * that can occur in React applications with complex layouts and animations.
 */

// Apply additional fixes for ResizeObserver loop errors
export const applyEnhancedResizeObserverFix = (): void => {
  // Patch the ResizeObserver prototype to add error handling
  if (typeof ResizeObserver !== 'undefined') {
    const OriginalResizeObserver = window.ResizeObserver;
    
    // Create a patched version of ResizeObserver
    window.ResizeObserver = class PatchedResizeObserver extends OriginalResizeObserver {
      constructor(callback: ResizeObserverCallback) {
        // Create a wrapped callback that catches errors
        const wrappedCallback: ResizeObserverCallback = (entries, observer) => {
          // Use requestAnimationFrame to break the loop
          window.requestAnimationFrame(() => {
            try {
              callback(entries, observer);
            } catch (error: any) {
              if (error && 
                  typeof error === 'object' && 
                  error.message && 
                  typeof error.message === 'string' && 
                  error.message.includes('ResizeObserver loop')) {
                console.warn('ResizeObserver loop error caught and suppressed in callback');
              } else {
                throw error;
              }
            }
          });
        };
        
        // Call the original constructor with the wrapped callback
        super(wrappedCallback);
      }
    } as any;
    
    // Copy static properties
    window.ResizeObserver.prototype = OriginalResizeObserver.prototype;
    
    console.log('Enhanced ResizeObserver patch applied');
  }
  
  // Add a global error handler for ResizeObserver loop errors
  const originalErrorHandler = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (message && 
        typeof message === 'string' && 
        message.includes('ResizeObserver loop')) {
      console.warn('ResizeObserver loop error caught in global error handler');
      return true; // Prevent the error from propagating
    }
    
    // Call the original error handler for other errors
    if (originalErrorHandler) {
      return originalErrorHandler.call(this, message, source, lineno, colno, error);
    }
    
    return false;
  };
  
  // Add CSS to help prevent ResizeObserver loops
  const style = document.createElement('style');
  style.textContent = `
    /* Apply contain: layout to elements that might cause ResizeObserver loops */
    .MuiBox-root,
    .MuiPaper-root,
    .MuiCard-root,
    .MuiGrid-root,
    .MuiContainer-root,
    .MuiDrawer-root,
    .MuiAppBar-root,
    .MuiToolbar-root,
    .MuiList-root,
    .MuiListItem-root,
    .MuiAccordion-root,
    .MuiTabs-root,
    .MuiTab-root,
    .MuiDataGrid-root,
    .MuiDataGrid-virtualScroller,
    .MuiDataGrid-virtualScrollerContent,
    .MuiDataGrid-virtualScrollerRenderZone,
    .recharts-wrapper,
    .recharts-responsive-container {
      contain: layout style paint;
    }
    
    /* Add specific fixes for elements that are known to cause issues */
    .MuiDataGrid-root {
      overflow: hidden !important;
    }
    
    /* Add transition to smooth out resizing */
    .MuiDataGrid-virtualScroller,
    .MuiDataGrid-virtualScrollerContent,
    .MuiDataGrid-virtualScrollerRenderZone {
      transition: height 0.2s ease-in-out, width 0.2s ease-in-out !important;
    }
  `;
  document.head.appendChild(style);
};

// Export the patch
export default applyEnhancedResizeObserverFix;
