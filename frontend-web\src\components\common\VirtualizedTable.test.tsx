import React from 'react';
import { render, screen, fireEvent } from '../../utils/testUtils';
import VirtualizedTable, { Column } from './VirtualizedTable';

// Mock the react-window components
jest.mock('react-window', () => ({
  FixedSizeList: ({ children, itemCount }: { children: any, itemCount: number }) => {
    const items = [];
    for (let i = 0; i < Math.min(itemCount, 10); i++) {
      items.push(children({ index: i, style: {} }));
    }
    return <div data-testid="virtualized-list">{items}</div>;
  },
  ListChildComponentProps: {}
}));

// Mock the AutoSizer component
jest.mock('react-virtualized-auto-sizer', () => ({ children }: { children: any }) => 
  children({ width: 1000, height: 500 })
);

// Mock the InfiniteLoader component
jest.mock('react-window-infinite-loader', () => ({ children, isItemLoaded, itemCount }: { children: any, isItemLoaded: any, itemCount: number }) => 
  children({ onItemsRendered: () => {}, ref: () => {} })
);

interface TestItem {
  id: string;
  name: string;
  age: number;
  status: string;
}

describe('VirtualizedTable Component', () => {
  const mockData: TestItem[] = [
    { id: '1', name: 'John Doe', age: 30, status: 'active' },
    { id: '2', name: 'Jane Smith', age: 25, status: 'inactive' },
    { id: '3', name: 'Bob Johnson', age: 40, status: 'active' },
  ];
  
  const mockColumns: Column<TestItem>[] = [
    { id: 'name', label: 'Name', minWidth: 100 },
    { id: 'age', label: 'Age', minWidth: 50, align: 'right' },
    { 
      id: 'status', 
      label: 'Status', 
      minWidth: 80,
      format: (value) => (
        <span data-testid={`status-${value}`}>
          {value === 'active' ? 'Active' : 'Inactive'}
        </span>
      )
    }
  ];
  
  it('renders with data and columns', () => {
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={mockData}
        uniqueKey="id"
        virtualizeRows={false} // Disable virtualization for testing
      />
    );
    
    // Check if column headers are rendered
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Age')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    
    // Check if data is rendered
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
    expect(screen.getByTestId('status-active')).toBeInTheDocument();
  });
  
  it('handles row selection', () => {
    const handleSelectionChange = jest.fn();
    
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={mockData}
        uniqueKey="id"
        selectable
        onSelectionChange={handleSelectionChange}
        virtualizeRows={false}
      />
    );
    
    // Find and click the first checkbox
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[1]); // First row checkbox (index 0 is the header)
    
    // Check if selection callback was called with the correct data
    expect(handleSelectionChange).toHaveBeenCalledWith([mockData[0]]);
  });
  
  it('handles sorting', () => {
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={mockData}
        uniqueKey="id"
        sortable
        defaultSortBy="age"
        virtualizeRows={false}
      />
    );
    
    // Check initial sort order
    const rows = screen.getAllByRole('row');
    
    // Click on the Name column header to sort by name
    fireEvent.click(screen.getByText('Name'));
    
    // In a real test, we would check if the data is sorted correctly
  });
  
  it('renders loading state', () => {
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={[]}
        loading
        uniqueKey="id"
      />
    );
    
    // Check if loading indicator is shown
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });
  
  it('renders error state', () => {
    const errorMessage = 'Failed to load data';
    
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={[]}
        error={errorMessage}
        uniqueKey="id"
      />
    );
    
    // Check if error message is shown
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });
  
  it('renders empty state', () => {
    const emptyMessage = 'No data available';
    
    render(
      <VirtualizedTable
        columns={mockColumns}
        data={[]}
        emptyMessage={emptyMessage}
        uniqueKey="id"
      />
    );
    
    // Check if empty message is shown
    expect(screen.getByText(emptyMessage)).toBeInTheDocument();
  });
});
