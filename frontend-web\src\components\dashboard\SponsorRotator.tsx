import React, { useState, useEffect, useCallback } from 'react';
import { Box, Paper, Typography, useTheme, alpha, IconButton, Tooltip } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
// Removed useThemeContext import as it was deleted
import { ChevronLeft, ChevronRight, OpenInNew, Pause, PlayArrow } from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';

interface SponsorRotatorProps {
  interval?: number;
}

/**
 * Enhanced component to display rotating sponsor images at the top of the dashboard
 * with manual navigation controls and improved transitions
 * @param interval - The interval in milliseconds to rotate images
 */
const SponsorRotator: React.FC<SponsorRotatorProps> = ({ interval = 10000 }) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [direction, setDirection] = useState<'left' | 'right'>('right');
  const [isHovered, setIsHovered] = useState(false);

  // Sponsor data
  const sponsors = [
    {
      name: 'Land Bank',
      image: '/images/sponsors/land bank.png',
      url: 'https://www.landbank.co.za/',
      description: 'Financial services for agricultural development'
    },
    {
      name: 'Agricultural Research Council',
      image: '/images/sponsors/Agricultural research council.png',
      url: 'https://www.arc.agric.za/',
      description: 'Research and development in agriculture'
    },
    {
      name: 'Department of Agriculture, Land Reform & Rural Development',
      image: '/images/sponsors/Agriculture , land reform & rural development.png',
      url: 'https://www.dalrrd.gov.za/',
      description: 'Government department for agricultural policy'
    },
    {
      name: 'National Agricultural Marketing Council',
      image: '/images/sponsors/National Agricultural Marketing Council.png',
      url: 'https://www.namc.co.za/',
      description: 'Agricultural marketing and trade'
    },
    {
      name: 'Perishable Products Export Control Board',
      image: '/images/sponsors/Perishable Productst Control Board.png',
      url: 'https://ppecb.com/',
      description: 'Quality certification for perishable exports'
    }
  ];

  // Navigate to previous sponsor
  const handlePrevious = useCallback(() => {
    setDirection('left');
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? sponsors.length - 1 : prevIndex - 1));
  }, [sponsors.length]);

  // Navigate to next sponsor
  const handleNext = useCallback(() => {
    setDirection('right');
    setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsors.length);
  }, [sponsors.length]);

  // Toggle pause/play
  const togglePause = useCallback(() => {
    setIsPaused(prev => !prev);
  }, []);

  // Jump to specific sponsor
  const jumpToSponsor = useCallback((index: number) => {
    setDirection(index > currentIndex ? 'right' : 'left');
    setCurrentIndex(index);
  }, [currentIndex]);

  // Rotate sponsors automatically when not paused
  useEffect(() => {
    if (!isPaused) {
      const timer = setInterval(() => {
        setDirection('right');
        setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsors.length);
      }, interval);

      return () => clearInterval(timer);
    }
  }, [interval, sponsors.length, isPaused]);

  // Get animation properties based on direction
  const getAnimationProps = useCallback(() => {
    return {
      initial: { opacity: 0, x: direction === 'right' ? 50 : -50 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: direction === 'right' ? -50 : 50 },
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration: 0.5
      }
    };
  }, [direction]);

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        width: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: 2,
        overflow: 'hidden',
        backgroundColor: alpha(theme.palette.background.paper, 0.7),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        mb: 3,
        position: 'relative',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.15)}`,
          transform: 'translateY(-2px)'
        }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Navigation buttons */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          left: 8,
          display: 'flex',
          alignItems: 'center',
          opacity: isHovered ? 1 : 0,
          transition: 'opacity 0.3s ease',
          zIndex: 2
        }}
      >
        <IconButton
          size="small"
          onClick={handlePrevious}
          sx={{
            backgroundColor: alpha(theme.palette.background.paper, 0.7),
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.1)
            }
          }}
        >
          <ChevronLeft />
        </IconButton>
      </Box>

      <Box
        sx={{
          position: 'absolute',
          top: 0,
          bottom: 0,
          right: 8,
          display: 'flex',
          alignItems: 'center',
          opacity: isHovered ? 1 : 0,
          transition: 'opacity 0.3s ease',
          zIndex: 2
        }}
      >
        <IconButton
          size="small"
          onClick={handleNext}
          sx={{
            backgroundColor: alpha(theme.palette.background.paper, 0.7),
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.1)
            }
          }}
        >
          <ChevronRight />
        </IconButton>
      </Box>

      {/* Play/Pause button */}
      <Box
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          opacity: isHovered ? 1 : 0,
          transition: 'opacity 0.3s ease',
          zIndex: 2
        }}
      >
        <IconButton
          size="small"
          onClick={togglePause}
          sx={{
            backgroundColor: alpha(theme.palette.background.paper, 0.7),
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.1)
            }
          }}
        >
          {isPaused ? <PlayArrow fontSize="small" /> : <Pause fontSize="small" />}
        </IconButton>
      </Box>

      {/* Sponsor content with improved animations */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          {...getAnimationProps()}
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            padding: '8px'
          }}
        >
          <Box
            component="a"
            href={sponsors[currentIndex].url}
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              color: 'inherit',
              transition: 'transform 0.3s ease',
              '&:hover': {
                transform: 'scale(1.05)'
              }
            }}
          >
            <Box
              component="img"
              src={sponsors[currentIndex].image}
              alt={sponsors[currentIndex].name}
              sx={{
                height: 60,
                maxWidth: 200,
                objectFit: 'contain',
                mr: 2,
                borderRadius: 1,
                backgroundColor: 'transparent',
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
              }}
            />
            <Box>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  textShadow: '0 1px 2px rgba(0,0,0,0.05)',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                {sponsors[currentIndex].name}
                <Tooltip title={translate ? translate('dashboard.sponsors.visit_site', { fallback: 'Visit website' }) : 'Visit website'}>
                  <IconButton
                    size="small"
                    sx={{ ml: 1, color: theme.palette.primary.main }}
                    onClick={(e) => {
                      e.stopPropagation();
                      window.open(sponsors[currentIndex].url, '_blank');
                    }}
                  >
                    <OpenInNew fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {sponsors[currentIndex].description}
              </Typography>
            </Box>
          </Box>
        </motion.div>
      </AnimatePresence>

      {/* Enhanced dots indicator with click functionality */}
      <Box
        sx={{
          display: 'flex',
          position: 'absolute',
          bottom: 8,
          left: 0,
          right: 0,
          justifyContent: 'center'
        }}
      >
        {sponsors.map((_, index) => (
          <Box
            key={index}
            onClick={() => jumpToSponsor(index)}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              mx: 0.5,
              cursor: 'pointer',
              backgroundColor: index === currentIndex
                ? theme.palette.primary.main
                : alpha(theme.palette.primary.main, 0.3),
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'scale(1.5)',
                backgroundColor: alpha(theme.palette.primary.main, 0.7)
              }
            }}
          />
        ))}
      </Box>
    </Paper>
  );
};

export default SponsorRotator;
