/**
 * financial Routes
 * 
 * API routes for financial module
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const financialController = require('../controllers/financialController');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * GET /api/financial
 * 
 * @route GET /
 * @group financial - financial operations
 * @returns {object} 200 - Success response
 */
router.get('/', financialController.getAll);

/**
 * GET /api/financial/:id
 * 
 * @route GET /:id
 * @group financial - financial operations
 * @returns {object} 200 - Success response
 */
router.get('/:id', financialController.getById);

/**
 * POST /api/financial
 * 
 * @route POST /
 * @group financial - financial operations
 * @returns {object} 201 - Created response
 */
router.post('/', financialController.create);

/**
 * PUT /api/financial/:id
 * 
 * @route PUT /:id
 * @group financial - financial operations
 * @returns {object} 200 - Success response
 */
router.put('/:id', financialController.update);

/**
 * DELETE /api/financial/:id
 * 
 * @route DELETE /:id
 * @group financial - financial operations
 * @returns {object} 200 - Success response
 */
router.delete('/:id', financialController.delete);

module.exports = router;
