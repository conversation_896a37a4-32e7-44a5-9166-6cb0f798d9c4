/**
 * WebSocket Service for Real-time Notifications
 * Handles real-time communication between client and server
 */

interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
  id?: string;
}

interface NotificationData {
  title: string;
  message: string;
  type: 'health' | 'breeding' | 'weather' | 'system' | 'alert';
  priority: 'low' | 'medium' | 'high' | 'critical';
  animalId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

type WebSocketEventHandler = (data: any) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private isConnected = false;
  private url: string;
  private token: string | null = null;

  constructor() {
    this.url = process.env.REACT_APP_WS_URL || 'ws://localhost:3001';
  }

  /**
   * Initialize WebSocket connection
   */
  async connect(token?: string): Promise<boolean> {
    try {
      this.token = token || localStorage.getItem('token');
      
      if (!this.token) {
        console.warn('No authentication token available for WebSocket connection');
        return false;
      }

      // Close existing connection if any
      if (this.ws) {
        this.ws.close();
      }

      // Create new WebSocket connection
      const wsUrl = `${this.url}?token=${this.token}`;
      this.ws = new WebSocket(wsUrl);

      // Set up event listeners
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve(false);
        }, 10000); // 10 second timeout

        this.ws!.onopen = () => {
          clearTimeout(timeout);
          this.handleOpen();
          resolve(true);
        };
      });
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      return false;
    }
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * Send message through WebSocket
   */
  send(type: string, data: any): boolean {
    if (!this.isConnected || !this.ws) {
      console.warn('WebSocket not connected');
      return false;
    }

    try {
      const message: WebSocketMessage = {
        type,
        data,
        timestamp: new Date().toISOString(),
        id: this.generateMessageId()
      };

      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }

  /**
   * Subscribe to specific event types
   */
  on(eventType: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  /**
   * Unsubscribe from event types
   */
  off(eventType: string, handler?: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      return;
    }

    if (handler) {
      const handlers = this.eventHandlers.get(eventType)!;
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      this.eventHandlers.delete(eventType);
    }
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // Subscribe to user-specific notifications
    this.send('subscribe', {
      types: ['health_reminder', 'breeding_alert', 'weather_alert', 'system_notification']
    });

    this.emit('connected', {});
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      console.log('WebSocket message received:', message);
      
      // Emit to specific event handlers
      this.emit(message.type, message.data);
      
      // Handle specific notification types
      switch (message.type) {
        case 'health_reminder':
          this.handleHealthReminder(message.data);
          break;
        case 'breeding_alert':
          this.handleBreedingAlert(message.data);
          break;
        case 'weather_alert':
          this.handleWeatherAlert(message.data);
          break;
        case 'system_notification':
          this.handleSystemNotification(message.data);
          break;
        case 'real_time_update':
          this.handleRealTimeUpdate(message.data);
          break;
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.isConnected = false;
    
    this.emit('disconnected', { code: event.code, reason: event.reason });
    
    // Attempt to reconnect if not a normal closure
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.emit('error', error);
  }

  /**
   * Attempt to reconnect to WebSocket
   */
  private attemptReconnect(): void {
    this.reconnectAttempts++;
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      this.connect(this.token || undefined);
    }, this.reconnectInterval * this.reconnectAttempts);
  }

  /**
   * Emit event to handlers
   */
  private emit(eventType: string, data: any): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * Handle health reminder notifications
   */
  private handleHealthReminder(data: NotificationData): void {
    // Import push notification service dynamically to avoid circular dependencies
    import('./pushNotificationService').then(({ default: pushService }) => {
      pushService.showHealthReminder(
        data.metadata?.animalName || 'Unknown Animal',
        data.message
      );
    });
  }

  /**
   * Handle breeding alert notifications
   */
  private handleBreedingAlert(data: NotificationData): void {
    import('./pushNotificationService').then(({ default: pushService }) => {
      pushService.showBreedingNotification(
        data.metadata?.animalName || 'Unknown Animal',
        data.message
      );
    });
  }

  /**
   * Handle weather alert notifications
   */
  private handleWeatherAlert(data: NotificationData): void {
    import('./pushNotificationService').then(({ default: pushService }) => {
      pushService.showWeatherAlert(data.message, data.priority as any);
    });
  }

  /**
   * Handle system notifications
   */
  private handleSystemNotification(data: NotificationData): void {
    // Show in-app notification
    this.emit('system_notification', data);
  }

  /**
   * Handle real-time updates
   */
  private handleRealTimeUpdate(data: any): void {
    // Emit real-time update for components to handle
    this.emit('real_time_update', data);
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get connection status
   */
  isWebSocketConnected(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Send heartbeat to keep connection alive
   */
  sendHeartbeat(): void {
    this.send('heartbeat', { timestamp: new Date().toISOString() });
  }

  /**
   * Start heartbeat interval
   */
  startHeartbeat(interval: number = 30000): void {
    setInterval(() => {
      if (this.isWebSocketConnected()) {
        this.sendHeartbeat();
      }
    }, interval);
  }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
export type { WebSocketMessage, NotificationData, WebSocketEventHandler };
