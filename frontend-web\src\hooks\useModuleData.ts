/**
 * Module Data Hook
 * 
 * This hook provides a standardized interface for data fetching and CRUD operations
 * that can be used across all modules in the application.
 */

import { useState, useEffect, useCallback } from 'react';
import { useSnackbar } from '../contexts/SnackbarContext';
import { useTranslation } from './useTranslation';
import { BaseEntity } from '../services/moduleService';

interface UseModuleDataOptions<T extends BaseEntity> {
  service: {
    getAll: (params?: Record<string, any>) => Promise<T[]>;
    getById: (id: string) => Promise<T>;
    create: (data: Omit<T, 'id'>) => Promise<T>;
    update: (id: string, data: Partial<T>) => Promise<T>;
    delete: (id: string) => Promise<void>;
  };
  initialData?: T[];
  autoLoad?: boolean;
  transformData?: (data: T[]) => T[];
  calculateStats?: (data: T[]) => Record<string, any>;
  onDataChange?: (data: T[]) => void;
  errorMessages?: {
    loadError?: string;
    createError?: string;
    updateError?: string;
    deleteError?: string;
  };
  successMessages?: {
    createSuccess?: string;
    updateSuccess?: string;
    deleteSuccess?: string;
  };
}

function useModuleData<T extends BaseEntity>({
  service,
  initialData = [],
  autoLoad = true,
  transformData,
  calculateStats,
  onDataChange,
  errorMessages = {},
  successMessages = {},
}: UseModuleDataOptions<T>) {
  const [data, setData] = useState<T[]>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<Record<string, any>>({});
  const { showSnackbar } = useSnackbar();
  const { translate } = useTranslation();

  // Default error messages
  const defaultErrorMessages = {
    loadError: translate ? translate('common.load_error', { fallback: 'Failed to load data' }) : 'Failed to load data',
    createError: translate ? translate('common.create_error', { fallback: 'Failed to create item' }) : 'Failed to create item',
    updateError: translate ? translate('common.update_error', { fallback: 'Failed to update item' }) : 'Failed to update item',
    deleteError: translate ? translate('common.delete_error', { fallback: 'Failed to delete item' }) : 'Failed to delete item',
  };

  // Default success messages
  const defaultSuccessMessages = {
    createSuccess: translate ? translate('common.create_success', { fallback: 'Item created successfully' }) : 'Item created successfully',
    updateSuccess: translate ? translate('common.update_success', { fallback: 'Item updated successfully' }) : 'Item updated successfully',
    deleteSuccess: translate ? translate('common.delete_success', { fallback: 'Item deleted successfully' }) : 'Item deleted successfully',
  };

  // Merge default and custom messages
  const errors = { ...defaultErrorMessages, ...errorMessages };
  const successes = { ...defaultSuccessMessages, ...successMessages };

  // Load data
  const loadData = useCallback(async (params?: Record<string, any>) => {
    setLoading(true);
    setError(null);
    try {
      let result = await service.getAll(params);
      
      // Apply transformation if provided
      if (transformData) {
        result = transformData(result);
      }
      
      setData(result);
      
      // Calculate stats if provided
      if (calculateStats) {
        setStats(calculateStats(result));
      }
      
      // Call onDataChange callback if provided
      if (onDataChange) {
        onDataChange(result);
      }
      
      return result;
    } catch (err) {
      console.error('Error loading data:', err);
      setError(errors.loadError || defaultErrorMessages.loadError);
      showSnackbar(errors.loadError || defaultErrorMessages.loadError, 'error');
      return [];
    } finally {
      setLoading(false);
    }
  }, [service, transformData, calculateStats, onDataChange, errors.loadError, showSnackbar]);

  // Load data on mount if autoLoad is true
  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
  }, [autoLoad, loadData]);

  // Get item by ID
  const getById = useCallback(async (id: string) => {
    setLoading(true);
    try {
      const result = await service.getById(id);
      return result;
    } catch (err) {
      console.error(`Error getting item with ID ${id}:`, err);
      showSnackbar(errors.loadError || defaultErrorMessages.loadError, 'error');
      return null;
    } finally {
      setLoading(false);
    }
  }, [service, errors.loadError, showSnackbar]);

  // Create item
  const createItem = useCallback(async (item: Omit<T, 'id'>) => {
    setLoading(true);
    try {
      const result = await service.create(item);
      
      // Update data state
      setData(prev => {
        const newData = [...prev, result];
        
        // Calculate stats if provided
        if (calculateStats) {
          setStats(calculateStats(newData));
        }
        
        // Call onDataChange callback if provided
        if (onDataChange) {
          onDataChange(newData);
        }
        
        return newData;
      });
      
      showSnackbar(successes.createSuccess || defaultSuccessMessages.createSuccess, 'success');
      return result;
    } catch (err) {
      console.error('Error creating item:', err);
      showSnackbar(errors.createError || defaultErrorMessages.createError, 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [service, calculateStats, onDataChange, errors.createError, successes.createSuccess, showSnackbar]);

  // Update item
  const updateItem = useCallback(async (id: string, item: Partial<T>) => {
    setLoading(true);
    try {
      const result = await service.update(id, item);
      
      // Update data state
      setData(prev => {
        const newData = prev.map(i => i.id === id ? { ...i, ...result } : i);
        
        // Calculate stats if provided
        if (calculateStats) {
          setStats(calculateStats(newData));
        }
        
        // Call onDataChange callback if provided
        if (onDataChange) {
          onDataChange(newData);
        }
        
        return newData;
      });
      
      showSnackbar(successes.updateSuccess || defaultSuccessMessages.updateSuccess, 'success');
      return result;
    } catch (err) {
      console.error(`Error updating item with ID ${id}:`, err);
      showSnackbar(errors.updateError || defaultErrorMessages.updateError, 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [service, calculateStats, onDataChange, errors.updateError, successes.updateSuccess, showSnackbar]);

  // Delete item
  const deleteItem = useCallback(async (id: string) => {
    setLoading(true);
    try {
      await service.delete(id);
      
      // Update data state
      setData(prev => {
        const newData = prev.filter(i => i.id !== id);
        
        // Calculate stats if provided
        if (calculateStats) {
          setStats(calculateStats(newData));
        }
        
        // Call onDataChange callback if provided
        if (onDataChange) {
          onDataChange(newData);
        }
        
        return newData;
      });
      
      showSnackbar(successes.deleteSuccess || defaultSuccessMessages.deleteSuccess, 'success');
    } catch (err) {
      console.error(`Error deleting item with ID ${id}:`, err);
      showSnackbar(errors.deleteError || defaultErrorMessages.deleteError, 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [service, calculateStats, onDataChange, errors.deleteError, successes.deleteSuccess, showSnackbar]);

  return {
    data,
    loading,
    error,
    stats,
    loadData,
    getById,
    createItem,
    updateItem,
    deleteItem,
    setData,
  };
}

export default useModuleData;
