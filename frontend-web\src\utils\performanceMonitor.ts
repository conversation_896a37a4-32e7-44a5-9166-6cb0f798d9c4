/**
 * Performance Monitoring Utility
 * Provides functionality for monitoring and optimizing application performance
 */

// Interface for performance metrics
interface PerformanceMetrics {
  navigationStart: number;
  loadTime: number;
  domContentLoaded: number;
  firstPaint: number;
  firstContentfulPaint: number;
  largestContentfulPaint?: number;
  firstInputDelay?: number;
  cumulativeLayoutShift?: number;
  memoryUsage?: number;
  resourceCount: number;
  resourceSize: number;
}

// Interface for resource metrics
interface ResourceMetrics {
  url: string;
  initiatorType: string;
  duration: number;
  size: number;
  startTime: number;
}

/**
 * Get basic performance metrics
 * @returns Object containing performance metrics
 */
export const getPerformanceMetrics = (): PerformanceMetrics => {
  if (!window.performance) {
    console.warn('Performance API not supported');
    return {
      navigationStart: 0,
      loadTime: 0,
      domContentLoaded: 0,
      firstPaint: 0,
      firstContentfulPaint: 0,
      resourceCount: 0,
      resourceSize: 0
    };
  }

  const navigationStart = performance.timing.navigationStart;
  const loadTime = performance.timing.loadEventEnd - navigationStart;
  const domContentLoaded = performance.timing.domContentLoadedEventEnd - navigationStart;

  // Get paint metrics
  let firstPaint = 0;
  let firstContentfulPaint = 0;
  const paintEntries = performance.getEntriesByType('paint');

  for (const entry of paintEntries) {
    if (entry.name === 'first-paint') {
      firstPaint = entry.startTime;
    } else if (entry.name === 'first-contentful-paint') {
      firstContentfulPaint = entry.startTime;
    }
  }

  // Get resource metrics
  const resourceEntries = performance.getEntriesByType('resource');
  const resourceCount = resourceEntries.length;
  let resourceSize = 0;

  for (const entry of resourceEntries) {
    // Use type assertion for PerformanceResourceTiming
    const resourceEntry = entry as PerformanceResourceTiming;
    if (resourceEntry.transferSize) {
      resourceSize += resourceEntry.transferSize;
    }
  }

  // Get memory usage if available
  let memoryUsage;
  // Chrome-specific memory API
  const perfMemory = performance as any;
  if (perfMemory.memory) {
    memoryUsage = perfMemory.memory.usedJSHeapSize;
  }

  // Get Web Vitals if available
  let largestContentfulPaint;
  let firstInputDelay;
  let cumulativeLayoutShift;

  // LCP
  const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
  if (lcpEntries.length > 0) {
    largestContentfulPaint = lcpEntries[lcpEntries.length - 1].startTime;
  }

  // FID - approximated from first-input entries
  const fidEntries = performance.getEntriesByType('first-input');
  if (fidEntries.length > 0) {
    // Use type assertion for PerformanceEventTiming
    const fidEntry = fidEntries[0] as any;
    firstInputDelay = fidEntry.processingStart - fidEntry.startTime;
  }

  // CLS - approximated from layout-shift entries
  const clsEntries = performance.getEntriesByType('layout-shift');
  if (clsEntries.length > 0) {
    // Use type assertion for layout shift entries
    cumulativeLayoutShift = clsEntries.reduce((sum, entry) => sum + (entry as any).value, 0);
  }

  return {
    navigationStart,
    loadTime,
    domContentLoaded,
    firstPaint,
    firstContentfulPaint,
    largestContentfulPaint,
    firstInputDelay,
    cumulativeLayoutShift,
    memoryUsage,
    resourceCount,
    resourceSize
  };
};

/**
 * Get detailed resource metrics
 * @returns Array of resource metrics
 */
export const getResourceMetrics = (): ResourceMetrics[] => {
  if (!window.performance) {
    console.warn('Performance API not supported');
    return [];
  }

  const resourceEntries = performance.getEntriesByType('resource');

  return resourceEntries.map(entry => {
    // Use type assertion for PerformanceResourceTiming
    const resourceEntry = entry as PerformanceResourceTiming;
    return {
      url: resourceEntry.name,
      initiatorType: resourceEntry.initiatorType,
      duration: resourceEntry.duration,
      size: resourceEntry.transferSize || 0,
      startTime: resourceEntry.startTime
    };
  });
};

/**
 * Clear performance entries
 * @param types Types of entries to clear (default: all)
 */
export const clearPerformanceEntries = (types?: string[]): void => {
  if (!window.performance || !performance.clearResourceTimings) {
    console.warn('Performance API not fully supported');
    return;
  }

  if (!types) {
    performance.clearResourceTimings();
    performance.clearMarks();
    performance.clearMeasures();
    return;
  }

  for (const type of types) {
    performance.clearResourceTimings();
  }
};

/**
 * Start measuring a custom performance mark
 * @param markName Name of the mark
 */
export const startMeasure = (markName: string): void => {
  if (!window.performance || !performance.mark) {
    console.warn('Performance API not fully supported');
    return;
  }

  performance.mark(`${markName}_start`);
};

/**
 * End measuring a custom performance mark and log the result
 * @param markName Name of the mark
 * @returns Duration in milliseconds
 */
export const endMeasure = (markName: string): number => {
  if (!window.performance || !performance.mark || !performance.measure) {
    console.warn('Performance API not fully supported');
    return 0;
  }

  try {
    performance.mark(`${markName}_end`);
    performance.measure(markName, `${markName}_start`, `${markName}_end`);

    const entries = performance.getEntriesByName(markName);
    const duration = entries.length > 0 ? entries[0].duration : 0;

    console.log(`Performance: ${markName} took ${duration.toFixed(2)}ms`);
    return duration;
  } catch (error) {
    console.error('Error measuring performance:', error);
    return 0;
  }
};

/**
 * Log performance metrics to console
 */
export const logPerformanceMetrics = (): void => {
  const metrics = getPerformanceMetrics();

  console.group('Performance Metrics');
  console.log('Load Time:', metrics.loadTime.toFixed(2), 'ms');
  console.log('DOM Content Loaded:', metrics.domContentLoaded.toFixed(2), 'ms');
  console.log('First Paint:', metrics.firstPaint.toFixed(2), 'ms');
  console.log('First Contentful Paint:', metrics.firstContentfulPaint.toFixed(2), 'ms');

  if (metrics.largestContentfulPaint) {
    console.log('Largest Contentful Paint:', metrics.largestContentfulPaint.toFixed(2), 'ms');
  }

  if (metrics.firstInputDelay) {
    console.log('First Input Delay:', metrics.firstInputDelay.toFixed(2), 'ms');
  }

  if (metrics.cumulativeLayoutShift) {
    console.log('Cumulative Layout Shift:', metrics.cumulativeLayoutShift.toFixed(4));
  }

  console.log('Resource Count:', metrics.resourceCount);
  console.log('Resource Size:', (metrics.resourceSize / 1024 / 1024).toFixed(2), 'MB');

  if (metrics.memoryUsage) {
    console.log('Memory Usage:', (metrics.memoryUsage / 1024 / 1024).toFixed(2), 'MB');
  }

  console.groupEnd();
};

/**
 * Register performance observers for monitoring
 * @param onLCP Callback for Largest Contentful Paint
 * @param onFID Callback for First Input Delay
 * @param onCLS Callback for Cumulative Layout Shift
 */
export const registerPerformanceObservers = (
  onLCP?: (value: number) => void,
  onFID?: (value: number) => void,
  onCLS?: (value: number) => void
): () => void => {
  if (!window.PerformanceObserver) {
    console.warn('PerformanceObserver not supported');
    return () => {};
  }

  const observers: PerformanceObserver[] = [];

  // LCP observer
  try {
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      const lcp = lastEntry.startTime;

      if (onLCP) {
        onLCP(lcp);
      }
    });

    lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
    observers.push(lcpObserver);
  } catch (e) {
    console.warn('LCP observer not supported', e);
  }

  // FID observer
  try {
    const fidObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      for (const entry of entries) {
        // Use type assertion for PerformanceEventTiming
        const fidEntry = entry as any;
        const fid = fidEntry.processingStart - fidEntry.startTime;

        if (onFID) {
          onFID(fid);
        }
      }
    });

    fidObserver.observe({ type: 'first-input', buffered: true });
    observers.push(fidObserver);
  } catch (e) {
    console.warn('FID observer not supported', e);
  }

  // CLS observer
  try {
    let clsValue = 0;
    let clsEntries: PerformanceEntry[] = [];

    const clsObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();

      for (const entry of entries) {
        // Only count layout shifts without recent user input
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
          clsEntries.push(entry);

          if (onCLS) {
            onCLS(clsValue);
          }
        }
      }
    });

    clsObserver.observe({ type: 'layout-shift', buffered: true });
    observers.push(clsObserver);
  } catch (e) {
    console.warn('CLS observer not supported', e);
  }

  // Return disconnect function
  return () => {
    observers.forEach(observer => observer.disconnect());
  };
};

export default {
  getPerformanceMetrics,
  getResourceMetrics,
  clearPerformanceEntries,
  startMeasure,
  endMeasure,
  logPerformanceMetrics,
  registerPerformanceObservers
};
