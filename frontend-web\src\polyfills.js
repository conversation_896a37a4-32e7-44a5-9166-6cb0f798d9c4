// This file provides simple empty polyfills for Node.js modules used by MongoDB
// Simplified version to avoid webpack compilation issues

// Simple empty implementations
const emptyFunction = () => {};
const emptyObject = {};
const emptyPromise = () => Promise.resolve();
const emptyClass = class {};

// Mock implementations for Node.js modules
const mockImplementations = {
  // Mock fs module
  fs: {
    readFileSync: () => '',
    existsSync: () => false,
    readdirSync: () => []
  },

  // Mock net module
  net: {
    connect: () => ({
      on: emptyFunction,
      end: emptyFunction
    }),
    Socket: class {
      connect() { return this; }
      on() { return this; }
      end() {}
    }
  },

  // Mock tls module
  tls: {
    connect: () => ({
      on: emptyFunction,
      end: emptyFunction
    }),
    TLSSocket: class {
      connect() { return this; }
      on() { return this; }
      end() {}
    }
  },

  // Mock dns module
  dns: {
    lookup: (hostname, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
      }
      callback(null, '127.0.0.1', 4);
    },
    resolve: (hostname, callback) => {
      callback(null, ['127.0.0.1']);
    }
  },

  // Mock child_process module
  child_process: {
    spawn: () => ({
      on: emptyFunction,
      stdout: { on: emptyFunction },
      stderr: { on: emptyFunction }
    }),
    exec: (cmd, callback) => {
      callback(null, '', '');
    }
  },

  // Mock fs/promises module
  'fs/promises': {
    readFile: emptyPromise,
    writeFile: emptyPromise
  },

  // Mock timers/promises module
  'timers/promises': {
    setTimeout: emptyPromise
  }
};

// Export all mock implementations
export default mockImplementations;

// Also export individual modules for direct imports
export const fs = mockImplementations.fs;
export const net = mockImplementations.net;
export const tls = mockImplementations.tls;
export const dns = mockImplementations.dns;
export const child_process = mockImplementations.child_process;
export const fsPromises = mockImplementations['fs/promises'];
export const timersPromises = mockImplementations['timers/promises'];
