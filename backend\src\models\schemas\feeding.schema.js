/**
 * Feeding Schema
 * 
 * Defines the MongoDB schema for the feeding collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Feed Item Schema (for individual feed components)
const feedItemSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['hay', 'grain', 'supplement', 'mineral', 'water', 'other'],
    default: 'other'
  },
  quantity: {
    type: Number,
    required: true,
    min: 0
  },
  unit: {
    type: String,
    required: true,
    enum: ['kg', 'g', 'lb', 'oz', 'l', 'ml', 'cup', 'scoop', 'bale', 'other'],
    default: 'kg'
  },
  cost: {
    type: Number,
    min: 0
  },
  nutritionalInfo: {
    protein: { type: Number, min: 0 },
    fat: { type: Number, min: 0 },
    fiber: { type: Number, min: 0 },
    calcium: { type: Number, min: 0 },
    phosphorus: { type: Number, min: 0 },
    additionalInfo: { type: Map, of: Number }
  },
  notes: {
    type: String
  }
});

// Feeding Record Schema
const feedingRecordSchema = new Schema({
  animal: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  feedItems: [feedItemSchema],
  feedingTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  feeder: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  location: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'completed', 'missed', 'adjusted'],
    default: 'completed'
  },
  consumption: {
    type: String,
    enum: ['all', 'most', 'some', 'none', 'unknown'],
    default: 'unknown'
  },
  notes: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Feeding Schedule Schema
const feedingScheduleSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  animals: [{
    type: Schema.Types.ObjectId,
    ref: 'Animal'
  }],
  animalGroups: [{
    type: String,
    trim: true
  }],
  feedItems: [feedItemSchema],
  schedule: {
    frequency: {
      type: String,
      enum: ['daily', 'weekly', 'monthly', 'custom'],
      default: 'daily'
    },
    times: [{
      type: String, // Format: HH:MM
      required: true
    }],
    daysOfWeek: [{
      type: Number, // 0 = Sunday, 1 = Monday, etc.
      min: 0,
      max: 6
    }],
    daysOfMonth: [{
      type: Number,
      min: 1,
      max: 31
    }],
    startDate: {
      type: Date
    },
    endDate: {
      type: Date
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
feedingRecordSchema.index({ animal: 1, feedingTime: -1 });
feedingRecordSchema.index({ feedingTime: -1 });
feedingRecordSchema.index({ status: 1 });

feedingScheduleSchema.index({ 'animals': 1 });
feedingScheduleSchema.index({ 'animalGroups': 1 });
feedingScheduleSchema.index({ 'schedule.frequency': 1 });
feedingScheduleSchema.index({ isActive: 1 });

// Pre-save hook to update timestamps
feedingRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

feedingScheduleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const FeedingRecord = mongoose.model('FeedingRecord', feedingRecordSchema);
const FeedingSchedule = mongoose.model('FeedingSchedule', feedingScheduleSchema);

module.exports = {
  FeedingRecord,
  FeedingSchedule
};
