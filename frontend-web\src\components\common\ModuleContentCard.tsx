import React from 'react';
import { Box, Typography, Paper, alpha, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { CustomButton } from './';

interface ModuleContentCardProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  height?: string | number;
  accentColor?: string;
  secondaryColor?: string;
  actionLabel?: string;
  onAction?: () => void;
  loading?: boolean;
  delay?: number;
  module?: string;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: () => void;
  hoverEffect?: 'scale' | 'glow' | 'border' | 'none';
}

/**
 * ModuleContentCard - A standardized card component for all module content
 * Based on the BusinessAnalyticsCard design pattern
 */
const ModuleContentCard: React.FC<ModuleContentCardProps> = ({
  title,
  subtitle,
  icon,
  children,
  height = 'auto',
  accentColor,
  secondaryColor,
  actionLabel,
  onAction,
  loading = false,
  delay = 0,
  module = 'default',
  selectable = false,
  selected = false,
  onSelect,
  hoverEffect = 'scale'
}) => {
  const theme = useTheme();
  
  // Use provided colors or fallback to theme colors
  const primaryColor = accentColor || theme.palette.primary.main;
  const secondaryColorValue = secondaryColor || theme.palette.primary.main;

  // Get module-specific colors
  const getModuleColors = () => {
    switch (module) {
      case 'animals':
        return {
          primary: theme.palette.primary.main,
          secondary: theme.palette.primary.dark
        };
      case 'health':
        return {
          primary: '#4caf50',
          secondary: '#388e3c'
        };
      case 'breeding':
        return {
          primary: '#9c27b0',
          secondary: '#7b1fa2'
        };
      case 'feeding':
        return {
          primary: '#ff9800',
          secondary: '#f57c00'
        };
      case 'financial':
        return {
          primary: '#2196f3',
          secondary: '#1976d2'
        };
      case 'reports':
        return {
          primary: '#795548',
          secondary: '#5d4037'
        };
      case 'commercial':
        return {
          primary: '#f44336',
          secondary: '#d32f2f'
        };
      case 'analytics':
        return {
          primary: '#3AA99F',
          secondary: '#2A8A82'
        };
      default:
        return {
          primary: primaryColor,
          secondary: secondaryColorValue
        };
    }
  };

  const moduleColors = getModuleColors();
  
  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: delay,
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  // Get hover effect styles
  const getHoverStyles = () => {
    switch (hoverEffect) {
      case 'scale':
        return {
          transform: 'scale(1.02)',
          boxShadow: '0 8px 30px rgba(0,0,0,0.12)'
        };
      case 'glow':
        return {
          boxShadow: `0 0 20px ${alpha(moduleColors.primary, 0.5)}`
        };
      case 'border':
        return {
          border: `2px solid ${moduleColors.primary}`
        };
      case 'none':
      default:
        return {};
    }
  };

  // Get selection styles
  const getSelectionStyles = () => {
    if (!selectable) return {};
    
    return selected ? {
      border: `2px solid ${moduleColors.primary}`,
      boxShadow: `0 0 10px ${alpha(moduleColors.primary, 0.5)}`
    } : {};
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      style={{ height: '100%' }}
    >
      <Paper
        elevation={3}
        onClick={selectable ? onSelect : onAction}
        sx={{
          height: height,
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          cursor: (selectable || onAction) ? 'pointer' : 'default',
          transition: 'all 0.3s ease',
          background: `linear-gradient(135deg, 
            ${alpha(theme.palette.background.paper, 0.8)}, 
            ${alpha(theme.palette.background.paper, 0.6)})`,
          backdropFilter: 'blur(10px)',
          '&:hover': getHoverStyles(),
          ...getSelectionStyles()
        }}
      >
        {/* Top accent bar */}
        <Box
          sx={{
            height: '4px',
            width: '100%',
            background: `linear-gradient(to right, ${moduleColors.primary}, ${moduleColors.secondary})`,
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        />
        
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 2,
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          {icon && (
            <Box
              sx={{
                mr: 1.5,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: moduleColors.primary
              }}
            >
              {icon}
            </Box>
          )}
          <Box>
            <Typography variant="h6" fontWeight="medium">
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
        
        {/* Content */}
        <Box sx={{ p: 2 }}>
          {children}
        </Box>
        
        {/* Action button */}
        {actionLabel && onAction && (
          <Box
            sx={{
              p: 2,
              borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              display: 'flex',
              justifyContent: 'flex-end'
            }}
          >
            <CustomButton
              variant="contained"
              color="primary"
              onClick={(e) => {
                e.stopPropagation();
                onAction();
              }}
              disabled={loading}
            >
              {actionLabel}
            </CustomButton>
          </Box>
        )}
      </Paper>
    </motion.div>
  );
};

export default ModuleContentCard;
