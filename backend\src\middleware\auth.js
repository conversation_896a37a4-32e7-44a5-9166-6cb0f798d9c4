const jwt = require('jsonwebtoken');
const { getCollection } = require('../config/db');
const logger = require('../utils/logger');

// JWT Secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET;

// Ensure JWT_SECRET is set
if (!JWT_SECRET) {
  logger.error('JWT_SECRET is not set in environment variables');
  throw new Error('JWT_SECRET is required for authentication');
}

/**
 * Middleware to authenticate JWT token
 */
const authenticateToken = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      logger.warn('Authentication failed: No token provided');
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Verify token
    jwt.verify(token, JWT_SECRET, async (err, decoded) => {
      if (err) {
        logger.warn('Authentication failed: Invalid token', err);
        return res.status(403).json({ error: 'Invalid or expired token' });
      }

      // Get user from database
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by ID
      let user;
      try {
        const ObjectId = require('mongodb').ObjectId;
        user = await usersCollection.findOne({ _id: new ObjectId(decoded.id) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        user = await usersCollection.findOne({ _id: decoded.id });
      }

      if (!user) {
        logger.warn(`User not found: ${decoded.id}`);
        return res.status(403).json({ error: 'Invalid token: User not found' });
      }

      // Check if user is active
      if (user.status === 'inactive' || user.isActive === false) {
        logger.warn(`Access attempt with inactive account: ${user.username}`);
        return res.status(403).json({ error: 'Account is inactive' });
      }

      // Check if user is suspended
      if (user.status === 'suspended') {
        logger.warn(`Access attempt with suspended account: ${user.username}`);
        return res.status(403).json({ error: 'Account is suspended' });
      }

      // Set user in request object
      req.user = {
        id: user._id.toString(),
        username: user.username,
        role: user.role,
        permissions: user.permissions || []
      };

      next();
    });
  } catch (error) {
    logger.error('Authentication error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Middleware to protect routes - alias for authenticateToken for compatibility
 */
const protect = authenticateToken;

/**
 * Middleware to authorize based on user role
 * @param {string|string[]} roles - Role(s) allowed to access the route
 */
const authorize = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Admin users always have access to everything
    if (req.user.role === 'admin') {
      logger.info(`Admin user ${req.user.username} granted full access`);
      return next();
    }

    // Convert roles to array if it's a single string
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (allowedRoles.includes(req.user.role)) {
      return next();
    }

    logger.warn(`Access denied for user ${req.user.username} with role ${req.user.role}. Required roles: ${allowedRoles.join(', ')}`);
    return res.status(403).json({
      error: 'You do not have permission to perform this action'
    });
  };
};

module.exports = {
  authenticateToken,
  protect,
  authorize
};
