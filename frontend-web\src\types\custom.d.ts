import '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    custom: {
      primaryMain: string;
      primaryDark: string;
      primaryLight: string;
      primaryLighter: string;
      cardGradient: string;
      lightBackground: string;
      subtleAccent: string;
      subtleBorder: string;
      subtleHover: string;
      borderColor: string;
      elevation: {
        0: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        [key: number]: string;
      };
      animation: {
        short: string;
        medium: string;
        long: string;
        bounce: string;
      };
    };
  }

  interface PaletteOptions {
    custom?: {
      primaryMain?: string;
      primaryDark?: string;
      primaryLight?: string;
      primaryLighter?: string;
      cardGradient?: string;
      lightBackground?: string;
      subtleAccent?: string;
      subtleBorder?: string;
      subtleHover?: string;
      borderColor?: string;
      elevation?: {
        0: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        [key: number]: string;
      };
      animation?: {
        short: string;
        medium: string;
        long: string;
        bounce: string;
      };
    };
  }
}

// Extend the theme to include custom properties
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      primaryMain: string;
      primaryDark: string;
      primaryLight: string;
      primaryLighter: string;
      cardGradient: string;
      lightBackground: string;
      subtleAccent: string;
      subtleBorder: string;
      subtleHover: string;
      borderColor: string;
      elevation: {
        0: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        [key: number]: string;
      };
      animation: {
        short: string;
        medium: string;
        long: string;
        bounce: string;
      };
    };
  }

  interface ThemeOptions {
    custom?: {
      primaryMain?: string;
      primaryDark?: string;
      primaryLight?: string;
      primaryLighter?: string;
      cardGradient?: string;
      lightBackground?: string;
      subtleAccent?: string;
      subtleBorder?: string;
      subtleHover?: string;
      borderColor?: string;
      elevation?: {
        0: string;
        1: string;
        2: string;
        3: string;
        4: string;
        5: string;
        [key: number]: string;
      };
      animation?: {
        short: string;
        medium: string;
        long: string;
        bounce: string;
      };
    };
  }
}
