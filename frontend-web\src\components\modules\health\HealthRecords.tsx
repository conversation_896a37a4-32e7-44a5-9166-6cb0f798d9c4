import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, useTheme, alpha, CircularProgress } from '@mui/material';
import {
  Healing,
  Vaccines,
  MedicalServices,
  Visibility
} from '../../../utils/iconImports';
import { getCollection } from '../../../utils/mockMongoDbClient';
import { useSnackbar } from '../../../contexts/SnackbarContext';

// Mock health records data with Sotho, Tswana, and Afrikaans names
const mockHealthRecords = [
  {
    id: 'HR001',
    animalId: 'CTL-2020-001',
    animalName: 'Tshepiso',
    recordType: 'Vaccination',
    description: 'Annual vaccination against Brucellosis',
    date: '2024-05-10',
    performedBy: 'Dr. <PERSON>',
    status: 'completed'
  },
  {
    id: 'HR002',
    animalId: 'CTL-2019-002',
    animalName: 'Tumelo',
    recordType: 'Treatment',
    description: 'Antibiotic treatment for respiratory infection',
    date: '2024-05-12',
    performedBy: 'Dr. <PERSON> Nkosi',
    status: 'completed'
  },
  {
    id: 'HR003',
    animalId: 'CTL-2021-003',
    animalName: 'Lerato',
    recordType: 'Examination',
    description: 'Routine health check and pregnancy confirmation',
    date: '2024-05-15',
    performedBy: 'Dr. Johan Smith',
    status: 'completed'
  },
  {
    id: 'HR004',
    animalId: 'CTL-2020-004',
    animalName: 'Botha',
    recordType: 'Surgery',
    description: 'Minor wound treatment and suturing',
    date: '2024-05-18',
    performedBy: 'Dr. Maria Nkosi',
    status: 'monitoring'
  },
  {
    id: 'HR005',
    animalId: 'CTL-2022-005',
    animalName: 'Naledi',
    recordType: 'Vaccination',
    description: 'Booster shot for Anthrax prevention',
    date: '2024-05-20',
    performedBy: 'Dr. Johan Smith',
    status: 'scheduled'
  },
  {
    id: 'HR006',
    animalId: 'SHP-2023-001',
    animalName: 'Kgosi',
    recordType: 'Treatment',
    description: 'Parasite control and deworming',
    date: '2024-05-22',
    performedBy: 'Dr. Maria Nkosi',
    status: 'scheduled'
  },
  {
    id: 'HR007',
    animalId: 'GOT-2023-001',
    animalName: 'Pule',
    recordType: 'Examination',
    description: 'Follow-up check after kidding',
    date: '2024-05-25',
    performedBy: 'Dr. Johan Smith',
    status: 'scheduled'
  }
];

interface HealthRecordsProps {
  limit?: number;
  showViewAll?: boolean;
}

const HealthRecords: React.FC<HealthRecordsProps> = ({ limit, showViewAll = false }) => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [healthRecords, setHealthRecords] = useState(mockHealthRecords);

  // Fetch health records from MongoDB
  useEffect(() => {
    const fetchHealthRecords = async () => {
      try {
        setLoading(true);
        const collection = await getCollection('health_records');
        const records = await collection.find({}).toArray();

        if (records && records.length > 0) {
          // Format the records to match the expected structure
          const formattedRecords = records.map(record => ({
            id: record._id || record.id,
            animalId: record.animalId || '',
            animalName: record.animalName || 'Unknown',
            recordType: record.type || record.recordType || 'Examination',
            description: record.description || '',
            date: record.date ? new Date(record.date).toLocaleDateString() : new Date().toLocaleDateString(),
            performedBy: record.performedBy || 'Unknown',
            status: record.status || 'completed'
          }));
          setHealthRecords(formattedRecords);
        }
      } catch (error) {
        console.error('Error fetching health records:', error);
        showSnackbar('Failed to load health records', 'error');
      } finally {
        setLoading(false);
      }
    };

    fetchHealthRecords();
  }, [showSnackbar]);

  // Limit the number of records if specified
  const displayRecords = limit ? healthRecords.slice(0, limit) : healthRecords;

  const getRecordTypeIcon = (recordType: string) => {
    switch (recordType) {
      case 'Vaccination':
        return <Vaccines fontSize="small" sx={{ color: theme.palette.primary.main }} />;
      case 'Treatment':
        return <Healing fontSize="small" sx={{ color: theme.palette.warning.main }} />;
      case 'Surgery':
        return <MedicalServices fontSize="small" sx={{ color: theme.palette.error.main }} />;
      case 'Examination':
      default:
        return <MedicalServices fontSize="small" sx={{ color: theme.palette.info.main }} />;
    }
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'completed':
        return <Chip
          label="Completed"
          size="small"
          color="success"
          variant="outlined"
        />;
      case 'monitoring':
        return <Chip
          label="Monitoring"
          size="small"
          color="warning"
          variant="outlined"
        />;
      case 'scheduled':
        return <Chip
          label="Scheduled"
          size="small"
          color="info"
          variant="outlined"
        />;
      default:
        return <Chip label={status} size="small" variant="outlined" />;
    }
  };

  return (
    <Box>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={40} />
        </Box>
      ) : (
        <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
          <Table size="small">
            <TableHead>
              <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                <TableCell>Record ID</TableCell>
                <TableCell>Animal</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {displayRecords.length > 0 ? displayRecords.map((record) => (
              <TableRow
                key={record.id}
                hover
                sx={{
                  '&:last-child td, &:last-child th': { border: 0 },
                  transition: 'background-color 0.3s',
                  '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.05) }
                }}
              >
                <TableCell component="th" scope="row">
                  <Typography variant="body2" fontWeight="medium">
                    {record.id}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {record.animalName} ({record.animalId})
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getRecordTypeIcon(record.recordType)}
                    <Typography variant="body2" sx={{ ml: 1 }}>
                      {record.recordType}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                    {record.description}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {record.date}
                  </Typography>
                </TableCell>
                <TableCell>{getStatusChip(record.status)}</TableCell>
                <TableCell align="right">
                  <CustomButton
                    size="small"
                    startIcon={<Visibility fontSize="small" />}
                    sx={{ minWidth: 'auto' }}
                  >
                    View
                  </CustomButton>
                </TableCell>
              </TableRow>
            )) : (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    <Typography variant="body2" sx={{ py: 2 }}>
                      No health records found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
          </TableBody>
        </Table>
      </TableContainer>
      )}

      {showViewAll && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
          <CustomButton variant="outlined" size="small">
            View All Health Records
          </CustomButton>
        </Box>
      )}
    </Box>
  );
};

export default HealthRecords;
