import React, { useState, useEffect } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  InputLabel,
  SelectChangeEvent,
  Box,
  Typography,
  useTheme,
  alpha,
  Tooltip,
  CircularProgress,
  Button,
  Menu,
  ListItemIcon,
  ListItemText,
  Chip,
  Paper,
  Fade,
  Backdrop
} from '@mui/material';
import { Language as LanguageIcon, ExpandMore, Check, Public } from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage, LANGUAGES, LANGUAGE_NAMES } from '../contexts/LanguageContext';

// Premium color palette
const premiumColors = {
  deepBlue: {
    primary: '#0F172A',
    secondary: '#1E293B',
    accent: '#3B82F6',
    light: '#64748B'
  },
  emeraldGreen: {
    primary: '#064E3B',
    secondary: '#065F46',
    accent: '#10B981',
    light: '#34D399'
  },
  warmGold: {
    primary: '#92400E',
    secondary: '#B45309',
    accent: '#F59E0B',
    light: '#FCD34D'
  }
};

// South African language flags and details
const languageDetails = {
  'en': { flag: '🇿🇦', name: 'English', nativeName: 'English' },
  'af': { flag: '🇿🇦', name: 'Afrikaans', nativeName: 'Afrikaans' },
  'zu': { flag: '🇿🇦', name: 'Zulu', nativeName: 'isiZulu' },
  'xh': { flag: '🇿🇦', name: 'Xhosa', nativeName: 'isiXhosa' },
  'st': { flag: '🇿🇦', name: 'Sotho', nativeName: 'Sesotho' },
  'tn': { flag: '🇿🇦', name: 'Tswana', nativeName: 'Setswana' },
  've': { flag: '🇿🇦', name: 'Venda', nativeName: 'Tshivenḓa' },
  'ts': { flag: '🇿🇦', name: 'Tsonga', nativeName: 'Xitsonga' },
  'nr': { flag: '🇿🇦', name: 'Ndebele', nativeName: 'isiNdebele' },
  'ss': { flag: '🇿🇦', name: 'Swati', nativeName: 'siSwati' },
  'nso': { flag: '🇿🇦', name: 'Northern Sotho', nativeName: 'Sepedi' }
};

interface LanguageSelectorProps {
  variant?: 'premium' | 'standard' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  className?: string;
  showLabel?: boolean;
  showIcon?: boolean;
  showTooltip?: boolean;
  position?: 'header' | 'login' | 'settings' | 'landing';
  onLanguageChange?: (language: string) => void;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'premium',
  size = 'medium',
  fullWidth = false,
  className = '',
  showLabel = false,
  showIcon = true,
  showTooltip = true,
  position = 'landing',
  onLanguageChange
}) => {
  const { language, setLanguage, translate } = useLanguage();
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [loading, setLoading] = useState(false);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (langCode: string) => {
    setLoading(true);
    setLanguage(langCode);
    if (onLanguageChange) {
      onLanguageChange(langCode);
    }
    setTimeout(() => {
      setLoading(false);
      handleClose();
    }, 300);
  };

  // Premium variant with glassmorphism
  if (variant === 'premium') {
    const currentLang = languageDetails[language as keyof typeof languageDetails] || languageDetails.en;

    return (
      <Box sx={{ position: 'relative' }}>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={handleClick}
            endIcon={<ExpandMore sx={{
              transform: open ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s ease'
            }} />}
            sx={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: 3,
              color: 'white',
              px: 3,
              py: 1.5,
              minWidth: 140,
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.15)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                transform: 'translateY(-2px)',
                boxShadow: `0 8px 32px ${alpha(premiumColors.warmGold.accent, 0.3)}`
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {showIcon && <Public sx={{ fontSize: 20 }} />}
              <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                {currentLang.flag} {size === 'large' ? currentLang.name : language.toUpperCase()}
              </Typography>
            </Box>
          </Button>
        </motion.div>

        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          disableScrollLock
          PaperProps={{
            elevation: 0,
            sx: {
              background: 'rgba(15, 23, 42, 0.95)',
              backdropFilter: 'blur(30px)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: 3,
              mt: 1,
              minWidth: 200,
              maxHeight: 400,
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: 6,
              },
              '&::-webkit-scrollbar-track': {
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: 3,
              },
              '&::-webkit-scrollbar-thumb': {
                background: premiumColors.warmGold.accent,
                borderRadius: 3,
              },
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <AnimatePresence>
            {Object.entries(languageDetails).map(([code, details], index) => (
              <motion.div
                key={code}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <MenuItem
                  onClick={() => handleLanguageSelect(code)}
                  selected={language === code}
                  sx={{
                    py: 1.5,
                    px: 2,
                    '&:hover': {
                      background: `linear-gradient(90deg, ${alpha(premiumColors.emeraldGreen.accent, 0.1)}, ${alpha(premiumColors.warmGold.accent, 0.1)})`,
                    },
                    '&.Mui-selected': {
                      background: `linear-gradient(90deg, ${alpha(premiumColors.emeraldGreen.accent, 0.2)}, ${alpha(premiumColors.warmGold.accent, 0.2)})`,
                      '&:hover': {
                        background: `linear-gradient(90deg, ${alpha(premiumColors.emeraldGreen.accent, 0.3)}, ${alpha(premiumColors.warmGold.accent, 0.3)})`,
                      }
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Typography sx={{ fontSize: '1.5rem' }}>
                      {details.flag}
                    </Typography>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography sx={{ color: 'white', fontWeight: language === code ? 'bold' : 'normal' }}>
                        {details.name}
                      </Typography>
                    }
                    secondary={
                      <Typography sx={{ color: alpha('#fff', 0.7), fontSize: '0.8rem' }}>
                        {details.nativeName}
                      </Typography>
                    }
                  />
                  {language === code && (
                    <Check sx={{ color: premiumColors.emeraldGreen.accent, ml: 1 }} />
                  )}
                </MenuItem>
              </motion.div>
            ))}
          </AnimatePresence>
        </Menu>

        {loading && (
          <Box
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1400
            }}
          >
            <CircularProgress size={20} sx={{ color: premiumColors.warmGold.accent }} />
          </Box>
        )}
      </Box>
    );
  }

  // Standard variant fallback
  const currentLang = languageDetails[language as keyof typeof languageDetails] || languageDetails.en;

  const selectorComponent = (
    <FormControl
      variant={variant as any}
      size={size}
      fullWidth={fullWidth}
      className={className}
    >
      {showLabel && (
        <InputLabel id="language-select-label">
          {translate('login.language_selection', { fallback: 'Language' })}
        </InputLabel>
      )}
      <Select
        labelId="language-select-label"
        id="language-select"
        value={language}
        onChange={(event: SelectChangeEvent<string>) => handleLanguageSelect(event.target.value)}
        label={showLabel ? translate('login.language_selection', { fallback: 'Language' }) : undefined}
        startAdornment={
          showIcon && (
            <LanguageIcon
              sx={{
                mr: 1,
                color: theme.palette.primary.main,
                opacity: 0.8
              }}
            />
          )
        }
        sx={{
          minWidth: position === 'header' ? 120 : 180,
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center'
          },
          ...(position === 'header' && {
            backgroundColor: alpha(theme.palette.background.paper, 0.9),
            backdropFilter: 'blur(10px)',
            '&:hover': {
              backgroundColor: alpha(theme.palette.background.paper, 0.95)
            }
          })
        }}
      >
        {Object.entries(languageDetails).map(([code, details]) => (
          <MenuItem key={code} value={code}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography sx={{ fontSize: '1.2rem' }}>
                {details.flag}
              </Typography>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: language === code ? 'bold' : 'normal' }}>
                  {details.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {details.nativeName}
                </Typography>
              </Box>
              {language === code && (
                <Check sx={{ color: theme.palette.primary.main, ml: 'auto' }} />
              )}
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );

  // Language indicator chip
  const languageChip = (
    <Chip
      label={`${currentLang.flag} ${language.toUpperCase()}`}
      size="small"
      sx={{
        position: 'absolute',
        top: -8,
        right: -8,
        background: `linear-gradient(45deg, ${premiumColors.emeraldGreen.accent}, ${premiumColors.warmGold.accent})`,
        color: 'white',
        fontWeight: 'bold',
        fontSize: '0.7rem',
        height: 20,
        '& .MuiChip-label': {
          px: 1
        }
      }}
    />
  );

  const componentWithIndicator = (
    <Box sx={{ position: 'relative' }}>
      {selectorComponent}
      {position === 'landing' && languageChip}
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            right: 8,
            transform: 'translateY(-50%)',
          }}
        >
          <CircularProgress size={16} />
        </Box>
      )}
    </Box>
  );

  return showTooltip ? (
    <Tooltip
      title={translate('login.language_selection', { fallback: 'Select Language' })}
      placement="bottom"
      arrow
    >
      {componentWithIndicator}
    </Tooltip>
  ) : componentWithIndicator;
};

export default LanguageSelector;
