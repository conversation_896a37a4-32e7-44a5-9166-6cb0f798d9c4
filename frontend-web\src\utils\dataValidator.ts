/**
 * Data Validation and Sanitization Utility
 * Provides functions for validating and sanitizing data
 */

// Email validation
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
};

// Phone number validation (South African format)
export const isValidSAPhoneNumber = (phone: string): boolean => {
  // Remove spaces, dashes, and parentheses
  const cleanPhone = phone.replace(/[\s\-()]/g, '');
  
  // Check for South African mobile number format
  // Valid formats: +27XXXXXXXXX, 27XXXXXXXXX, 0XXXXXXXXX
  const phoneRegex = /^(\+27|27|0)[6-8][0-9]{8}$/;
  return phoneRegex.test(cleanPhone);
};

// ID number validation (South African format)
export const isValidSAIDNumber = (idNumber: string): boolean => {
  // Remove spaces and dashes
  const cleanId = idNumber.replace(/[\s\-]/g, '');
  
  // Check length and numeric
  if (cleanId.length !== 13 || !/^\d+$/.test(cleanId)) {
    return false;
  }
  
  // Check date validity
  const year = parseInt(cleanId.substring(0, 2));
  const month = parseInt(cleanId.substring(2, 4));
  const day = parseInt(cleanId.substring(4, 6));
  
  // Basic date validation
  if (month < 1 || month > 12 || day < 1 || day > 31) {
    return false;
  }
  
  // Check citizenship
  const citizenship = parseInt(cleanId.substring(10, 11));
  if (citizenship !== 0 && citizenship !== 1) {
    return false;
  }
  
  // Check checksum using Luhn algorithm
  let sum = 0;
  let alternate = false;
  
  for (let i = cleanId.length - 1; i >= 0; i--) {
    let n = parseInt(cleanId.substring(i, i + 1));
    if (alternate) {
      n *= 2;
      if (n > 9) {
        n = (n % 10) + 1;
      }
    }
    sum += n;
    alternate = !alternate;
  }
  
  return (sum % 10 === 0);
};

// URL validation
export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};

// Date validation
export const isValidDate = (date: string): boolean => {
  const d = new Date(date);
  return !isNaN(d.getTime());
};

// Number validation
export const isValidNumber = (value: any): boolean => {
  return !isNaN(parseFloat(value)) && isFinite(value);
};

// Currency validation (ZAR format)
export const isValidZARCurrency = (value: string): boolean => {
  const currencyRegex = /^(R\s?)?(\d{1,3}(,\d{3})*|(\d+))(\.\d{2})?$/;
  return currencyRegex.test(value);
};

// Text sanitization (remove HTML tags and scripts)
export const sanitizeText = (text: string): string => {
  return text
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, ''); // Remove event handlers
};

// HTML sanitization (allow only safe tags)
export const sanitizeHTML = (html: string): string => {
  // Define allowed tags and attributes
  const allowedTags = ['b', 'i', 'u', 'p', 'br', 'ul', 'ol', 'li', 'strong', 'em'];
  const allowedAttrs = ['href', 'title', 'target'];
  
  // Create a temporary element
  const temp = document.createElement('div');
  temp.innerHTML = html;
  
  // Process all nodes recursively
  const sanitizeNode = (node: Node) => {
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      
      // Remove disallowed tags
      if (!allowedTags.includes(tagName)) {
        element.parentNode?.removeChild(element);
        return;
      }
      
      // Remove disallowed attributes
      Array.from(element.attributes).forEach(attr => {
        if (!allowedAttrs.includes(attr.name)) {
          element.removeAttribute(attr.name);
        }
      });
      
      // Sanitize href attributes
      if (element.hasAttribute('href')) {
        const href = element.getAttribute('href') || '';
        if (href.toLowerCase().startsWith('javascript:')) {
          element.removeAttribute('href');
        }
      }
      
      // Process child nodes
      Array.from(element.childNodes).forEach(sanitizeNode);
    }
  };
  
  // Start sanitization
  Array.from(temp.childNodes).forEach(sanitizeNode);
  
  return temp.innerHTML;
};

// Object validation against a schema
interface ValidationSchema {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'date' | 'email' | 'url' | 'phone' | 'id' | 'currency';
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: any[];
    custom?: (value: any) => boolean;
  };
}

interface ValidationResult {
  valid: boolean;
  errors: {
    [key: string]: string;
  };
}

export const validateObject = (obj: any, schema: ValidationSchema): ValidationResult => {
  const result: ValidationResult = {
    valid: true,
    errors: {}
  };
  
  // Check each field in the schema
  for (const [field, rules] of Object.entries(schema)) {
    const value = obj[field];
    
    // Check required fields
    if (rules.required && (value === undefined || value === null || value === '')) {
      result.valid = false;
      result.errors[field] = `${field} is required`;
      continue;
    }
    
    // Skip validation for optional empty fields
    if (!rules.required && (value === undefined || value === null || value === '')) {
      continue;
    }
    
    // Type validation
    switch (rules.type) {
      case 'string':
        if (typeof value !== 'string') {
          result.valid = false;
          result.errors[field] = `${field} must be a string`;
        } else {
          // Check min/max length
          if (rules.min !== undefined && value.length < rules.min) {
            result.valid = false;
            result.errors[field] = `${field} must be at least ${rules.min} characters`;
          }
          if (rules.max !== undefined && value.length > rules.max) {
            result.valid = false;
            result.errors[field] = `${field} must be at most ${rules.max} characters`;
          }
        }
        break;
        
      case 'number':
        if (!isValidNumber(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a number`;
        } else {
          const numValue = parseFloat(value);
          // Check min/max value
          if (rules.min !== undefined && numValue < rules.min) {
            result.valid = false;
            result.errors[field] = `${field} must be at least ${rules.min}`;
          }
          if (rules.max !== undefined && numValue > rules.max) {
            result.valid = false;
            result.errors[field] = `${field} must be at most ${rules.max}`;
          }
        }
        break;
        
      case 'boolean':
        if (typeof value !== 'boolean') {
          result.valid = false;
          result.errors[field] = `${field} must be a boolean`;
        }
        break;
        
      case 'date':
        if (!isValidDate(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid date`;
        }
        break;
        
      case 'email':
        if (!isValidEmail(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid email address`;
        }
        break;
        
      case 'url':
        if (!isValidURL(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid URL`;
        }
        break;
        
      case 'phone':
        if (!isValidSAPhoneNumber(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid South African phone number`;
        }
        break;
        
      case 'id':
        if (!isValidSAIDNumber(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid South African ID number`;
        }
        break;
        
      case 'currency':
        if (!isValidZARCurrency(value)) {
          result.valid = false;
          result.errors[field] = `${field} must be a valid ZAR currency amount`;
        }
        break;
    }
    
    // Pattern validation
    if (rules.pattern && !rules.pattern.test(value)) {
      result.valid = false;
      result.errors[field] = `${field} has an invalid format`;
    }
    
    // Enum validation
    if (rules.enum && !rules.enum.includes(value)) {
      result.valid = false;
      result.errors[field] = `${field} must be one of: ${rules.enum.join(', ')}`;
    }
    
    // Custom validation
    if (rules.custom && !rules.custom(value)) {
      result.valid = false;
      result.errors[field] = `${field} is invalid`;
    }
  }
  
  return result;
};

export default {
  isValidEmail,
  isValidSAPhoneNumber,
  isValidSAIDNumber,
  isValidURL,
  isValidDate,
  isValidNumber,
  isValidZARCurrency,
  sanitizeText,
  sanitizeHTML,
  validateObject
};
