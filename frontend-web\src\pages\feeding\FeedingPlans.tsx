import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar, List, ListItem, ListItemText, ListItemAvatar, Divider, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  LocalShipping,
  Inventory,
  Pets,
  Schedule,
  FilterList,
  Restaurant
 } from '../../utils/iconImports';
import {  Mo<PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { useFeedingData } from '../../hooks/useFeedingData';
import { formatCurrency } from '../../utils/formatters';

interface FeedingPlan {
  id: string;
  name: string;
  animalGroup: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'completed' | 'draft' | 'paused';
  feedItems: {
    feedId: string;
    feedName: string;
    quantity: number;
    unit: string;
    frequency: string;
    timeOfDay: string;
  }[];
  notes: string;
  createdBy: string;
  lastUpdated: string;
}

const FeedingPlans: React.FC = () => {
  const { feedInventory, stats } = useFeedingData();
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<FeedingPlan | null>(null);
  const [feedingPlans, setFeedingPlans] = useState<FeedingPlan[]>([
    {
      id: 'FP001',
      name: 'Cattle Winter Feeding Plan',
      animalGroup: 'Beef Cattle',
      startDate: '2024-05-01',
      endDate: '2024-08-31',
      status: 'active',
      feedItems: [
        {
          feedId: 'FD001',
          feedName: 'Lucerne',
          quantity: 5,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        },
        {
          feedId: 'FD002',
          feedName: 'Maize',
          quantity: 3,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'evening'
        },
        {
          feedId: 'FD003',
          feedName: 'Mineral Lick',
          quantity: 0.1,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        }
      ],
      notes: 'Winter feeding plan for beef cattle to maintain condition during dry season',
      createdBy: 'Johan van der Merwe',
      lastUpdated: '2024-04-15'
    },
    {
      id: 'FP002',
      name: 'Sheep Breeding Program',
      animalGroup: 'Breeding Ewes',
      startDate: '2024-03-15',
      endDate: '2024-06-15',
      status: 'active',
      feedItems: [
        {
          feedId: 'FD001',
          feedName: 'Lucerne',
          quantity: 1.5,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        },
        {
          feedId: 'FD004',
          feedName: 'Eragrostis',
          quantity: 1,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'evening'
        },
        {
          feedId: 'FD005',
          feedName: 'Protein Supplement',
          quantity: 0.2,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        }
      ],
      notes: 'Enhanced nutrition for breeding ewes to improve lambing percentage',
      createdBy: 'Pieter Venter',
      lastUpdated: '2024-03-10'
    },
    {
      id: 'FP003',
      name: 'Goat Maintenance Diet',
      animalGroup: 'Boer Goats',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      feedItems: [
        {
          feedId: 'FD006',
          feedName: 'Teff',
          quantity: 1,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        },
        {
          feedId: 'FD003',
          feedName: 'Mineral Lick',
          quantity: 0.05,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'available'
        }
      ],
      notes: 'Basic maintenance diet for Boer goats with veld grazing',
      createdBy: 'Thabo Molefe',
      lastUpdated: '2023-12-20'
    },
    {
      id: 'FP004',
      name: 'Cattle Fattening Program',
      animalGroup: 'Finishing Cattle',
      startDate: '2024-02-01',
      endDate: '2024-04-30',
      status: 'completed',
      feedItems: [
        {
          feedId: 'FD002',
          feedName: 'Maize',
          quantity: 6,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'split'
        },
        {
          feedId: 'FD001',
          feedName: 'Lucerne',
          quantity: 2,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        },
        {
          feedId: 'FD007',
          feedName: 'Molasses Meal',
          quantity: 1,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'evening'
        }
      ],
      notes: 'High-energy diet for finishing cattle before market',
      createdBy: 'Johan van der Merwe',
      lastUpdated: '2024-01-25'
    },
    {
      id: 'FP005',
      name: 'Lamb Weaning Diet',
      animalGroup: 'Weaned Lambs',
      startDate: '2024-04-01',
      endDate: '2024-05-15',
      status: 'draft',
      feedItems: [
        {
          feedId: 'FD001',
          feedName: 'Lucerne',
          quantity: 0.5,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'morning'
        },
        {
          feedId: 'FD002',
          feedName: 'Maize',
          quantity: 0.3,
          unit: 'kg',
          frequency: 'daily',
          timeOfDay: 'evening'
        }
      ],
      notes: 'Transitional diet for weaned lambs',
      createdBy: 'Pieter Venter',
      lastUpdated: '2024-03-25'
    }
  ]);

  const handleAddNew = () => {
    setSelectedPlan(null);
    setOpenDialog(true);
  };

  const handleEdit = (plan: FeedingPlan) => {
    setSelectedPlan(plan);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setFeedingPlans(feedingPlans.filter(plan => plan.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="success" size="small" />;
      case 'completed':
        return <Chip label="Completed" color="default" size="small" />;
      case 'draft':
        return <Chip label="Draft" color="info" size="small" />;
      case 'paused':
        return <Chip label="Paused" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDailyFeedRequirement = (plan: FeedingPlan) => {
    return plan.feedItems.reduce((total, item) => {
      return total + item.quantity;
    }, 0);
  };

  const calculateDailyCost = (plan: FeedingPlan) => {
    return plan.feedItems.reduce((total, item) => {
      const feedItem = feedInventory.find(feed => feed.id === item.feedId || feed.name === item.feedName);
      if (feedItem) {
        return total + (item.quantity * feedItem.costPerUnit);
      }
      return total;
    }, 0);
  };

  const getActivePlans = () => {
    return feedingPlans.filter(plan => plan.status === 'active');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <ModuleHeader
        title="Feeding Plans"
        subtitle="Create and manage feeding plans for different animal groups"
        module="feed"
        submodule="plans"
        actionLabel="Create Feeding Plan"
        actionIcon={<Add />}
        onAction={handleAddNew}
        icon={<Restaurant />}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >

          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
            Feeding Plan Overview
          </Typography>

          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants} transition={{ delay: 0.1 }}>
                <AnimatedBackgroundCard
                  title="Feeding Plan Status"
                  backgroundImage="https://images.unsplash.com/photo-1605152276897-4f618f831968?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  icon={<Schedule />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  overlay="gradient"
                >
                  <Box mt={2}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                          <Typography variant="h4" fontWeight="bold" color="success.main">
                            {feedingPlans.filter(plan => plan.status === 'active').length}
                          </Typography>
                          <Typography variant="body2">Active</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                          <Typography variant="h4" fontWeight="bold" color="primary.main">
                            {feedingPlans.filter(plan => plan.status === 'draft').length}
                          </Typography>
                          <Typography variant="body2">Draft</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box textAlign="center" p={1.5} bgcolor="rgba(158, 158, 158, 0.1)" borderRadius={1}>
                          <Typography variant="h4" fontWeight="bold" color="text.secondary">
                            {feedingPlans.filter(plan => plan.status === 'completed').length}
                          </Typography>
                          <Typography variant="body2">Completed</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box textAlign="center" p={1.5} bgcolor="rgba(255, 152, 0, 0.1)" borderRadius={1}>
                          <Typography variant="h4" fontWeight="bold" color="warning.main">
                            {feedingPlans.filter(plan => plan.status === 'paused').length}
                          </Typography>
                          <Typography variant="body2">Paused</Typography>
                        </Box>
                      </Grid>
                    </Grid>

                    <Box mt={3}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Daily Feed Requirements
                      </Typography>
                      <Box textAlign="center" p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                        <Typography variant="h4" fontWeight="bold" color="secondary.main">
                          {getActivePlans().reduce((total, plan) => total + calculateDailyFeedRequirement(plan), 0).toFixed(1)} kg
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Total daily feed for active plans
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={8}>
              <motion.div variants={itemVariants} transition={{ delay: 0.2 }}>
                <AnimatedBackgroundCard
                  title="Active Feeding Plans"
                  backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
                  icon={<Pets />}
                  accentColor={theme.palette.secondary.main}
                  secondaryColor={theme.palette.secondary.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                >
                  <List>
                {getActivePlans().map((plan, index) => (
                  <React.Fragment key={plan.id}>
                    <ListItem alignItems="flex-start">
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <Schedule />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="subtitle1" fontWeight="medium">
                              {plan.name}
                            </Typography>
                            {getStatusChip(plan.status)}
                          </Box>
                        }
                        secondary={
                          <React.Fragment>
                            <Box display="flex" alignItems="center" mt={0.5}>
                              <Pets fontSize="small" sx={{ fontSize: 14, mr: 0.5 }} />
                              <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                {plan.animalGroup}
                              </Typography>
                              <CalendarToday fontSize="small" sx={{ fontSize: 14, mr: 0.5, ml: 1 }} />
                              <Typography variant="body2" color="text.secondary">
                                {formatDate(plan.startDate)} - {plan.endDate ? formatDate(plan.endDate) : 'Ongoing'}
                              </Typography>
                            </Box>
                            <Box mt={1}>
                              <Typography variant="body2" color="text.secondary">
                                Feed Items:
                              </Typography>
                              <Box display="flex" flexWrap="wrap" gap={0.5} mt={0.5}>
                                {plan.feedItems.map((item, idx) => (
                                  <Chip
                                    key={idx}
                                    label={`${item.feedName} (${item.quantity} ${item.unit})`}
                                    size="small"
                                    color="primary"
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                            </Box>
                            <Box display="flex" justifyContent="space-between" mt={1}>
                              <Typography variant="body2" color="text.secondary">
                                Daily requirement: {calculateDailyFeedRequirement(plan).toFixed(1)} kg
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Daily cost: {formatCurrency(calculateDailyCost(plan))}
                              </Typography>
                            </Box>
                          </React.Fragment>
                        }
                      />
                    </ListItem>
                    {index < getActivePlans().length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
                {getActivePlans().length === 0 && (
                  <ListItem>
                    <ListItemText primary="No active feeding plans" />
                  </ListItem>
                )}
                  </List>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>
          </Grid>

          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            All Feeding Plans
          </Typography>

          <motion.div variants={itemVariants} transition={{ delay: 0.3 }}>
            <AnimatedBackgroundCard
              title="Feeding Plans List"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<Schedule />}
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              delay={0.3}
              height="auto"
              overlay="gradient"
            >
              <TableContainer component={Paper} sx={{ boxShadow: 'none', mt: 2 }}>
                <Table>
                  <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                    <TableRow>
                      <TableCell><strong>Name</strong></TableCell>
                      <TableCell><strong>Animal Group</strong></TableCell>
                      <TableCell><strong>Date Range</strong></TableCell>
                      <TableCell><strong>Status</strong></TableCell>
                      <TableCell><strong>Feed Items</strong></TableCell>
                      <TableCell><strong>Daily Requirement</strong></TableCell>
                      <TableCell><strong>Daily Cost</strong></TableCell>
                      <TableCell><strong>Actions</strong></TableCell>
                    </TableRow>
                  </TableHead>
              <TableBody>
                {feedingPlans.map((plan) => (
                  <TableRow key={plan.id} hover>
                    <TableCell>{plan.name}</TableCell>
                    <TableCell>{plan.animalGroup}</TableCell>
                    <TableCell>
                      {formatDate(plan.startDate)} - {plan.endDate ? formatDate(plan.endDate) : 'Ongoing'}
                    </TableCell>
                    <TableCell>{getStatusChip(plan.status)}</TableCell>
                    <TableCell>
                      {plan.feedItems.map((item, idx) => (
                        <Tooltip
                          key={idx}
                          title={`${item.quantity} ${item.unit} ${item.frequency}, ${item.timeOfDay}`}
                        >
                          <Chip
                            label={item.feedName}
                            size="small"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        </Tooltip>
                      ))}
                    </TableCell>
                    <TableCell>{calculateDailyFeedRequirement(plan).toFixed(1)} kg</TableCell>
                    <TableCell>{formatCurrency(calculateDailyCost(plan))}</TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(plan)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(plan.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AnimatedBackgroundCard>
        </motion.div>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedPlan ? 'Edit Feeding Plan' : 'Create New Feeding Plan'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="name"
                label="Plan Name"
                defaultValue={selectedPlan?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="animalGroup"
                label="Animal Group"
                defaultValue={selectedPlan?.animalGroup || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="startDate"
                label="Start Date"
                type="date"
                defaultValue={selectedPlan?.startDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="endDate"
                label="End Date (Optional)"
                type="date"
                defaultValue={selectedPlan?.endDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedPlan?.status || 'draft'}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="paused">Paused</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom mt={2}>
                Feed Items
              </Typography>
              {/* Feed items would go here - simplified for this example */}
              <Box p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel id="feed-select-label">Feed</InputLabel>
                      <Select
                        labelId="feed-select-label"
                        id="feed-select"
                        label="Feed"
                        defaultValue=""
                      >
                        {feedInventory.map(feed => (
                          <MenuItem key={feed.id} value={feed.id}>
                            {feed.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      fullWidth
                      label="Quantity"
                      type="number"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <TextField
                      fullWidth
                      label="Unit"
                      defaultValue="kg"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <FormControl fullWidth>
                      <InputLabel id="frequency-select-label">Frequency</InputLabel>
                      <Select
                        labelId="frequency-select-label"
                        id="frequency-select"
                        label="Frequency"
                        defaultValue="daily"
                      >
                        <MenuItem value="daily">Daily</MenuItem>
                        <MenuItem value="weekly">Weekly</MenuItem>
                        <MenuItem value="monthly">Monthly</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel id="time-select-label">Time of Day</InputLabel>
                      <Select
                        labelId="time-select-label"
                        id="time-select"
                        label="Time of Day"
                        defaultValue="morning"
                      >
                        <MenuItem value="morning">Morning</MenuItem>
                        <MenuItem value="evening">Evening</MenuItem>
                        <MenuItem value="split">Split (AM/PM)</MenuItem>
                        <MenuItem value="available">Always Available</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                <CustomButton
                  variant="outlined"
                  startIcon={<Add />}
                  sx={{ mt: 2 }}
                >
                  Add Another Feed Item
                </CustomButton>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedPlan?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedPlan ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
        </motion.div>
      </Box>
    </Box>
  );
};

export default FeedingPlans;
