import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, Divider, Chip, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, TextField, InputAdornment, MenuItem, Select, FormControl, InputLabel, Pagination, useTheme, alpha, Tooltip } from '@mui/material';
import {
  Search as SearchIcon,
  History as HistoryIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  NotificationsActive as NotificationsActiveIcon,
  RecordVoiceOver as VoiceIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { NotificationHistoryEntry } from '../../services/notificationHistoryService';
import { NotificationType, NotificationPriority } from '../../services/notificationService';
import { VirtualizedTable } from '../common';

interface NotificationHistoryProps {
  history: NotificationHistoryEntry[];
  animalId?: string;
  onExport?: () => void;
}

const NotificationHistory: React.FC<NotificationHistoryProps> = ({
  history,
  animalId,
  onExport
}) => {
  const theme = useTheme();
  const [filteredHistory, setFilteredHistory] = useState<NotificationHistoryEntry[]>(history);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<NotificationType | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'success' | 'failed'>('all');
  const [priorityFilter, setPriorityFilter] = useState<NotificationPriority | 'all'>('all');
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // Update filtered history when filters change
  useEffect(() => {
    let filtered = [...history];
    
    // Filter by animal ID if provided
    if (animalId) {
      filtered = filtered.filter(entry => entry.animalId === animalId);
    }
    
    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.animalName.toLowerCase().includes(term) ||
        entry.animalId.toLowerCase().includes(term) ||
        entry.recipient.name.toLowerCase().includes(term) ||
        entry.recipient.contact.toLowerCase().includes(term) ||
        entry.message.toLowerCase().includes(term)
      );
    }
    
    // Filter by notification type
    if (typeFilter !== 'all') {
      filtered = filtered.filter(entry => entry.notificationType === typeFilter);
    }
    
    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(entry => entry.status === statusFilter);
    }
    
    // Filter by priority
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(entry => entry.priority === priorityFilter);
    }
    
    setFilteredHistory(filtered);
    setPage(1); // Reset to first page when filters change
  }, [history, animalId, searchTerm, typeFilter, statusFilter, priorityFilter]);
  
  // Get notification type icon
  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return <PhoneIcon fontSize="small" />;
      case 'voice':
        return <VoiceIcon fontSize="small" />;
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'whatsapp':
        return <WhatsAppIcon fontSize="small" />;
      case 'app':
        return <NotificationsActiveIcon fontSize="small" />;
      default:
        return <NotificationsActiveIcon fontSize="small" />;
    }
  };
  
  // Get notification type color
  const getNotificationTypeColor = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return theme.palette.primary.main;
      case 'voice':
        return theme.palette.error.main;
      case 'email':
        return theme.palette.info.main;
      case 'whatsapp':
        return theme.palette.success.main;
      case 'app':
        return theme.palette.warning.main;
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Get priority color
  const getPriorityColor = (priority: NotificationPriority) => {
    switch (priority) {
      case 'critical':
        return theme.palette.error.main;
      case 'high':
        return theme.palette.warning.main;
      case 'medium':
        return theme.palette.info.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.text.secondary;
    }
  };
  
  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };
  
  // Handle clearing all filters
  const handleClearFilters = () => {
    setSearchTerm('');
    setTypeFilter('all');
    setStatusFilter('all');
    setPriorityFilter('all');
  };
  
  // Calculate pagination
  const paginatedHistory = filteredHistory.slice(
    (page - 1) * rowsPerPage,
    page * rowsPerPage
  );
  
  // Define table columns
  const columns = [
    {
      id: 'timestamp',
      label: 'Date & Time',
      minWidth: 150,
      format: (value: Date) => format(value, 'MMM dd, yyyy HH:mm')
    },
    {
      id: 'animalName',
      label: 'Animal',
      minWidth: 120,
      format: (value: string, row: NotificationHistoryEntry) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">{value}</Typography>
          <Typography variant="caption" color="text.secondary">ID: {row.animalId}</Typography>
        </Box>
      )
    },
    {
      id: 'notificationType',
      label: 'Type',
      minWidth: 100,
      format: (value: NotificationType) => (
        <Chip
          icon={getNotificationTypeIcon(value)}
          label={value.toUpperCase()}
          size="small"
          sx={{
            bgcolor: alpha(getNotificationTypeColor(value), 0.1),
            color: getNotificationTypeColor(value),
            '& .MuiChip-icon': {
              color: getNotificationTypeColor(value)
            }
          }}
        />
      )
    },
    {
      id: 'recipient',
      label: 'Recipient',
      minWidth: 150,
      format: (value: { name: string; contact: string }) => (
        <Box>
          <Typography variant="body2" fontWeight="medium">{value.name}</Typography>
          <Typography variant="caption" color="text.secondary">{value.contact}</Typography>
        </Box>
      )
    },
    {
      id: 'priority',
      label: 'Priority',
      minWidth: 100,
      format: (value: NotificationPriority) => (
        <Chip
          label={value.toUpperCase()}
          size="small"
          sx={{
            bgcolor: alpha(getPriorityColor(value), 0.1),
            color: getPriorityColor(value)
          }}
        />
      )
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 100,
      format: (value: 'success' | 'failed') => (
        <Chip
          icon={value === 'success' ? <CheckCircleIcon /> : <ErrorIcon />}
          label={value === 'success' ? 'Sent' : 'Failed'}
          color={value === 'success' ? 'success' : 'error'}
          size="small"
        />
      )
    },
    {
      id: 'message',
      label: 'Message',
      minWidth: 200,
      format: (value: string) => (
        <Tooltip title={value} arrow>
          <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
            {value}
          </Typography>
        </Tooltip>
      )
    }
  ];
  
  return (
    <Card
      sx={{
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        overflow: 'hidden',
        mb: 4,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
      }}
    >
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <HistoryIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6" fontWeight="bold">
            {animalId ? 'Animal Notification History' : 'Notification History'}
          </Typography>
          <Box flexGrow={1} />
          {onExport && (
            <CustomButton
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={onExport}
              size="small"
              sx={{ mr: 1 }}
            >
              Export
            </CustomButton>
          )}
        </Box>
        
        <Divider sx={{ mb: 3 }} />
        
        {/* Filters */}
        <Box mb={3}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                  endAdornment: searchTerm ? (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchTerm('')}
                        edge="end"
                      >
                        <ClearIcon fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ) : null
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel id="notification-type-filter-label">Type</InputLabel>
                <Select
                  labelId="notification-type-filter-label"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as NotificationType | 'all')}
                  label="Type"
                >
                  <MenuItem value="all">All Types</MenuItem>
                  <MenuItem value="sms">SMS</MenuItem>
                  <MenuItem value="voice">Voice Call</MenuItem>
                  <MenuItem value="email">Email</MenuItem>
                  <MenuItem value="whatsapp">WhatsApp</MenuItem>
                  <MenuItem value="app">In-App</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="status-filter-label">Status</InputLabel>
                <Select
                  labelId="status-filter-label"
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as 'all' | 'success' | 'failed')}
                  label="Status"
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="success">Sent</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="priority-filter-label">Priority</InputLabel>
                <Select
                  labelId="priority-filter-label"
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value as NotificationPriority | 'all')}
                  label="Priority"
                >
                  <MenuItem value="all">All Priorities</MenuItem>
                  <MenuItem value="critical">Critical</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <CustomButton
                fullWidth
                variant="outlined"
                startIcon={<ClearIcon />}
                onClick={handleClearFilters}
                disabled={!searchTerm && typeFilter === 'all' && statusFilter === 'all' && priorityFilter === 'all'}
              >
                Clear Filters
              </CustomButton>
            </Grid>
          </Grid>
        </Box>
        
        {/* Summary */}
        <Box mb={3}>
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  textAlign: 'center'
                }}
              >
                <Typography variant="h5" fontWeight="bold" color="primary.main">
                  {filteredHistory.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Notifications
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  textAlign: 'center'
                }}
              >
                <Typography variant="h5" fontWeight="bold" color="success.main">
                  {filteredHistory.filter(entry => entry.status === 'success').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Sent Successfully
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  textAlign: 'center'
                }}
              >
                <Typography variant="h5" fontWeight="bold" color="error.main">
                  {filteredHistory.filter(entry => entry.status === 'failed').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Failed
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  textAlign: 'center'
                }}
              >
                <Typography variant="h5" fontWeight="bold" color="warning.main">
                  {filteredHistory.filter(entry => entry.priority === 'critical' || entry.priority === 'high').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  High Priority
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
        
        {/* History Table */}
        {filteredHistory.length > 0 ? (
          <Box>
            <VirtualizedTable
              columns={columns}
              data={paginatedHistory}
              uniqueKey="id"
              maxHeight={500}
              virtualizeRows={false}
              showPagination={false}
              stickyHeader
            />
            
            <Box display="flex" justifyContent="center" mt={2}>
              <Pagination
                count={Math.ceil(filteredHistory.length / rowsPerPage)}
                page={page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
              />
            </Box>
          </Box>
        ) : (
          <Box
            sx={{
              p: 4,
              textAlign: 'center',
              bgcolor: 'background.paper',
              borderRadius: 1
            }}
          >
            <Typography color="text.secondary">
              No notification history found.
              {(searchTerm || typeFilter !== 'all' || statusFilter !== 'all' || priorityFilter !== 'all') && (
                <Box component="span" display="block" mt={1}>
                  Try clearing your filters.
                </Box>
              )}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Helper component for the grid layout
const Grid: React.FC<{
  container?: boolean;
  item?: boolean;
  xs?: number;
  sm?: number;
  md?: number;
  spacing?: number;
  children: React.ReactNode;
  alignItems?: string;
}> = ({ container, item, xs, sm, md, spacing, children, alignItems }) => {
  const theme = useTheme();
  
  if (container) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          margin: spacing ? -theme.spacing(spacing / 2) : 0,
          alignItems: alignItems || 'flex-start'
        }}
      >
        {children}
      </Box>
    );
  }
  
  if (item) {
    return (
      <Box
        sx={{
          padding: spacing ? theme.spacing(spacing / 2) : 0,
          flexBasis: {
            xs: xs ? `${(xs / 12) * 100}%` : 'auto',
            sm: sm ? `${(sm / 12) * 100}%` : (xs ? `${(xs / 12) * 100}%` : 'auto'),
            md: md ? `${(md / 12) * 100}%` : (sm ? `${(sm / 12) * 100}%` : (xs ? `${(xs / 12) * 100}%` : 'auto'))
          },
          maxWidth: {
            xs: xs ? `${(xs / 12) * 100}%` : '100%',
            sm: sm ? `${(sm / 12) * 100}%` : (xs ? `${(xs / 12) * 100}%` : '100%'),
            md: md ? `${(md / 12) * 100}%` : (sm ? `${(sm / 12) * 100}%` : (xs ? `${(xs / 12) * 100}%` : '100%'))
          },
          flexGrow: 0
        }}
      >
        {children}
      </Box>
    );
  }
  
  return <Box>{children}</Box>;
};

export default NotificationHistory;
