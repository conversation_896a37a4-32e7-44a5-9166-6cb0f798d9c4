import { generateMockData, randomDate, randomNumber, randomChoice } from '../utils/mockDataGenerator';
import { mockAnimals } from './animalData';

// Define and export the interfaces
export interface BreedingRecord {
  id: string;
  femaleId: string;
  maleId: string;
  date: string;
  type: 'natural' | 'artificial';
  status: 'confirmed' | 'pending' | 'unsuccessful';
  expectedDueDate: string;
  technician: string;
  notes: string;
  followUpDates: FollowUpDate[];
}

export interface FollowUpDate {
  date: string;
  type: string;
  status: 'completed' | 'scheduled' | 'cancelled'; // Align with types/breeding.ts
  notes: string;
}

export interface BirthRecord {
  id: string;
  breedingRecordId: string;
  date: string;
  calfDetails: {
    tagNumber: string;
    gender: 'male' | 'female';
    weight: number;
    health: string;
  };
  complications: string | null;
  assistanceRequired: boolean;
  notes: string;
}

export interface HeatRecord {
  id: string;
  animalId: string;
  date: string;
  intensity: 'high' | 'medium' | 'low';
  notes: string;
}

const breedingMethods = ['Natural', 'AI', 'Embryo Transfer'];
const breedingStatuses = ['Pending', 'Confirmed', 'Failed', 'Completed'];
// South African cattle breeds
const cattleBreeds = ['Nguni', 'Bonsmara', 'Afrikaner', 'Drakensberger', 'Brahman', 'Simbra', 'Beefmaster'];
const sheepBreeds = ['Dorper', 'Merino', 'Damara', 'Meatmaster', 'Dormer', 'Van Rooy'];
const goatBreeds = ['Boer', 'Kalahari Red', 'Savanna', 'Indigenous Veld', 'Angora'];
// South African farm staff
const staffMembers = ['Sipho Ndlovu', 'Thabo Molefe', 'Johan van der Merwe', 'Pieter Venter', 'Dr. van Niekerk', 'Dr. Botha'];
// These variables are defined for future use but not currently used
// const complications = ['None', 'Dystocia', 'Retained Placenta', 'Weak Calf', 'Stillbirth'];
// const calfStatuses = ['Healthy', 'Weak', 'Requires Attention', 'Deceased'];

// Filter female and male animals
const femaleAnimals = mockAnimals.filter(animal => animal.gender === 'Female');
const maleAnimals = mockAnimals.filter(animal => animal.gender === 'Male');

// Generate breeding records with proper format for the application
export const mockBreedingRecords: BreedingRecord[] = generateMockData(4, (i) => {
  const femaleId = femaleAnimals[i % femaleAnimals.length]?.id || `F${randomNumber(1, 50)}`;
  const maleId = maleAnimals[i % maleAnimals.length]?.id || `M${randomNumber(51, 100)}`;
  const breedingDate = randomDate(new Date(2023, 0, 1), new Date(2024, 3, 30));

  // Map status to the expected format in the interface
  const statusMap: Record<string, 'confirmed' | 'pending' | 'unsuccessful'> = {
    'Confirmed': 'confirmed',
    'Pending': 'pending',
    'Failed': 'unsuccessful',
    'Completed': 'confirmed'
  };

  const status = statusMap[randomChoice(breedingStatuses)] || 'pending';

  // Calculate expected due date (approximately 9 months after breeding for cattle)
  const expectedDueDate = new Date(breedingDate);
  expectedDueDate.setDate(expectedDueDate.getDate() + 283); // Average gestation period for cattle

  // Generate follow-up dates
  const followUpCount = randomNumber(1, 4);
  const followUpDates: FollowUpDate[] = [];

  for (let j = 0; j < followUpCount; j++) {
    // Space follow-ups throughout the gestation period
    const followUpDate = new Date(breedingDate);
    followUpDate.setDate(followUpDate.getDate() + (j + 1) * Math.floor(283 / (followUpCount + 1)));

    followUpDates.push({
      date: followUpDate.toISOString().split('T')[0],
      type: randomChoice(['pregnancy check', 'ultrasound', 'pre-birth check', 'vaccination']),
      status: randomChoice(['completed', 'scheduled', 'cancelled']) as 'completed' | 'scheduled' | 'cancelled',
      notes: randomChoice([
        'Pregnancy confirmed',
        'Check fetal development',
        'Final check before expected birth',
        'Routine health check',
        'Vaccination administered',
        'No issues detected'
      ])
    });
  }

  return {
    id: `BR${String(i + 1).padStart(3, '0')}`,
    femaleId,
    maleId,
    date: breedingDate.toISOString().split('T')[0],
    type: randomChoice(['natural', 'artificial']) as 'natural' | 'artificial',
    status,
    expectedDueDate: expectedDueDate.toISOString().split('T')[0],
    technician: randomChoice(staffMembers),
    notes: randomChoice([
      'Successful breeding procedure',
      'AI procedure completed by experienced technician',
      'Pregnancy confirmed via ultrasound',
      'Natural breeding in pasture',
      'Synchronized breeding program',
      'Part of genetic improvement program'
    ]),
    followUpDates
  };
});

export const mockBirthRecords: BirthRecord[] = [
  {
    id: 'BR001',
    breedingRecordId: 'BR001',
    date: '2024-11-08',
    calfDetails: {
      tagNumber: 'NGN-2024-002',
      gender: 'female',
      weight: 35.5,
      health: 'healthy'
    },
    complications: null,
    assistanceRequired: false,
    notes: 'Normal delivery'
  },
  {
    id: 'BR002',
    breedingRecordId: 'BR002',
    date: '2023-11-12',
    calfDetails: {
      tagNumber: 'BNS-2023-045',
      gender: 'male',
      weight: 38.2,
      health: 'healthy'
    },
    complications: null,
    assistanceRequired: false,
    notes: 'Quick and easy delivery'
  },
  {
    id: 'BR003',
    breedingRecordId: 'BR005',
    date: '2023-09-10',
    calfDetails: {
      tagNumber: 'AFR-2023-038',
      gender: 'female',
      weight: 32.7,
      health: 'weak'
    },
    complications: 'Prolonged labor',
    assistanceRequired: true,
    notes: 'Calf required supplemental feeding for first 48 hours'
  }
];

// Generate more comprehensive heat records for calendar display
export const mockHeatRecords: HeatRecord[] = generateMockData(4, (i) => {
  // Get a random female animal
  const animalId = femaleAnimals[i % femaleAnimals.length]?.id || `F${randomNumber(1, 50)}`;

  // Generate dates spanning the last 6 months and next 3 months
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  const threeMonthsAhead = new Date();
  threeMonthsAhead.setMonth(threeMonthsAhead.getMonth() + 3);

  const heatDate = randomDate(sixMonthsAgo, threeMonthsAhead);

  // For realistic cattle heat cycles, add records approximately 21 days apart for some animals
  const intensityOptions: ('high' | 'medium' | 'low')[] = ['high', 'medium', 'low'];

  return {
    id: `HR${String(i + 1).padStart(3, '0')}`,
    animalId,
    date: heatDate.toISOString().split('T')[0],
    intensity: intensityOptions[Math.floor(Math.random() * intensityOptions.length)],
    notes: randomChoice([
      'Clear signs observed - mounting behavior and restlessness',
      'Some signs observed - slight vaginal discharge',
      'Very clear signs, ready for breeding - standing heat observed',
      'Early signs detected, monitor closely over next 24 hours',
      'Clear signs, bred immediately with bull',
      'Regular cycle observed, consistent with previous patterns',
      'Detected via activity monitoring system',
      'Confirmed by farm manager during morning inspection',
      'Slight delay from expected cycle date',
      'First heat observed since calving',
      'Heat detected during routine check',
      'Synchronized heat as part of breeding program'
    ])
  };
});

// Add some cyclical heat records for specific animals to show patterns
const cyclicalAnimals = femaleAnimals.slice(0, 2).map(animal => animal.id);

for (const animalId of cyclicalAnimals) {
  const startDate = new Date(2023, 9, 1); // October 1, 2023
  const cycles = 2; // Create 2 heat cycles

  for (let i = 0; i < cycles; i++) {
    const cycleDate = new Date(startDate);
    cycleDate.setDate(cycleDate.getDate() + (i * 21)); // 21-day cycle for cattle

    mockHeatRecords.push({
      id: `HRC${animalId}-${i}`,
      animalId,
      date: cycleDate.toISOString().split('T')[0],
      intensity: i % 2 === 0 ? 'high' : 'medium',
      notes: `Regular ${i+1}${['st', 'nd', 'rd', 'th', 'th', 'th'][i]} heat cycle observed for this animal`
    });
  }
}

export const mockCalvingRecords = [
  {
    id: 'CR001',
    breedingRecordId: 'BR001',
    date: '2024-11-08',
    calfDetails: {
      tagNumber: 'NGN-2024-002',
      gender: 'female',
      weight: 35.5,
      health: 'healthy'
    },
    complications: null,
    assistanceRequired: false,
    notes: 'Normal delivery'
  },
  {
    id: 'CR002',
    breedingRecordId: 'BR002',
    date: '2023-11-12',
    calfDetails: {
      tagNumber: 'BNS-2023-045',
      gender: 'male',
      weight: 38.2,
      health: 'healthy'
    },
    complications: null,
    assistanceRequired: false,
    notes: 'Quick and easy delivery'
  },
  {
    id: 'CR003',
    breedingRecordId: 'BR005',
    date: '2023-09-10',
    calfDetails: {
      tagNumber: 'AFR-2023-038',
      gender: 'female',
      weight: 32.7,
      health: 'weak'
    },
    complications: 'Prolonged labor',
    assistanceRequired: true,
    notes: 'Calf required supplemental feeding for first 48 hours'
  },
  {
    id: 'CR004',
    breedingRecordId: 'BR005',
    date: '2023-09-10',
    calfDetails: {
      tagNumber: 'AFR-2023-039',
      gender: 'male',
      weight: 34.1,
      health: 'healthy'
    },
    complications: null,
    assistanceRequired: false,
    notes: 'Twin birth, second calf delivered without complications'
  }
];

export const mockBreedingStats = {
  totalBreedings: 24,
  successRate: 75,
  averageGestation: 283,
  monthlyBreedings: [4, 6, 3, 5, 2, 4, 3, 5, 7, 6, 4, 3],
  breedingMethodStats: {
    natural: 16,
    artificial: 8
  },
  successByMethod: {
    natural: 82,
    artificial: 62
  },
  calvingComplications: 15,
  twinRate: 8
};

// Mock breeding performance data for radar chart
export const mockBreedingPerformanceData = [
  { subject: 'Success Rate', A: 85, fullMark: 100 },
  { subject: 'Conception Rate', A: 78, fullMark: 100 },
  { subject: 'Calving Ease', A: 92, fullMark: 100 },
  { subject: 'Gestation Length', A: 88, fullMark: 100 },
  { subject: 'Calf Survival', A: 95, fullMark: 100 },
  { subject: 'Dam Recovery', A: 82, fullMark: 100 },
];
