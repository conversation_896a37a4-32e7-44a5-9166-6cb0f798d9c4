import React, { useState } from 'react';
import { Box, Typography, Card, CardContent, Grid, FormControl, InputLabel, Select, MenuItem, useTheme, alpha, Tooltip, IconButton, Divider } from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  TrendingDown,
  Info,
  Download,
  FilterList,
  Event,
  AttachMoney
} from '../../utils/iconImports';
import {  Mo<PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

// Mock data for financial forecasts
const mockRevenueForecastData = [
  { month: 'Jan', actual: 42500, forecast: 0 },
  { month: 'Feb', actual: 45250, forecast: 0 },
  { month: 'Mar', actual: 48000, forecast: 0 },
  { month: 'Apr', actual: 52750, forecast: 0 },
  { month: 'May', actual: 56500, forecast: 0 },
  { month: 'Jun', actual: 59250, forecast: 0 },
  { month: 'Jul', actual: 0, forecast: 62000 },
  { month: 'Aug', actual: 0, forecast: 65500 },
  { month: 'Sep', actual: 0, forecast: 68750 },
  { month: 'Oct', actual: 0, forecast: 72500 },
  { month: 'Nov', actual: 0, forecast: 75250 },
  { month: 'Dec', actual: 0, forecast: 78000 }
];

const mockExpenseForecastData = [
  { month: 'Jan', actual: 32500, forecast: 0 },
  { month: 'Feb', actual: 34250, forecast: 0 },
  { month: 'Mar', actual: 35000, forecast: 0 },
  { month: 'Apr', actual: 36750, forecast: 0 },
  { month: 'May', actual: 38500, forecast: 0 },
  { month: 'Jun', actual: 39250, forecast: 0 },
  { month: 'Jul', actual: 0, forecast: 41000 },
  { month: 'Aug', actual: 0, forecast: 42500 },
  { month: 'Sep', actual: 0, forecast: 43750 },
  { month: 'Oct', actual: 0, forecast: 45500 },
  { month: 'Nov', actual: 0, forecast: 46250 },
  { month: 'Dec', actual: 0, forecast: 47000 }
];

// Mock data for cash flow forecast
const mockCashFlowForecast = [
  { month: 'Jul', inflow: 62000, outflow: 41000 },
  { month: 'Aug', inflow: 65500, outflow: 42500 },
  { month: 'Sep', inflow: 68750, outflow: 43750 },
  { month: 'Oct', inflow: 72500, outflow: 45500 },
  { month: 'Nov', inflow: 75250, outflow: 46250 },
  { month: 'Dec', inflow: 78000, outflow: 47000 }
];

// Mock data for financial projections
const mockFinancialProjections = [
  { category: 'Revenue', currentYear: 650000, nextYear: 780000, change: '+20%' },
  { category: 'Expenses', currentYear: 450000, nextYear: 520000, change: '+15.5%' },
  { category: 'Profit', currentYear: 200000, nextYear: 260000, change: '+30%' },
  { category: 'ROI', currentYear: 18.5, nextYear: 22.5, change: '+21.6%' },
  { category: 'Cash Reserves', currentYear: 120000, nextYear: 180000, change: '+50%' }
];

const Forecast: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState<string>('6months');
  const [forecastType, setForecastType] = useState<string>('revenue');

  // Filter data based on selected time range
  const getFilteredForecastData = () => {
    const data = forecastType === 'revenue' ? mockRevenueForecastData : mockExpenseForecastData;
    
    if (timeRange === '3months') {
      return data.slice(3, 9);
    } else if (timeRange === '6months') {
      return data.slice(0, 12);
    } else {
      return data;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Financial Forecast"
        subtitle="Project future revenue, expenses, and cash flow based on historical data and market trends"
        module="financial"
        submodule="forecast"
        actionLabel="Export Forecast"
        actionIcon={<Download />}
        onAction={() => console.log('Export forecast data')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Filters */}
          <motion.div variants={itemVariants}>
            <Box sx={{ mb: 4, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Time Range</InputLabel>
                <Select
                  value={timeRange}
                  label="Time Range"
                  onChange={(e) => setTimeRange(e.target.value)}
                >
                  <MenuItem value="3months">3 Months</MenuItem>
                  <MenuItem value="6months">6 Months</MenuItem>
                  <MenuItem value="12months">12 Months</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Forecast Type</InputLabel>
                <Select
                  value={forecastType}
                  label="Forecast Type"
                  onChange={(e) => setForecastType(e.target.value)}
                >
                  <MenuItem value="revenue">Revenue</MenuItem>
                  <MenuItem value="expenses">Expenses</MenuItem>
                </Select>
              </FormControl>
              
              <CustomButton 
                variant="outlined" 
                startIcon={<FilterList />}
                size="small"
              >
                More Filters
              </CustomButton>
            </Box>
          </motion.div>

          {/* Revenue/Expense Forecast Chart */}
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={`${forecastType === 'revenue' ? 'Revenue' : 'Expense'} Forecast`}
              subtitle={`Projected ${forecastType} for the next 6 months based on historical data and trends`}
              module="financial"
              uniqueId="financial-forecast"
              icon={<TrendingUp />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              height={400}
              delay={0.1}
            >
              <Box sx={{ height: 320, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={getFilteredForecastData()}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                    <XAxis dataKey="month" />
                    <YAxis domain={['dataMin - 5000', 'dataMax + 5000']} />
                    <RechartsTooltip 
                      formatter={(value: number) => [`R${value.toLocaleString()}`, '']}
                      labelFormatter={(label) => `Month: ${label}`}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="actual"
                      name="Actual"
                      stroke={theme.palette.primary.main}
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="forecast"
                      name="Forecast"
                      stroke={theme.palette.secondary.main}
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={{ r: 4 }}
                      activeDot={{ r: 8 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>

          {/* Cash Flow and Projections */}
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Cash Flow Forecast */}
            <Grid item xs={12} md={7}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Cash Flow Forecast"
                  subtitle="Projected cash inflows and outflows for the next 6 months"
                  module="financial"
                  uniqueId="cash-flow"
                  icon={<AttachMoney />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={400}
                  delay={0.2}
                >
                  <Box sx={{ height: 320, mt: 2 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={mockCashFlowForecast}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <RechartsTooltip 
                          formatter={(value: number) => [`R${value.toLocaleString()}`, '']}
                          labelFormatter={(label) => `Month: ${label}`}
                        />
                        <Legend />
                        <Area
                          type="monotone"
                          dataKey="inflow"
                          name="Cash Inflow"
                          stroke={theme.palette.success.main}
                          fill={alpha(theme.palette.success.main, 0.2)}
                          activeDot={{ r: 8 }}
                        />
                        <Area
                          type="monotone"
                          dataKey="outflow"
                          name="Cash Outflow"
                          stroke={theme.palette.error.main}
                          fill={alpha(theme.palette.error.main, 0.2)}
                          activeDot={{ r: 8 }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            {/* Financial Projections */}
            <Grid item xs={12} md={5}>
              <motion.div variants={itemVariants}>
                <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" fontWeight="bold">
                        Financial Projections
                      </Typography>
                      <Tooltip title="Projections based on current trends and market conditions">
                        <IconButton size="small">
                          <Info fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="text.secondary">
                        Year-over-year financial projections for the next fiscal year
                      </Typography>
                    </Box>
                    
                    <Divider sx={{ mb: 2 }} />
                    
                    {mockFinancialProjections.map((item, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="subtitle2" fontWeight="medium">
                            {item.category}
                          </Typography>
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            color: item.change.startsWith('+') ? 'success.main' : 'error.main' 
                          }}>
                            {item.change.startsWith('+') ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                            {item.change}
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">
                            Current: {item.category === 'ROI' ? `${item.currentYear}%` : `R${item.currentYear.toLocaleString()}`}
                          </Typography>
                          <Typography variant="body2" fontWeight="medium">
                            Next Year: {item.category === 'ROI' ? `${item.nextYear}%` : `R${item.nextYear.toLocaleString()}`}
                          </Typography>
                        </Box>
                        <Divider sx={{ mt: 1.5, mb: 1.5 }} />
                      </Box>
                    ))}
                    
                    <CustomButton 
                      variant="outlined" 
                      fullWidth 
                      startIcon={<Event />}
                      sx={{ mt: 2 }}
                    >
                      View Detailed Projections
                    </CustomButton>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>

          {/* Assumptions and Notes */}
          <motion.div variants={itemVariants} style={{ marginTop: '24px' }}>
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Forecast Assumptions
                </Typography>
                <Typography variant="body2" paragraph>
                  These forecasts are based on the following assumptions:
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
                      Revenue Assumptions
                    </Typography>
                    <ul style={{ paddingLeft: '20px', margin: 0 }}>
                      <li>
                        <Typography variant="body2">
                          Livestock sales growth of 15% year-over-year
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          Seasonal variations in market prices accounted for
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          New market expansion in Q3 with 8% additional revenue
                        </Typography>
                      </li>
                    </ul>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
                      Expense Assumptions
                    </Typography>
                    <ul style={{ paddingLeft: '20px', margin: 0 }}>
                      <li>
                        <Typography variant="body2">
                          Feed costs increasing by 5% due to market conditions
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          Operational efficiency improvements reducing labor costs by 3%
                        </Typography>
                      </li>
                      <li>
                        <Typography variant="body2">
                          Capital expenditures planned for Q4 (R120,000)
                        </Typography>
                      </li>
                    </ul>
                  </Grid>
                </Grid>
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Note: These forecasts are subject to change based on market conditions, weather patterns, and other external factors.
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </Box>
    </Box>
  );
};

export default Forecast;
