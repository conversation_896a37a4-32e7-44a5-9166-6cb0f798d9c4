import React, { useState, useEffect } from 'react';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction } from '@mui/material';
import {
  Add,
  VerifiedUser,
  Warning,
  ErrorOutline,
  CalendarToday,
  Business,
  Description,
  Download,
  Edit,
  Delete,
  Visibility,
  FileCopy
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';

// Mock data for certifications
const mockCertifications = [
  {
    id: 'CERT001',
    name: 'Livestock Identification Certificate',
    issuingBody: 'Department of Agriculture, Land Reform and Rural Development',
    issueDate: '2023-05-15',
    expiryDate: '2028-05-15',
    status: 'valid',
    requirements: ['Annual inspection', 'Proper record keeping', 'Compliance with animal identification regulations'],
    documentUrl: 'https://example.com/cert001.pdf',
    notes: 'Primary farm certification for livestock identification and traceability'
  },
  {
    id: 'CERT002',
    name: 'Animal Health Certificate',
    issuingBody: 'South African Veterinary Association',
    issueDate: '2023-08-10',
    expiryDate: '2024-08-10',
    status: 'valid',
    requirements: ['Regular veterinary inspections', 'Disease prevention protocols', 'Vaccination records'],
    documentUrl: 'https://example.com/cert002.pdf',
    notes: 'Certifies compliance with animal health standards'
  },
  {
    id: 'CERT003',
    name: 'Organic Farming Certification',
    issuingBody: 'South African Organic Sector Organisation',
    issueDate: '2022-11-20',
    expiryDate: '2023-11-20',
    status: 'expired',
    requirements: ['No synthetic pesticides', 'No GMO feed', 'Sustainable farming practices'],
    documentUrl: 'https://example.com/cert003.pdf',
    notes: 'Certification for organic livestock production methods'
  },
  {
    id: 'CERT004',
    name: 'Export Compliance Certificate',
    issuingBody: 'Department of Trade and Industry',
    issueDate: '2023-09-05',
    expiryDate: '2024-09-05',
    status: 'valid',
    requirements: ['Compliance with export regulations', 'Product quality standards', 'Proper documentation'],
    documentUrl: 'https://example.com/cert004.pdf',
    notes: 'Required for exporting livestock products'
  },
  {
    id: 'CERT005',
    name: 'Food Safety Certification',
    issuingBody: 'National Regulator for Compulsory Specifications',
    issueDate: '2023-06-30',
    expiryDate: '2024-06-30',
    status: 'valid',
    requirements: ['HACCP implementation', 'Regular testing', 'Staff training'],
    documentUrl: 'https://example.com/cert005.pdf',
    notes: 'Certifies compliance with food safety standards'
  },
  {
    id: 'CERT006',
    name: 'Environmental Compliance Certificate',
    issuingBody: 'Department of Environmental Affairs',
    issueDate: '2023-04-12',
    expiryDate: '2025-04-12',
    status: 'valid',
    requirements: ['Waste management plan', 'Water usage compliance', 'Emissions control'],
    documentUrl: 'https://example.com/cert006.pdf',
    notes: 'Certifies compliance with environmental regulations'
  },
  {
    id: 'CERT007',
    name: 'Animal Welfare Certification',
    issuingBody: 'Animal Welfare Association of South Africa',
    issueDate: '2023-02-18',
    expiryDate: '2024-02-18',
    status: 'expiring',
    requirements: ['Proper housing', 'Humane handling', 'Adequate nutrition'],
    documentUrl: 'https://example.com/cert007.pdf',
    notes: 'Certifies compliance with animal welfare standards'
  },
  {
    id: 'CERT008',
    name: 'Quality Management System Certificate',
    issuingBody: 'South African Bureau of Standards',
    issueDate: '2022-10-05',
    expiryDate: '2023-10-05',
    status: 'expired',
    requirements: ['Documented procedures', 'Internal audits', 'Management reviews'],
    documentUrl: 'https://example.com/cert008.pdf',
    notes: 'ISO 9001 certification for quality management'
  }
];

// Mock data for certification statistics
const certificationStats = {
  total: 8,
  valid: 5,
  expiring: 1,
  expired: 2,
  upcomingRenewals: 1
};

// Interface for certification
interface Certification {
  id: string;
  name: string;
  issuingBody: string;
  issueDate: string;
  expiryDate: string;
  status: 'valid' | 'expiring' | 'expired' | 'pending';
  requirements: string[];
  documentUrl?: string;
  notes?: string;
}

const Certifications: React.FC<{}> = () => {
  const theme = useTheme();
  const [certifications, setCertifications] = useState<Certification[]>(mockCertifications as unknown as Certification[]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCertification, setSelectedCertification] = useState<Certification | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    name: '',
    issuingBody: '',
    issueDate: dayjs(),
    expiryDate: dayjs().add(1, 'year'),
    status: 'valid',
    requirements: [''],
    notes: ''
  });

  // Handle dialog open
  const handleOpenDialog = (certification?: Certification) => {
    if (certification) {
      setSelectedCertification(certification);
      setFormData({
        id: certification.id,
        name: certification.name,
        issuingBody: certification.issuingBody,
        issueDate: dayjs(certification.issueDate),
        expiryDate: dayjs(certification.expiryDate),
        status: certification.status,
        requirements: certification.requirements,
        notes: certification.notes || ''
      });
    } else {
      setSelectedCertification(null);
      setFormData({
        id: `CERT${String(certifications.length + 1).padStart(3, '0')}`,
        name: '',
        issuingBody: '',
        issueDate: dayjs(),
        expiryDate: dayjs().add(1, 'year'),
        status: 'valid',
        requirements: [''],
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedCertification(null);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle date change
  const handleDateChange = (name: string, date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData({
        ...formData,
        [name]: date
      });
    }
  };

  // Handle status change
  const handleStatusChange = (e: any) => {
    setFormData({
      ...formData,
      status: e.target.value
    });
  };

  // Handle requirements change
  const handleRequirementChange = (index: number, value: string) => {
    const updatedRequirements = [...formData.requirements];
    updatedRequirements[index] = value;
    setFormData({
      ...formData,
      requirements: updatedRequirements
    });
  };

  // Add requirement field
  const addRequirement = () => {
    setFormData({
      ...formData,
      requirements: [...formData.requirements, '']
    });
  };

  // Remove requirement field
  const removeRequirement = (index: number) => {
    const updatedRequirements = [...formData.requirements];
    updatedRequirements.splice(index, 1);
    setFormData({
      ...formData,
      requirements: updatedRequirements
    });
  };

  // Handle save
  const handleSave = () => {
    const newCertification: Certification = {
      id: formData.id,
      name: formData.name,
      issuingBody: formData.issuingBody,
      issueDate: formData.issueDate.format('YYYY-MM-DD'),
      expiryDate: formData.expiryDate.format('YYYY-MM-DD'),
      status: formData.status as 'valid' | 'expiring' | 'expired' | 'pending',
      requirements: formData.requirements.filter(req => req.trim() !== ''),
      notes: formData.notes
    };

    if (selectedCertification) {
      // Update existing certification
      setCertifications(certifications.map(cert =>
        cert.id === selectedCertification.id ? newCertification : cert
      ));
    } else {
      // Add new certification
      setCertifications([...certifications, newCertification]);
    }

    handleCloseDialog();
  };

  // Handle delete
  const handleDelete = (id: string) => {
    setCertifications(certifications.filter(cert => cert.id !== id));
  };

  // Filter certifications by status
  const filteredCertifications = filterStatus === 'all'
    ? certifications
    : certifications.filter(cert => cert.status === filterStatus);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'success';
      case 'expiring':
        return 'warning';
      case 'expired':
        return 'error';
      case 'pending':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <VerifiedUser color="success" />;
      case 'expiring':
        return <Warning color="warning" />;
      case 'expired':
        return <ErrorOutline color="error" />;
      case 'pending':
        return <CalendarToday color="info" />;
      default:
        return null;
    }
  };

  // Table columns
  const columns = [
    {
      id: 'id',
      label: 'ID',
      minWidth: 80
    },
    {
      id: 'name',
      label: 'Certificate Name',
      minWidth: 200
    },
    {
      id: 'issuingBody',
      label: 'Issuing Body',
      minWidth: 200
    },
    {
      id: 'issueDate',
      label: 'Issue Date',
      minWidth: 120
    },
    {
      id: 'expiryDate',
      label: 'Expiry Date',
      minWidth: 120
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => {
        // Create a simple formatted string instead of a component
        return value.charAt(0).toUpperCase() + value.slice(1);
      }
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box>
      <ModuleHeader
        title="Certifications"
        subtitle="Manage and track your compliance certifications"
        module="compliance"
        submodule="certifications"
        actionLabel="Add Certification"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Certifications"
                  subtitle={certificationStats.total.toString()}
                  backgroundImage="https://th.bing.com/th/id/OIP.TM1iOYX0L32k_18oig5WJwHaEC?rs=1&pid=ImgDetMain"
                  icon={<Description />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height={160}
                  overlay="gradient"
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Valid Certifications"
                  subtitle={certificationStats.valid.toString()}
                  backgroundImage="https://th.bing.com/th/id/R.********************************?rik=WINm%2fw4uIdi%2fQQ&pid=ImgRaw&r=0"
                  icon={<VerifiedUser />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  delay={0.2}
                  height={160}
                  overlay="gradient"
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Expiring Soon"
                  subtitle={certificationStats.expiring.toString()}
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<Warning />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  delay={0.3}
                  height={160}
                  overlay="gradient"
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Expired"
                  subtitle={certificationStats.expired.toString()}
                  backgroundImage="https://th.bing.com/th/id/OIP.TM1iOYX0L32k_18oig5WJwHaEC?rs=1&pid=ImgDetMain"
                  icon={<ErrorOutline />}
                  accentColor={theme.palette.error.main}
                  secondaryColor={theme.palette.error.dark}
                  delay={0.4}
                  height={160}
                  overlay="gradient"
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and view toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Status Filter</InputLabel>
            <Select
              value={filterStatus}
              label="Status Filter"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="all">All Certifications</MenuItem>
              <MenuItem value="valid">Valid</MenuItem>
              <MenuItem value="expiring">Expiring Soon</MenuItem>
              <MenuItem value="expired">Expired</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
            </Select>
          </FormControl>
          <Box>
            <CustomButton
              variant={viewMode === 'grid' ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setViewMode('grid')}
              sx={{ mr: 1 }}
            >
              Grid View
            </CustomButton>
            <CustomButton
              variant={viewMode === 'table' ? 'contained' : 'outlined'}
              size="small"
              onClick={() => setViewMode('table')}
            >
              Table View
            </CustomButton>
          </Box>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={3}>
              {filteredCertifications.map((certification, index) => (
                <Grid item xs={12} md={6} lg={4} key={certification.id}>
                  <motion.div variants={itemVariants}>
                    <Card sx={{ height: '100%', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main, mr: 1 }}>
                              <Description />
                            </Avatar>
                            <Typography variant="h6" fontWeight="bold">
                              {certification.name}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              bgcolor: getStatusColor(certification.status) === 'success' ? 'success.light' :
                                      getStatusColor(certification.status) === 'warning' ? 'warning.light' :
                                      getStatusColor(certification.status) === 'error' ? 'error.light' : 'primary.light',
                              color: '#fff',
                              px: 1,
                              py: 0.5,
                              borderRadius: 1,
                              fontSize: '0.75rem'
                            }}
                          >
                            {getStatusIcon(certification.status) &&
                              React.cloneElement(getStatusIcon(certification.status) as React.ReactElement,
                                { fontSize: 'small', style: { marginRight: 4 } }
                              )
                            }
                            {certification.status.charAt(0).toUpperCase() + certification.status.slice(1)}
                          </Box>
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Business fontSize="small" sx={{ mr: 1 }} />
                            {certification.issuingBody}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarToday fontSize="small" sx={{ mr: 1 }} />
                            Issue Date: {certification.issueDate}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarToday fontSize="small" sx={{ mr: 1 }} />
                            Expiry Date: {certification.expiryDate}
                          </Typography>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="subtitle2" gutterBottom>
                          Requirements:
                        </Typography>
                        <List dense disablePadding>
                          {certification.requirements.map((req, idx) => (
                            <ListItem key={idx} disablePadding sx={{ py: 0.5 }}>
                              <ListItemText
                                primary={req}
                                primaryTypographyProps={{ variant: 'body2' }}
                              />
                            </ListItem>
                          ))}
                        </List>

                        <Divider sx={{ my: 2 }} />

                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          <IconButton size="small" sx={{ mr: 1 }} onClick={() => console.log('View', certification.id)}>
                            <Visibility fontSize="small" />
                          </IconButton>
                          <IconButton size="small" sx={{ mr: 1 }} onClick={() => handleOpenDialog(certification)}>
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error" onClick={() => handleDelete(certification.id)}>
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EnhancedDataTable
              title="Certifications"
              subtitle="Manage your compliance certifications"
              columns={columns}
              data={filteredCertifications}
              keyField="id"
              onView={(row: any) => console.log('View', row.id)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['name', 'issuingBody', 'id']}
              emptyMessage="No certifications found"
              accentColor={theme.palette.primary.main}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedCertification ? 'Edit Certification' : 'Add New Certification'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Certificate ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled={!!selectedCertification}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Certificate Name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Issuing Body"
                name="issuingBody"
                value={formData.issuingBody}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Issue Date"
                value={formData.issueDate}
                onChange={(date) => handleDateChange('issueDate', date)}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Expiry Date"
                value={formData.expiryDate}
                onChange={(date) => handleDateChange('expiryDate', date)}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={handleStatusChange}
                >
                  <MenuItem value="valid">Valid</MenuItem>
                  <MenuItem value="expiring">Expiring Soon</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Requirements
              </Typography>
              {formData.requirements.map((req, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 2 }}>
                  <TextField
                    label={`Requirement ${index + 1}`}
                    value={req}
                    onChange={(e) => handleRequirementChange(index, e.target.value)}
                    fullWidth
                    margin="dense"
                  />
                  <IconButton
                    color="error"
                    onClick={() => removeRequirement(index)}
                    disabled={formData.requirements.length <= 1}
                    sx={{ ml: 1 }}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={addRequirement}
                variant="outlined"
                size="small"
              >
                Add Requirement
              </CustomButton>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSave}
            variant="contained"
            color="primary"
            disabled={!formData.name || !formData.issuingBody}
          >
            {selectedCertification ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Certifications;
