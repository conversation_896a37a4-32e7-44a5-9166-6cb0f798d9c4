import React, { ReactNode } from 'react';
import { Box, Paper, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import GlobalBackground from './GlobalBackground';
import { getDashboardContentContainerStyle } from '../../utils/dashboardStyles';

interface UnifiedModuleContainerProps {
  module?: string;
  accentColor?: string;
  secondaryColor?: string;
  backgroundPattern?: 'gradient' | 'radial' | 'mesh' | 'dots' | 'waves';
  animated?: boolean;
  children: ReactNode;
}

/**
 * UnifiedModuleContainer - A consistent container for all module content
 * This component provides a unified layout and styling for all modules and submodules
 */
const UnifiedModuleContainer: React.FC<UnifiedModuleContainerProps> = ({
  module,
  accentColor,
  secondaryColor,
  backgroundPattern = 'radial',
  animated = true,
  children
}) => {
  const theme = useTheme();
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  return (
    <GlobalBackground
      module={module}
      accentColor={accentColor}
      secondaryColor={secondaryColor}
      pattern={backgroundPattern}
      animated={animated}
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{ height: '100%' }}
      >
        <Box
          sx={{
            ...getDashboardContentContainerStyle(theme),
            height: '100%'
          }}
        >
          {children}
        </Box>
      </motion.div>
    </GlobalBackground>
  );
};

export default UnifiedModuleContainer;
