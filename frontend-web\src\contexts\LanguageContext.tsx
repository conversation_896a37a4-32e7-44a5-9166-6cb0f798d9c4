import React, { createContext, useState, useContext, useEffect } from 'react';

// Define the available languages
export const LANGUAGES = {
  ENGLISH: 'en',
  AFRIKAANS: 'af',
  ZULU: 'zu',
  XHOSA: 'xh',
  SOTHO: 'st',
  TSWANA: 'tn',
  VENDA: 've',
  TSONGA: 'ts',
  NDEBELE: 'nr',
  SWATI: 'ss',
  SEPEDI: 'nso',
  HINDI: 'hi',
  PORTUGUESE: 'pt'
};

// Define the language names for display
export const LANGUAGE_NAMES = {
  [LANGUAGES.ENGLISH]: 'English',
  [LANGUAGES.AFRIKAANS]: 'Afrikaans',
  [LANGUAGES.ZULU]: 'isiZulu',
  [LANGUAGES.XHOSA]: 'isiXhosa',
  [LANGUAGES.SOTHO]: 'Sesotho',
  [LANGUAGES.TSWANA]: 'Setswana',
  [LANGUAGES.VENDA]: 'Tshivenda',
  [LANGUAGES.TSONGA]: 'Xi<PERSON>onga',
  [LANGUAGES.NDEBELE]: 'isiNdebele',
  [LANGUAGES.SWATI]: 'siSwati',
  [LANGUAGES.SEPEDI]: 'Sepedi',
  [LANGUAGES.HINDI]: 'हिन्दी',
  [LANGUAGES.PORTUGUESE]: 'Português'
};

// Define the context type
interface LanguageContextType {
  language: string;
  setLanguage: (language: string) => void;
  translate: (key: string, params?: Record<string, string | number>) => string;
  languageNames: typeof LANGUAGE_NAMES;
  isLoading: boolean;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | null>(null);

// Create a hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Create the language provider component
export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Get the language from localStorage or use English as default
  const [language, setLanguageState] = useState<string>(
    localStorage.getItem('language') || LANGUAGES.ENGLISH
  );

  // Store translations
  const [translations, setTranslations] = useState<Record<string, Record<string, string>>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Update localStorage and document language when language changes
  const setLanguage = (newLanguage: string) => {
    localStorage.setItem('language', newLanguage);
    setLanguageState(newLanguage);

    // Update the document language attribute
    document.documentElement.lang = newLanguage;

    // Update the document direction for RTL languages if needed
    // Currently none of our languages are RTL, but this is here for future support
    const rtlLanguages: string[] = [];
    document.documentElement.dir = rtlLanguages.includes(newLanguage) ? 'rtl' : 'ltr';
  };

  // Set initial document language
  useEffect(() => {
    document.documentElement.lang = language;
    document.documentElement.dir = 'ltr'; // All supported languages are LTR
  }, []);

  // Load translations for the current language
  useEffect(() => {
    const loadTranslations = async () => {
      try {
        setIsLoading(true);
        console.log(`Loading translations for language: ${language}`);
        // Import the translation file dynamically
        const translationModule = await import(`../translations/${language}.json`);
        console.log(`Successfully loaded translations for ${language}`);
        setTranslations(prev => ({
          ...prev,
          [language]: translationModule.default
        }));
      } catch (error) {
        console.error(`Failed to load translations for ${language}:`, error);
        // Fallback to English if translation file is not found
        if (language !== LANGUAGES.ENGLISH) {
          try {
            console.log(`Falling back to English translations for ${language}`);
            const englishModule = await import(`../translations/en.json`);
            setTranslations(prev => ({
              ...prev,
              [language]: englishModule.default
            }));
          } catch (err) {
            console.error('Failed to load English translations:', err);
          }
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (!translations[language]) {
      console.log(`No translations found for ${language}, loading now...`);
      loadTranslations();
    } else {
      console.log(`Using cached translations for ${language}`);
    }
  }, [language, translations]);

  // Translation function
  const translate = (key: string, params?: Record<string, string | number> & { fallback?: string }): string => {
    if (isLoading || !translations[language]) {
      console.log(`Translation not available yet for ${language}, key: ${key}, using fallback`);
      return params?.fallback || key; // Return fallback or key if translations are not loaded yet
    }

    // Get the translation or fallback to the provided fallback or the key itself
    let text = translations[language][key];

    if (!text) {
      console.log(`No translation found for key: ${key} in language: ${language}`);
      // If translation doesn't exist, try English as fallback
      if (language !== LANGUAGES.ENGLISH && translations[LANGUAGES.ENGLISH]) {
        text = translations[LANGUAGES.ENGLISH][key];
        if (text) {
          console.log(`Found English fallback for key: ${key}`);
        }
      }

      // If still no translation, use fallback or key
      if (!text) {
        console.log(`No translation found in English either for key: ${key}, using fallback`);
        text = params?.fallback || key;
      }
    } else {
      console.log(`Found translation for key: ${key} in language: ${language}`);
    }

    // Replace parameters in the text if provided
    if (params) {
      Object.entries(params).forEach(([paramKey, paramValue]) => {
        if (paramKey !== 'fallback') { // Skip the fallback parameter
          text = text.replace(`{{${paramKey}}}`, String(paramValue));
        }
      });
    }

    return text;
  };

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      translate,
      languageNames: LANGUAGE_NAMES,
      isLoading
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
