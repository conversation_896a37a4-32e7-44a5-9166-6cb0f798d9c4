/**
 * <PERSON><PERSON>t to test API endpoints
 *
 * This script tests the API endpoints
 * Run with: node src/scripts/test-api.js
 */

const axios = require('axios');

// API URL
const API_URL = 'http://localhost:3001/api';

// Test login
async function testLogin() {
  try {
    console.log('Testing login endpoint...');
    const response = await axios.post(`${API_URL}/auth/login`, {
      username: 'AM<PERSON>',
      password: '123'
    });
    console.log('Login successful!');
    console.log('Response:', response.data);
    return response.data.token;
  } catch (error) {
    console.error('Login failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Test get users
async function testGetUsers(token) {
  try {
    console.log('\nTesting get users endpoint...');
    const response = await axios.get(`${API_URL}/users`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('Get users successful!');
    console.log(`Found ${response.data.length} users`);
    return response.data;
  } catch (error) {
    console.error('Get users failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Test create user
async function testCreateUser(token) {
  try {
    console.log('\nTesting create user endpoint...');
    const response = await axios.post(
      `${API_URL}/users`,
      {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Test@123',
        firstName: 'Test',
        lastName: 'User',
        role: 'viewer',
        status: 'active',
        permissions: ['animals:read']
      },
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    console.log('Create user successful!');
    console.log('Response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Create user failed:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
    return null;
  }
}

// Run tests
async function runTests() {
  // Test login
  const token = await testLogin();
  if (!token) {
    console.error('Login failed, cannot continue with other tests');
    return;
  }

  // Test get users
  const users = await testGetUsers(token);
  
  // Test create user
  if (users) {
    await testCreateUser(token);
  }
}

// Run the tests
runTests()
  .then(() => {
    console.log('\nAPI tests completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\nAPI tests failed:', error);
    process.exit(1);
  });
