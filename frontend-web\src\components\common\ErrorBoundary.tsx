import React, { Component, ErrorInfo, ReactNode } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, useTheme, alpha } from '@mui/material';
import RefreshIcon from '@mui/icons-material/Refresh';
import BugReportIcon from '@mui/icons-material/BugReport';
import HomeIcon from '@mui/icons-material/Home';
import { motion } from 'framer-motion';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * ErrorBoundary component
 * Catches JavaScript errors anywhere in the child component tree
 * and displays a fallback UI instead of crashing the whole app
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // You can also log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleReset = (): void => {
    // Reset the error boundary state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });

    // Call the onReset prop if provided
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback 
        error={this.state.error} 
        errorInfo={this.state.errorInfo}
        onReset={this.handleReset}
      />;
    }

    return this.props.children;
  }
}

interface ErrorFallbackProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  onReset: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, errorInfo, onReset }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '50vh',
          p: 4,
          textAlign: 'center'
        }}
      >
        <Paper
          elevation={0}
          sx={{
            p: 4,
            maxWidth: 600,
            borderRadius: 2,
            bgcolor: (theme) => alpha(theme.palette.error.light, 0.1),
            border: (theme) => `1px solid ${alpha(theme.palette.error.main, 0.2)}`
          }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 3
            }}
          >
            <BugReportIcon 
              color="error" 
              sx={{ 
                fontSize: 40,
                mr: 1
              }} 
            />
            <Typography 
              variant="h5" 
              component="h2" 
              color="error.main"
              sx={{ fontWeight: 600 }}
            >
              Something went wrong
            </Typography>
          </Box>

          <Typography 
            variant="body1" 
            color="text.secondary" 
            paragraph
            sx={{ mb: 3 }}
          >
            {error?.message || 'An unexpected error occurred in this component.'}
          </Typography>

          {errorInfo && (
            <Box
              sx={{
                mt: 2,
                mb: 3,
                p: 2,
                borderRadius: 1,
                bgcolor: (theme) => alpha(theme.palette.background.default, 0.5),
                border: (theme) => `1px solid ${theme.palette.divider}`,
                overflow: 'auto',
                maxHeight: 200,
                textAlign: 'left'
              }}
            >
              <Typography 
                variant="caption" 
                component="pre" 
                sx={{ 
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  color: 'text.secondary',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word'
                }}
              >
                {errorInfo.componentStack}
              </Typography>
            </Box>
          )}

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
              mt: 2
            }}
          >
            <CustomButton
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={onReset}
            >
              Try Again
            </CustomButton>
            <CustomButton
              variant="contained"
              color="primary"
              startIcon={<HomeIcon />}
              onClick={() => window.location.href = '/'}
            >
              Go to Dashboard
            </CustomButton>
          </Box>
        </Paper>
      </Box>
    </motion.div>
  );
};

export default ErrorBoundary;
