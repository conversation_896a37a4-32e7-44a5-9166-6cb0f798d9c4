import { renderHook, act } from '@testing-library/react-hooks';
import { useAccessibility } from './useAccessibility';

describe('useAccessibility Hook', () => {
  test('handleKeyboardNavigation calls the correct callback for Enter key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onEnterMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'Enter', preventDefault: jest.fn() } as any,
        { onEnter: onEnterMock }
      );
    });
    
    expect(onEnterMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for Space key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onSpaceMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: ' ', preventDefault: jest.fn() } as any,
        { onSpace: onSpaceMock }
      );
    });
    
    expect(onSpaceMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for ArrowUp key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onArrowUpMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'ArrowUp', preventDefault: jest.fn() } as any,
        { onArrowUp: onArrowUpMock }
      );
    });
    
    expect(onArrowUpMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for ArrowDown key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onArrowDownMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'ArrowDown', preventDefault: jest.fn() } as any,
        { onArrowDown: onArrowDownMock }
      );
    });
    
    expect(onArrowDownMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for ArrowLeft key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onArrowLeftMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'ArrowLeft', preventDefault: jest.fn() } as any,
        { onArrowLeft: onArrowLeftMock }
      );
    });
    
    expect(onArrowLeftMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for ArrowRight key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onArrowRightMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'ArrowRight', preventDefault: jest.fn() } as any,
        { onArrowRight: onArrowRightMock }
      );
    });
    
    expect(onArrowRightMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for Escape key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onEscapeMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'Escape', preventDefault: jest.fn() } as any,
        { onEscape: onEscapeMock }
      );
    });
    
    expect(onEscapeMock).toHaveBeenCalledTimes(1);
  });

  test('handleKeyboardNavigation calls the correct callback for Tab key', () => {
    const { result } = renderHook(() => useAccessibility());
    const onTabMock = jest.fn();
    
    act(() => {
      result.current.handleKeyboardNavigation(
        { key: 'Tab', preventDefault: jest.fn() } as any,
        { onTab: onTabMock }
      );
    });
    
    expect(onTabMock).toHaveBeenCalledTimes(1);
  });

  test('setFocus sets focus on the provided element', () => {
    const { result } = renderHook(() => useAccessibility());
    const mockElement = { focus: jest.fn() } as any;
    
    act(() => {
      result.current.setFocus(mockElement);
    });
    
    expect(mockElement.focus).toHaveBeenCalledTimes(1);
    expect(result.current.focusRef.current).toBe(mockElement);
  });

  test('getAriaAttributes returns correct attributes for button role', () => {
    const { result } = renderHook(() => useAccessibility());
    
    const attributes = result.current.getAriaAttributes('button', {
      label: 'Test Button',
      description: 'This is a test button',
      expanded: true,
      selected: true,
      disabled: false,
      required: true,
      hasPopup: true,
      controls: 'test-panel',
      labelledBy: 'test-label',
      describedBy: 'test-description'
    });
    
    expect(attributes).toEqual({
      role: 'button',
      'aria-label': 'Test Button',
      'aria-description': 'This is a test button',
      'aria-expanded': true,
      'aria-selected': true,
      'aria-disabled': false,
      'aria-required': true,
      'aria-haspopup': true,
      'aria-controls': 'test-panel',
      'aria-labelledby': 'test-label',
      'aria-describedby': 'test-description'
    });
  });

  test('getAriaAttributes returns only role when no options provided', () => {
    const { result } = renderHook(() => useAccessibility());
    
    const attributes = result.current.getAriaAttributes('region');
    
    expect(attributes).toEqual({
      role: 'region'
    });
  });
});
