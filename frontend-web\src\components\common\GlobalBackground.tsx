import React, { useEffect, useState } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface GlobalBackgroundProps {
  module?: string;
  accentColor?: string;
  secondaryColor?: string;
  opacity?: number;
  animated?: boolean;
  pattern?: 'gradient' | 'radial' | 'mesh' | 'dots' | 'waves';
  children?: React.ReactNode;
}

/**
 * GlobalBackground - A consistent background component for the entire application
 * This component provides a unified background style across all modules and submodules
 */
const GlobalBackground: React.FC<GlobalBackgroundProps> = ({
  module,
  accentColor,
  secondaryColor,
  opacity = 0.05,
  animated = true,
  pattern = 'radial',
  children
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Get colors based on module or theme
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      case 'analytics':
        return theme.palette.primary.dark;
      default:
        return theme.palette.primary.main;
    }
  };

  const primaryColor = accentColor || getModuleColor();
  const secondaryColorValue = secondaryColor || (
    module ?
      (theme.palette.mode === 'dark' ? alpha(primaryColor, 0.7) : alpha(primaryColor, 0.5)) :
      theme.palette.secondary.main
  );

  // Track mouse movement for interactive backgrounds
  useEffect(() => {
    if (animated) {
      const handleMouseMove = (e: MouseEvent) => {
        setMousePosition({
          x: e.clientX,
          y: e.clientY
        });
      };

      window.addEventListener('mousemove', handleMouseMove);

      return () => {
        window.removeEventListener('mousemove', handleMouseMove);
      };
    }
  }, [animated]);

  // Get background pattern based on selected pattern type
  const getBackgroundPattern = () => {
    switch (pattern) {
      case 'gradient':
        return `linear-gradient(135deg, ${alpha(primaryColor, opacity)}, ${alpha(secondaryColorValue, opacity)})`;
      case 'radial':
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 40%),
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColorValue, opacity)}, transparent 40%)`;
      case 'mesh':
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 30%),
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColorValue, opacity)}, transparent 30%),
                radial-gradient(circle at 30% 70%, ${alpha(primaryColor, opacity/2)}, transparent 20%),
                radial-gradient(circle at 70% 30%, ${alpha(secondaryColorValue, opacity/2)}, transparent 20%)`;
      case 'dots':
        return `radial-gradient(circle at 25% 25%, ${alpha(primaryColor, opacity)} 1%, transparent 1%),
                radial-gradient(circle at 75% 75%, ${alpha(secondaryColorValue, opacity)} 1%, transparent 1%),
                radial-gradient(circle at 25% 75%, ${alpha(primaryColor, opacity/2)} 1%, transparent 1%),
                radial-gradient(circle at 75% 25%, ${alpha(secondaryColorValue, opacity/2)} 1%, transparent 1%)`;
      case 'waves':
        return `linear-gradient(45deg, ${alpha(primaryColor, opacity)} 25%, transparent 25%),
                linear-gradient(-45deg, ${alpha(primaryColor, opacity)} 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, ${alpha(secondaryColorValue, opacity)} 75%),
                linear-gradient(-45deg, transparent 75%, ${alpha(secondaryColorValue, opacity)} 75%)`;
      default:
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 40%),
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColorValue, opacity)}, transparent 40%)`;
    }
  };

  // Get interactive gradient based on mouse position
  const getInteractiveGradient = () => {
    if (!animated) return '';

    const x = (mousePosition.x / window.innerWidth) * 100;
    const y = (mousePosition.y / window.innerHeight) * 100;

    return `radial-gradient(circle at ${x}% ${y}%, ${alpha(primaryColor, opacity * 2)}, transparent 20%)`;
  };

  // Get background image based on module
  const getBackgroundImage = (): string => {
    switch (module?.toLowerCase()) {
      case 'animals':
        return '/images/modules/animals/cattle-1.jpeg';
      case 'health':
        return '/images/modules/health/health-main.png';
      case 'breeding':
        return '/images/modules/breeding/breeding-main.png';
      case 'feeding':
        return '/images/modules/feeding/feed-main.jpeg';
      case 'financial':
        return '/images/modules/financial/financial-main.jpg';
      case 'commercial':
        return '/images/modules/commercial/commercial-main.webp';
      case 'rfid':
        return '/images/modules/rfid/rfid-1.webp';
      case 'dashboard':
      default:
        return '/images/dashboard/main-dashboard.jpg';
    }
  };

  return (
    <Box sx={{
      position: 'relative',
      minHeight: '100%',
      width: '100%',
      backgroundColor: theme.palette.background.default,
      backdropFilter: 'blur(0px)',
      borderRadius: 0,
      border: 'none',
      boxShadow: 'none',
      marginLeft: 0,
      paddingLeft: 0,
      left: 0,
      transition: 'all 0.5s ease',
      // Add subtle animated background effect
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'radial-gradient(circle at 30% 50%, rgba(41, 121, 255, 0.03) 0%, transparent 50%), radial-gradient(circle at 70% 50%, rgba(25, 65, 125, 0.03) 0%, transparent 50%)',
        backgroundSize: '200% 200%',
        animation: 'gradientFloat 15s ease-in-out infinite alternate',
        zIndex: -1,
        opacity: 0.5
      },
      '@keyframes gradientFloat': {
        '0%': { backgroundPosition: '0% 0%' },
        '50%': { backgroundPosition: '100% 100%' },
        '100%': { backgroundPosition: '0% 0%' }
      },
      // Remove any potential white space or borders
      '& .MuiPaper-root': {
        border: 'none',
        boxShadow: 'none',
        borderRadius: '12px',
      },
      '&:hover': {
        backdropFilter: 'blur(3px)',
      }
    }}>
      {/* Background Image with low opacity */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${getBackgroundImage()})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.05,
          zIndex: -4,
        }}
      />

      {/* Static Background */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -2,
          background: getBackgroundPattern(),
          backgroundSize: pattern === 'dots' || pattern === 'waves' ? '50px 50px' : 'cover',
          opacity: 0.07, // Slightly increased opacity for better visibility
          borderRadius: 0,
          // Add subtle animation
          animation: 'patternPulse 10s ease-in-out infinite alternate',
          filter: 'blur(1px)'
        }}
      />

      {/* Add subtle grid pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: -3,
          backgroundImage: 'linear-gradient(rgba(41, 121, 255, 0.03) 1px, transparent 1px), linear-gradient(90deg, rgba(41, 121, 255, 0.03) 1px, transparent 1px)',
          backgroundSize: '20px 20px',
          opacity: 0.3,
          '@keyframes patternPulse': {
            '0%': { opacity: 0.05 },
            '50%': { opacity: 0.1 },
            '100%': { opacity: 0.05 }
          }
        }}
      />

      {/* Interactive Background */}
      {animated && (
        <motion.div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: -1,
            background: getInteractiveGradient(),
            opacity: 0.2,
            filter: 'blur(30px)',
            mixBlendMode: 'overlay'
          }}
          animate={{
            background: getInteractiveGradient()
          }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        />
      )}

      {/* Add subtle floating particles */}
      {animated && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: -1,
            overflow: 'hidden',
            opacity: 0.3
          }}
        >
          {[...Array(15)].map((_, i) => (
            <Box
              key={i}
              component="span"
              sx={{
                position: 'absolute',
                width: Math.random() * 5 + 2 + 'px',
                height: Math.random() * 5 + 2 + 'px',
                backgroundColor: 'rgba(41, 121, 255, 0.3)',
                borderRadius: '50%',
                top: Math.random() * 100 + '%',
                left: Math.random() * 100 + '%',
                animation: `floatParticle ${Math.random() * 10 + 10}s linear infinite`,
                opacity: Math.random() * 0.5 + 0.2,
                boxShadow: '0 0 10px rgba(41, 121, 255, 0.5)',
                '@keyframes floatParticle': {
                  '0%': {
                    transform: 'translateY(0) translateX(0)',
                    opacity: Math.random() * 0.5 + 0.2
                  },
                  '50%': {
                    transform: `translateY(${Math.random() * 100 - 50}px) translateX(${Math.random() * 100 - 50}px)`,
                    opacity: Math.random() * 0.7 + 0.3
                  },
                  '100%': {
                    transform: 'translateY(0) translateX(0)',
                    opacity: Math.random() * 0.5 + 0.2
                  }
                }
              }}
            />
          ))}
        </Box>
      )}

      {/* Content */}
      {children}
    </Box>
  );
};

export default GlobalBackground;
