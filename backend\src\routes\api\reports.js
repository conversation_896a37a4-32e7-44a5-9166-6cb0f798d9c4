/**
 * Reports API Routes
 * 
 * This module provides API routes for generating reports.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/reports
 * @desc Get all saved reports
 * @access Private
 */
router.get('/', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const { 
      type, 
      search,
      sort = 'createdAt', 
      order = 'desc',
      limit = 100, 
      page = 1 
    } = req.query;
    
    // Build query
    const query = {};
    if (type) query.type = type;
    
    // Add search if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };
    
    // Get reports
    const reports = await ApiService.find('Report', query, options);
    
    // Get total count
    const total = await ApiService.count('Report', query);
    
    // Return reports
    res.json({
      success: true,
      data: reports,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/reports/:id
 * @desc Get report by ID
 * @access Private
 */
router.get('/:id', authenticate, async (req, res, next) => {
  try {
    // Get report
    const report = await ApiService.findById('Report', req.params.id);
    
    // Check if report exists
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // Return report
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/reports
 * @desc Create a new report
 * @access Private
 */
router.post('/', authenticate, requirePermission(['create_reports']), async (req, res, next) => {
  try {
    // Create report
    const report = await ApiService.create('Report', {
      ...req.body,
      createdBy: req.user.id
    });
    
    // Return report
    res.status(201).json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/reports/:id
 * @desc Delete a report
 * @access Private
 */
router.delete('/:id', authenticate, requirePermission(['delete_reports']), async (req, res, next) => {
  try {
    // Get report
    const report = await ApiService.findById('Report', req.params.id);
    
    // Check if report exists
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Report not found'
      });
    }
    
    // Delete report
    await ApiService.deleteById('Report', req.params.id);
    
    // Return success
    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/reports/generate/animal
 * @desc Generate animal report
 * @access Private
 */
router.post('/generate/animal', authenticate, requirePermission(['view_reports']), async (req, res, next) => {
  try {
    const { 
      species, 
      status, 
      gender, 
      location, 
      startDate, 
      endDate,
      includeHealthRecords = false,
      includeBreedingRecords = false
    } = req.body;
    
    // Build query
    const query = {};
    if (species) query.species = species;
    if (status) query.status = status;
    if (gender) query.gender = gender;
    if (location) query.location = location;
    
    // Add date range if provided
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }
    
    // Get animals
    const animals = await ApiService.find('Animal', query);
    
    // Get additional data if requested
    let healthRecords = [];
    let breedingRecords = [];
    
    if (includeHealthRecords && animals.length > 0) {
      const animalIds = animals.map(animal => animal._id);
      healthRecords = await ApiService.find('HealthRecord', { animal: { $in: animalIds } });
    }
    
    if (includeBreedingRecords && animals.length > 0) {
      const animalIds = animals.map(animal => animal._id);
      breedingRecords = await ApiService.find('BreedingRecord', { 
        $or: [
          { femaleAnimal: { $in: animalIds } },
          { maleAnimal: { $in: animalIds } }
        ]
      });
    }
    
    // Calculate statistics
    const totalAnimals = animals.length;
    const animalsBySpecies = {};
    const animalsByStatus = {};
    const animalsByGender = {};
    const animalsByLocation = {};
    
    animals.forEach(animal => {
      // Count by species
      if (animalsBySpecies[animal.species]) {
        animalsBySpecies[animal.species]++;
      } else {
        animalsBySpecies[animal.species] = 1;
      }
      
      // Count by status
      if (animalsByStatus[animal.status]) {
        animalsByStatus[animal.status]++;
      } else {
        animalsByStatus[animal.status] = 1;
      }
      
      // Count by gender
      if (animalsByGender[animal.gender]) {
        animalsByGender[animal.gender]++;
      } else {
        animalsByGender[animal.gender] = 1;
      }
      
      // Count by location
      if (animalsByLocation[animal.location]) {
        animalsByLocation[animal.location]++;
      } else {
        animalsByLocation[animal.location] = 1;
      }
    });
    
    // Create report result
    const reportResult = {
      generatedAt: new Date(),
      parameters: req.body,
      data: {
        animals,
        healthRecords: includeHealthRecords ? healthRecords : [],
        breedingRecords: includeBreedingRecords ? breedingRecords : [],
        statistics: {
          totalAnimals,
          animalsBySpecies,
          animalsByStatus,
          animalsByGender,
          animalsByLocation
        }
      }
    };
    
    // Save report result if requested
    if (req.body.saveReport) {
      await ApiService.create('ReportResult', {
        reportId: req.body.reportId || 'ad-hoc',
        data: reportResult,
        generatedAt: new Date(),
        createdBy: req.user.id
      });
    }
    
    // Return report result
    res.json({
      success: true,
      data: reportResult
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/reports/generate/financial
 * @desc Generate financial report
 * @access Private
 */
router.post('/generate/financial', authenticate, requirePermission(['view_reports', 'view_transactions']), async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate,
      type,
      category,
      groupBy = 'month'
    } = req.body;
    
    // Validate date range
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Start date and end date are required'
      });
    }
    
    // Convert dates to Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    // Build query
    const query = {
      transactionDate: { $gte: startDateObj, $lte: endDateObj }
    };
    
    if (type) query.type = type;
    if (category) query.category = category;
    
    // Get transactions
    const transactions = await ApiService.find('Transaction', query, { sort: { transactionDate: 1 } });
    
    // Calculate statistics
    const totalTransactions = transactions.length;
    let totalIncome = 0;
    let totalExpenses = 0;
    const transactionsByType = {};
    const transactionsByCategory = {};
    const transactionsByPeriod = {};
    
    // Define period format function
    const formatPeriod = (date) => {
      if (groupBy === 'day') {
        return date.toISOString().split('T')[0];
      } else if (groupBy === 'month') {
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      } else if (groupBy === 'year') {
        return date.getFullYear().toString();
      } else {
        return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
      }
    };
    
    transactions.forEach(transaction => {
      // Calculate totals
      if (transaction.type === 'income') {
        totalIncome += transaction.amount;
      } else if (transaction.type === 'expense') {
        totalExpenses += transaction.amount;
      }
      
      // Count by type
      if (transactionsByType[transaction.type]) {
        transactionsByType[transaction.type].count++;
        transactionsByType[transaction.type].amount += transaction.amount;
      } else {
        transactionsByType[transaction.type] = {
          count: 1,
          amount: transaction.amount
        };
      }
      
      // Count by category
      if (transactionsByCategory[transaction.category]) {
        transactionsByCategory[transaction.category].count++;
        transactionsByCategory[transaction.category].amount += transaction.amount;
      } else {
        transactionsByCategory[transaction.category] = {
          count: 1,
          amount: transaction.amount
        };
      }
      
      // Group by period
      const period = formatPeriod(new Date(transaction.transactionDate));
      
      if (!transactionsByPeriod[period]) {
        transactionsByPeriod[period] = {
          income: 0,
          expenses: 0
        };
      }
      
      if (transaction.type === 'income') {
        transactionsByPeriod[period].income += transaction.amount;
      } else if (transaction.type === 'expense') {
        transactionsByPeriod[period].expenses += transaction.amount;
      }
    });
    
    // Calculate profit and profit margin
    const profit = totalIncome - totalExpenses;
    const profitMargin = totalIncome > 0 ? (profit / totalIncome) * 100 : 0;
    
    // Create report result
    const reportResult = {
      generatedAt: new Date(),
      parameters: req.body,
      data: {
        transactions,
        statistics: {
          totalTransactions,
          totalIncome,
          totalExpenses,
          profit,
          profitMargin,
          transactionsByType,
          transactionsByCategory,
          transactionsByPeriod
        }
      }
    };
    
    // Save report result if requested
    if (req.body.saveReport) {
      await ApiService.create('ReportResult', {
        reportId: req.body.reportId || 'ad-hoc',
        data: reportResult,
        generatedAt: new Date(),
        createdBy: req.user.id
      });
    }
    
    // Return report result
    res.json({
      success: true,
      data: reportResult
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/reports/generate/breeding
 * @desc Generate breeding report
 * @access Private
 */
router.post('/generate/breeding', authenticate, requirePermission(['view_reports', 'view_breeding_records']), async (req, res, next) => {
  try {
    const { 
      startDate, 
      endDate,
      status,
      includeOffspring = false
    } = req.body;
    
    // Build query
    const query = {};
    if (status) query.status = status;
    
    // Add date range if provided
    if (startDate || endDate) {
      query.breedingDate = {};
      if (startDate) query.breedingDate.$gte = new Date(startDate);
      if (endDate) query.breedingDate.$lte = new Date(endDate);
    }
    
    // Get breeding records
    const breedingRecords = await ApiService.find('BreedingRecord', query, { 
      sort: { breedingDate: 1 },
      populate: ['femaleAnimal', 'maleAnimal']
    });
    
    // Get birth records if requested
    let birthRecords = [];
    if (includeOffspring && breedingRecords.length > 0) {
      const breedingIds = breedingRecords.map(record => record._id);
      birthRecords = await ApiService.find('BirthRecord', { breedingRecord: { $in: breedingIds } }, {
        populate: ['mother', 'father']
      });
    }
    
    // Calculate statistics
    const totalBreedingRecords = breedingRecords.length;
    const breedingByStatus = {};
    const breedingByMonth = {};
    let successRate = 0;
    let totalOffspring = 0;
    
    breedingRecords.forEach(record => {
      // Count by status
      if (breedingByStatus[record.status]) {
        breedingByStatus[record.status]++;
      } else {
        breedingByStatus[record.status] = 1;
      }
      
      // Group by month
      const month = `${new Date(record.breedingDate).getFullYear()}-${(new Date(record.breedingDate).getMonth() + 1).toString().padStart(2, '0')}`;
      
      if (!breedingByMonth[month]) {
        breedingByMonth[month] = {
          total: 0,
          successful: 0,
          pending: 0,
          unsuccessful: 0
        };
      }
      
      breedingByMonth[month].total++;
      
      if (record.status === 'confirmed') {
        breedingByMonth[month].successful++;
      } else if (record.status === 'pending') {
        breedingByMonth[month].pending++;
      } else if (record.status === 'unsuccessful') {
        breedingByMonth[month].unsuccessful++;
      }
    });
    
    // Calculate success rate
    const successfulBreedings = breedingByStatus['confirmed'] || 0;
    successRate = totalBreedingRecords > 0 ? (successfulBreedings / totalBreedingRecords) * 100 : 0;
    
    // Calculate total offspring
    birthRecords.forEach(record => {
      totalOffspring += record.totalOffspring || 0;
    });
    
    // Create report result
    const reportResult = {
      generatedAt: new Date(),
      parameters: req.body,
      data: {
        breedingRecords,
        birthRecords: includeOffspring ? birthRecords : [],
        statistics: {
          totalBreedingRecords,
          successfulBreedings,
          successRate,
          totalOffspring,
          breedingByStatus,
          breedingByMonth
        }
      }
    };
    
    // Save report result if requested
    if (req.body.saveReport) {
      await ApiService.create('ReportResult', {
        reportId: req.body.reportId || 'ad-hoc',
        data: reportResult,
        generatedAt: new Date(),
        createdBy: req.user.id
      });
    }
    
    // Return report result
    res.json({
      success: true,
      data: reportResult
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
