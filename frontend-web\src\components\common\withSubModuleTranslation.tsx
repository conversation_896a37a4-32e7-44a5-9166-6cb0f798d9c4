import React from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { translateSubModuleField, translateField } from '../../utils/translationHelpers';

/**
 * Higher-order component that provides translation functions for sub-module pages
 * @param WrappedComponent The component to wrap
 * @param module The main module name (e.g., 'animals', 'breeding')
 * @param submodule The sub-module name (e.g., 'profiles', 'schedule')
 * @returns A wrapped component with translation functions
 */
export const withSubModuleTranslation = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  module: string,
  submodule: string
) => {
  // Create a wrapper component
  const WithSubModuleTranslation: React.FC<P> = (props) => {
    const { translate } = useLanguage();

    // Create translation functions specific to this sub-module
    const translateSubModule = (field: string, fallback: string) => 
      translateSubModuleField(translate, module, submodule, field, fallback);
    
    const translateModuleField = (field: string, fallback?: string) =>
      translateField(translate, field, module, fallback);

    // Pass the translation functions to the wrapped component
    return (
      <WrappedComponent
        {...props}
        translate={translate}
        translateSubModule={translateSubModule}
        translateModuleField={translateModuleField}
      />
    );
  };

  // Set display name for debugging
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithSubModuleTranslation.displayName = `withSubModuleTranslation(${displayName})`;

  return WithSubModuleTranslation;
};

export default withSubModuleTranslation;
