import React, { useState, useEffect } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface ProfessionalLoginBackgroundProps {
  images: string[];
  interval?: number;
  children?: React.ReactNode;
}

/**
 * ProfessionalLoginBackground - A high-quality background component for the login page
 * Features professional livestock images with smooth transitions
 */
const ProfessionalLoginBackground: React.FC<ProfessionalLoginBackgroundProps> = ({
  images,
  interval = 8000,
  children
}) => {
  const theme = useTheme();
  const { currentColor } = useThemeContext();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Only rotate through images if there's more than one
  useEffect(() => {
    // Skip rotation if there's only one image
    if (images.length <= 1) {
      return;
    }

    const timer = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setIsTransitioning(false);
      }, 500); // Transition duration
    }, interval);

    return () => clearInterval(timer);
  }, [images.length, interval]);

  return (
    <Box sx={{
      position: 'relative',
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      borderRadius: { xs: 0, md: 1 },
      boxShadow: 'none'
    }}>
      {/* Main Background Images */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${images[currentImageIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: 0
        }}
      />

      {/* Professional Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg,
            ${alpha(theme.palette.primary.dark, 0.4)},
            ${alpha(theme.palette.primary.main, 0.2)},
            ${alpha(theme.palette.secondary.main, 0.3)})`,
          mixBlendMode: 'multiply',
          zIndex: 1
        }}
      />

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 10,
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 0
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ProfessionalLoginBackground;
