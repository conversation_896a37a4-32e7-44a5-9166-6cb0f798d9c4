/**
 * Update Admin Password
 * 
 * This script updates the admin user's password in the MongoDB database.
 * Run with: node scripts/update-admin-password.js
 */

require('dotenv').config();
const mongodb = require('../src/config/mongodb');
const bcrypt = require('bcryptjs');
const logger = require('../src/utils/logger');

// New admin password
const newPassword = 'Admin@123';

// Hash password
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

// Update admin password
async function updateAdminPassword() {
  try {
    logger.info('Updating admin password...');
    
    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB');
    
    // Find admin user
    const adminUser = await db.collection('users').findOne({ username: 'admin' });
    if (!adminUser) {
      logger.error('Admin user not found');
      return false;
    }
    
    logger.info(`Found admin user: ${adminUser.username}`);
    
    // Hash new password
    const hashedPassword = await hashPassword(newPassword);
    logger.info(`Generated new password hash: ${hashedPassword.substring(0, 10)}...`);
    
    // Update admin password
    await db.collection('users').updateOne(
      { username: 'admin' },
      { $set: { password: hashedPassword, updatedAt: new Date() } }
    );
    logger.info('Admin password updated successfully');
    
    // Verify the update
    const updatedUser = await db.collection('users').findOne({ username: 'admin' });
    logger.info(`Updated admin user password hash: ${updatedUser.password.substring(0, 10)}...`);
    
    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    
    return true;
  } catch (error) {
    logger.error('Error updating admin password:', error);
    return false;
  }
}

// Run the script
updateAdminPassword()
  .then(success => {
    if (success) {
      logger.info('Admin password update completed successfully');
      process.exit(0);
    } else {
      logger.error('Admin password update failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during admin password update:', error);
    process.exit(1);
  });
