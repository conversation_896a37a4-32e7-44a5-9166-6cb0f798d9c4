/**
 * Inventory Management Controller
 * Handles all inventory-related CRUD operations
 */

const mongodb = require('../../config/mongodb');
const logger = require('../../utils/logger');
const { ObjectId } = require('mongodb');

class InventoryController {
  // Get all inventory items
  async getInventoryItems(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const category = req.query.category;
      const lowStock = req.query.lowStock === 'true';
      
      // Build filter
      const filter = {};
      if (category) filter.category = category;
      if (lowStock) {
        filter.$expr = { $lte: ['$currentStock', '$minimumStock'] };
      }
      
      // Get items with pagination
      const items = await collection
        .find(filter)
        .sort({ name: 1 })
        .skip(skip)
        .limit(limit)
        .toArray();
      
      const total = await collection.countDocuments(filter);
      
      res.json({
        success: true,
        data: items,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching inventory items:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch inventory items',
        error: error.message
      });
    }
  }

  // Get inventory item by ID
  async getInventoryItemById(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      const item = await collection.findOne({ _id: new ObjectId(req.params.id) });
      
      if (!item) {
        return res.status(404).json({
          success: false,
          message: 'Inventory item not found'
        });
      }
      
      res.json({
        success: true,
        data: item
      });
    } catch (error) {
      logger.error('Error fetching inventory item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch inventory item',
        error: error.message
      });
    }
  }

  // Create new inventory item
  async createInventoryItem(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      const {
        name,
        category,
        unit,
        costPerUnit,
        currentStock,
        minimumStock,
        maximumStock,
        supplier
      } = req.body;
      
      // Validate required fields
      if (!name || !category || !unit || !costPerUnit) {
        return res.status(400).json({
          success: false,
          message: 'Name, category, unit, and cost per unit are required'
        });
      }
      
      const inventoryItem = {
        name,
        category,
        unit,
        costPerUnit: parseFloat(costPerUnit),
        currentStock: parseInt(currentStock) || 0,
        minimumStock: parseInt(minimumStock) || 0,
        maximumStock: parseInt(maximumStock) || 1000,
        supplier: supplier || '',
        lastRestocked: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await collection.insertOne(inventoryItem);
      
      res.status(201).json({
        success: true,
        message: 'Inventory item created successfully',
        data: { ...inventoryItem, _id: result.insertedId }
      });
    } catch (error) {
      logger.error('Error creating inventory item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create inventory item',
        error: error.message
      });
    }
  }

  // Update inventory item
  async updateInventoryItem(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      const updateData = { ...req.body };
      delete updateData._id;
      
      // Convert numeric fields
      if (updateData.costPerUnit) updateData.costPerUnit = parseFloat(updateData.costPerUnit);
      if (updateData.currentStock) updateData.currentStock = parseInt(updateData.currentStock);
      if (updateData.minimumStock) updateData.minimumStock = parseInt(updateData.minimumStock);
      if (updateData.maximumStock) updateData.maximumStock = parseInt(updateData.maximumStock);
      if (updateData.lastRestocked) updateData.lastRestocked = new Date(updateData.lastRestocked);
      
      updateData.updatedAt = new Date();
      
      const result = await collection.updateOne(
        { _id: new ObjectId(req.params.id) },
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Inventory item not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Inventory item updated successfully'
      });
    } catch (error) {
      logger.error('Error updating inventory item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update inventory item',
        error: error.message
      });
    }
  }

  // Delete inventory item
  async deleteInventoryItem(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      const result = await collection.deleteOne({ _id: new ObjectId(req.params.id) });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Inventory item not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Inventory item deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting inventory item:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete inventory item',
        error: error.message
      });
    }
  }

  // Get inventory statistics
  async getInventoryStatistics(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('inventory_items');
      
      // Get basic counts
      const totalItems = await collection.countDocuments();
      const lowStockItems = await collection.countDocuments({
        $expr: { $lte: ['$currentStock', '$minimumStock'] }
      });
      const outOfStockItems = await collection.countDocuments({ currentStock: 0 });
      
      // Get total inventory value
      const inventoryValue = await collection.aggregate([
        {
          $group: {
            _id: null,
            totalValue: { $sum: { $multiply: ['$currentStock', '$costPerUnit'] } }
          }
        }
      ]).toArray();
      
      // Get items by category
      const itemsByCategory = await collection.aggregate([
        { $group: { _id: '$category', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get low stock alerts
      const lowStockAlerts = await collection
        .find({ $expr: { $lte: ['$currentStock', '$minimumStock'] } })
        .sort({ currentStock: 1 })
        .limit(10)
        .toArray();
      
      res.json({
        success: true,
        data: {
          totalItems,
          lowStockItems,
          outOfStockItems,
          totalValue: inventoryValue[0]?.totalValue || 0,
          itemsByCategory,
          lowStockAlerts
        }
      });
    } catch (error) {
      logger.error('Error fetching inventory statistics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch inventory statistics',
        error: error.message
      });
    }
  }

  // Get inventory overview
  async getInventory(req, res) {
    try {
      res.json({
        success: true,
        message: 'Inventory module is operational',
        data: {
          module: 'inventory',
          status: 'active',
          features: [
            'Inventory Items Management',
            'Stock Level Tracking',
            'Low Stock Alerts',
            'Supplier Management',
            'Cost Tracking'
          ]
        }
      });
    } catch (error) {
      logger.error('Error in inventory overview:', error);
      res.status(500).json({
        success: false,
        message: 'Inventory module error',
        error: error.message
      });
    }
  }
}

module.exports = new InventoryController();
