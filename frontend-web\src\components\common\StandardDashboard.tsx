import React, { ReactNode } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import ModernDashboard, { DashboardStat, DashboardAction, DashboardTab } from './ModernDashboard';
import { ErrorBoundary } from '.';
import UnifiedModuleContainer from './UnifiedModuleContainer';

export interface StandardDashboardProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  stats?: DashboardStat[];
  actions?: DashboardAction[];
  tabs?: DashboardTab[];
  activeTab?: number;
  onTabChange?: (index: number) => void;
  isLoading?: boolean;
  loadingMessage?: string;
  onRefresh?: () => void;
  children?: ReactNode;
  module?: string;
  extraHeaderContent?: ReactNode;
}

/**
 * StandardDashboard component
 * A standardized dashboard layout for all modules in the application
 * Ensures consistent styling and layout across all modules
 */
const StandardDashboard: React.FC<StandardDashboardProps> = ({
  title,
  subtitle,
  icon,
  stats,
  actions,
  tabs,
  activeTab = 0,
  onTabChange,
  isLoading = false,
  loadingMessage,
  onRefresh,
  children,
  module,
  extraHeaderContent
}) => {
  const theme = useTheme();

  return (
    <ErrorBoundary>
      <UnifiedModuleContainer module={module}>
        <ModernDashboard
          title={title}
          subtitle={subtitle}
          icon={icon}
          stats={stats}
          actions={actions}
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={onTabChange}
          isLoading={isLoading}
          loadingMessage={loadingMessage}
          onRefresh={onRefresh}
          module={module}
          extraHeaderContent={extraHeaderContent}
        >
          <Box
            className={module === 'reports' ? 'reports-module' : ''}
            sx={{
            '& .MuiPaper-root': {
              borderRadius: '12px',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden',
              transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
              backdropFilter: 'blur(10px)',
              backgroundColor: 'transparent',
              background: `linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.6)},
                ${alpha(theme.palette.background.paper, 0.4)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 8px 30px rgba(0,0,0,0.15)',
                background: `linear-gradient(135deg,
                  ${alpha(theme.palette.background.paper, 0.7)},
                  ${alpha(theme.palette.background.paper, 0.5)})`,
                borderColor: alpha(theme.palette.primary.main, 0.25)
              }
            },
            '& .MuiTableContainer-root': {
              background: 'transparent',
              backdropFilter: 'blur(10px)',
            },
            '& .MuiTable-root': {
              background: 'transparent',
            },
            '& .MuiTableHead-root': {
              background: `linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.7)},
                ${alpha(theme.palette.background.paper, 0.5)})`,
            },
            '& .MuiTableRow-root': {
              transition: 'all 0.2s ease',
              '&:hover': {
                background: alpha(theme.palette.primary.main, 0.05),
              }
            },
            '& .recharts-wrapper': {
              '& .recharts-surface': {
                overflow: 'visible'
              }
            },
            '& .MuiCard-root': {
              borderRadius: '12px',
              overflow: 'hidden',
              transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 8px 30px rgba(0,0,0,0.15)'
              }
            },
            '& .MuiTableContainer-root': {
              borderRadius: '12px',
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            },
            '& .MuiButton-root': {
              borderRadius: '8px',
              transition: 'all 0.3s ease',
            },
            '& .MuiChip-root': {
              borderRadius: '8px',
              transition: 'all 0.3s ease',
            }
          }}>
            {children}
          </Box>
        </ModernDashboard>
      </UnifiedModuleContainer>
    </ErrorBoundary>
  );
};

export default StandardDashboard;
