import React, { forwardRef, HTMLAttributes, ReactNode } from 'react';
import { Box, BoxProps } from '@mui/material';
import { useAccessibility } from '../../hooks/useAccessibility';

interface AccessibleWrapperProps extends BoxProps {
  role?: string;
  ariaLabel?: string;
  ariaDescription?: string;
  ariaExpanded?: boolean;
  ariaSelected?: boolean;
  ariaChecked?: boolean;
  ariaDisabled?: boolean;
  ariaRequired?: boolean;
  ariaHasPopup?: boolean;
  ariaControls?: string;
  ariaLabelledBy?: string;
  ariaDescribedBy?: string;
  ariaLevel?: number;
  tabIndex?: number;
  onKeyAction?: {
    onEnter?: () => void;
    onSpace?: () => void;
    onArrowUp?: () => void;
    onArrowDown?: () => void;
    onArrowLeft?: () => void;
    onArrowRight?: () => void;
    onEscape?: () => void;
    onTab?: () => void;
  };
  children: ReactNode;
}

/**
 * AccessibleWrapper component
 * A wrapper component that adds accessibility attributes and keyboard navigation
 */
const AccessibleWrapper = forwardRef<HTMLDivElement, AccessibleWrapperProps>(
  (
    {
      role = 'region',
      ariaLabel,
      ariaDescription,
      ariaExpanded,
      ariaSelected,
      ariaChecked,
      ariaDisabled,
      ariaRequired,
      ariaHasPopup,
      ariaControls,
      ariaLabelledBy,
      ariaDescribedBy,
      ariaLevel,
      tabIndex = 0,
      onKeyAction = {},
      children,
      ...boxProps
    },
    ref
  ) => {
    const { handleKeyboardNavigation, getAriaAttributes } = useAccessibility();

    // Get ARIA attributes
    const ariaAttributes = getAriaAttributes(role, {
      label: ariaLabel,
      description: ariaDescription,
      expanded: ariaExpanded,
      selected: ariaSelected,
      checked: ariaChecked,
      disabled: ariaDisabled,
      required: ariaRequired,
      hasPopup: ariaHasPopup,
      controls: ariaControls,
      labelledBy: ariaLabelledBy,
      describedBy: ariaDescribedBy,
      level: ariaLevel,
    });

    // Handle keyboard events
    const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>) => {
      handleKeyboardNavigation(event, onKeyAction);
    };

    return (
      <Box
        ref={ref}
        {...ariaAttributes}
        tabIndex={tabIndex}
        onKeyDown={handleKeyDown}
        {...boxProps}
      >
        {children}
      </Box>
    );
  }
);

AccessibleWrapper.displayName = 'AccessibleWrapper';

export default AccessibleWrapper;
