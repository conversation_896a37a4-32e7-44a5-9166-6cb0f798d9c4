const express = require('express');
const authController = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

module.exports = () => {
  const router = express.Router();

  // Public routes
  router.post('/login', authController.login);
  router.post('/forgot-password', authController.requestPasswordReset);
  router.post('/reset-password', authController.resetPassword);

  // Protected routes
  router.get('/me', authenticateToken, authController.getCurrentUser);

  return router;
};
