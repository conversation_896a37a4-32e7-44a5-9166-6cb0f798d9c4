import React, { useState, useEffect } from 'react';
import { Box, Typography, Grid, Paper, Select, MenuItem, FormControl, InputLabel, Chip, IconButton, Tabs, Tab } from '@mui/material';
import { motion } from 'framer-motion';
import {  ModuleHeader , CustomButton } from '../../components/common';
import GoogleMapComponent from '../../components/maps/GoogleMapComponent';
import RealTimeTracking from '../../components/animals/RealTimeTracking';
import { LocationOn, Refresh, SignalCellular4Bar, History, AccessTime, BatteryFull } from '../../utils/iconImports';
import { ModuleContainer } from '../../components/common';
import { alpha, useTheme } from '@mui/material/styles';

interface LocationPoint {
  id: string;
  timestamp: string;
  location: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  animalId: string;
  animalName: string;
  readerId: string;
  readerLocation: string;
}

interface Animal {
  id: string;
  name: string;
  tag: string;
  species: string;
  breed: string;
  lastSeen?: {
    location: string;
    timestamp: string;
  };
}

const mockAnimals: Animal[] = [
  {
    id: 'ANM001',
    name: 'Tshepiso',
    tag: 'CTL-2024-001',
    species: 'Cattle',
    breed: 'Nguni',
    lastSeen: {
      location: 'Veld A',
      timestamp: '2024-03-15 08:45:22'
    }
  },
  {
    id: 'ANM002',
    name: 'Kgosi',
    tag: 'CTL-2024-002',
    species: 'Cattle',
    breed: 'Bonsmara',
    lastSeen: {
      location: 'Kraal 1',
      timestamp: '2024-03-15 09:12:05'
    }
  },
  {
    id: 'ANM003',
    name: 'Lesedi',
    tag: 'CTL-2024-003',
    species: 'Cattle',
    breed: 'Afrikaner',
    lastSeen: {
      location: 'Kraal 2',
      timestamp: '2024-03-15 07:30:18'
    }
  },
  {
    id: 'ANM004',
    name: 'Pule',
    tag: 'SHP-2024-001',
    species: 'Sheep',
    breed: 'Dorper',
    lastSeen: {
      location: 'Boma 1',
      timestamp: '2024-03-15 10:05:33'
    }
  },
  {
    id: 'ANM005',
    name: 'Lerato',
    tag: 'GT-2024-001',
    species: 'Goat',
    breed: 'Boer',
    lastSeen: {
      location: 'Boma 2',
      timestamp: '2024-03-15 08:22:47'
    }
  }
];

const mockLocationHistory: LocationPoint[] = [
  {
    id: 'LOC001',
    timestamp: '2024-03-15 08:45:22',
    location: 'Veld A',
    coordinates: {
      lat: -25.7461,
      lng: 28.1881
    },
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    readerId: 'RDR001',
    readerLocation: 'Veld A Gate'
  },
  {
    id: 'LOC002',
    timestamp: '2024-03-14 16:30:15',
    location: 'Kraal 1',
    coordinates: {
      lat: -25.7465,
      lng: 28.1885
    },
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    readerId: 'RDR002',
    readerLocation: 'Kraal 1 Entrance'
  },
  {
    id: 'LOC003',
    timestamp: '2024-03-14 08:15:33',
    location: 'Dipping Area',
    coordinates: {
      lat: -25.7470,
      lng: 28.1890
    },
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    readerId: 'RDR003',
    readerLocation: 'Dipping Station'
  },
  {
    id: 'LOC004',
    timestamp: '2024-03-13 17:22:45',
    location: 'Veld B',
    coordinates: {
      lat: -25.7475,
      lng: 28.1895
    },
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    readerId: 'RDR004',
    readerLocation: 'Veld B Gate'
  },
  {
    id: 'LOC005',
    timestamp: '2024-03-13 08:30:12',
    location: 'Veld A',
    coordinates: {
      lat: -25.7461,
      lng: 28.1881
    },
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    readerId: 'RDR001',
    readerLocation: 'Veld A Gate'
  }
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`tracking-tabpanel-${index}`}
      aria-labelledby={`tracking-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `tracking-tab-${index}`,
    'aria-controls': `tracking-tabpanel-${index}`,
  };
}

const AnimalTracking: React.FC = () => {
  const [selectedAnimal, setSelectedAnimal] = useState<string>('ANM001');
  const [timeRange, setTimeRange] = useState<string>('7');
  const [locationHistory, setLocationHistory] = useState<LocationPoint[]>(mockLocationHistory);
  const [tabValue, setTabValue] = useState(0);

  const handleAnimalChange = (animalId: string) => {
    setSelectedAnimal(animalId);
    // In a real implementation, this would fetch location history for the selected animal
    // For now, we'll just use the mock data
  };

  const handleTimeRangeChange = (days: string) => {
    setTimeRange(days);
    // In a real implementation, this would fetch location history for the selected time range
    // For now, we'll just use the mock data
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    return date.toLocaleString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const selectedAnimalData = mockAnimals.find(animal => animal.id === selectedAnimal);

  // Prepare map locations from location history
  const mapLocations = locationHistory.map(point => ({
    id: point.id,
    position: point.coordinates,
    title: `${point.animalName} - ${point.location}`,
    info: `${formatDateTime(point.timestamp)}\nReader: ${point.readerLocation}`
  }));

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const theme = useTheme();

  return (
    <ModuleContainer module="animals">
      <Box sx={{ width: '100%' }}>
        <ModuleHeader
          title="Animal Tracking"
          subtitle="Track and monitor animal movements in real-time"
          module="animals"
          submodule="tracking"
          icon={<LocationOn />}
        />

        <Box sx={{ px: 3, pb: 5 }}>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="tracking tabs"
                sx={{
                  '& .MuiTab-root': {
                    fontSize: '1rem',
                    fontWeight: 500,
                    py: 2
                  }
                }}
              >
                <Tab
                  icon={<History />}
                  label="Historical Tracking"
                  iconPosition="start"
                  {...a11yProps(0)}
                />
                <Tab
                  icon={<AccessTime />}
                  label="Real-Time Tracking"
                  iconPosition="start"
                  {...a11yProps(1)}
                />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              {/* Historical Tracking Controls */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3,
                flexWrap: { xs: 'wrap', md: 'nowrap' },
                gap: 2
              }}>
                <Typography variant="h5" fontWeight="bold">
                  Historical Location Tracking
                </Typography>

                <Box sx={{
                  display: 'flex',
                  gap: 2,
                  flexWrap: { xs: 'wrap', md: 'nowrap' }
                }}>
                  <FormControl sx={{ minWidth: 200 }}>
                    <InputLabel id="animal-select-label">Select Animal</InputLabel>
                    <Select
                      labelId="animal-select-label"
                      value={selectedAnimal}
                      label="Select Animal"
                      onChange={(e) => handleAnimalChange(e.target.value as string)}
                      size="small"
                    >
                      {mockAnimals.map(animal => (
                        <MenuItem key={animal.id} value={animal.id}>
                          {animal.name} ({animal.tag})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>

                  <FormControl sx={{ minWidth: 150 }}>
                    <InputLabel id="time-range-label">Time Range</InputLabel>
                    <Select
                      labelId="time-range-label"
                      value={timeRange}
                      label="Time Range"
                      onChange={(e) => handleTimeRangeChange(e.target.value as string)}
                      size="small"
                    >
                      <MenuItem value="1">Last 24 hours</MenuItem>
                      <MenuItem value="7">Last 7 days</MenuItem>
                      <MenuItem value="30">Last 30 days</MenuItem>
                      <MenuItem value="90">Last 3 months</MenuItem>
                    </Select>
                  </FormControl>

                  <CustomButton
                    variant="contained"
                    startIcon={<Refresh />}
                    sx={{ height: 40 }}
                  >
                    Refresh
                  </CustomButton>
                </Box>
              </Box>

            {/* Animal Info Cards */}
            {selectedAnimalData && (
              <Grid container spacing={3} mb={4}>
                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <Paper sx={{ p: 3, height: '100%', borderRadius: 2 }}>
                      <Typography variant="h6" fontWeight="bold" mb={2}>
                        Animal Details
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Typography variant="body1">
                          <Box component="span" fontWeight="bold">Name:</Box> {selectedAnimalData.name}
                        </Typography>
                        <Typography variant="body1">
                          <Box component="span" fontWeight="bold">Tag:</Box> {selectedAnimalData.tag}
                        </Typography>
                        <Typography variant="body1">
                          <Box component="span" fontWeight="bold">Species:</Box> {selectedAnimalData.species}
                        </Typography>
                        <Typography variant="body1">
                          <Box component="span" fontWeight="bold">Breed:</Box> {selectedAnimalData.breed}
                        </Typography>
                      </Box>
                    </Paper>
                  </motion.div>
                </Grid>

                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <Paper sx={{ p: 3, height: '100%', borderRadius: 2 }}>
                      <Typography variant="h6" fontWeight="bold" mb={2}>
                        Current Location
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationOn color="error" />
                          <Typography variant="body1">
                            <Box component="span" fontWeight="bold">Location:</Box> {selectedAnimalData.lastSeen?.location || 'Unknown'}
                          </Typography>
                        </Box>
                        <Typography variant="body1">
                          <Box component="span" fontWeight="bold">Last Seen:</Box> {selectedAnimalData.lastSeen ? formatDateTime(selectedAnimalData.lastSeen.timestamp) : 'Unknown'}
                        </Typography>
                      </Box>
                    </Paper>
                  </motion.div>
                </Grid>

                <Grid item xs={12} md={4}>
                  <motion.div variants={itemVariants}>
                    <Paper sx={{ p: 3, height: '100%', borderRadius: 2 }}>
                      <Typography variant="h6" fontWeight="bold" mb={2}>
                        RFID Information
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <LocationOn color="primary" />
                          <Typography variant="body1">
                            <Box component="span" fontWeight="bold">RFID Tag:</Box> {`RFID-${selectedAnimalData.tag}`}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <BatteryFull sx={{ color: theme.palette.success.main }} />
                          <Typography variant="body1">
                            <Box component="span" fontWeight="bold">Battery Status:</Box>
                            <Box component="span" sx={{ color: theme.palette.success.main, ml: 1 }}>Good</Box>
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SignalCellular4Bar sx={{ color: theme.palette.success.main }} />
                          <Typography variant="body1">
                            <Box component="span" fontWeight="bold">Signal Strength:</Box>
                            <Box component="span" sx={{ color: theme.palette.success.main, ml: 1 }}>Strong</Box>
                          </Typography>
                        </Box>
                      </Box>
                    </Paper>
                  </motion.div>
                </Grid>
              </Grid>
            )}

            {/* Location History and Map */}
            <Grid container spacing={3}>
              <Grid item xs={12} md={5}>
                <motion.div variants={itemVariants}>
                  <Paper sx={{ p: 3, borderRadius: 2 }}>
                    <Typography variant="h6" fontWeight="bold" mb={2}>
                      Location History
                    </Typography>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                        <thead style={{ position: 'sticky', top: 0, background: theme.palette.background.paper }}>
                          <tr>
                            <th style={{ padding: '12px 16px', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: theme.palette.text.secondary, borderBottom: `1px solid ${theme.palette.divider}` }}>
                              Date & Time
                            </th>
                            <th style={{ padding: '12px 16px', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: theme.palette.text.secondary, borderBottom: `1px solid ${theme.palette.divider}` }}>
                              Location
                            </th>
                            <th style={{ padding: '12px 16px', textAlign: 'left', fontSize: '0.875rem', fontWeight: 600, color: theme.palette.text.secondary, borderBottom: `1px solid ${theme.palette.divider}` }}>
                              Reader
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {locationHistory.map((point) => (
                            <tr key={point.id}>
                              <td style={{ padding: '12px 16px', whiteSpace: 'nowrap', borderBottom: `1px solid ${theme.palette.divider}` }}>
                                {formatDateTime(point.timestamp)}
                              </td>
                              <td style={{ padding: '12px 16px', whiteSpace: 'nowrap', borderBottom: `1px solid ${theme.palette.divider}` }}>
                                {point.location}
                              </td>
                              <td style={{ padding: '12px 16px', whiteSpace: 'nowrap', borderBottom: `1px solid ${theme.palette.divider}` }}>
                                {point.readerLocation}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>

              <Grid item xs={12} md={7}>
                <motion.div variants={itemVariants}>
                  <Paper sx={{ p: 3, borderRadius: 2 }}>
                    <Typography variant="h6" fontWeight="bold" mb={2}>
                      Location Map
                    </Typography>
                    <Box sx={{ height: 400, borderRadius: 1, overflow: 'hidden' }}>
                      <GoogleMapComponent
                        locations={mapLocations}
                        center={mapLocations.length > 0 ? mapLocations[0].position : undefined}
                        height="100%"
                        zoom={14}
                      />
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>
            </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <motion.div variants={itemVariants}>
                <RealTimeTracking
                  animalIds={[selectedAnimal]}
                  refreshInterval={30}
                />
              </motion.div>
            </TabPanel>
          </motion.div>
        </Box>
      </Box>
    </ModuleContainer>
  );
};

export default AnimalTracking;
