import React, { useState } from 'react';
import CustomButton from '../../common/CustomButton';
import { Box, Typography, Card, CardContent, Grid, Chip, Rating, Divider, TextField, InputAdornment, useTheme, alpha, Avatar, List, ListItem, ListItemAvatar, ListItemText, IconButton, Tooltip } from '@mui/material';
import { motion } from 'framer-motion';
import {
  ShoppingCart,
  Store,
  LocalShipping,
  Favorite,
  Visibility as FavoriteBorder,
  Share,
  ArrowBack,
  CheckCircle,
  Info,
  LocationOn,
  Phone,
  Email,
  Star
} from '../../../utils/iconImports';
import { getConsistentBackground } from '../../../utils/backgroundThemes';

interface MarketplaceListingDetailProps {
  listingId: number;
  onBack: () => void;
}

// Mock listing data
const mockListingDetail = {
  id: 4,
  title: 'Breeding Bulls - Angus',
  description: 'Purebred Angus bulls with excellent genetics, ideal for improving your herd quality. These bulls have been carefully selected for their superior traits including growth rate, feed efficiency, and docile temperament.',
  price: 45000,
  unit: 'each',
  supplier: 'Premium Livestock Breeders',
  supplierRating: 4.9,
  supplierLocation: 'Vryburg, North West Province',
  supplierContact: '+27 82 123 4567',
  supplierEmail: '<EMAIL>',
  rating: 4.9,
  category: 'Livestock',
  inStock: true,
  quantity: 5,
  age: '3 years',
  weight: '850 kg',
  breed: 'Angus',
  certification: 'Certified Purebred',
  deliveryOptions: ['Farm Pickup', 'Arranged Transport'],
  paymentOptions: ['Bank Transfer', 'Financing Available'],
  additionalInfo: 'All animals come with complete health records and breeding history. Vaccination up to date.',
  relatedProducts: [
    { id: 7, name: 'Angus Heifers', price: 38000 },
    { id: 8, name: 'Premium Cattle Feed', price: 2500 },
    { id: 9, name: 'Livestock Transport Service', price: 5000 }
  ],
  reviews: [
    { id: 1, user: 'Johan Botha', rating: 5, comment: 'Excellent quality bulls, very satisfied with the purchase.', date: '2024-03-15' },
    { id: 2, user: 'Pieter van Zyl', rating: 5, comment: 'Great genetics, already seeing improvements in my herd.', date: '2024-02-20' },
    { id: 3, user: 'Willem Pretorius', rating: 4, comment: 'Good animals, delivery was a bit delayed but overall happy.', date: '2024-01-10' }
  ]
};

const MarketplaceListingDetail: React.FC<MarketplaceListingDetailProps> = ({ listingId, onBack }) => {
  const theme = useTheme();
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);

  // In a real app, you would fetch the listing details based on the ID
  const listing = mockListingDetail;

  const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value);
    if (!isNaN(value) && value > 0 && value <= listing.quantity) {
      setQuantity(value);
    }
  };

  const handleAddToCart = () => {
    console.log(`Added ${quantity} of ${listing.title} to cart`);
    // In a real app, you would dispatch an action to add to cart
  };

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <Box sx={{ px: 3, pb: 5 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Back Button */}
        <motion.div variants={itemVariants}>
          <CustomButton
            startIcon={<ArrowBack />}
            onClick={onBack}
            sx={{ mb: 3 }}
          >
            Back to Marketplace
          </CustomButton>
        </motion.div>

        <Grid container spacing={4}>
          {/* Product Image */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <Box
                sx={{
                  height: 400,
                  borderRadius: '12px',
                  overflow: 'hidden',
                  position: 'relative',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backgroundImage: `url(${getConsistentBackground(String(listing.id))})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.4)}, ${alpha(theme.palette.primary.dark, 0.5)})`,
                    zIndex: 1
                  }
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: 16,
                    right: 16,
                    zIndex: 2
                  }}
                >
                  <IconButton
                    sx={{
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                    }}
                    onClick={handleToggleFavorite}
                  >
                    {isFavorite ? <Favorite color="error" /> : <FavoriteBorder />}
                  </IconButton>
                  <IconButton
                    sx={{
                      ml: 1,
                      bgcolor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': { bgcolor: 'rgba(255, 255, 255, 1)' }
                    }}
                  >
                    <Share />
                  </IconButton>
                </Box>
                <Chip
                  label={listing.category}
                  sx={{
                    position: 'absolute',
                    bottom: 16,
                    left: 16,
                    bgcolor: alpha(theme.palette.background.paper, 0.9),
                    zIndex: 2
                  }}
                />
              </Box>
            </motion.div>
          </Grid>

          {/* Product Details */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                {listing.title}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={listing.rating} precision={0.5} readOnly />
                <Typography variant="body2" sx={{ ml: 1 }}>
                  {listing.rating} ({listing.reviews.length} reviews)
                </Typography>
              </Box>

              <Typography variant="h5" color="primary" fontWeight="bold" sx={{ mb: 2 }}>
                R{listing.price.toLocaleString()} <Typography component="span" variant="body1">{listing.unit}</Typography>
              </Typography>

              <Chip
                icon={listing.inStock ? <CheckCircle /> : <Info />}
                label={listing.inStock ? 'In Stock' : 'Out of Stock'}
                color={listing.inStock ? 'success' : 'error'}
                sx={{ mb: 3 }}
              />

              <Typography variant="body1" paragraph>
                {listing.description}
              </Typography>

              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Age</Typography>
                  <Typography variant="body1">{listing.age}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Weight</Typography>
                  <Typography variant="body1">{listing.weight}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Breed</Typography>
                  <Typography variant="body1">{listing.breed}</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="subtitle2" color="text.secondary">Certification</Typography>
                  <Typography variant="body1">{listing.certification}</Typography>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <TextField
                  label="Quantity"
                  type="number"
                  value={quantity}
                  onChange={handleQuantityChange}
                  InputProps={{
                    inputProps: { min: 1, max: listing.quantity }
                  }}
                  sx={{ width: 100, mr: 2 }}
                />
                <CustomButton
                  variant="contained"
                  size="large"
                  startIcon={<ShoppingCart />}
                  onClick={handleAddToCart}
                  disabled={!listing.inStock}
                  sx={{ flexGrow: 1 }}
                >
                  Add to Cart
                </CustomButton>
              </Box>

              <Divider sx={{ my: 3 }} />

              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Supplier Information
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Store sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body1">{listing.supplier}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                  <Star sx={{ fontSize: 16, color: theme.palette.warning.main }} />
                  <Typography variant="body2" sx={{ ml: 0.5 }}>
                    {listing.supplierRating}
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{listing.supplierLocation}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{listing.supplierContact}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Email sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">{listing.supplierEmail}</Typography>
              </Box>

              <CustomButton variant="outlined" startIcon={<Store />} sx={{ mb: 3 }}>
                View Supplier Profile
              </CustomButton>
            </motion.div>
          </Grid>
        </Grid>

        {/* Additional Information */}
        <motion.div variants={itemVariants}>
          <Card sx={{ mt: 4, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Additional Information
              </Typography>
              <Typography variant="body1" paragraph>
                {listing.additionalInfo}
              </Typography>

              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Delivery Options
                  </Typography>
                  <List disablePadding>
                    {listing.deliveryOptions.map((option, index) => (
                      <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                        <ListItemAvatar sx={{ minWidth: 36 }}>
                          <LocalShipping fontSize="small" color="primary" />
                        </ListItemAvatar>
                        <ListItemText primary={option} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Payment Options
                  </Typography>
                  <List disablePadding>
                    {listing.paymentOptions.map((option, index) => (
                      <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                        <ListItemAvatar sx={{ minWidth: 36 }}>
                          <CheckCircle fontSize="small" color="primary" />
                        </ListItemAvatar>
                        <ListItemText primary={option} />
                      </ListItem>
                    ))}
                  </List>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </motion.div>

        {/* Reviews */}
        <motion.div variants={itemVariants}>
          <Card sx={{ mt: 4, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Customer Reviews
              </Typography>

              <List>
                {listing.reviews.map((review) => (
                  <React.Fragment key={review.id}>
                    <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar>{review.user.charAt(0)}</Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {review.user}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {review.date}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Rating value={review.rating} size="small" readOnly />
                            <Typography variant="body1" sx={{ mt: 1 }}>
                              {review.comment}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>

              <CustomButton variant="outlined" fullWidth sx={{ mt: 2 }}>
                Write a Review
              </CustomButton>
            </CardContent>
          </Card>
        </motion.div>

        {/* Related Products */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Related Products
            </Typography>

            <Grid container spacing={3}>
              {listing.relatedProducts.map((product) => (
                <Grid item xs={12} sm={6} md={4} key={product.id}>
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        height: 140,
                        backgroundImage: `url(${getConsistentBackground(String(product.id))})`,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        position: 'relative',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.65)}, ${alpha(theme.palette.primary.dark, 0.7)})`,
                          zIndex: 1
                        }
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 10,
                          right: 10,
                          bgcolor: alpha(theme.palette.primary.main, 0.9),
                          color: 'white',
                          px: 1.5,
                          py: 0.5,
                          borderRadius: 1,
                          fontWeight: 'bold',
                          zIndex: 2
                        }}
                      >
                        R{product.price.toLocaleString()}
                      </Box>
                    </Box>
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h2" gutterBottom>
                        {product.name}
                      </Typography>
                      <CustomButton
                        size="small"
                        variant="outlined"
                        startIcon={<ShoppingCart />}
                        fullWidth
                        sx={{ mt: 2 }}
                      >
                        View Product
                      </CustomButton>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </motion.div>
      </motion.div>
    </Box>
  );
};

export default MarketplaceListingDetail;
