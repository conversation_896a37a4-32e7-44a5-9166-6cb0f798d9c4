// These imports are not currently used but may be needed for future dynamic data generation
// import { generateMockData, randomChoice, randomNumber } from '../utils/mockDataGenerator';

export interface Pasture {
  id: number;
  name: string;
  size: number;
  unit: string;
  location: string;
  soilType: string;
  forage: string[];
  waterSource: string[];
  fencingType: string;
  status: string;
  currentAnimals: number[];
  notes: string;
}

export const mockPastures: Pasture[] = [
  {
    id: 1,
    name: 'North Pasture',
    size: 25,
    unit: 'acres',
    location: 'North section of property',
    soilType: 'Loam',
    forage: ['Orchard Grass', 'White Clover', 'Ryegrass'],
    waterSource: ['Stream', 'Water trough'],
    fencingType: 'Electric',
    status: 'Active',
    currentAnimals: [1, 4, 5],
    notes: 'Good shade coverage on west side'
  },
  {
    id: 2,
    name: 'South Field',
    size: 30,
    unit: 'acres',
    location: 'South section of property',
    soilType: 'Clay loam',
    forage: ['Alfalfa', '<PERSON>', 'Bluegrass'],
    waterSource: ['Pond'],
    fencingType: 'Woven wire',
    status: 'Resting',
    currentAnimals: [],
    notes: 'Resting until May 1st for regrowth'
  }
];