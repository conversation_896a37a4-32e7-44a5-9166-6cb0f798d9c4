/**
 * Database Seeding Script
 *
 * This script populates the MongoDB database with sample data for testing and demonstration.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const models = require('../src/models');
const logger = require('../src/utils/logger');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const DB_NAME = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME
    });
    logger.info('Connected to MongoDB');
    return mongoose.connection;
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Seed users
async function seedUsers() {
  try {
    // Check if users already exist
    const userCount = await models.User.countDocuments();

    if (userCount > 0) {
      logger.info(`Users collection already has ${userCount} documents. Skipping seeding.`);
      return;
    }

    // Create admin user
    const adminPassword = await bcrypt.hash('Admin@123', 10);

    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'May',
        lastName: 'Rakgama',
        role: 'admin',
        permissions: ['all'],
        status: 'active',
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        password: await bcrypt.hash('Manager@123', 10),
        firstName: 'John',
        lastName: 'Doe',
        role: 'manager',
        permissions: [
          'view_animals', 'create_animal', 'update_animal',
          'view_health_records', 'create_health_record', 'update_health_record',
          'view_breeding_records', 'create_breeding_record', 'update_breeding_record',
          'view_transactions', 'create_transaction', 'update_transaction',
          'view_reports', 'create_reports', 'export_reports'
        ],
        status: 'active',
        lastLogin: null,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'staff',
        email: '<EMAIL>',
        password: await bcrypt.hash('Staff@123', 10),
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'staff',
        permissions: [
          'view_animals', 'update_animal',
          'view_health_records', 'create_health_record',
          'view_breeding_records',
          'view_transactions',
          'view_reports'
        ],
        status: 'active',
        lastLogin: null,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'vet',
        email: '<EMAIL>',
        password: await bcrypt.hash('Vet@123', 10),
        firstName: 'Sarah',
        lastName: 'Johnson',
        role: 'veterinarian',
        permissions: [
          'view_animals',
          'view_health_records', 'create_health_record', 'update_health_record',
          'view_breeding_records'
        ],
        status: 'active',
        lastLogin: null,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert users
    await models.User.insertMany(users);
    logger.info(`Inserted ${users.length} users`);
  } catch (error) {
    logger.error('Error seeding users:', error);
    throw error;
  }
}

// Seed animals
async function seedAnimals() {
  try {
    // Check if animals already exist
    const animalCount = await models.Animal.countDocuments();

    if (animalCount > 0) {
      logger.info(`Animals collection already has ${animalCount} documents. Skipping seeding.`);
      return;
    }

    // Get admin user
    const admin = await models.User.findOne({ username: 'admin' });

    if (!admin) {
      throw new Error('Admin user not found. Please seed users first.');
    }

    // Create animals
    const animals = [
      {
        tagNumber: 'CTL-2023-001',
        name: 'Themba',
        type: 'Cattle',
        species: 'Cattle',
        breed: 'Nguni',
        gender: 'male',
        birthDate: new Date('2023-01-15'),
        weight: 450,
        status: 'active',
        healthStatus: 'healthy',
        location: 'North Paddock',
        purchaseDate: new Date('2023-02-20'),
        purchasePrice: 8000,
        notes: 'Excellent breeding bull',
        rfidTag: 'RF001',
        imageUrl: '/images/animals/cattle-1.jpeg',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        tagNumber: 'CTL-2023-002',
        name: 'Nandi',
        type: 'Cattle',
        species: 'Cattle',
        breed: 'Nguni',
        gender: 'female',
        birthDate: new Date('2023-03-10'),
        weight: 380,
        status: 'active',
        healthStatus: 'healthy',
        location: 'North Paddock',
        purchaseDate: new Date('2023-04-15'),
        purchasePrice: 7500,
        notes: 'Good breeding cow',
        rfidTag: 'RF002',
        imageUrl: '/images/animals/cattle-2.avif',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        tagNumber: 'SHP-2023-001',
        name: 'Vusi',
        type: 'Sheep',
        species: 'Sheep',
        breed: 'Dorper',
        gender: 'male',
        birthDate: new Date('2023-02-05'),
        weight: 65,
        status: 'active',
        healthStatus: 'healthy',
        location: 'East Paddock',
        purchaseDate: new Date('2023-03-10'),
        purchasePrice: 1200,
        notes: 'Breeding ram',
        rfidTag: 'RF003',
        imageUrl: '/images/animals/sheep-1.jpeg',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        tagNumber: 'SHP-2023-002',
        name: 'Lerato',
        type: 'Sheep',
        species: 'Sheep',
        breed: 'Dorper',
        gender: 'female',
        birthDate: new Date('2023-02-20'),
        weight: 45,
        status: 'active',
        healthStatus: 'healthy',
        location: 'East Paddock',
        purchaseDate: new Date('2023-03-25'),
        purchasePrice: 1000,
        notes: 'Breeding ewe',
        rfidTag: 'RF004',
        imageUrl: '/images/animals/sheep-2.jpeg',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        tagNumber: 'GOT-2023-001',
        name: 'Sipho',
        type: 'Goat',
        species: 'Goat',
        breed: 'Boer',
        gender: 'male',
        birthDate: new Date('2023-01-25'),
        weight: 70,
        status: 'active',
        healthStatus: 'healthy',
        location: 'West Paddock',
        purchaseDate: new Date('2023-03-05'),
        purchasePrice: 1500,
        notes: 'Breeding buck',
        rfidTag: 'RF005',
        imageUrl: '/images/animals/goat-1.jpeg',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert animals
    await models.Animal.insertMany(animals);
    logger.info(`Inserted ${animals.length} animals`);

    return animals;
  } catch (error) {
    logger.error('Error seeding animals:', error);
    throw error;
  }
}

// Seed health records
async function seedHealthRecords(animals) {
  try {
    // Check if health records already exist
    const healthRecordCount = await models.HealthRecord.countDocuments();

    if (healthRecordCount > 0) {
      logger.info(`HealthRecord collection already has ${healthRecordCount} documents. Skipping seeding.`);
      return;
    }

    // Get animals if not provided
    if (!animals || animals.length === 0) {
      animals = await models.Animal.find();

      if (animals.length === 0) {
        throw new Error('No animals found. Please seed animals first.');
      }
    }

    // Get vet user
    const vet = await models.User.findOne({ role: 'veterinarian' });

    if (!vet) {
      throw new Error('Veterinarian user not found. Please seed users first.');
    }

    // Create health records
    const healthRecords = [
      {
        animal: animals[0]._id,
        recordType: 'checkup',
        date: new Date('2023-05-15'),
        performer: vet._id,
        diagnosis: 'Healthy',
        symptoms: [],
        medications: [],
        procedures: [
          {
            name: 'General checkup',
            notes: 'All vital signs normal',
            date: new Date('2023-05-15')
          }
        ],
        cost: 200,
        notes: 'Annual checkup',
        status: 'completed',
        priority: 'medium',
        createdBy: vet._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        animal: animals[1]._id,
        recordType: 'vaccination',
        date: new Date('2023-06-10'),
        performer: vet._id,
        diagnosis: '',
        symptoms: [],
        medications: [
          {
            name: 'Bovine Viral Diarrhea Vaccine',
            dosage: '5ml',
            frequency: 'once',
            startDate: new Date('2023-06-10'),
            endDate: new Date('2023-06-10')
          }
        ],
        procedures: [
          {
            name: 'Vaccination',
            notes: 'Standard vaccination protocol',
            date: new Date('2023-06-10')
          }
        ],
        cost: 150,
        notes: 'Annual vaccination',
        status: 'completed',
        priority: 'high',
        createdBy: vet._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        animal: animals[2]._id,
        recordType: 'treatment',
        date: new Date('2023-07-05'),
        performer: vet._id,
        diagnosis: 'Minor wound',
        symptoms: ['Limping', 'Small cut on front leg'],
        medications: [
          {
            name: 'Antibiotic ointment',
            dosage: 'Apply twice daily',
            frequency: 'twice daily',
            startDate: new Date('2023-07-05'),
            endDate: new Date('2023-07-12')
          }
        ],
        procedures: [
          {
            name: 'Wound cleaning',
            notes: 'Cleaned and disinfected wound',
            date: new Date('2023-07-05')
          }
        ],
        cost: 100,
        notes: 'Minor injury, should heal within a week',
        status: 'completed',
        priority: 'medium',
        createdBy: vet._id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert health records
    await models.HealthRecord.insertMany(healthRecords);
    logger.info(`Inserted ${healthRecords.length} health records`);
  } catch (error) {
    logger.error('Error seeding health records:', error);
    throw error;
  }
}

// Seed breeding records
async function seedBreedingRecords(animals) {
  try {
    // Check if breeding records already exist
    const breedingRecordCount = await models.BreedingRecord.countDocuments();

    if (breedingRecordCount > 0) {
      logger.info(`BreedingRecord collection already has ${breedingRecordCount} documents. Skipping seeding.`);
      return;
    }

    // Get animals if not provided
    if (!animals || animals.length === 0) {
      animals = await models.Animal.find();

      if (animals.length === 0) {
        throw new Error('No animals found. Please seed animals first.');
      }
    }

    // Get admin user
    const admin = await models.User.findOne({ username: 'admin' });

    if (!admin) {
      throw new Error('Admin user not found. Please seed users first.');
    }

    // Find male and female cattle
    const maleCattle = animals.find(animal => animal.type === 'Cattle' && animal.gender === 'male');
    const femaleCattle = animals.find(animal => animal.type === 'Cattle' && animal.gender === 'female');

    if (!maleCattle || !femaleCattle) {
      throw new Error('Male and female cattle not found. Please seed animals first.');
    }

    // Create breeding records
    const breedingRecords = [
      {
        maleAnimal: maleCattle._id,
        femaleAnimal: femaleCattle._id,
        breedingDate: new Date('2023-08-15'),
        method: 'natural',
        location: 'North Paddock',
        status: 'confirmed',
        confirmedPregnant: true,
        confirmationDate: new Date('2023-09-10'),
        confirmationMethod: 'Veterinary examination',
        expectedDueDate: new Date('2024-05-25'),
        notes: 'Successful breeding',
        performer: admin._id,
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert breeding records
    await models.BreedingRecord.insertMany(breedingRecords);
    logger.info(`Inserted ${breedingRecords.length} breeding records`);

    return breedingRecords;
  } catch (error) {
    logger.error('Error seeding breeding records:', error);
    throw error;
  }
}

// Seed birth records
async function seedBirthRecords(animals, breedingRecords) {
  try {
    // Check if birth records already exist
    const birthRecordCount = await models.BirthRecord.countDocuments();

    if (birthRecordCount > 0) {
      logger.info(`BirthRecord collection already has ${birthRecordCount} documents. Skipping seeding.`);
      return;
    }

    // Get animals if not provided
    if (!animals || animals.length === 0) {
      animals = await models.Animal.find();

      if (animals.length === 0) {
        throw new Error('No animals found. Please seed animals first.');
      }
    }

    // Get breeding records if not provided
    if (!breedingRecords || breedingRecords.length === 0) {
      breedingRecords = await models.BreedingRecord.find();

      if (breedingRecords.length === 0) {
        throw new Error('No breeding records found. Please seed breeding records first.');
      }
    }

    // Get admin user
    const admin = await models.User.findOne({ username: 'admin' });

    if (!admin) {
      throw new Error('Admin user not found. Please seed users first.');
    }

    // Find female cattle
    const femaleCattle = animals.find(animal => animal.type === 'Cattle' && animal.gender === 'female');
    const maleCattle = animals.find(animal => animal.type === 'Cattle' && animal.gender === 'male');

    if (!femaleCattle || !maleCattle) {
      throw new Error('Female and male cattle not found. Please seed animals first.');
    }

    // Create birth records
    const birthRecords = [
      {
        mother: femaleCattle._id,
        father: maleCattle._id,
        birthDate: new Date('2023-05-25'),
        location: 'North Paddock',
        birthType: 'natural',
        assistance: 'None',
        complications: 'None',
        breedingRecord: breedingRecords[0]._id,
        offspring: [],
        totalOffspring: 1,
        aliveOffspring: 1,
        notes: 'Healthy calf born',
        performer: admin._id,
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert birth records
    await models.BirthRecord.insertMany(birthRecords);
    logger.info(`Inserted ${birthRecords.length} birth records`);
  } catch (error) {
    logger.error('Error seeding birth records:', error);
    throw error;
  }
}

// Seed transactions
async function seedTransactions() {
  try {
    // Check if transactions already exist
    const transactionCount = await models.Transaction.countDocuments();

    if (transactionCount > 0) {
      logger.info(`Transaction collection already has ${transactionCount} documents. Skipping seeding.`);
      return;
    }

    // Get admin user
    const admin = await models.User.findOne({ username: 'admin' });

    if (!admin) {
      throw new Error('Admin user not found. Please seed users first.');
    }

    // Create transactions
    const transactions = [
      {
        transactionDate: new Date('2023-04-15'),
        type: 'expense',
        category: 'Feed',
        subcategory: 'Hay',
        amount: 2500,
        currency: 'ZAR',
        description: 'Purchase of hay for cattle',
        paymentMethod: 'Bank Transfer',
        reference: 'INV-2023-001',
        vendor: 'Farm Supplies Ltd',
        status: 'completed',
        notes: 'Monthly feed purchase',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        transactionDate: new Date('2023-05-10'),
        type: 'expense',
        category: 'Veterinary',
        subcategory: 'Checkup',
        amount: 1200,
        currency: 'ZAR',
        description: 'Veterinary services for cattle',
        paymentMethod: 'Cash',
        reference: 'INV-2023-002',
        vendor: 'Animal Health Clinic',
        status: 'completed',
        notes: 'Regular checkup for all animals',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        transactionDate: new Date('2023-06-20'),
        type: 'income',
        category: 'Sales',
        subcategory: 'Livestock',
        amount: 15000,
        currency: 'ZAR',
        description: 'Sale of 2 cattle',
        paymentMethod: 'Bank Transfer',
        reference: 'SALE-2023-001',
        status: 'completed',
        notes: 'Sale to local farmer',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        transactionDate: new Date('2023-07-05'),
        type: 'expense',
        category: 'Equipment',
        subcategory: 'Maintenance',
        amount: 3500,
        currency: 'ZAR',
        description: 'Repair of fencing',
        paymentMethod: 'Bank Transfer',
        reference: 'INV-2023-003',
        vendor: 'Farm Services',
        status: 'completed',
        notes: 'Repair of damaged fencing in north paddock',
        createdBy: admin._id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insert transactions
    await models.Transaction.insertMany(transactions);
    logger.info(`Inserted ${transactions.length} transactions`);
  } catch (error) {
    logger.error('Error seeding transactions:', error);
    throw error;
  }
}

// Main function to seed all data
async function seedAll() {
  try {
    // Connect to MongoDB
    await connectDB();

    // Seed data
    await seedUsers();
    const animals = await seedAnimals();
    await seedHealthRecords(animals);
    const breedingRecords = await seedBreedingRecords(animals);
    await seedBirthRecords(animals, breedingRecords);
    await seedTransactions();

    logger.info('All data seeded successfully');

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');

    process.exit(0);
  } catch (error) {
    logger.error('Error seeding data:', error);

    // Disconnect from MongoDB
    try {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    } catch (disconnectError) {
      logger.error('Error disconnecting from MongoDB:', disconnectError);
    }

    process.exit(1);
  }
}

// Run the seeding function
seedAll();
