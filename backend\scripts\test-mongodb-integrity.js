/**
 * MongoDB Data Integrity Test Script
 *
 * This script tests the MongoDB connection and verifies data integrity across collections.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  tlsAllowInvalidCertificates: true, // For development only
  tlsAllowInvalidHostnames: true, // For development only
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000, // 30 seconds timeout
  socketTimeoutMS: 45000, // 45 seconds timeout
});

/**
 * Test MongoDB connection and data integrity
 */
async function testMongoDBIntegrity() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database
    const db = client.db(dbName);

    // Get all collections
    const collections = await db.listCollections().toArray();
    logger.info(`Found ${collections.length} collections in database`);

    // Check collection counts
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      logger.info(`Collection ${collection.name} has ${count} documents`);

      if (count === 0) {
        logger.warn(`Collection ${collection.name} is empty!`);
      }
    }

    // Check relationships between collections
    logger.info('Checking relationships between collections...');

    // Check if animals collection exists
    if (collections.some(c => c.name === 'animals')) {
      const animals = await db.collection('animals').find({}).toArray();
      logger.info(`Found ${animals.length} animals`);

      // Check health records
      if (collections.some(c => c.name === 'health_records')) {
        const healthRecords = await db.collection('health_records').find({}).toArray();
        logger.info(`Found ${healthRecords.length} health records`);

        // Check if health records reference valid animals
        const invalidHealthRecords = healthRecords.filter(record =>
          !animals.some(animal => animal.id === record.animalId)
        );

        if (invalidHealthRecords.length > 0) {
          logger.warn(`Found ${invalidHealthRecords.length} health records with invalid animal references`);
        } else {
          logger.info('All health records reference valid animals');
        }
      }

      // Check breeding records
      if (collections.some(c => c.name === 'breeding_records')) {
        const breedingRecords = await db.collection('breeding_records').find({}).toArray();
        logger.info(`Found ${breedingRecords.length} breeding records`);

        // Check if breeding records reference valid animals
        const invalidBreedingRecords = breedingRecords.filter(record =>
          !animals.some(animal => animal.id === record.femaleId) ||
          !animals.some(animal => animal.id === record.maleId)
        );

        if (invalidBreedingRecords.length > 0) {
          logger.warn(`Found ${invalidBreedingRecords.length} breeding records with invalid animal references`);
        } else {
          logger.info('All breeding records reference valid animals');
        }
      }
    }

    // Check business analysis collections
    if (collections.some(c => c.name === 'business_kpis')) {
      const kpis = await db.collection('business_kpis').find({}).toArray();
      logger.info(`Found ${kpis.length} business KPIs`);

      // Check if KPIs have valid categories
      const validCategories = ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'];
      const invalidKPIs = kpis.filter(kpi => !validCategories.includes(kpi.category));

      if (invalidKPIs.length > 0) {
        logger.warn(`Found ${invalidKPIs.length} KPIs with invalid categories`);
      } else {
        logger.info('All KPIs have valid categories');
      }
    }

    // Check strategic goals and initiatives
    if (collections.some(c => c.name === 'strategic_goals') && collections.some(c => c.name === 'strategic_initiatives')) {
      const goals = await db.collection('strategic_goals').find({}).toArray();
      const initiatives = await db.collection('strategic_initiatives').find({}).toArray();

      logger.info(`Found ${goals.length} strategic goals and ${initiatives.length} strategic initiatives`);

      // Check if initiatives reference valid goals
      const invalidInitiatives = initiatives.filter(initiative =>
        initiative.relatedGoalIds && initiative.relatedGoalIds.some(goalId =>
          !goals.some(goal => goal.id === goalId)
        )
      );

      if (invalidInitiatives.length > 0) {
        logger.warn(`Found ${invalidInitiatives.length} initiatives with invalid goal references`);
      } else {
        logger.info('All initiatives reference valid goals');
      }
    }

    logger.info('MongoDB data integrity check completed');
  } catch (error) {
    logger.error('Error testing MongoDB integrity:', error);
  } finally {
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the test
testMongoDBIntegrity();
