import React, { ReactNode } from 'react';
import {
  Box,
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Table,
  TableContainer,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  useTheme,
  alpha
} from '@mui/material';
import { motion } from 'framer-motion';

// Animation presets
export const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.3 }
  },
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
    transition: { duration: 0.3 }
  },
  slideIn: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3 }
  },
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.3 }
  }
};

// Enhanced Page Container
interface EnhancedPageContainerProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  moduleType?: 'animals' | 'health' | 'breeding' | 'feeding' | 'commercial' | 'compliance';
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  backgroundImage?: string;
}

export const EnhancedPageContainer: React.FC<EnhancedPageContainerProps> = ({
  children,
  title,
  subtitle,
  moduleType = 'animals',
  maxWidth = 'lg',
  backgroundImage
}) => {
  const theme = useTheme();

  const getModuleColor = () => {
    switch (moduleType) {
      case 'animals': return theme.palette.primary.main;
      case 'health': return theme.palette.success.main;
      case 'breeding': return theme.palette.info.main;
      case 'feeding': return theme.palette.warning.main;
      case 'commercial': return theme.palette.secondary.main;
      case 'compliance': return theme.palette.error.main;
      default: return theme.palette.primary.main;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        pb: 4,
        ...(backgroundImage && {
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.05,
            zIndex: 0
          }
        })
      }}
    >
      {(title || subtitle) && (
        <Box
          sx={{
            py: { xs: 3, sm: 4, md: 5 },
            mb: { xs: 3, sm: 4, md: 5 },
            background: `linear-gradient(45deg, ${getModuleColor()}, ${alpha(getModuleColor(), 0.7)})`,
            color: 'white',
            position: 'relative',
            zIndex: 1
          }}
        >
          <Container maxWidth={maxWidth}>
            <motion.div {...animations.slideUp}>
              {title && (
                <Typography
                  variant="h4"
                  component="h1"
                  fontWeight="bold"
                  gutterBottom
                  sx={{
                    fontSize: { xs: '1.75rem', sm: '2rem', md: '2.25rem' },
                    lineHeight: 1.2
                  }}
                >
                  {title}
                </Typography>
              )}
              {subtitle && (
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                    lineHeight: 1.4,
                    maxWidth: { sm: '80%', md: '70%' }
                  }}
                >
                  {subtitle}
                </Typography>
              )}
            </motion.div>
          </Container>
        </Box>
      )}
      <Container maxWidth={maxWidth} sx={{ position: 'relative', zIndex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Box sx={{ width: '100%', maxWidth: '1200px' }}>
          {children}
        </Box>
      </Container>
    </Box>
  );
};

// Enhanced Card
interface EnhancedCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'outlined' | 'glass';
  backgroundImage?: string;
  accentColor?: string;
  delay?: number;
  height?: string | number;
  onClick?: () => void;
  sx?: any;
}

export const EnhancedCard: React.FC<EnhancedCardProps> = ({
  children,
  title,
  subtitle,
  variant = 'default',
  backgroundImage,
  accentColor,
  delay = 0,
  height = 'auto',
  onClick,
  sx = {}
}) => {
  const theme = useTheme();

  const getCardStyles = () => {
    const baseStyles = {
      height,
      borderRadius: '12px',
      overflow: 'hidden',
      position: 'relative',
      cursor: onClick ? 'pointer' : 'default',
      transition: 'all 0.3s ease',
      '&:hover': onClick ? {
        transform: 'translateY(-5px)',
        boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
      } : {}
    };

    switch (variant) {
      case 'outlined':
        return {
          ...baseStyles,
          border: `1px solid ${theme.palette.divider}`,
          boxShadow: 'none',
          ...(accentColor && {
            borderTop: `3px solid ${accentColor}`
          })
        };
      case 'glass':
        return {
          ...baseStyles,
          background: alpha(theme.palette.background.paper, 0.7),
          backdropFilter: 'blur(10px)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          ...(accentColor && {
            borderLeft: `3px solid ${accentColor}`
          })
        };
      default:
        return {
          ...baseStyles,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: theme.palette.background.paper,
          ...(accentColor && {
            borderBottom: `3px solid ${accentColor}`
          })
        };
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
    >
      <Card sx={{ ...getCardStyles(), ...sx }} onClick={onClick}>
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.1,
              zIndex: 0
            }}
          />
        )}
        {(title || subtitle) && (
          <Box
            sx={{
              p: 2,
              borderBottom: `1px solid ${theme.palette.divider}`,
              position: 'relative',
              zIndex: 1
            }}
          >
            {title && (
              <Typography variant="h6" fontWeight="bold">
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        )}
        <Box sx={{ position: 'relative', zIndex: 1 }}>
          {children}
        </Box>
      </Card>
    </motion.div>
  );
};

// Enhanced Table
interface EnhancedTableProps {
  columns: {
    id: string;
    label: string;
    align?: 'left' | 'right' | 'center';
    minWidth?: number;
    format?: (value: any) => React.ReactNode;
  }[];
  rows: any[];
  title?: string;
  subtitle?: string;
  onRowClick?: (row: any) => void;
  maxHeight?: string | number;
}

export const EnhancedTable: React.FC<EnhancedTableProps> = ({
  columns,
  rows,
  title,
  subtitle,
  onRowClick,
  maxHeight
}) => {
  const theme = useTheme();

  return (
    <EnhancedCard title={title} subtitle={subtitle}>
      <TableContainer sx={{ maxHeight }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                  sx={{
                    fontWeight: 'bold',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, index) => (
              <TableRow
                hover
                key={row.id || index}
                onClick={onRowClick ? () => onRowClick(row) : undefined}
                sx={{ cursor: onRowClick ? 'pointer' : 'default' }}
              >
                {columns.map((column) => {
                  const value = row[column.id];
                  return (
                    <TableCell key={column.id} align={column.align || 'left'}>
                      {column.format ? column.format(value) : value}
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </EnhancedCard>
  );
};

// Enhanced Grid
interface EnhancedGridProps {
  children: ReactNode;
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  spacing?: number;
  sx?: any;
}

export const EnhancedGrid: React.FC<EnhancedGridProps> = ({
  children,
  columns = { xs: 12, sm: 6, md: 4 },
  spacing = 2,
  sx = {}
}) => {
  return (
    <Grid
      container
      spacing={spacing}
      justifyContent="center"
      alignItems="stretch"
      sx={{
        width: '100%',
        margin: '0 auto',
        ...sx
      }}
    >
      {React.Children.map(children, (child) => (
        <Grid
          item
          xs={columns.xs}
          sm={columns.sm}
          md={columns.md}
          lg={columns.lg}
          xl={columns.xl}
          sx={{ display: 'flex', flexDirection: 'column' }}
        >
          {child}
        </Grid>
      ))}
    </Grid>
  );
};

// Enhanced Section
interface EnhancedSectionProps {
  children: ReactNode;
  backgroundImage?: string;
  height?: string | number;
  parallax?: boolean;
  overlayColor?: string;
  sx?: any;
}

export const EnhancedSection: React.FC<EnhancedSectionProps> = ({
  children,
  backgroundImage,
  height = 300,
  parallax = false,
  overlayColor = 'rgba(0,0,0,0.5)',
  sx = {}
}) => {
  return (
    <Box
      sx={{
        position: 'relative',
        height,
        mb: 4,
        overflow: 'hidden',
        borderRadius: '12px',
        ...sx
      }}
    >
      {backgroundImage && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundAttachment: parallax ? 'fixed' : 'scroll',
            zIndex: 0
          }}
        />
      )}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: overlayColor,
          zIndex: 1
        }}
      />
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          height: '100%',
          p: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

// Export as default object
const DesignSystem = {
  EnhancedPageContainer,
  EnhancedCard,
  EnhancedTable,
  EnhancedGrid,
  EnhancedSection,
  animations
};

export default DesignSystem;
