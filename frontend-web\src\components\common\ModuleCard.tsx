import React from 'react';
import { Card, CardContent, CardProps, Typography, Box, useTheme, alpha } from '@mui/material';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';
import { moduleBackgrounds, getAllModuleImages } from '../../utils/localImages';
import RotatingBackground from './RotatingBackground';
import { isExcludedModule } from '../../utils/applyDesignPattern';

interface ModuleCardProps extends CardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  to: string;
  module: string;
}

/**
 * A card component for module navigation with rotating background images
 */
const ModuleCard: React.FC<ModuleCardProps> = ({
  title,
  description,
  icon,
  to,
  module,
  sx = {},
  ...props
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Apply metallic blue color scheme for non-excluded modules
  const isExcluded = isExcludedModule(module);
  const METALLIC_BLUE = {
    primary: '#4A6FA5',
    secondary: '#3A5A8C',
  };

  // Use metallic blue for non-excluded modules
  const moduleColor = isExcluded ? themeColor : {
    primary: METALLIC_BLUE.primary,
    secondary: METALLIC_BLUE.secondary
  };

  // Get module images
  const moduleKey = module.toLowerCase().replace('/', '') as keyof typeof moduleBackgrounds;
  const images = moduleBackgrounds[moduleKey]
    ? getAllModuleImages(moduleKey)
    : [moduleBackgrounds.animals.main]; // Fallback to animals

  // Animation variants
  const cardVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { y: 0, opacity: 1, transition: { duration: 0.5 } },
    hover: { y: -5, boxShadow: theme.shadows[10], transition: { duration: 0.3 } }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      style={{ height: '100%' }}
    >
      <Card
        component={Link}
        to={to}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '12px',
          overflow: 'hidden',
          textDecoration: 'none',
          color: 'inherit',
          transition: 'all 0.3s ease',
          border: `1px solid ${alpha(moduleColor.primary, 0.1)}`,
          '&:hover': {
            color: moduleColor.primary
          },
          ...sx
        }}
        {...props}
      >
        {/* Card Header with Rotating Background */}
        <Box sx={{ height: '120px', position: 'relative', overflow: 'hidden' }}>
          <RotatingBackground
            images={images}
            interval={10000}
            primaryColor={moduleColor.primary}
            secondaryColor={moduleColor.secondary}
            opacity={0.85}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                width: '100%',
                p: 2
              }}
            >
              {icon && (
                <Box
                  sx={{
                    fontSize: '3rem',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {icon}
                </Box>
              )}
            </Box>
          </RotatingBackground>
        </Box>

        {/* Card Content */}
        <CardContent sx={{ flexGrow: 1, p: 3 }}>
          <Typography variant="h6" component="h3" gutterBottom fontWeight="bold">
            {title}
          </Typography>
          {description && (
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ModuleCard;
