import React, { ReactNode } from 'react';
import { Box, Grid, Typography, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks';
import { ModuleHeader } from './';

interface DashboardSection {
  id: string;
  title?: string;
  subtitle?: string;
  fullWidth?: boolean;
  children: ReactNode;
}

interface ResponsiveDashboardProps {
  title: string;
  subtitle?: string;
  module: string;
  backgroundImage?: string;
  sections: DashboardSection[];
  headerAction?: {
    label: string;
    icon?: ReactNode;
    onClick: () => void;
  };
  breadcrumbs?: Array<{label: string; path?: string}>;
}

/**
 * ResponsiveDashboard component
 * Provides a consistent, responsive layout for dashboard pages
 */
const ResponsiveDashboard: React.FC<ResponsiveDashboardProps> = ({
  title,
  subtitle,
  module,
  backgroundImage,
  sections,
  headerAction,
  breadcrumbs
}) => {
  const theme = useTheme();
  const { isMobile } = useResponsive();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box sx={{ minHeight: '100%' }}>
      {/* Module Header */}
      <ModuleHeader
        title={title}
        subtitle={subtitle}
        module={module}
        actionLabel={headerAction?.label}
        actionIcon={headerAction?.icon}
        onAction={headerAction?.onClick}
        breadcrumbs={breadcrumbs}
      />

      {/* Dashboard Content */}
      <Box
        sx={{
          px: { xs: 2, sm: 3, md: 4 },
          pb: { xs: 4, sm: 5 },
          position: 'relative'
        }}
      >
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {sections.map((section) => (
            <motion.div key={section.id} variants={itemVariants}>
              {/* Optional Section Header */}
              {section.title && (
                <Box sx={{ mb: 2, mt: 4 }}>
                  <Typography 
                    variant="h5" 
                    component="h2"
                    sx={{
                      fontWeight: 600,
                      fontSize: { xs: '1.25rem', sm: '1.35rem', md: '1.5rem' },
                      color: theme.palette.text.primary
                    }}
                  >
                    {section.title}
                  </Typography>
                  {section.subtitle && (
                    <Typography 
                      variant="body1" 
                      color="text.secondary"
                      sx={{ mt: 0.5 }}
                    >
                      {section.subtitle}
                    </Typography>
                  )}
                </Box>
              )}

              {/* Section Content */}
              <Box sx={{ mb: 4 }}>
                {section.fullWidth ? (
                  <Box>{section.children}</Box>
                ) : (
                  <Grid container spacing={3}>
                    {React.Children.map(section.children, (child) => (
                      <Grid item xs={12} md={6} lg={4}>
                        {child}
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            </motion.div>
          ))}
        </motion.div>
      </Box>
    </Box>
  );
};

export default ResponsiveDashboard;
