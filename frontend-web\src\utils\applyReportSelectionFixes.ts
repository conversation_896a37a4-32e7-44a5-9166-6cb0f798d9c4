/**
 * Utility functions to apply selection fixes to report items
 */

/**
 * Apply selection fixes to all report items
 * This function can be called from any component to ensure proper report item selection
 */
export const applyReportSelectionFixes = () => {
  try {
    // Find all report card elements in the DOM
    const reportCardElements = document.querySelectorAll('.MuiCard-root');
    const tableRowElements = document.querySelectorAll('.MuiTableRow-root');
    const buttonElements = document.querySelectorAll('button');
    const iconButtonElements = document.querySelectorAll('.MuiIconButton-root');
    
    // Apply styles to report card elements
    reportCardElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '5', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });
    
    // Apply styles to table row elements
    tableRowElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '5', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });
    
    // Apply styles to button elements
    buttonElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });
    
    // Apply styles to icon button elements
    iconButtonElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });
  } catch (error) {
    console.error('Error applying report selection fixes:', error);
  }
};

/**
 * Apply selection fixes to a specific report item
 * @param element - The DOM element to apply fixes to
 */
export const applyReportSelectionFixesToElement = (element: HTMLElement) => {
  try {
    // Apply styles to the element
    element.style.setProperty('position', 'relative', 'important');
    element.style.setProperty('z-index', '5', 'important');
    element.style.setProperty('pointer-events', 'auto', 'important');
    
    // Find all button elements in the element
    const buttonElements = element.querySelectorAll('button');
    
    // Apply styles to button elements
    buttonElements.forEach(buttonElement => {
      (buttonElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (buttonElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (buttonElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (buttonElement as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });
    
    // Find all icon button elements in the element
    const iconButtonElements = element.querySelectorAll('.MuiIconButton-root');
    
    // Apply styles to icon button elements
    iconButtonElements.forEach(iconButtonElement => {
      (iconButtonElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (iconButtonElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (iconButtonElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (iconButtonElement as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });
  } catch (error) {
    console.error('Error applying report selection fixes to element:', error);
  }
};

export default {
  applyReportSelectionFixes,
  applyReportSelectionFixesToElement
};
