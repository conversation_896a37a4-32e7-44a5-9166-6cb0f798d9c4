import React from 'react';
import { CustomButton } from '../../components/common';
import { Link, useNavigate } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { IconButton, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper, Box, Grid, Typography, useTheme, alpha } from '@mui/material';
import { Add, Visibility, Edit, Restaurant } from '../../../utils/iconImports';
import { motion } from 'framer-motion';
import { ModuleMetric, QuickAction } from '../../ModuleDashboard';
import ModuleHeader from '../../common/ModuleHeader';
import AnimatedBackgroundCard from '../../common/AnimatedBackgroundCard';
import { useResponsive } from '../../../hooks/useResponsive';

const FeedManagementDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { isMobile, isTablet } = useResponsive();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const metrics: ModuleMetric[] = [
    { label: 'Feed Stock', value: '85%', trend: '-5%', status: 'neutral' },
    { label: 'Feed Cost', value: 'R1,250/month', trend: '+2%', status: 'negative' },
    { label: 'Feed Efficiency', value: '1.8', trend: '+0.2', status: 'positive' },
    { label: 'Upcoming Orders', value: '3', trend: 'neutral', status: 'neutral' }
  ];

  const quickActions: QuickAction[] = [
    { name: 'Add Feed Record', icon: '🌾', path: `${ROUTES.FEED_MANAGEMENT}/record` },
    { name: 'Feed Schedule', icon: '📅', path: `${ROUTES.FEED_MANAGEMENT}/schedule` },
    { name: 'Order Feed', icon: '🛒', path: `${ROUTES.FEED_MANAGEMENT}/order` },
    { name: 'Feed Analysis', icon: '📊', path: `${ROUTES.FEED_MANAGEMENT}/analysis` }
  ];

  // Mock feed records
  const feedRecords = [
    { id: 'FR001', type: 'Hay', quantity: '500kg', date: '2024-04-01', cost: 'R450', supplier: 'Farm Supplies Inc.' },
    { id: 'FR002', type: 'Grain Mix', quantity: '200kg', date: '2024-04-03', cost: 'R320', supplier: 'Nutrition Plus' },
    { id: 'FR003', type: 'Silage', quantity: '800kg', date: '2024-04-05', cost: 'R380', supplier: 'Green Valley Farms' },
    { id: 'FR004', type: 'Protein Supplement', quantity: '50kg', date: '2024-04-07', cost: 'R210', supplier: 'Animal Nutrition Co.' },
    { id: 'FR005', type: 'Mineral Mix', quantity: '25kg', date: '2024-04-10', cost: 'R180', supplier: 'Farm Supplies Inc.' }
  ];

  return (
    <Box sx={{ width: '100%' }}>
      {/* Module Header */}
      <ModuleHeader
        title="Feed Management"
        subtitle="Monitor and manage your livestock feed resources efficiently"
        module="feed"
        actionLabel="Add Feed Record"
        actionIcon={<Add />}
        onAction={() => navigate(`${ROUTES.FEED_MANAGEMENT}/record`)}
        icon={<Restaurant />}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Metrics Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
            Feed Statistics
          </Typography>

          <Grid container spacing={3} mb={4}>
            {metrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={metric.label}>
                <motion.div variants={itemVariants} transition={{ delay: index * 0.1 }}>
                  <AnimatedBackgroundCard
                    title={metric.label}
                    backgroundImage="https://images.unsplash.com/photo-1605152276897-4f618f831968?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                    accentColor={theme.palette.primary.main}
                    secondaryColor={theme.palette.primary.dark}
                    module="feed"
                    uniqueId={`feed-metric-${index}`}
                    height={140}
                    delay={index * 0.1}
                    overlay="gradient"
                  >
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="h4" fontWeight="bold">
                        {metric.value}
                      </Typography>
                      <Box
                        sx={{
                          display: 'inline-block',
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          mt: 1,
                          bgcolor:
                            metric.status === 'positive' ? 'success.100' :
                            metric.status === 'negative' ? 'error.100' :
                            'grey.100',
                          color:
                            metric.status === 'positive' ? 'success.800' :
                            metric.status === 'negative' ? 'error.800' :
                            'grey.800',
                        }}
                      >
                        <Typography variant="caption" fontWeight="medium">
                          {metric.trend}
                        </Typography>
                      </Box>
                    </Box>
                  </AnimatedBackgroundCard>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Quick Actions */}
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Quick Actions
          </Typography>

          <Grid container spacing={3} mb={4}>
            {quickActions.map((action, index) => (
              <Grid item xs={6} sm={3} key={action.name}>
                <motion.div variants={itemVariants} transition={{ delay: 0.3 + index * 0.1 }}>
                  <AnimatedBackgroundCard
                    title={action.name}
                    backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
                    icon={<Typography variant="h3">{action.icon}</Typography>}
                    accentColor={theme.palette.secondary.main}
                    secondaryColor={theme.palette.secondary.dark}
                    module="feed"
                    uniqueId={`feed-action-${index}`}
                    height={120}
                    delay={0.3 + index * 0.1}
                    onAction={() => navigate(action.path)}
                    overlay="light"
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Recent Feed Records */}
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Recent Feed Records
          </Typography>

          <motion.div variants={itemVariants} transition={{ delay: 0.7 }}>
            <AnimatedBackgroundCard
              title="Feed Records"
              module="feed"
              uniqueId="feed-records"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              height="auto"
              delay={0.7}
              overlay="gradient"
            >
              <Box sx={{ mt: 2, overflow: 'auto' }}>
                <TableContainer component={Paper} sx={{
                  boxShadow: 'none',
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  borderRadius: 2,
                  overflow: 'auto',
                  maxWidth: '100%'
                }}>
                  <Table sx={{ minWidth: isMobile ? 650 : 800 }}>
                    <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                      <TableRow>
                        <TableCell><strong>ID</strong></TableCell>
                        <TableCell><strong>Feed Type</strong></TableCell>
                        <TableCell><strong>Quantity</strong></TableCell>
                        <TableCell><strong>Date</strong></TableCell>
                        <TableCell><strong>Cost</strong></TableCell>
                        {!isMobile && <TableCell><strong>Supplier</strong></TableCell>}
                        <TableCell align="right"><strong>Actions</strong></TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {feedRecords.map((record) => (
                        <TableRow key={record.id} hover>
                          <TableCell>{record.id}</TableCell>
                          <TableCell>{record.type}</TableCell>
                          <TableCell>{record.quantity}</TableCell>
                          <TableCell>{record.date}</TableCell>
                          <TableCell>{record.cost}</TableCell>
                          {!isMobile && <TableCell>{record.supplier}</TableCell>}
                          <TableCell align="right">
                            <IconButton size="small" color="primary" sx={{ mr: 1 }}>
                              <Visibility fontSize="small" />
                            </IconButton>
                            <IconButton size="small" color="secondary">
                              <Edit fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    component={Link}
                    to={`${ROUTES.FEED_MANAGEMENT}/records`}
                    sx={{ borderRadius: 2 }}
                  >
                    View All Records
                  </CustomButton>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </motion.div>
      </Box>
    </Box>
  );
};

export default FeedManagementDashboard;