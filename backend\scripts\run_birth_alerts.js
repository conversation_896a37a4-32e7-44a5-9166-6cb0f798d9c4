/**
 * Run Birth Alerts
 *
 * This script runs the birth alert system for the specified animal
 * It's designed to be run from the command line
 */

const ruralNotificationService = require('../services/ruralNotificationService');

// Command line arguments
const args = process.argv.slice(2);
const animalId = args[0] || 'CTL001';
const animalName = args[1] || 'Bella';
const daysRemaining = parseInt(args[2] || '1', 10);

// Use additional phone numbers instead of primary
const phoneNumber = args[3] || '0761246783';
const additionalPhones = ['0828808204', '0766535887'];

// Animal data with Sotho, Tswana, and Afrikaans names
const animals = {
  'CTL001': {
    id: 'CTL001',
    name: 'Tshepiso',
    dueDate: new Date('2024-07-15'),
    daysRemaining: 45,
    riskLevel: 'high',
    specialCare: [
      'Regular monitoring',
      'Balanced nutrition',
      'Ensure calm environment'
    ]
  },
  'CTL002': {
    id: 'CTL002',
    name: '<PERSON><PERSON>',
    dueDate: new Date('2024-06-18'),
    daysRemaining: 18,
    riskLevel: 'medium-high',
    specialCare: [
      'Monitor vital signs',
      'Supplemental nutrition',
      'Reduce stress'
    ]
  },
  'CTL003': {
    id: 'CTL003',
    name: 'Pule',
    dueDate: new Date('2024-08-18'),
    daysRemaining: 79,
    riskLevel: 'low',
    specialCare: [
      'Regular checkups',
      'Standard care protocol'
    ]
  }
};

/**
 * Run the birth alert for a specific animal
 */
async function runBirthAlert() {
  // Get animal data
  const animal = animals[animalId] || {
    id: animalId,
    name: animalName,
    dueDate: new Date(Date.now() + daysRemaining * 24 * 60 * 60 * 1000),
    daysRemaining: daysRemaining,
    riskLevel: daysRemaining <= 3 ? 'high' : daysRemaining <= 7 ? 'medium' : 'low',
    specialCare: [
      'Regular monitoring',
      'Ensure clean water supply',
      'Prepare calving area'
    ]
  };

  console.log('=== AMPD Livestock Birth Alert System ===');
  console.log(`Running birth alert for ${animal.name} (${animal.id})`);
  console.log(`Due date: ${animal.dueDate.toLocaleDateString()}`);
  console.log(`Days remaining: ${animal.daysRemaining}`);
  console.log(`Risk level: ${animal.riskLevel}`);
  console.log(`Special care: ${animal.specialCare.join(', ')}`);
  console.log(`Sending to: ${phoneNumber}`);
  console.log('');

  // Force all notification types for testing
  const notificationTypes = ['sms', 'voice', 'whatsapp'];

  // Override days remaining for testing critical alerts
  animal.daysRemaining = 1;

  // Send the birth alert
  try {
    const results = await ruralNotificationService.sendBirthPredictionAlert({
      animalId: animal.id,
      animalName: animal.name,
      daysRemaining: animal.daysRemaining,
      expectedDueDate: animal.dueDate,
      riskLevel: animal.riskLevel,
      specialCare: animal.specialCare,
      recipient: {
        name: 'Farm Manager',
        phone: phoneNumber
      },
      notificationTypes
    });

    console.log('Birth alert sent successfully');
    console.log('Results:', JSON.stringify(results, null, 2));
  } catch (error) {
    console.error('Error sending birth alert:', error);
  }
}

// Run the birth alert
runBirthAlert().catch(console.error);
