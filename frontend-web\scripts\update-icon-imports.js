/**
 * This script automatically updates Material UI icon imports in all files
 * to use the optimized iconImports utility instead of direct imports.
 * 
 * Usage: node scripts/update-icon-imports.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Get all TypeScript and TSX files in the src directory
const files = glob.sync('src/**/*.{ts,tsx}', { ignore: ['src/utils/iconImports.ts'] });

// Regular expression to match Material UI icon imports
const iconImportRegex = /import\s+\{([^}]+)\}\s+from\s+['"]@mui\/icons-material['"];/g;

// Counter for tracking changes
let filesChanged = 0;
let totalImportsReplaced = 0;

// Process each file
files.forEach(file => {
  const filePath = path.resolve(file);
  let content = fs.readFileSync(filePath, 'utf8');
  let fileChanged = false;
  let importsReplaced = 0;
  
  // Check if the file has Material UI icon imports
  const matches = content.match(iconImportRegex);
  if (matches) {
    // Calculate the relative path to the iconImports utility
    const relativePath = path.relative(path.dirname(filePath), path.resolve('src/utils'))
      .replace(/\\/g, '/'); // Convert Windows backslashes to forward slashes
    
    // Replace the imports
    content = content.replace(iconImportRegex, (match, iconNames) => {
      importsReplaced++;
      return `import { ${iconNames} } from '${relativePath}/iconImports';`;
    });
    
    // Write the updated content back to the file
    fs.writeFileSync(filePath, content, 'utf8');
    fileChanged = true;
    
    console.log(`Updated ${importsReplaced} icon imports in ${file}`);
  }
  
  if (fileChanged) {
    filesChanged++;
    totalImportsReplaced += importsReplaced;
  }
});

console.log(`\nSummary:`);
console.log(`- Files processed: ${files.length}`);
console.log(`- Files changed: ${filesChanged}`);
console.log(`- Total imports replaced: ${totalImportsReplaced}`);
console.log(`\nDone! All Material UI icon imports have been updated to use the optimized utility.`);
