/**
 * Utility functions to ensure consistent selection behavior across components
 */

import { CSSProperties } from 'react';

/**
 * Returns CSS properties to ensure elements are properly selectable
 * This fixes issues with motion.div wrappers blocking selection
 */
export const getSelectableStyles = (): CSSProperties => ({
  position: 'relative',
  zIndex: 1,
  cursor: 'pointer',
  pointerEvents: 'auto'
});

/**
 * Returns CSS properties for clickable card elements
 */
export const getClickableCardStyles = (): CSSProperties => {
  const styles = getSelectableStyles();
  // Return only the standard CSS properties without pseudo-selectors
  return {
    ...styles,
    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out'
  };
};

/**
 * Returns CSS properties for ensuring proper tab selection
 */
export const getSelectableTabStyles = () => ({
  '& .MuiTabs-root': {
    position: 'relative',
    zIndex: 2,
  },
  '& .MuiTab-root': {
    position: 'relative',
    zIndex: 2,
    pointerEvents: 'auto',
    cursor: 'pointer',
  }
});

/**
 * Returns CSS properties for ensuring proper button selection
 */
export const getSelectableButtonStyles = () => ({
  position: 'relative',
  zIndex: 2,
  pointerEvents: 'auto',
  cursor: 'pointer',
});

/**
 * Returns CSS properties for ensuring proper table row selection
 */
export const getSelectableTableRowStyles = () => ({
  '& .MuiTableRow-root': {
    position: 'relative',
    zIndex: 1,
    cursor: 'pointer',
    pointerEvents: 'auto',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)'
    }
  }
});
