/**
 * Run Migration Script
 * 
 * This script runs the MongoDB migration.
 */

const { spawn } = require('child_process');
const path = require('path');

// Path to migration script
const migrationScriptPath = path.join(__dirname, 'migrate-to-mongodb.js');

// Run the migration script
console.log('Starting MongoDB migration...');
const migration = spawn('node', [migrationScriptPath], { stdio: 'inherit' });

migration.on('close', (code) => {
  if (code === 0) {
    console.log('Migration completed successfully');
  } else {
    console.error(`Migration failed with code ${code}`);
  }
});
