// This file sets up polyfills for Node.js modules used by MongoDB
// Simplified version to avoid webpack compilation issues

// Import required polyfills
import { Buffer } from 'buffer';
import process from 'process/browser';

// Import our simplified polyfills
import mockImplementations, {
  fs,
  net,
  tls,
  dns,
  child_process,
  fsPromises,
  timersPromises
} from './polyfills';

// Import patches for third-party libraries
import { applyReactBigCalendarPatches } from './patches/react-big-calendar-fix';
import { applyResizeObserverFix } from './utils/resizeObserverFix';
import applyEnhancedResizeObserverFix from './patches/resize-observer-patch';

// Extend Window interface
declare global {
  interface Window {
    Buffer: typeof Buffer;
    process: typeof process;
    global: Window;
    fs: any;
    net: any;
    tls: any;
    dns: any;
    child_process: any;
    [key: string]: any;
  }
}

// Make Buffer and process available globally
window.Buffer = Buffer;
window.process = process;

// Define global variables for Node.js modules
window.global = window;
window.global.process = process;
window.global.Buffer = Buffer;

// Use mock implementations for modules without browser versions
window.fs = fs;
window.net = net;
window.tls = tls;
window.dns = dns;
window.child_process = child_process;
window['fs/promises'] = fsPromises;
window['timers/promises'] = timersPromises;

console.log('Simplified polyfills have been set up successfully!');

// Apply patches to third-party libraries
try {
  applyReactBigCalendarPatches();

  // Apply ResizeObserver fixes
  applyResizeObserverFix();
  applyEnhancedResizeObserverFix();
  console.log('ResizeObserver fixes applied');
} catch (error) {
  console.error('Error applying patches:', error);
}
