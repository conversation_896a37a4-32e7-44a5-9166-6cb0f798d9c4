/**
 * Users Controller
 *
 * Controller for users module
 */

const { getCollection, getModel } = require('../config/mongodb');
const logger = require('../utils/logger');
const mongoose = require('mongoose');

/**
 * Get all users
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAll = async (req, res) => {
  try {
    // Try to use the User model
    try {
      const User = getModel('User');
      const items = await User.find({}).select('-password'); // Exclude password field for security
      return res.status(200).json({ success: true, data: items });
    } catch (modelError) {
      logger.warn(`User model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('users');
      const items = await collection.find({}).toArray();

      // Remove password field for security
      items.forEach(item => {
        if (item.password) delete item.password;
      });

      return res.status(200).json({ success: true, data: items });
    }
  } catch (error) {
    logger.error(`Error in getAll users: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Get users by ID
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getById = async (req, res) => {
  try {
    const id = req.params.id;

    // Try to use the User model
    try {
      const User = getModel('User');
      const item = await User.findById(id).select('-password'); // Exclude password field for security

      if (!item) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      return res.status(200).json({ success: true, data: item });
    } catch (modelError) {
      logger.warn(`User model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('users');
      const item = await collection.findOne({ _id: id });

      if (!item) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Remove password field for security
      if (item.password) delete item.password;

      return res.status(200).json({ success: true, data: item });
    }
  } catch (error) {
    logger.error(`Error in getById users: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Create users
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const create = async (req, res) => {
  try {
    const newItem = req.body;

    if (!newItem) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }

    // Validate required fields
    if (!newItem.username || !newItem.email || !newItem.password) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        requiredFields: ['username', 'email', 'password']
      });
    }

    // Try to use the User model
    try {
      const User = getModel('User');

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { username: newItem.username },
          { email: newItem.email }
        ]
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'User already exists with this username or email'
        });
      }

      const user = new User(newItem);
      const savedUser = await user.save();

      // Remove password from response
      const userResponse = savedUser.toObject();
      delete userResponse.password;

      return res.status(201).json({ success: true, data: userResponse });
    } catch (modelError) {
      logger.warn(`User model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('users');

      // Check if user already exists
      const existingUser = await collection.findOne({
        $or: [
          { username: newItem.username },
          { email: newItem.email }
        ]
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'User already exists with this username or email'
        });
      }

      // Hash password manually if model is not available
      const bcrypt = require('bcryptjs');
      newItem.password = await bcrypt.hash(newItem.password, 10);

      const result = await collection.insertOne(newItem);

      // Remove password from response
      const userResponse = { _id: result.insertedId, ...newItem };
      delete userResponse.password;

      return res.status(201).json({ success: true, data: userResponse });
    }
  } catch (error) {
    logger.error(`Error in create users: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Update users
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const update = async (req, res) => {
  try {
    const id = req.params.id;
    const updates = req.body;

    if (!updates) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }

    // Try to use the User model
    try {
      const User = getModel('User');

      // Check if updating username or email to one that already exists
      if (updates.username || updates.email) {
        const existingUser = await User.findOne({
          _id: { $ne: id }, // Not the current user
          $or: [
            ...(updates.username ? [{ username: updates.username }] : []),
            ...(updates.email ? [{ email: updates.email }] : [])
          ]
        });

        if (existingUser) {
          return res.status(409).json({
            success: false,
            message: 'Another user already exists with this username or email'
          });
        }
      }

      // If updating password, hash it
      if (updates.password) {
        // Password will be hashed by the pre-save hook in the model
      }

      const updatedUser = await User.findByIdAndUpdate(id, updates, { new: true }).select('-password');

      if (!updatedUser) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      return res.status(200).json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      });
    } catch (modelError) {
      logger.warn(`User model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('users');

      // Check if updating username or email to one that already exists
      if (updates.username || updates.email) {
        const existingUser = await collection.findOne({
          _id: { $ne: id }, // Not the current user
          $or: [
            ...(updates.username ? [{ username: updates.username }] : []),
            ...(updates.email ? [{ email: updates.email }] : [])
          ]
        });

        if (existingUser) {
          return res.status(409).json({
            success: false,
            message: 'Another user already exists with this username or email'
          });
        }
      }

      // If updating password, hash it
      if (updates.password) {
        const bcrypt = require('bcryptjs');
        updates.password = await bcrypt.hash(updates.password, 10);
      }

      const result = await collection.updateOne({ _id: id }, { $set: updates });

      if (result.matchedCount === 0) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Get the updated user to return (without password)
      const updatedUser = await collection.findOne({ _id: id });
      if (updatedUser && updatedUser.password) delete updatedUser.password;

      return res.status(200).json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      });
    }
  } catch (error) {
    logger.error(`Error in update users: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Delete users
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const deleteUsers = async (req, res) => {
  try {
    const id = req.params.id;

    // Try to use the User model
    try {
      const User = getModel('User');

      // Check if trying to delete the admin user
      const user = await User.findById(id);

      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Prevent deletion of the admin user
      if (user.role === 'admin' && user.username === 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Cannot delete the main admin user'
        });
      }

      const deletedUser = await User.findByIdAndDelete(id);

      if (!deletedUser) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      return res.status(200).json({ success: true, message: 'User deleted successfully' });
    } catch (modelError) {
      logger.warn(`User model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('users');

      // Check if trying to delete the admin user
      const user = await collection.findOne({ _id: id });

      if (!user) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      // Prevent deletion of the admin user
      if (user.role === 'admin' && user.username === 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Cannot delete the main admin user'
        });
      }

      const result = await collection.deleteOne({ _id: id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ success: false, message: 'User not found' });
      }

      return res.status(200).json({ success: true, message: 'User deleted successfully' });
    }
  } catch (error) {
    logger.error(`Error in delete users: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: deleteUsers
};
