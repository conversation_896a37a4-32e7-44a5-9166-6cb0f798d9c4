# Manual Start Guide for MayCaiphus Livestock Management System

This guide provides instructions for manually starting the MayCaiphus Livestock Management System in VS Code.

## Option 1: Using VS Code Tasks (Recommended)

1. Open the project in VS Code
2. Press `Ctrl+Shift+P` to open the command palette
3. Type "Tasks: Run Task" and select it
4. Choose "Start Full Application"

This will start both the backend and frontend servers in separate terminal windows.

## Option 2: Using Terminal Commands

### Step 1: Start the Backend Server

1. Open VS Code
2. Open a new terminal (Terminal > New Terminal)
3. Navigate to the backend directory:
   ```
   cd backend
   ```
4. Start the backend server:
   ```
   set PORT=3001
   npm run dev
   ```

### Step 2: Start the Frontend Server

1. Open a new terminal in VS Code (Terminal > New Terminal)
2. Navigate to the frontend-web directory:
   ```
   cd frontend-web
   ```
3. Start the frontend server:
   ```
   set PORT=3000
   npm run start:dev
   ```

## Option 3: Using the Batch File

1. Simply double-click the `start-app.bat` file in the root directory
2. This will start both the backend and frontend servers

## Accessing the Application

1. Open your browser
2. Navigate to: http://localhost:3000
3. Login with the following credentials:
   - Username: admin
   - Password: Admin@123

## Stopping the Application

### If using VS Code Tasks or Terminal Commands:
1. Press `Ctrl+C` in each terminal window
2. Confirm with `Y` if prompted

### If using the Batch File:
1. Press any key in the batch file window when prompted to stop the servers

## Troubleshooting

If you encounter any issues:

1. Check that MongoDB is properly configured in the `.env` file in the backend directory
2. Ensure all dependencies are installed:
   ```
   cd backend && npm install
   cd frontend-web && npm install
   ```
3. Check the terminal for any error messages
4. Restart the servers if needed

## Database Configuration

The MongoDB connection string should be configured in the backend/.env file:

```
MONGODB_URI=mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGODB_DB_NAME=AMPD Live Stock
```
