const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');
const webpack = require('webpack');

module.exports = function(app) {
  // Proxy API requests to the backend
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:3005', // Updated to match backend PORT from .env
      changeOrigin: true,
      logLevel: 'debug',
      onError: (err, req, res) => {
        console.error('Proxy error:', err);
        res.status(500).json({ error: 'Proxy error', message: err.message });
      }
    })
  );

  // This function is called by react-scripts during development
  // We can use it to modify the webpack configuration
  try {
    // Get the webpack compiler from the app
    const compiler = app.locals.compiler;

    if (compiler && compiler.options) {
      // Get the webpack configuration
      const config = compiler.options;

      // Add fallbacks for node modules
      config.resolve.fallback = {
        ...config.resolve.fallback,
        // Use actual polyfill packages for Node.js modules
        "crypto": require.resolve('crypto-browserify'),
        "stream": require.resolve('stream-browserify'),
        "timers": require.resolve('timers-browserify'),
        "os": require.resolve('os-browserify/browser'),
        "zlib": require.resolve('browserify-zlib'),
        "http": require.resolve('stream-http'),
        "https": require.resolve('https-browserify'),
        "querystring": require.resolve('querystring-es3'),
        "buffer": require.resolve('buffer/'),
        "process": require.resolve('process/browser'),

        // Use our mock implementations for modules without browser versions
        "fs": path.resolve(__dirname, './polyfills.js'),
        "net": path.resolve(__dirname, './polyfills.js'),
        "tls": path.resolve(__dirname, './polyfills.js'),
        "dns": path.resolve(__dirname, './polyfills.js'),
        "child_process": path.resolve(__dirname, './polyfills.js'),
        "fs/promises": path.resolve(__dirname, './polyfills.js'),
        "timers/promises": path.resolve(__dirname, './polyfills.js'),
        "path": false
      };

      // Add plugins for polyfills
      config.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer']
        })
      );

      // Invalidate the webpack compiler to apply the changes
      compiler.hooks.done.tap('setupProxy', () => {
        console.log('Webpack configuration has been modified to include polyfills for MongoDB');
      });
    }
  } catch (error) {
    console.error('Error modifying webpack configuration:', error);
  }
};
