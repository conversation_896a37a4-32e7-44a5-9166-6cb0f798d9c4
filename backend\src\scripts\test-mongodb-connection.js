/**
 * MongoDB Connection Test Script
 *
 * This script tests the connection to MongoDB Atlas and provides detailed error information
 * to help diagnose connection issues.
 * Run with: node src/scripts/test-mongodb-connection.js
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');

// MongoDB connection string
const MONGODB_URI = "mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const DB_NAME = process.env.MONGODB_DB_NAME || 'AMPD_Live_Stock';

// Connection options
const options = {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: process.env.MONGODB_SSL === 'true',
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
  socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT || '45000'),
  maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '50'),
  minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE || '5'),
  retryWrites: process.env.MONGODB_RETRY_WRITES === 'true',
  retryReads: process.env.MONGODB_RETRY_READS === 'true'
};

// Connect to MongoDB and test connection
async function testConnection() {
  let client;

  try {
    // Redact sensitive information from URI for logging
    const redactedUri = MONGODB_URI.replace(/:\/\/([^:]+):([^@]+)@/, '://$1:****@');
    console.log('Connecting to MongoDB...');
    console.log(`URI: ${redactedUri}`);
    console.log(`Database: ${DB_NAME}`);
    console.log(`Options: ${JSON.stringify(options, null, 2)}`);

    // Connect to MongoDB
    client = new MongoClient(MONGODB_URI, options);
    await client.connect();
    console.log('✅ Connected to MongoDB server successfully!');

    // Get database and collection
    const db = client.db(DB_NAME);
    console.log(`✅ Connected to database: ${db.databaseName}`);

    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    console.log('✅ Ping command successful!');

    // List collections
    const collections = await db.listCollections().toArray();
    console.log(`✅ Found ${collections.length} collections in database`);

    // Log collection names and document counts
    if (collections.length > 0) {
      console.log('Collections:');
      for (const collection of collections) {
        const count = await db.collection(collection.name).countDocuments();
        console.log(`- ${collection.name}: ${count} documents`);
      }
    } else {
      console.log('No collections found in the database');
    }

    console.log('✅ MongoDB connection test completed successfully!');
  } catch (error) {
    console.error('❌ MongoDB connection test failed!');

    // Check for common connection errors
    if (error.name === 'MongoNetworkError') {
      console.error('Network error connecting to MongoDB. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Connection string is correct');
      console.error('- Network allows connection to MongoDB port');
    } else if (error.name === 'MongoServerSelectionError') {
      console.error('Server selection error. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Authentication credentials are correct');
      console.error('- MongoDB server is accessible from this network');
    } else if (error.message && error.message.includes('timeout')) {
      console.error('Connection timeout. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Network latency is not too high');
      console.error('- Firewall settings allow connection');
    } else if (error.message && error.message.includes('authentication')) {
      console.error('Authentication failed. Please check:');
      console.error('- Username and password are correct');
      console.error('- User has appropriate permissions');
      console.error('- Authentication database is correct');
    }

    // Log the original error for debugging
    console.error('Original error:', error);
  } finally {
    // Close MongoDB connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the test
testConnection()
  .then(() => {
    console.log('Test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
