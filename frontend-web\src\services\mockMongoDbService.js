
// Mock MongoDB service for frontend use
import { mockData } from '../mocks/mockData';

export class MockMongoDbService {
  constructor() {
    this.collections = {
      users: [...(mockData.users || [])],
      animals: [...(mockData.animals || [])],
      feeds: [...(mockData.feeds || [])],
      health: [...(mockData.health || [])],
      breeding: [...(mockData.breeding || [])],
      commercial: [...(mockData.commercial || [])],
      // Add more collections as needed
    };
  }

  // Find documents in a collection
  async find(collectionName, query = {}) {
    const collection = this.collections[collectionName] || [];

    // Simple query implementation
    return collection.filter(item => {
      for (const [key, value] of Object.entries(query)) {
        if (item[key] !== value) {
          return false;
        }
      }
      return true;
    });
  }

  // Insert a document into a collection
  async insertOne(collectionName, document) {
    if (!this.collections[collectionName]) {
      this.collections[collectionName] = [];
    }

    const newDocument = { ...document, _id: this.generateId() };
    this.collections[collectionName].push(newDocument);

    return { insertedId: newDocument._id };
  }

  // Update a document in a collection
  async updateOne(collectionName, filter, update) {
    if (!this.collections[collectionName]) {
      return { modifiedCount: 0 };
    }

    let modifiedCount = 0;
    this.collections[collectionName] = this.collections[collectionName].map(item => {
      let matches = true;
      for (const [key, value] of Object.entries(filter)) {
        if (item[key] !== value) {
          matches = false;
          break;
        }
      }

      if (matches) {
        modifiedCount++;
        return { ...item, ...update.$set };
      }

      return item;
    });

    return { modifiedCount };
  }

  // Delete a document from a collection
  async deleteOne(collectionName, filter) {
    if (!this.collections[collectionName]) {
      return { deletedCount: 0 };
    }

    const initialLength = this.collections[collectionName].length;
    this.collections[collectionName] = this.collections[collectionName].filter(item => {
      for (const [key, value] of Object.entries(filter)) {
        if (item[key] === value) {
          return false;
        }
      }
      return true;
    });

    return { deletedCount: initialLength - this.collections[collectionName].length };
  }

  // Generate a simple ID
  generateId() {
    return Math.random().toString(36).substring(2, 15);
  }
}

// Export a singleton instance
export const mockMongoDb = new MockMongoDbService();
