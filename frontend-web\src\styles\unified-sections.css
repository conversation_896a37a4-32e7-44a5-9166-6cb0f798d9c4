/**
 * Unified Sectioning and Card System for AgriIntel
 * 
 * Provides consistent content division, spacing, and visual hierarchy
 * across all pages based on the reference design pattern.
 */

/* Section containers */
.unified-section {
  margin-bottom: 3rem;
  padding: 0;
}

.unified-section-large {
  margin-bottom: 4rem;
  padding: 0;
}

.unified-section-small {
  margin-bottom: 2rem;
  padding: 0;
}

/* Content containers */
.unified-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.unified-container-full {
  width: 100%;
  padding: 0 1.5rem;
}

.unified-container-narrow {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Card system - consistent with reference design */
.unified-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.unified-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.unified-card-elevated {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 20px;
  padding: 2.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.unified-card-elevated:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.unified-card-compact {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.unified-card-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Grid systems */
.unified-grid {
  display: grid;
  gap: 1.5rem;
  width: 100%;
}

.unified-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.unified-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.unified-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.unified-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Responsive grid adjustments */
@media (max-width: 1024px) {
  .unified-grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .unified-grid-3,
  .unified-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .unified-grid {
    gap: 1rem;
  }
}

@media (max-width: 640px) {
  .unified-grid-2,
  .unified-grid-3,
  .unified-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .unified-container,
  .unified-container-full,
  .unified-container-narrow {
    padding: 0 1rem;
  }
  
  .unified-card {
    padding: 1.5rem;
    border-radius: 12px;
  }
  
  .unified-card-elevated {
    padding: 2rem;
    border-radius: 16px;
  }
  
  .unified-card-compact {
    padding: 1rem;
    border-radius: 10px;
  }
}

/* Visual separators */
.unified-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 2rem 0;
  border: none;
}

.unified-divider-thick {
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.15), transparent);
  margin: 3rem 0;
  border: none;
}

.unified-divider-subtle {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
  margin: 1.5rem 0;
  border: none;
}

/* Content spacing utilities */
.unified-spacing-xs { margin-bottom: 0.5rem; }
.unified-spacing-sm { margin-bottom: 1rem; }
.unified-spacing-md { margin-bottom: 1.5rem; }
.unified-spacing-lg { margin-bottom: 2rem; }
.unified-spacing-xl { margin-bottom: 3rem; }
.unified-spacing-2xl { margin-bottom: 4rem; }

/* Padding utilities */
.unified-padding-xs { padding: 0.5rem; }
.unified-padding-sm { padding: 1rem; }
.unified-padding-md { padding: 1.5rem; }
.unified-padding-lg { padding: 2rem; }
.unified-padding-xl { padding: 3rem; }

/* Flexbox utilities for consistent alignment */
.unified-flex {
  display: flex;
}

.unified-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.unified-flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.unified-flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.unified-flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.unified-flex-column {
  display: flex;
  flex-direction: column;
}

.unified-flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* Text alignment utilities */
.unified-text-center { text-align: center; }
.unified-text-left { text-align: left; }
.unified-text-right { text-align: right; }

/* Background overlays for content over images */
.unified-overlay {
  position: relative;
}

.unified-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  border-radius: inherit;
  z-index: 1;
}

.unified-overlay > * {
  position: relative;
  z-index: 2;
}

.unified-overlay-light::before {
  background: rgba(255, 255, 255, 0.8);
}

.unified-overlay-dark::before {
  background: rgba(0, 0, 0, 0.6);
}

/* Status indicators */
.unified-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.unified-status-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.unified-status-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: #D97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.unified-status-error {
  background-color: rgba(239, 68, 68, 0.1);
  color: #DC2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.unified-status-info {
  background-color: rgba(59, 130, 246, 0.1);
  color: #2563EB;
  border: 1px solid rgba(59, 130, 246, 0.2);
}
