/**
 * Upgrade Prompt Component
 * Shows upgrade prompts for locked features with compelling messaging
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  useTheme,
  alpha,
  Divider
} from '@mui/material';
import {
  Lock,
  Upgrade,
  Star,
  Check,
  TrendingUp,
  AccountBalance,
  Assessment,
  Inventory,
  Business,
  Security,
  Close
} from '@mui/icons-material';
import { getUpgradeMessage, moduleAccessConfig } from '../../utils/betaAccessControl';
import SubscriptionManager from './SubscriptionManager';

interface UpgradePromptProps {
  open: boolean;
  onClose: () => void;
  moduleId: string;
  moduleName: string;
  currentPlan?: string;
}

const featureIcons: Record<string, React.ReactElement> = {
  breeding: <TrendingUp />,
  financial: <AccountBalance />,
  analytics: <Assessment />,
  inventory: <Inventory />,
  commercial: <Business />,
  compliance: <Security />,
  reports: <Assessment />
};

const featureBenefits: Record<string, string[]> = {
  breeding: [
    'Track breeding cycles and pregnancy status',
    'Optimize breeding programs with AI insights',
    'Manage genetic records and lineage',
    'Predict calving dates and outcomes',
    'Improve breeding efficiency by 30%'
  ],
  financial: [
    'Complete income and expense tracking',
    'Automated profit/loss calculations',
    'Tax-ready financial reports',
    'Budget planning and forecasting',
    'Increase profitability by 25%'
  ],
  analytics: [
    'AI-powered predictive insights',
    'Performance trend analysis',
    'Automated recommendations',
    'Risk assessment and alerts',
    'Data-driven decision making'
  ],
  inventory: [
    'Smart inventory management',
    'Automated reorder alerts',
    'Cost tracking and optimization',
    'Supplier management',
    'Reduce waste by 20%'
  ],
  commercial: [
    'Sales and marketing tools',
    'Customer relationship management',
    'Market price tracking',
    'Contract management',
    'Increase sales by 35%'
  ],
  compliance: [
    'Regulatory compliance tracking',
    'Automated documentation',
    'Audit trail management',
    'Certification tracking',
    'Avoid compliance penalties'
  ],
  reports: [
    'Advanced custom reports',
    'Automated report generation',
    'Multi-format exports',
    'Scheduled reporting',
    'Save 10+ hours per week'
  ]
};

const UpgradePrompt: React.FC<UpgradePromptProps> = ({
  open,
  onClose,
  moduleId,
  moduleName,
  currentPlan = 'beta'
}) => {
  const theme = useTheme();
  const [showSubscriptionManager, setShowSubscriptionManager] = useState(false);

  const moduleConfig = moduleAccessConfig[moduleId];
  const upgradeMessage = getUpgradeMessage(moduleId);
  const benefits = featureBenefits[moduleId] || [];
  const icon = featureIcons[moduleId] || <Lock />;

  const handleUpgradeClick = () => {
    setShowSubscriptionManager(true);
  };

  const handleSubscriptionClose = () => {
    setShowSubscriptionManager(false);
    onClose();
  };

  const getRequiredPlan = () => {
    if (!moduleConfig) return 'Professional';
    return moduleConfig.requiredTier === 'Enterprise' ? 'Enterprise' : 'Professional';
  };

  const getPlanPrice = () => {
    const requiredPlan = getRequiredPlan();
    return requiredPlan === 'Enterprise' ? 'R599/month' : 'R299/month';
  };

  const getPlanColor = () => {
    const requiredPlan = getRequiredPlan();
    return requiredPlan === 'Enterprise' ? '#9C27B0' : '#2196F3';
  };

  return (
    <>
      <Dialog 
        open={open && !showSubscriptionManager} 
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { 
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(getPlanColor(), 0.05)} 0%, ${alpha(getPlanColor(), 0.02)} 100%)`
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
          <Box
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              backgroundColor: alpha(getPlanColor(), 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px',
              color: getPlanColor()
            }}
          >
            {React.cloneElement(icon, { fontSize: 'large' })}
          </Box>
          
          <Typography variant="h5" gutterBottom sx={{ color: getPlanColor() }}>
            Unlock {moduleName}
          </Typography>
          
          <Typography variant="body1" color="text.secondary">
            Take your livestock management to the next level
          </Typography>
        </DialogTitle>

        <DialogContent>
          <Alert 
            severity="info" 
            sx={{ 
              mb: 3,
              backgroundColor: alpha(getPlanColor(), 0.1),
              color: getPlanColor(),
              '& .MuiAlert-icon': {
                color: getPlanColor()
              }
            }}
          >
            <Typography variant="body2">
              {upgradeMessage}
            </Typography>
          </Alert>

          <Card 
            sx={{ 
              mb: 3,
              border: `1px solid ${alpha(getPlanColor(), 0.2)}`,
              backgroundColor: alpha(getPlanColor(), 0.02)
            }}
          >
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Chip
                  label={getRequiredPlan()}
                  sx={{
                    backgroundColor: getPlanColor(),
                    color: 'white',
                    fontWeight: 600,
                    mr: 2
                  }}
                />
                <Typography variant="h6" sx={{ color: getPlanColor() }}>
                  {getPlanPrice()}
                </Typography>
              </Box>

              <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                What you'll get with {moduleName}:
              </Typography>

              <List dense>
                {benefits.map((benefit, index) => (
                  <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 32 }}>
                      <Check color="success" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText 
                      primary={benefit}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          <Box 
            sx={{ 
              textAlign: 'center',
              p: 2,
              backgroundColor: alpha(theme.palette.success.main, 0.1),
              borderRadius: 1,
              mb: 2
            }}
          >
            <Typography variant="subtitle2" color="success.main" gutterBottom>
              🎉 Limited Time Offer
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Get 30% off your first 3 months when you upgrade today!
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              ✓ 30-day money-back guarantee<br />
              ✓ Cancel anytime<br />
              ✓ Instant access to all features
            </Typography>
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 3, flexDirection: 'column', gap: 2 }}>
          <Button
            variant="contained"
            size="large"
            fullWidth
            onClick={handleUpgradeClick}
            startIcon={<Upgrade />}
            sx={{
              background: `linear-gradient(45deg, ${getPlanColor()}, ${alpha(getPlanColor(), 0.8)})`,
              py: 1.5,
              fontSize: '1.1rem',
              fontWeight: 600
            }}
          >
            Upgrade to {getRequiredPlan()} - {getPlanPrice()}
          </Button>
          
          <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
            <Button
              variant="outlined"
              onClick={onClose}
              startIcon={<Close />}
              sx={{ flex: 1 }}
            >
              Maybe Later
            </Button>
            <Button
              variant="text"
              onClick={() => setShowSubscriptionManager(true)}
              sx={{ flex: 1 }}
            >
              Compare Plans
            </Button>
          </Box>
        </DialogActions>
      </Dialog>

      <SubscriptionManager
        open={showSubscriptionManager}
        onClose={handleSubscriptionClose}
        currentPlan={currentPlan}
        lockedFeature={moduleName}
      />
    </>
  );
};

export default UpgradePrompt;
