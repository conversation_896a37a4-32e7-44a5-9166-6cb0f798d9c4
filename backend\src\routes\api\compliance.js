/**
 * Compliance API Routes
 *
 * This module provides API routes for compliance management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');
const complianceController = require('../../controllers/complianceController');

/**
 * @route GET /api/compliance
 * @desc Get compliance overview
 * @access Private
 */
router.get('/', complianceController.getCompliance);

/**
 * @route GET /api/compliance/documents
 * @desc Get all compliance documents
 * @access Private
 */
router.get('/documents', complianceController.getDocuments);

/**
 * @route GET /api/compliance/inspections
 * @desc Get all compliance inspections
 * @access Private
 */
router.get('/inspections', complianceController.getInspections);

/**
 * @route GET /api/compliance/stats
 * @desc Get compliance statistics
 * @access Private
 */
router.get('/stats', complianceController.getStats);

/**
 * @route GET /api/compliance/certifications
 * @desc Get all compliance certifications
 * @access Private
 */
router.get('/certifications', async (req, res, next) => {
  try {
    // Return mock compliance certifications for now
    return res.json({
      success: true,
      data: [
        { id: '1', name: 'Organic Certification', issuer: 'Organic Standards Board', expiryDate: new Date('2025-12-31') },
        { id: '2', name: 'Animal Welfare Certification', issuer: 'Animal Welfare Association', expiryDate: new Date('2025-06-30') },
        { id: '3', name: 'Quality Assurance Certification', issuer: 'Quality Standards Authority', expiryDate: new Date('2025-09-15') }
      ]
    });
  } catch (error) {
    logger.error('Error getting compliance certifications:', error);
    next(error);
  }
});



module.exports = router;
