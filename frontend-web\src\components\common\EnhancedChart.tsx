import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, IconButton, Tooltip, Menu, MenuItem, useTheme, alpha, Card, CardContent, CardHeader, Divider, Chip } from '@mui/material';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ScatterChart,
  Scatter,
  ZAxis
} from 'recharts';
import {
  <PERSON><PERSON><PERSON> as BarChartIcon,
  ShowChart as LineChartIcon,
  PieChart as PieChartIcon,
  B<PERSON>bleChart as ScatterChartIcon,
  DonutLarge as RadarChartIcon, // Using DonutLarge as a replacement for <PERSON><PERSON>hart
  StackedLineChart as AreaChartIcon,
  MoreVert,
  Download,
  Print,
  Share,
  Fullscreen,
  Info,
  Refresh
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

export type ChartType = 'bar' | 'line' | 'area' | 'pie' | 'radar' | 'scatter';
export type TimeRange = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'all';

export interface EnhancedChartProps {
  title: string;
  subtitle?: string;
  data: any[];
  type?: ChartType;
  dataKeys: string[];
  xAxisDataKey?: string;
  height?: number;
  accentColor?: string;
  allowChartTypeChange?: boolean;
  allowTimeRangeChange?: boolean;
  module?: string;
  tooltip?: string;
  onRefresh?: () => void;
  onDownload?: () => void;
  onPrint?: () => void;
  onShare?: () => void;
  onFullscreen?: () => void;
  emptyMessage?: string;
  loading?: boolean;
}

const EnhancedChart: React.FC<EnhancedChartProps> = ({
  title,
  subtitle,
  data,
  type = 'bar',
  dataKeys,
  xAxisDataKey = 'name',
  height = 300,
  accentColor,
  allowChartTypeChange = false,
  allowTimeRangeChange = false,
  module,
  tooltip,
  onRefresh,
  onDownload,
  onPrint,
  onShare,
  onFullscreen,
  emptyMessage = 'No data available',
  loading = false
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const [chartType, setChartType] = useState<ChartType>(type);
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [chartTypeAnchorEl, setChartTypeAnchorEl] = useState<null | HTMLElement>(null);
  const [timeRangeAnchorEl, setTimeRangeAnchorEl] = useState<null | HTMLElement>(null);
  const [showTooltip, setShowTooltip] = useState(false);

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      default:
        return accentColor || theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  // Generate colors for multi-series charts
  const generateColors = () => {
    const baseColor = moduleColor;
    const colors = [baseColor];

    // Generate additional colors based on the base color
    for (let i = 1; i < dataKeys.length; i++) {
      if (i % 3 === 1) {
        // Lighter shade
        colors.push(alpha(baseColor, 0.7));
      } else if (i % 3 === 2) {
        // Darker shade
        colors.push(theme.palette.secondary.main);
      } else {
        // Complementary color
        colors.push(theme.palette.warning.main);
      }
    }

    return colors;
  };

  const colors = generateColors();

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleChartTypeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setChartTypeAnchorEl(event.currentTarget);
  };

  const handleChartTypeMenuClose = () => {
    setChartTypeAnchorEl(null);
  };

  const handleTimeRangeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setTimeRangeAnchorEl(event.currentTarget);
  };

  const handleTimeRangeMenuClose = () => {
    setTimeRangeAnchorEl(null);
  };

  const handleChartTypeChange = (type: ChartType) => {
    setChartType(type);
    handleChartTypeMenuClose();
  };

  const handleTimeRangeChange = (range: TimeRange) => {
    setTimeRange(range);
    handleTimeRangeMenuClose();
  };

  // Get chart type icon
  const getChartTypeIcon = () => {
    switch (chartType) {
      case 'bar':
        return <BarChartIcon />;
      case 'line':
        return <LineChartIcon />;
      case 'area':
        return <AreaChartIcon />;
      case 'pie':
        return <PieChartIcon />;
      case 'radar':
        return <RadarChartIcon />;
      case 'scatter':
        return <ScatterChartIcon />;
      default:
        return <BarChartIcon />;
    }
  };

  // Render chart based on type
  const renderChart = () => {
    if (!data || data.length === 0) {
      return (
        <Box
          sx={{
            height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            p: 3
          }}
        >
          <Typography variant="body1" color="text.secondary" align="center">
            {emptyMessage}
          </Typography>
          {onRefresh && (
            <CustomButton
              startIcon={<Refresh />}
              onClick={onRefresh}
              sx={{ mt: 2 }}
              variant="outlined"
              size="small"
            >
              {translate('common.refresh')}
            </CustomButton>
          )}
        </Box>
      );
    }

    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
              />
              <Legend />
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={colors[index % colors.length]}
                  radius={[4, 4, 0, 0]}
                  animationDuration={1500}
                  animationEasing="ease-out"
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
              />
              <Legend />
              {dataKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={colors[index % colors.length]}
                  strokeWidth={2}
                  dot={{ r: 4, fill: colors[index % colors.length], strokeWidth: 1 }}
                  activeDot={{ r: 6, fill: colors[index % colors.length], strokeWidth: 0 }}
                  animationDuration={1500}
                  animationEasing="ease-out"
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
              />
              <Legend />
              {dataKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={colors[index % colors.length]}
                  fill={alpha(colors[index % colors.length], 0.2)}
                  animationDuration={1500}
                  animationEasing="ease-out"
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );
      case 'pie':
        // For pie charts, we need to transform the data
        const pieData = dataKeys.length === 1
          ? data.map(item => ({
              name: item[xAxisDataKey],
              value: item[dataKeys[0]]
            }))
          : dataKeys.map(key => ({
              name: key,
              value: data.reduce((sum, item) => sum + (item[key] || 0), 0)
            }));

        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={true}
                outerRadius={80}
                innerRadius={30}
                fill={moduleColor}
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                animationDuration={1500}
                animationEasing="ease-out"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                ))}
              </Pie>
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
      case 'radar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <RadarChart cx="50%" cy="50%" outerRadius={80} data={data}>
              <PolarGrid stroke={alpha(theme.palette.divider, 0.2)} />
              <PolarAngleAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
              />
              <PolarRadiusAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                angle={30}
              />
              {dataKeys.map((key, index) => (
                <Radar
                  key={key}
                  name={key}
                  dataKey={key}
                  stroke={colors[index % colors.length]}
                  fill={alpha(colors[index % colors.length], 0.2)}
                  animationDuration={1500}
                  animationEasing="ease-out"
                />
              ))}
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        );
      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <ScatterChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.2)} />
              <XAxis
                dataKey={xAxisDataKey}
                type="number"
                name={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <YAxis
                dataKey={dataKeys[0]}
                type="number"
                name={dataKeys[0]}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                axisLine={{ stroke: theme.palette.divider }}
              />
              <ZAxis range={[60, 400]} />
              <RechartsTooltip
                contentStyle={{
                  backgroundColor: alpha(theme.palette.background.paper, 0.9),
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                  backdropFilter: 'blur(8px)'
                }}
                cursor={{ strokeDasharray: '3 3' }}
              />
              <Legend />
              <Scatter
                name={dataKeys[0]}
                data={data}
                fill={moduleColor}
                animationDuration={1500}
                animationEasing="ease-out"
              />
              {dataKeys.length > 1 && (
                <Scatter
                  name={dataKeys[1]}
                  data={data}
                  fill={colors[1 % colors.length]}
                  animationDuration={1500}
                  animationEasing="ease-out"
                />
              )}
            </ScatterChart>
          </ResponsiveContainer>
        );
      default:
        return null;
    }
  };

  return (
    <Card
      elevation={2}
      className="color-transition"
      sx={{
        height: '100%',
        borderRadius: '12px',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
        backdropFilter: 'blur(10px)',
        background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
        '&:hover': {
          boxShadow: `0 8px 25px ${alpha(moduleColor, 0.15)}`,
          transform: 'translateY(-5px)',
        }
      }}
    >
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              {title}
            </Typography>
            {tooltip && (
              <Tooltip title={tooltip}>
                <IconButton size="small" sx={{ ml: 1 }}>
                  <Info fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        }
        subheader={subtitle}
        action={
          <Box sx={{ display: 'flex' }}>
            {allowChartTypeChange && (
              <Tooltip title={translate('chart.change_type')}>
                <IconButton onClick={handleChartTypeMenuOpen} size="small">
                  {getChartTypeIcon()}
                </IconButton>
              </Tooltip>
            )}
            {allowTimeRangeChange && (
              <Chip
                label={translate(`chart.time_range.${timeRange}`)}
                size="small"
                onClick={handleTimeRangeMenuOpen}
                sx={{ mr: 1, height: 28, cursor: 'pointer' }}
              />
            )}
            <IconButton onClick={handleMenuOpen} size="small">
              <MoreVert fontSize="small" />
            </IconButton>
          </Box>
        }
        sx={{
          pb: 0,
          '& .MuiCardHeader-title': {
            fontSize: '1rem',
            fontWeight: 'bold'
          },
          '& .MuiCardHeader-subheader': {
            fontSize: '0.8rem'
          }
        }}
      />
      <CardContent sx={{ pt: 1, pb: 2, height: 'calc(100% - 60px)' }}>
        <Box sx={{ height: '100%', position: 'relative' }}>
          {loading ? (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography variant="body2" color="text.secondary">
                {translate('common.loading')}
              </Typography>
            </Box>
          ) : (
            renderChart()
          )}
        </Box>
      </CardContent>

      {/* Chart Type Menu */}
      <Menu
        anchorEl={chartTypeAnchorEl}
        open={Boolean(chartTypeAnchorEl)}
        onClose={handleChartTypeMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            mt: 1
          }
        }}
      >
        <MenuItem onClick={() => handleChartTypeChange('bar')} selected={chartType === 'bar'}>
          <BarChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.bar')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('line')} selected={chartType === 'line'}>
          <LineChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.line')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('area')} selected={chartType === 'area'}>
          <AreaChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.area')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('pie')} selected={chartType === 'pie'}>
          <PieChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.pie')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('radar')} selected={chartType === 'radar'}>
          <RadarChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.radar')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('scatter')} selected={chartType === 'scatter'}>
          <ScatterChartIcon sx={{ mr: 1 }} fontSize="small" />
          {translate('chart.type.scatter')}
        </MenuItem>
      </Menu>

      {/* Time Range Menu */}
      <Menu
        anchorEl={timeRangeAnchorEl}
        open={Boolean(timeRangeAnchorEl)}
        onClose={handleTimeRangeMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            mt: 1
          }
        }}
      >
        <MenuItem onClick={() => handleTimeRangeChange('day')} selected={timeRange === 'day'}>
          {translate('chart.time_range.day')}
        </MenuItem>
        <MenuItem onClick={() => handleTimeRangeChange('week')} selected={timeRange === 'week'}>
          {translate('chart.time_range.week')}
        </MenuItem>
        <MenuItem onClick={() => handleTimeRangeChange('month')} selected={timeRange === 'month'}>
          {translate('chart.time_range.month')}
        </MenuItem>
        <MenuItem onClick={() => handleTimeRangeChange('quarter')} selected={timeRange === 'quarter'}>
          {translate('chart.time_range.quarter')}
        </MenuItem>
        <MenuItem onClick={() => handleTimeRangeChange('year')} selected={timeRange === 'year'}>
          {translate('chart.time_range.year')}
        </MenuItem>
        <MenuItem onClick={() => handleTimeRangeChange('all')} selected={timeRange === 'all'}>
          {translate('chart.time_range.all')}
        </MenuItem>
      </Menu>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            mt: 1
          }
        }}
      >
        {onRefresh && (
          <MenuItem onClick={() => { onRefresh(); handleMenuClose(); }}>
            <Refresh fontSize="small" sx={{ mr: 1 }} />
            {translate('common.refresh')}
          </MenuItem>
        )}
        {onDownload && (
          <MenuItem onClick={() => { onDownload(); handleMenuClose(); }}>
            <Download fontSize="small" sx={{ mr: 1 }} />
            {translate('common.download')}
          </MenuItem>
        )}
        {onPrint && (
          <MenuItem onClick={() => { onPrint(); handleMenuClose(); }}>
            <Print fontSize="small" sx={{ mr: 1 }} />
            {translate('common.print')}
          </MenuItem>
        )}
        {onShare && (
          <MenuItem onClick={() => { onShare(); handleMenuClose(); }}>
            <Share fontSize="small" sx={{ mr: 1 }} />
            {translate('common.share')}
          </MenuItem>
        )}
        {onFullscreen && (
          <MenuItem onClick={() => { onFullscreen(); handleMenuClose(); }}>
            <Fullscreen fontSize="small" sx={{ mr: 1 }} />
            Fullscreen
          </MenuItem>
        )}
      </Menu>
    </Card>
  );
};

export default EnhancedChart;
