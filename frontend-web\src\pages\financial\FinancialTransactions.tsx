import React, { useState } from 'react';
import { Box, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, TextField, InputAdornment, Grid, Tabs, Tab, useTheme, alpha } from '@mui/material';
import CustomButton from '../../components/common/CustomButton';
import {  Add, Search, FilterList, Edit, Delete, AttachMoney, Receipt, Visibility  } from '../../utils/iconImports';
import { ModuleHeader, AnimatedBackgroundCard } from '../../components/common';

// Mock transactions data - Limited to 10 records with South African currency (Rands)
const mockTransactions = [
  { id: 'TR001', date: '2024-03-15', description: 'Cattle Sale to Beefmaster', category: 'Livestock Sales', amount: 125000, type: 'income', status: 'completed' },
  { id: 'TR002', date: '2024-03-12', description: 'Monthly Feed Purchase from Afgri', category: 'Feed', amount: 35000, type: 'expense', status: 'completed' },
  { id: 'TR003', date: '2024-03-10', description: 'Quarterly Veterinary Services', category: 'Veterinary', amount: 12000, type: 'expense', status: 'completed' },
  { id: 'TR004', date: '2024-03-08', description: 'Milk Sales to Clover', category: 'Dairy', amount: 45000, type: 'income', status: 'completed' },
  { id: 'TR005', date: '2024-03-05', description: 'Tractor Maintenance', category: 'Equipment', amount: 8500, type: 'expense', status: 'completed' },
  { id: 'TR006', date: '2024-03-03', description: 'Monthly Staff Salaries', category: 'Labor', amount: 50000, type: 'expense', status: 'completed' },
  { id: 'TR007', date: '2024-03-01', description: 'Agricultural Grant Payment', category: 'Subsidies', amount: 30000, type: 'income', status: 'completed' },
  { id: 'TR008', date: '2024-02-28', description: 'Eskom Electricity Bill', category: 'Utilities', amount: 7500, type: 'expense', status: 'completed' },
  { id: 'TR009', date: '2024-02-25', description: 'AI Services from Taurus', category: 'Breeding', amount: 18000, type: 'expense', status: 'pending' },
  { id: 'TR010', date: '2024-02-22', description: 'Weaner Calf Sales at Auction', category: 'Livestock Sales', amount: 95000, type: 'income', status: 'completed' },
];

const FinancialTransactions: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Filter transactions based on tab and search term
  const filteredTransactions = mockTransactions.filter(transaction => {
    const matchesTab =
      (tabValue === 0) || // All
      (tabValue === 1 && transaction.type === 'income') || // Income
      (tabValue === 2 && transaction.type === 'expense'); // Expense

    const matchesSearch =
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesTab && matchesSearch;
  });

  // Calculate totals
  const totalIncome = filteredTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpense = filteredTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <Box>
      <ModuleHeader
        title="Financial Transactions"
        subtitle="Manage income, expenses, and track financial activities"
        module="financial"
        submodule="transactions"
        actionLabel="Add Transaction"
        actionIcon={<Add />}
        onAction={() => console.log('Add transaction')}
      />

      <Box sx={{ px: 3, pb: 5 }}>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <AnimatedBackgroundCard
            title="Total Transactions"
            subtitle={filteredTransactions.length.toString()}
            backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2021/02/animal-vaccination.jpg"
            icon={<FilterList />}
            accentColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            delay={0.1}
            height={140}
            overlay="gradient"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <AnimatedBackgroundCard
            title="Total Income"
            subtitle={`R${totalIncome.toLocaleString()}`}
            backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2020/02/farm-finances.jpg"
            icon={<AttachMoney />}
            accentColor={theme.palette.success.main}
            secondaryColor={theme.palette.success.dark}
            delay={0.2}
            height={140}
            overlay="gradient"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <AnimatedBackgroundCard
            title="Total Expenses"
            subtitle={`R${totalExpense.toLocaleString()}`}
            backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2020/07/jj-gouin-3y-HX8jxvKQ-unsplash.jpg"
            icon={<Receipt />}
            accentColor={theme.palette.error.main}
            secondaryColor={theme.palette.error.dark}
            delay={0.3}
            height={140}
            overlay="gradient"
          />
        </Grid>
      </Grid>

      <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
        <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="All Transactions" />
              <Tab label="Income" />
              <Tab label="Expenses" />
            </Tabs>
            <Box display="flex" alignItems="center">
              <TextField
                placeholder="Search transactions..."
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ mr: 1 }}
              />
              <IconButton>
                <FilterList />
              </IconButton>
            </Box>
          </Box>
        </Box>

        <TableContainer>
          <Table>
            <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Amount</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredTransactions.map((transaction) => (
                <TableRow key={transaction.id} hover>
                  <TableCell>{transaction.id}</TableCell>
                  <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.category}
                      size="small"
                      color="default"
                    />
                  </TableCell>
                  <TableCell>R{transaction.amount.toLocaleString()}</TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.type === 'income' ? 'Income' : 'Expense'}
                      color={transaction.type === 'income' ? 'success' : 'error'}
                      icon={transaction.type === 'income' ? <AttachMoney /> : <Receipt />}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.status}
                      color={transaction.status === 'completed' ? 'success' : 'warning'}
                      variant="outlined"
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" color="info">
                      <Visibility fontSize="small" />
                    </IconButton>
                    <IconButton size="small" color="primary">
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <Delete fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
        <CustomButton variant="outlined" startIcon={<FilterList />}>
          Filter
        </CustomButton>
        <CustomButton variant="contained" color="primary" startIcon={<Add />}>
          Add Transaction
        </CustomButton>
      </Box>
      </Box>
    </Box>
  );
};

export default FinancialTransactions;
