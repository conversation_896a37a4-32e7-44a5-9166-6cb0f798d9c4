const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { validateBreedingRecord, validateObjectId, validatePagination } = require('../middleware/validation');
const { getCollection } = require('../config/database');
const { ObjectId } = require('mongodb');
const logger = require('../utils/logger');

// GET all breeding records
router.get('/', authenticateToken, validatePagination, async (req, res) => {
  try {
    const { page = 1, limit = 10, animalId, status } = req.query;
    const skip = (page - 1) * limit;

    const breedingCollection = await getCollection('breeding_records');
    if (!breedingCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed'
      });
    }

    // Build query
    const query = {};
    if (animalId) {
      query.$or = [
        { femaleId: new ObjectId(animalId) },
        { maleId: new ObjectId(animalId) }
      ];
    }
    if (status) {
      query.status = status;
    }

    // Get records with pagination
    const records = await breedingCollection
      .find(query)
      .sort({ breedingDate: -1 })
      .skip(parseInt(skip))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await breedingCollection.countDocuments(query);

    res.json({
      success: true,
      data: records,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    logger.error('Error fetching breeding records:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch breeding records',
      error: error.message
    });
  }
});

// POST new breeding record
router.post('/', authenticateToken, validateBreedingRecord, async (req, res) => {
  try {
    const breedingCollection = await getCollection('breeding_records');
    if (!breedingCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed'
      });
    }

    const breedingRecord = {
      ...req.body,
      femaleId: new ObjectId(req.body.femaleId),
      maleId: new ObjectId(req.body.maleId),
      breedingDate: new Date(req.body.breedingDate),
      expectedCalvingDate: req.body.expectedCalvingDate ? new Date(req.body.expectedCalvingDate) : null,
      status: 'active',
      createdBy: new ObjectId(req.user.id),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await breedingCollection.insertOne(breedingRecord);

    if (result.insertedId) {
      const newRecord = await breedingCollection.findOne({ _id: result.insertedId });
      res.status(201).json({
        success: true,
        message: 'Breeding record created successfully',
        data: newRecord
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Failed to create breeding record'
      });
    }
  } catch (error) {
    logger.error('Error creating breeding record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create breeding record',
      error: error.message
    });
  }
});

// GET breeding record by ID
router.get('/:id', authenticateToken, validateObjectId, async (req, res) => {
  try {
    const breedingCollection = await getCollection('breeding_records');
    if (!breedingCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed'
      });
    }

    const record = await breedingCollection.findOne({ _id: new ObjectId(req.params.id) });

    if (!record) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    res.json({
      success: true,
      data: record
    });
  } catch (error) {
    logger.error('Error fetching breeding record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch breeding record',
      error: error.message
    });
  }
});

// PUT update breeding record
router.put('/:id', authenticateToken, validateObjectId, async (req, res) => {
  try {
    const breedingCollection = await getCollection('breeding_records');
    if (!breedingCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed'
      });
    }

    const updateData = {
      ...req.body,
      updatedBy: new ObjectId(req.user.id),
      updatedAt: new Date()
    };

    // Convert date strings to Date objects
    if (updateData.breedingDate) {
      updateData.breedingDate = new Date(updateData.breedingDate);
    }
    if (updateData.expectedCalvingDate) {
      updateData.expectedCalvingDate = new Date(updateData.expectedCalvingDate);
    }
    if (updateData.actualCalvingDate) {
      updateData.actualCalvingDate = new Date(updateData.actualCalvingDate);
    }

    const result = await breedingCollection.updateOne(
      { _id: new ObjectId(req.params.id) },
      { $set: updateData }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    const updatedRecord = await breedingCollection.findOne({ _id: new ObjectId(req.params.id) });

    res.json({
      success: true,
      message: 'Breeding record updated successfully',
      data: updatedRecord
    });
  } catch (error) {
    logger.error('Error updating breeding record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update breeding record',
      error: error.message
    });
  }
});

// DELETE breeding record
router.delete('/:id', authenticateToken, validateObjectId, async (req, res) => {
  try {
    const breedingCollection = await getCollection('breeding_records');
    if (!breedingCollection) {
      return res.status(500).json({
        success: false,
        message: 'Database connection failed'
      });
    }

    const result = await breedingCollection.deleteOne({ _id: new ObjectId(req.params.id) });

    if (result.deletedCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Breeding record not found'
      });
    }

    res.json({
      success: true,
      message: 'Breeding record deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting breeding record:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete breeding record',
      error: error.message
    });
  }
});

module.exports = router;