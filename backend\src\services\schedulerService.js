/**
 * Scheduler Service
 * 
 * Service for scheduling periodic tasks in the application.
 */

const cron = require('node-cron');
const logger = require('../utils/logger');
const supplierIntegrationService = require('./supplierIntegrationService');

/**
 * Initialize all scheduled tasks
 */
function initializeScheduledTasks() {
  logger.info('Initializing scheduled tasks');
  
  // Schedule supplier data updates every 12 hours
  // Runs at 00:00 and 12:00 every day
  cron.schedule('0 0,12 * * *', async () => {
    logger.info('Running scheduled supplier data update');
    try {
      await supplierIntegrationService.updateSupplierData();
      logger.info('Scheduled supplier data update completed successfully');
    } catch (error) {
      logger.error('Error in scheduled supplier data update:', error);
    }
  });
  
  logger.info('Scheduled tasks initialized');
}

module.exports = {
  initializeScheduledTasks
};
