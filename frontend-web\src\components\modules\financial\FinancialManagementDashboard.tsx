import React from 'react';
import { CustomButton } from '../../components/common';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { IconButton, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Paper } from '@mui/material';
import {  Add, Visibility, Edit  } from '../../../utils/iconImports';
import { motion } from 'framer-motion';
import { ModuleMetric, QuickAction } from '../../ModuleDashboard';

const FinancialManagementDashboard: React.FC = () => {
  const metrics: ModuleMetric[] = [
    { label: 'Monthly Revenue', value: 'R12,450', trend: '+8%', status: 'positive' },
    { label: 'Expenses', value: 'R8,320', trend: '-3%', status: 'positive' },
    { label: 'Net Profit', value: 'R4,130', trend: '+15%', status: 'positive' },
    { label: 'ROI', value: '18.5%', trend: '+2%', status: 'positive' }
  ];

  const quickActions: QuickAction[] = [
    { name: 'Add Transaction', icon: '💰', path: `${ROUTES.FINANCIAL}/transaction` },
    { name: 'View Reports', icon: '📊', path: `${ROUTES.FINANCIAL}/reports` },
    { name: 'Budgets', icon: '📑', path: `${ROUTES.FINANCIAL}/budgets` },
    { name: 'Cost Analysis', icon: '📈', path: `${ROUTES.FINANCIAL}/analysis` }
  ];

  // Mock financial transactions with ZAR currency
  const transactions = [
    { id: 'TRX001', date: '2024-04-10', type: 'Sale', description: 'Livestock sale - 5 cattle', amount: 'R85,000', status: 'Completed' },
    { id: 'TRX002', date: '2024-04-08', type: 'Expense', description: 'Feed purchase', amount: 'R12,500', status: 'Completed' },
    { id: 'TRX003', date: '2024-04-05', type: 'Expense', description: 'Veterinary services', amount: 'R7,500', status: 'Completed' },
    { id: 'TRX004', date: '2024-04-03', type: 'Sale', description: 'Milk production', amount: 'R32,000', status: 'Completed' },
    { id: 'TRX005', date: '2024-04-01', type: 'Expense', description: 'Equipment maintenance', amount: 'R4,500', status: 'Completed' }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Financial Management Dashboard</h1>
          <p className="text-gray-600">Track and manage your farm's financial performance</p>
        </div>
        <CustomButton
          variant="contained"
          startIcon={<Add />}
          className="bg-primary-600"
          component={Link}
          to={`${ROUTES.FINANCIAL}/transaction`}
        >
          Add Transaction
        </CustomButton>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-md p-4"
          >
            <div className="flex justify-between items-start">
              <h3 className="text-gray-500 text-sm">{metric.label}</h3>
              <span className={`text-sm px-2 py-1 rounded ${
                metric.status === 'positive' ? 'bg-green-100 text-green-800' :
                metric.status === 'negative' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {metric.trend}
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-800 mt-2">{metric.value}</p>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {quickActions.map((action) => (
          <Link key={action.name} to={action.path}>
            <motion.div
              whileHover={{ scale: 1.03 }}
              className="bg-white rounded-xl shadow-md p-4 flex flex-col items-center justify-center text-center h-32 cursor-pointer"
            >
              <span className="text-3xl mb-2">{action.icon}</span>
              <h3 className="font-medium text-gray-800">{action.name}</h3>
            </motion.div>
          </Link>
        ))}
      </div>

      {/* Recent Transactions */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Recent Transactions</h2>
        <TableContainer component={Paper} className="shadow-md">
          <Table>
            <TableHead className="bg-gray-100">
              <TableRow>
                <TableCell><strong>ID</strong></TableCell>
                <TableCell><strong>Date</strong></TableCell>
                <TableCell><strong>Type</strong></TableCell>
                <TableCell><strong>Description</strong></TableCell>
                <TableCell><strong>Amount</strong></TableCell>
                <TableCell><strong>Status</strong></TableCell>
                <TableCell><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id} hover>
                  <TableCell>{transaction.id}</TableCell>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      transaction.type === 'Sale' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {transaction.type}
                    </span>
                  </TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>{transaction.amount}</TableCell>
                  <TableCell>{transaction.status}</TableCell>
                  <TableCell>
                    <IconButton size="small">
                      <Visibility fontSize="small" />
                    </IconButton>
                    <IconButton size="small">
                      <Edit fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <CustomButton
          variant="outlined"
          className="mt-4"
          component={Link}
          to={ROUTES.FINANCIAL}
        >
          View All Transactions
        </CustomButton>
      </div>
    </div>
  );
};

export default FinancialManagementDashboard;