/**
 * This file contains a monkey patch for the MUI Button component.
 * It directly modifies the Button component to prevent the "Cannot read properties of undefined (reading 'dark')" error.
 */

// Apply the monkey patch
(function() {
  // Wait for the document to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyPatch);
  } else {
    applyPatch();
  }
  
  // Apply the patch again after a delay to ensure all components are loaded
  setTimeout(applyPatch, 1000);
  setTimeout(applyPatch, 2000);
  setTimeout(applyPatch, 3000);
  
  function applyPatch() {
    try {
      // Find the Button component in the window object
      const muiButtonModule = window.__MUI_BUTTON_MODULE__;
      
      if (muiButtonModule) {
        console.log('Found MUI Button module, applying patch...');
        
        // Store the original Button component
        const originalButton = muiButtonModule.default;
        
        // Override the Button component
        muiButtonModule.default = function(props) {
          // Create a safe version of the props
          const safeProps = { ...props };
          
          // Ensure the theme exists
          if (!safeProps.theme) {
            safeProps.theme = {
              palette: {
                primary: {
                  main: '#3AA99F',
                  light: '#4FBEB4',
                  dark: '#2A8A82',
                  contrastText: '#ffffff'
                },
                secondary: {
                  main: '#38B2AC',
                  light: '#4FD1CB',
                  dark: '#2C8A84',
                  contrastText: '#ffffff'
                }
              }
            };
          } else if (safeProps.theme.palette) {
            // Ensure primary palette exists
            if (!safeProps.theme.palette.primary) {
              safeProps.theme.palette.primary = {
                main: '#3AA99F',
                light: '#4FBEB4',
                dark: '#2A8A82',
                contrastText: '#ffffff'
              };
            } else {
              // Ensure primary.dark exists
              if (!safeProps.theme.palette.primary.dark) {
                safeProps.theme.palette.primary.dark = safeProps.theme.palette.primary.main || '#2A8A82';
              }
            }
            
            // Ensure secondary palette exists
            if (!safeProps.theme.palette.secondary) {
              safeProps.theme.palette.secondary = {
                main: '#38B2AC',
                light: '#4FD1CB',
                dark: '#2C8A84',
                contrastText: '#ffffff'
              };
            } else {
              // Ensure secondary.dark exists
              if (!safeProps.theme.palette.secondary.dark) {
                safeProps.theme.palette.secondary.dark = safeProps.theme.palette.secondary.main || '#2C8A84';
              }
            }
          }
          
          // Call the original Button component with the safe props
          return originalButton(safeProps);
        };
        
        console.log('MUI Button patch applied successfully');
      } else {
        // Try to find the Button component in the DOM
        const buttonElements = document.querySelectorAll('.MuiButton-root');
        
        if (buttonElements.length > 0) {
          console.log('Found', buttonElements.length, 'Button components, applying direct styles...');
          
          // Apply direct CSS styles to each Button component
          buttonElements.forEach(button => {
            // Apply base styles
            button.style.display = 'inline-flex';
            button.style.alignItems = 'center';
            button.style.justifyContent = 'center';
            button.style.position = 'relative';
            button.style.boxSizing = 'border-box';
            button.style.outline = '0';
            button.style.border = '0';
            button.style.margin = '0';
            button.style.cursor = 'pointer';
            button.style.userSelect = 'none';
            button.style.verticalAlign = 'middle';
            button.style.textDecoration = 'none';
            button.style.fontWeight = '500';
            button.style.fontSize = '0.875rem';
            button.style.lineHeight = '1.75';
            button.style.letterSpacing = '0.02857em';
            button.style.textTransform = 'uppercase';
            button.style.minWidth = '64px';
            button.style.padding = '6px 16px';
            button.style.borderRadius = '4px';
            button.style.transition = 'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms';
            
            // Apply variant-specific styles
            if (button.classList.contains('MuiButton-contained')) {
              button.style.color = '#fff';
              button.style.backgroundColor = '#3AA99F';
              button.style.boxShadow = '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)';
            } else if (button.classList.contains('MuiButton-outlined')) {
              button.style.border = '1px solid rgba(0, 0, 0, 0.23)';
              button.style.padding = '5px 15px';
              button.style.color = '#3AA99F';
              button.style.backgroundColor = 'transparent';
            } else if (button.classList.contains('MuiButton-text')) {
              button.style.padding = '6px 8px';
              button.style.color = '#3AA99F';
              button.style.backgroundColor = 'transparent';
            }
          });
          
          console.log('Applied direct CSS styles to Button components');
        } else {
          console.log('No Button components found, patch not applied');
        }
      }
    } catch (error) {
      console.error('Error applying Button patch:', error);
    }
  }
})();
