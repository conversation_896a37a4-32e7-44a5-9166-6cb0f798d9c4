export interface FollowUpDate {
  date: string;
  type: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  notes: string;
}

export interface BreedingRecord {
  id: string;
  _id?: string; // MongoDB ID
  femaleId: string;
  maleId: string;
  sireId?: string; // Added for compatibility
  sireName?: string; // Added for compatibility
  date: string;
  breedingDate?: string; // Added for compatibility
  type: 'natural' | 'artificial';
  method?: 'natural' | 'artificial'; // Added for compatibility
  status: 'confirmed' | 'pending' | 'unsuccessful' | 'scheduled';
  expectedDueDate: string;
  technician: string;
  notes: string;
  followUpDates: FollowUpDate[];
}

export interface CalfDetails {
  tagNumber: string;
  gender: 'male' | 'female';
  weight: number;
  health: 'healthy' | 'weak' | 'sick' | 'deceased';
}

export interface CalvingRecord {
  id: string;
  breedingRecordId: string;
  date: string;
  calfDetails: CalfDetails;
  complications: string | null;
  assistanceRequired: boolean;
  notes?: string;
}
