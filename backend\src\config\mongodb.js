/**
 * MongoDB Configuration
 *
 * This module provides functions to connect to MongoDB and access collections
 */

const { MongoClient } = require('mongodb');
const logger = require('../utils/logger');
require('dotenv').config();

// MongoDB connection string
const uri = process.env.MONGODB_URI || "mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const getDbName = () => process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Log connection information (without exposing credentials)
logger.info(`MongoDB URI: ${uri ? uri.replace(/:[^:]*@/, ':****@') : 'URI is not set'}`);
logger.info(`MongoDB DB Name: ${getDbName()}`);

// MongoDB client instance
let client = null;
let db = null;
let isConnected = false;

/**
 * Connect to MongoDB
 * @returns {Promise<Object>} MongoDB database instance
 */
async function connectDB() {
  if (isConnected && db) {
    logger.info('Already connected to MongoDB');
    return { client, db };
  }

  try {
    logger.info('Connecting to MongoDB...');
    logger.info(`Using connection string: ${uri.replace(/:[^:]*@/, ':****@')}`);
    logger.info(`Using database: ${getDbName()}`);

    // Add detailed logging to the connectDB function
    logger.info('MongoDB URI:', uri);
    logger.info('MongoDB Database Name:', getDbName());

    // Log connection options
    logger.info('Connection options:', {
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      w: 'majority',
      maxPoolSize: 10
    });

    client = new MongoClient(uri, {
      authSource: 'admin', // Explicitly set the authentication database
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000,
      retryWrites: true,
      w: 'majority',
      maxPoolSize: 10 // Maintain up to 10 socket connections
    });

    await client.connect();
    db = client.db(getDbName());
    isConnected = true;

    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    logger.info('Successfully connected to MongoDB!');

    // Set up connection event handlers
    client.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
      isConnected = false;
    });

    client.on('close', () => {
      logger.warn('MongoDB connection closed');
      isConnected = false;
      db = null;

      // Attempt to reconnect after a delay
      setTimeout(async () => {
        logger.info('Attempting to reconnect to MongoDB...');
        try {
          await connectDB();
        } catch (err) {
          logger.error('Failed to reconnect to MongoDB:', err);
        }
      }, 5000);
    });

    client.on('reconnect', () => {
      logger.info('MongoDB reconnected');
      isConnected = true;
    });

    return { client, db };
  } catch (error) {
    logger.error('Error connecting to MongoDB:', error);
    isConnected = false;
    db = null;

    // Implement connection retry logic
    setTimeout(async () => {
      logger.info('Attempting to reconnect to MongoDB...');
      try {
        await connectDB();
      } catch (err) {
        logger.error('Failed to reconnect to MongoDB:', err);
      }
    }, 5000);

    throw error;
  }
}

/**
 * Disconnect from MongoDB
 */
async function disconnectDB() {
  if (!isConnected || !client) {
    return;
  }

  try {
    await client.close();
    isConnected = false;
    db = null;
    client = null;
    logger.info('Disconnected from MongoDB');
  } catch (error) {
    logger.error('Failed to disconnect from MongoDB', error);
    throw error;
  }
}

/**
 * Get MongoDB database instance
 */
function getDB() {
  if (!isConnected || !db) {
    logger.warn('Not connected to MongoDB. Call connectDB() first.');
    throw new Error('Not connected to MongoDB. Call connectDB() first.');
  }
  return db;
}

/**
 * Get a collection from the database
 * @param {string} collectionName The name of the collection
 */
async function getCollection(collectionName) {
  try {
    if (!isConnected) {
      logger.info('Not connected to MongoDB. Attempting to connect...');
      await connectDB();
    }

    return db.collection(collectionName);
  } catch (error) {
    logger.error(`Error getting collection ${collectionName}:`, error);
    throw error;
  }
}

/**
 * Check MongoDB connection
 * @returns {Promise<boolean>} Connection status
 */
async function checkConnection() {
  try {
    if (!isConnected) {
      await connectDB();
    }

    // Ping the database
    await db.command({ ping: 1 });
    logger.info('MongoDB connection check successful');
    return true;
  } catch (error) {
    logger.error('MongoDB connection check failed:', error);
    isConnected = false;
    return false;
  }
}

/**
 * Create indexes for collections
 */
async function createIndexes() {
  try {
    if (!isConnected) {
      await connectDB();
    }

    // Create indexes for animals collection
    const animalsCollection = db.collection('animals');

    try {
      // First, fix any null tagNumber values to prevent duplicate key errors
      await animalsCollection.updateMany(
        { tagNumber: null },
        { $set: { tagNumber: `untagged-${Date.now()}-${Math.floor(Math.random() * 1000)}` } }
      );

      // Then create the indexes
      await animalsCollection.createIndex({ tagNumber: 1 }, {
        unique: true,
        sparse: true // Skip documents where tagNumber is null
      });
      await animalsCollection.createIndex({ species: 1 });
      await animalsCollection.createIndex({ status: 1 });
    } catch (indexError) {
      logger.warn('Error creating animal indexes, continuing anyway:', indexError.message);
    }

    // Create indexes for users collection
    const usersCollection = db.collection('users');
    await usersCollection.createIndex({ username: 1 }, { unique: true });
    await usersCollection.createIndex({ email: 1 }, { unique: true });

    logger.info('MongoDB indexes created successfully');
  } catch (error) {
    logger.error('Error creating MongoDB indexes:', error);
  }
}

/**
 * Get the MongoDB connection status
 * @returns {Object} Connection status object
 */
function getConnectionStatus() {
  return {
    connected: isConnected,
    client: client ? true : false,
    database: db ? getDbName() : null,
    uri: uri ? uri.replace(/:[^:]*@/, ':****@') : null
  };
}

module.exports = {
  connectDB,
  disconnectDB,
  getDB,
  getCollection,
  checkConnection,
  createIndexes,
  getConnectionStatus,
  client
};
