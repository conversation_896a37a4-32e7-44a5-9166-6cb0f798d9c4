import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { useNavigate } from 'react-router-dom';
import { useMockData } from '../../utils/config';
import api from '../../services/apiService';
import { mockAnimals } from '../../mocks';
import { Animal as AnimalType } from '../../types/animal';
import { motion } from 'framer-motion';
import AnimalMarketplaceCard from '../modules/animals/AnimalMarketplaceCard';
import { ROUTES } from '../../constants/routes';
import { CircularProgress, Alert, Grid, Paper, Box, Typography, IconButton, Chip, Tooltip, TextField, InputAdornment, useTheme, alpha, Menu, MenuItem,  } from '@mui/material';
import {
  Edit,
  Delete,
  Visibility,
  Search,
  FilterList,
  GetApp,
  Add,
  Pets,
  LocationOn,
  LocalHospital,
} from '../../utils/iconImports';

interface Animal {
  id: string;
  tagNumber: string;
  species: string;
  breed: string;
  dateOfBirth: string;
  status: string;
  currentLocation: string;
  currentWeight: number;
  healthStatus: string;
}

const AnimalList: React.FC = () => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterBy, setFilterBy] = useState<string>('all');
  const navigate = useNavigate();
  const theme = useTheme();

  // MongoDB connection removed
  // const { isConnected } = useMongoDb();

  useEffect(() => {
    fetchAnimals();
  }, []);

  // Define the mock data fallback function outside of fetchAnimals
  const loadMockData = async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    // Convert mockAnimals to the local Animal interface
    const convertedAnimals = mockAnimals.map(animal => ({
      id: animal.id.toString(),
      tagNumber: animal.tagNumber || '',
      species: animal.species || '',
      breed: animal.breed || '',
      dateOfBirth: String(animal.birthDate || ''),
      status: animal.status || '',
      currentLocation: animal.location || '',
      currentWeight: animal.weight || 0,
      healthStatus: animal.healthStatus || ''
    }));
    setAnimals(convertedAnimals);
  };

  // Get unique species for filter
  const uniqueSpecies = animals.length > 0 ?
    Array.from(new Set(animals.map(animal => animal.species))) :
    [];

  // Handle export to CSV
  const exportToCSV = () => {
    const headers = ['Tag Number', 'Species', 'Breed', 'Age', 'Status', 'Location', 'Health Status'];
    const csvData = filteredAnimals.map(animal => [
      animal.tagNumber,
      animal.species,
      animal.breed,
      `${new Date().getFullYear() - new Date(animal.dateOfBirth).getFullYear()} years`,
      animal.status,
      animal.currentLocation,
      animal.healthStatus
    ]);

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'animals.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const fetchAnimals = async () => {
    try {
      setLoading(true);

      // Always use mock data for now
      if (useMockData) {
        // Use mock data
        await loadMockData();
      } else {
        // Use API
        const response = await api.get('/animals');
        setAnimals(response.data);
      }
    } catch (err) {
      setError('Failed to fetch animals');
      console.error('Error fetching animals:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter animals based on search term and filter selection
  const filteredAnimals = animals
    .filter(animal => {
      if (filterBy === 'all') return true;
      return animal.species.toLowerCase() === filterBy.toLowerCase();
    })
    .filter(animal => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        animal.tagNumber.toLowerCase().includes(searchLower) ||
        animal.species.toLowerCase().includes(searchLower) ||
        animal.breed.toLowerCase().includes(searchLower) ||
        animal.currentLocation.toLowerCase().includes(searchLower) ||
        animal.healthStatus.toLowerCase().includes(searchLower)
      );
    })
    .slice(0, 5); // Limit to 5 records as per requirement

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2, fontSize: '1rem' }}>
        {error}
      </Alert>
    );
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  // Handle view animal
  const handleViewAnimal = (animal: any) => {
    navigate(`${ROUTES.ANIMALS}/${animal.id}`);
  };

  // Handle edit animal
  const handleEditAnimal = (animal: any) => {
    navigate(`${ROUTES.ANIMALS}/edit/${animal.id}`);
  };

  // Handle delete animal
  const handleDeleteAnimal = (animal: any) => {
    // In a real implementation, this would show a confirmation dialog
    console.log(`Delete animal ${animal.id}`);
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Search and Filter Controls */}
      <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 2, alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', flexGrow: 1, maxWidth: '500px' }}>
          <TextField
            fullWidth
            placeholder="Search animals..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            variant="outlined"
            size="small"
            sx={{
              mr: 1,
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                fontSize: '1rem',
                '&.Mui-focused fieldset': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '2px'
                }
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search color="action" />
                </InputAdornment>
              ),
            }}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
            {uniqueSpecies.map(species => (
              <Chip
                key={species}
                label={species}
                clickable
                color={filterBy === species ? 'primary' : 'default'}
                onClick={() => setFilterBy(filterBy === species ? 'all' : species)}
                sx={{ mr: 0.5, fontSize: '0.9rem' }}
              />
            ))}
            {filterBy !== 'all' && (
              <Chip
                label="Clear Filter"
                color="secondary"
                onClick={() => setFilterBy('all')}
                onDelete={() => setFilterBy('all')}
                sx={{ fontSize: '0.9rem' }}
              />
            )}
          </Box>
        </Box>
        <Box>
          <Tooltip title="Export to CSV">
            <IconButton onClick={exportToCSV} color="primary" sx={{ mr: 1 }}>
              <GetApp />
            </IconButton>
          </Tooltip>
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<Add />}
            onClick={() => navigate('/animals/new')}
            sx={{
              borderRadius: '8px',
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
              fontSize: '1rem',
              fontWeight: 'bold'
            }}
          >
            Add New Animal
          </CustomButton>
        </Box>
      </Box>

      {/* Results Summary */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Showing {filteredAnimals.length} of {animals.length} animals
          {filterBy !== 'all' && ` (filtered by ${filterBy})`}
          {searchTerm && ` (search: "${searchTerm}")`}
        </Typography>
      </Box>

      {/* Animal Cards Grid */}
      {filteredAnimals.length > 0 ? (
        <Grid container spacing={3}>
          {filteredAnimals.map((animal, index) => {
            // Convert to the AnimalType expected by AnimalProfileCard
            const profileAnimal: AnimalType = {
              id: animal.id,
              tagNumber: animal.tagNumber,
              name: animal.tagNumber, // Use tag number as name if not available
              type: animal.species,
              species: animal.species,
              breed: animal.breed,
              gender: 'Unknown', // Default value
              birthDate: animal.dateOfBirth,
              weight: animal.currentWeight,
              status: animal.status,
              healthStatus: animal.healthStatus.toLowerCase() as any,
              location: animal.currentLocation,
              rfidTag: `RFID-${animal.tagNumber}`
            };

            return (
              <Grid item xs={12} sm={6} md={4} lg={3} key={animal.id}>
                <motion.div variants={itemVariants}>
                  <AnimalMarketplaceCard
                    animal={profileAnimal}
                    onView={handleViewAnimal}
                    onAction={handleEditAnimal}
                    delay={index * 0.05}
                  />
                </motion.div>
              </Grid>
            );
          })}
        </Grid>
      ) : (
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            No animals found matching your criteria
          </Typography>
          <CustomButton
            variant="outlined"
            color="primary"
            sx={{ mt: 2 }}
            onClick={() => {
              setSearchTerm('');
              setFilterBy('all');
            }}
          >
            Clear Filters
          </CustomButton>
        </Box>
      )}
    </motion.div>
  );
};

export default AnimalList;

