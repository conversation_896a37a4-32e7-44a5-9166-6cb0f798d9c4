# AgriIntel Local Development Guide

This guide provides instructions for setting up and maintaining your AgriIntel project locally, without relying on cloud services. This approach helps save data and avoids reinstalling packages repeatedly.

## Available Scripts

The following batch files are available to help manage your project:

### `start-app.bat`
Starts both the backend and frontend servers in separate command windows.

### `cleanup.bat`
Cleans up your project by removing:
- Build artifacts
- Unused dependencies
- Temporary files
- Duplicate node_modules folders

### `setup-local-mongodb.bat`
Sets up a local MongoDB instance for development instead of using the cloud connection.

### `optimize-project.bat`
Optimizes your project structure by:
- Installing dependencies with better disk usage (using pnpm)
- Setting up local npm cache
- Configuring for offline development

### `backup-project.bat`
Creates a local backup of your project to a specified directory.

## Local Development Best Practices

### 1. Use Local Storage Instead of OneDrive

To prevent syncing your project to OneDrive:
1. Move the project to a local drive (e.g., `C:\Projects\ampd-livestock`)
2. Exclude the folder from OneDrive sync:
   - Right-click the folder in OneDrive
   - Select "Always keep on this device" or exclude it in OneDrive settings

### 2. Avoid Reinstalling Packages

- Use `npm ci` instead of `npm install` when possible
- The `.npmrc` file is configured to prefer offline packages
- Consider using `pnpm` which has better disk space usage

### 3. Regular Maintenance

Run these maintenance tasks regularly:
- `cleanup.bat` - to remove unused files and dependencies
- `backup-project.bat` - to create local backups

### 4. Local MongoDB

Using a local MongoDB instance:
1. Install MongoDB Community Edition
2. Run `setup-local-mongodb.bat`
3. Start MongoDB with `mongod`
4. Use the generated `.env.local` file

## Troubleshooting

### Node Modules Issues
If you encounter issues with node modules:
1. Delete the node_modules folder
2. Run `npm cache clean --force`
3. Run `npm install` or `pnpm install`

### MongoDB Connection Issues
If you can't connect to MongoDB:
1. Check if MongoDB is running with `mongod`
2. Verify your connection string in the `.env` file
3. Try connecting with MongoDB Compass to test the connection

## Additional Resources

- [MongoDB Community Edition](https://www.mongodb.com/try/download/community)
- [pnpm Documentation](https://pnpm.io/motivation)
- [npm Documentation](https://docs.npmjs.com/)
