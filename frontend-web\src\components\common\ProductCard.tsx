import React from 'react';
import { CustomButton } from '../common';
import { Typography, Box, Rating, Chip, useTheme } from '@mui/material';
import BlendedBackgroundCard from './BlendedBackgroundCard';

interface ProductCardProps {
  id: string;
  title: string;
  description: string;
  price: number;
  currency?: string;
  rating?: number;
  reviewCount?: number;
  backgroundImage: string;
  inStock?: boolean;
  discountPercentage?: number;
  onAddToCart?: () => void;
  onViewDetails?: () => void;
  primaryColor?: string;
  secondaryColor?: string;
}

/**
 * Product card component for marketplace items
 * Displays product information with a blended background image
 */
const ProductCard: React.FC<ProductCardProps> = ({
  id,
  title,
  description,
  price,
  currency = 'R',
  rating = 0,
  reviewCount = 0,
  backgroundImage,
  inStock = true,
  discountPercentage,
  onAddToCart,
  onViewDetails,
  primaryColor,
  secondaryColor,
}) => {
  const theme = useTheme();

  // Format price with currency
  const formattedPrice = `${currency}${price.toLocaleString()}`;
  
  // Calculate discounted price if applicable
  const discountedPrice = discountPercentage 
    ? price - (price * (discountPercentage / 100)) 
    : null;
  
  // Format discounted price with currency
  const formattedDiscountedPrice = discountedPrice 
    ? `${currency}${discountedPrice.toLocaleString()}` 
    : null;

  return (
    <BlendedBackgroundCard
      backgroundImage={backgroundImage}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      height={320}
      title={
        <Box>
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            {description}
          </Typography>
        </Box>
      }
      content={
        <Box sx={{ mt: 2 }}>
          {/* Price Section */}
          <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
            <Typography variant="h5" fontWeight="bold" sx={{ mr: 1 }}>
              {formattedDiscountedPrice || formattedPrice}
            </Typography>
            
            {discountedPrice && (
              <Typography 
                variant="body2" 
                sx={{ 
                  textDecoration: 'line-through',
                  opacity: 0.7
                }}
              >
                {formattedPrice}
              </Typography>
            )}
            
            {discountPercentage && (
              <Chip 
                label={`-${discountPercentage}%`} 
                size="small" 
                color="error"
                sx={{ 
                  ml: 1, 
                  height: 20, 
                  fontSize: '0.7rem',
                  fontWeight: 'bold'
                }} 
              />
            )}
          </Box>
          
          {/* Rating Section */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Rating 
              value={rating} 
              precision={0.5} 
              size="small" 
              readOnly 
              sx={{ color: theme.palette.warning.light }}
            />
            <Typography variant="body2" sx={{ ml: 1, opacity: 0.9 }}>
              {rating.toFixed(1)} ({reviewCount})
            </Typography>
          </Box>
          
          {/* Stock Status */}
          <Chip 
            label={inStock ? 'In Stock' : 'Out of Stock'} 
            size="small" 
            color={inStock ? 'success' : 'error'}
            variant="outlined"
            sx={{ 
              height: 20, 
              fontSize: '0.7rem',
              backgroundColor: inStock 
                ? 'rgba(46, 125, 50, 0.1)' 
                : 'rgba(211, 47, 47, 0.1)',
              borderColor: inStock 
                ? 'rgba(46, 125, 50, 0.3)' 
                : 'rgba(211, 47, 47, 0.3)',
            }} 
          />
        </Box>
      }
      action={
        <Box sx={{ display: 'flex', gap: 1 }}>
          <CustomButton 
            variant="contained" 
            size="small" 
            fullWidth
            onClick={onAddToCart}
            disabled={!inStock}
            sx={{ 
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.3)',
              }
            }}
          >
            Add to Cart
          </CustomButton>
          
          <CustomButton 
            variant="outlined" 
            size="small" 
            onClick={onViewDetails}
            sx={{ 
              borderColor: 'rgba(255, 255, 255, 0.5)',
              color: 'white',
              '&:hover': {
                borderColor: 'white',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
              }
            }}
          >
            Details
          </CustomButton>
        </Box>
      }
    />
  );
};

export default ProductCard;
