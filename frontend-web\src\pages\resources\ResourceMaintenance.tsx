import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Paper, Grid, Card, CardContent, Chip, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tabs, Tab, List, ListItem, ListItemText, ListItemAvatar, Avatar, Divider } from '@mui/material';
import {  
  Add, 
  Build, 
  CheckCircle, 
  Warning, 
  Schedule, 
  CalendarToday,
  Edit,
  Delete
 } from '../../utils/iconImports';

// Mock maintenance data
const mockMaintenanceRecords = [
  { 
    id: 'MR001', 
    resourceId: 'RES001',
    resourceName: 'Tractor #1',
    resourceType: 'Equipment',
    maintenanceType: 'Preventive',
    description: 'Regular oil change and filter replacement',
    scheduledDate: '2023-06-20',
    completedDate: null,
    status: 'scheduled',
    assignedTo: '<PERSON>',
    estimatedCost: 250,
    actualCost: null,
    notes: 'Order filters in advance'
  },
  { 
    id: 'MR002', 
    resourceId: 'RES005',
    resourceName: 'Feed Mixer',
    resourceType: 'Equipment',
    maintenanceType: 'Repair',
    description: 'Fix broken belt and lubricate bearings',
    scheduledDate: '2023-06-15',
    completedDate: '2023-06-15',
    status: 'completed',
    assignedTo: 'Mike <PERSON>',
    estimatedCost: 350,
    actualCost: 320,
    notes: 'Replaced belt with heavy-duty version'
  },
  { 
    id: 'MR003', 
    resourceId: 'RES008',
    resourceName: 'Barn Roof',
    resourceType: 'Facility',
    maintenanceType: 'Repair',
    description: 'Fix leak in northwest corner',
    scheduledDate: '2023-06-18',
    completedDate: null,
    status: 'in-progress',
    assignedTo: 'Construction Team',
    estimatedCost: 1200,
    actualCost: null,
    notes: 'Materials delivered, work started'
  },
  { 
    id: 'MR004', 
    resourceId: 'RES003',
    resourceName: 'Water Pump',
    resourceType: 'Equipment',
    maintenanceType: 'Preventive',
    description: 'Annual inspection and maintenance',
    scheduledDate: '2023-06-25',
    completedDate: null,
    status: 'scheduled',
    assignedTo: 'John Smith',
    estimatedCost: 150,
    actualCost: null,
    notes: 'Check for signs of wear on impeller'
  },
  { 
    id: 'MR005', 
    resourceId: 'RES012',
    resourceName: 'Fencing - North Pasture',
    resourceType: 'Infrastructure',
    maintenanceType: 'Repair',
    description: 'Replace damaged posts and wires',
    scheduledDate: '2023-06-10',
    completedDate: '2023-06-12',
    status: 'completed',
    assignedTo: 'Farm Hands',
    estimatedCost: 500,
    actualCost: 580,
    notes: 'Needed more materials than estimated'
  },
  { 
    id: 'MR006', 
    resourceId: 'RES007',
    resourceName: 'Milking Machine',
    resourceType: 'Equipment',
    maintenanceType: 'Emergency',
    description: 'Repair vacuum pump failure',
    scheduledDate: '2023-06-14',
    completedDate: '2023-06-14',
    status: 'completed',
    assignedTo: 'Service Technician',
    estimatedCost: 800,
    actualCost: 950,
    notes: 'After-hours emergency service'
  },
  { 
    id: 'MR007', 
    resourceId: 'RES002',
    resourceName: 'Tractor #2',
    resourceType: 'Equipment',
    maintenanceType: 'Preventive',
    description: '500-hour service',
    scheduledDate: '2023-07-05',
    completedDate: null,
    status: 'scheduled',
    assignedTo: 'Dealer Service',
    estimatedCost: 650,
    actualCost: null,
    notes: 'Schedule delivery to dealer'
  },
];

// Upcoming maintenance for this week
const upcomingMaintenance = mockMaintenanceRecords
  .filter(record => record.status === 'scheduled')
  .sort((a, b) => new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime())
  .slice(0, 3);

const ResourceMaintenance: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Filter maintenance records based on tab
  const filteredRecords = mockMaintenanceRecords.filter(record => {
    if (tabValue === 0) return true; // All
    if (tabValue === 1) return record.status === 'scheduled';
    if (tabValue === 2) return record.status === 'in-progress';
    if (tabValue === 3) return record.status === 'completed';
    return true;
  });

  return (
    <Box p={4}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">Resource Maintenance</Typography>
        <CustomButton variant="contained" color="primary" startIcon={<Add />}>
          Schedule Maintenance
        </CustomButton>
      </Box>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'info.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Total Maintenance Records</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockMaintenanceRecords.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'warning.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Pending Maintenance</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockMaintenanceRecords.filter(r => r.status === 'scheduled' || r.status === 'in-progress').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'success.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Completed This Month</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockMaintenanceRecords.filter(r => r.status === 'completed').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="All Maintenance" />
                <Tab label="Scheduled" />
                <Tab label="In Progress" />
                <Tab label="Completed" />
              </Tabs>
            </Box>

            <TableContainer>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.light' }}>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Resource</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Scheduled Date</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredRecords.map((record) => {
                    let statusColor = 'info';
                    if (record.status === 'completed') statusColor = 'success';
                    if (record.status === 'in-progress') statusColor = 'warning';
                    
                    return (
                      <TableRow key={record.id} hover>
                        <TableCell>{record.id}</TableCell>
                        <TableCell>{record.resourceName}</TableCell>
                        <TableCell>
                          <Chip 
                            label={record.maintenanceType} 
                            size="small"
                            color={record.maintenanceType === 'Emergency' ? 'error' : 'default'}
                          />
                        </TableCell>
                        <TableCell>{record.description}</TableCell>
                        <TableCell>{new Date(record.scheduledDate).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Chip 
                            label={record.status.charAt(0).toUpperCase() + record.status.slice(1)} 
                            color={statusColor as any}
                            icon={
                              record.status === 'completed' ? <CheckCircle /> : 
                              record.status === 'in-progress' ? <Build /> : 
                              <Schedule />
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton size="small" color="primary">
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 3 }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Upcoming Maintenance</Typography>
              <List>
                {upcomingMaintenance.map((record, index) => (
                  <React.Fragment key={record.id}>
                    <ListItem alignItems="flex-start">
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'primary.main' }}>
                          <Build />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center">
                            <Typography variant="subtitle1" fontWeight="bold">{record.resourceName}</Typography>
                            <Typography variant="body2" sx={{ ml: 2 }}>
                              <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center' }}>
                                <CalendarToday fontSize="small" sx={{ mr: 0.5 }} />
                                {new Date(record.scheduledDate).toLocaleDateString()}
                              </Box>
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" component="span">
                              {record.description}
                            </Typography>
                            <Box mt={1}>
                              <Chip 
                                label={record.maintenanceType} 
                                size="small"
                                color={record.maintenanceType === 'Emergency' ? 'error' : 'default'}
                                sx={{ mr: 1 }}
                              />
                              <Chip 
                                label={`$${record.estimatedCost}`} 
                                size="small"
                                color="primary"
                              />
                            </Box>
                          </>
                        }
                      />
                    </ListItem>
                    {index < upcomingMaintenance.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>

          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Maintenance Statistics</Typography>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Preventive Maintenance</Typography>
                <Typography variant="h5" fontWeight="bold">
                  {mockMaintenanceRecords.filter(r => r.maintenanceType === 'Preventive').length}
                </Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">Repair Maintenance</Typography>
                <Typography variant="h5" fontWeight="bold">
                  {mockMaintenanceRecords.filter(r => r.maintenanceType === 'Repair').length}
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Emergency Maintenance</Typography>
                <Typography variant="h5" fontWeight="bold">
                  {mockMaintenanceRecords.filter(r => r.maintenanceType === 'Emergency').length}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ResourceMaintenance;
