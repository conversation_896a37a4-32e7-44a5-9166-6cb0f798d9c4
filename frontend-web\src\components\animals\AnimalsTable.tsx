import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableRow, Paper, TableContainer, IconButton, Tooltip, alpha } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import {  Edit, Delete, Visibility  } from '../../utils/iconImports';
import { Animal } from '../../types/animal';

interface AnimalsTableProps {
  animals: Animal[];
  onView: (animal: Animal) => void;
  onEdit: (animal: Animal) => void;
  onDelete: (animal: Animal) => void;
}

export const AnimalsTable: React.FC<AnimalsTableProps> = ({
  animals,
  onView,
  onEdit,
  onDelete
}) => {
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  return (
    <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px', overflow: 'hidden' }}>
      <Table>
        <TableHead sx={{ bgcolor: alpha(themeColor.primary, 0.1) }}>
          <TableRow>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Tag Number</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Name</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Type</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Breed</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Status</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Health</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Location</TableCell>
            <TableCell sx={{ fontWeight: 'bold', color: themeColor.secondary }}>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {animals.map((animal) => (
            <TableRow key={animal.id} hover sx={{
              '&:hover': {
                backgroundColor: alpha(themeColor.primary, 0.05),
              },
              '& td': {
                borderBottom: `1px solid ${alpha(themeColor.primary, 0.1)}`
              }
            }}>
              <TableCell>{animal.tagNumber}</TableCell>
              <TableCell>{animal.name}</TableCell>
              <TableCell>{animal.type}</TableCell>
              <TableCell>{animal.breed}</TableCell>
              <TableCell>{animal.status}</TableCell>
              <TableCell>{animal.healthStatus}</TableCell>
              <TableCell>{animal.location}</TableCell>
              <TableCell>
                <Tooltip title="View Details">
                  <IconButton
                    onClick={() => onView(animal)}
                    sx={{ color: themeColor.primary }}
                  >
                    <Visibility />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Edit">
                  <IconButton
                    onClick={() => onEdit(animal)}
                    sx={{ color: themeColor.primary }}
                  >
                    <Edit />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Delete">
                  <IconButton
                    onClick={() => onDelete(animal)}
                    sx={{ color: themeColor.primary }}
                  >
                    <Delete />
                  </IconButton>
                </Tooltip>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};