import React, { useState } from 'react';
import {
  <PERSON>, Typography, Tabs, Tab, Table, TableBody, TableCell,
  TableContainer, TableHead, TableRow, Paper, Chip, Grid, Card,
  CardContent, Divider, useTheme, alpha, LinearProgress
} from '@mui/material';
import { motion } from 'framer-motion';
import ModuleHeader from '../components/common/ModuleHeader';
import AnimatedBackgroundCard from '../components/common/AnimatedBackgroundCard';
import { Restaurant, Schedule, Science } from '../utils/iconImports';
import { useResponsive } from '../hooks/useResponsive';
import { getCardStyle, getTableHeaderStyle, getTableRowStyle, getChipStyle } from '../utils/cardStyles';

interface FeedSchedule {
  id: string;
  groupId: string;
  groupName: string;
  feedType: string;
  time: string;
  quantity: number;
  unit: string;
  frequency: string;
  status: string;
}

interface NutritionPlan {
  id: string;
  name: string;
  animalType: string;
  components: {
    feedType: string;
    percentage: number;
    purpose: string;
  }[];
  dailyAmount: number;
  unit: string;
}

const mockFeedSchedules: FeedSchedule[] = [
  {
    id: 'FS001',
    groupId: 'G001',
    groupName: 'Dairy Cattle',
    feedType: 'Mixed Feed',
    time: '06:00',
    quantity: 250,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS002',
    groupId: 'G002',
    groupName: 'Beef Cattle',
    feedType: 'Hay and Supplements',
    time: '15:00',
    quantity: 180,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS003',
    groupId: 'G003',
    groupName: 'Sheep',
    feedType: 'Grain Mix',
    time: '07:30',
    quantity: 120,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS004',
    groupId: 'G004',
    groupName: 'Goats',
    feedType: 'Forage Mix',
    time: '16:30',
    quantity: 90,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS005',
    groupId: 'G005',
    groupName: 'Calves',
    feedType: 'Calf Starter',
    time: '08:00',
    quantity: 75,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  }
];

const mockNutritionPlans: NutritionPlan[] = [
  {
    id: 'NP001',
    name: 'Nutrition Plan for Dairy Cattle',
    animalType: 'Dairy Cattle',
    components: [
      { feedType: 'Alfalfa Hay', percentage: 40, purpose: 'Fiber source' },
      { feedType: 'Grain Mix', percentage: 35, purpose: 'Energy source' },
      { feedType: 'Protein Supplement', percentage: 15, purpose: 'Protein source' },
      { feedType: 'Minerals', percentage: 10, purpose: 'Nutritional balance' }
    ],
    dailyAmount: 25,
    unit: 'kg'
  },
  {
    id: 'NP002',
    name: 'Nutrition Plan for Beef Cattle',
    animalType: 'Beef Cattle',
    components: [
      { feedType: 'Grass Hay', percentage: 45, purpose: 'Base feed' },
      { feedType: 'Corn Silage', percentage: 30, purpose: 'Energy source' },
      { feedType: 'Protein Mix', percentage: 20, purpose: 'Growth support' },
      { feedType: 'Mineral Mix', percentage: 5, purpose: 'Mineral supplementation' }
    ],
    dailyAmount: 20,
    unit: 'kg'
  },
  {
    id: 'NP003',
    name: 'Nutrition Plan for Sheep',
    animalType: 'Sheep',
    components: [
      { feedType: 'Pasture Grass', percentage: 50, purpose: 'Primary feed' },
      { feedType: 'Grain Mix', percentage: 25, purpose: 'Energy supplement' },
      { feedType: 'Protein Pellets', percentage: 15, purpose: 'Protein source' },
      { feedType: 'Mineral Block', percentage: 10, purpose: 'Essential minerals' }
    ],
    dailyAmount: 3,
    unit: 'kg'
  },
  {
    id: 'NP004',
    name: 'Nutrition Plan for Goats',
    animalType: 'Goats',
    components: [
      { feedType: 'Browse Vegetation', percentage: 55, purpose: 'Natural diet' },
      { feedType: 'Alfalfa Pellets', percentage: 20, purpose: 'Protein source' },
      { feedType: 'Grain Mix', percentage: 15, purpose: 'Energy supplement' },
      { feedType: 'Mineral Mix', percentage: 10, purpose: 'Nutritional balance' }
    ],
    dailyAmount: 2.5,
    unit: 'kg'
  },
  {
    id: 'NP005',
    name: 'Nutrition Plan for Calves',
    animalType: 'Calves',
    components: [
      { feedType: 'Milk Replacer', percentage: 40, purpose: 'Primary nutrition' },
      { feedType: 'Calf Starter', percentage: 35, purpose: 'Rumen development' },
      { feedType: 'Fine Hay', percentage: 20, purpose: 'Fiber introduction' },
      { feedType: 'Vitamin Mix', percentage: 5, purpose: 'Growth support' }
    ],
    dailyAmount: 5,
    unit: 'kg'
  }
];

const FeedManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const theme = useTheme();
  const { isMobile, isTablet } = useResponsive();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Module Header */}
      <Card sx={{ ...getCardStyle('feed', theme), mb: 3 }}>
        <CardContent sx={{ py: 2 }}>
          <Box display="flex" alignItems="center">
            <Box sx={{ mr: 2, width: 60, height: 60, borderRadius: '50%', overflow: 'hidden' }}>
              <img
                src="/images/modules/feeding/feed-main.jpeg"
                alt="Feed Management"
                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              />
            </Box>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="white">
                Feed Management
              </Typography>
              <Typography variant="subtitle1" color="white" sx={{ opacity: 0.8 }}>
                Monitor and manage your livestock feed resources efficiently
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ p: { xs: 2, sm: 3, md: 4 }, mt: -2, width: '100%' }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Tabs */}
          <Card sx={{ ...getCardStyle('feed', theme), mb: 3 }}>
            <CardContent sx={{ py: 1 }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                aria-label="feed management tabs"
                sx={{
                  '& .MuiTab-root': {
                    fontSize: { xs: '0.875rem', md: '1rem' },
                    minWidth: { xs: 'auto', md: 150 },
                    px: { xs: 2, md: 3 },
                    py: 2,
                    color: 'white',
                  },
                  '& .Mui-selected': {
                    color: 'white',
                    fontWeight: 'bold',
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: 'white',
                  }
                }}
              >
                <Tab
                  icon={<Schedule fontSize="small" sx={{ color: 'white' }} />}
                  iconPosition="start"
                  label="Feeding Schedules"
                  sx={{ textTransform: 'none' }}
                />
                <Tab
                  icon={<Science fontSize="small" sx={{ color: 'white' }} />}
                  iconPosition="start"
                  label="Nutrition Plans"
                  sx={{ textTransform: 'none' }}
                />
              </Tabs>
            </CardContent>
          </Card>

          {/* Schedules Content */}
          {activeTab === 0 && (
            <motion.div variants={itemVariants} transition={{ delay: 0.2 }}>
              <Card sx={{ ...getCardStyle('feed', theme), mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                    Active Feeding Schedules
                  </Typography>
                  <Box sx={{ mt: 2, overflow: 'auto' }}>
                    <TableContainer component={Paper} sx={{
                      boxShadow: 'none',
                      borderRadius: 2,
                      overflow: 'auto',
                      maxWidth: '100%',
                      bgcolor: 'transparent'
                    }}>
                      <Table sx={{ minWidth: isMobile ? 650 : 800 }}>
                        <TableHead sx={{ ...getTableHeaderStyle('feed', theme) }}>
                          <TableRow>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Group</TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Feed Type</TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Time</TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Quantity</TableCell>
                            <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {mockFeedSchedules.map((schedule) => (
                            <TableRow key={schedule.id} hover sx={{ ...getTableRowStyle('feed', theme) }}>
                              <TableCell sx={{ color: 'white' }}>{schedule.groupName}</TableCell>
                              <TableCell sx={{ color: 'white' }}>{schedule.feedType}</TableCell>
                              <TableCell sx={{ color: 'white' }}>{schedule.time}</TableCell>
                              <TableCell sx={{ color: 'white' }}>
                                {schedule.quantity} {schedule.unit}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={schedule.status}
                                  size="small"
                                  sx={{
                                    ...getChipStyle('feed', theme, 'success'),
                                    fontSize: '0.75rem',
                                    fontWeight: 'medium'
                                  }}
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Nutrition Plans Content */}
          {activeTab === 1 && (
            <motion.div variants={itemVariants} transition={{ delay: 0.2 }}>
              <Grid container spacing={3}>
                {mockNutritionPlans.map((plan, index) => (
                  <Grid item xs={12} md={6} key={plan.id}>
                    <Card sx={{
                      ...getCardStyle('feed', theme),
                      height: '100%',
                      backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('/images/modules/feeding/feed-${index % 3 + 1}.jpeg')`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}>
                      <CardContent>
                        <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                          {plan.name}
                        </Typography>
                        <Typography variant="subtitle2" color="white" sx={{ opacity: 0.8 }} gutterBottom>
                          For: {plan.animalType}
                        </Typography>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" color="white" gutterBottom>
                            Components
                          </Typography>
                          <Box sx={{ mb: 2 }}>
                            {plan.components.map((component, idx) => (
                              <Box key={idx} sx={{ mb: 1 }}>
                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                                  <Typography variant="body2" color="white">{component.feedType}</Typography>
                                  <Typography variant="body2" fontWeight="medium" color="white">{component.percentage}%</Typography>
                                </Box>
                                <LinearProgress
                                  variant="determinate"
                                  value={component.percentage}
                                  sx={{
                                    height: 6,
                                    borderRadius: 1,
                                    bgcolor: 'rgba(255,255,255,0.2)',
                                    '& .MuiLinearProgress-bar': {
                                      bgcolor: 'white'
                                    }
                                  }}
                                />
                                <Typography variant="caption" color="white" sx={{ opacity: 0.8 }}>
                                  {component.purpose}
                                </Typography>
                              </Box>
                            ))}
                          </Box>
                          <Divider sx={{ my: 2, borderColor: 'rgba(255,255,255,0.1)' }} />
                          <Typography variant="body2" color="white">
                            Daily Amount: <strong>{plan.dailyAmount} {plan.unit}</strong>
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}
        </motion.div>
      </Box>
    </Box>
  );
};

export default FeedManagement;
