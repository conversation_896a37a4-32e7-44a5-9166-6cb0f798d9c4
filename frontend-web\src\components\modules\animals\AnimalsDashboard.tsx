import React from 'react';
import { CustomButton } from '../../components/common';
import { useNavigate, Link } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { Grid, Typography, Box, CircularProgress } from '@mui/material';
import {  Add  } from '../../../utils/iconImports';
import { motion } from 'framer-motion';
import { useAnimalData } from '../../../hooks/useAnimalData';
import DataCard from '../../common/DataCard';
import DataTable from '../../common/DataTable';
import StatusBadge from '../../common/StatusBadge';
import {  Pets, LocalHospital, Spa  } from '../../../utils/iconImports';

const AnimalsDashboard: React.FC = () => {
  const { animals, stats, loading } = useAnimalData();
  const navigate = useNavigate();

  const metrics = [
    { label: 'Total Animals', value: stats?.totalAnimals?.toString() || '0', icon: <Pets />, status: 'positive' },
    { label: 'Health Status', value: stats?.healthPercentage?.toString() + '%' || '0%', icon: <LocalHospital />, status: 'positive' },
    { label: 'Pending Checkups', value: stats?.pendingCheckups?.toString() || '0', icon: <Spa />, status: 'neutral' },
    { label: 'Recent Additions', value: stats?.recentAdditions?.toString() || '0', status: 'positive' },
  ];

  const columns = [
    { id: 'tagNumber', label: 'Tag Number' },
    { id: 'type', label: 'Type' },
    { id: 'breed', label: 'Breed' },
    { id: 'gender', label: 'Gender' },
    { 
      id: 'healthStatus', 
      label: 'Health Status',
      format: (value: string) => <StatusBadge status={value} type="health" />
    }
  ];

  const handleRowClick = (animal: any) => {
    navigate(`/animals/${animal.id}`);
  };

  if (loading) {
    return <CircularProgress />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Animals Dashboard</h1>
          <p className="text-gray-600">Manage your livestock and animal records</p>
        </div>
        <CustomButton 
          variant="contained" 
          startIcon={<Add />}
          component={Link}
          to={`${ROUTES.ANIMALS}/new`}
        >
          Add Animal
        </CustomButton>
      </div>

      {/* Metrics Grid */}
      <Grid container spacing={3}>
        {metrics.map((metric) => (
          <Grid item xs={12} sm={6} md={3} key={metric.label}>
            <DataCard
              title={metric.label}
              value={metric.value}
              icon={metric.icon}
              status={metric.status as any}
            />
          </Grid>
        ))}
      </Grid>

      {/* Recent Animals */}
      <Box className="bg-white rounded-xl shadow-md p-6 mt-6">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h6">Recent Animals</Typography>
          <CustomButton 
            variant="outlined" 
            component={Link} 
            to={`${ROUTES.ANIMALS}/list`}
          >
            View All
          </CustomButton>
        </div>
        <DataTable 
          columns={columns} 
          rows={animals.slice(0, 5)} 
          onRowClick={handleRowClick}
          pagination={false}
        />
      </Box>
    </div>
  );
};

export default AnimalsDashboard;

