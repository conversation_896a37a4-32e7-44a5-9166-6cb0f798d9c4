import React, { ReactNode } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, IconButton, Divider, useTheme, alpha, Tooltip, CardProps } from '@mui/material';
import { MoreVert, GetApp, FilterList, Search } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface DashboardCardProps extends Omit<CardProps, 'children'> {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  headerColor?: string;
  headerAction?: ReactNode;
  showExport?: boolean;
  showFilter?: boolean;
  showSearch?: boolean;
  showMoreOptions?: boolean;
  onExport?: () => void;
  onFilter?: () => void;
  onSearch?: () => void;
  onMoreOptions?: () => void;
  footerContent?: ReactNode;
  footerAction?: {
    label: string;
    onClick: () => void;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';
  };
  children: ReactNode;
  delay?: number;
  height?: number | string;
}

/**
 * Standardized dashboard card component for consistent styling
 */
const DashboardCard: React.FC<DashboardCardProps> = ({
  title,
  subtitle,
  icon,
  headerColor,
  headerAction,
  showExport = false,
  showFilter = false,
  showSearch = false,
  showMoreOptions = false,
  onExport,
  onFilter,
  onSearch,
  onMoreOptions,
  footerContent,
  footerAction,
  children,
  delay = 0,
  height,
  ...cardProps
}) => {
  const theme = useTheme();
  const { showThemeAnimations } = useThemeContext();

  // Determine header color
  const cardHeaderColor = headerColor || theme.palette.primary.main;

  return (
    <motion.div
      initial={showThemeAnimations ? { opacity: 0, y: 20 } : { opacity: 1, y: 0 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
      style={{ height: '100%' }}
    >
      <Card
        {...cardProps}
        sx={{
          borderRadius: '16px',
          boxShadow: '0 6px 24px rgba(0,0,0,0.08)',
          height: height || '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          backgroundColor: alpha(cardHeaderColor, 0.03),
          border: `1px solid ${alpha(cardHeaderColor, 0.1)}`,
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            transform: 'translateY(-2px)',
            borderColor: alpha(cardHeaderColor, 0.2)
          },
          ...cardProps.sx
        }}
      >
        {/* Card Header */}
        <Box
          sx={{
            p: 2.5,
            bgcolor: cardHeaderColor,
            color: 'white',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: alpha('#fff', 0.2)
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && <Box sx={{ mr: 1.5 }}>{icon}</Box>}
            <Box>
              <Typography variant="h6" fontWeight="bold">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {headerAction && headerAction}

            {showExport && (
              <Tooltip title="Export">
                <IconButton
                  color="inherit"
                  size="small"
                  onClick={onExport}
                  sx={{ ml: 0.5 }}
                >
                  <GetApp fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {showSearch && (
              <Tooltip title="Search">
                <IconButton
                  color="inherit"
                  size="small"
                  onClick={onSearch}
                  sx={{ ml: 0.5 }}
                >
                  <Search fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {showFilter && (
              <Tooltip title="Filter">
                <IconButton
                  color="inherit"
                  size="small"
                  onClick={onFilter}
                  sx={{ ml: 0.5 }}
                >
                  <FilterList fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {showMoreOptions && (
              <Tooltip title="More Options">
                <IconButton
                  color="inherit"
                  size="small"
                  onClick={onMoreOptions}
                  sx={{ ml: 0.5 }}
                >
                  <MoreVert fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Card Content */}
        <CardContent sx={{
          flex: 1,
          overflow: 'auto',
          p: 3,
          '&:last-child': {
            paddingBottom: 3
          }
        }}>
          {children}
        </CardContent>

        {/* Card Footer */}
        {(footerContent || footerAction) && (
          <>
            <Divider />
            <Box
              sx={{
                p: 2.5,
                display: 'flex',
                justifyContent: footerAction ? 'space-between' : 'flex-start',
                alignItems: 'center',
                backgroundColor: alpha(cardHeaderColor, 0.03),
                borderTop: `1px solid ${alpha(cardHeaderColor, 0.1)}`
              }}
            >
              {footerContent && <Box>{footerContent}</Box>}

              {footerAction && (
                <CustomButton
                  variant="outlined"
                  color={footerAction.color || 'primary'}
                  onClick={footerAction.onClick}
                >
                  {footerAction.label}
                </CustomButton>
              )}
            </Box>
          </>
        )}
      </Card>
    </motion.div>
  );
};

export default DashboardCard;
