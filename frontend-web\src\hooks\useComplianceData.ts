import { useState, useEffect, useCallback } from 'react';
import { useMockData } from '../utils/config';
import api from '../services/apiService';

// Define types for compliance data
export interface ComplianceDocument {
  id: string;
  title: string;
  type: string;
  status: 'valid' | 'expired' | 'pending';
  issueDate: string;
  expiryDate: string;
  issuedBy: string;
  documentUrl?: string;
  notes?: string;
}

export interface ComplianceInspection {
  id: string;
  type: string;
  date: string;
  inspector: string;
  status: 'passed' | 'failed' | 'pending';
  findings: string;
  actionItems?: string[];
  followUpDate?: string;
}

export interface ComplianceCertification {
  id: string;
  name: string;
  issuingBody: string;
  issueDate: string;
  expiryDate: string;
  status: 'valid' | 'expired' | 'pending';
  requirements: string[];
  documentUrl?: string;
}

export interface ComplianceStats {
  validDocuments: number;
  expiringDocuments: number;
  expiredDocuments: number;
  upcomingInspections: number;
  validCertifications: number;
  expiringCertifications: number;
}

// Create mock data
const mockDocuments: ComplianceDocument[] = Array.from({ length: 15 }, (_, i) => {
  const types = ['permit', 'license', 'certificate', 'registration', 'approval'];
  const statuses: ComplianceDocument['status'][] = ['valid', 'expired', 'pending'];
  const issuers = [
    'Department of Agriculture, Land Reform and Rural Development',
    'Department of Environmental Affairs',
    'South African Veterinary Council',
    'National Animal Health Forum',
    'Red Meat Industry Forum'
  ];
  
  const issueDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  const expiryDate = new Date(issueDate);
  expiryDate.setFullYear(expiryDate.getFullYear() + Math.floor(Math.random() * 3) + 1);
  
  const status = Math.random() > 0.7 ? 'valid' : (Math.random() > 0.5 ? 'expired' : 'pending');
  
  return {
    id: `DOC-${(i + 1).toString().padStart(3, '0')}`,
    title: `${types[i % types.length].charAt(0).toUpperCase() + types[i % types.length].slice(1)} Document ${i + 1}`,
    type: types[i % types.length],
    status: status as ComplianceDocument['status'],
    issueDate: issueDate.toISOString().split('T')[0],
    expiryDate: expiryDate.toISOString().split('T')[0],
    issuedBy: issuers[i % issuers.length],
    documentUrl: Math.random() > 0.3 ? `https://example.com/documents/doc-${i + 1}.pdf` : undefined,
    notes: Math.random() > 0.5 ? `Notes for document ${i + 1}` : undefined
  };
});

const mockInspections: ComplianceInspection[] = Array.from({ length: 10 }, (_, i) => {
  const types = ['health', 'safety', 'environmental', 'quality', 'welfare'];
  const statuses: ComplianceInspection['status'][] = ['passed', 'failed', 'pending'];
  const inspectors = [
    'Dr. van der Merwe',
    'Inspector Naidoo',
    'Dr. Molefe',
    'Inspector Botha',
    'Dr. Ndlovu'
  ];
  
  const date = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000);
  const followUpDate = new Date(date);
  followUpDate.setMonth(followUpDate.getMonth() + Math.floor(Math.random() * 3) + 1);
  
  const status = Math.random() > 0.6 ? 'passed' : (Math.random() > 0.5 ? 'failed' : 'pending');
  
  return {
    id: `INSP-${(i + 1).toString().padStart(3, '0')}`,
    type: types[i % types.length],
    date: date.toISOString().split('T')[0],
    inspector: inspectors[i % inspectors.length],
    status: status as ComplianceInspection['status'],
    findings: `Findings for inspection ${i + 1}`,
    actionItems: status === 'failed' ? [
      `Action item 1 for inspection ${i + 1}`,
      `Action item 2 for inspection ${i + 1}`
    ] : undefined,
    followUpDate: status === 'failed' ? followUpDate.toISOString().split('T')[0] : undefined
  };
});

const mockCertifications: ComplianceCertification[] = Array.from({ length: 8 }, (_, i) => {
  const names = [
    'Organic Certification',
    'Animal Welfare Approved',
    'Good Agricultural Practices',
    'ISO 9001',
    'HACCP Certification',
    'Halal Certification',
    'Kosher Certification',
    'Fair Trade Certification'
  ];
  
  const issuingBodies = [
    'South African Organic Sector Organisation',
    'Animal Welfare Association',
    'Global G.A.P.',
    'South African Bureau of Standards',
    'National Regulator for Compulsory Specifications',
    'South African National Halal Authority',
    'Beth Din of South Africa',
    'Fair Trade South Africa'
  ];
  
  const statuses: ComplianceCertification['status'][] = ['valid', 'expired', 'pending'];
  
  const issueDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  const expiryDate = new Date(issueDate);
  expiryDate.setFullYear(expiryDate.getFullYear() + Math.floor(Math.random() * 3) + 1);
  
  const status = Math.random() > 0.7 ? 'valid' : (Math.random() > 0.5 ? 'expired' : 'pending');
  
  return {
    id: `CERT-${(i + 1).toString().padStart(3, '0')}`,
    name: names[i % names.length],
    issuingBody: issuingBodies[i % issuingBodies.length],
    issueDate: issueDate.toISOString().split('T')[0],
    expiryDate: expiryDate.toISOString().split('T')[0],
    status: status as ComplianceCertification['status'],
    requirements: [
      `Requirement 1 for ${names[i % names.length]}`,
      `Requirement 2 for ${names[i % names.length]}`,
      `Requirement 3 for ${names[i % names.length]}`
    ],
    documentUrl: Math.random() > 0.3 ? `https://example.com/certifications/cert-${i + 1}.pdf` : undefined
  };
});

const mockStats: ComplianceStats = {
  validDocuments: mockDocuments.filter(d => d.status === 'valid').length,
  expiringDocuments: 3,
  expiredDocuments: mockDocuments.filter(d => d.status === 'expired').length,
  upcomingInspections: 2,
  validCertifications: mockCertifications.filter(c => c.status === 'valid').length,
  expiringCertifications: 1
};

// Create the hook
export const useComplianceData = () => {
  const [documents, setDocuments] = useState<ComplianceDocument[]>([]);
  const [inspections, setInspections] = useState<ComplianceInspection[]>([]);
  const [certifications, setCertifications] = useState<ComplianceCertification[]>([]);
  const [stats, setStats] = useState<ComplianceStats>({
    validDocuments: 0,
    expiringDocuments: 0,
    expiredDocuments: 0,
    upcomingInspections: 0,
    validCertifications: 0,
    expiringCertifications: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchComplianceData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setDocuments(mockDocuments);
        setInspections(mockInspections);
        setCertifications(mockCertifications);
        setStats(mockStats);
      } else {
        // Use real API
        const [documentsRes, inspectionsRes, certificationsRes, statsRes] = await Promise.all([
          api.get('/compliance/documents'),
          api.get('/compliance/inspections'),
          api.get('/compliance/certifications'),
          api.get('/compliance/stats')
        ]);

        setDocuments(documentsRes.data);
        setInspections(inspectionsRes.data);
        setCertifications(certificationsRes.data);
        setStats(statsRes.data);
      }
    } catch (err) {
      console.error('Error fetching compliance data:', err);
      setError('Failed to fetch compliance data');
    } finally {
      setLoading(false);
    }
  }, []);

  // CRUD operations for documents
  const addDocument = async (document: Omit<ComplianceDocument, 'id'>) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const newDocument: ComplianceDocument = {
          ...document,
          id: `DOC-${(documents.length + 1).toString().padStart(3, '0')}`
        };
        
        setDocuments(prev => [...prev, newDocument]);
        updateStats();
        
        return newDocument;
      } else {
        const response = await api.post('/compliance/documents', document);
        await fetchComplianceData();
        return response.data;
      }
    } catch (err) {
      console.error('Error adding document:', err);
      setError('Failed to add document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateDocument = async (id: string, document: Partial<ComplianceDocument>) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setDocuments(prev => prev.map(d => d.id === id ? { ...d, ...document } : d));
        updateStats();
        
        return documents.find(d => d.id === id);
      } else {
        const response = await api.put(`/compliance/documents/${id}`, document);
        await fetchComplianceData();
        return response.data;
      }
    } catch (err) {
      console.error('Error updating document:', err);
      setError('Failed to update document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteDocument = async (id: string) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setDocuments(prev => prev.filter(d => d.id !== id));
        updateStats();
      } else {
        await api.delete(`/compliance/documents/${id}`);
        await fetchComplianceData();
      }
    } catch (err) {
      console.error('Error deleting document:', err);
      setError('Failed to delete document');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Similar CRUD operations for inspections and certifications...

  // Update stats
  const updateStats = () => {
    setStats({
      validDocuments: documents.filter(d => d.status === 'valid').length,
      expiringDocuments: documents.filter(d => {
        const expiryDate = new Date(d.expiryDate);
        const now = new Date();
        const diffTime = expiryDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return d.status === 'valid' && diffDays <= 30;
      }).length,
      expiredDocuments: documents.filter(d => d.status === 'expired').length,
      upcomingInspections: inspections.filter(i => {
        const inspectionDate = new Date(i.date);
        const now = new Date();
        const diffTime = inspectionDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 && diffDays <= 30;
      }).length,
      validCertifications: certifications.filter(c => c.status === 'valid').length,
      expiringCertifications: certifications.filter(c => {
        const expiryDate = new Date(c.expiryDate);
        const now = new Date();
        const diffTime = expiryDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return c.status === 'valid' && diffDays <= 30;
      }).length
    });
  };

  // Initial data fetch
  useEffect(() => {
    fetchComplianceData();
  }, [fetchComplianceData]);

  return {
    documents,
    inspections,
    certifications,
    stats,
    loading,
    error,
    fetchComplianceData,
    addDocument,
    updateDocument,
    deleteDocument
    // Add other CRUD operations as needed
  };
};
