#!/usr/bin/env python
"""
Voice <PERSON><PERSON> for AMPD Livestock
Plays a text-to-speech alert for birth predictions
"""

import argparse
import os
import sys
import time
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('voice_alert')

def play_alert(message, output_id, repeat=3, volume=1.0):
    """
    Play a voice alert using text-to-speech
    
    Args:
        message (str): Message to speak
        output_id (str): Unique identifier for this alert
        repeat (int): Number of times to repeat the message
        volume (float): Volume level (0.0 to 1.0)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Try to import pyttsx3
        import pyttsx3
        
        logger.info(f"Initializing text-to-speech engine")
        engine = pyttsx3.init()
        
        # Set properties
        engine.setProperty('rate', 150)  # Speed of speech
        engine.setProperty('volume', volume)  # Volume (0.0 to 1.0)
        
        # Get available voices
        voices = engine.getProperty('voices')
        
        # Try to find a female voice
        female_voice = None
        for voice in voices:
            if 'female' in voice.name.lower():
                female_voice = voice.id
                break
        
        # Set voice
        if female_voice:
            logger.info(f"Using female voice: {female_voice}")
            engine.setProperty('voice', female_voice)
        else:
            logger.info(f"Using default voice: {voices[0].name}")
            engine.setProperty('voice', voices[0].id)
        
        # Play the alert multiple times
        for i in range(repeat):
            if i > 0:
                # Add a pause between repetitions
                time.sleep(2)
            
            logger.info(f"Playing alert (repetition {i+1}/{repeat})")
            engine.say(message)
            engine.runAndWait()
        
        logger.info(f"Alert played successfully")
        return True
    
    except ImportError:
        logger.error("pyttsx3 not installed. Please install it with: pip install pyttsx3")
        return False
    
    except Exception as e:
        logger.error(f"Error playing alert: {str(e)}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Play a voice alert for AMPD Livestock')
    parser.add_argument('--message', required=True, help='Message to speak')
    parser.add_argument('--output', required=True, help='Unique identifier for this alert')
    parser.add_argument('--repeat', type=int, default=3, help='Number of times to repeat the message')
    parser.add_argument('--volume', type=float, default=1.0, help='Volume level (0.0 to 1.0)')
    
    args = parser.parse_args()
    
    # Play the alert
    success = play_alert(args.message, args.output, args.repeat, args.volume)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
