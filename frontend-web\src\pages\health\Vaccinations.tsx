import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar, useTheme, alpha } from '@mui/material';
import { getCardStyle, getTableHeaderStyle, getTableRowStyle, getChipStyle } from '../../utils/cardStyles';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  LocalHospital,
  Notifications,
  Event
 } from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { mockAnimals } from '../../mocks/animalData';

interface VaccinationRecord {
  id: string;
  animalId: string;
  animalName: string;
  breed: string;
  vaccineType: string;
  date: string;
  nextDueDate: string;
  status: 'completed' | 'scheduled' | 'overdue';
  administeredBy: string;
  batchNumber: string;
  notes: string;
}

const Vaccinations: React.FC = () => {
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<VaccinationRecord | null>(null);
  const [vaccinationRecords, setVaccinationRecords] = useState<VaccinationRecord[]>([
    {
      id: 'VAC001',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      breed: 'Nguni',
      vaccineType: 'Anthrax',
      date: '2024-02-15',
      nextDueDate: '2025-02-15',
      status: 'completed',
      administeredBy: 'Dr. van Niekerk',
      batchNumber: 'ANT-2024-0215',
      notes: 'Annual vaccination completed'
    },
    {
      id: 'VAC002',
      animalId: 'ANM002',
      animalName: 'Kgosi',
      breed: 'Bonsmara',
      vaccineType: 'Botulism',
      date: '2024-02-20',
      nextDueDate: '2025-02-20',
      status: 'completed',
      administeredBy: 'Dr. van Niekerk',
      batchNumber: 'BOT-2024-0220',
      notes: 'Annual vaccination completed'
    },
    {
      id: 'VAC003',
      animalId: 'ANM003',
      animalName: 'Lesedi',
      breed: 'Afrikaner',
      vaccineType: 'Brucellosis',
      date: '2024-01-10',
      nextDueDate: '2025-01-10',
      status: 'completed',
      administeredBy: 'Dr. Botha',
      batchNumber: 'BRU-2024-0110',
      notes: 'Required for breeding program'
    },
    {
      id: 'VAC004',
      animalId: 'ANM004',
      animalName: 'Pule',
      breed: 'Dorper',
      vaccineType: 'Blue tongue',
      date: '2024-03-25',
      nextDueDate: '2025-03-25',
      status: 'scheduled',
      administeredBy: '',
      batchNumber: '',
      notes: 'Schedule for spring vaccination program'
    },
    {
      id: 'VAC005',
      animalId: 'ANM005',
      animalName: 'Tumelo',
      breed: 'Boer Goat',
      vaccineType: 'Rift Valley fever',
      date: '2024-01-05',
      nextDueDate: '2024-03-05',
      status: 'overdue',
      administeredBy: 'Dr. Botha',
      batchNumber: 'RVF-2024-0105',
      notes: 'Booster shot overdue'
    }
  ]);

  const handleAddNew = () => {
    setSelectedRecord(null);
    setOpenDialog(true);
  };

  const handleEdit = (record: VaccinationRecord) => {
    setSelectedRecord(record);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setVaccinationRecords(vaccinationRecords.filter(record => record.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'completed':
        return <Chip label="Completed" color="success" size="small" />;
      case 'scheduled':
        return <Chip label="Scheduled" color="primary" size="small" />;
      case 'overdue':
        return <Chip label="Overdue" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (dateString: string) => {
    const today = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getVaccinesByType = () => {
    const vaccineTypes: Record<string, number> = {};

    vaccinationRecords.forEach(record => {
      if (vaccineTypes[record.vaccineType]) {
        vaccineTypes[record.vaccineType]++;
      } else {
        vaccineTypes[record.vaccineType] = 1;
      }
    });

    return Object.entries(vaccineTypes).map(([type, count]) => ({ type, count }));
  };

  return (
    <Box>
      <ModuleHeader
        title="Vaccination Schedule"
        subtitle="Manage and track all vaccination records and schedules for your livestock"
        module="health"
        submodule="vaccinations"
        actionLabel="Add Vaccination"
        actionIcon={<Add />}
        onAction={handleAddNew}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Vaccination Status"
              module="health"
              uniqueId="vaccination-status"
              icon={<LocalHospital />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >

              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(255, 255, 255, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="white">
                        {vaccinationRecords.filter(record => record.status === 'completed').length}
                      </Typography>
                      <Typography variant="body2" color="white">Completed</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(255, 255, 255, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="white">
                        {vaccinationRecords.filter(record => record.status === 'scheduled').length}
                      </Typography>
                      <Typography variant="body2" color="white">Scheduled</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(255, 255, 255, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="white">
                        {vaccinationRecords.filter(record => record.status === 'overdue').length}
                      </Typography>
                      <Typography variant="body2" color="white">Overdue</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" color="white" gutterBottom>
                    Upcoming Vaccinations
                  </Typography>
                  {vaccinationRecords
                    .filter(record => record.status === 'scheduled')
                    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                    .slice(0, 3)
                    .map(record => (
                      <Box key={record.id} mb={2} p={1.5} bgcolor="rgba(255,255,255,0.1)" borderRadius={1}>
                        <Typography variant="subtitle2" fontWeight="bold" color="white">
                          {record.animalName} - {record.vaccineType}
                        </Typography>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                          <Typography variant="body2" color="white">
                            <Event fontSize="small" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle', color: 'white' }} />
                            {formatDate(record.date)}
                          </Typography>
                          <Chip
                            label={`${calculateDaysRemaining(record.date)} days`}
                            size="small"
                            sx={{
                              ...getChipStyle('health', theme, calculateDaysRemaining(record.date) < 7 ? 'warning' : 'primary')
                            }}
                          />
                        </Box>
                      </Box>
                    ))}
                  {vaccinationRecords.filter(record => record.status === 'scheduled').length === 0 && (
                    <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                      <Typography variant="body2" color="white">No upcoming vaccinations</Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Vaccine Types"
              module="health"
              uniqueId="vaccination-types"
              icon={<Info />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >

              <Box mt={2}>
                {getVaccinesByType().map(({ type, count }) => (
                  <Box key={type} mb={2} p={1.5} bgcolor="rgba(255,255,255,0.1)" borderRadius={1}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2" fontWeight="bold" color="white">
                        {type}
                      </Typography>
                      <Chip
                        label={count}
                        size="small"
                        sx={{
                          ...getChipStyle('health', theme, 'primary')
                        }}
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Overdue Vaccinations"
              module="health"
              uniqueId="vaccination-overdue"
              icon={<Warning />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >

              <Box mt={2}>
                {vaccinationRecords
                  .filter(record => record.status === 'overdue')
                  .map(record => (
                    <Box key={record.id} mb={2} p={1.5} bgcolor="rgba(255,255,255,0.1)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold" color="white">
                        {record.animalName} - {record.vaccineType}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="white">
                          Due: {formatDate(record.nextDueDate)}
                        </Typography>
                        <CustomButton
                          size="small"
                          variant="outlined"
                          sx={{ color: 'white', borderColor: 'white' }}
                          onClick={() => handleEdit(record)}
                        >
                          Reschedule
                        </CustomButton>
                      </Box>
                    </Box>
                  ))}
                {vaccinationRecords.filter(record => record.status === 'overdue').length === 0 && (
                  <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                    <Typography variant="body2" color="white">No overdue vaccinations</Typography>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Card
          sx={{
            ...getCardStyle('health', theme),
            mb: 4
          }}
        >
          <CardContent sx={{ p: 0 }}>
          <TableContainer component={Paper} sx={{ boxShadow: 'none', bgcolor: 'transparent' }}>
            <Table>
              <TableHead sx={{ ...getTableHeaderStyle('health', theme) }}>
                <TableRow>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Animal</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Breed</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Vaccine Type</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Date</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Next Due Date</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Administered By</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {vaccinationRecords.map((record) => (
                  <TableRow key={record.id} hover sx={{ ...getTableRowStyle('health', theme) }}>
                    <TableCell sx={{ color: 'white' }}>{record.animalName}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{record.breed}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{record.vaccineType}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{formatDate(record.date)}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{formatDate(record.nextDueDate)}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        sx={{
                          ...getChipStyle('health', theme, record.status === 'completed' ? 'success' :
                                                          record.status === 'scheduled' ? 'primary' : 'error')
                        }}
                        size="small"
                      />
                    </TableCell>
                    <TableCell sx={{ color: 'white' }}>{record.administeredBy || '-'}</TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" sx={{ color: 'white' }} onClick={() => handleEdit(record)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" sx={{ color: 'white' }} onClick={() => handleDelete(record.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
      </Box>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedRecord ? 'Edit Vaccination Record' : 'Add New Vaccination Record'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="animal-select-label">Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  label="Animal"
                  defaultValue={selectedRecord?.animalId || ''}
                >
                  {mockAnimals.map(animal => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} ({animal.breed})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="vaccine-select-label">Vaccine Type</InputLabel>
                <Select
                  labelId="vaccine-select-label"
                  id="vaccine-select"
                  label="Vaccine Type"
                  defaultValue={selectedRecord?.vaccineType || ''}
                >
                  <MenuItem value="Anthrax">Anthrax</MenuItem>
                  <MenuItem value="Botulism">Botulism</MenuItem>
                  <MenuItem value="Brucellosis">Brucellosis</MenuItem>
                  <MenuItem value="Lumpy skin disease">Lumpy skin disease</MenuItem>
                  <MenuItem value="Rift Valley fever">Rift Valley fever</MenuItem>
                  <MenuItem value="Blue tongue">Blue tongue</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="date"
                label="Vaccination Date"
                type="date"
                defaultValue={selectedRecord?.date || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="nextDueDate"
                label="Next Due Date"
                type="date"
                defaultValue={selectedRecord?.nextDueDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedRecord?.status || 'scheduled'}
                >
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="overdue">Overdue</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="administeredBy"
                label="Administered By"
                defaultValue={selectedRecord?.administeredBy || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="batchNumber"
                label="Batch Number"
                defaultValue={selectedRecord?.batchNumber || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedRecord?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedRecord ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Vaccinations;
