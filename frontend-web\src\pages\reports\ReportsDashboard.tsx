import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, Box, Chip, IconButton, Avatar, useTheme, List, ListItem, ListItemText, ListItemAvatar, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, alpha, Snackbar, Alert,  } from '@mui/material';
import useReportSelection from '../../hooks/useReportSelection';
import {
  Add,
  Assessment,
  TrendingUp,
  BarChart,
  PieChart,
  Search,
  FilterList,
  MoreVert,
  ArrowForward,
  MonetizationOn,
  LocalHospital,
  Download,
  Print,
  Share
} from '../../utils/iconImports';
import {  StandardDashboard, ModernChart, AnimatedBackgroundCard, SimpleButton , CustomButton } from '../../components/common';
import ReportCard from '../../components/modules/reports/ReportCard';
import ReportButton from '../../components/modules/reports/ReportButton';
import ReportsModuleWrapper from '../../components/modules/reports/ReportsModuleWrapper';
import { applyReportSelectionFixes } from '../../utils/applyReportSelectionFixes';
import { withSubModuleTranslation } from '../../components/common';

// Mock reports data
const mockReports = [
  {
    id: 'REP001',
    title: 'Monthly Livestock Status Report',
    type: 'livestock',
    date: '2024-02-15',
    status: 'generated',
    size: '2.5 MB',
    viewCount: 42,
    description: 'Comprehensive overview of all livestock including counts, health status, and weight trends',
    author: 'System',
    lastModified: '2024-02-15',
    format: 'PDF',
    tags: ['livestock', 'monthly', 'status']
  },
  {
    id: 'REP002',
    title: 'Q1 Financial Summary',
    type: 'financial',
    date: '2024-01-31',
    status: 'generated',
    size: '1.8 MB',
    viewCount: 35,
    description: 'Detailed financial analysis for Q1 including revenue, expenses, and profit margins by category',
    author: 'System',
    lastModified: '2024-01-31',
    format: 'Excel',
    tags: ['financial', 'quarterly', 'summary']
  },
  {
    id: 'REP003',
    title: 'Breeding Performance Analysis',
    type: 'breeding',
    date: '2024-02-10',
    status: 'generated',
    size: '3.2 MB',
    viewCount: 28,
    description: 'Analysis of breeding program performance including success rates, genetic improvements, and offspring quality',
    author: 'System',
    lastModified: '2024-02-10',
    format: 'PDF',
    tags: ['breeding', 'performance', 'genetics']
  },
  {
    id: 'REP004',
    title: 'Feed Consumption Trends',
    type: 'feeding',
    date: '2024-02-05',
    status: 'generated',
    size: '2.1 MB',
    viewCount: 19,
    description: 'Analysis of feed consumption patterns, efficiency metrics, and cost analysis by feed type',
    author: 'System',
    lastModified: '2024-02-05',
    format: 'PDF',
    tags: ['feeding', 'consumption', 'trends']
  },
  {
    id: 'REP005',
    title: 'Health Inspection Summary',
    type: 'health',
    date: '2024-02-20',
    status: 'generated',
    size: '1.5 MB',
    viewCount: 31,
    description: 'Summary of health inspections, vaccination status, treatment outcomes, and disease prevention measures',
    author: 'System',
    lastModified: '2024-02-20',
    format: 'PDF',
    tags: ['health', 'inspection', 'vaccination']
  },
  {
    id: 'REP006',
    title: 'Commercial Sales Analysis',
    type: 'commercial',
    date: '2024-02-25',
    status: 'generated',
    size: '2.8 MB',
    viewCount: 27,
    description: 'Detailed analysis of commercial sales performance, customer demographics, and market trends',
    author: 'System',
    lastModified: '2024-02-25',
    format: 'Excel',
    tags: ['commercial', 'sales', 'market']
  },
  {
    id: 'REP007',
    title: 'Compliance Documentation',
    type: 'compliance',
    date: '2024-02-18',
    status: 'generated',
    size: '4.2 MB',
    viewCount: 15,
    description: 'Comprehensive documentation of regulatory compliance, certifications, and inspection results',
    author: 'System',
    lastModified: '2024-02-18',
    format: 'PDF',
    tags: ['compliance', 'regulatory', 'certification']
  }
];

// Mock report categories data
const reportCategoriesData = [
  { name: 'Livestock', value: 35 },
  { name: 'Financial', value: 25 },
  { name: 'Breeding', value: 15 },
  { name: 'Health', value: 15 },
  { name: 'Feeding', value: 10 },
  { name: 'Commercial', value: 12 },
  { name: 'Compliance', value: 8 }
];

// Mock report usage data
const reportUsageData = [
  { month: 'Jan', generated: 12, viewed: 45, downloaded: 8 },
  { month: 'Feb', generated: 15, viewed: 52, downloaded: 10 },
  { month: 'Mar', generated: 18, viewed: 60, downloaded: 12 },
  { month: 'Apr', generated: 14, viewed: 48, downloaded: 9 },
  { month: 'May', generated: 21, viewed: 65, downloaded: 15 },
  { month: 'Jun', generated: 25, viewed: 72, downloaded: 18 }
];

// Mock report stats
const reportStats = {
  totalReports: 45,
  generatedThisMonth: 15,
  mostViewed: 'Monthly Livestock Status Report',
  mostDownloaded: 'Q1 Financial Summary',
  averageSize: '2.2 MB',
  reportsByType: {
    livestock: 15,
    financial: 10,
    breeding: 8,
    health: 7,
    feeding: 5
  }
};

interface ReportsDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const ReportsDashboard: React.FC<ReportsDashboardProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // State for chart interactions
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });

  // State for report selection
  const {
    selectedReports,
    toggleReportSelection,
    selectSingleReport,
    isReportSelected,
    clearSelectedReports
  } = useReportSelection(mockReports);

  // Apply report selection fixes when component mounts
  useEffect(() => {
    // Apply fixes immediately
    applyReportSelectionFixes();

    // Apply fixes again after a delay to ensure all components are rendered
    const timer1 = setTimeout(() => {
      applyReportSelectionFixes();
    }, 500);

    // Apply fixes one more time after a longer delay
    const timer2 = setTimeout(() => {
      applyReportSelectionFixes();
    }, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  // State for snackbar
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Handle report selection
  const handleReportSelect = (report: any) => {
    toggleReportSelection(report);
    setSnackbar({
      open: true,
      message: `Report "${report.title}" ${isReportSelected(report.id) ? 'deselected' : 'selected'}`,
      severity: 'info'
    });
  };

  // Handle report view
  const handleReportView = (report: any) => {
    navigate(`/reports/view/${report.id}`);
  };

  // Close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translateModuleField ? translateModuleField('total_reports', "Total Reports") : "Total Reports",
      value: reportStats.totalReports.toString(),
      icon: <Assessment />,
      color: theme.palette.primary.main,
      trend: {
        value: 8,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('generated_this_month', "Generated This Month") : "Generated This Month",
      value: reportStats.generatedThisMonth.toString(),
      icon: <BarChart />,
      color: theme.palette.success.main,
      trend: {
        value: 12,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('financial_reports', "Financial Reports") : "Financial Reports",
      value: reportStats.reportsByType.financial.toString(),
      icon: <MonetizationOn />,
      color: theme.palette.info.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('health_reports', "Health Reports") : "Health Reports",
      value: reportStats.reportsByType.health.toString(),
      icon: <LocalHospital />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translateModuleField ? translateModuleField('generate_report', "Generate Report") : "Generate Report",
      icon: <Add />,
      onClick: () => navigate('/reports/generate'),
      color: 'primary'
    }
  ];

  return (
    <ReportsModuleWrapper>
      <StandardDashboard
        title={translateSubModule ? translateSubModule('title', "Reports & Analytics") : "Reports & Analytics"}
        subtitle={translateSubModule ? translateSubModule('subtitle', "Generate insights, track performance metrics, and make data-driven decisions for your MayCaiphus livestock operation") : "Generate insights, track performance metrics, and make data-driven decisions for your MayCaiphus livestock operation"}
        icon={<Assessment />}
        stats={dashboardStats}
        actions={dashboardActions}
        isLoading={false}
        loadingMessage={translateModuleField ? translateModuleField('loading', "Loading MayCaiphus reports data...") : "Loading MayCaiphus reports data..."}
        onRefresh={() => console.log('Refreshing reports data')}
        module="reports"
      >
      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
            <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
              {translateModuleField ? translateModuleField('reports_analytics', "Reports & Analytics") : "Reports & Analytics"}
            </Typography>
            <Box display="flex" gap={2}>
              <CustomButton
                variant="contained"
                color="primary"
                startIcon={<Add />}
                onClick={() => navigate('/reports/generate')}
                sx={{ borderRadius: '8px', boxShadow: '0 4px 10px rgba(0,0,0,0.1)' }}
              >
                {translateModuleField ? translateModuleField('generate_report', "Generate Report") : "Generate Report"}
              </CustomButton>
              <CustomButton
                variant="outlined"
                color="primary"
                startIcon={<Download />}
                sx={{ borderRadius: '8px' }}
              >
                {translateModuleField ? translateModuleField('download_all', "Download All") : "Download All"}
              </CustomButton>
            </Box>
          </Box>
        </motion.div>

        {/* Charts and Lists Section */}
        <Grid container spacing={3} mb={4} sx={{ '& .recharts-responsive-container': { minHeight: { xs: 200, sm: 250, md: 300 } } }}>
          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
            >
              <ModernChart
                title={translateModuleField ? translateModuleField('reports_by_category', "Reports by Category") : "Reports by Category"}
                subtitle={translateModuleField ? translateModuleField('reports_by_category_desc', "Distribution of reports by category") : "Distribution of reports by category"}
                data={reportCategoriesData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="reports"
                tooltip={translateModuleField ? translateModuleField('reports_by_category_help', "Shows the distribution of reports across different categories") : "Shows the distribution of reports across different categories"}
                formatValue={(value) => `${value} reports`}
                onClick={(data) => {
                  setSelectedCategory(data.name);
                  setSelectedChart('categories');
                  setChartTooltip({
                    show: true,
                    content: `${data.name}: ${data.value} reports (${(data.percent ? (data.percent * 100).toFixed(1) : 0)}%)`
                  });
                }}
              />
            </motion.div>
          </Grid>

          <Grid item xs={12} md={6}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 }}
            >
              <AnimatedBackgroundCard
                title="Most Viewed Reports"
                module="reports"
                backgroundImage="/images/modules/reports/reports-bg.jpeg"
                icon={<Assessment />}
                accentColor={theme.palette.info.main}
                secondaryColor={theme.palette.info.dark}
                delay={0.2}
                height="100%"
                overlay="gradient"
              >
                <List sx={{ '& .MuiListItem-root': { py: { xs: 2, md: 2.5 } } }}>
                  {mockReports.slice(0, 3).map((report) => (
                    <ListItem key={report.id} divider sx={{ borderColor: 'rgba(255,255,255,0.1)' }}>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                          {report.type === 'livestock' ? <Assessment sx={{ color: 'white' }} /> :
                           report.type === 'financial' ? <MonetizationOn sx={{ color: 'white' }} /> :
                           report.type === 'health' ? <LocalHospital sx={{ color: 'white' }} /> :
                           <BarChart sx={{ color: 'white' }} />}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={report.title}
                        secondary={`Generated: ${new Date(report.date).toLocaleDateString()} • Views: ${report.viewCount}`}
                        primaryTypographyProps={{
                          sx: { fontSize: { xs: '1rem', sm: '1.1rem', md: '1.15rem' }, fontWeight: 500, color: 'white' }
                        }}
                        secondaryTypographyProps={{
                          sx: { fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' }, color: 'rgba(255,255,255,0.7)' }
                        }}
                      />
                      <Chip
                        label={report.type.toUpperCase()}
                        size="small"
                        sx={{
                          mr: 1,
                          fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                          bgcolor: 'rgba(255,255,255,0.15)',
                          color: 'white'
                        }}
                      />
                      <IconButton size="small" onClick={() => navigate(`/reports/view/${report.id}`)} sx={{ color: 'white' }}>
                        <ArrowForward fontSize="small" />
                      </IconButton>
                    </ListItem>
                  ))}
                </List>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>
        </Grid>

        {/* Report Usage Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <ModernChart
            title={translateModuleField ? translateModuleField('report_usage_trends', "Report Usage Trends") : "Report Usage Trends"}
            subtitle={translateModuleField ? translateModuleField('report_usage_trends_desc', "Track report generation, views, and downloads over time") : "Track report generation, views, and downloads over time"}
            data={reportUsageData}
            type="line"
            dataKeys={['generated', 'viewed', 'downloaded']}
            xAxisDataKey="month"
            height={400}
            accentColor={theme.palette.primary.main}
            allowChartTypeChange={true}
            allowTimeRangeChange={true}
            module="reports"
            tooltip={translateModuleField ? translateModuleField('report_usage_trends_help', "Shows report usage trends by month") : "Shows report usage trends by month"}
            formatValue={(value) => `${value}`}
            onRefresh={() => {
              // Refresh data logic would go here
              console.log('Refreshing report usage data');
            }}
            onClick={(data) => {
              if (data && data.activePayload && data.activePayload.length > 0) {
                const payload = data.activePayload[0].payload;
                setSelectedChart('usage');
                setChartTooltip({
                  show: true,
                  content: `${payload.month}: Generated: ${payload.generated}, Viewed: ${payload.viewed}, Downloaded: ${payload.downloaded}`
                });
              }
            }}
          />
        </motion.div>

        {/* Recent Reports */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden' }}>
            <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" fontWeight="bold">
                Recent Reports
              </Typography>
              <Box>
                <IconButton color="inherit" size="small">
                  <Search />
                </IconButton>
                <IconButton color="inherit" size="small">
                  <FilterList />
                </IconButton>
                <IconButton color="inherit" size="small">
                  <MoreVert />
                </IconButton>
              </Box>
            </Box>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Report ID</TableCell>
                    <TableCell>Title</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Format</TableCell>
                    <TableCell>Size</TableCell>
                    <TableCell>Views</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockReports.map((report) => (
                    <TableRow
                      key={report.id}
                      hover
                      onClick={() => handleReportSelect(report)}
                      selected={isReportSelected(report.id)}
                      sx={{
                        cursor: 'pointer',
                        '&.Mui-selected': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        },
                        '&.Mui-selected:hover': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.15),
                        },
                        position: 'relative',
                        zIndex: 5,
                        pointerEvents: 'auto'
                      }}
                    >
                      <TableCell>{report.id}</TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">{report.title}</Typography>
                          <Typography variant="caption" color="textSecondary" sx={{ display: 'block', mt: 0.5 }}>
                            {report.description.length > 60 ? report.description.substring(0, 60) + '...' : report.description}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={report.type.charAt(0).toUpperCase() + report.type.slice(1)}
                          color={
                            report.type === 'livestock' ? 'primary' :
                            report.type === 'financial' ? 'success' :
                            report.type === 'health' ? 'error' :
                            report.type === 'breeding' ? 'warning' :
                            report.type === 'commercial' ? 'info' :
                            'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{new Date(report.date).toLocaleDateString()}</TableCell>
                      <TableCell>{report.format}</TableCell>
                      <TableCell>{report.size}</TableCell>
                      <TableCell>{report.viewCount}</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the row's onClick
                            navigate(`/reports/view/${report.id}`);
                          }}
                          sx={{ position: 'relative', zIndex: 10, pointerEvents: 'auto' }}
                        >
                          <Search fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={(e) => e.stopPropagation()} // Prevent triggering the row's onClick
                          sx={{ position: 'relative', zIndex: 10, pointerEvents: 'auto' }}
                        >
                          <Download fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={(e) => e.stopPropagation()} // Prevent triggering the row's onClick
                          sx={{ position: 'relative', zIndex: 10, pointerEvents: 'auto' }}
                        >
                          <Print fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={(e) => e.stopPropagation()} // Prevent triggering the row's onClick
                          sx={{ position: 'relative', zIndex: 10, pointerEvents: 'auto' }}
                        >
                          <Share fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <ReportButton
                variant="outlined"
                color="primary"
                onClick={() => navigate('/reports/all')}
              >
                View All Reports
              </ReportButton>
            </Box>
          </Card>
        </motion.div>

        {/* Featured Reports Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
        >
          <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" fontWeight="bold">
                Featured Reports
              </Typography>
              <ReportButton
                variant="outlined"
                color="primary"
                endIcon={<ArrowForward />}
                onClick={() => navigate('/reports/all')}
              >
                View All
              </ReportButton>
            </Box>

            <Grid container spacing={3}>
              {mockReports.slice(0, 3).map((report) => (
                <Grid item xs={12} md={4} key={report.id}>
                  <ReportCard
                    report={{
                      id: report.id,
                      title: report.title,
                      type: report.type,
                      date: report.date,
                      summary: report.description,
                      status: report.id === 'REP001' ? 'new' : 'viewed',
                      metrics: [
                        {
                          label: 'Size',
                          value: report.size
                        },
                        {
                          label: 'Views',
                          value: report.viewCount.toString(),
                          trend: 'up',
                          change: '+12%'
                        }
                      ]
                    }}
                    onSelect={handleReportSelect}
                    onView={handleReportView}
                    selected={isReportSelected(report.id)}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        </motion.div>

        {/* Snackbar for notifications */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={3000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* Chart Interaction Feedback */}
        {chartTooltip.show && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Typography variant="h6" fontWeight="bold" color="primary.main">
                    {selectedChart === 'categories' ? 'Report Category Details' :
                     selectedChart === 'usage' ? 'Report Usage Details' :
                     'Report Details'}
                  </Typography>
                  <IconButton size="small" onClick={() => setChartTooltip({ show: false, content: '' })}>
                    <MoreVert />
                  </IconButton>
                </Box>
                <Typography variant="body1" mt={1}>
                  {chartTooltip.content}
                </Typography>
                {selectedCategory && (
                  <Box mt={2}>
                    <ReportButton
                      variant="outlined"
                      size="small"
                      color="primary"
                      onClick={() => navigate(`/reports/${selectedCategory.toLowerCase()}`)}
                    >
                      View {selectedCategory} Reports
                    </ReportButton>
                    <Typography variant="body2" color="text.secondary" mt={1}>
                      {selectedChart === 'categories' && 'Analyze report categories to understand your focus areas.'}
                      {selectedChart === 'usage' && 'Track report usage to optimize your reporting strategy.'}
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </Box>
    </StandardDashboard>
    </ReportsModuleWrapper>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(ReportsDashboard, 'reports', 'dashboard');
