import { lazy, ComponentType } from 'react';

// Track loaded chunks for performance monitoring
const loadedChunks: Record<string, {
  loadTime: number,
  retries: number,
  size?: number
}> = {};

// Track preloaded chunks
const preloadedChunks: Set<string> = new Set();

/**
 * Enhanced lazy loading utility with retry mechanism and better error handling
 * @param factory Function that imports the component
 * @param chunkName Optional chunk name for better debugging
 * @param maxRetries Maximum number of retries (default: 2)
 * @param retryDelay Delay between retries in ms (default: 1000)
 * @returns Lazy loaded component
 */
export function lazyWithRetry<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  chunkName: string = 'unknown',
  maxRetries: number = 2,
  retryDelay: number = 1000
): React.LazyExoticComponent<T> {
  return lazy(() => {
    const startTime = performance.now();

    // Function to handle loading with retries
    const loadWithRetry = (retries: number): Promise<{ default: T }> => {
      return factory().then(module => {
        // Calculate load time
        const loadTime = performance.now() - startTime;

        // Store metrics
        loadedChunks[chunkName] = {
          loadTime,
          retries: maxRetries - retries,
          size: (module as any).__chunkSize
        };

        // Log performance if not in production
        if (process.env.NODE_ENV !== 'production') {
          console.log(`Chunk "${chunkName}" loaded in ${loadTime.toFixed(2)}ms (${maxRetries - retries} retries)`);
        }

        return module;
      }).catch((error) => {
        console.error(`Error loading chunk "${chunkName}" (${maxRetries - retries} retries):`, error);

        // If we have retries left, try again after delay
        if (retries > 0) {
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(loadWithRetry(retries - 1));
            }, retryDelay);
          });
        }

        // No more retries, throw error
        console.error(`Failed to load chunk "${chunkName}" after ${maxRetries} retries:`, error);
        throw error;
      });
    };

    // Start loading with max retries
    return loadWithRetry(maxRetries);
  });
}

/**
 * Preload a component to improve perceived performance
 * @param factory Function that imports the component
 * @param chunkName Optional chunk name for better debugging
 * @param priority Priority of preloading (higher = sooner)
 */
export function preloadComponent(
  factory: () => Promise<{ default: any }>,
  chunkName: string = 'unknown',
  priority: number = 0
) {
  // Skip if already preloaded
  if (preloadedChunks.has(chunkName)) {
    return;
  }

  // Mark as preloaded
  preloadedChunks.add(chunkName);

  // Use requestIdleCallback if available, otherwise setTimeout
  const schedulePreload = (callback: () => void) => {
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      (window as any).requestIdleCallback(callback, { timeout: 2000 });
    } else {
      setTimeout(callback, 100);
    }
  };

  // Schedule preloading based on priority
  setTimeout(() => {
    schedulePreload(() => {
      const startTime = performance.now();

      factory().then(module => {
        const loadTime = performance.now() - startTime;

        // Store metrics
        loadedChunks[chunkName] = {
          loadTime,
          retries: 0,
          size: (module as any).__chunkSize
        };

        if (process.env.NODE_ENV !== 'production') {
          console.log(`Preloaded chunk "${chunkName}" in ${loadTime.toFixed(2)}ms`);
        }
      }).catch(error => {
        console.warn(`Error preloading component "${chunkName}":`, error);
      });
    });
  }, priority === 0 ? 0 : 1000 / priority);
}

/**
 * Preload multiple components
 * @param factories Array of functions that import components
 * @param prioritized Whether to prioritize loading order (default: true)
 */
export function preloadComponents(
  factories: Array<() => Promise<{ default: any }>> | Array<[() => Promise<{ default: any }>, string, number?]>,
  prioritized: boolean = true
) {
  if (factories.length === 0) return;

  // Check if the first item is a tuple [factory, chunkName, priority]
  const isDetailedFormat = Array.isArray(factories[0]);

  if (isDetailedFormat) {
    // Sort by priority if prioritized is true
    const sortedFactories = prioritized
      ? [...factories as Array<[() => Promise<{ default: any }>, string, number?]>]
          .sort((a, b) => (b[2] || 0) - (a[2] || 0))
      : factories as Array<[() => Promise<{ default: any }>, string, number?]>;

    // Preload each component with its chunk name and priority
    sortedFactories.forEach(([factory, chunkName, priority]) => {
      preloadComponent(factory, chunkName, priority);
    });
  } else {
    // Simple format, just preload each factory
    (factories as Array<() => Promise<{ default: any }>>).forEach(factory => {
      preloadComponent(factory);
    });
  }
}

/**
 * Get performance metrics for loaded chunks
 * @returns Record of chunk metrics
 */
export function getChunkMetrics() {
  return { ...loadedChunks };
}
