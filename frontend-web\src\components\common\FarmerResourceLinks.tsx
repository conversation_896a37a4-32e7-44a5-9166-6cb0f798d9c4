import React from 'react';
import { Box, Card, CardContent, Typography, Link, Grid, Chip, Divider, useTheme, alpha, List, ListItem, ListItemIcon, ListItemText, IconButton, Tooltip } from '@mui/material';
import {
  Launch,
  LocalLibrary,
  Agriculture,
  School,
  Gavel,
  Business,
  Info
} from '@mui/icons-material';
import { motion } from 'framer-motion';

export interface ResourceLink {
  id: string;
  name: string;
  url: string;
  description: string;
  category: 'government' | 'education' | 'regulatory' | 'financial' | 'other';
  icon?: React.ReactNode;
  logoUrl?: string;
}

interface FarmerResourceLinksProps {
  resources: ResourceLink[];
  title?: string;
  showCategories?: boolean;
}

const FarmerResourceLinks: React.FC<FarmerResourceLinksProps> = ({
  resources,
  title = "Useful Resources for Farmers",
  showCategories = false
}) => {
  const theme = useTheme();

  // Group resources by category if showCategories is true
  const groupedResources = React.useMemo(() => {
    if (!showCategories) return { all: resources };

    return resources.reduce((acc, resource) => {
      if (!acc[resource.category]) {
        acc[resource.category] = [];
      }
      acc[resource.category].push(resource);
      return acc;
    }, {} as Record<string, ResourceLink[]>);
  }, [resources, showCategories]);

  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'government':
        return <Agriculture />;
      case 'education':
        return <School />;
      case 'regulatory':
        return <Gavel />;
      case 'financial':
        return <Business />;
      default:
        return <Info />;
    }
  };

  // Get display name for category
  const getCategoryDisplayName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  // Handle opening resource link
  const handleOpenLink = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <Card
      sx={{
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <CardContent sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Typography variant="h5" fontWeight="bold" gutterBottom>
          {title}
        </Typography>

        {showCategories ? (
          <Box sx={{ mt: 2, flex: 1 }}>
            {Object.entries(groupedResources).map(([category, categoryResources]) => (
              <Box key={category} sx={{ mb: 3 }}>
                <Box display="flex" alignItems="center" mb={1}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      mr: 1
                    }}
                  >
                    {getCategoryIcon(category)}
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    {getCategoryDisplayName(category)}
                  </Typography>
                </Box>

                <Divider sx={{ mb: 2 }} />

                <List disablePadding>
                  {categoryResources.map((resource) => (
                    <ListItem
                      key={resource.id}
                      disablePadding
                      sx={{
                        mb: 1,
                        p: 1,
                        borderRadius: 1,
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05)
                        }
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        {resource.icon || getCategoryIcon(resource.category)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Typography variant="body1" fontWeight="medium">
                            {resource.name}
                          </Typography>
                        }
                        secondary={resource.description}
                        primaryTypographyProps={{ fontWeight: 'bold' }}
                      />
                      <Tooltip title="Visit Website">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleOpenLink(resource.url)}
                        >
                          <Launch fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </ListItem>
                  ))}
                </List>
              </Box>
            ))}
          </Box>
        ) : (
          <List disablePadding sx={{ mt: 1, flex: 1 }}>
            {resources.map((resource) => (
              <ListItem
                key={resource.id}
                disablePadding
                sx={{
                  mb: 1,
                  p: 1,
                  borderRadius: 1,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  {resource.icon || getCategoryIcon(resource.category)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Typography variant="body1" fontWeight="medium">
                      {resource.name}
                    </Typography>
                  }
                  secondary={resource.description}
                />
                <Tooltip title="Visit Website">
                  <IconButton
                    size="small"
                    color="primary"
                    onClick={() => handleOpenLink(resource.url)}
                  >
                    <Launch fontSize="small" />
                  </IconButton>
                </Tooltip>
              </ListItem>
            ))}
          </List>
        )}
      </CardContent>
    </Card>
  );
};

export default FarmerResourceLinks;
