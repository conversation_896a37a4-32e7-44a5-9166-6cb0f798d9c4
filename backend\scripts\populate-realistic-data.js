/**
 * Populate Realistic Data for Beta Version
 * Creates comprehensive realistic data for all modules
 */

require('dotenv').config();
const mongodb = require('../src/config/mongodb');
const logger = require('../src/utils/logger');

// Realistic data generators
const generateAnimals = (count = 50) => {
  const species = ['cattle', 'sheep', 'goat', 'pig'];
  const breeds = {
    cattle: ['Holstein', 'Angus', 'Hereford', 'Simmental', 'Brahman'],
    sheep: ['Merino', 'Dorper', 'Blackhead Persian', 'Damara'],
    goat: ['Boer', '<PERSON>ora', 'Saanen', 'Kalahari Red'],
    pig: ['Large White', 'Landrace', 'Duroc', 'Hampshire']
  };
  const genders = ['male', 'female'];
  const statuses = ['active', 'active', 'active', 'active', 'sold', 'deceased'];
  const healthStatuses = ['healthy', 'healthy', 'healthy', 'sick', 'injured', 'recovering'];
  const locations = ['Pen A', 'Pen B', 'Pen C', 'Pasture 1', 'Pasture 2', 'Barn 1', 'Barn 2'];

  const animals = [];
  for (let i = 1; i <= count; i++) {
    const selectedSpecies = species[Math.floor(Math.random() * species.length)];
    const selectedBreed = breeds[selectedSpecies][Math.floor(Math.random() * breeds[selectedSpecies].length)];
    const selectedGender = genders[Math.floor(Math.random() * genders.length)];
    
    animals.push({
      tagNumber: `${selectedSpecies.toUpperCase().charAt(0)}${String(i).padStart(3, '0')}`,
      name: Math.random() > 0.7 ? `${selectedBreed} ${i}` : null,
      species: selectedSpecies,
      breed: selectedBreed,
      gender: selectedGender,
      birthDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 3), // 0-3 years ago
      acquisitionDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000 * 2), // 0-2 years ago
      weight: Math.floor(Math.random() * 500) + 100, // 100-600 kg
      status: statuses[Math.floor(Math.random() * statuses.length)],
      healthStatus: healthStatuses[Math.floor(Math.random() * healthStatuses.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      rfidTag: Math.random() > 0.5 ? `RFID${String(i).padStart(6, '0')}` : null,
      notes: Math.random() > 0.8 ? `Notes for animal ${i}` : '',
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
  return animals;
};

const generateHealthRecords = (animals) => {
  const recordTypes = ['vaccination', 'treatment', 'checkup', 'injury', 'illness'];
  const treatments = ['Antibiotic', 'Vitamin B12', 'Deworming', 'Vaccination', 'Pain Relief'];
  const veterinarians = ['Dr. Smith', 'Dr. Johnson', 'Dr. Williams', 'Dr. Brown'];
  
  const records = [];
  animals.forEach(animal => {
    const recordCount = Math.floor(Math.random() * 5) + 1; // 1-5 records per animal
    for (let i = 0; i < recordCount; i++) {
      records.push({
        animalId: animal.tagNumber,
        recordType: recordTypes[Math.floor(Math.random() * recordTypes.length)],
        date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // Within last year
        treatment: treatments[Math.floor(Math.random() * treatments.length)],
        veterinarian: veterinarians[Math.floor(Math.random() * veterinarians.length)],
        cost: Math.floor(Math.random() * 500) + 50, // R50-R550
        notes: `Health record for ${animal.tagNumber}`,
        nextDueDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000), // Next 6 months
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  });
  return records;
};

const generateBreedingRecords = (animals) => {
  const femaleAnimals = animals.filter(a => a.gender === 'female' && a.species === 'cattle');
  const maleAnimals = animals.filter(a => a.gender === 'male' && a.species === 'cattle');
  
  const records = [];
  femaleAnimals.forEach(female => {
    if (Math.random() > 0.3) { // 70% chance of breeding record
      const male = maleAnimals[Math.floor(Math.random() * maleAnimals.length)];
      records.push({
        femaleId: female.tagNumber,
        maleId: male.tagNumber,
        breedingDate: new Date(Date.now() - Math.random() * 300 * 24 * 60 * 60 * 1000), // Last 10 months
        expectedCalvingDate: new Date(Date.now() + Math.random() * 100 * 24 * 60 * 60 * 1000), // Next 3 months
        status: Math.random() > 0.2 ? 'pregnant' : 'not_pregnant',
        method: Math.random() > 0.5 ? 'natural' : 'artificial_insemination',
        notes: `Breeding record for ${female.tagNumber} and ${male.tagNumber}`,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
  });
  return records;
};

const generateFinancialRecords = () => {
  const transactionTypes = ['income', 'expense'];
  const categories = {
    income: ['Animal Sales', 'Milk Sales', 'Meat Sales', 'Breeding Fees'],
    expense: ['Feed Purchase', 'Veterinary Bills', 'Equipment', 'Labor', 'Utilities', 'Insurance']
  };
  
  const records = [];
  for (let i = 0; i < 100; i++) {
    const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
    const category = categories[type][Math.floor(Math.random() * categories[type].length)];
    
    records.push({
      type,
      category,
      amount: Math.floor(Math.random() * 10000) + 100, // R100-R10,100
      date: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000), // Last year
      description: `${category} transaction ${i + 1}`,
      reference: `TXN${String(i + 1).padStart(6, '0')}`,
      paymentMethod: Math.random() > 0.5 ? 'bank_transfer' : 'cash',
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
  return records;
};

const generateInventoryItems = () => {
  const items = [
    { name: 'Cattle Feed - Premium', category: 'feed', unit: 'kg', costPerUnit: 15 },
    { name: 'Sheep Feed - Standard', category: 'feed', unit: 'kg', costPerUnit: 12 },
    { name: 'Hay Bales', category: 'feed', unit: 'bale', costPerUnit: 80 },
    { name: 'Vaccines - Multivalent', category: 'medical', unit: 'dose', costPerUnit: 25 },
    { name: 'Antibiotics', category: 'medical', unit: 'bottle', costPerUnit: 150 },
    { name: 'Ear Tags', category: 'equipment', unit: 'piece', costPerUnit: 5 },
    { name: 'RFID Tags', category: 'equipment', unit: 'piece', costPerUnit: 35 },
    { name: 'Feed Buckets', category: 'equipment', unit: 'piece', costPerUnit: 45 },
    { name: 'Water Troughs', category: 'equipment', unit: 'piece', costPerUnit: 250 }
  ];
  
  return items.map(item => ({
    ...item,
    currentStock: Math.floor(Math.random() * 1000) + 50,
    minimumStock: Math.floor(Math.random() * 100) + 10,
    maximumStock: Math.floor(Math.random() * 500) + 200,
    supplier: `Supplier ${Math.floor(Math.random() * 5) + 1}`,
    lastRestocked: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    createdAt: new Date(),
    updatedAt: new Date()
  }));
};

// Main population function
async function populateRealisticData() {
  try {
    logger.info('Starting realistic data population...');
    
    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB');
    
    // Generate data
    logger.info('Generating realistic data...');
    const animals = generateAnimals(50);
    const healthRecords = generateHealthRecords(animals);
    const breedingRecords = generateBreedingRecords(animals);
    const financialRecords = generateFinancialRecords();
    const inventoryItems = generateInventoryItems();
    
    // Clear existing data (optional)
    const clearExisting = process.argv.includes('--clear');
    if (clearExisting) {
      logger.info('Clearing existing data...');
      await db.collection('animals').deleteMany({});
      await db.collection('health_records').deleteMany({});
      await db.collection('breeding_records').deleteMany({});
      await db.collection('financial_transactions').deleteMany({});
      await db.collection('inventory_items').deleteMany({});
    }
    
    // Insert data
    logger.info('Inserting animals...');
    await db.collection('animals').insertMany(animals);
    
    logger.info('Inserting health records...');
    await db.collection('health_records').insertMany(healthRecords);
    
    logger.info('Inserting breeding records...');
    await db.collection('breeding_records').insertMany(breedingRecords);
    
    logger.info('Inserting financial records...');
    await db.collection('financial_transactions').insertMany(financialRecords);
    
    logger.info('Inserting inventory items...');
    await db.collection('inventory_items').insertMany(inventoryItems);
    
    // Create indexes for better performance
    logger.info('Creating indexes...');
    await db.collection('animals').createIndex({ tagNumber: 1 }, { unique: true });
    await db.collection('animals').createIndex({ species: 1 });
    await db.collection('animals').createIndex({ status: 1 });
    await db.collection('health_records').createIndex({ animalId: 1 });
    await db.collection('health_records').createIndex({ date: -1 });
    await db.collection('breeding_records').createIndex({ femaleId: 1 });
    await db.collection('breeding_records').createIndex({ breedingDate: -1 });
    await db.collection('financial_transactions').createIndex({ date: -1 });
    await db.collection('financial_transactions').createIndex({ type: 1 });
    await db.collection('inventory_items').createIndex({ category: 1 });
    
    // Log summary
    const summary = {
      animals: await db.collection('animals').countDocuments(),
      healthRecords: await db.collection('health_records').countDocuments(),
      breedingRecords: await db.collection('breeding_records').countDocuments(),
      financialRecords: await db.collection('financial_transactions').countDocuments(),
      inventoryItems: await db.collection('inventory_items').countDocuments()
    };
    
    logger.info('Data population completed successfully!');
    logger.info('Summary:', summary);
    
    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    
    return true;
  } catch (error) {
    logger.error('Error populating realistic data:', error);
    return false;
  }
}

// Run the script
populateRealisticData()
  .then(success => {
    if (success) {
      console.log('\n=== REALISTIC DATA POPULATION COMPLETED ===');
      console.log('✅ 50 Animals with realistic data');
      console.log('✅ Health records for all animals');
      console.log('✅ Breeding records for cattle');
      console.log('✅ Financial transaction history');
      console.log('✅ Inventory items with stock levels');
      console.log('✅ Database indexes for performance');
      console.log('==========================================\n');
      process.exit(0);
    } else {
      console.log('❌ Data population failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during data population:', error);
    process.exit(1);
  });
