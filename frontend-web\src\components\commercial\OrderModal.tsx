import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, IconButton, Typography, FormControl, InputLabel, Select, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Divider, Box, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete, Add, Remove  } from '../../utils/iconImports';
import { DatePicker } from '@mui/x-date-pickers';
import { CommercialOrder, OrderItem } from '../../types/commercial';
import dayjs from 'dayjs';

interface OrderModalProps {
  open: boolean;
  onClose: () => void;
  order?: CommercialOrder | null;
  onSave: (order: Omit<CommercialOrder, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdate?: (id: string, order: Partial<CommercialOrder>) => void;
  onDelete?: (id: string) => void;
  loading?: boolean;
}

const OrderModal: React.FC<OrderModalProps> = ({
  open,
  onClose,
  order,
  onSave,
  onUpdate,
  onDelete,
  loading = false,
}) => {
  const isEditMode = !!order;

  // Form state
  const [formData, setFormData] = useState<Partial<CommercialOrder>>({
    orderNumber: '',
    date: new Date(),
    customer: '',
    items: [],
    totalAmount: 0,
    status: 'pending',
    paymentStatus: 'pending',
    shippingAddress: '',
    notes: '',
  });

  // Items state for the order
  const [items, setItems] = useState<OrderItem[]>([]);
  
  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when order changes
  useEffect(() => {
    if (order) {
      setFormData({
        orderNumber: order.orderNumber,
        date: order.date,
        customer: order.customer,
        status: order.status,
        paymentStatus: order.paymentStatus,
        shippingAddress: order.shippingAddress,
        notes: order.notes,
        totalAmount: order.totalAmount,
      });
      setItems(order.items);
    } else {
      // Reset form for new order
      setFormData({
        orderNumber: `ORD-${Math.floor(10000 + Math.random() * 90000)}`,
        date: new Date(),
        customer: '',
        items: [],
        totalAmount: 0,
        status: 'pending',
        paymentStatus: 'pending',
        shippingAddress: '',
        notes: '',
      });
      setItems([]);
    }
    setErrors({});
  }, [order, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle date changes
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date: date.toDate() }));
      
      // Clear error when field is edited
      if (errors.date) {
        setErrors(prev => ({ ...prev, date: '' }));
      }
    }
  };

  // Add a new item to the order
  const handleAddItem = () => {
    const newItem: OrderItem = {
      id: `ITEM${Date.now()}`,
      productId: '',
      productName: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
    };
    setItems(prev => [...prev, newItem]);
  };

  // Update an item in the order
  const handleItemChange = (index: number, field: keyof OrderItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate total price for the item
    if (field === 'quantity' || field === 'unitPrice') {
      updatedItems[index].totalPrice = 
        updatedItems[index].quantity * updatedItems[index].unitPrice;
    }
    
    setItems(updatedItems);
    
    // Update total amount
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0);
    setFormData(prev => ({ ...prev, totalAmount, items: updatedItems }));
  };

  // Remove an item from the order
  const handleRemoveItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    setItems(updatedItems);
    
    // Update total amount
    const totalAmount = updatedItems.reduce((sum, item) => sum + item.totalPrice, 0);
    setFormData(prev => ({ ...prev, totalAmount, items: updatedItems }));
  };

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.orderNumber) newErrors.orderNumber = 'Order number is required';
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.customer) newErrors.customer = 'Customer is required';
    if (!formData.status) newErrors.status = 'Status is required';
    if (!formData.paymentStatus) newErrors.paymentStatus = 'Payment status is required';
    if (!formData.shippingAddress) newErrors.shippingAddress = 'Shipping address is required';
    
    if (items.length === 0) {
      newErrors.items = 'At least one item is required';
    } else {
      // Validate each item
      const invalidItems = items.some(item => 
        !item.productName || 
        !item.quantity || 
        !item.unitPrice
      );
      
      if (invalidItems) {
        newErrors.items = 'All items must have a name, quantity, and price';
      }
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    const orderData = {
      ...formData,
      items,
      totalAmount: items.reduce((sum, item) => sum + item.totalPrice, 0),
    } as Omit<CommercialOrder, 'id' | 'createdAt' | 'updatedAt'>;
    
    if (isEditMode && order && onUpdate) {
      onUpdate(order.id, orderData);
    } else {
      onSave(orderData);
    }
  };

  // Handle order deletion
  const handleDelete = () => {
    if (isEditMode && order && onDelete) {
      onDelete(order.id);
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{ 
        sx: { 
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        } 
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {isEditMode ? 'Edit Order' : 'New Order'}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Order Details */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Order Details
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Order Number"
              name="orderNumber"
              value={formData.orderNumber || ''}
              onChange={handleChange}
              error={!!errors.orderNumber}
              helperText={errors.orderNumber}
              disabled={isEditMode}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Order Date"
              value={formData.date ? dayjs(formData.date) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.date,
                  helperText: errors.date
                }
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Customer"
              name="customer"
              value={formData.customer || ''}
              onChange={handleChange}
              error={!!errors.customer}
              helperText={errors.customer}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.status}>
              <InputLabel>Status</InputLabel>
              <Select
                name="status"
                value={formData.status || ''}
                onChange={handleSelectChange}
                label="Status"
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="processing">Processing</MenuItem>
                <MenuItem value="shipped">Shipped</MenuItem>
                <MenuItem value="delivered">Delivered</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.paymentStatus}>
              <InputLabel>Payment Status</InputLabel>
              <Select
                name="paymentStatus"
                value={formData.paymentStatus || ''}
                onChange={handleSelectChange}
                label="Payment Status"
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="partial">Partial</MenuItem>
                <MenuItem value="refunded">Refunded</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Shipping Address"
              name="shippingAddress"
              value={formData.shippingAddress || ''}
              onChange={handleChange}
              error={!!errors.shippingAddress}
              helperText={errors.shippingAddress}
              required
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Notes"
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              multiline
              rows={2}
            />
          </Grid>
          
          {/* Order Items */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="subtitle1" fontWeight="bold">
                Order Items
              </Typography>
              <CustomButton 
                startIcon={<Add />} 
                variant="outlined" 
                onClick={handleAddItem}
                size="small"
              >
                Add Item
              </CustomButton>
            </Box>
            
            {errors.items && (
              <Typography color="error" variant="body2" sx={{ mb: 2 }}>
                {errors.items}
              </Typography>
            )}
            
            <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Unit Price</TableCell>
                    <TableCell align="right">Total</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
                          No items added yet. Click "Add Item" to add products to this order.
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    items.map((item, index) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            placeholder="Product name"
                            value={item.productName}
                            onChange={(e) => handleItemChange(index, 'productName', e.target.value)}
                            variant="standard"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            size="small"
                            type="number"
                            inputProps={{ min: 1, style: { textAlign: 'right' } }}
                            value={item.quantity}
                            onChange={(e) => handleItemChange(index, 'quantity', Number(e.target.value))}
                            variant="standard"
                            sx={{ width: '80px' }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            size="small"
                            type="number"
                            inputProps={{ min: 0, step: 0.01, style: { textAlign: 'right' } }}
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(index, 'unitPrice', Number(e.target.value))}
                            variant="standard"
                            sx={{ width: '100px' }}
                          />
                        </TableCell>
                        <TableCell align="right">
                          ${item.totalPrice.toFixed(2)}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton 
                            size="small" 
                            color="error"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <Remove fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                  
                  {items.length > 0 && (
                    <TableRow>
                      <TableCell colSpan={3} align="right" sx={{ fontWeight: 'bold' }}>
                        Total Amount:
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                        ${items.reduce((sum, item) => sum + item.totalPrice, 0).toFixed(2)}
                      </TableCell>
                      <TableCell />
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        {isEditMode && onDelete && (
          <CustomButton 
            onClick={handleDelete} 
            color="error" 
            startIcon={<Delete />}
            disabled={loading}
          >
            Delete
          </CustomButton>
        )}
        <Box flexGrow={1} />
        <CustomButton onClick={onClose} disabled={loading}>
          Cancel
        </CustomButton>
        <CustomButton 
          onClick={handleSubmit} 
          variant="contained" 
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            isEditMode ? 'Update' : 'Save'
          )}
        </CustomButton>
      </DialogActions>
    </Dialog>
  );
};

export default OrderModal;
