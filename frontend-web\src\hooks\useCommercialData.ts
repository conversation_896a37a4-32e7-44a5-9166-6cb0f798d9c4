import { useState, useEffect, useCallback } from 'react';
import {
  CommercialOrder,
  Supplier,
  Auction,
  PriceUpdate,
  CommercialStats
} from '../types/commercial';
import {
  mockOrders,
  mockSuppliers,
  mockAuctions,
  mockPriceUpdates,
  mockCommercialStats
} from '../mocks/commercialData';
import api from '../services/apiService';
// import { useSnackbar } from '../contexts/SnackbarContext';

// Always use mock data to avoid 404 errors
const useMockData = true;

export const useCommercialData = () => {
  // State for data
  const [orders, setOrders] = useState<CommercialOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [priceUpdates, setPriceUpdates] = useState<PriceUpdate[]>([]);
  const [stats, setStats] = useState<CommercialStats>({
    totalOrders: 0,
    pendingOrders: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    activeSuppliers: 0,
    pendingAuctions: 0,
    averageOrderValue: 0,
    topSellingProducts: [],
    revenueByMonth: [],
    ordersByStatus: []
  });

  // State for loading and error
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Define a local showSnackbar function since SnackbarContext might not be set up
  const showSnackbar = (message: string, severity: 'success' | 'error' | 'info' | 'warning') => {
    console.log(`${severity.toUpperCase()}: ${message}`);
    // In a real app, you would use a proper snackbar/toast notification
  };

  // Fetch all commercial data
  const fetchCommercialData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (useMockData) {
        // Use mock data
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
        setOrders(mockOrders);
        setSuppliers(mockSuppliers);
        setAuctions(mockAuctions);
        setPriceUpdates(mockPriceUpdates);
        setStats(mockCommercialStats);
      } else {
        // Use real API
        const [ordersRes, suppliersRes, auctionsRes, priceUpdatesRes, statsRes] = await Promise.all([
          api.get('/commercial/orders'),
          api.get('/commercial/suppliers'),
          api.get('/commercial/auctions'),
          api.get('/commercial/price-updates'),
          api.get('/commercial/stats')
        ]);

        setOrders(ordersRes.data);
        setSuppliers(suppliersRes.data);
        setAuctions(auctionsRes.data);
        setPriceUpdates(priceUpdatesRes.data);
        setStats(statsRes.data);
      }
    } catch (err) {
      console.error('Error fetching commercial data:', err);
      setError('Failed to fetch commercial data');
      showSnackbar('Failed to load commercial data', 'error');
    } finally {
      setLoading(false);
    }
  }, [showSnackbar]);

  // Update stats based on current data
  const updateStats = useCallback(() => {
    const newStats: CommercialStats = {
      totalOrders: orders.length,
      pendingOrders: orders.filter(order => order.status === 'pending').length,
      totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
      monthlyRevenue: orders
        .filter(order => {
          const orderDate = new Date(order.date);
          const currentDate = new Date();
          return orderDate.getMonth() === currentDate.getMonth() &&
                orderDate.getFullYear() === currentDate.getFullYear();
        })
        .reduce((sum, order) => sum + order.totalAmount, 0),
      activeSuppliers: suppliers.filter(supplier => supplier.status === 'active').length,
      pendingAuctions: auctions.filter(auction => auction.status === 'upcoming').length,
      averageOrderValue: orders.length > 0
        ? orders.reduce((sum, order) => sum + order.totalAmount, 0) / orders.length
        : 0,

      topSellingProducts: calculateTopSellingProducts(),
      revenueByMonth: calculateRevenueByMonth(),
      ordersByStatus: [
        { status: 'pending', count: orders.filter(order => order.status === 'pending').length },
        { status: 'processing', count: orders.filter(order => order.status === 'processing').length },
        { status: 'shipped', count: orders.filter(order => order.status === 'shipped').length },
        { status: 'delivered', count: orders.filter(order => order.status === 'delivered').length },
        { status: 'cancelled', count: orders.filter(order => order.status === 'cancelled').length }
      ]
    };

    setStats(newStats);
  }, [orders, suppliers, auctions]);

  // Helper function to calculate top selling products
  const calculateTopSellingProducts = () => {
    const productMap = new Map<string, { quantity: number, revenue: number }>();

    orders.forEach(order => {
      order.items.forEach(item => {
        const existing = productMap.get(item.productName);
        if (existing) {
          existing.quantity += item.quantity;
          existing.revenue += item.totalPrice;
        } else {
          productMap.set(item.productName, {
            quantity: item.quantity,
            revenue: item.totalPrice
          });
        }
      });
    });

    return Array.from(productMap.entries())
      .map(([productName, data]) => ({
        productName,
        quantity: data.quantity,
        revenue: data.revenue
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
  };

  // Helper function to calculate revenue by month
  const calculateRevenueByMonth = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const revenueMap = new Map<string, number>();

    // Initialize all months with zero
    months.forEach(month => revenueMap.set(month, 0));

    // Sum up revenue by month
    orders.forEach(order => {
      const orderDate = new Date(order.date);
      const month = months[orderDate.getMonth()];
      const currentRevenue = revenueMap.get(month) || 0;
      revenueMap.set(month, currentRevenue + order.totalAmount);
    });

    return Array.from(revenueMap.entries())
      .map(([month, revenue]) => ({ month, revenue }))
      .filter((_, index) => index < 6); // Only show 6 months
  };

  // Load data on component mount
  useEffect(() => {
    fetchCommercialData();
  }, [fetchCommercialData]);

  // Update stats when data changes
  useEffect(() => {
    if (orders.length > 0 || suppliers.length > 0 || auctions.length > 0) {
      updateStats();
    }
  }, [orders, suppliers, auctions, updateStats]);

  // CRUD operations for orders
  const addOrder = async (order: Omit<CommercialOrder, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Generate a new ID for the mock order
        const newId = `ORD${(orders.length + 1).toString().padStart(3, '0')}`;
        const now = new Date();

        const newOrder: CommercialOrder = {
          ...order,
          id: newId,
          createdAt: now,
          updatedAt: now
        };

        setOrders(prev => [...prev, newOrder]);
        updateStats();
        showSnackbar('Order created successfully', 'success');
        return newOrder;
      } else {
        // Use real API
        const response = await api.post('/commercial/orders', order);
        setOrders(prev => [...prev, response.data]);
        updateStats();
        showSnackbar('Order created successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error adding order:', err);
      showSnackbar('Failed to create order', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateOrder = async (id: string, updates: Partial<CommercialOrder>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Update in the local state
        const updatedOrders = orders.map(order =>
          order.id === id
            ? { ...order, ...updates, updatedAt: new Date() }
            : order
        );

        setOrders(updatedOrders);
        updateStats();
        showSnackbar('Order updated successfully', 'success');
        return updatedOrders.find(order => order.id === id);
      } else {
        // Use real API
        const response = await api.put(`/commercial/orders/${id}`, updates);
        setOrders(prev => prev.map(order => order.id === id ? response.data : order));
        updateStats();
        showSnackbar('Order updated successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error updating order:', err);
      showSnackbar('Failed to update order', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteOrder = async (id: string) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Remove from the local state
        setOrders(prev => prev.filter(order => order.id !== id));
        updateStats();
        showSnackbar('Order deleted successfully', 'success');
        return true;
      } else {
        // Use real API
        await api.delete(`/commercial/orders/${id}`);
        setOrders(prev => prev.filter(order => order.id !== id));
        updateStats();
        showSnackbar('Order deleted successfully', 'success');
        return true;
      }
    } catch (err) {
      console.error('Error deleting order:', err);
      showSnackbar('Failed to delete order', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // CRUD operations for suppliers
  const addSupplier = async (supplier: Omit<Supplier, 'id'>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Generate a new ID for the mock supplier
        const newId = `SUP${(suppliers.length + 1).toString().padStart(3, '0')}`;

        const newSupplier: Supplier = {
          ...supplier,
          id: newId
        };

        setSuppliers(prev => [...prev, newSupplier]);
        updateStats();
        showSnackbar('Supplier added successfully', 'success');
        return newSupplier;
      } else {
        // Use real API
        const response = await api.post('/commercial/suppliers', supplier);
        setSuppliers(prev => [...prev, response.data]);
        updateStats();
        showSnackbar('Supplier added successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error adding supplier:', err);
      showSnackbar('Failed to add supplier', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateSupplier = async (id: string, updates: Partial<Supplier>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Update in the local state
        const updatedSuppliers = suppliers.map(supplier =>
          supplier.id === id
            ? { ...supplier, ...updates }
            : supplier
        );

        setSuppliers(updatedSuppliers);
        updateStats();
        showSnackbar('Supplier updated successfully', 'success');
        return updatedSuppliers.find(supplier => supplier.id === id);
      } else {
        // Use real API
        const response = await api.put(`/commercial/suppliers/${id}`, updates);
        setSuppliers(prev => prev.map(supplier => supplier.id === id ? response.data : supplier));
        updateStats();
        showSnackbar('Supplier updated successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error updating supplier:', err);
      showSnackbar('Failed to update supplier', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteSupplier = async (id: string) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Remove from the local state
        setSuppliers(prev => prev.filter(supplier => supplier.id !== id));
        updateStats();
        showSnackbar('Supplier deleted successfully', 'success');
        return true;
      } else {
        // Use real API
        await api.delete(`/commercial/suppliers/${id}`);
        setSuppliers(prev => prev.filter(supplier => supplier.id !== id));
        updateStats();
        showSnackbar('Supplier deleted successfully', 'success');
        return true;
      }
    } catch (err) {
      console.error('Error deleting supplier:', err);
      showSnackbar('Failed to delete supplier', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // CRUD operations for auctions
  const addAuction = async (auction: Omit<Auction, 'id'>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Generate a new ID for the mock auction
        const newId = `AUC${(auctions.length + 1).toString().padStart(3, '0')}`;

        const newAuction: Auction = {
          ...auction,
          id: newId
        };

        setAuctions(prev => [...prev, newAuction]);
        updateStats();
        showSnackbar('Auction created successfully', 'success');
        return newAuction;
      } else {
        // Use real API
        const response = await api.post('/commercial/auctions', auction);
        setAuctions(prev => [...prev, response.data]);
        updateStats();
        showSnackbar('Auction created successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error adding auction:', err);
      showSnackbar('Failed to create auction', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateAuction = async (id: string, updates: Partial<Auction>) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Update in the local state
        const updatedAuctions = auctions.map(auction =>
          auction.id === id
            ? { ...auction, ...updates }
            : auction
        );

        setAuctions(updatedAuctions);
        updateStats();
        showSnackbar('Auction updated successfully', 'success');
        return updatedAuctions.find(auction => auction.id === id);
      } else {
        // Use real API
        const response = await api.put(`/commercial/auctions/${id}`, updates);
        setAuctions(prev => prev.map(auction => auction.id === id ? response.data : auction));
        updateStats();
        showSnackbar('Auction updated successfully', 'success');
        return response.data;
      }
    } catch (err) {
      console.error('Error updating auction:', err);
      showSnackbar('Failed to update auction', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteAuction = async (id: string) => {
    try {
      setLoading(true);

      if (useMockData) {
        // Remove from the local state
        setAuctions(prev => prev.filter(auction => auction.id !== id));
        updateStats();
        showSnackbar('Auction deleted successfully', 'success');
        return true;
      } else {
        // Use real API
        await api.delete(`/commercial/auctions/${id}`);
        setAuctions(prev => prev.filter(auction => auction.id !== id));
        updateStats();
        showSnackbar('Auction deleted successfully', 'success');
        return true;
      }
    } catch (err) {
      console.error('Error deleting auction:', err);
      showSnackbar('Failed to delete auction', 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Generate Excel report
  const generateExcelReport = async (reportType: 'orders' | 'suppliers' | 'auctions') => {
    try {
      setLoading(true);

      if (useMockData) {
        // Simulate report generation
        await new Promise(resolve => setTimeout(resolve, 1500));
        showSnackbar(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully`, 'success');
        return `${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx`;
      } else {
        // Use real API
        const response = await api.get(`/commercial/reports/${reportType}`);
        showSnackbar(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully`, 'success');
        return response.data.reportUrl;
      }
    } catch (err) {
      console.error(`Error generating ${reportType} report:`, err);
      showSnackbar(`Failed to generate ${reportType} report`, 'error');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    orders,
    suppliers,
    auctions,
    priceUpdates,
    stats,
    loading,
    error,
    fetchCommercialData,
    addOrder,
    updateOrder,
    deleteOrder,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    addAuction,
    updateAuction,
    deleteAuction,
    generateExcelReport
  };
};
