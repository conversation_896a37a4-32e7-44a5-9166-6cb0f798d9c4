import { PieProps } from 'recharts';

// Enhanced type definitions for recharts components
export interface EnhancedPieLabelRenderProps {
  cx?: number;
  cy?: number;
  midAngle?: number;
  innerRadius?: number;
  outerRadius?: number;
  percent?: number;
  index?: number;
  name?: string;
  value?: number;
  fill?: string;
  stroke?: string;
  payload?: any;
}

// Additional chart types
export interface AreaChartProps {
  data?: any[];
  width?: number;
  height?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  stackOffset?: 'expand' | 'none' | 'wiggle' | 'silhouette';
  baseValue?: number | 'dataMin' | 'dataMax' | 'auto';
}

export interface LineChartProps {
  data?: any[];
  width?: number;
  height?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

export interface ScatterChartProps {
  data?: any[];
  width?: number;
  height?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

export interface RadarChartProps {
  data?: any[];
  width?: number;
  height?: number;
  cx?: number | string;
  cy?: number | string;
  innerRadius?: number | string;
  outerRadius?: number | string;
}

export interface RadialBarChartProps {
  data?: any[];
  width?: number;
  height?: number;
  cx?: number | string;
  cy?: number | string;
  innerRadius?: number | string;
  outerRadius?: number | string;
  barSize?: number;
  startAngle?: number;
  endAngle?: number;
}

export interface ComposedChartProps {
  data?: any[];
  width?: number;
  height?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

export interface TreemapProps {
  data?: any[];
  width?: number;
  height?: number;
  dataKey?: string;
  aspectRatio?: number;
}

export interface SankeyProps {
  data?: {
    nodes: Array<{ name: string }>;
    links: Array<{ source: number; target: number; value: number }>;
  };
  width?: number;
  height?: number;
  nodePadding?: number;
  nodeWidth?: number;
  linkCurvature?: number;
}
