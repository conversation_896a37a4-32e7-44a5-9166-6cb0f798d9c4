/**
 * Initialize MongoDB Script
 * 
 * This script initializes the MongoDB database with proper schemas and indexes
 */

const { connectDB, disconnectDB } = require('../src/config/mongodb');
const logger = require('../src/utils/logger');

async function initializeDatabase() {
  try {
    logger.info('Initializing MongoDB database...');
    
    // Connect to MongoDB
    const { db } = await connectDB();
    
    // Create collections with validators
    await createCollections(db);
    
    // Create indexes
    await createIndexes(db);
    
    logger.info('MongoDB database initialization completed successfully');
  } catch (error) {
    logger.error('Error initializing MongoDB database:', error);
  } finally {
    await disconnectDB();
  }
}

async function createCollections(db) {
  logger.info('Creating collections with validators...');
  
  // Define collections with validators
  const collections = [
    {
      name: 'animals',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['tagNumber', 'species', 'status'],
          properties: {
            tagNumber: { bsonType: 'string' },
            name: { bsonType: 'string' },
            species: { bsonType: 'string' },
            breed: { bsonType: 'string' },
            gender: { bsonType: 'string' },
            birthDate: { bsonType: ['date', 'string'] },
            acquisitionDate: { bsonType: ['date', 'string'] },
            weight: { bsonType: 'number' },
            status: { bsonType: 'string' },
            healthStatus: { bsonType: 'string' },
            location: { bsonType: 'string' },
            rfidTag: { bsonType: 'string' },
            notes: { bsonType: 'string' },
            purchasePrice: { bsonType: 'number' },
            estimatedValue: { bsonType: 'number' },
            images: { bsonType: 'array' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    },
    {
      name: 'healthrecords',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['animal', 'recordType', 'date'],
          properties: {
            animal: { bsonType: 'objectId' },
            recordType: { bsonType: 'string' },
            date: { bsonType: ['date', 'string'] },
            performer: { bsonType: 'objectId' },
            diagnosis: { bsonType: 'string' },
            symptoms: { bsonType: 'array' },
            medications: { bsonType: 'array' },
            procedures: { bsonType: 'array' },
            cost: { bsonType: 'number' },
            notes: { bsonType: 'string' },
            status: { bsonType: 'string' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    },
    {
      name: 'breedingrecords',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['maleAnimal', 'femaleAnimal', 'breedingDate'],
          properties: {
            maleAnimal: { bsonType: 'objectId' },
            femaleAnimal: { bsonType: 'objectId' },
            breedingDate: { bsonType: ['date', 'string'] },
            method: { bsonType: 'string' },
            location: { bsonType: 'string' },
            status: { bsonType: 'string' },
            confirmedPregnant: { bsonType: 'bool' },
            confirmationDate: { bsonType: ['date', 'string'] },
            confirmationMethod: { bsonType: 'string' },
            expectedDueDate: { bsonType: ['date', 'string'] },
            notes: { bsonType: 'string' },
            performer: { bsonType: 'objectId' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    },
    {
      name: 'birthrecords',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['mother', 'birthDate', 'offspring'],
          properties: {
            mother: { bsonType: 'objectId' },
            father: { bsonType: 'objectId' },
            birthDate: { bsonType: ['date', 'string'] },
            location: { bsonType: 'string' },
            birthType: { bsonType: 'string' },
            assistance: { bsonType: 'string' },
            complications: { bsonType: 'string' },
            breedingRecord: { bsonType: 'objectId' },
            offspring: { bsonType: 'array' },
            totalOffspring: { bsonType: 'int' },
            aliveOffspring: { bsonType: 'int' },
            notes: { bsonType: 'string' },
            performer: { bsonType: 'objectId' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    },
    {
      name: 'transactions',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['transactionDate', 'type', 'category', 'amount'],
          properties: {
            transactionDate: { bsonType: ['date', 'string'] },
            type: { bsonType: 'string' },
            category: { bsonType: 'string' },
            subcategory: { bsonType: 'string' },
            amount: { bsonType: 'number' },
            currency: { bsonType: 'string' },
            description: { bsonType: 'string' },
            paymentMethod: { bsonType: 'string' },
            reference: { bsonType: 'string' },
            vendor: { bsonType: 'string' },
            status: { bsonType: 'string' },
            relatedTo: { bsonType: 'object' },
            notes: { bsonType: 'string' },
            createdBy: { bsonType: 'objectId' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    },
    {
      name: 'users',
      validator: {
        $jsonSchema: {
          bsonType: 'object',
          required: ['username', 'email', 'role'],
          properties: {
            username: { bsonType: 'string' },
            email: { bsonType: 'string' },
            password: { bsonType: 'string' },
            firstName: { bsonType: 'string' },
            lastName: { bsonType: 'string' },
            role: { bsonType: 'string' },
            status: { bsonType: 'string' },
            permissions: { bsonType: 'array' },
            lastLogin: { bsonType: 'date' },
            createdAt: { bsonType: 'date' },
            updatedAt: { bsonType: 'date' }
          }
        }
      }
    }
  ];
  
  // Create collections with validators
  for (const collection of collections) {
    try {
      // Check if collection exists
      const collectionExists = await db.listCollections({ name: collection.name }).hasNext();
      
      if (!collectionExists) {
        logger.info(`Creating collection: ${collection.name}`);
        await db.createCollection(collection.name, { validator: collection.validator });
        logger.info(`Collection ${collection.name} created successfully`);
      } else {
        logger.info(`Collection ${collection.name} already exists`);
      }
    } catch (error) {
      logger.error(`Error creating collection ${collection.name}:`, error);
    }
  }
}

async function createIndexes(db) {
  logger.info('Creating indexes...');
  
  // Define indexes
  const indexes = [
    {
      collection: 'animals',
      indexes: [
        { key: { tagNumber: 1 }, options: { unique: true } },
        { key: { species: 1 }, options: {} },
        { key: { status: 1 }, options: {} },
        { key: { location: 1 }, options: {} }
      ]
    },
    {
      collection: 'healthrecords',
      indexes: [
        { key: { animal: 1 }, options: {} },
        { key: { date: -1 }, options: {} },
        { key: { recordType: 1 }, options: {} }
      ]
    },
    {
      collection: 'breedingrecords',
      indexes: [
        { key: { femaleAnimal: 1 }, options: {} },
        { key: { breedingDate: -1 }, options: {} },
        { key: { status: 1 }, options: {} }
      ]
    },
    {
      collection: 'birthrecords',
      indexes: [
        { key: { mother: 1 }, options: {} },
        { key: { birthDate: -1 }, options: {} }
      ]
    },
    {
      collection: 'transactions',
      indexes: [
        { key: { transactionDate: -1 }, options: {} },
        { key: { type: 1 }, options: {} },
        { key: { category: 1 }, options: {} }
      ]
    },
    {
      collection: 'users',
      indexes: [
        { key: { username: 1 }, options: { unique: true } },
        { key: { email: 1 }, options: { unique: true } },
        { key: { role: 1 }, options: {} }
      ]
    }
  ];
  
  // Create indexes
  for (const item of indexes) {
    try {
      const collection = db.collection(item.collection);
      
      for (const index of item.indexes) {
        logger.info(`Creating index on ${item.collection}: ${JSON.stringify(index.key)}`);
        await collection.createIndex(index.key, index.options);
        logger.info(`Index created successfully`);
      }
    } catch (error) {
      logger.error(`Error creating indexes for collection ${item.collection}:`, error);
    }
  }
}

// Run the initialization function
initializeDatabase()
  .then(() => {
    logger.info('MongoDB database initialization script completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Error in MongoDB database initialization script:', error);
    process.exit(1);
  });
