/**
 * Notification API Routes
 */

const express = require('express');
const router = express.Router();
const notificationService = require('../services/notificationService');

/**
 * @route POST /api/notifications/sms
 * @desc Send an SMS notification
 * @access Private
 */
router.post('/sms', async (req, res) => {
  try {
    const { to, message, priority } = req.body;
    
    if (!to || !message) {
      return res.status(400).json({ success: false, error: 'Phone number and message are required' });
    }
    
    const result = await notificationService.sendSMS(to, message, priority || 'medium');
    
    if (result.success) {
      return res.json({ success: true, messageId: result.messageId, timestamp: result.timestamp });
    } else {
      return res.status(500).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Error in SMS endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

/**
 * @route POST /api/notifications/voice
 * @desc Make a voice call
 * @access Private
 */
router.post('/voice', async (req, res) => {
  try {
    const { to, message, priority } = req.body;
    
    if (!to || !message) {
      return res.status(400).json({ success: false, error: 'Phone number and message are required' });
    }
    
    const result = await notificationService.makeVoiceCall(to, message, priority || 'high');
    
    if (result.success) {
      return res.json({ success: true, callId: result.callId, timestamp: result.timestamp });
    } else {
      return res.status(500).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Error in voice call endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

/**
 * @route POST /api/notifications/whatsapp
 * @desc Send a WhatsApp message
 * @access Private
 */
router.post('/whatsapp', async (req, res) => {
  try {
    const { to, message, priority } = req.body;
    
    if (!to || !message) {
      return res.status(400).json({ success: false, error: 'Phone number and message are required' });
    }
    
    const result = await notificationService.sendWhatsApp(to, message, priority || 'medium');
    
    if (result.success) {
      return res.json({ success: true, messageId: result.messageId, timestamp: result.timestamp });
    } else {
      return res.status(500).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Error in WhatsApp endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

/**
 * @route POST /api/notifications/email
 * @desc Send an email
 * @access Private
 */
router.post('/email', async (req, res) => {
  try {
    const { to, subject, message, priority } = req.body;
    
    if (!to || !subject || !message) {
      return res.status(400).json({ success: false, error: 'Email address, subject, and message are required' });
    }
    
    const result = await notificationService.sendEmail(to, subject, message, priority || 'low');
    
    if (result.success) {
      return res.json({ success: true, messageId: result.messageId, timestamp: result.timestamp });
    } else {
      return res.status(500).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Error in email endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

/**
 * @route POST /api/notifications/birth-alert
 * @desc Send a birth prediction alert using multiple notification methods
 * @access Private
 */
router.post('/birth-alert', async (req, res) => {
  try {
    const {
      animalId,
      animalName,
      daysRemaining,
      recipient,
      notificationTypes,
      customMessage,
      priority
    } = req.body;
    
    if (!animalId || !animalName || daysRemaining === undefined || !recipient || !notificationTypes) {
      return res.status(400).json({
        success: false,
        error: 'Animal ID, name, days remaining, recipient, and notification types are required'
      });
    }
    
    const results = await notificationService.sendBirthPredictionAlert({
      animalId,
      animalName,
      daysRemaining,
      recipient,
      notificationTypes,
      customMessage,
      priority
    });
    
    const successCount = results.filter(r => r.result.success).length;
    
    if (successCount === results.length) {
      return res.json({
        success: true,
        message: `All ${successCount} notifications sent successfully`,
        results
      });
    } else if (successCount > 0) {
      return res.json({
        success: true,
        message: `${successCount}/${results.length} notifications sent successfully`,
        results
      });
    } else {
      return res.status(500).json({
        success: false,
        error: 'Failed to send notifications',
        results
      });
    }
  } catch (error) {
    console.error('Error in birth alert endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

/**
 * @route GET /api/notifications/test
 * @desc Test the notification service
 * @access Private
 */
router.get('/test', async (req, res) => {
  try {
    // Test SMS
    const smsResult = await notificationService.sendSMS(
      '0794484159',
      'This is a test SMS from AMPD Livestock',
      'medium'
    );
    
    return res.json({
      success: true,
      message: 'Notification service test completed',
      results: {
        sms: smsResult
      }
    });
  } catch (error) {
    console.error('Error in test endpoint:', error);
    return res.status(500).json({ success: false, error: error.message || 'Server error' });
  }
});

module.exports = router;
