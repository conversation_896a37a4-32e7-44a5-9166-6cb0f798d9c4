/**
 * User Validation Schemas
 * 
 * This module provides validation schemas for user-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// User roles
const roles = ['admin', 'manager', 'super_user', 'staff', 'veterinarian', 'viewer'];

// User permissions
const permissions = [
  'view_animals', 'create_animal', 'update_animal', 'delete_animal',
  'view_health_records', 'create_health_record', 'update_health_record', 'delete_health_record',
  'view_breeding_records', 'create_breeding_record', 'update_breeding_record', 'delete_breeding_record',
  'view_birth_records', 'create_birth_record', 'update_birth_record', 'delete_birth_record',
  'view_transactions', 'create_transaction', 'update_transaction', 'delete_transaction',
  'view_inventory', 'create_inventory', 'update_inventory', 'delete_inventory',
  'view_reports', 'create_reports', 'export_reports',
  'view_users', 'create_user', 'update_user', 'delete_user',
  'all'
];

// User status
const statuses = ['active', 'inactive', 'locked', 'pending'];

// Login validation schema
const loginSchema = Joi.object({
  username: Joi.string().trim().required().messages({
    'string.empty': 'Username cannot be empty',
    'any.required': 'Username is required'
  }),
  password: Joi.string().required().messages({
    'string.empty': 'Password cannot be empty',
    'any.required': 'Password is required'
  })
});

// Register validation schema
const registerSchema = Joi.object({
  username: Joi.string().trim().min(3).max(30).required().messages({
    'string.empty': 'Username cannot be empty',
    'string.min': 'Username must be at least 3 characters',
    'string.max': 'Username cannot exceed 30 characters',
    'any.required': 'Username is required'
  }),
  email: Joi.string().email().required().messages({
    'string.empty': 'Email cannot be empty',
    'string.email': 'Email must be a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).required().messages({
    'string.empty': 'Password cannot be empty',
    'string.min': 'Password must be at least 8 characters',
    'any.required': 'Password is required'
  }),
  firstName: Joi.string().trim().required().messages({
    'string.empty': 'First name cannot be empty',
    'any.required': 'First name is required'
  }),
  lastName: Joi.string().trim().required().messages({
    'string.empty': 'Last name cannot be empty',
    'any.required': 'Last name is required'
  }),
  role: Joi.string().valid(...roles).default('viewer').messages({
    'any.only': `Role must be one of: ${roles.join(', ')}`
  }),
  permissions: Joi.array().items(Joi.string().valid(...permissions)).default([]).messages({
    'array.base': 'Permissions must be an array',
    'any.only': `Permission must be one of: ${permissions.join(', ')}`
  })
});

// Create user validation schema
const createUserSchema = Joi.object({
  username: Joi.string().trim().min(3).max(30).required().messages({
    'string.empty': 'Username cannot be empty',
    'string.min': 'Username must be at least 3 characters',
    'string.max': 'Username cannot exceed 30 characters',
    'any.required': 'Username is required'
  }),
  email: Joi.string().email().required().messages({
    'string.empty': 'Email cannot be empty',
    'string.email': 'Email must be a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).required().messages({
    'string.empty': 'Password cannot be empty',
    'string.min': 'Password must be at least 8 characters',
    'any.required': 'Password is required'
  }),
  firstName: Joi.string().trim().required().messages({
    'string.empty': 'First name cannot be empty',
    'any.required': 'First name is required'
  }),
  lastName: Joi.string().trim().required().messages({
    'string.empty': 'Last name cannot be empty',
    'any.required': 'Last name is required'
  }),
  role: Joi.string().valid(...roles).default('viewer').messages({
    'any.only': `Role must be one of: ${roles.join(', ')}`
  }),
  permissions: Joi.array().items(Joi.string().valid(...permissions)).default([]).messages({
    'array.base': 'Permissions must be an array',
    'any.only': `Permission must be one of: ${permissions.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).default('active').messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  })
});

// Update user validation schema
const updateUserSchema = Joi.object({
  email: Joi.string().email().messages({
    'string.email': 'Email must be a valid email address'
  }),
  password: Joi.string().min(8).messages({
    'string.min': 'Password must be at least 8 characters'
  }),
  firstName: Joi.string().trim().messages({
    'string.empty': 'First name cannot be empty'
  }),
  lastName: Joi.string().trim().messages({
    'string.empty': 'Last name cannot be empty'
  }),
  role: Joi.string().valid(...roles).messages({
    'any.only': `Role must be one of: ${roles.join(', ')}`
  }),
  permissions: Joi.array().items(Joi.string().valid(...permissions)).messages({
    'array.base': 'Permissions must be an array',
    'any.only': `Permission must be one of: ${permissions.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  })
});

// Get users query validation schema
const getUsersQuerySchema = Joi.object({
  role: Joi.string().valid(...roles).messages({
    'any.only': `Role must be one of: ${roles.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  })
}).concat(schemas.pagination);

// User ID parameter validation schema
const userIdParamSchema = Joi.object({
  id: schemas.id
});

// Change password validation schema
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    'string.empty': 'Current password cannot be empty',
    'any.required': 'Current password is required'
  }),
  newPassword: Joi.string().min(8).required().messages({
    'string.empty': 'New password cannot be empty',
    'string.min': 'New password must be at least 8 characters',
    'any.required': 'New password is required'
  })
});

module.exports = {
  loginSchema,
  registerSchema,
  createUserSchema,
  updateUserSchema,
  getUsersQuerySchema,
  userIdParamSchema,
  changePasswordSchema,
  roles,
  permissions,
  statuses
};
