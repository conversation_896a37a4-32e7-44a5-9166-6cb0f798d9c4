/**
 * Error Handling Middleware
 *
 * This middleware provides centralized error handling for the API.
 */

const logger = require('../utils/logger');

/**
 * Not Found Error Handler
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function notFound(req, res, next) {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  error.status = 404;
  logger.warn(`404 Not Found: ${req.method} ${req.originalUrl}`);
  next(error);
}

/**
 * General Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function errorHandler(err, req, res, next) {
  // Set default status code
  const statusCode = err.status || 500;

  // Log the error
  if (statusCode === 500) {
    logger.error(`Error: ${err.message}`, {
      stack: err.stack,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });
  } else {
    logger.warn(`${statusCode} Error: ${err.message}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });
  }

  // Prepare error response
  const errorResponse = {
    success: false,
    message: err.message || 'Something went wrong',
    status: statusCode
  };

  // Add stack trace in development environment
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = err.stack;
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
}

/**
 * Mongoose Validation Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function validationErrorHandler(err, req, res, next) {
  // Check if it's a Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = {};

    // Extract validation errors
    Object.keys(err.errors).forEach(key => {
      errors[key] = err.errors[key].message;
    });

    // Log the validation error
    logger.warn(`Validation Error: ${JSON.stringify(errors)}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });

    // Send validation error response
    return res.status(400).json({
      success: false,
      message: 'Validation Error',
      errors
    });
  }

  // If it's not a validation error, pass it to the next error handler
  next(err);
}

/**
 * Duplicate Key Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function duplicateKeyErrorHandler(err, req, res, next) {
  // Check if it's a MongoDB duplicate key error
  if (err.name === 'MongoError' && err.code === 11000) {
    // Extract the duplicate key field
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];

    // Log the duplicate key error
    logger.warn(`Duplicate Key Error: ${field}=${value}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });

    // Send duplicate key error response
    return res.status(409).json({
      success: false,
      message: 'Duplicate Key Error',
      error: `${field} '${value}' already exists`
    });
  }

  // If it's not a duplicate key error, pass it to the next error handler
  next(err);
}

/**
 * Cast Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function castErrorHandler(err, req, res, next) {
  // Check if it's a Mongoose cast error
  if (err.name === 'CastError') {
    // Log the cast error
    logger.warn(`Cast Error: ${err.message}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });

    // Send cast error response
    return res.status(400).json({
      success: false,
      message: 'Invalid Data',
      error: `Invalid ${err.path}: ${err.value}`
    });
  }

  // If it's not a cast error, pass it to the next error handler
  next(err);
}

/**
 * JWT Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function jwtErrorHandler(err, req, res, next) {
  // Check if it's a JWT error
  if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError' || err.name === 'NotBeforeError') {
    // Log the JWT error
    logger.warn(`JWT Error: ${err.message}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip
    });

    // Send JWT error response
    return res.status(401).json({
      success: false,
      message: 'Authentication Error',
      error: err.message
    });
  }

  // If it's not a JWT error, pass it to the next error handler
  next(err);
}

/**
 * Rate Limit Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function rateLimitErrorHandler(err, req, res, next) {
  // Check if it's a rate limit error
  if (err.name === 'RateLimitError') {
    // Log the rate limit error
    logger.warn(`Rate Limit Error: ${err.message}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });

    // Send rate limit error response
    return res.status(429).json({
      success: false,
      message: 'Too Many Requests',
      error: err.message
    });
  }

  // If it's not a rate limit error, pass it to the next error handler
  next(err);
}

/**
 * Permission Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function permissionErrorHandler(err, req, res, next) {
  // Check if it's a permission error
  if (err.name === 'PermissionError' || (err.status === 403 && err.message.includes('permission'))) {
    // Log the permission error
    logger.warn(`Permission Error: ${err.message}`, {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? `${req.user.username} (${req.user.role})` : 'anonymous',
      requiredPermissions: err.requiredPermissions || 'unknown'
    });

    // Send permission error response
    return res.status(403).json({
      success: false,
      message: 'Permission Denied',
      error: err.message,
      requiredPermissions: err.requiredPermissions,
      userRole: req.user ? req.user.role : null,
      userPermissions: req.user ? req.user.permissions : null
    });
  }

  // If it's not a permission error, pass it to the next error handler
  next(err);
}

/**
 * Database Connection Error Handler
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
function dbConnectionErrorHandler(err, req, res, next) {
  // Check if it's a database connection error
  if (err.name === 'MongoNetworkError' ||
      err.name === 'MongoTimeoutError' ||
      (err.message && err.message.includes('MongoDB'))) {

    // Log the database connection error
    logger.error(`Database Connection Error: ${err.message}`, {
      stack: err.stack,
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      user: req.user ? req.user.username : 'anonymous'
    });

    // Send database connection error response
    return res.status(503).json({
      success: false,
      message: 'Database Connection Error',
      error: 'The server is currently unable to connect to the database. Please try again later.'
    });
  }

  // If it's not a database connection error, pass it to the next error handler
  next(err);
}

// Export all error handlers
module.exports = {
  notFound,
  validationErrorHandler,
  duplicateKeyErrorHandler,
  castErrorHandler,
  jwtErrorHandler,
  rateLimitErrorHandler,
  permissionErrorHandler,
  dbConnectionErrorHandler,
  errorHandler
};
