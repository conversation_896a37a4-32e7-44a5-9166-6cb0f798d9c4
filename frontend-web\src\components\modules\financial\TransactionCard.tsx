import React from 'react';
import { <PERSON>, CardContent, Typography, Box, Chip, IconButton, <PERSON><PERSON><PERSON>, Divider, useTheme, alpha } from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  AttachMoney as MoneyIcon,
  Receipt as ReceiptIcon,
  AccountBalance as BankIcon,
  MoneyOff as ExpenseIcon,
  Pets as AnimalIcon,
  Inventory as InventoryIcon
} from '@mui/icons-material';
import { Transaction } from '../../../mocks/financialData';
import { formatCurrency } from '../../../utils/formatters';

// Transaction type colors
const TRANSACTION_TYPE_COLORS = {
  income: 'success',
  expense: 'error'
};

// Transaction status colors
const TRANSACTION_STATUS_COLORS = {
  pending: 'warning',
  completed: 'success',
  cancelled: 'error'
};

// Payment method icons
const PAYMENT_METHOD_ICONS = {
  cash: <MoneyIcon />,
  bank: <BankIcon />,
  credit: <ReceiptIcon />
};

interface TransactionCardProps {
  transaction: Transaction;
  onEdit?: (transaction: Transaction) => void;
  onDelete?: (transaction: Transaction) => void;
  onView?: (transaction: Transaction) => void;
  showActions?: boolean;
  compact?: boolean;
}

const TransactionCard: React.FC<TransactionCardProps> = ({
  transaction,
  onEdit,
  onDelete,
  onView,
  showActions = true,
  compact = false
}) => {
  const theme = useTheme();
  
  // Format date
  const formattedDate = new Date(transaction.date).toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
  
  // Determine card height based on compact mode
  const cardHeight = compact ? 'auto' : 'auto';
  
  // Get related entity icon
  const getRelatedEntityIcon = () => {
    if (!transaction.relatedEntity) return null;
    
    switch (transaction.relatedEntity.type) {
      case 'animal':
        return <AnimalIcon fontSize="small" />;
      case 'feed':
      case 'equipment':
        return <InventoryIcon fontSize="small" />;
      default:
        return null;
    }
  };
  
  return (
    <Card
      sx={{
        height: cardHeight,
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8]
        },
        bgcolor: alpha(
          transaction.type === 'income' 
            ? theme.palette.success.main 
            : theme.palette.error.main,
          0.05
        ),
        borderLeft: `4px solid ${
          transaction.type === 'income'
            ? theme.palette.success.main
            : theme.palette.error.main
        }`
      }}
    >
      <CardContent sx={{ flexGrow: 1, pb: 1 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Box>
            <Typography variant="h6" gutterBottom noWrap>
              {transaction.category}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {formattedDate}
            </Typography>
          </Box>
          <Box>
            <Typography 
              variant="h6" 
              color={transaction.type === 'income' ? 'success.main' : 'error.main'}
              fontWeight="bold"
            >
              {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
            </Typography>
            <Chip
              label={transaction.type}
              size="small"
              color={TRANSACTION_TYPE_COLORS[transaction.type] as any}
              sx={{ fontWeight: 'bold', fontSize: '0.7rem' }}
            />
          </Box>
        </Box>
        
        <Divider sx={{ my: 1 }} />
        
        <Typography variant="body2" sx={{ mb: 1 }}>
          {transaction.description}
        </Typography>
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Tooltip title={`Paid via ${transaction.paymentMethod}`}>
              <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                {PAYMENT_METHOD_ICONS[transaction.paymentMethod] || <MoneyIcon />}
              </Box>
            </Tooltip>
            
            <Chip
              label={transaction.status}
              size="small"
              color={TRANSACTION_STATUS_COLORS[transaction.status] as any}
              sx={{ fontWeight: 'medium', fontSize: '0.7rem' }}
            />
          </Box>
          
          {transaction.relatedEntity && (
            <Tooltip title={`Related to ${transaction.relatedEntity.type} #${transaction.relatedEntity.id}`}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {getRelatedEntityIcon()}
                <Typography variant="caption" sx={{ ml: 0.5 }}>
                  {transaction.relatedEntity.type}
                </Typography>
              </Box>
            </Tooltip>
          )}
        </Box>
      </CardContent>
      
      {/* Action buttons */}
      {showActions && (
        <Box 
          sx={{ 
            display: 'flex', 
            justifyContent: 'flex-end',
            p: 1,
            borderTop: `1px solid ${theme.palette.divider}`,
            bgcolor: theme.palette.background.default
          }}
        >
          {onView && (
            <Tooltip title="View Details">
              <IconButton size="small" onClick={() => onView(transaction)}>
                <ViewIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {onEdit && (
            <Tooltip title="Edit">
              <IconButton size="small" onClick={() => onEdit(transaction)}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          {onDelete && (
            <Tooltip title="Delete">
              <IconButton size="small" onClick={() => onDelete(transaction)}>
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}
    </Card>
  );
};

export default TransactionCard;
