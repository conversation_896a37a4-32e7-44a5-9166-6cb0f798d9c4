/**
 * Financial Validation Schemas
 * 
 * This module provides validation schemas for financial-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// Transaction types
const transactionTypes = ['income', 'expense', 'transfer', 'adjustment'];

// Transaction categories
const incomeCategories = [
  'sales', 
  'services', 
  'grants', 
  'subsidies', 
  'insurance', 
  'investment', 
  'other_income'
];

const expenseCategories = [
  'feed', 
  'veterinary', 
  'medication', 
  'supplies', 
  'equipment', 
  'utilities', 
  'labor', 
  'maintenance', 
  'fuel', 
  'taxes', 
  'insurance', 
  'rent', 
  'purchase', 
  'other_expense'
];

// All categories
const categories = [...incomeCategories, ...expenseCategories, 'transfer', 'adjustment'];

// Payment methods
const paymentMethods = [
  'cash', 
  'check', 
  'credit_card', 
  'debit_card', 
  'bank_transfer', 
  'mobile_payment', 
  'other'
];

// Create transaction validation schema
const createTransactionSchema = Joi.object({
  date: Joi.date().iso().required().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),
  type: Joi.string().valid(...transactionTypes).required().messages({
    'any.only': `Type must be one of: ${transactionTypes.join(', ')}`,
    'any.required': 'Type is required'
  }),
  category: Joi.string().valid(...categories).required().messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`,
    'any.required': 'Category is required'
  }),
  amount: Joi.number().required().messages({
    'number.base': 'Amount must be a number',
    'any.required': 'Amount is required'
  }),
  description: Joi.string().trim().required().messages({
    'string.empty': 'Description cannot be empty',
    'any.required': 'Description is required'
  }),
  paymentMethod: Joi.string().valid(...paymentMethods).default('cash').messages({
    'any.only': `Payment method must be one of: ${paymentMethods.join(', ')}`
  }),
  reference: Joi.string().trim().allow('').messages({
    'string.base': 'Reference must be a string'
  }),
  relatedEntityType: Joi.string().trim().valid('animal', 'breeding', 'health', 'inventory', 'other').allow('').messages({
    'any.only': 'Related entity type must be one of: animal, breeding, health, inventory, other'
  }),
  relatedEntityId: Joi.string().trim().allow('').messages({
    'string.base': 'Related entity ID must be a string'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  attachments: Joi.array().items(Joi.string()).default([]).messages({
    'array.base': 'Attachments must be an array'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

// Update transaction validation schema
const updateTransactionSchema = Joi.object({
  date: Joi.date().iso().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)'
  }),
  type: Joi.string().valid(...transactionTypes).messages({
    'any.only': `Type must be one of: ${transactionTypes.join(', ')}`
  }),
  category: Joi.string().valid(...categories).messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`
  }),
  amount: Joi.number().messages({
    'number.base': 'Amount must be a number'
  }),
  description: Joi.string().trim().messages({
    'string.empty': 'Description cannot be empty'
  }),
  paymentMethod: Joi.string().valid(...paymentMethods).messages({
    'any.only': `Payment method must be one of: ${paymentMethods.join(', ')}`
  }),
  reference: Joi.string().trim().allow('').messages({
    'string.base': 'Reference must be a string'
  }),
  relatedEntityType: Joi.string().trim().valid('animal', 'breeding', 'health', 'inventory', 'other').allow('').messages({
    'any.only': 'Related entity type must be one of: animal, breeding, health, inventory, other'
  }),
  relatedEntityId: Joi.string().trim().allow('').messages({
    'string.base': 'Related entity ID must be a string'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  attachments: Joi.array().items(Joi.string()).messages({
    'array.base': 'Attachments must be an array'
  }),
  metadata: Joi.object().messages({
    'object.base': 'Metadata must be an object'
  })
});

// Get transactions query validation schema
const getTransactionsQuerySchema = Joi.object({
  type: Joi.string().valid(...transactionTypes).messages({
    'any.only': `Type must be one of: ${transactionTypes.join(', ')}`
  }),
  category: Joi.string().valid(...categories).messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`
  }),
  startDate: Joi.date().iso().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
  }),
  endDate: Joi.date().iso().messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format (YYYY-MM-DD)'
  }),
  minAmount: Joi.number().messages({
    'number.base': 'Minimum amount must be a number'
  }),
  maxAmount: Joi.number().messages({
    'number.base': 'Maximum amount must be a number'
  }),
  paymentMethod: Joi.string().valid(...paymentMethods).messages({
    'any.only': `Payment method must be one of: ${paymentMethods.join(', ')}`
  }),
  relatedEntityType: Joi.string().trim().valid('animal', 'breeding', 'health', 'inventory', 'other').messages({
    'any.only': 'Related entity type must be one of: animal, breeding, health, inventory, other'
  }),
  relatedEntityId: Joi.string().trim().messages({
    'string.base': 'Related entity ID must be a string'
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  })
}).concat(schemas.pagination);

// Transaction ID parameter validation schema
const transactionIdParamSchema = Joi.object({
  id: schemas.id
});

// Financial summary query validation schema
const financialSummaryQuerySchema = Joi.object({
  startDate: Joi.date().iso().required().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Start date is required'
  }),
  endDate: Joi.date().iso().required().messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'End date is required'
  }),
  groupBy: Joi.string().valid('day', 'week', 'month', 'quarter', 'year', 'category').default('month').messages({
    'any.only': 'Group by must be one of: day, week, month, quarter, year, category'
  })
});

module.exports = {
  createTransactionSchema,
  updateTransactionSchema,
  getTransactionsQuerySchema,
  transactionIdParamSchema,
  financialSummaryQuerySchema,
  transactionTypes,
  incomeCategories,
  expenseCategories,
  categories,
  paymentMethods
};
