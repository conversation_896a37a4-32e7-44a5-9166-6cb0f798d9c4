/**
 * MongoDB Migration Script
 *
 * This script migrates mock data from the backend to MongoDB.
 * It reads mock data files and inserts them into MongoDB collections.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../src/utils/logger');

// Import mock data
let mockAnimals = [];
let mockHealthRecords = [];
let mockVaccinations = [];
let mockBreedingRecords = [];
let mockCalvingRecords = [];
let mockFeedingRecords = [];
let mockFeedInventory = [];
let mockInventory = [];
let mockPastures = [];
let mockExpenses = [];

// Load mock data with error handling
try {
  mockAnimals = require('../src/mocks/animalData').mockAnimals || [];
  logger.info(`Loaded ${mockAnimals.length} animals from mock data`);
} catch (error) {
  logger.error('Error loading animalData:', error);
}

try {
  const healthData = require('../src/mocks/healthData');
  mockHealthRecords = healthData.mockHealthRecords || [];
  mockVaccinations = healthData.mockVaccinations || [];
  logger.info(`Loaded ${mockHealthRecords.length} health records and ${mockVaccinations ? mockVaccinations.length : 0} vaccinations from mock data`);
} catch (error) {
  logger.error('Error loading healthData:', error);
}

try {
  const breedingData = require('../src/mocks/breedingData');
  mockBreedingRecords = breedingData.mockBreedingRecords || [];
  mockCalvingRecords = breedingData.mockCalvingRecords || [];
  logger.info(`Loaded ${mockBreedingRecords.length} breeding records and ${mockCalvingRecords.length} calving records from mock data`);
} catch (error) {
  logger.error('Error loading breedingData:', error);
}

try {
  const feedingData = require('../src/mocks/feedingData');
  mockFeedingRecords = feedingData.mockFeedingRecords || [];
  mockFeedInventory = feedingData.mockFeedInventory || [];
  logger.info(`Loaded ${mockFeedingRecords.length} feeding records and ${mockFeedInventory.length} feed inventory items from mock data`);
} catch (error) {
  logger.error('Error loading feedingData:', error);
}

try {
  mockInventory = require('../src/mocks/inventoryData').mockInventory || [];
  logger.info(`Loaded ${mockInventory.length} inventory items from mock data`);
} catch (error) {
  logger.error('Error loading inventoryData:', error);
}

try {
  mockPastures = require('../src/mocks/pastureMgmtData').mockPastures || [];
  logger.info(`Loaded ${mockPastures.length} pastures from mock data`);
} catch (error) {
  logger.error('Error loading pastureMgmtData:', error);
}

try {
  mockExpenses = require('../src/mocks/financialData').mockExpenses || [];
  logger.info(`Loaded ${mockExpenses.length} expenses from mock data`);
} catch (error) {
  logger.error('Error loading financialData:', error);
}

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  }
});

/**
 * Migrate data to MongoDB
 */
async function migrateToMongoDB() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database
    const db = client.db(dbName);

    // Migrate animals
    if (mockAnimals.length > 0) {
      const animalsCollection = db.collection('animals');

      // Check if collection already has data
      const existingCount = await animalsCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Animals collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert animals
        const result = await animalsCollection.insertMany(mockAnimals);
        logger.info(`${result.insertedCount} animals inserted into MongoDB`);
      }
    }

    // Migrate health records
    if (mockHealthRecords.length > 0) {
      const healthCollection = db.collection('health_records');

      // Check if collection already has data
      const existingCount = await healthCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Health records collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert health records
        const result = await healthCollection.insertMany(mockHealthRecords);
        logger.info(`${result.insertedCount} health records inserted into MongoDB`);
      }
    }

    // Migrate vaccinations
    if (mockVaccinations && mockVaccinations.length > 0) {
      const vaccinationsCollection = db.collection('vaccinations');

      // Check if collection already has data
      const existingCount = await vaccinationsCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Vaccinations collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert vaccinations
        const result = await vaccinationsCollection.insertMany(mockVaccinations);
        logger.info(`${result.insertedCount} vaccinations inserted into MongoDB`);
      }
    }

    // Migrate breeding records
    if (mockBreedingRecords.length > 0) {
      const breedingCollection = db.collection('breeding_records');

      // Check if collection already has data
      const existingCount = await breedingCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Breeding records collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert breeding records
        const result = await breedingCollection.insertMany(mockBreedingRecords);
        logger.info(`${result.insertedCount} breeding records inserted into MongoDB`);
      }
    }

    // Migrate calving records
    if (mockCalvingRecords.length > 0) {
      const calvingCollection = db.collection('calving_records');

      // Check if collection already has data
      const existingCount = await calvingCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Calving records collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert calving records
        const result = await calvingCollection.insertMany(mockCalvingRecords);
        logger.info(`${result.insertedCount} calving records inserted into MongoDB`);
      }
    }

    // Migrate feeding records
    if (mockFeedingRecords.length > 0) {
      const feedingCollection = db.collection('feeding_records');

      // Check if collection already has data
      const existingCount = await feedingCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Feeding records collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert feeding records
        const result = await feedingCollection.insertMany(mockFeedingRecords);
        logger.info(`${result.insertedCount} feeding records inserted into MongoDB`);
      }
    }

    // Migrate feed inventory
    if (mockFeedInventory.length > 0) {
      const feedInventoryCollection = db.collection('feed_inventory');

      // Check if collection already has data
      const existingCount = await feedInventoryCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Feed inventory collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert feed inventory
        const result = await feedInventoryCollection.insertMany(mockFeedInventory);
        logger.info(`${result.insertedCount} feed inventory items inserted into MongoDB`);
      }
    }

    // Migrate inventory
    if (mockInventory.length > 0) {
      const inventoryCollection = db.collection('inventory');

      // Check if collection already has data
      const existingCount = await inventoryCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Inventory collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert inventory
        const result = await inventoryCollection.insertMany(mockInventory);
        logger.info(`${result.insertedCount} inventory items inserted into MongoDB`);
      }
    }

    // Migrate pastures
    if (mockPastures.length > 0) {
      const pasturesCollection = db.collection('pastures');

      // Check if collection already has data
      const existingCount = await pasturesCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Pastures collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert pastures
        const result = await pasturesCollection.insertMany(mockPastures);
        logger.info(`${result.insertedCount} pastures inserted into MongoDB`);
      }
    }

    // Migrate expenses
    if (mockExpenses.length > 0) {
      const expensesCollection = db.collection('expenses');

      // Check if collection already has data
      const existingCount = await expensesCollection.countDocuments();
      if (existingCount > 0) {
        logger.info(`Expenses collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Insert expenses
        const result = await expensesCollection.insertMany(mockExpenses);
        logger.info(`${result.insertedCount} expenses inserted into MongoDB`);
      }
    }

    logger.info('Migration completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
  } finally {
    // Close the MongoDB connection
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the migration
migrateToMongoDB();
