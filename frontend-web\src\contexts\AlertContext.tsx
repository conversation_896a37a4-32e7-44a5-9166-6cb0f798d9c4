import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import AlertSystem from '../components/common/AlertSystem';

interface AlertContextProps {
  showAlert: (options: AlertOptions) => void;
  hideAlert: () => void;
}

export interface AlertOptions {
  message: string;
  severity?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  autoHideDuration?: number;
  position?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'center' | 'right';
  };
}

interface AlertState extends AlertOptions {
  open: boolean;
}

const initialAlertState: AlertState = {
  open: false,
  message: '',
  severity: 'info',
  title: undefined,
  autoHideDuration: 6000,
  position: { vertical: 'bottom', horizontal: 'right' }
};

const AlertContext = createContext<AlertContextProps | undefined>(undefined);

export const useAlert = (): AlertContextProps => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

interface AlertProviderProps {
  children: ReactNode;
}

export const AlertProvider: React.FC<AlertProviderProps> = ({ children }) => {
  const [alertState, setAlertState] = useState<AlertState>(initialAlertState);

  const showAlert = useCallback((options: AlertOptions) => {
    setAlertState({
      ...initialAlertState,
      ...options,
      open: true
    });
  }, []);

  const hideAlert = useCallback(() => {
    setAlertState(prevState => ({
      ...prevState,
      open: false
    }));
  }, []);

  return (
    <AlertContext.Provider value={{ showAlert, hideAlert }}>
      {children}
      <AlertSystem
        open={alertState.open}
        message={alertState.message}
        severity={alertState.severity}
        title={alertState.title}
        autoHideDuration={alertState.autoHideDuration}
        position={alertState.position}
        onClose={hideAlert}
      />
    </AlertContext.Provider>
  );
};

export default AlertContext;
