import { useState, useEffect, useCallback } from 'react';
import performanceMonitor from '../utils/performanceMonitor';

/**
 * Hook for monitoring component performance
 * @param componentName Name of the component to monitor
 * @returns Object containing performance metrics and utility functions
 */
export const usePerformance = (componentName: string) => {
  const [renderCount, setRenderCount] = useState(0);
  const [renderTime, setRenderTime] = useState(0);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Increment render count on each render
  useEffect(() => {
    setRenderCount(prev => prev + 1);
  });

  // Start monitoring performance
  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    performanceMonitor.startMeasure(`${componentName}_render`);
  }, [componentName]);

  // Stop monitoring performance
  const stopMonitoring = useCallback(() => {
    if (isMonitoring) {
      const duration = performanceMonitor.endMeasure(`${componentName}_render`);
      setRenderTime(duration);
      setIsMonitoring(false);
    }
  }, [componentName, isMonitoring]);

  // Measure a specific operation
  const measureOperation = useCallback((operationName: string, callback: () => void) => {
    performanceMonitor.startMeasure(`${componentName}_${operationName}`);
    callback();
    return performanceMonitor.endMeasure(`${componentName}_${operationName}`);
  }, [componentName]);

  // Log component performance metrics
  const logMetrics = useCallback(() => {
    console.group(`Performance Metrics for ${componentName}`);
    console.log('Render Count:', renderCount);
    console.log('Last Render Time:', renderTime.toFixed(2), 'ms');
    console.groupEnd();
  }, [componentName, renderCount, renderTime]);

  // Auto-start monitoring on mount and stop on unmount
  useEffect(() => {
    startMonitoring();
    
    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);

  return {
    renderCount,
    renderTime,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    measureOperation,
    logMetrics
  };
};

export default usePerformance;
