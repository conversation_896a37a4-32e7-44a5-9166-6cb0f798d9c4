/**
 * Comprehensive CSS fixes for tab selection and navigation issues
 * These styles ensure that tabs are properly clickable and functional
 */

/* Make tabs clickable and properly styled */
.MuiTabs-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  background: transparent !important;
}

.MuiTabs-scroller {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

.MuiTabs-flexContainer {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

.MuiTab-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  min-height: 48px !important;
  transition: all 0.2s ease !important;
  text-transform: none !important;
  font-weight: 500 !important;
}

.MuiTab-root:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
  opacity: 1 !important;
}

.MuiTab-root.Mui-selected {
  background-color: rgba(25, 118, 210, 0.08) !important;
  color: #1976d2 !important;
  font-weight: 600 !important;
}

.MuiTab-root.Mui-disabled {
  pointer-events: none !important;
  opacity: 0.5 !important;
}

/* Make tab panels properly visible and clickable */
[role="tabpanel"] {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
  width: 100% !important;
}

/* Fix tab indicator */
.MuiTabs-indicator {
  z-index: 11 !important;
  height: 3px !important;
  border-radius: 3px 3px 0 0 !important;
}

/* Ensure tab content is properly displayed */
.MuiTabPanel-root {
  padding: 24px !important;
}

/* Fix for hidden/visible tab content */
[role="tabpanel"][hidden] {
  display: none !important;
}

[role="tabpanel"]:not([hidden]) {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for tab content containers */
.tab-content-container {
  position: relative !important;
  z-index: 1 !important;
  pointer-events: auto !important;
}

/* Ensure buttons within tabs are clickable */
.MuiTab-root button,
[role="tabpanel"] button {
  pointer-events: auto !important;
  z-index: 12 !important;
  position: relative !important;
}

/* Fix for module tabs specifically */
.module-tabs .MuiTab-root {
  min-width: 120px !important;
  padding: 12px 16px !important;
}

/* Fix for dashboard tabs */
.dashboard-tabs .MuiTab-root {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

/* Ensure tab content is not blocked by overlays */
.MuiTabPanel-root * {
  pointer-events: auto !important;
}

/* Fix for responsive tabs */
@media (max-width: 768px) {
  .MuiTab-root {
    min-width: 80px !important;
    padding: 8px 12px !important;
    font-size: 0.75rem !important;
  }
}

/* Make tab content clickable */
.MuiTabPanel-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Ensure buttons in tabs are clickable */
.MuiTabPanel-root .MuiButton-root,
.MuiTabPanel-root .MuiIconButton-root,
.MuiTabs-root .MuiButton-root,
.MuiTabs-root .MuiIconButton-root,
[role="tabpanel"] .MuiButton-root,
[role="tabpanel"] .MuiIconButton-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Ensure cards in tabs are clickable */
.MuiTabPanel-root .MuiCard-root,
.MuiTabPanel-root .MuiCardContent-root,
.MuiTabs-root .MuiCard-root,
.MuiTabs-root .MuiCardContent-root,
[role="tabpanel"] .MuiCard-root,
[role="tabpanel"] .MuiCardContent-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Fix for specific theme settings tabs */
#theme-tab-0, #theme-tab-1, #theme-tab-2 {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

#theme-tabpanel-0, #theme-tabpanel-1, #theme-tabpanel-2 {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Fix for any motion divs that might be blocking clicks */
.motion-div {
  pointer-events: none !important;
}

.motion-div * {
  pointer-events: auto !important;
}

/* Ensure all interactive elements are clickable */
button, a, input, select, textarea, [role="button"] {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Fix for main navigation tabs in the header */
header .MuiTabs-root,
header [role="tab"],
[role="tab"] {
  position: relative !important;
  z-index: 1300 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Fix for main app bar */
.MuiAppBar-root {
  z-index: 1200 !important;
  pointer-events: auto !important;
}

/* Fix for toolbar */
.MuiToolbar-root {
  position: relative !important;
  z-index: 1200 !important;
  pointer-events: auto !important;
}

/* Fix for overlays that might be blocking clicks */
.MuiBox-root::before {
  pointer-events: none !important;
}

/* Fix for tab content */
[id^="tab-content-"] {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Fix for any overlay issues */
.MuiBackdrop-root:not(.MuiBackdrop-open),
.MuiModal-backdrop:not(.MuiBackdrop-open) {
  z-index: 1 !important;
  pointer-events: none !important;
  display: none !important;
}

/* Remove white blur overlays completely */
.MuiBackdrop-root {
  background-color: transparent !important;
}

/* Ensure no white overlays persist */
[style*="rgba(255, 255, 255"]:not(.MuiCard-root):not(.MuiPaper-root) {
  background: transparent !important;
}

/* Fix for persistent modal overlays */
.MuiModal-root .MuiBackdrop-root {
  background-color: rgba(0, 0, 0, 0.5) !important;
}

/* Remove any white blur from body */
body {
  filter: none !important;
  -webkit-backdrop-filter: none !important;
  backdrop-filter: none !important;
}
