/**
 * Hook to apply tab selection fixes to components
 */

import { useEffect, useRef } from 'react';
import { applyTabSelectionFixesToElement } from '../utils/applyTabSelectionFixes';

/**
 * Hook to ensure proper tab selection behavior
 * @returns A ref to attach to the component
 */
export const useTabSelection = () => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current) {
      // Apply tab selection fixes to the component
      applyTabSelectionFixesToElement(ref.current);
    }
  }, []);

  return ref;
};

export default useTabSelection;
