{"common": {"loading": "Laai...", "save": "Stoor", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Verwyder", "edit": "<PERSON><PERSON><PERSON>", "add": "Voeg by", "new": "<PERSON><PERSON><PERSON>", "view": "Bekyk", "search": "<PERSON><PERSON>", "filter": "Filter", "export": "Uit<PERSON><PERSON>", "import": "Invoer", "print": "<PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "close": "Sluit", "submit": "Dien in", "confirm": "Bevestig", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "ok": "OK", "error": "Fout", "success": "<PERSON><PERSON><PERSON>", "warning": "Waarskuwing", "info": "Inligting"}, "auth": {"login": "<PERSON>d aan", "logout": "Meld af", "register": "Registreer", "username": "Gebruikersnaam", "password": "Wagwoord", "email": "E-pos", "firstName": "<PERSON><PERSON><PERSON><PERSON>", "lastName": "<PERSON>", "confirmPassword": "<PERSON><PERSON><PERSON><PERSON> wagwoord", "forgotPassword": "Wagwoord vergeet?", "rememberMe": "Onthou my", "loginSuccess": "Suksesvol aangemeld", "loginError": "Aanmeldfout", "logoutSuccess": "Suksesvol afgemeld", "registerSuccess": "Registrasie suksesvol", "registerError": "Registrasiefout"}, "nav": {"home": "<PERSON><PERSON>", "dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animals": "<PERSON><PERSON>", "health": "Gesondheid", "breeding": "Teling", "feeding": "Voeding", "financial": "Finansies", "inventory": "<PERSON><PERSON><PERSON><PERSON>", "commercial": "Kommersieel", "reports": "<PERSON><PERSON><PERSON><PERSON>", "resources": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "compliance": "Nakoming", "settings": "Instellings", "analytics": "<PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON>"}, "animals": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAnimal": "<PERSON><PERSON>g dier by", "editAnimal": "<PERSON><PERSON><PERSON> dier", "deleteAnimal": "<PERSON>er<PERSON><PERSON> dier", "animalDetails": "Dierbesonderhede", "tagNumber": "Merknommer", "name": "<PERSON><PERSON>", "species": "Spesie", "breed": "<PERSON><PERSON>", "gender": "Geslag", "birthDate": "Geboortedatum", "weight": "<PERSON><PERSON><PERSON>", "status": "Status", "location": "Ligging", "notes": "Notas", "cattle": "Beeste", "sheep": "<PERSON><PERSON><PERSON>", "goat": "Bokke", "pig": "<PERSON><PERSON><PERSON>", "chicken": "Ho<PERSON>s", "male": "Manlik", "female": "Vroulik", "active": "Aktief", "sold": "Verkoop", "deceased": "<PERSON><PERSON><PERSON><PERSON>"}, "health": {"title": "G<PERSON>dh<PERSON>dsbeheer", "addRecord": "<PERSON><PERSON>g rekord by", "editRecord": "<PERSON><PERSON><PERSON> rekord", "deleteRecord": "Verwyder rekord", "healthRecord": "Gesondheidsrekord", "recordType": "Rekordtipe", "date": "Datum", "treatment": "Behandeling", "veterinarian": "<PERSON><PERSON><PERSON><PERSON>", "cost": "<PERSON><PERSON>", "nextDueDate": "Volgende vervaldatum", "vaccination": "Inenting", "checkup": "Ondersoek", "treatment_type": "Behandeling", "injury": "Besering", "illness": "Siekte", "healthy": "<PERSON><PERSON><PERSON>", "sick": "<PERSON><PERSON>", "injured": "<PERSON>seer", "recovering": "<PERSON><PERSON><PERSON>"}, "breeding": {"title": "Telingsbeheer", "addRecord": "<PERSON>oeg telingrek<PERSON> by", "editRecord": "<PERSON><PERSON><PERSON> telingrekord", "breedingRecord": "Telingrekord", "femaleId": "Vroulike ID", "maleId": "Manlike ID", "breedingDate": "Telingsdatum", "expectedCalvingDate": "Verwagte kalfdatum", "actualCalvingDate": "Werklike kalfdatum", "method": "Metode", "status": "Status", "natural": "Natuurlik", "artificialInsemination": "Kunsmatige bevrugting", "bred": "Get<PERSON>", "pregnant": "<PERSON><PERSON><PERSON><PERSON>", "calved": "<PERSON><PERSON><PERSON>", "failed": "Misluk"}, "financial": {"title": "Finansiële bestuur", "addTransaction": "<PERSON><PERSON><PERSON><PERSON> by", "editTransaction": "<PERSON><PERSON><PERSON>", "transaction": "Transaksie", "type": "Tipe", "category": "<PERSON><PERSON><PERSON>", "amount": "Bedrag", "date": "Datum", "description": "Beskrywing", "reference": "Verwysing", "paymentMethod": "Betaalmetode", "income": "Inkomste", "expense": "Uitgawe", "cash": "<PERSON><PERSON><PERSON>", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "card": "<PERSON><PERSON>", "animalSales": "Dierverkope", "milkSales": "Melkverkope", "feedPurchase": "<PERSON><PERSON><PERSON><PERSON>", "veterinaryBills": "<PERSON><PERSON>artsko<PERSON><PERSON>"}, "inventory": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addItem": "Voeg item by", "editItem": "Wysig item", "item": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON><PERSON>", "currentStock": "<PERSON><PERSON><PERSON> v<PERSON>", "minimumStock": "Minimum voorraad", "maximumStock": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "costPerUnit": "Koste per eenheid", "supplier": "<PERSON><PERSON><PERSON>ffer", "lastRestocked": "<PERSON><PERSON>", "feed": "<PERSON><PERSON><PERSON>", "medical": "Mediese", "equipment": "<PERSON><PERSON><PERSON>", "lowStock": "<PERSON><PERSON> v<PERSON><PERSON>", "outOfStock": "<PERSON><PERSON> v<PERSON>ad"}, "reports": {"title": "<PERSON><PERSON><PERSON><PERSON>", "generateReport": "<PERSON><PERSON> ve<PERSON>", "animalReport": "Dierverslag", "healthReport": "Gesondheidsverslag", "breedingReport": "Telingsverslag", "financialReport": "Finansiële verslag", "inventoryReport": "Voorraadverslag", "dateRange": "Datumreeks", "startDate": "Begindatum", "endDate": "Einddatum", "exportPdf": "Uitvoer na PDF", "exportExcel": "Uitvoer na Excel"}, "settings": {"title": "Instellings", "general": "<PERSON><PERSON><PERSON><PERSON>", "language": "Taal", "theme": "<PERSON><PERSON>", "notifications": "Kennisgewings", "backup": "<PERSON><PERSON><PERSON><PERSON>", "users": "Gebruikers", "database": "Databasis", "selectLanguage": "<PERSON><PERSON> taal", "selectTheme": "<PERSON><PERSON> tema", "light": "<PERSON>g", "dark": "<PERSON><PERSON>", "auto": "Outomaties"}, "dashboard": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcome": "Welkom by AgriIntel", "overview": "<PERSON><PERSON><PERSON>", "totalAnimals": "Totale diere", "healthyAnimals": "Gesonde diere", "sickAnimals": "<PERSON><PERSON> diere", "recentActivities": "Onlangse aktiwiteite", "upcomingTasks": "<PERSON><PERSON><PERSON> take", "quickActions": "Vinnige aksies", "statistics": "Statistieke", "alerts": "Waarskuwings"}, "beta": {"title": "Beta Weergawe", "welcome": "Welkom by AgriIntel Beta", "description": "<PERSON><PERSON><PERSON> die toekoms van veebeheer", "features": "<PERSON><PERSON><PERSON>", "limitations": "<PERSON><PERSON><PERSON><PERSON>", "upgrade": "Opgradeer", "trialDaysLeft": "<PERSON>ef dae oor", "maxAnimals": "<PERSON><PERSON><PERSON><PERSON> diere", "basicFeatures": "Basiese kenmerke", "premiumFeatures": "Premium kenmerke", "locked": "Gesluit", "upgradeRequired": "Opgradering vereis"}, "subscription": {"title": "Inskrywing", "plans": "Planne", "current": "<PERSON><PERSON><PERSON>", "upgrade": "Opgradeer", "downgrade": "Degradeer", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "betaAccess": "Beta Toegang", "professional": "Professioneel", "enterprise": "Onderneming", "monthly": "<PERSON><PERSON><PERSON><PERSON>", "annually": "Jaarliks", "features": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "free": "<PERSON><PERSON><PERSON>", "perMonth": "per maand", "perYear": "per jaar"}, "errors": {"general": "Iets het verkeerd geloop", "network": "Netwerkfout", "notFound": "Nie gevind nie", "unauthorized": "Ongemagtig", "forbidden": "Verbode", "serverError": "Bed<PERSON>er fout", "validationError": "Validasiefout", "requiredField": "Hierdie veld is verplig", "invalidEmail": "Ongeldige e-posadres", "passwordTooShort": "Wagwoord te kort", "passwordsDoNotMatch": "Wagwoorde stem nie ooreen nie"}, "notifications": {"title": "Kennisgewings", "markAsRead": "<PERSON><PERSON> as gelees", "markAllAsRead": "<PERSON><PERSON> alles as gelees", "noNotifications": "<PERSON>n kennisgewings", "healthCheckDue": "Gesondheidsondersoek verskuldig", "vaccinationDue": "In<PERSON><PERSON> versk<PERSON>ig", "breedingScheduled": "Teling gesked<PERSON>er", "lowStock": "<PERSON><PERSON> v<PERSON><PERSON>", "newBirth": "<PERSON><PERSON><PERSON>"}}