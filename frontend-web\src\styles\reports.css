/* Enhanced Report Styles */
.report-container {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px;
  background-color: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.report-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
}

.report-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  color: #2c3e50;
  letter-spacing: -0.5px;
}

.report-subtitle {
  font-size: 20px;
  color: #3498db;
  margin: 10px 0;
  font-weight: 500;
}

.report-date {
  font-size: 14px;
  color: #7f8c8d;
  font-style: italic;
}

.report-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 22px;
  color: #2980b9;
  border-bottom: 3px solid #3498db;
  padding-bottom: 10px;
  margin-bottom: 20px;
  font-weight: 600;
}

.summary-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  min-width: 220px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 16px;
  color: #7f8c8d;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

th, td {
  padding: 15px 18px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

th {
  background-color: #f2f8fd;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: #f5f9ff;
}

.chart-container {
  margin-bottom: 40px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 25px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 5px 10px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.legend-color {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 8px;
  border-radius: 4px;
}

.legend-label {
  color: #333;
  font-weight: 500;
}

.footer {
  margin-top: 60px;
  text-align: center;
  font-size: 13px;
  color: #95a5a6;
  padding-top: 30px;
  border-top: 1px solid #eee;
}

/* SVG Chart Styles */
svg {
  max-width: 100%;
  height: auto;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

svg text {
  font-family: 'Roboto', Arial, sans-serif;
}

svg .chart-title {
  font-size: 18px;
  font-weight: 600;
}

svg .axis-label {
  font-size: 13px;
}

svg .grid-line {
  stroke: #e0e0e0;
  stroke-dasharray: 3,3;
}

svg .data-point {
  stroke-width: 2;
}

svg .data-label {
  font-size: 12px;
  text-anchor: middle;
  font-weight: 500;
}

/* Print styles */
@media print {
  body {
    padding: 0;
    font-size: 12pt;
    background-color: white;
  }
  
  .report-container {
    box-shadow: none;
    padding: 0;
    max-width: 100%;
  }
  
  .chart-container {
    break-inside: avoid;
    page-break-inside: avoid;
    box-shadow: none;
    border: 1px solid #eee;
  }
  
  table {
    break-inside: auto;
    page-break-inside: auto;
    box-shadow: none;
    border: 1px solid #eee;
  }
  
  tr {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .no-print {
    display: none;
  }
  
  .stat-card {
    box-shadow: none;
    border: 1px solid #eee;
  }
  
  svg {
    box-shadow: none;
  }
}

/* Financial Report Specific Styles */
.financial-highlight {
  color: #2980b9;
  font-weight: 600;
}

.financial-positive {
  color: #27ae60;
  font-weight: 600;
}

.financial-negative {
  color: #e74c3c;
  font-weight: 600;
}

.financial-summary-box {
  background-color: #f8f9fa;
  border-left: 4px solid #3498db;
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 0 8px 8px 0;
}

.financial-summary-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 15px;
}

.financial-summary-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.financial-summary-item {
  flex: 1;
  min-width: 200px;
}

.financial-summary-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.financial-summary-value {
  font-size: 22px;
  font-weight: 700;
  color: #2c3e50;
}

.financial-trend-indicator {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  margin-left: 10px;
}

.financial-trend-up {
  color: #27ae60;
}

.financial-trend-down {
  color: #e74c3c;
}
