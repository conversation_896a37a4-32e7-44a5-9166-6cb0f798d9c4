{"login.welcome": "Re a o Amohela Ha<PERSON>", "login.subtitle": "<PERSON><PERSON> a<PERSON> ya hao ya MayCaiphus Livestock", "login.username": "Le<PERSON><PERSON> la mosebedisi", "login.password": "<PERSON><PERSON><PERSON><PERSON>", "login.signin": "KENA", "login.demo_credentials": "<PERSON><PERSON><PERSON><PERSON> ba demo: le<PERSON><PERSON> <PERSON> mose<PERSON>: <PERSON><PERSON><PERSON><PERSON>: 123", "login.enter_username": "Kenya lebitso la hao la mosebedisi", "login.enter_password": "Kenya lekunutu la hao", "login.error.credentials": "Lebitso la mosebedisi kapa lekunutu le fosahetseng", "login.error.empty_fields": "Ka kopo kenya lebitso la mosebedisi le lekunutu", "login.language_selection": "<PERSON><PERSON><PERSON>", "login.forgot_password": "Ke lebetse lekunutu?", "dashboard.title": "Dashboard", "dashboard.welcome": "O amohelehile ho AgriIntel", "dashboard.subtitle": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> tse <PERSON> - Tharollo ya hao e felletseng bakeng sa taolo e bohlale ya leruo", "dashboard.preparing": "Re lokisa dashboard ya hao...", "dashboard.modules": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.sponsors": "<PERSON><PERSON><PERSON><PERSON> ba <PERSON>a", "dashboard.system_overview": "<PERSON><PERSON><PERSON>o ya Sistimi", "dashboard.kpi_description": "<PERSON><PERSON><PERSON> tsa tshebetso ya bohlokwa le boemo ba sistimi", "dashboard.health_trends": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tsa <PERSON> bo <PERSON> ba Leruo", "dashboard.weekly_health": "<PERSON><PERSON><PERSON>o ya boemo ba bophelo bo botle ba beke", "dashboard.financial_performance": "<PERSON><PERSON><PERSON><PERSON> ya Ditjhelete", "dashboard.monthly_revenue": "<PERSON><PERSON><PERSON> le ditshen<PERSON>elo tsa <PERSON>wedi", "dashboard.animals_attention": "{{count}} di hloka tlhokomelo", "dashboard.pending_checkups": "{{count}} di<PERSON><PERSON><PERSON><PERSON><PERSON> tse emeng", "dashboard.due_dates": "{{count}} mat<PERSON><PERSON> a ho fela a haufi", "dashboard.low_stock": "{{count}} ditemoso tsa thepa e tlase", "dashboard.revenue_month": "Lekeno kgweding ena", "dashboard.new_insights": "{{count}} di<PERSON><PERSON><PERSON><PERSON> tse ntjha", "dashboard.analytics": "Diana<PERSON><PERSON>", "dashboard.sponsor": "Motshehedi {{number}}", "dashboard.sponsor_alt": "Motshehedi {{number}}", "dashboard.stat.total_animals": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.stat.active_alerts": "<PERSON><PERSON><PERSON> tse <PERSON>", "dashboard.stat.pending_tasks": "Mesebetsi e Emeng", "dashboard.stat.system_health": "<PERSON><PERSON><PERSON> bo <PERSON><PERSON> ba Sist<PERSON><PERSON>", "dashboard.stat.total_revenue": "<PERSON><PERSON><PERSON>", "dashboard.stat.feed_stock": "<PERSON><PERSON> ya Di<PERSON>", "common.loading": "E ntse e jarisa...", "common.error": "Ho etsahetse phoso", "common.success": "<PERSON>e", "common.save": "Boloka", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "<PERSON><PERSON><PERSON>", "common.edit": "<PERSON><PERSON><PERSON>", "common.add": "Kenya", "common.search": "<PERSON><PERSON>", "common.filter": "Sefa", "common.sort": "<PERSON><PERSON><PERSON><PERSON>", "common.view": "<PERSON><PERSON>", "common.download": "Jaro<PERSON>", "common.export": "<PERSON><PERSON> ka<PERSON>", "common.import": "Kenya kahare", "common.print": "Print<PERSON>", "common.close": "Kwala", "common.confirm": "Netefatsa", "common.back": "<PERSON><PERSON><PERSON>", "common.next": "E latelang", "common.previous": "E fetileng", "common.submit": "<PERSON><PERSON>", "common.reset": "<PERSON>a botjha", "common.refresh": "Ntjhafatsa", "common.help": "Thuso", "common.active": "<PERSON> se<PERSON>", "common.new": "E ntjha", "common.more": "Hape", "common.clear_all": "<PERSON><PERSON><PERSON>", "common.filters": "<PERSON><PERSON><PERSON>", "nav.dashboard": "Dashboard", "nav.animals": "<PERSON><PERSON><PERSON><PERSON>", "nav.breeding": "<PERSON> tswala", "nav.health": "<PERSON><PERSON><PERSON> bo botle", "nav.feeding": "Ho fepa", "nav.financial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nav.reports": "Ditlale<PERSON>", "nav.settings": "Disetting", "nav.logout": "Tswa", "nav.profile": "<PERSON><PERSON><PERSON>", "nav.commercial": "Kgwebo", "nav.resources": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nav.compliance": "Boikamahany<PERSON>", "sidebar.dashboard": "Dashboard", "sidebar.animals": "<PERSON><PERSON><PERSON><PERSON>", "sidebar.breeding": "<PERSON> tswala", "sidebar.health": "<PERSON><PERSON><PERSON> bo botle", "sidebar.feeding": "Ho fepa", "sidebar.financial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebar.reports": "Ditlale<PERSON>", "sidebar.settings": "Disetting", "sidebar.logout": "Tswa", "animals.dashboard": "Dashboard ya <PERSON>phoofolo", "animals.manage": "<PERSON><PERSON> tsa hao tsa diphoofolo le leruo", "animals.add": "Kenya Phoofolo", "animals.total": "<PERSON><PERSON><PERSON><PERSON>", "animals.active": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>", "animals.healthy": "Sekgahla sa <PERSON> bo Botle", "animals.animals": "diphoofolo", "animals.inventory.title": "Inventari ya Leruo", "animals.inventory.subtitle": "<PERSON><PERSON> le ho hlokomela inventari ya hao ya diphoofolo ka disebediswa tse felletseng", "animals.search_placeholder": "Batla diphoofolo...", "animals.no_animals_found": "Ha ho diphoofolo tse fumanweng tse tsamaelanang le ditlhoko tsa hao", "animals.location": "Sebaka", "animals.weight": "<PERSON><PERSON>", "animals.age": "<PERSON><PERSON><PERSON>", "animals.years": "<PERSON><PERSON><PERSON>", "animals.not_specified": "<PERSON> e a hlahiswa", "animals.not_recorded": "Ha e a rekotiwa", "animals.unknown": "Ha e tsejwe", "animals.filter.all": "<PERSON><PERSON><PERSON><PERSON>", "animals.filter.healthy": "<PERSON><PERSON>", "animals.filter.attention": "Di Hloka Tlhokomelo", "animals.filter.critical": "<PERSON><PERSON>", "animals.filter.pregnant": "<PERSON><PERSON>", "animals.species.cattle": "Dikgomo", "animals.species.sheep": "<PERSON><PERSON>", "animals.species.goat": "<PERSON><PERSON><PERSON>", "animals.species.horse": "<PERSON><PERSON><PERSON>", "animals.species.game": "<PERSON><PERSON><PERSON><PERSON> tsa <PERSON>", "animals.species_distribution": "<PERSON><PERSON><PERSON><PERSON> ka <PERSON>", "animals.species_distribution_desc": "Kabo ya diphoofolo ka mofuta wa tsona", "animals.species_distribution_help": "<PERSON><PERSON><PERSON> karolo ho bona dintlha ka mofuta oo", "animals.health_distribution": "<PERSON>bo ya <PERSON>em<PERSON> ba <PERSON><PERSON><PERSON> bo <PERSON><PERSON>", "animals.health_distribution_desc": "Kabo ya diphoofolo ho ya ka boemo ba bophelo bo botle", "animals.health_distribution_help": "E bontsha dipalo tsa diphoofolo tse phelang hantle, tse kulang, tse le<PERSON>, le tse immeng", "animals.location_distribution": "<PERSON><PERSON><PERSON><PERSON> ka Sebaka", "animals.location_distribution_desc": "Kabo ya diphoofolo ho ya ka sebaka", "animals.location_distribution_help": "E bontsha moo diphoofolo tsa hao di leng teng", "animals.growth_chart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tsa <PERSON>", "animals.growth_chart_desc": "Tswelliso ya boima le botelele ka kgwedi", "animals.growth_chart_help": "<PERSON><PERSON> kgolo ya diphoofolo nakong", "animals.asset_overview": "<PERSON><PERSON><PERSON><PERSON> ya Tsamaiso ya Thepa", "animals.asset_overview_desc": "Ka<PERSON>etso ya ditjhelete ya thepa ya hao ya diphoofolo", "animals.total_asset_value": "<PERSON><PERSON><PERSON> ba <PERSON><PERSON>", "animals.average_roi": "ROI e Mahareng", "animals.active_assets": "Thepa e Sebetsang", "animals.retired_assets": "<PERSON><PERSON> e <PERSON>", "animals.asset_status": "<PERSON><PERSON><PERSON> ba <PERSON><PERSON>", "animals.retired": "<PERSON> <PERSON><PERSON><PERSON>", "animals.near_retirement": "<PERSON><PERSON><PERSON> le ho <PERSON> Me<PERSON>", "animals.view_details": "<PERSON><PERSON>", "animals.retirement_reasons": "<PERSON><PERSON><PERSON> a ho Bewa Meja Fatshe", "animals.retirement_reasons_desc": "<PERSON><PERSON><PERSON> a ho bewa meja fatshe ha diphoofolo", "animals.retirement_reasons_help": "E bontsha kabo ya mabaka a ho bewa meja fatshe", "animals.registry": "Rejistara ya Diphoofolo", "animals.registry_desc": "<PERSON><PERSON> diphoofolo tsa hao tse ngodisitsweng", "animals.featured": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>", "animals.view_all": "<PERSON><PERSON>", "chart.no_data": "Ha ho data e fumanehang", "chart.change_type": "<PERSON><PERSON><PERSON> mofuta wa tjhate", "chart.type.bar": "<PERSON><PERSON><PERSON><PERSON> ya Dibara", "chart.type.line": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>", "chart.type.area": "T<PERSON>hate ya Sebaka", "chart.type.pie": "<PERSON><PERSON><PERSON><PERSON> ya <PERSON>i", "chart.type.radar": "<PERSON><PERSON><PERSON><PERSON> ya Radar", "chart.type.scatter": "<PERSON><PERSON><PERSON><PERSON> ya Phatlalatso", "chart.time_range.day": "Kajeno", "chart.time_range.week": "<PERSON><PERSON>", "chart.time_range.month": "Kgwedi Ena", "chart.time_range.quarter": "<PERSON><PERSON>", "chart.time_range.year": "<PERSON><PERSON><PERSON>", "chart.time_range.all": "<PERSON><PERSON>", "table.no_data": "Ha ho data e fumanehang", "table.selected": "e kgethilweng", "table.actions": "<PERSON><PERSON><PERSON>", "table.columns": "Di<PERSON><PERSON><PERSON>", "table.rows_per_page": "<PERSON>a ka leqephe", "table.of": "ya", "dashboard.welcome.title": "O amohelehile ho MayCaiphus Livestock", "dashboard.welcome.subtitle": "<PERSON><PERSON><PERSON>o ya hao e felletseng bakeng sa taolo ya leruo. <PERSON>lokomela diphoofolo tsa hao, latela direkoto tsa bophelo bo botle, la<PERSON> ho tswala, le tse ding.", "dashboard.welcome.add_animal": "Kenya Phoofolo e Ntjha", "dashboard.welcome.view_reports": "<PERSON><PERSON>", "dashboard.overview.title": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.overview.total_animals": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.overview.pending_tasks": "Mesebetsi e Emeng", "dashboard.overview.health_alerts": "Ditemoso tsa <PERSON> bo <PERSON>", "dashboard.overview.recent_births": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>", "dashboard.modules.title": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.activity.title": "<PERSON><PERSON><PERSON> t<PERSON>", "dashboard.animals.title": "<PERSON>lo ya Diphoofolo", "dashboard.animals.description": "<PERSON><PERSON> inventari ya hao ya leruo", "dashboard.animals.stats.total": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.animals.stats.active": "<PERSON><PERSON>", "dashboard.animals.submodules.profiles": "Diprofa<PERSON>", "dashboard.animals.submodules.health": "<PERSON><PERSON><PERSON> tsa <PERSON> bo <PERSON><PERSON>", "dashboard.animals.submodules.growth": "<PERSON>", "dashboard.status.needs_attention": "E hloka tlhokomelo", "dashboard.status.updated_today": "E ntjhafaditswe kajeno", "dashboard.status.low_stock": "Thepa e tlase", "dashboard.breeding.title": "<PERSON>lo ya ho <PERSON>wala", "dashboard.breeding.description": "<PERSON><PERSON> di<PERSON>o tsa ho tswala le ho ima", "dashboard.breeding.stats.pregnant": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>ng", "dashboard.breeding.stats.births": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>", "dashboard.breeding.submodules.records": "<PERSON><PERSON><PERSON> tsa ho <PERSON>", "dashboard.breeding.submodules.heat": "<PERSON><PERSON><PERSON><PERSON> ya Phi<PERSON>", "dashboard.breeding.submodules.pregnancy": "<PERSON> ho <PERSON>", "dashboard.health.title": "Taolo ya Bophelo bo <PERSON><PERSON>", "dashboard.health.description": "<PERSON><PERSON><PERSON><PERSON><PERSON> bophelo bo botle ba diphoofolo le dikalafo", "dashboard.health.stats.alerts": "Ditemoso tsa <PERSON> bo <PERSON>", "dashboard.health.stats.vaccinations": "<PERSON><PERSON> tse <PERSON>", "dashboard.health.submodules.records": "<PERSON><PERSON><PERSON> tsa <PERSON> bo <PERSON><PERSON>", "dashboard.health.submodules.vaccinations": "<PERSON><PERSON><PERSON> ya <PERSON>", "dashboard.health.submodules.treatments": "Merero ya Kalafo", "dashboard.feeding.title": "<PERSON><PERSON> Dijo", "dashboard.feeding.description": "Laola inventari ya dijo le dishejule", "dashboard.feeding.stats.schedules": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>", "dashboard.feeding.stats.low_stock": "Dintho tsa Thepa e Tlase", "dashboard.feeding.submodules.schedules": "<PERSON><PERSON><PERSON><PERSON> tsa <PERSON>", "dashboard.feeding.submodules.inventory": "Inventari ya Dijo", "dashboard.feeding.submodules.nutrition": "T<PERSON><PERSON>sheko ya Phepo", "dashboard.commercial.title": "Kgwebo", "dashboard.commercial.description": "<PERSON><PERSON> tsa mmaraka le dithekiso", "dashboard.commercial.stats.orders": "<PERSON><PERSON><PERSON> t<PERSON>", "dashboard.commercial.stats.revenue": "Lekeno", "dashboard.commercial.submodules.orders": "<PERSON><PERSON><PERSON>", "dashboard.commercial.submodules.marketplace": "Mmaraka", "dashboard.commercial.submodules.suppliers": "<PERSON><PERSON><PERSON>", "dashboard.financial.title": "Taolo ya Ditjhelete", "dashboard.financial.description": "<PERSON><PERSON> le le<PERSON>", "dashboard.financial.stats.revenue": "Lekeno la Kgwedi", "dashboard.financial.stats.expenses": "<PERSON><PERSON><PERSON><PERSON><PERSON> tsa <PERSON>", "dashboard.financial.submodules.overview": "Ka<PERSON>etso ya Ditjhelete", "dashboard.financial.submodules.transactions": "Ditransekshene", "dashboard.financial.submodules.reports": "Ditlale<PERSON>", "dashboard.reports.title": "Ditlaleho & Dianalitiki", "dashboard.reports.description": "<PERSON><PERSON><PERSON> le ditlaleho", "dashboard.reports.stats.saved": "<PERSON><PERSON><PERSON><PERSON> tse Bo<PERSON>lweng", "dashboard.reports.stats.metrics": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>", "dashboard.reports.submodules.analysis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboard.reports.submodules.performance": "<PERSON><PERSON><PERSON><PERSON>", "dashboard.reports.submodules.financial": "<PERSON><PERSON><PERSON><PERSON> tsa <PERSON>", "dashboard.reports.submodules.health": "Dipalop<PERSON> tsa <PERSON> bo <PERSON>", "dashboard.reports.submodules.market": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya Mmaraka", "dashboard.reports.submodules.custom": "<PERSON><PERSON><PERSON><PERSON> tse <PERSON>", "dashboard.compliance.title": "Boikamahany<PERSON>", "dashboard.compliance.description": "<PERSON><PERSON> boi<PERSON>o ba molao", "dashboard.compliance.stats.documents": "Ditokomane", "dashboard.compliance.stats.certifications": "Disetifikeiti", "dashboard.compliance.submodules.certifications": "Disetifikeiti", "dashboard.compliance.submodules.inspections": "Ditlhahlobo", "dashboard.compliance.submodules.documentation": "Ditokomane", "dashboard.settings.title": "Disetting", "dashboard.settings.description": "<PERSON><PERSON> disetting tsa sistimi", "dashboard.settings.stats.users": "Diak<PERSON>nte tsa <PERSON>", "dashboard.settings.stats.version": "Phetolelo ya Sistimi", "dashboard.settings.submodules.users": "<PERSON><PERSON>", "dashboard.settings.submodules.theme": "Disetting tsa Theme", "dashboard.settings.submodules.database": "Disetting tsa Database", "dashboard.settings.submodules.backup": "Backup & Restore", "financial.dashboard": "Dashboto ea Lichelete", "financial.dashboard.title": "Dashboto ea Lichelete", "financial.dashboard.subtitle": "<PERSON><PERSON> ea liche<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, le litekanyetso tsa phaello", "financial.dashboard.action": "Kenya Phetiso", "financial.manage": "<PERSON><PERSON> lire<PERSON> tsa lichelete le likhoebo", "financial.add": "Kenya Rekoto ea Lichelete", "financial.financial_management": "Tsamaiso ea Lichelete", "financial.add_transaction": "Kenya Phetiso", "financial.generate_reports": "<PERSON><PERSON><PERSON>", "financial.total_revenue": "Litseno <PERSON>", "financial.total_expenses": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "financial.net_profit": "Phaello e Hloekileng", "financial.cash_on_hand": "Chelete e Teng", "financial.expenses_by_category": "Litšenyehelo ka <PERSON>", "financial.expenses_by_category_desc": "Kabo ea lit<PERSON>elo ka mefuta", "financial.revenue_by_source": "Litseno ka Mohloli", "financial.revenue_by_source_desc": "Kabo ea litseno ka mohloli", "financial.monthly_financial_performance": "<PERSON><PERSON><PERSON><PERSON> ea Lichelete ea <PERSON>", "financial.monthly_financial_performance_desc": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, le pha<PERSON> ka nako", "financial.transactions": "Li<PERSON>tis<PERSON>", "financial.recent_transactions": "<PERSON><PERSON><PERSON><PERSON>", "financial.recent_transactions_desc": "<PERSON><PERSON> le ho laola liphetiso tsa hau tsa lichelete tsa haufinyane", "financial.roi": "Sebala sa ROI", "financial.forecast": "<PERSON> lepa", "financial.financial_forecast": "Ho lepa Lichelete", "financial.financial_forecast_desc": "<PERSON>ts<PERSON> le litšenyehelo tse lebelletsoeng bakeng sa likhoeli tse 6 tse tlang", "financial.invoices": "Ho etsa li-invoice", "financial.budget_vs_actual": "<PERSON><PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON>", "financial.budget_vs_actual_desc": "Bapisa chelete e abetsoeng le tšebeliso ea 'nete", "financial.financial_health": "<PERSON><PERSON><PERSON> bo <PERSON><PERSON> ba Lichel<PERSON>", "financial.financial_health_desc": "Likamano tsa bohlokoa tsa lichelete le litšoantšo", "financial.view_budget_details": "<PERSON><PERSON>", "financial.view_detailed_forecast": "Sheba Tlhaloso e Qaqileng", "financial.view_financial_health": "<PERSON><PERSON> bo <PERSON><PERSON> ba Lichel<PERSON>", "financial.view_all_transactions": "<PERSON><PERSON>", "financial.loading": "E kenya data ea lichelete..."}