import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';

const CommercialDashboard: React.FC = () => {
  const metrics = [
    { label: 'Active Orders', value: '24', trend: '+12%', status: 'positive' },
    { label: 'Monthly Revenue', value: '$45,678', trend: '+8%', status: 'positive' },
    { label: 'Pending Auctions', value: '5', trend: 'neutral', status: 'neutral' },
    { label: 'Active Suppliers', value: '18', trend: '-2', status: 'negative' },
  ];

  const quickActions = [
    { name: 'New Order', icon: '📝', path: `${ROUTES.COMMERCIAL}/orders/new` },
    { name: 'Price Updates', icon: '💰', path: `${ROUTES.COMMERCIAL}/pricing` },
    { name: 'Join Auction', icon: '🔨', path: `${ROUTES.COMMERCIAL}/auctions` },
    { name: 'Add Supplier', icon: '🤝', path: `${ROUTES.COMMERCIAL}/suppliers/new` },
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Commercial Dashboard</h1>
          <p className="text-gray-600">Manage your market operations and trade activities</p>
        </div>
        <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
          Create New Order
        </button>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-md p-4"
          >
            <div className="flex justify-between items-start">
              <h3 className="text-gray-500 text-sm">{metric.label}</h3>
              <span className={`text-sm px-2 py-1 rounded ${
                metric.status === 'positive' ? 'bg-green-100 text-green-800' :
                metric.status === 'negative' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {metric.trend}
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-800 mt-2">{metric.value}</p>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              to={action.path}
              className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mb-2">{action.icon}</span>
              <span className="text-sm text-gray-600">{action.name}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Orders</h2>
          <div className="space-y-4">
            {[1, 2, 3].map((order) => (
              <div key={order} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div>
                  <p className="font-medium">Order #{order}234</p>
                  <p className="text-sm text-gray-500">2 hours ago</p>
                </div>
                <span className="px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                  Processing
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Upcoming Auctions</h2>
          <div className="space-y-4">
            {[1, 2, 3].map((auction) => (
              <div key={auction} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <div>
                  <p className="font-medium">Livestock Auction #{auction}</p>
                  <p className="text-sm text-gray-500">Starting in 2 days</p>
                </div>
                <button className="px-3 py-1 rounded-lg text-sm bg-primary-100 text-primary-800 hover:bg-primary-200 transition-colors">
                  Join
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommercialDashboard;