/**
 * Utility to patch the MUI Button component at runtime
 * This fixes the "Cannot read properties of undefined (reading 'dark')" error
 */

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    __MUI_BUTTON_PATCHED__?: boolean;
    __MUI_BUTTON_PATCH_APPLIED__?: boolean;
    __webpack_modules__?: Record<string, any>;
  }
}

/**
 * Patch the MUI Button component at runtime
 * This function injects a script into the page that patches the MUI Button component
 */
export const patchMuiButton = (): void => {
  try {
    // Check if the patch has already been applied
    if (window.__MUI_BUTTON_PATCHED__) {
      console.log('MUI Button component already patched');
      return;
    }

    // Set a global flag to prevent multiple patches
    window.__MUI_BUTTON_PATCHED__ = true;

    // Create a script element
    const script = document.createElement('script');

    // Set the script content
    script.textContent = `
      // Wait for the MUI Button component to be loaded
      (function() {
        try {
          // Check if the MUI Button component is loaded
          if (window.__MUI_BUTTON_PATCH_APPLIED__) {
            return;
          }

          // Find the MUI Button component
          const muiButtonModule = Object.values(window.__webpack_modules__ || {}).find(module => {
            return module && typeof module === 'function' && module.toString().includes('ButtonRoot');
          });

          // If the MUI Button component is not found, try again later
          if (!muiButtonModule) {
            setTimeout(patchMuiButton, 100);
            return;
          }

          // Patch the MUI Button component
          const originalMuiButtonModule = muiButtonModule;

          // Replace the MUI Button module with a patched version
          window.__webpack_modules__[Object.keys(window.__webpack_modules__).find(key => window.__webpack_modules__[key] === muiButtonModule)] = function(module, exports, __webpack_require__) {
            try {
              // Call the original module
              originalMuiButtonModule(module, exports, __webpack_require__);

              // Get the ButtonRoot component
              const ButtonRoot = exports.ButtonRoot;

              // If the ButtonRoot component is not found, return
              if (!ButtonRoot) {
                return;
              }

              // Replace the ButtonRoot component with a patched version
              exports.ButtonRoot = function(props) {
                try {
                  // Make sure ownerState is defined
                  if (props && props.ownerState && props.ownerState.color && !props.theme.palette[props.ownerState.color]) {
                    // Use primary color as fallback
                    props.ownerState.color = 'primary';
                  }

                  // Make sure theme.palette[ownerState.color] is defined
                  if (props && props.ownerState && props.ownerState.color && !props.theme.palette[props.ownerState.color].main) {
                    // Use a fallback color
                    props.theme.palette[props.ownerState.color] = {
                      main: '#3AA99F',
                      dark: '#2A8A82',
                      light: '#4FBEB4',
                      contrastText: '#ffffff'
                    };
                  }

                  // Call the original ButtonRoot component
                  return ButtonRoot(props);
                } catch (error) {
                  console.error('Error in patched ButtonRoot:', error);

                  // Return a fallback button
                  return React.createElement('button', {
                    className: 'MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButtonBase-root mui-button-fixed',
                    style: {
                      backgroundColor: '#3AA99F',
                      color: '#ffffff',
                      padding: '6px 16px',
                      borderRadius: '4px',
                      border: 'none',
                      cursor: 'pointer',
                      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                      fontWeight: 500,
                      fontSize: '0.875rem',
                      lineHeight: 1.75,
                      letterSpacing: '0.02857em',
                      textTransform: 'uppercase',
                      minWidth: '64px',
                      boxShadow: '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)',
                      transition: 'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms'
                    },
                    ...props
                  }, props.children);
                }
              };

              // Mark the MUI Button component as patched
              window.__MUI_BUTTON_PATCH_APPLIED__ = true;

              console.log('MUI Button component patched successfully');
            } catch (error) {
              console.error('Error patching MUI Button component:', error);
            }
          };
        } catch (error) {
          console.error('Error in patchMuiButton:', error);
        }
      })();
    `;

    // Append the script to the document
    document.head.appendChild(script);

    // Remove the script after it has been executed
    script.onload = () => {
      document.head.removeChild(script);
    };
  } catch (error) {
    console.error('Error patching MUI Button component:', error);
  }
};

export default {
  patchMuiButton
};
