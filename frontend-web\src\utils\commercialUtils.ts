/**
 * Utility functions for the Commercial module
 */

// Format currency in ZAR
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount);
};

// Format date in South African format
export const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Format time in 12-hour format
export const formatTime = (time: string): string => {
  return time;
};

// Get status color based on status string
export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'completed':
    case 'delivered':
    case 'paid':
      return 'success';
    case 'pending':
    case 'upcoming':
      return 'primary';
    case 'processing':
    case 'partial':
      return 'info';
    case 'cancelled':
    case 'refunded':
      return 'error';
    default:
      return 'default';
  }
};
