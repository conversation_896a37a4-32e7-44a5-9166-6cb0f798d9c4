/**
 * Inventory Dashboard
 *
 * This component displays the inventory dashboard with key metrics,
 * charts, and inventory items using standardized components.
 */

import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Grid, Typography, alpha, useTheme } from '@mui/material';
import {
  Add,
  Inventory,
  TrendingUp,
  TrendingDown,
  LocalShipping,
  Category,
  Warning,
  Assessment
} from '../../utils/iconImports';
import { formatCurrency } from '../../utils/formatters';
import {
  StandardDashboard,
  ModernChart,
  ModuleContentCard,
  withSubModuleTranslation
} from '../../components/common';
import { useLanguage } from '../../contexts/LanguageContext';
import { InventoryAnalyticsCard } from '../../components/inventory';

interface InventoryDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const InventoryDashboard: React.FC<InventoryDashboardProps> = ({
  translate: propTranslate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate: contextTranslate } = useLanguage();
  
  // Use the translate prop if provided, otherwise use the context translate
  const translate = propTranslate || contextTranslate;
  
  // Loading state
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Mock inventory data
  const inventoryStats = {
    totalItems: 156,
    lowStockItems: 12,
    totalValue: 450000,
    reorderItems: 8,
    itemsByCategory: {
      'Feed': 45,
      'Medicine': 32,
      'Equipment': 28,
      'Supplies': 51
    },
    stockLevels: {
      'Optimal': 98,
      'Low': 12,
      'Critical': 5,
      'Excess': 41
    },
    inventoryTrends: [
      { month: 'Jan', value: 380000 },
      { month: 'Feb', value: 395000 },
      { month: 'Mar', value: 420000 },
      { month: 'Apr', value: 405000 },
      { month: 'May', value: 430000 },
      { month: 'Jun', value: 450000 }
    ],
    topItems: [
      { name: 'Cattle Feed', quantity: 2500, unit: 'kg', value: 75000 },
      { name: 'Sheep Feed', quantity: 1200, unit: 'kg', value: 42000 },
      { name: 'Antibiotics', quantity: 50, unit: 'bottles', value: 35000 },
      { name: 'Vaccines', quantity: 120, unit: 'doses', value: 28000 },
      { name: 'Fencing Materials', quantity: 500, unit: 'meters', value: 25000 }
    ]
  };

  // Prepare data for category chart
  const categoryData = Object.entries(inventoryStats.itemsByCategory).map(([name, value]) => ({
    name,
    value,
  }));

  // Prepare data for stock levels chart
  const stockLevelData = Object.entries(inventoryStats.stockLevels).map(([name, value]) => ({
    name,
    value,
  }));

  // Handle refresh data
  const handleRefreshData = useCallback(() => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  }, []);

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translate('inventory.total_items'),
      value: inventoryStats.totalItems.toString(),
      icon: <Inventory />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('inventory.low_stock'),
      value: inventoryStats.lowStockItems.toString(),
      icon: <Warning />,
      color: theme.palette.warning.main,
      trend: {
        value: 2,
        isPositive: false,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('inventory.total_value'),
      value: formatCurrency(inventoryStats.totalValue),
      icon: <TrendingUp />,
      color: theme.palette.success.main,
      trend: {
        value: 8,
        isPositive: true,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('inventory.reorder_items'),
      value: inventoryStats.reorderItems.toString(),
      icon: <LocalShipping />,
      color: theme.palette.error.main,
      trend: {
        value: 1,
        isPositive: false,
        label: translate('dashboard.since_last_month')
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translate('inventory.add_item'),
      icon: <Add />,
      onClick: () => navigate('/inventory/items/new'),
      color: 'primary'
    }
  ];

  // Prepare dashboard tabs
  const dashboardTabs = [
    {
      label: translate('dashboard.overview'),
      icon: <Assessment />,
      content: (
        <Box>
          {/* Charts Section */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate('inventory.items_by_category')}
                subtitle={translate('inventory.items_by_category_desc')}
                data={categoryData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="inventory"
                tooltip={translate('inventory.items_by_category_help')}
                formatValue={(value) => `${value} ${translate('inventory.items')}`}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate('inventory.stock_levels')}
                subtitle={translate('inventory.stock_levels_desc')}
                data={stockLevelData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.warning.main}
                allowChartTypeChange={true}
                module="inventory"
                tooltip={translate('inventory.stock_levels_help')}
                formatValue={(value) => `${value} ${translate('inventory.items')}`}
              />
            </Grid>
          </Grid>

          {/* Inventory Value Trend */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <ModernChart
                title={translate('inventory.value_trend')}
                subtitle={translate('inventory.value_trend_desc')}
                data={inventoryStats.inventoryTrends}
                type="line"
                dataKeys={['value']}
                xAxisDataKey="month"
                height={350}
                accentColor={theme.palette.success.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="inventory"
                tooltip={translate('inventory.value_trend_help')}
                formatValue={(value) => formatCurrency(Number(value))}
              />
            </Grid>
          </Grid>

          {/* Top Inventory Items */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <InventoryAnalyticsCard
                title={translate('inventory.top_items')}
                subtitle={translate('inventory.top_items_desc')}
                icon={<Category />}
                height={400}
                accentColor={theme.palette.info.main}
                actionLabel={translate('inventory.view_all')}
                onAction={() => navigate('/inventory/items')}
              >
                <Box sx={{ mt: 2 }}>
                  {inventoryStats.topItems.map((item, index) => (
                    <Box
                      key={index}
                      sx={{
                        p: 2,
                        mb: 2,
                        borderRadius: '8px',
                        bgcolor: alpha(theme.palette.background.paper, 0.05),
                        '&:hover': {
                          bgcolor: alpha(theme.palette.background.paper, 0.1),
                          transform: 'translateY(-2px)',
                          transition: 'all 0.3s ease'
                        }
                      }}
                    >
                      <Grid container alignItems="center">
                        <Grid item xs={6}>
                          <Typography variant="subtitle1" fontWeight="medium">{item.name}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.quantity} {item.unit}
                          </Typography>
                        </Grid>
                        <Grid item xs={6} textAlign="right">
                          <Typography variant="h6" fontWeight="bold">{formatCurrency(item.value)}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {translate('inventory.total_value')}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  ))}
                </Box>
              </InventoryAnalyticsCard>
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('inventory.stock_management'),
      icon: <Inventory />,
      content: (
        <Box>
          {/* Stock Management Content */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <ModuleContentCard
                title={translate('inventory.stock_management')}
                subtitle={translate('inventory.stock_management_desc')}
                icon={<Inventory />}
                module="inventory"
                height="100%"
                delay={0.1}
                actionLabel={translate('inventory.manage_stock')}
                onAction={() => navigate('/inventory/stock')}
              >
                <Box sx={{ p: 2 }}>
                  <Typography variant="body1">
                    {translate('inventory.stock_management_content')}
                  </Typography>
                </Box>
              </ModuleContentCard>
            </Grid>
          </Grid>
        </Box>
      )
    }
  ];

  return (
    <StandardDashboard
      title={translate('inventory.dashboard')}
      subtitle={translate('inventory.manage')}
      icon={<Inventory />}
      stats={dashboardStats.map(stat => ({
        label: stat.label,
        value: stat.value,
        trend: {
          value: stat.trend.value,
          isPositive: stat.trend.isPositive
        }
      }))}
      actions={dashboardActions}
      tabs={dashboardTabs}
      activeTab={0}
      isLoading={isLoading}
      loadingMessage={translate('common.loading')}
      onRefresh={handleRefreshData}
      module="inventory"
    />
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(InventoryDashboard, 'inventory', 'dashboard');
