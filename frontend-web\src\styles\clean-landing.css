/*
 * AGRIINTEL - <PERSON><PERSON><PERSON> PROFESSIONAL LANDING PAGE
 * Simple, accessible, production-ready design
 * Inspired by modern SaaS platforms with clean layouts
 */

:root {
    /* Clean Color Palette */
    --primary-blue: #1565C0;
    --primary-green: #2E7D32;
    --accent-gold: #F57C00;
    --beta-orange: #FF9800;
    --pro-green: #4CAF50;
    
    /* Neutral Colors */
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-600: #4B5563;
    --gray-900: #111827;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --space-2: 0.5rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
}

/* Clean Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-900);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Main Landing Container */
.agri-landing-main {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%);
    position: relative;
}

/* Navigation */
.agri-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: var(--space-4) 0;
}

.agri-nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.agri-nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.agri-logo {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-md);
}

.agri-brand-text {
    display: flex;
    flex-direction: column;
}

.agri-brand-title {
    font-size: var(--font-size-xl) !important;
    font-weight: 700 !important;
    color: var(--primary-blue) !important;
    line-height: 1.2 !important;
    margin: 0 !important;
}

.agri-brand-subtitle {
    font-size: var(--font-size-sm) !important;
    color: var(--gray-600) !important;
    margin: 0 !important;
}

.agri-nav-center {
    flex: 1;
    text-align: center;
}

.agri-tagline {
    font-size: var(--font-size-lg) !important;
    color: var(--gray-600) !important;
    font-weight: 500 !important;
    margin: 0 !important;
}

.agri-status-badge {
    background: var(--accent-gold) !important;
    color: var(--white) !important;
    font-weight: 600 !important;
    font-size: var(--font-size-sm) !important;
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-md) !important;
}

/* Hero Section */
.agri-hero-section {
    padding: calc(80px + var(--space-20)) 0 var(--space-20) 0;
    text-align: center;
    color: var(--white);
}

.agri-hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.agri-hero-title {
    font-size: var(--font-size-5xl) !important;
    font-weight: 900 !important;
    color: var(--white) !important;
    margin-bottom: var(--space-6) !important;
    line-height: 1.1 !important;
}

.agri-hero-accent {
    background: linear-gradient(135deg, var(--accent-gold), #FFB74D);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.agri-hero-subtitle {
    font-size: var(--font-size-xl) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin-bottom: var(--space-8) !important;
    line-height: 1.5 !important;
}

.agri-hero-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: var(--space-8);
}

.agri-cta-primary {
    background: var(--accent-gold) !important;
    color: var(--white) !important;
    font-size: var(--font-size-lg) !important;
    font-weight: 600 !important;
    padding: var(--space-4) var(--space-8) !important;
    border-radius: var(--radius-lg) !important;
    text-transform: none !important;
    box-shadow: var(--shadow-lg) !important;
    transition: var(--transition) !important;
    min-width: 200px;
    min-height: 56px;
}

.agri-cta-primary:hover {
    background: #E65100 !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg) !important;
}

.agri-cta-secondary {
    background: transparent !important;
    color: var(--white) !important;
    border: 2px solid var(--white) !important;
    font-size: var(--font-size-lg) !important;
    font-weight: 600 !important;
    padding: var(--space-4) var(--space-8) !important;
    border-radius: var(--radius-lg) !important;
    text-transform: none !important;
    transition: var(--transition) !important;
    min-width: 200px;
    min-height: 56px;
}

.agri-cta-secondary:hover {
    background: var(--white) !important;
    color: var(--primary-blue) !important;
    transform: translateY(-2px);
}

.agri-hero-trust {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
}

.agri-trust-text {
    font-size: var(--font-size-lg) !important;
    color: rgba(255, 255, 255, 0.8) !important;
    margin: 0 !important;
}

/* Features Section */
.agri-features-section {
    background: var(--white);
    padding: var(--space-20) 0;
}

.agri-section-header {
    text-align: center;
    margin-bottom: var(--space-16);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 var(--space-4);
}

.agri-section-title {
    font-size: var(--font-size-4xl) !important;
    font-weight: 800 !important;
    color: var(--gray-900) !important;
    margin-bottom: var(--space-4) !important;
    line-height: 1.2 !important;
}

.agri-section-subtitle {
    font-size: var(--font-size-xl) !important;
    color: var(--gray-600) !important;
    margin: 0 !important;
}

.agri-features-grid {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-4);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.agri-feature-card {
    background: var(--white) !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: var(--shadow-md) !important;
    transition: var(--transition) !important;
    border: 1px solid var(--gray-100) !important;
    overflow: hidden;
}

.agri-feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg) !important;
}

.agri-feature-content {
    padding: var(--space-8) !important;
    text-align: center;
}

.agri-feature-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-gold), #FFB74D);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6) auto;
}

.agri-feature-icon svg {
    font-size: 2rem !important;
    color: var(--white);
}

.agri-feature-title {
    font-size: var(--font-size-2xl) !important;
    font-weight: 700 !important;
    color: var(--gray-900) !important;
    margin-bottom: var(--space-4) !important;
}

.agri-feature-description {
    font-size: var(--font-size-lg) !important;
    color: var(--gray-600) !important;
    line-height: 1.6 !important;
    margin: 0 !important;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .agri-nav-container {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }
    
    .agri-hero-title {
        font-size: var(--font-size-4xl) !important;
    }
    
    .agri-hero-subtitle {
        font-size: var(--font-size-lg) !important;
    }
    
    .agri-hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .agri-cta-primary,
    .agri-cta-secondary {
        width: 100%;
        max-width: 300px;
    }
    
    .agri-features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid var(--accent-gold);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --primary-blue: #0D47A1;
        --primary-green: #1B5E20;
        --accent-gold: #E65100;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
