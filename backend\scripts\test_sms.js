/**
 * Test SMS Service with <PERSON><PERSON><PERSON>
 *
 * This script tests the SMS service with <PERSON>wilio
 */

const ruralNotificationService = require('../services/ruralNotificationService');

// Configure Twilio with your account credentials
ruralNotificationService.config = {
  ...ruralNotificationService.config,
  smsGateways: {
    ...ruralNotificationService.config.smsGateways,
    twilio: {
      ...ruralNotificationService.config.smsGateways.twilio,
      enabled: true,
      // Replace these with your actual Twilio credentials
      accountSid: '**********************************',
      authToken: '00000000000000000000000000000000',
      fromNumber: '+***********'
    },
    // Disable other gateways
    clickatell: { ...ruralNotificationService.config.smsGateways.clickatell, enabled: false },
    bulksms: { ...ruralNotificationService.config.smsGateways.bulksms, enabled: false }
  }
};

/**
 * Test sending SMS to a specific phone number
 * @param {string} phoneNumber - Phone number to send SMS to
 */
async function testSMS(phoneNumber) {
  console.log('=== AMPD Livestock SMS Test ===');
  console.log(`Testing with phone number: ${phoneNumber}`);
  console.log(`Current time: ${new Date().toLocaleString()}`);
  console.log('');

  try {
    // Test SMS
    console.log('=== Sending SMS to', phoneNumber, '===');
    const message = `AMPD Livestock SMS Test: This is a test message sent at ${new Date().toLocaleTimeString()}`;
    console.log('Message:', message);

    // Send the SMS
    const result = await ruralNotificationService.sendSMS(phoneNumber, message, 'high');

    console.log('SMS Result:', result);

    if (result.success) {
      console.log('SMS sent successfully!');
      console.log('Message ID:', result.messageId);
      console.log('Gateway:', result.gateway || 'simulated');
    } else {
      console.log('Failed to send SMS:', result.error);
    }
  } catch (error) {
    console.error('Error testing SMS:', error);
  }

  console.log('');
  console.log('=== Test completed ===');
}

// Get phone number from command line arguments
const phoneNumber = process.argv[2] || '0761246783';

// Run the test
testSMS(phoneNumber).catch(console.error);
