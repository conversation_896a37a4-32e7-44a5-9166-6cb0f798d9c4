import React from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, IconButton, Tab, Tabs, Box, Typography } from '@mui/material';
import {  Close, Edit, LocalHospital, Timeline, Assessment  } from '../../utils/iconImports';
import AnimalStatusBadge from './AnimalStatusBadge';

interface AnimalQuickViewProps {
  animal: any;
  onClose: () => void;
}

const AnimalQuickView: React.FC<AnimalQuickViewProps> = ({ animal, onClose }) => {
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Dialog
      open={true}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Typography variant="h6">
            Animal #{animal.tagNumber}
          </Typography>
          <AnimalStatusBadge status={animal.status} />
        </div>
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <div className="grid grid-cols-2 gap-6 mb-6">
          <div>
            <Typography variant="subtitle2" color="textSecondary">
              Breed
            </Typography>
            <Typography variant="body1">
              {animal.breed}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" color="textSecondary">
              Age
            </Typography>
            <Typography variant="body1">
              {animal.dateOfBirth}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" color="textSecondary">
              Weight
            </Typography>
            <Typography variant="body1">
              {animal.weight} kg
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" color="textSecondary">
              Last Checkup
            </Typography>
            <Typography variant="body1">
              {new Date(animal.lastCheckup).toLocaleDateString()}
            </Typography>
          </div>
        </div>

        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab icon={<Assessment />} label="Overview" />
          <Tab icon={<LocalHospital />} label="Health" />
          <Tab icon={<Timeline />} label="History" />
        </Tabs>

        <Box className="mt-4">
          {tabValue === 0 && (
            <div className="space-y-4">
              <Typography variant="h6">Key Metrics</Typography>
              {/* Add overview metrics here */}
            </div>
          )}
          {tabValue === 1 && (
            <div className="space-y-4">
              <Typography variant="h6">Health Records</Typography>
              {/* Add health records here */}
            </div>
          )}
          {tabValue === 2 && (
            <div className="space-y-4">
              <Typography variant="h6">Activity History</Typography>
              {/* Add activity history here */}
            </div>
          )}
        </Box>

        <div className="mt-6 flex justify-end space-x-4">
          <CustomButton
            startIcon={<LocalHospital />}
            variant="outlined"
            color="primary"
          >
            Record Health Check
          </CustomButton>
          <CustomButton
            startIcon={<Edit />}
            variant="contained"
            color="primary"
          >
            Edit Details
          </CustomButton>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AnimalQuickView;