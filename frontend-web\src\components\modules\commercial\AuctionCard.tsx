import React from 'react';
import { motion } from 'framer-motion';

interface AuctionCardProps {
  auction: {
    id: string;
    title: string;
    date: string;
    startTime: string;
    location: string;
    itemCount: number;
    status: 'upcoming' | 'live' | 'ended';
    currentBid?: number;
  };
}

const AuctionCard: React.FC<AuctionCardProps> = ({ auction }) => {
  const getStatusStyles = (status: string) => {
    const styles = {
      upcoming: 'bg-blue-100 text-blue-800',
      live: 'bg-green-100 text-green-800',
      ended: 'bg-gray-100 text-gray-800'
    };
    return styles[status as keyof typeof styles];
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6"
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold">{auction.title}</h3>
          <p className="text-gray-500 text-sm mt-1">
            {auction.date} at {auction.startTime}
          </p>
        </div>
        <span className={`px-3 py-1 rounded-full text-sm ${getStatusStyles(auction.status)}`}>
          {auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
        </span>
      </div>

      <div className="mt-4 space-y-2">
        <div className="flex items-center text-gray-600">
          <span className="mr-2">📍</span>
          <span className="text-sm">{auction.location}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <span className="mr-2">🐄</span>
          <span className="text-sm">{auction.itemCount} items listed</span>
        </div>
        {auction.currentBid && (
          <div className="flex items-center text-gray-600">
            <span className="mr-2">💰</span>
            <span className="text-sm">Current Bid: ${auction.currentBid.toLocaleString()}</span>
          </div>
        )}
      </div>

      <div className="mt-6 flex space-x-3">
        <button className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
          {auction.status === 'live' ? 'Join Auction' : 'View Details'}
        </button>
        {auction.status === 'upcoming' && (
          <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            Set Reminder
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default AuctionCard;