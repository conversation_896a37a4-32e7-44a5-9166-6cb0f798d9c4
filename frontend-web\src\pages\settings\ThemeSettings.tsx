import React, { useEffect } from 'react';
import { Box, useMediaQuery, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { ColorLens } from '@mui/icons-material';
import { <PERSON><PERSON>leHeader, ThemeSafeWrapper, ThemeSafeButton } from '../../components/common';
import EnhancedThemeSettings from '../../components/settings/EnhancedThemeSettings';
import { applyTabSelectionFixes } from '../../utils/applyTabSelectionFixes';

const ThemeSettings: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Apply tab selection fixes when component mounts
  useEffect(() => {
    // Apply tab selection fixes to ensure tabs are clickable
    applyTabSelectionFixes();

    // Set up multiple delays to ensure the fixes are applied after any animations complete
    const timer1 = setTimeout(() => {
      applyTabSelectionFixes();
    }, 100);

    const timer2 = setTimeout(() => {
      applyTabSelectionFixes();
    }, 500);

    const timer3 = setTimeout(() => {
      applyTabSelectionFixes();
    }, 1000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <ThemeSafeWrapper>
      <Box sx={{ width: '100%', p: { xs: 2, sm: 3 } }}>
        <ModuleHeader
          title="Theme Settings"
          subtitle="Customize the appearance of your MayCaiphus Livestock Management System"
          module="settings"
          icon={<ColorLens />}
        />

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mt-6"
        >
          <EnhancedThemeSettings />
        </motion.div>
      </Box>
    </ThemeSafeWrapper>
  );
};

export default ThemeSettings;
