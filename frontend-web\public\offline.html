<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <!-- Theme color for supported browsers (Chrome, Safari, Edge) -->
    <meta name="theme-color" content="#3AA99F" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#2E8B7A" media="(prefers-color-scheme: dark)" />
    <!-- Microsoft Edge/IE -->
    <meta name="msapplication-navbutton-color" content="#3AA99F" />
    <!-- Safari -->
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="description" content="AgriIntel - You're currently offline" />
    <title>AgriIntel - Offline</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
            margin: 20px;
        }

        .offline-icon {
            font-size: 80px;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                will-change: transform;
            }
            50% {
                transform: scale(1.1);
                will-change: transform;
            }
            100% {
                transform: scale(1);
                will-change: transform;
            }
        }

        .logo {
            color: #3AA99F;
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #3AA99F, #2E8B7A);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1 {
            color: #2c3e50;
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .message {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .features {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 30px 0;
            text-align: left;
        }

        .features h3 {
            color: #3AA99F;
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: "✓";
            color: #3AA99F;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3AA99F, #2E8B7A);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(58, 169, 159, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .status {
            margin-top: 30px;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }

        .status.online {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .connection-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .connection-indicator.online {
            background: #28a745;
        }

        .connection-indicator.offline {
            background: #dc3545;
        }

        @media (max-width: 480px) {
            .offline-container {
                padding: 40px 20px;
            }
            
            .logo {
                font-size: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        
        <div class="logo">AgriIntel</div>
        
        <h1>You're Currently Offline</h1>
        
        <div class="message">
            Don't worry! AgriIntel works offline too. Some features are still available while you're disconnected.
        </div>

        <div class="features">
            <h3>Available Offline Features</h3>
            <ul class="feature-list">
                <li>View cached animal records</li>
                <li>Access recent health data</li>
                <li>Browse saved reports</li>
                <li>Use basic calculators</li>
                <li>View offline documentation</li>
            </ul>
        </div>

        <div class="buttons">
            <button type="button" class="btn btn-primary" onclick="retryConnection()">
                Try Again
            </button>
            <a href="/dashboard" class="btn btn-secondary">
                Go to Dashboard
            </a>
        </div>

        <div id="connectionStatus" class="status offline">
            <span class="connection-indicator offline"></span>
            Connection Status: Offline
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.connection-indicator');
            
            if (navigator.onLine) {
                statusElement.className = 'status online';
                statusElement.innerHTML = '<span class="connection-indicator online"></span>Connection Status: Online';
                
                // Auto-redirect when back online
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 2000);
            } else {
                statusElement.className = 'status offline';
                statusElement.innerHTML = '<span class="connection-indicator offline"></span>Connection Status: Offline';
            }
        }

        // Retry connection
        function retryConnection() {
            const btn = document.querySelector('.btn-primary');
            btn.textContent = 'Checking...';
            btn.disabled = true;
            
            // Check if we can reach the server
            fetch('/api/health', { 
                method: 'GET',
                cache: 'no-cache'
            })
            .then(response => {
                if (response.ok) {
                    btn.textContent = 'Connected!';
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                } else {
                    throw new Error('Server not reachable');
                }
            })
            .catch(() => {
                btn.textContent = 'Still Offline';
                setTimeout(() => {
                    btn.textContent = 'Try Again';
                    btn.disabled = false;
                }, 2000);
            });
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Periodic connection check
        setInterval(updateConnectionStatus, 5000);

        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }

        // Handle keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' || event.key === ' ') {
                retryConnection();
            } else if (event.key === 'Escape') {
                window.location.href = '/dashboard';
            }
        });
    </script>
</body>
</html>
