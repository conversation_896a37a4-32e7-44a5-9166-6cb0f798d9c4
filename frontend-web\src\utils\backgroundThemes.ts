import React from 'react';

/**
 * Background Themes Utility
 *
 * This file provides a centralized system for managing background themes across the application.
 * It ensures consistency in design and allows for easy theme changes.
 */

// Define a set of high-quality, professional background images for the entire application
// These are the manually added images from the UXUI LiveStock folder
export const backgroundImages = {
  // Primary backgrounds for all modules (using manually added images)
  primary: [
    '/images/dashboard/main-dashboard.jpg', // Maindash.jpg
    '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg
    '/images/modules/animals/cattle-2.avif', // cows 2.avif
    '/images/modules/animals/cattle-3.jpeg', // cows 3.jpeg
    '/images/modules/animals/cattle-4.jpeg', // cows 4.jpeg
    '/images/modules/animals/cattle-5.avif', // cows 5.avif
  ],

  // Secondary backgrounds (using manually added images)
  secondary: [
    '/images/modules/health/health-main.png', // health Managemnt.png
    '/images/modules/health/veterinary-1.jpg', // veteian health 1.jpg
    '/images/modules/health/veterinary-2.jpg', // veteian health 2.jpg
    '/images/modules/health/veterinary-3.jpg', // veteian health 3.jpg
    '/images/modules/health/veterinary-main.jpg', // Veteran health.jpg
    '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg
  ],

  // Module-specific backgrounds
  breeding: [
    '/images/modules/breeding/breeding-main.png', // breending and pregnancy.png
    '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg
    '/images/modules/animals/cattle-2.avif', // cows 2.avif
  ],

  health: [
    '/images/modules/health/health-main.png', // health Managemnt.png
    '/images/modules/health/veterinary-1.jpg', // veteian health 1.jpg
    '/images/modules/health/veterinary-2.jpg', // veteian health 2.jpg
    '/images/modules/health/veterinary-3.jpg', // veteian health 3.jpg
    '/images/modules/health/veterinary-main.jpg', // Veteran health.jpg
  ],

  feed: [
    '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg
    '/images/modules/feeding/feed-2.jpeg', // feed management 2.jpeg
    '/images/modules/feeding/feed-sheep.jpeg', // feed managemnt sheep.jpeg
  ],

  commercial: [
    '/images/modules/commercial/commercial-main.webp', // commercial products.webp
    '/images/modules/commercial/commercial-1.jpeg', // commercial products 1.web.jpeg
    '/images/modules/commercial/commercial-2.jpeg', // commercial products 2.web.jpeg
    '/images/modules/commercial/commercial-3.jpeg', // commercial products 3.web.jpeg
  ],

  financial: [
    '/images/modules/commercial/commercial-1.jpeg', // commercial products 1.web.jpeg
    '/images/modules/commercial/commercial-2.jpeg', // commercial products 2.web.jpeg
    '/images/modules/commercial/commercial-3.jpeg', // commercial products 3.web.jpeg
  ],

  resources: [
    '/images/modules/health/veterinary-1.jpg', // veteian health 1.jpg
    '/images/modules/health/veterinary-2.jpg', // veteian health 2.jpg
    '/images/modules/health/veterinary-3.jpg', // veteian health 3.jpg
  ],

  animals: [
    '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg
    '/images/modules/animals/cattle-2.avif', // cows 2.avif
    '/images/modules/animals/cattle-3.jpeg', // cows 3.jpeg
    '/images/modules/animals/cattle-4.jpeg', // cows 4.jpeg
    '/images/modules/animals/cattle-5.avif', // cows 5.avif
  ],

  // Animal-specific images for cards
  animalCards: [
    '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg
    '/images/modules/animals/cattle-2.avif', // cows 2.avif
    '/images/modules/animals/cattle-3.jpeg', // cows 3.jpeg
    '/images/modules/animals/cattle-4.jpeg', // cows 4.jpeg
    '/images/modules/animals/cattle-5.avif', // cows 5.avif
  ],

  // RFID module
  rfid: [
    '/images/modules/rfid/rfid-1.webp', // RFID 1.webp
    '/images/modules/rfid/rfid-2.jpg', // RFID 2.jpg
    '/images/modules/rfid/rfid-3.jpg', // FRID 3.jpg
    '/images/modules/rfid/rfid-4.jpg', // FRID 4.jpg
  ],

  // Dashboard
  dashboard: [
    '/images/dashboard/main-dashboard.jpg', // Maindash.jpg
  ],

  // Login
  login: [
    '/images/login/login-bg.jpeg', // log in page.jpeg
  ],
};

// Function to create module themes based on the current theme color
export const createModuleThemes = (primaryColor: string, secondaryColor: string) => ({
  dashboard: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  animals: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  breeding: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  health: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  feed: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  commercial: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  financial: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  compliance: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  resources: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },

  reports: {
    primaryColor,
    secondaryColor,
    backgroundImage: '', // No background image, using solid colors
    gradientOverlay: `linear-gradient(135deg, ${primaryColor}F2, ${secondaryColor}D9)`,
  },
});

/**
 * Get the theme for a specific module
 * @param module The module name
 * @param primaryColor The primary color to use for the theme
 * @param secondaryColor The secondary color to use for the theme
 * @returns The theme object for the specified module
 */
// Always use the teal color scheme for all modules to maintain visual consistency
export const getModuleTheme = (module: string, primaryColor: string = '#3AA99F', secondaryColor: string = '#2A8A82'): any => {
  const moduleName = module.toLowerCase();
  const moduleThemes = createModuleThemes(primaryColor, secondaryColor);

  // Check if the module exists in our themes
  if (moduleName in moduleThemes) {
    const theme = moduleThemes[moduleName as keyof ReturnType<typeof createModuleThemes>];

    // If the module has specific backgrounds, use one of them randomly
    if (moduleName in backgroundImages) {
      const moduleBackgrounds = backgroundImages[moduleName as keyof typeof backgroundImages] as string[];
      if (moduleBackgrounds && moduleBackgrounds.length > 0) {
        const randomIndex = Math.floor(Math.random() * moduleBackgrounds.length);
        theme.backgroundImage = moduleBackgrounds[randomIndex];
      }
    }
    // Special case for 'feeding' module - map to 'feed' backgrounds
    else if (moduleName === 'feeding' && 'feed' in backgroundImages) {
      const moduleBackgrounds = backgroundImages['feed'] as string[];
      if (moduleBackgrounds && moduleBackgrounds.length > 0) {
        const randomIndex = Math.floor(Math.random() * moduleBackgrounds.length);
        theme.backgroundImage = moduleBackgrounds[randomIndex];
      }
    }

    return theme;
  }

  // Return dashboard theme as default
  return moduleThemes.dashboard;
};

/**
 * Get a random background image
 * @returns A random background image URL
 */
export const getRandomBackground = (): string => {
  const allImages = [...backgroundImages.primary, ...backgroundImages.secondary];
  const randomIndex = Math.floor(Math.random() * allImages.length);
  return allImages[randomIndex];
};

/**
 * Get a background image based on a unique identifier
 * @param id A unique identifier
 * @returns A consistent background image URL for the specified ID
 */
export const getConsistentBackground = (id: string): string => {
  // For animal cards, use the animal-specific images
  const allImages = backgroundImages.animalCards || [...backgroundImages.primary, ...backgroundImages.secondary];
  const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const index = hash % allImages.length;
  return allImages[index];
};

/**
 * Create a CSS style object for a blended background
 * @param backgroundImage The background image URL (can be empty for solid colors)
 * @param primaryColor The primary color for the gradient overlay
 * @param secondaryColor The secondary color for the gradient overlay
 * @param opacity The opacity of the gradient overlay (0-1)
 * @returns A CSS style object for the blended background
 */
export const createBlendedBackground = (
  backgroundImage: string,
  primaryColor: string,
  secondaryColor: string,
  opacity: number = 0.8
): React.CSSProperties => {
  // If no background image, use a solid color gradient
  if (!backgroundImage) {
    return {
      background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
    };
  }

  // If background image is provided, use it with a gradient overlay (for backward compatibility)
  return {
    backgroundImage: `linear-gradient(135deg, ${primaryColor}${Math.round(opacity * 100)}%, ${secondaryColor}${Math.round(opacity * 90)}%), url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  };
};

/**
 * Get a blended background style for a specific module
 * @param module The module name
 * @param opacity The opacity of the gradient overlay (0-1)
 * @returns A CSS style object for the blended background and theme properties
 */
export const getModuleBlendedBackground = (module: string, opacity: number = 0.8): { backgroundImage: string; primaryColor: string; secondaryColor: string; style: React.CSSProperties } => {
  const theme = getModuleTheme(module);
  return {
    backgroundImage: theme.backgroundImage,
    primaryColor: theme.primaryColor,
    secondaryColor: theme.secondaryColor,
    style: createBlendedBackground(
      theme.backgroundImage,
      theme.primaryColor,
      theme.secondaryColor,
      opacity
    )
  };
};
