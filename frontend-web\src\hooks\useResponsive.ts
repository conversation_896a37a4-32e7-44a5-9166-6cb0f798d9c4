import { useMediaQuery, useTheme } from '@mui/material';
import { Breakpoint } from '@mui/material/styles';

/**
 * Custom hook for responsive design
 * Provides utility functions to check screen sizes
 *
 * @param query Optional query type ('up', 'down', 'between', 'only')
 * @param key Optional breakpoint key
 * @param end Optional end breakpoint key for 'between' query
 */
export const useResponsive = (query?: 'up' | 'down' | 'between' | 'only', key?: Breakpoint, end?: Breakpoint) => {
  const theme = useTheme();

  // Direct media query results
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const isLargeDesktop = useMediaQuery(theme.breakpoints.up('lg'));

  // If query and key are provided, return the result directly
  if (query && key) {
    if (query === 'down') return down(key);
    if (query === 'up') return up(key);
    if (query === 'between' && end) return between(key, end);
    if (query === 'only') return only(key);
  }

  // Create safe wrapper functions that use the pre-computed values
  const down = (breakpoint: Breakpoint) => {
    if (breakpoint === 'xs') return true;
    if (breakpoint === 'sm') return isMobile;
    if (breakpoint === 'md') return isMobile || isTablet;
    if (breakpoint === 'lg') return isMobile || isTablet || !isLargeDesktop;
    return false;
  };

  const up = (breakpoint: Breakpoint) => {
    if (breakpoint === 'xs') return true;
    if (breakpoint === 'sm') return !isMobile;
    if (breakpoint === 'md') return isDesktop || isLargeDesktop;
    if (breakpoint === 'lg') return isLargeDesktop;
    return false;
  };

  const between = (start: Breakpoint, end: Breakpoint) => {
    return up(start) && down(end);
  };

  const only = (breakpoint: Breakpoint) => {
    if (breakpoint === 'xs') return isMobile;
    if (breakpoint === 'sm') return isTablet;
    if (breakpoint === 'md') return isDesktop && !isLargeDesktop;
    if (breakpoint === 'lg') return isLargeDesktop;
    return false;
  };

  // Get current breakpoint name
  const getCurrentBreakpoint = (): Breakpoint => {
    if (down('sm')) return 'xs';
    if (between('sm', 'md')) return 'sm';
    if (between('md', 'lg')) return 'md';
    if (between('lg', 'xl')) return 'lg';
    return 'xl';
  };

  // Get font size based on breakpoint
  const getResponsiveFontSize = (
    base: number,
    options?: {
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    }
  ): string => {
    const breakpoint = getCurrentBreakpoint();
    const sizes = {
      xs: base,
      sm: options?.sm || base * 1.1,
      md: options?.md || base * 1.2,
      lg: options?.lg || base * 1.3,
      xl: options?.xl || base * 1.4,
    };

    return `${sizes[breakpoint]}rem`;
  };

  // Get spacing based on breakpoint
  const getResponsiveSpacing = (
    base: number,
    options?: {
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    }
  ): number => {
    const breakpoint = getCurrentBreakpoint();
    const spacing = {
      xs: base,
      sm: options?.sm || base * 1.25,
      md: options?.md || base * 1.5,
      lg: options?.lg || base * 1.75,
      xl: options?.xl || base * 2,
    };

    return spacing[breakpoint];
  };

  return {
    down,
    up,
    between,
    only,
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    getCurrentBreakpoint,
    getResponsiveFontSize,
    getResponsiveSpacing,
  };
};

export default useResponsive;
