/**
 * AgriIntel Service Worker
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'agriintel-v1.0.0';
const OFFLINE_URL = '/offline.html';

// Resources to cache for offline functionality
const STATIC_CACHE_URLS = [
  '/',
  '/dashboard',
  '/beta',
  '/login',
  '/register',
  '/offline.html',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
  '/favicon.ico',
  '/logo192.png',
  '/logo512.png'
];

// API endpoints to cache
const API_CACHE_URLS = [
  '/api/auth/me',
  '/api/dashboard/stats',
  '/api/animals',
  '/api/health',
  '/api/weather'
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('AgriIntel Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static resources...');
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        console.log('Static resources cached successfully');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Failed to cache static resources:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('AgriIntel Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests with caching strategy
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - Network First strategy
    event.respondWith(handleApiRequest(request));
  } else if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2)$/)) {
    // Static assets - Cache First strategy
    event.respondWith(handleStaticAssets(request));
  } else {
    // HTML pages - Network First with offline fallback
    event.respondWith(handlePageRequest(request));
  }
});

/**
 * Handle API requests with Network First strategy
 */
async function handleApiRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed for API request, trying cache:', request.url);
    
    // Fallback to cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline response for API requests
    return new Response(
      JSON.stringify({
        success: false,
        message: 'Offline - data not available',
        offline: true
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle static assets with Cache First strategy
 */
async function handleStaticAssets(request) {
  const cache = await caches.open(CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('Failed to fetch static asset:', request.url);
    return new Response('Asset not available offline', { status: 404 });
  }
}

/**
 * Handle page requests with Network First and offline fallback
 */
async function handlePageRequest(request) {
  const cache = await caches.open(CACHE_NAME);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful HTML responses
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed for page request, trying cache:', request.url);
    
    // Try cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fallback to offline page
    const offlineResponse = await cache.match(OFFLINE_URL);
    if (offlineResponse) {
      return offlineResponse;
    }
    
    // Last resort - basic offline message
    return new Response(
      `<!DOCTYPE html>
      <html>
        <head>
          <title>AgriIntel - Offline</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .offline-container { max-width: 400px; margin: 0 auto; }
            .offline-icon { font-size: 64px; margin-bottom: 20px; }
            h1 { color: #3AA99F; }
            p { color: #666; line-height: 1.6; }
            .retry-btn { 
              background: #3AA99F; 
              color: white; 
              border: none; 
              padding: 12px 24px; 
              border-radius: 6px; 
              cursor: pointer; 
              font-size: 16px;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="offline-container">
            <div class="offline-icon">📡</div>
            <h1>You're Offline</h1>
            <p>AgriIntel is currently unavailable. Please check your internet connection and try again.</p>
            <p>Some cached data may still be available in the dashboard.</p>
            <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
          </div>
        </body>
      </html>`,
      {
        status: 200,
        headers: { 'Content-Type': 'text/html' }
      }
    );
  }
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'background-sync-animals') {
    event.waitUntil(syncOfflineAnimals());
  } else if (event.tag === 'background-sync-health') {
    event.waitUntil(syncOfflineHealthRecords());
  }
});

/**
 * Sync offline animal data when connection is restored
 */
async function syncOfflineAnimals() {
  try {
    // Get offline data from IndexedDB or localStorage
    const offlineData = await getOfflineData('animals');
    
    if (offlineData && offlineData.length > 0) {
      console.log('Syncing offline animal data...');
      
      for (const data of offlineData) {
        try {
          await fetch('/api/animals', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });
          
          // Remove from offline storage after successful sync
          await removeOfflineData('animals', data.id);
        } catch (error) {
          console.error('Failed to sync animal data:', error);
        }
      }
      
      console.log('Animal data sync completed');
    }
  } catch (error) {
    console.error('Background sync failed for animals:', error);
  }
}

/**
 * Sync offline health records when connection is restored
 */
async function syncOfflineHealthRecords() {
  try {
    const offlineData = await getOfflineData('health');
    
    if (offlineData && offlineData.length > 0) {
      console.log('Syncing offline health records...');
      
      for (const data of offlineData) {
        try {
          await fetch('/api/health', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });
          
          await removeOfflineData('health', data.id);
        } catch (error) {
          console.error('Failed to sync health record:', error);
        }
      }
      
      console.log('Health records sync completed');
    }
  } catch (error) {
    console.error('Background sync failed for health records:', error);
  }
}

/**
 * Get offline data from storage
 */
async function getOfflineData(type) {
  // This would typically use IndexedDB
  // For now, return empty array
  return [];
}

/**
 * Remove offline data after successful sync
 */
async function removeOfflineData(type, id) {
  // This would typically remove from IndexedDB
  console.log(`Removing offline ${type} data for ID: ${id}`);
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event);
  
  const options = {
    body: 'You have new updates in AgriIntel',
    icon: '/logo192.png',
    badge: '/favicon.ico',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Dashboard',
        icon: '/favicon.ico'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/favicon.ico'
      }
    ]
  };
  
  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.title = data.title || 'AgriIntel';
  }
  
  event.waitUntil(
    self.registration.showNotification('AgriIntel', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/dashboard')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('AgriIntel Service Worker loaded successfully');
