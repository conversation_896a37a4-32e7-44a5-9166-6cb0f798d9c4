import React from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Card, CardContent } from '@mui/material';
import { getSponsorImagePath } from '../../utils/imageUtils';

const ImageTest: React.FC = () => {
  // Test all sponsor images using our utility function
  const imageNames = [
    'land bank.png',
    'Agricultural research council.png',
    'Agriculture , land reform & rural development.png',
    'National Agricultural Marketing Council.png',
    'Perishable Productst Control Board.png'
  ];

  // Get the full paths using our utility function
  const imagePaths = imageNames.map(name => getSponsorImagePath(name));

  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>Image Test</Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          Testing image loading with utility function
        </Typography>

        {imagePaths.map((src, index) => (
          <Box key={index} sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom>{imageNames[index]}</Typography>
            <Typography variant="caption" color="textSecondary" display="block" gutterBottom>
              Path: {src}
            </Typography>
            <img
              src={src}
              alt={`Test image ${index}`}
              style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block', marginBottom: '10px' }}
            />
            <CustomButton
              variant="outlined"
              size="small"
              onClick={() => window.open(src, '_blank')}
            >
              Open Image Directly
            </CustomButton>
          </Box>
        ))}
      </CardContent>
    </Card>
  );
};

export default ImageTest;
