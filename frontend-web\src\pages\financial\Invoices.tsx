import React, { useState } from 'react';
import { Box, Typo<PERSON>, Card, CardContent, Grid, TextField, InputAdornment, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Menu, MenuItem, Tooltip, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Search,
  FilterList,
  Add,
  MoreVert,
  Visibility,
  Download,
  Print,
  Delete,
  Edit,
  AttachMoney,
  CheckCircle,
  Warning,
  Error
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { format } from 'date-fns';

// Mock data for invoices
const mockInvoices = [
  {
    id: 'INV-2024-001',
    date: '2024-05-15',
    dueDate: '2024-06-15',
    customer: 'Beefmaster <PERSON>',
    amount: 45000,
    status: 'paid',
    items: [
      { description: 'Angus Bulls (2)', quantity: 2, unitPrice: 22500, total: 45000 }
    ]
  },
  {
    id: 'INV-2024-002',
    date: '2024-05-18',
    dueDate: '2024-06-18',
    customer: 'Karan <PERSON>',
    amount: 35000,
    status: 'pending',
    items: [
      { description: 'Bonsmara Heifers (5)', quantity: 5, unitPrice: 7000, total: 35000 }
    ]
  },
  {
    id: 'INV-2024-003',
    date: '2024-05-20',
    dueDate: '2024-06-20',
    customer: 'Cavalier Abattoir',
    amount: 120000,
    status: 'paid',
    items: [
      { description: 'Beef Cattle (15)', quantity: 15, unitPrice: 8000, total: 120000 }
    ]
  },
  {
    id: 'INV-2024-004',
    date: '2024-05-22',
    dueDate: '2024-06-22',
    customer: 'Sparta Beef',
    amount: 18500,
    status: 'overdue',
    items: [
      { description: 'Premium Feed (5 tons)', quantity: 5, unitPrice: 3700, total: 18500 }
    ]
  },
  {
    id: 'INV-2024-005',
    date: '2024-05-25',
    dueDate: '2024-06-25',
    customer: 'Chalmar Beef',
    amount: 65000,
    status: 'pending',
    items: [
      { description: 'Nguni Bulls (2)', quantity: 2, unitPrice: 32500, total: 65000 }
    ]
  },
  {
    id: 'INV-2024-006',
    date: '2024-05-28',
    dueDate: '2024-06-28',
    customer: 'Clover Dairy',
    amount: 28500,
    status: 'draft',
    items: [
      { description: 'Jersey Heifers (3)', quantity: 3, unitPrice: 9500, total: 28500 }
    ]
  }
];

// Invoice summary data
const invoiceSummary = {
  total: mockInvoices.reduce((sum, invoice) => sum + invoice.amount, 0),
  paid: mockInvoices.filter(invoice => invoice.status === 'paid').reduce((sum, invoice) => sum + invoice.amount, 0),
  pending: mockInvoices.filter(invoice => invoice.status === 'pending').reduce((sum, invoice) => sum + invoice.amount, 0),
  overdue: mockInvoices.filter(invoice => invoice.status === 'overdue').reduce((sum, invoice) => sum + invoice.amount, 0),
  draft: mockInvoices.filter(invoice => invoice.status === 'draft').reduce((sum, invoice) => sum + invoice.amount, 0)
};

const Invoices: React.FC = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, invoiceId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedInvoice(invoiceId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedInvoice(null);
  };

  // Filter invoices based on search query and status filter
  const filteredInvoices = mockInvoices.filter(invoice => {
    const matchesSearch = 
      invoice.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invoice.customer.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'paid':
        return <Chip 
          icon={<CheckCircle fontSize="small" />} 
          label="Paid" 
          size="small" 
          color="success" 
        />;
      case 'pending':
        return <Chip 
          icon={<Warning fontSize="small" />} 
          label="Pending" 
          size="small" 
          color="warning" 
        />;
      case 'overdue':
        return <Chip 
          icon={<Error fontSize="small" />} 
          label="Overdue" 
          size="small" 
          color="error" 
        />;
      case 'draft':
        return <Chip 
          label="Draft" 
          size="small" 
          variant="outlined" 
        />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Invoices"
        subtitle="Manage and track all sales invoices, payments, and customer accounts"
        module="financial"
        submodule="invoices"
        actionLabel="Create Invoice"
        actionIcon={<Add />}
        onAction={() => console.log('Create new invoice')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Invoice Summary Cards */}
          <motion.div variants={itemVariants}>
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={6} md={3}>
                <AnimatedBackgroundCard
                  title="Total Invoices"
                  subtitle={`R${invoiceSummary.total.toLocaleString()}`}
                  module="financial"
                  uniqueId="invoice-total"
                  icon={<AttachMoney />}
                  height={120}
                  delay={0.1}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <AnimatedBackgroundCard
                  title="Paid"
                  subtitle={`R${invoiceSummary.paid.toLocaleString()}`}
                  module="financial"
                  uniqueId="invoice-paid"
                  icon={<CheckCircle />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={120}
                  delay={0.2}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <AnimatedBackgroundCard
                  title="Pending"
                  subtitle={`R${invoiceSummary.pending.toLocaleString()}`}
                  module="financial"
                  uniqueId="invoice-pending"
                  icon={<Warning />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={120}
                  delay={0.3}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <AnimatedBackgroundCard
                  title="Overdue"
                  subtitle={`R${invoiceSummary.overdue.toLocaleString()}`}
                  module="financial"
                  uniqueId="invoice-overdue"
                  icon={<Error />}
                  accentColor={theme.palette.error.main}
                  secondaryColor={theme.palette.error.dark}
                  height={120}
                  delay={0.4}
                />
              </Grid>
            </Grid>
          </motion.div>

          {/* Search and Filter */}
          <motion.div variants={itemVariants}>
            <Box sx={{ mb: 4, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
              <TextField
                placeholder="Search invoices..."
                variant="outlined"
                fullWidth
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
                sx={{ flex: 1 }}
              />
              <Box sx={{ display: 'flex', gap: 2 }}>
                <CustomButton
                  variant="outlined"
                  startIcon={<FilterList />}
                  onClick={(e) => setStatusFilter(statusFilter === 'all' ? 'paid' : 'all')}
                  sx={{ minWidth: 120 }}
                >
                  {statusFilter === 'all' ? 'All Statuses' : `Filter: ${statusFilter}`}
                </CustomButton>
                <CustomButton
                  variant="contained"
                  startIcon={<Add />}
                  sx={{ minWidth: 180 }}
                >
                  Create Invoice
                </CustomButton>
              </Box>
            </Box>
          </motion.div>

          {/* Invoices Table */}
          <motion.div variants={itemVariants}>
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent sx={{ p: 0 }}>
                <TableContainer component={Paper} sx={{ boxShadow: 'none', borderRadius: '12px' }}>
                  <Table>
                    <TableHead>
                      <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
                        <TableCell>Invoice #</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Due Date</TableCell>
                        <TableCell>Customer</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredInvoices.map((invoice) => (
                        <TableRow 
                          key={invoice.id}
                          hover
                          sx={{ 
                            '&:last-child td, &:last-child th': { border: 0 },
                            transition: 'background-color 0.3s',
                            '&:hover': { backgroundColor: alpha(theme.palette.primary.main, 0.05) }
                          }}
                        >
                          <TableCell component="th" scope="row">
                            <Typography variant="body2" fontWeight="medium">
                              {invoice.id}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {format(new Date(invoice.date), 'dd MMM yyyy')}
                          </TableCell>
                          <TableCell>
                            {format(new Date(invoice.dueDate), 'dd MMM yyyy')}
                          </TableCell>
                          <TableCell>{invoice.customer}</TableCell>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              R{invoice.amount.toLocaleString()}
                            </Typography>
                          </TableCell>
                          <TableCell>{getStatusChip(invoice.status)}</TableCell>
                          <TableCell align="right">
                            <Tooltip title="View">
                              <IconButton size="small" sx={{ mr: 1 }}>
                                <Visibility fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="More Actions">
                              <IconButton 
                                size="small"
                                onClick={(e) => handleMenuOpen(e, invoice.id)}
                              >
                                <MoreVert fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {filteredInvoices.length === 0 && (
                  <Box sx={{ py: 4, textAlign: 'center' }}>
                    <Typography variant="body1" color="text.secondary">
                      No invoices found matching your criteria
                    </Typography>
                    <CustomButton
                      variant="outlined"
                      sx={{ mt: 2 }}
                      onClick={() => {
                        setSearchQuery('');
                        setStatusFilter('all');
                      }}
                    >
                      Clear Filters
                    </CustomButton>
                  </Box>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              elevation: 3,
              sx: { borderRadius: '8px', minWidth: '180px' }
            }}
          >
            <MenuItem onClick={handleMenuClose}>
              <Edit fontSize="small" sx={{ mr: 1.5 }} />
              Edit Invoice
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <Print fontSize="small" sx={{ mr: 1.5 }} />
              Print Invoice
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <Download fontSize="small" sx={{ mr: 1.5 }} />
              Download PDF
            </MenuItem>
            <MenuItem onClick={handleMenuClose} sx={{ color: theme.palette.error.main }}>
              <Delete fontSize="small" sx={{ mr: 1.5 }} />
              Delete Invoice
            </MenuItem>
          </Menu>
        </motion.div>
      </Box>
    </Box>
  );
};

export default Invoices;
