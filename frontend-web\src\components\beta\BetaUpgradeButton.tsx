import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Card,
  CardContent,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha
} from '@mui/material';
import {
  Upgrade,
  Star,
  CheckCircle,
  Close
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useSubscription } from '../../contexts/SubscriptionContext';

interface BetaUpgradeButtonProps {
  variant?: 'sidebar' | 'inline' | 'floating';
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

const BetaUpgradeButton: React.FC<BetaUpgradeButtonProps> = ({
  variant = 'sidebar',
  size = 'medium',
  showText = true
}) => {
  const theme = useTheme();
  const { subscription } = useSubscription();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);

  // Only show for beta users
  if (subscription?.plan?.name !== 'beta') {
    return null;
  }

  const plans = [
    {
      name: 'Professional',
      price: 'R299',
      period: '/month',
      color: theme.palette.primary.main,
      features: [
        'Up to 200 animals',
        'Advanced analytics',
        'Financial intelligence',
        'Priority support',
        'Data export (all formats)',
        'Mobile app access'
      ],
      savings: 'Save R8,000+ annually',
      popular: false
    },
    {
      name: 'Enterprise',
      price: 'R599',
      period: '/month',
      color: theme.palette.secondary.main,
      features: [
        'Unlimited animals',
        'AI agent mode',
        'Advanced analytics',
        'Financial intelligence',
        '24/7 priority support',
        'Custom integrations',
        'Dedicated account manager',
        'Training sessions'
      ],
      savings: 'Save R15,000+ annually',
      popular: true
    }
  ];

  const handleUpgrade = (planName: string) => {
    // Handle upgrade logic here
    console.log(`Upgrading to ${planName}`);
    setShowUpgradeDialog(false);
    // In a real app, this would redirect to payment processing
  };

  const buttonContent = () => {
    switch (variant) {
      case 'sidebar':
        return (
          <Button
            variant="contained"
            color="secondary"
            size={size}
            startIcon={<Upgrade />}
            onClick={() => setShowUpgradeDialog(true)}
            sx={{
              width: '100%',
              borderRadius: 2,
              fontWeight: 'bold',
              background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
              boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.3)}`,
              '&:hover': {
                background: `linear-gradient(45deg, ${theme.palette.secondary.dark}, ${theme.palette.secondary.main})`,
                transform: 'translateY(-2px)',
                boxShadow: `0 6px 16px ${alpha(theme.palette.secondary.main, 0.4)}`
              },
              transition: 'all 0.3s ease'
            }}
          >
            {showText && 'Upgrade Now'}
          </Button>
        );

      case 'floating':
        return (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Button
              variant="contained"
              color="secondary"
              size={size}
              onClick={() => setShowUpgradeDialog(true)}
              sx={{
                borderRadius: '50%',
                minWidth: 56,
                height: 56,
                position: 'fixed',
                bottom: 24,
                right: 24,
                zIndex: 1000,
                background: `linear-gradient(45deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                boxShadow: `0 8px 24px ${alpha(theme.palette.secondary.main, 0.4)}`,
                '&:hover': {
                  background: `linear-gradient(45deg, ${theme.palette.secondary.dark}, ${theme.palette.secondary.main})`,
                  boxShadow: `0 12px 32px ${alpha(theme.palette.secondary.main, 0.5)}`
                }
              }}
            >
              <Upgrade />
            </Button>
          </motion.div>
        );

      default:
        return (
          <Button
            variant="contained"
            color="secondary"
            size={size}
            startIcon={<Upgrade />}
            onClick={() => setShowUpgradeDialog(true)}
            sx={{
              borderRadius: 2,
              fontWeight: 'bold'
            }}
          >
            {showText && 'Upgrade'}
          </Button>
        );
    }
  };

  return (
    <>
      {buttonContent()}

      {/* Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`,
            backdropFilter: 'blur(20px)'
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Star sx={{ color: 'secondary.main', mr: 1 }} />
              <Typography variant="h5" fontWeight="bold">
                Upgrade Your Plan
              </Typography>
            </Box>
            <Button
              onClick={() => setShowUpgradeDialog(false)}
              sx={{ minWidth: 'auto', p: 1 }}
            >
              <Close />
            </Button>
          </Box>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Unlock the full potential of AMPD Livestock Management
          </Typography>
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mb: 3, p: 2, borderRadius: 2, backgroundColor: alpha(theme.palette.success.main, 0.1) }}>
            <Typography variant="h6" color="success.main" fontWeight="bold" sx={{ mb: 1 }}>
              🎉 Special Beta Offer!
            </Typography>
            <Typography variant="body2">
              First month FREE + 20% off your first year when you upgrade now!
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {plans.map((plan) => (
              <Grid item xs={12} md={6} key={plan.name}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      border: plan.popular ? `2px solid ${plan.color}` : `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                      borderRadius: 3,
                      position: 'relative',
                      background: plan.popular 
                        ? `linear-gradient(135deg, ${alpha(plan.color, 0.05)}, ${alpha(plan.color, 0.02)})`
                        : 'transparent'
                    }}
                  >
                    {plan.popular && (
                      <Chip
                        label="Most Popular"
                        color="secondary"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: -8,
                          left: '50%',
                          transform: 'translateX(-50%)',
                          fontWeight: 'bold'
                        }}
                      />
                    )}

                    <CardContent sx={{ p: 3 }}>
                      <Typography variant="h6" fontWeight="bold" sx={{ mb: 1, color: plan.color }}>
                        {plan.name}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'baseline', mb: 1 }}>
                        <Typography variant="h3" fontWeight="bold" sx={{ color: plan.color }}>
                          {plan.price}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                          {plan.period}
                        </Typography>
                      </Box>

                      <Typography variant="body2" color="success.main" fontWeight="bold" sx={{ mb: 2 }}>
                        {plan.savings}
                      </Typography>

                      <List dense sx={{ mb: 2 }}>
                        {plan.features.map((feature, index) => (
                          <ListItem key={index} sx={{ px: 0, py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText 
                              primary={feature}
                              primaryTypographyProps={{ fontSize: '0.9rem' }}
                            />
                          </ListItem>
                        ))}
                      </List>

                      <Button
                        variant={plan.popular ? 'contained' : 'outlined'}
                        color="secondary"
                        fullWidth
                        size="large"
                        onClick={() => handleUpgrade(plan.name)}
                        sx={{
                          borderRadius: 2,
                          fontWeight: 'bold',
                          py: 1.5
                        }}
                      >
                        Choose {plan.name}
                      </Button>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1 }}>
          <Typography variant="body2" color="text.secondary" sx={{ flex: 1 }}>
            Cancel anytime. No hidden fees. 30-day money-back guarantee.
          </Typography>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Maybe Later
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BetaUpgradeButton;
