import { generateMockData, randomDate, random<PERSON>umber, randomChoice } from '../utils/mockDataGenerator';
import { mockAnimals } from './animalData';
import { HealthRecord, Vaccination, HealthStats } from '../types/health';

const recordTypes = ['vaccination', 'treatment', 'checkup', 'surgery', 'quarantine', 'dipping'];
const statuses = ['completed', 'scheduled', 'cancelled'];
// South African specific health issues and treatments
const diagnoses = ['Healthy', 'Tick-borne disease', 'Heartwater', 'Redwater', 'Corridor disease', 'Lumpy skin disease', 'Foot rot', 'Blue tongue', 'Parasites', 'Digestive issue', null];
const treatments = ['Antibiotics', 'Anti-inflammatory', 'Deworming', 'Dipping', 'Hoof trimming', 'Dietary adjustment', 'None required'];
const medications = ['Penicillin', 'Ivermectin', 'Flunixin', 'Terramycin', 'Oxytetracycline', 'Alamycin', null];
const veterinarians = ['<PERSON><PERSON> <PERSON>', 'Dr. <PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>. <PERSON><PERSON><PERSON>'];

export const mockHealthRecords: HealthRecord[] = generateMockData(4, (i) => {
  const recordType = randomChoice(recordTypes);
  const recordDate = randomDate(new Date(2022, 0, 1), new Date());
  const animalId = randomNumber(1, mockAnimals.length);
  const status = randomChoice(statuses);

  return {
    id: `HR${i.toString().padStart(3, '0')}`,
    animalId,
    date: recordDate.toISOString().split('T')[0],
    type: recordType,
    description: `${recordType} for animal #${animalId}`,
    performedBy: randomChoice(veterinarians),
    cost: recordType === 'checkup' ? randomNumber(50, 150) : randomNumber(100, 500),
    notes: `${recordType} for animal #${animalId}`,
    followUpDate: status === 'cancelled' ? new Date(recordDate.getTime() + randomNumber(7, 30) * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
    status
  };
});

export const mockHealthStats: HealthStats = {
  totalRecords: mockHealthRecords.length,
  byType: {
    vaccination: mockHealthRecords.filter(r => r.type === 'vaccination').length,
    treatment: mockHealthRecords.filter(r => r.type === 'treatment').length,
    checkup: mockHealthRecords.filter(r => r.type === 'checkup').length,
    surgery: mockHealthRecords.filter(r => r.type === 'surgery').length,
    quarantine: mockHealthRecords.filter(r => r.type === 'quarantine').length
  },
  byStatus: {
    completed: mockHealthRecords.filter(r => r.status === 'completed').length,
    scheduled: mockHealthRecords.filter(r => r.status === 'scheduled').length,
    requiresFollowup: mockHealthRecords.filter(r => r.status === 'cancelled').length,
    cancelled: mockHealthRecords.filter(r => r.status === 'cancelled').length
  },
  pendingCount: mockHealthRecords.filter(r => r.status !== 'completed').length
};

// Generate mock vaccination records
export const mockVaccinations: Vaccination[] = generateMockData(4, (i) => {
  const animalId = randomNumber(1, mockAnimals.length);
  const date = randomDate(new Date(2022, 0, 1), new Date());
  const expiryDate = new Date(date);
  expiryDate.setFullYear(expiryDate.getFullYear() + 1);

  return {
    id: `VAC${i.toString().padStart(3, '0')}`,
    animalId,
    vaccineType: randomChoice(['Anthrax', 'Blackleg', 'Botulism', 'Brucellosis', 'Lumpy skin disease', 'Rift Valley fever', 'Blue tongue']),
    date: date.toISOString().split('T')[0],
    expiryDate: expiryDate.toISOString().split('T')[0],
    batchNumber: `B${randomNumber(1000, 9999)}`,
    administeredBy: randomChoice(veterinarians),
    notes: randomChoice(['Compulsory vaccination', 'Booster shot', 'Initial vaccination', 'Required for movement permit', 'State veterinary service vaccination'])
  };
});

