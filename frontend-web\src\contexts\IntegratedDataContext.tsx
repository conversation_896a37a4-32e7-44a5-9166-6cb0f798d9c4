import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { dataIntegrationService, dataChangeEmitter, DataChangeEventType } from '../services/dataIntegrationService';
import { Animal } from '../types/animal';
import { AnimalStats } from '../hooks/useAnimalData';
import { Transaction } from '../mocks/financialData';

// Define the context type
interface IntegratedDataContextType {
  // Animal data
  animals: Animal[];
  animalStats: AnimalStats;

  // Financial data
  transactions: Transaction[];
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;

  // Loading and error states
  loading: boolean;
  error: Error | null;

  // CRUD operations
  createAnimal: (animal: Omit<Animal, 'id'>) => Promise<Animal>;
  updateAnimal: (id: string, animal: Partial<Animal>) => Promise<Animal>;
  deleteAnimal: (id: string) => Promise<boolean>;

  createTransaction: (transaction: Omit<Transaction, 'id'>) => Promise<Transaction>;
  updateTransaction: (id: string, transaction: Partial<Transaction>) => Promise<Transaction>;
  deleteTransaction: (id: string) => Promise<boolean>;

  // Refresh data
  refreshData: () => Promise<void>;
}

// Create the context
const IntegratedDataContext = createContext<IntegratedDataContextType | undefined>(undefined);

// Create the provider component
export const IntegratedDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // State for animal data
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [animalStats, setAnimalStats] = useState<AnimalStats>({
    totalAnimals: 0,
    healthPercentage: 0,
    pendingCheckups: 0,
    activeAnimals: 0,
    recentAdditions: 0,
    bySpecies: {},
    byStatus: {},
    byLocation: {},
    byHealth: {
      healthy: 0,
      sick: 0,
      injured: 0,
      pregnant: 0
    }
  });

  // State for financial data
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalRevenue, setTotalRevenue] = useState<number>(0);
  const [totalExpenses, setTotalExpenses] = useState<number>(0);
  const [netProfit, setNetProfit] = useState<number>(0);

  // Loading and error states
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Load initial data
  useEffect(() => {
    loadData();

    // Listen for data change events
    const handleDataChange = async (event: any) => {
      // Refresh data when changes occur
      await loadData();
    };

    dataChangeEmitter.on('dataChange', handleDataChange);

    return () => {
      dataChangeEmitter.off('dataChange', handleDataChange);
    };
  }, []);

  // Load all data
  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load animals
      await loadAnimals();

      // Load animal stats
      await loadAnimalStats();

      // Load financial data
      await loadFinancialData();
    } catch (err) {
      setError(err as Error);
      console.error('Error loading data:', err);
    } finally {
      setLoading(false);
    }
  };

  // Load animals
  const loadAnimals = async () => {
    try {
      const animalsData = await dataIntegrationService.getDocuments('animals', {}, 5);
      setAnimals(animalsData.map(convertMongoAnimalToAnimal));
    } catch (err) {
      console.error('Error loading animals:', err);
      throw err;
    }
  };

  // Load animal stats
  const loadAnimalStats = async () => {
    try {
      const statsData = await dataIntegrationService.getDocument('dashboard_stats', 'animal_stats');

      if (statsData) {
        setAnimalStats({
          totalAnimals: statsData.totalAnimals || 0,
          healthPercentage: statsData.healthPercentage || 0,
          pendingCheckups: statsData.pendingCheckups || 0,
          activeAnimals: statsData.activeAnimals || 0,
          recentAdditions: statsData.recentAdditions || 0,
          bySpecies: statsData.bySpecies || {},
          byStatus: statsData.byStatus || {},
          byLocation: statsData.byLocation || {},
          byHealth: statsData.byHealth || {
            healthy: 0,
            sick: 0,
            injured: 0,
            pregnant: 0
          }
        });
      }
    } catch (err) {
      console.error('Error loading animal stats:', err);
      throw err;
    }
  };

  // Load financial data
  const loadFinancialData = async () => {
    try {
      // Load transactions
      const transactionsData = await dataIntegrationService.getDocuments('financial_records', {}, 5);
      setTransactions(transactionsData.map(convertMongoTransactionToTransaction));

      // Load financial stats
      const statsData = await dataIntegrationService.getDocument('dashboard_stats', 'financial_stats');

      if (statsData) {
        setTotalRevenue(statsData.totalRevenue || 0);
        setTotalExpenses(statsData.totalExpenses || 0);
        setNetProfit(statsData.netProfit || 0);
      }
    } catch (err) {
      console.error('Error loading financial data:', err);
      throw err;
    }
  };

  // Create animal
  const createAnimal = async (animal: Omit<Animal, 'id'>): Promise<Animal> => {
    try {
      const createdAnimal = await dataIntegrationService.createDocument('animals', animal);
      return convertMongoAnimalToAnimal(createdAnimal);
    } catch (err) {
      console.error('Error creating animal:', err);
      throw err;
    }
  };

  // Update animal
  const updateAnimal = async (id: string, animal: Partial<Animal>): Promise<Animal> => {
    try {
      const updatedAnimal = await dataIntegrationService.updateDocument('animals', id, animal);
      return convertMongoAnimalToAnimal(updatedAnimal);
    } catch (err) {
      console.error('Error updating animal:', err);
      throw err;
    }
  };

  // Delete animal
  const deleteAnimal = async (id: string): Promise<boolean> => {
    try {
      return await dataIntegrationService.deleteDocument('animals', id);
    } catch (err) {
      console.error('Error deleting animal:', err);
      throw err;
    }
  };

  // Create transaction
  const createTransaction = async (transaction: Omit<Transaction, 'id'>): Promise<Transaction> => {
    try {
      const createdTransaction = await dataIntegrationService.createDocument('financial_records', transaction);
      return convertMongoTransactionToTransaction(createdTransaction);
    } catch (err) {
      console.error('Error creating transaction:', err);
      throw err;
    }
  };

  // Update transaction
  const updateTransaction = async (id: string, transaction: Partial<Transaction>): Promise<Transaction> => {
    try {
      const updatedTransaction = await dataIntegrationService.updateDocument('financial_records', id, transaction);
      return convertMongoTransactionToTransaction(updatedTransaction);
    } catch (err) {
      console.error('Error updating transaction:', err);
      throw err;
    }
  };

  // Delete transaction
  const deleteTransaction = async (id: string): Promise<boolean> => {
    try {
      return await dataIntegrationService.deleteDocument('financial_records', id);
    } catch (err) {
      console.error('Error deleting transaction:', err);
      throw err;
    }
  };

  // Refresh data
  const refreshData = async (): Promise<void> => {
    await loadData();
  };

  // Convert MongoDB animal to Animal type
  const convertMongoAnimalToAnimal = (mongoAnimal: any): Animal => {
    return {
      id: mongoAnimal._id.toString(),
      tagNumber: mongoAnimal.tagNumber || '',
      name: mongoAnimal.name || '',
      type: mongoAnimal.type || '',
      breed: mongoAnimal.breed || '',
      gender: mongoAnimal.gender || '',
      birthDate: mongoAnimal.birthDate || '',
      weight: mongoAnimal.weight || 0,
      status: mongoAnimal.status || 'Active',
      healthStatus: mongoAnimal.healthStatus || 'healthy',
      location: mongoAnimal.location || '',
      purchaseDate: mongoAnimal.purchaseDate || '',
      purchasePrice: mongoAnimal.purchasePrice || 0,
      notes: mongoAnimal.notes || '',
      parentId: mongoAnimal.parentId || '',
      species: mongoAnimal.species || mongoAnimal.type || '',
      imageUrl: mongoAnimal.imageUrl || '',
      rfidTag: mongoAnimal.rfidTag || ''
    };
  };

  // Convert MongoDB transaction to Transaction type
  const convertMongoTransactionToTransaction = (mongoTransaction: any): Transaction => {
    return {
      id: mongoTransaction._id.toString(),
      date: new Date(mongoTransaction.date),
      type: mongoTransaction.type,
      category: mongoTransaction.category,
      amount: mongoTransaction.amount,
      description: mongoTransaction.description,
      paymentMethod: mongoTransaction.paymentMethod,
      status: mongoTransaction.status,
      relatedEntity: mongoTransaction.relatedEntity,
      attachments: mongoTransaction.attachments,
      notes: mongoTransaction.notes
    };
  };

  // Context value
  const value: IntegratedDataContextType = {
    animals,
    animalStats,
    transactions,
    totalRevenue,
    totalExpenses,
    netProfit,
    loading,
    error,
    createAnimal,
    updateAnimal,
    deleteAnimal,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    refreshData
  };

  return (
    <IntegratedDataContext.Provider value={value}>
      {children}
    </IntegratedDataContext.Provider>
  );
};

// Custom hook to use the integrated data context
export const useIntegratedData = () => {
  const context = useContext(IntegratedDataContext);
  if (context === undefined) {
    throw new Error('useIntegratedData must be used within an IntegratedDataProvider');
  }
  return context;
};

// Custom hooks for specific data
export const useIntegratedAnimalData = () => {
  const {
    animals,
    animalStats,
    loading,
    error,
    createAnimal,
    updateAnimal,
    deleteAnimal,
    refreshData
  } = useIntegratedData();

  return {
    animals,
    stats: animalStats,
    loading,
    error,
    createAnimal,
    updateAnimal,
    deleteAnimal,
    refreshData
  };
};

export const useIntegratedFinancialData = () => {
  const {
    transactions,
    totalRevenue,
    totalExpenses,
    netProfit,
    loading,
    error,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    refreshData
  } = useIntegratedData();

  return {
    transactions,
    stats: {
      totalRevenue,
      totalExpenses,
      netProfit
    },
    loading,
    error,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    refreshData
  };
};
