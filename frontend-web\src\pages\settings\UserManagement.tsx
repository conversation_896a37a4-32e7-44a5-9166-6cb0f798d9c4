import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Paper, TextField, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, SelectChangeEvent, MenuItem, Chip, FormControlLabel, Checkbox, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Alert, CircularProgress, useTheme, alpha } from '@mui/material';
import { Add, Edit, Delete, Person, Security } from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { User } from '../../types/user';
import { userMongoService } from '../../services/userMongoService';
import ModuleContainer from '../../components/common/ModuleContainer';

// Available permissions in the system
const availablePermissions = [
  { value: 'animals:read', label: 'Animals (View)' },
  { value: 'animals:write', label: 'Animals (Edit)' },
  { value: 'health:read', label: 'Health (View)' },
  { value: 'health:write', label: 'Health (Edit)' },
  { value: 'breeding:read', label: 'Breeding (View)' },
  { value: 'breeding:write', label: 'Breeding (Edit)' },
  { value: 'feeding:read', label: 'Feeding (View)' },
  { value: 'feeding:write', label: 'Feeding (Edit)' },
  { value: 'financial:read', label: 'Financial (View)' },
  { value: 'financial:write', label: 'Financial (Edit)' },
  { value: 'reports:read', label: 'Reports (View)' },
  { value: 'reports:write', label: 'Reports (Edit)' },
  { value: 'settings:read', label: 'Settings (View)' },
  { value: 'settings:write', label: 'Settings (Edit)' },
  { value: 'users:read', label: 'Users (View)' },
  { value: 'users:write', label: 'Users (Edit)' },
  { value: 'all', label: 'All Permissions (Admin)' }
];

// Available roles
const roles = [
  { value: 'admin', label: 'Administrator' },
  { value: 'manager', label: 'Manager' },
  { value: 'super_user', label: 'Super User' },
  { value: 'staff', label: 'Staff' },
  { value: 'veterinarian', label: 'Veterinarian' },
  { value: 'viewer', label: 'Viewer (Read-only)' }
];

const UserManagement: React.FC = () => {
  const theme = useTheme();
  const { user: currentUser, hasPermission } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    firstName: '',
    lastName: '',
    role: 'viewer' as 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer',
    status: 'active' as 'active' | 'inactive' | 'suspended',
    permissions: [] as string[],
    phoneNumber: '',
    position: '',
    department: '',
    profileImage: ''
  });

  // Function to fetch users from MongoDB
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const data = await userMongoService.getAllUsers();
      setUsers(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching users from MongoDB:', err);
      setError('Failed to load users. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Check if current user has permission to manage users
  const canManageUsers = hasPermission('users:write') || (currentUser?.role === 'admin');

  // Handle dialog open for adding new user
  const handleAddUser = () => {
    setEditingUser(null);
    setFormData({
      username: '',
      password: '',
      email: '',
      firstName: '',
      lastName: '',
      role: 'viewer' as 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer',
      status: 'active' as 'active' | 'inactive' | 'suspended',
      permissions: ['animals:read'],
      phoneNumber: '',
      position: '',
      department: '',
      profileImage: ''
    });
    setOpenDialog(true);
  };

  // Handle dialog open for editing user
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      password: '', // Don't populate password for security
      email: user.email || '',
      firstName: user.firstName || '',
      lastName: user.lastName || '',
      role: user.role,
      status: user.status || 'active',
      permissions: user.permissions || [],
      phoneNumber: user.phoneNumber || '',
      position: user.position || '',
      department: user.department || '',
      profileImage: user.profileImage || ''
    });
    setOpenDialog(true);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Handle role change
  const handleRoleChange = (e: SelectChangeEvent<string>) => {
    const role = e.target.value;

    // Set default permissions based on role
    let permissions: string[] = [];

    switch (role) {
      case 'admin':
        permissions = ['all'];
        break;
      case 'manager':
        permissions = [
          'animals:read', 'animals:write',
          'health:read', 'health:write',
          'breeding:read', 'breeding:write',
          'feeding:read', 'feeding:write',
          'financial:read', 'financial:write',
          'reports:read', 'reports:write'
        ];
        break;
      case 'super_user':
        permissions = [
          'animals:read', 'animals:write',
          'health:read', 'health:write',
          'breeding:read', 'breeding:write',
          'feeding:read', 'feeding:write',
          'financial:read', 'reports:read'
        ];
        break;
      case 'staff':
        permissions = [
          'animals:read', 'animals:write',
          'health:read', 'breeding:read',
          'feeding:read', 'reports:read'
        ];
        break;
      case 'veterinarian':
        permissions = [
          'animals:read', 'health:read', 'health:write',
          'breeding:read', 'reports:read'
        ];
        break;
      case 'viewer':
        permissions = ['animals:read', 'reports:read'];
        break;
      default:
        permissions = [];
    }

    setFormData({
      ...formData,
      role: role as 'admin' | 'manager' | 'super_user' | 'staff' | 'veterinarian' | 'viewer',
      permissions
    });
  };

  // Handle permission toggle
  const handlePermissionToggle = (permission: string) => {
    const newPermissions = formData.permissions.includes(permission)
      ? formData.permissions.filter(p => p !== permission)
      : [...formData.permissions, permission];

    setFormData({
      ...formData,
      permissions: newPermissions
    });
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);

      if (editingUser) {
        // Update existing user
        const updatedUser = await userMongoService.updateUser(editingUser.id, {
          ...formData,
          updatedAt: new Date()
        });

        // Update local state
        setUsers(users.map(u =>
          u.id === editingUser.id ? updatedUser : u
        ));

        setSuccessMessage('User updated successfully');
      } else {
        // Create new user
        const newUser = await userMongoService.createUser({
          ...formData,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        // Update local state
        setUsers([...users, newUser]);
        setSuccessMessage('User created successfully');
      }

      // Refresh users from MongoDB to ensure we have the latest data
      fetchUsers();
      setOpenDialog(false);
    } catch (err) {
      console.error('Error saving user to MongoDB:', err);
      setError(`Failed to save user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) {
      return;
    }

    try {
      setLoading(true);
      const success = await userMongoService.deleteUser(userId);

      if (success) {
        // Update local state
        setUsers(users.filter(u => u.id !== userId));
        setSuccessMessage('User deleted successfully');
      } else {
        setError('Failed to delete user. User not found.');
      }
    } catch (err) {
      console.error('Error deleting user from MongoDB:', err);
      setError(`Failed to delete user: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle pagination
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Clear success message after 3 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  return (
    <ModuleContainer module="settings">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            User Management
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Manage users and their access rights to the AMPD Livestock Management system.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {successMessage && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {successMessage}
            </Alert>
          )}

          {canManageUsers && (
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddUser}
              sx={{ mb: 3 }}
            >
              Add New User
            </CustomButton>
          )}

          <Paper
            sx={{
              p: 0,
              borderRadius: 2,
              overflow: 'hidden',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
            }}
          >
            {loading && users.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                      <TableRow>
                        <TableCell>Username</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Email</TableCell>
                        <TableCell>Role</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Department</TableCell>
                        <TableCell>Last Login</TableCell>
                        {canManageUsers && <TableCell align="right">Actions</TableCell>}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {users
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((user) => (
                          <TableRow key={user.id} hover>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <Person sx={{ mr: 1, color: 'text.secondary' }} />
                                {user.username}
                              </Box>
                            </TableCell>
                            <TableCell>
                              {user.firstName} {user.lastName}
                            </TableCell>
                            <TableCell>
                              {user.email}
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={user.role}
                                color={
                                  user.role === 'admin' ? 'error' :
                                  user.role === 'manager' ? 'warning' :
                                  user.role === 'super_user' ? 'success' :
                                  user.role === 'staff' ? 'primary' :
                                  user.role === 'veterinarian' ? 'info' : 'default'
                                }
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={user.status || 'active'}
                                color={
                                  user.status === 'active' ? 'success' :
                                  user.status === 'inactive' ? 'default' : 'error'
                                }
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              {user.department || '-'}
                            </TableCell>
                            <TableCell>
                              {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                            </TableCell>
                            {canManageUsers && (
                              <TableCell align="right">
                                <IconButton
                                  color="primary"
                                  onClick={() => handleEditUser(user)}
                                  disabled={loading}
                                  size="small"
                                >
                                  <Edit />
                                </IconButton>
                                {user.id !== currentUser?.id && (
                                  <IconButton
                                    color="error"
                                    onClick={() => handleDeleteUser(user.id)}
                                    disabled={loading}
                                    size="small"
                                  >
                                    <Delete />
                                  </IconButton>
                                )}
                              </TableCell>
                            )}
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={users.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </>
            )}
          </Paper>
        </Box>

        {/* User Form Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            {editingUser ? 'Edit User' : 'Add New User'}
          </DialogTitle>
          <DialogContent dividers>
            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
              <TextField
                label="Username"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                fullWidth
                required
                disabled={!!editingUser}
              />
              <TextField
                label="Password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleInputChange}
                fullWidth
                required={!editingUser}
                helperText={editingUser ? "Leave blank to keep current password" : ""}
              />
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
              <TextField
                label="First Name"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                fullWidth
              />
              <TextField
                label="Last Name"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                fullWidth
              />
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
              <TextField
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                fullWidth
              />
              <TextField
                label="Phone Number"
                name="phoneNumber"
                value={formData.phoneNumber || ''}
                onChange={handleInputChange}
                fullWidth
              />
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 2 }}>
              <TextField
                label="Position"
                name="position"
                value={formData.position || ''}
                onChange={handleInputChange}
                fullWidth
              />
              <TextField
                label="Department"
                name="department"
                value={formData.department || ''}
                onChange={handleInputChange}
                fullWidth
              />
            </Box>

            <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mb: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  name="role"
                  value={formData.role}
                  onChange={handleRoleChange}
                  label="Role"
                >
                  {roles.map((role) => (
                    <MenuItem key={role.value} value={role.value}>
                      {role.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                </Select>
              </FormControl>
            </Box>

            <Typography variant="subtitle1" gutterBottom>
              Permissions
            </Typography>

            {formData.role === 'admin' ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                Administrators automatically have all permissions.
              </Alert>
            ) : (
              <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1 }}>
                {availablePermissions
                  .filter(p => p.value !== 'all')
                  .map((permission) => (
                    <FormControlLabel
                      key={permission.value}
                      control={
                        <Checkbox
                          checked={formData.permissions.includes(permission.value)}
                          onChange={() => handlePermissionToggle(permission.value)}
                          disabled={formData.role === 'admin'}
                        />
                      }
                      label={permission.label}
                    />
                  ))}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <CustomButton onClick={() => setOpenDialog(false)}>
              Cancel
            </CustomButton>
            <CustomButton
              onClick={handleSubmit}
              variant="contained"
              disabled={loading || !formData.username}
            >
              {loading ? <CircularProgress size={24} /> : 'Save'}
            </CustomButton>
          </DialogActions>
        </Dialog>
      </motion.div>
    </ModuleContainer>
  );
};

export default UserManagement;
