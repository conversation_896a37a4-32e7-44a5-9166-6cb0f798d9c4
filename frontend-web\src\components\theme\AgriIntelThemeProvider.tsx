/**
 * AgriIntel Theme Provider
 * Comprehensive theme system with gradient themes and livestock branding
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// Theme types
export type ThemeVariant = 'nature' | 'earth' | 'sky' | 'sunset' | 'forest';
export type ThemeMode = 'light' | 'dark' | 'auto';

interface AgriIntelTheme {
  variant: ThemeVariant;
  mode: ThemeMode;
  gradients: Record<string, string>;
  colors: Record<string, string>;
}

interface ThemeContextType {
  theme: AgriIntelTheme;
  setThemeVariant: (variant: ThemeVariant) => void;
  setThemeMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
}

// Theme configurations
const themeVariants: Record<ThemeVariant, any> = {
  nature: {
    primary: '#2E7D32',
    primaryLight: '#4CAF50',
    primaryDark: '#1B5E20',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%)',
    name: 'Nature Green'
  },
  earth: {
    primary: '#8D6E63',
    primaryLight: '#A1887F',
    primaryDark: '#5D4037',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #8D6E63 0%, #A1887F 50%, #BCAAA4 100%)',
    name: 'Earth Brown'
  },
  sky: {
    primary: '#1976D2',
    primaryLight: '#42A5F5',
    primaryDark: '#0D47A1',
    secondary: '#FF8F00',
    gradient: 'linear-gradient(135deg, #1976D2 0%, #42A5F5 50%, #90CAF9 100%)',
    name: 'Sky Blue'
  },
  sunset: {
    primary: '#FF8F00',
    primaryLight: '#FFB74D',
    primaryDark: '#E65100',
    secondary: '#2E7D32',
    gradient: 'linear-gradient(135deg, #FF8F00 0%, #FFB74D 50%, #FFCC02 100%)',
    name: 'Sunset Orange'
  },
  forest: {
    primary: '#388E3C',
    primaryLight: '#66BB6A',
    primaryDark: '#2E7D32',
    secondary: '#8D6E63',
    gradient: 'linear-gradient(135deg, #388E3C 0%, #66BB6A 50%, #A5D6A7 100%)',
    name: 'Forest Green'
  }
};

// Create theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme provider component
export const AgriIntelThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeVariant, setThemeVariant] = useState<ThemeVariant>('nature');
  const [themeMode, setThemeMode] = useState<ThemeMode>('light');

  // Load theme from localStorage
  useEffect(() => {
    const savedVariant = localStorage.getItem('agri-theme-variant') as ThemeVariant;
    const savedMode = localStorage.getItem('agri-theme-mode') as ThemeMode;
    
    if (savedVariant && themeVariants[savedVariant]) {
      setThemeVariant(savedVariant);
    }
    
    if (savedMode) {
      setThemeMode(savedMode);
    } else if (savedMode === 'auto') {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setThemeMode(prefersDark ? 'dark' : 'light');
    }
  }, []);

  // Save theme to localStorage
  useEffect(() => {
    localStorage.setItem('agri-theme-variant', themeVariant);
    localStorage.setItem('agri-theme-mode', themeMode);
  }, [themeVariant, themeMode]);

  // Create MUI theme
  const muiTheme = createTheme({
    palette: {
      mode: themeMode === 'auto' ? 
        (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : 
        themeMode,
      primary: {
        main: themeVariants[themeVariant].primary,
        light: themeVariants[themeVariant].primaryLight,
        dark: themeVariants[themeVariant].primaryDark,
      },
      secondary: {
        main: themeVariants[themeVariant].secondary,
      },
      background: {
        default: themeMode === 'dark' ? '#121212' : '#f5f5f5',
        paper: themeMode === 'dark' ? '#1e1e1e' : '#ffffff',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", sans-serif',
      h1: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 700,
      },
      h2: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h3: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 600,
      },
      h4: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h5: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
      h6: {
        fontFamily: '"Poppins", "Inter", sans-serif',
        fontWeight: 500,
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiCard: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 600,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
            },
          },
          contained: {
            background: themeVariants[themeVariant].primary,
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            '&:hover': {
              background: themeVariants[themeVariant].primaryDark,
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 8,
              background: 'rgba(255, 255, 255, 0.9)',
              transition: 'all 0.3s ease',
              '&:hover': {
                background: 'rgba(255, 255, 255, 1)',
              },
              '&.Mui-focused': {
                background: 'rgba(255, 255, 255, 1)',
                boxShadow: `0 0 0 3px ${themeVariants[themeVariant].primary}20`,
              },
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
  });

  // Theme context value
  const themeContextValue: ThemeContextType = {
    theme: {
      variant: themeVariant,
      mode: themeMode,
      gradients: {
        primary: themeVariants[themeVariant].gradient,
        nature: themeVariants.nature.gradient,
        earth: themeVariants.earth.gradient,
        sky: themeVariants.sky.gradient,
        sunset: themeVariants.sunset.gradient,
        forest: themeVariants.forest.gradient,
      },
      colors: {
        primary: themeVariants[themeVariant].primary,
        primaryLight: themeVariants[themeVariant].primaryLight,
        primaryDark: themeVariants[themeVariant].primaryDark,
        secondary: themeVariants[themeVariant].secondary,
      },
    },
    setThemeVariant: (variant: ThemeVariant) => {
      setThemeVariant(variant);
    },
    setThemeMode: (mode: ThemeMode) => {
      setThemeMode(mode);
    },
    toggleMode: () => {
      setThemeMode(prev => prev === 'light' ? 'dark' : 'light');
    },
  };

  // Apply theme to body
  useEffect(() => {
    const body = document.body;
    body.style.background = themeVariants[themeVariant].gradient;
    body.style.backgroundAttachment = 'fixed';
    body.style.minHeight = '100vh';
    
    // Apply theme class
    body.className = `theme-${themeVariant} mode-${themeMode}`;
    
    return () => {
      body.style.background = '';
      body.style.backgroundAttachment = '';
      body.className = '';
    };
  }, [themeVariant, themeMode]);

  return (
    <ThemeContext.Provider value={themeContextValue}>
      <ThemeProvider theme={muiTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};

// Hook to use theme
export const useAgriIntelTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useAgriIntelTheme must be used within an AgriIntelThemeProvider');
  }
  return context;
};

// Theme selector component
export const ThemeSelector: React.FC = () => {
  const { theme, setThemeVariant, setThemeMode, toggleMode } = useAgriIntelTheme();

  return (
    <div className="theme-selector">
      <h4>Theme Variant</h4>
      <div className="theme-variants">
        {Object.entries(themeVariants).map(([key, variant]) => (
          <button
            key={key}
            className={`theme-variant-btn ${theme.variant === key ? 'active' : ''}`}
            onClick={() => setThemeVariant(key as ThemeVariant)}
            style={{ background: variant.gradient }}
          >
            {variant.name}
          </button>
        ))}
      </div>
      
      <h4>Theme Mode</h4>
      <div className="theme-modes">
        <button
          className={`theme-mode-btn ${theme.mode === 'light' ? 'active' : ''}`}
          onClick={() => setThemeMode('light')}
        >
          Light
        </button>
        <button
          className={`theme-mode-btn ${theme.mode === 'dark' ? 'active' : ''}`}
          onClick={() => setThemeMode('dark')}
        >
          Dark
        </button>
        <button
          className={`theme-mode-btn ${theme.mode === 'auto' ? 'active' : ''}`}
          onClick={() => setThemeMode('auto')}
        >
          Auto
        </button>
      </div>
    </div>
  );
};

export default AgriIntelThemeProvider;
