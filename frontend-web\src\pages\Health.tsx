import React from 'react';
import { Routes, Route } from 'react-router-dom';
import HealthDashboard from './health/HealthDashboard';
import Vaccinations from './health/Vaccinations';
import Diseases from './health/Diseases';
import Treatments from './health/Treatments';
import Appointments from './health/Appointments';
import HealthRecords from './health/HealthRecords';

const Health: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<HealthDashboard />} />
      <Route path="/vaccinations" element={<Vaccinations />} />
      <Route path="/diseases" element={<Diseases />} />
      <Route path="/treatments" element={<Treatments />} />
      <Route path="/appointments" element={<Appointments />} />
      <Route path="/records" element={<HealthRecords />} />
    </Routes>
  );
};

export default Health;
