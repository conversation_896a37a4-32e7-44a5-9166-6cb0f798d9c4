export interface FinancialRecord {
  id: string;
  date: Date;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  description: string;
  relatedEntityId?: string;
  relatedEntityType?: string;
  paymentMethod?: string;
  reference?: string;
  status?: 'pending' | 'completed' | 'cancelled';
  createdBy?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FinancialSummary {
  totalRevenue: number;
  totalExpenses: number;
  netProfit: number;
  roi: number;
  revenueByCategory: Record<string, number>;
  expensesByCategory: Record<string, number>;
  monthlyTrends: {
    month: string;
    revenue: number;
    expenses: number;
    profit: number;
  }[];
}

export interface Budget {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  categories: {
    name: string;
    budgeted: number;
    actual: number;
  }[];
  totalBudgeted: number;
  totalActual: number;
  status: 'active' | 'completed' | 'draft';
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  date: Date;
  dueDate: Date;
  customer: {
    id: string;
    name: string;
    email: string;
    address: string;
  };
  items: {
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }[];
  subtotal: number;
  tax: number;
  total: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  notes?: string;
}

export interface Payment {
  id: string;
  date: Date;
  amount: number;
  method: string;
  reference: string;
  relatedInvoiceId?: string;
  notes?: string;
}
