/**
 * Utility functions to ensure data consistency across modules
 */

import { v4 as uuidv4 } from 'uuid';

/**
 * Generates a unique ID for data records
 * @returns A unique ID string
 */
export const generateUniqueId = (): string => {
  return uuidv4();
};

/**
 * Ensures that data arrays have unique IDs
 * @param data The data array to check
 * @param idField The field name for the ID (default: 'id')
 * @returns The data array with unique IDs
 */
export const ensureUniqueIds = <T extends Record<string, any>>(
  data: T[],
  idField: string = 'id'
): T[] => {
  const idSet = new Set<string>();
  
  return data.map(item => {
    // If the item doesn't have an ID or the ID is already used, generate a new one
    if (!item[idField] || idSet.has(item[idField])) {
      return {
        ...item,
        [idField]: generateUniqueId()
      };
    }
    
    // Otherwise, keep the existing ID
    idSet.add(item[idField]);
    return item;
  });
};

/**
 * Creates a deep copy of data with a module-specific prefix for IDs
 * This ensures that each module has its own unique data
 * @param data The data array to copy
 * @param modulePrefix The prefix to add to IDs
 * @param idField The field name for the ID (default: 'id')
 * @returns A new data array with module-specific IDs
 */
export const createModuleSpecificData = <T extends Record<string, any>>(
  data: T[],
  modulePrefix: string,
  idField: string = 'id'
): T[] => {
  return data.map(item => ({
    ...item,
    [idField]: `${modulePrefix}_${item[idField] || generateUniqueId()}`
  }));
};

/**
 * Generates random South African currency values
 * @param min Minimum value in Rands
 * @param max Maximum value in Rands
 * @param decimals Number of decimal places (default: 2)
 * @returns A random currency value as a string with R prefix
 */
export const generateRandomZARValue = (
  min: number,
  max: number,
  decimals: number = 2
): string => {
  const value = Math.random() * (max - min) + min;
  return `R ${value.toFixed(decimals)}`;
};

/**
 * Formats a number as South African currency (ZAR)
 * @param value The number to format
 * @returns A formatted currency string
 */
export const formatZAR = (value: number): string => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(value);
};

/**
 * Generates a random date within a range
 * @param start Start date
 * @param end End date
 * @returns A random date between start and end
 */
export const generateRandomDate = (start: Date, end: Date): Date => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

/**
 * Generates a random South African farm name
 * @returns A random farm name
 */
export const generateRandomFarmName = (): string => {
  const prefixes = ['Groot', 'Klein', 'Mooi', 'Bosveld', 'Kalahari', 'Karoo', 'Highveld', 'Lowveld', 'Bushveld'];
  const suffixes = ['Plaas', 'Fontein', 'Kraal', 'Vlei', 'Koppie', 'Rivier', 'Vallei', 'Hoek', 'Rand'];
  
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  
  return `${prefix} ${suffix}`;
};

/**
 * Generates a random South African location
 * @returns A random location in South Africa
 */
export const generateRandomSALocation = (): string => {
  const locations = [
    'Johannesburg, Gauteng',
    'Cape Town, Western Cape',
    'Durban, KwaZulu-Natal',
    'Pretoria, Gauteng',
    'Bloemfontein, Free State',
    'Port Elizabeth, Eastern Cape',
    'Nelspruit, Mpumalanga',
    'Kimberley, Northern Cape',
    'Polokwane, Limpopo',
    'Rustenburg, North West',
    'Stellenbosch, Western Cape',
    'Pietermaritzburg, KwaZulu-Natal'
  ];
  
  return locations[Math.floor(Math.random() * locations.length)];
};

/**
 * Generates a random South African phone number
 * @returns A random SA phone number
 */
export const generateRandomSAPhoneNumber = (): string => {
  const prefixes = ['060', '061', '062', '063', '064', '065', '066', '067', '068', '071', '072', '073', '074', '076', '078', '079', '081', '082', '083', '084'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  
  let number = '';
  for (let i = 0; i < 7; i++) {
    number += Math.floor(Math.random() * 10);
  }
  
  return `${prefix} ${number.substring(0, 3)} ${number.substring(3)}`;
};

/**
 * Generates a random South African company name
 * @returns A random company name
 */
export const generateRandomCompanyName = (): string => {
  const prefixes = ['South African', 'Cape', 'Transvaal', 'Natal', 'Karoo', 'Highveld', 'Bushveld', 'Kalahari', 'African'];
  const middles = ['Farming', 'Agricultural', 'Livestock', 'Cattle', 'Sheep', 'Goat', 'Dairy', 'Meat', 'Produce', 'Farm'];
  const suffixes = ['Ltd', 'Pty Ltd', 'Holdings', 'Group', 'Farms', 'Cooperative', 'Enterprises', 'Company', 'Corporation'];
  
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const middle = middles[Math.floor(Math.random() * middles.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
  
  return `${prefix} ${middle} ${suffix}`;
};

export default {
  generateUniqueId,
  ensureUniqueIds,
  createModuleSpecificData,
  generateRandomZARValue,
  formatZAR,
  generateRandomDate,
  generateRandomFarmName,
  generateRandomSALocation,
  generateRandomSAPhoneNumber,
  generateRandomCompanyName
};
