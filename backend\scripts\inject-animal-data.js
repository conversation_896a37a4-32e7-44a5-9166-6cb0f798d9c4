/**
 * Inject Animal Data Script
 *
 * This script injects realistic animal data into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection string
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

// Animal data to inject
const animals = [
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2024-001',
    name: 'Thabo',
    species: 'Cattle',
    breed: 'Nguni',
    gender: 'male',
    birthDate: new Date('2022-03-15'),
    acquisitionDate: new Date('2022-03-15'),
    weight: 450,
    status: 'active',
    healthStatus: 'healthy',
    location: 'North Paddock',
    rfidTag: 'RF00123456',
    notes: 'Prime breeding bull with excellent conformation',
    purchasePrice: 15000,
    estimatedValue: 25000,
    images: [
      {
        url: '/images/animals/cattle-1.jpeg',
        caption: 'Thabo - Front View',
        isPrimary: true,
        uploadDate: new Date()
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2024-002',
    name: 'Lesedi',
    species: 'Cattle',
    breed: 'Bonsmara',
    gender: 'female',
    birthDate: new Date('2021-06-10'),
    acquisitionDate: new Date('2021-06-10'),
    weight: 380,
    status: 'active',
    healthStatus: 'healthy',
    location: 'South Paddock',
    rfidTag: 'RF00123457',
    notes: 'Excellent milk producer',
    purchasePrice: 12000,
    estimatedValue: 18000,
    images: [
      {
        url: '/images/animals/cattle-2.avif',
        caption: 'Lesedi - Side View',
        isPrimary: true,
        uploadDate: new Date()
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'SHP-2024-001',
    name: 'Kgosi',
    species: 'Sheep',
    breed: 'Dorper',
    gender: 'male',
    birthDate: new Date('2023-01-20'),
    acquisitionDate: new Date('2023-01-20'),
    weight: 65,
    status: 'active',
    healthStatus: 'healthy',
    location: 'East Paddock',
    rfidTag: 'RF00123458',
    notes: 'Breeding ram with excellent wool quality',
    purchasePrice: 3500,
    estimatedValue: 5000,
    images: [
      {
        url: '/images/animals/sheep-1.jpeg',
        caption: 'Kgosi - Front View',
        isPrimary: true,
        uploadDate: new Date()
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'SHP-2024-002',
    name: 'Naledi',
    species: 'Sheep',
    breed: 'Merino',
    gender: 'female',
    birthDate: new Date('2022-11-05'),
    acquisitionDate: new Date('2022-11-05'),
    weight: 45,
    status: 'active',
    healthStatus: 'healthy',
    location: 'East Paddock',
    rfidTag: 'RF00123459',
    notes: 'High wool producer',
    purchasePrice: 2800,
    estimatedValue: 4000,
    images: [
      {
        url: '/images/animals/sheep-2.jpeg',
        caption: 'Naledi - Side View',
        isPrimary: true,
        uploadDate: new Date()
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'GOT-2024-001',
    name: 'Tumelo',
    species: 'Goat',
    breed: 'Boer Goat',
    gender: 'male',
    birthDate: new Date('2023-02-15'),
    acquisitionDate: new Date('2023-02-15'),
    weight: 70,
    status: 'active',
    healthStatus: 'healthy',
    location: 'West Paddock',
    rfidTag: 'RF00123460',
    notes: 'Breeding buck with excellent meat conformation',
    purchasePrice: 4000,
    estimatedValue: 6000,
    images: [
      {
        url: '/images/animals/goat-1.jpeg',
        caption: 'Tumelo - Front View',
        isPrimary: true,
        uploadDate: new Date()
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject animal data
async function injectAnimalData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const animalsCollection = db.collection('animals');

    // Check if animals already exist
    const existingAnimals = await animalsCollection.find({}).toArray();
    logger.info(`Found ${existingAnimals.length} existing animals`);

    // Insert or update animals
    for (const animal of animals) {
      const existingAnimal = await animalsCollection.findOne({ tagNumber: animal.tagNumber });

      if (existingAnimal) {
        logger.info(`Updating animal: ${animal.tagNumber}`);
        await animalsCollection.updateOne(
          { tagNumber: animal.tagNumber },
          { $set: { ...animal, _id: existingAnimal._id } }
        );
      } else {
        logger.info(`Inserting new animal: ${animal.tagNumber}`);
        await animalsCollection.insertOne(animal);
      }
    }

    logger.info('Animal data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting animal data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectAnimalData()
  .then(() => {
    logger.info('Animal data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in animal data injection script:', error);
  });

module.exports = { animals };
