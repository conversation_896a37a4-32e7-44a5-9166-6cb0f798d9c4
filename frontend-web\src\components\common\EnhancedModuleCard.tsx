import React from 'react';
import { Card, CardContent, Box, Typography, IconButton, Chip } from '@mui/material';
import { Lock, Star, TrendingUp, Analytics } from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import { motion } from 'framer-motion';
import OptimizedImage from './OptimizedImage';

interface EnhancedModuleCardProps {
  id: string;
  name: string;
  description: string;
  icon: React.ReactElement;
  backgroundImage?: string;
  color: string;
  isLocked?: boolean;
  isPremium?: boolean;
  comingSoon?: boolean;
  onClick?: () => void;
  shape?: 'circle' | 'ring' | 'hexagon' | 'connected' | 'floating';
  variant?: 'glass' | 'gradient' | 'neon' | 'connected' | 'floating';
  size?: 'small' | 'medium' | 'large' | 'xl';
  connectionLines?: boolean;
  floatingElements?: boolean;
}

const EnhancedModuleCard: React.FC<EnhancedModuleCardProps> = ({
  id,
  name,
  description,
  icon,
  backgroundImage,
  color,
  isLocked = false,
  isPremium = false,
  comingSoon = false,
  onClick,
  shape = 'circle',
  variant = 'glass',
  size = 'medium',
  connectionLines = true,
  floatingElements = true
}) => {
  const getSizeStyles = () => {
    const sizes = {
      small: { width: 200, height: 200 },
      medium: { width: 280, height: 280 },
      large: { width: 350, height: 350 },
      xl: { width: 420, height: 420 }
    };
    return sizes[size] || sizes.medium;
  };

  const getShapeStyles = () => {
    const shapes = {
      circle: {
        borderRadius: '50%',
        position: 'relative',
        overflow: 'visible'
      },
      ring: {
        borderRadius: '50%',
        border: `4px solid ${alpha(color, 0.3)}`,
        position: 'relative',
        overflow: 'visible',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: '-8px',
          left: '-8px',
          right: '-8px',
          bottom: '-8px',
          borderRadius: '50%',
          border: `2px solid ${alpha(color, 0.1)}`,
          zIndex: -1
        }
      },
      hexagon: {
        clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
        borderRadius: 0,
        position: 'relative'
      },
      connected: {
        borderRadius: '24px',
        position: 'relative',
        transform: 'rotate(-5deg)',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: '20%',
          right: '-20px',
          width: '40px',
          height: '2px',
          background: `linear-gradient(90deg, ${color}, transparent)`,
          zIndex: -1
        }
      },
      floating: {
        borderRadius: '20px',
        position: 'relative',
        transform: 'perspective(1000px) rotateX(10deg) rotateY(-5deg)',
        boxShadow: `0 20px 60px ${alpha(color, 0.3)}`
      }
    };
    return shapes[shape] || shapes.circle;
  };

  const getVariantStyles = () => {
    const variants = {
      glass: {
        background: `linear-gradient(135deg, ${alpha('#ffffff', 0.15)}, ${alpha('#ffffff', 0.05)})`,
        backdropFilter: 'blur(25px)',
        WebkitBackdropFilter: 'blur(25px)',
        border: `1px solid ${alpha(color, 0.2)}`,
        boxShadow: `0 8px 32px ${alpha(color, 0.15)}, inset 0 1px 0 ${alpha('#ffffff', 0.2)}`
      },
      gradient: {
        background: `linear-gradient(135deg, ${color}15, ${color}05)`,
        border: `1px solid ${alpha(color, 0.3)}`,
        boxShadow: `0 10px 40px ${alpha(color, 0.2)}`
      },
      neon: {
        background: `linear-gradient(135deg, ${alpha('#000', 0.8)}, ${alpha('#000', 0.6)})`,
        border: `2px solid ${color}`,
        boxShadow: `
          0 0 20px ${alpha(color, 0.6)},
          0 0 40px ${alpha(color, 0.4)},
          0 0 80px ${alpha(color, 0.2)},
          inset 0 0 20px ${alpha(color, 0.1)}
        `
      },
      connected: {
        background: `linear-gradient(135deg, ${alpha(color, 0.1)}, ${alpha(color, 0.05)})`,
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        border: `1px solid ${alpha(color, 0.3)}`,
        position: 'relative',
        '&::after': {
          content: '""',
          position: 'absolute',
          top: '50%',
          right: '-30px',
          width: '60px',
          height: '2px',
          background: `linear-gradient(90deg, ${color}, transparent)`,
          zIndex: 1
        }
      },
      floating: {
        background: `linear-gradient(135deg, ${alpha('#ffffff', 0.2)}, ${alpha('#ffffff', 0.1)})`,
        backdropFilter: 'blur(30px)',
        WebkitBackdropFilter: 'blur(30px)',
        border: `1px solid ${alpha(color, 0.3)}`,
        boxShadow: `
          0 20px 60px ${alpha(color, 0.3)},
          0 10px 30px ${alpha('#000', 0.1)},
          inset 0 1px 0 ${alpha('#ffffff', 0.3)}
        `
      }
    };
    return variants[variant] || variants.glass;
  };

  return (
    <motion.div
      whileHover={{ 
        scale: isLocked ? 1 : 1.05, 
        y: isLocked ? 0 : -8 
      }}
      whileTap={{ scale: isLocked ? 1 : 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <Card
        sx={{
          position: 'relative',
          height: '280px',
          cursor: isLocked ? 'not-allowed' : 'pointer',
          overflow: 'hidden',
          opacity: isLocked ? 0.7 : 1,
          transition: 'all 0.3s ease',
          ...getShapeStyles(),
          ...getVariantStyles(),
          '&:hover': {
            boxShadow: isLocked 
              ? `0 8px 32px ${alpha('#000', 0.2)}`
              : `0 16px 48px ${alpha(color, 0.4)}`,
            transform: isLocked ? 'none' : 'translateY(-4px)'
          }
        }}
        onClick={onClick}
      >
        {/* Background Image */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 0,
              ...getShapeStyles()
            }}
          >
            <OptimizedImage
              src={backgroundImage}
              alt={`${name} background`}
              width="100%"
              height="100%"
              opacity={0.4}
              filter="brightness(0.7) contrast(1.1)"
              overlay={alpha(color, 0.2)}
              sx={getShapeStyles()}
            />
          </Box>
        )}

        {/* Lock Overlay */}
        {isLocked && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: alpha('#000', 0.6),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 3,
              ...getShapeStyles()
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              style={{ textAlign: 'center', color: 'white' }}
            >
              <Lock sx={{ fontSize: 48, mb: 1, color }} />
              <Typography variant="h6" fontWeight="bold" color="white">
                {isPremium ? 'Premium' : 'Locked'}
              </Typography>
              <Typography variant="body2" sx={{ color: alpha('#fff', 0.8) }}>
                Click to upgrade
              </Typography>
            </motion.div>
          </Box>
        )}

        {/* Badges */}
        {isPremium && (
          <Chip
            label="PRO"
            size="small"
            sx={{
              position: 'absolute',
              top: 12,
              right: 12,
              background: `linear-gradient(45deg, ${color}, ${alpha(color, 0.8)})`,
              color: 'white',
              fontWeight: 'bold',
              zIndex: 4
            }}
          />
        )}

        {comingSoon && (
          <Chip
            label="Soon"
            size="small"
            sx={{
              position: 'absolute',
              top: 12,
              left: 12,
              background: `linear-gradient(45deg, #F59E0B, #FBBF24)`,
              color: 'white',
              fontWeight: 'bold',
              zIndex: 4
            }}
          />
        )}

        <CardContent
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            p: 3
          }}
        >
          {/* Icon */}
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            <motion.div
              animate={!isLocked ? {
                scale: [1, 1.1, 1],
                rotate: [0, 10, 0]
              } : {}}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              <Box
                sx={{
                  width: 64,
                  height: 64,
                  borderRadius: '12px',
                  background: `linear-gradient(135deg, ${color}, ${alpha(color, 0.8)})`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 'auto',
                  color: 'white',
                  boxShadow: `0 8px 24px ${alpha(color, 0.4)}`
                }}
              >
                {React.cloneElement(icon, { fontSize: 'large' })}
              </Box>
            </motion.div>
          </Box>

          {/* Content */}
          <Box sx={{ textAlign: 'center', flexGrow: 1 }}>
            <Typography
              variant="h6"
              fontWeight="bold"
              sx={{
                color: 'white',
                mb: 1,
                textShadow: '0 2px 4px rgba(0,0,0,0.5)'
              }}
            >
              {name}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: alpha('#fff', 0.9),
                textShadow: '0 1px 2px rgba(0,0,0,0.5)',
                lineHeight: 1.4
              }}
            >
              {description}
            </Typography>
          </Box>

          {/* Action Button */}
          {!isLocked && (
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Typography
                  variant="button"
                  sx={{
                    color,
                    fontWeight: 'bold',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    textShadow: '0 1px 2px rgba(0,0,0,0.3)'
                  }}
                >
                  Open Module
                </Typography>
              </motion.div>
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EnhancedModuleCard;
