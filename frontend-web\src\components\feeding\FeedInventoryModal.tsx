import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, Grid, FormControl, InputLabel, Select, MenuItem, IconButton, Typography, Box, Divider, CircularProgress, Slider, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete  } from '../../utils/iconImports';
import { DatePicker } from '@mui/x-date-pickers';
import { FeedInventory } from '../../types/feeding';
import dayjs from 'dayjs';

interface FeedInventoryModalProps {
  open: boolean;
  onClose: () => void;
  feedItem?: FeedInventory | null;
  onSave: (feedItem: Omit<FeedInventory, 'id'>) => void;
  onUpdate?: (id: string, feedItem: Partial<FeedInventory>) => void;
  onDelete?: (id: string) => void;
  loading?: boolean;
}

const FeedInventoryModal: React.FC<FeedInventoryModalProps> = ({
  open,
  onClose,
  feedItem,
  onSave,
  onUpdate,
  onDelete,
  loading = false,
}) => {
  const isEditMode = !!feedItem;

  // Form state
  const [formData, setFormData] = useState<Partial<FeedInventory>>({
    name: '',
    type: 'concentrate',
    quantity: 0,
    unit: 'kg',
    location: '',
    supplier: '',
    purchaseDate: new Date(),
    expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 6)), // Default to 6 months from now
    batchNumber: '',
    costPerUnit: 0,
    nutritionalInfo: {
      protein: 0,
      fiber: 0,
      energy: 0,
      minerals: {},
      moisture: 0,
    },
    minimumStock: 0,
    status: 'available',
    reorderLevel: 0,
  });

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when feedItem changes
  useEffect(() => {
    if (feedItem) {
      setFormData({
        name: feedItem.name,
        type: feedItem.type,
        quantity: feedItem.quantity,
        unit: feedItem.unit,
        location: feedItem.location,
        supplier: feedItem.supplier,
        purchaseDate: new Date(feedItem.purchaseDate),
        expiryDate: new Date(feedItem.expiryDate),
        batchNumber: feedItem.batchNumber,
        costPerUnit: feedItem.costPerUnit,
        nutritionalInfo: { ...feedItem.nutritionalInfo },
        minimumStock: feedItem.minimumStock,
        status: feedItem.status,
        reorderLevel: feedItem.reorderLevel,
      });
    } else {
      // Reset form for new feed item
      setFormData({
        name: '',
        type: 'concentrate',
        quantity: 0,
        unit: 'kg',
        location: '',
        supplier: '',
        purchaseDate: new Date(),
        expiryDate: new Date(new Date().setMonth(new Date().getMonth() + 6)),
        batchNumber: '',
        costPerUnit: 0,
        nutritionalInfo: {
          protein: 0,
          fiber: 0,
          energy: 0,
          minerals: {},
          moisture: 0,
        },
        minimumStock: 0,
        status: 'available',
        reorderLevel: 0,
      });
    }
    setErrors({});
  }, [feedItem, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }

    // Update status based on quantity and reorder level
    if (name === 'quantity' || name === 'reorderLevel' || name === 'minimumStock') {
      const quantity = name === 'quantity' ? parseFloat(value) : formData.quantity || 0;
      const reorderLevel = name === 'reorderLevel' ? parseFloat(value) : formData.reorderLevel || 0;
      const minimumStock = name === 'minimumStock' ? parseFloat(value) : formData.minimumStock || 0;

      let status: FeedInventory['status'] = 'available';
      if (quantity <= 0) {
        status = 'reorder';
      } else if (quantity < minimumStock) {
        status = 'low';
      }

      setFormData(prev => ({ ...prev, status }));
    }
  };

  // Handle number field changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: parseFloat(value) }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle date changes
  const handleDateChange = (field: 'purchaseDate' | 'expiryDate', date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, [field]: date.toDate() }));

      // Clear error when field is edited
      if (errors[field]) {
        setErrors(prev => ({ ...prev, [field]: '' }));
      }
    }
  };

  // Handle nutritional info changes
  const handleNutritionalChange = (field: keyof FeedInventory['nutritionalInfo'], value: number) => {
    setFormData(prev => {
      const nutritionalInfo = prev.nutritionalInfo || {
        protein: 0,
        fiber: 0,
        energy: 0,
        minerals: {},
        moisture: 0
      };

      return {
        ...prev,
        nutritionalInfo: {
          ...nutritionalInfo,
          [field]: value,
        },
      };
    });
  };

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.type) newErrors.type = 'Type is required';
    if (!formData.unit) newErrors.unit = 'Unit is required';
    if (!formData.location) newErrors.location = 'Location is required';
    if (!formData.supplier) newErrors.supplier = 'Supplier is required';
    if (!formData.purchaseDate) newErrors.purchaseDate = 'Purchase date is required';
    if (!formData.expiryDate) newErrors.expiryDate = 'Expiry date is required';
    if (!formData.batchNumber) newErrors.batchNumber = 'Batch number is required';

    if (formData.quantity === undefined || formData.quantity < 0) {
      newErrors.quantity = 'Quantity must be a positive number';
    }

    if (formData.costPerUnit === undefined || formData.costPerUnit < 0) {
      newErrors.costPerUnit = 'Cost per unit must be a positive number';
    }

    if (formData.minimumStock === undefined || formData.minimumStock < 0) {
      newErrors.minimumStock = 'Minimum stock must be a positive number';
    }

    if (formData.reorderLevel === undefined || formData.reorderLevel < 0) {
      newErrors.reorderLevel = 'Reorder level must be a positive number';
    }

    if (formData.purchaseDate && formData.expiryDate && formData.purchaseDate > formData.expiryDate) {
      newErrors.expiryDate = 'Expiry date must be after purchase date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;

    const feedItemData = {
      ...formData,
    } as Omit<FeedInventory, 'id'>;

    if (isEditMode && feedItem && onUpdate) {
      onUpdate(feedItem.id, feedItemData);
    } else {
      onSave(feedItemData);
    }
  };

  // Handle feed item deletion
  const handleDelete = () => {
    if (isEditMode && feedItem && onDelete) {
      onDelete(feedItem.id);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {isEditMode ? 'Edit Feed Inventory Item' : 'New Feed Inventory Item'}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Basic Information
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Feed Name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.type}>
              <InputLabel>Feed Type</InputLabel>
              <Select
                name="type"
                value={formData.type || ''}
                onChange={handleSelectChange}
                label="Feed Type"
              >
                <MenuItem value="concentrate">Concentrate</MenuItem>
                <MenuItem value="forage">Forage</MenuItem>
                <MenuItem value="supplement">Supplement</MenuItem>
                <MenuItem value="mineral">Mineral</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </Select>
              {errors.type && (
                <Typography variant="caption" color="error">
                  {errors.type}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Quantity"
              name="quantity"
              type="number"
              inputProps={{ min: 0, step: 0.01 }}
              value={formData.quantity || ''}
              onChange={handleChange}
              error={!!errors.quantity}
              helperText={errors.quantity}
              required
            />
          </Grid>

          <Grid item xs={12} sm={4}>
            <FormControl fullWidth required error={!!errors.unit}>
              <InputLabel>Unit</InputLabel>
              <Select
                name="unit"
                value={formData.unit || ''}
                onChange={handleSelectChange}
                label="Unit"
              >
                <MenuItem value="kg">Kilograms (kg)</MenuItem>
                <MenuItem value="tons">Tons</MenuItem>
                <MenuItem value="bales">Bales</MenuItem>
              </Select>
              {errors.unit && (
                <Typography variant="caption" color="error">
                  {errors.unit}
                </Typography>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Cost Per Unit"
              name="costPerUnit"
              type="number"
              inputProps={{ min: 0, step: 0.01 }}
              value={formData.costPerUnit || ''}
              onChange={handleChange}
              error={!!errors.costPerUnit}
              helperText={errors.costPerUnit}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Location"
              name="location"
              value={formData.location || ''}
              onChange={handleChange}
              error={!!errors.location}
              helperText={errors.location}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Supplier"
              name="supplier"
              value={formData.supplier || ''}
              onChange={handleChange}
              error={!!errors.supplier}
              helperText={errors.supplier}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Purchase Date"
              value={formData.purchaseDate ? dayjs(formData.purchaseDate) : null}
              onChange={(date) => handleDateChange('purchaseDate', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.purchaseDate,
                  helperText: errors.purchaseDate
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Expiry Date"
              value={formData.expiryDate ? dayjs(formData.expiryDate) : null}
              onChange={(date) => handleDateChange('expiryDate', date)}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.expiryDate,
                  helperText: errors.expiryDate
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Batch Number"
              name="batchNumber"
              value={formData.batchNumber || ''}
              onChange={handleChange}
              error={!!errors.batchNumber}
              helperText={errors.batchNumber}
              required
            />
          </Grid>

          {/* Inventory Management */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Inventory Management
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Minimum Stock Level"
              name="minimumStock"
              type="number"
              inputProps={{ min: 0 }}
              value={formData.minimumStock || ''}
              onChange={handleChange}
              error={!!errors.minimumStock}
              helperText={errors.minimumStock || 'Alert will be triggered when stock falls below this level'}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Reorder Level"
              name="reorderLevel"
              type="number"
              inputProps={{ min: 0 }}
              value={formData.reorderLevel || ''}
              onChange={handleChange}
              error={!!errors.reorderLevel}
              helperText={errors.reorderLevel || 'Level at which new stock should be ordered'}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                name="status"
                value={formData.status || 'available'}
                onChange={handleSelectChange}
                label="Status"
              >
                <MenuItem value="available">Available</MenuItem>
                <MenuItem value="low">Low Stock</MenuItem>
                <MenuItem value="reorder">Reorder</MenuItem>
                <MenuItem value="expired">Expired</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {/* Nutritional Information */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Nutritional Information
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Protein (%)</Typography>
            <Slider
              value={formData.nutritionalInfo?.protein || 0}
              onChange={(_, value) => handleNutritionalChange('protein', value as number)}
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={50}
            />
            <TextField
              size="small"
              type="number"
              inputProps={{ min: 0, max: 50, step: 0.1 }}
              value={formData.nutritionalInfo?.protein || 0}
              onChange={(e) => handleNutritionalChange('protein', parseFloat(e.target.value))}
              sx={{ width: '100px', mt: 1 }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Fiber (%)</Typography>
            <Slider
              value={formData.nutritionalInfo?.fiber || 0}
              onChange={(_, value) => handleNutritionalChange('fiber', value as number)}
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={50}
            />
            <TextField
              size="small"
              type="number"
              inputProps={{ min: 0, max: 50, step: 0.1 }}
              value={formData.nutritionalInfo?.fiber || 0}
              onChange={(e) => handleNutritionalChange('fiber', parseFloat(e.target.value))}
              sx={{ width: '100px', mt: 1 }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Energy (MJ/kg)</Typography>
            <Slider
              value={formData.nutritionalInfo?.energy || 0}
              onChange={(_, value) => handleNutritionalChange('energy', value as number)}
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={20}
            />
            <TextField
              size="small"
              type="number"
              inputProps={{ min: 0, max: 20, step: 0.1 }}
              value={formData.nutritionalInfo?.energy || 0}
              onChange={(e) => handleNutritionalChange('energy', parseFloat(e.target.value))}
              sx={{ width: '100px', mt: 1 }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography gutterBottom>Moisture (%)</Typography>
            <Slider
              value={formData.nutritionalInfo?.moisture || 0}
              onChange={(_, value) => handleNutritionalChange('moisture', value as number)}
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={100}
            />
            <TextField
              size="small"
              type="number"
              inputProps={{ min: 0, max: 100, step: 0.1 }}
              value={formData.nutritionalInfo?.moisture || 0}
              onChange={(e) => handleNutritionalChange('moisture', parseFloat(e.target.value))}
              sx={{ width: '100px', mt: 1 }}
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {isEditMode && onDelete && (
          <CustomButton
            onClick={handleDelete}
            color="error"
            startIcon={<Delete />}
            disabled={loading}
          >
            Delete
          </CustomButton>
        )}
        <Box flexGrow={1} />
        <CustomButton onClick={onClose} disabled={loading}>
          Cancel
        </CustomButton>
        <CustomButton
          onClick={handleSubmit}
          variant="contained"
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            isEditMode ? 'Update' : 'Save'
          )}
        </CustomButton>
      </DialogActions>
    </Dialog>
  );
};

export default FeedInventoryModal;
