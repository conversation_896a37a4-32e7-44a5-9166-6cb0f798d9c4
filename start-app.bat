@echo off
echo ===================================================
echo    MayCaiphus Livestock Management System
echo ===================================================
echo.

echo Stopping any running Node.js processes...
taskkill /f /im node.exe > nul 2>&1

echo Starting Backend Server on port 3001...
start cmd /k "cd backend && set PORT=3001 && npm start"

echo Starting Frontend Server on port 3002...
start cmd /k "cd frontend-web && set PORT=3002 && npm start"

echo.
echo MayCaiphus Livestock Management System is starting...
echo Backend API: http://localhost:3001
echo Frontend: http://localhost:3002
echo.
echo Login credentials available in documentation
echo Check README.md for default development credentials
echo.
echo Press any key to open the application in your browser...

pause > nul

start http://localhost:3002

echo Press any key to stop all servers...
pause > nul

echo Stopping servers...
taskkill /f /im node.exe > nul 2>&1
echo All servers stopped.