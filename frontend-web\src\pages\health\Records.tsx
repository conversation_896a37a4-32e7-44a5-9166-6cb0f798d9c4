import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Tabs, Tab } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Assessment,
  Timeline,
  LocalHospital,
  Healing,
  MedicalServices,
  Vaccines,
  EventNote,
  FilterList,
  Download,
  Print,
  Share
} from '../../utils/iconImports';
import { 
  ModuleHeader,
  AnimatedBackgroundCard,
  ModuleContainer,
  withSubModuleTranslation,
  SubModuleDashboard,
  SubModuleMetricsCard,
  SubModuleActionPanel,
  SubModuleDataTable,
  SubModuleChart
, CustomButton } from '../../components/common';
import { format } from 'date-fns';
import { mockAnimals } from '../../mocks/animalData';

interface HealthRecord {
  id: string;
  animalId: string;
  animalName: string;
  breed: string;
  recordType: 'examination' | 'treatment' | 'vaccination' | 'surgery' | 'test';
  date: string;
  veterinarian: string;
  diagnosis?: string;
  treatment?: string;
  medication?: string;
  dosage?: string;
  notes: string;
  followUpDate?: string;
  testResults?: {
    name: string;
    value: string;
    unit: string;
    normalRange?: string;
    status: 'normal' | 'abnormal' | 'critical';
  }[];
}

interface RecordsProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const Records: React.FC<RecordsProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<HealthRecord | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState('month');
  const [healthRecords, setHealthRecords] = useState<HealthRecord[]>([
    {
      id: 'HR001',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      breed: 'Nguni',
      recordType: 'examination',
      date: '2024-03-15',
      veterinarian: 'Dr. van Niekerk',
      diagnosis: 'Healthy, no issues detected',
      notes: 'Routine health check',
      testResults: [
        {
          name: 'Temperature',
          value: '38.5',
          unit: '°C',
          normalRange: '38.0-39.0',
          status: 'normal'
        },
        {
          name: 'Heart Rate',
          value: '70',
          unit: 'bpm',
          normalRange: '60-80',
          status: 'normal'
        },
        {
          name: 'Respiratory Rate',
          value: '25',
          unit: 'breaths/min',
          normalRange: '20-30',
          status: 'normal'
        }
      ]
    },
    {
      id: 'HR002',
      animalId: 'ANM002',
      animalName: 'Tumelo',
      breed: 'Bonsmara',
      recordType: 'treatment',
      date: '2024-03-10',
      veterinarian: 'Dr. Botha',
      diagnosis: 'Mild respiratory infection',
      treatment: 'Antibiotics administered',
      medication: 'Oxytetracycline',
      dosage: '10mg/kg',
      notes: 'Responding well to treatment',
      followUpDate: '2024-03-17'
    },
    {
      id: 'HR003',
      animalId: 'ANM003',
      animalName: 'Lerato',
      breed: 'Afrikaner',
      recordType: 'vaccination',
      date: '2024-02-28',
      veterinarian: 'Dr. Mkhize',
      treatment: 'Vaccination',
      medication: 'Anthrax vaccine',
      dosage: 'Standard dose',
      notes: 'Annual vaccination program',
      followUpDate: '2025-02-28'
    },
    {
      id: 'HR004',
      animalId: 'ANM004',
      animalName: 'Katlego',
      breed: 'Dorper',
      recordType: 'surgery',
      date: '2024-02-15',
      veterinarian: 'Dr. van Niekerk',
      diagnosis: 'Abscess on right flank',
      treatment: 'Surgical drainage and cleaning',
      medication: 'Penicillin',
      dosage: '5mg/kg for 7 days',
      notes: 'Surgery successful, monitoring for infection',
      followUpDate: '2024-02-22'
    },
    {
      id: 'HR005',
      animalId: 'ANM005',
      animalName: 'Naledi',
      breed: 'Boer Goat',
      recordType: 'test',
      date: '2024-03-05',
      veterinarian: 'Dr. Botha',
      diagnosis: 'Suspected parasite load',
      notes: 'Fecal sample collected for testing',
      testResults: [
        {
          name: 'Fecal Egg Count',
          value: '850',
          unit: 'EPG',
          normalRange: '<500',
          status: 'abnormal'
        },
        {
          name: 'Haemonchus',
          value: 'Present',
          unit: '',
          status: 'abnormal'
        },
        {
          name: 'Coccidia',
          value: 'Present',
          unit: '',
          status: 'abnormal'
        }
      ]
    }
  ]);

  const handleAddNew = () => {
    setSelectedRecord(null);
    setOpenDialog(true);
  };

  const handleEdit = (record: HealthRecord) => {
    setSelectedRecord(record);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setHealthRecords(healthRecords.filter(record => record.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getRecordTypeChip = (type: string) => {
    switch (type) {
      case 'examination':
        return <Chip label="Examination" color="primary" size="small" />;
      case 'treatment':
        return <Chip label="Treatment" color="secondary" size="small" />;
      case 'vaccination':
        return <Chip label="Vaccination" color="success" size="small" />;
      case 'surgery':
        return <Chip label="Surgery" color="error" size="small" />;
      case 'test':
        return <Chip label="Test" color="info" size="small" />;
      default:
        return <Chip label={type} size="small" />;
    }
  };

  const getTestResultStatusChip = (status: string) => {
    switch (status) {
      case 'normal':
        return <Chip label="Normal" color="success" size="small" />;
      case 'abnormal':
        return <Chip label="Abnormal" color="warning" size="small" />;
      case 'critical':
        return <Chip label="Critical" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRecordsByType = () => {
    const recordTypes: Record<string, number> = {
      examination: 0,
      treatment: 0,
      vaccination: 0,
      surgery: 0,
      test: 0
    };

    healthRecords.forEach(record => {
      recordTypes[record.recordType]++;
    });

    return Object.entries(recordTypes).map(([type, count]) => ({ type, count }));
  };

  const getFilteredRecords = () => {
    if (tabValue === 0) {
      return healthRecords;
    } else {
      const recordTypes = ['examination', 'treatment', 'vaccination', 'surgery', 'test'];
      return healthRecords.filter(record => record.recordType === recordTypes[tabValue - 1]);
    }
  };

  const getRecentAbnormalResults = () => {
    return healthRecords
      .filter(record => record.testResults && record.testResults.some(test => test.status !== 'normal'))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3);
  };

  // Define metrics for the dashboard
  const metrics = [
    {
      label: translateModuleField ? translateModuleField('total_records', "Total Records") : "Total Records",
      value: healthRecords.length,
      icon: <LocalHospital />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('active_treatments', "Active Treatments") : "Active Treatments",
      value: healthRecords.filter(r => r.recordType === 'treatment').length,
      icon: <Healing />,
      color: theme.palette.warning.main,
      trend: {
        value: 2,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('vaccinations', "Vaccinations") : "Vaccinations",
      value: healthRecords.filter(r => r.recordType === 'vaccination').length,
      icon: <Vaccines />,
      color: theme.palette.success.main,
      trend: {
        value: 8,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('upcoming_appointments', "Upcoming Appointments") : "Upcoming Appointments",
      value: healthRecords.filter(r => r.followUpDate && new Date(r.followUpDate) > new Date()).length,
      icon: <EventNote />,
      color: theme.palette.info.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last month"
      }
    }
  ];

  // Define actions for the dashboard
  const actions = [
    {
      label: translateModuleField ? translateModuleField('add_record', "Add Health Record") : "Add Health Record",
      icon: <Add />,
      onClick: handleAddNew,
      color: theme.palette.success.main,
      description: "Record new health event"
    },
    {
      label: translateModuleField ? translateModuleField('schedule_appointment', "Schedule Appointment") : "Schedule Appointment",
      icon: <EventNote />,
      onClick: () => navigate('/dashboard/health/appointments'),
      color: theme.palette.info.main,
      description: "Book veterinary visit"
    },
    {
      label: translateModuleField ? translateModuleField('record_vaccination', "Record Vaccination") : "Record Vaccination",
      icon: <Vaccines />,
      onClick: () => navigate('/dashboard/health/vaccinations'),
      color: theme.palette.warning.main,
      description: "Log new vaccination"
    },
    {
      label: translateModuleField ? translateModuleField('generate_report', "Generate Report") : "Generate Report",
      icon: <Download />,
      onClick: () => console.log("Generate health report"),
      color: theme.palette.secondary.main,
      description: "Export health data"
    }
  ];

  // Define health records by type chart data
  const healthRecordsByTypeData = {
    labels: ['Examination', 'Treatment', 'Vaccination', 'Surgery', 'Test'],
    datasets: [
      {
        label: 'Records by Type',
        data: [
          healthRecords.filter(r => r.recordType === 'examination').length,
          healthRecords.filter(r => r.recordType === 'treatment').length,
          healthRecords.filter(r => r.recordType === 'vaccination').length,
          healthRecords.filter(r => r.recordType === 'surgery').length,
          healthRecords.filter(r => r.recordType === 'test').length
        ],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.error.main,
          theme.palette.info.main
        ]
      }
    ]
  };

  // Define monthly health activities chart data
  const monthlyHealthActivitiesData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Vaccinations',
        data: [12, 15, 10, 8, 14, 16, 12, 9, 11, 13, 10, 8],
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.main
      },
      {
        label: 'Treatments',
        data: [8, 10, 12, 15, 9, 7, 10, 12, 14, 8, 6, 9],
        backgroundColor: theme.palette.warning.main,
        borderColor: theme.palette.warning.main
      },
      {
        label: 'Checkups',
        data: [5, 7, 9, 6, 8, 10, 7, 5, 8, 9, 7, 6],
        backgroundColor: theme.palette.success.main,
        borderColor: theme.palette.success.main
      },
      {
        label: 'Issues',
        data: [3, 2, 4, 5, 2, 1, 3, 4, 2, 3, 2, 1],
        backgroundColor: theme.palette.error.main,
        borderColor: theme.palette.error.main
      }
    ]
  };

  // Define health records table columns
  const healthRecordsColumns = [
    {
      id: 'animal',
      label: translateModuleField ? translateModuleField('animal', "Animal") : "Animal",
      minWidth: 150,
      format: (value: any) => (
        <Box>
          <Typography variant="body1">{value.name}</Typography>
          <Typography variant="body2" color="text.secondary">{value.breed}</Typography>
        </Box>
      )
    },
    {
      id: 'date',
      label: translateModuleField ? translateModuleField('date', "Date") : "Date",
      minWidth: 100,
      format: (value: string) => formatDate(value)
    },
    {
      id: 'recordType',
      label: translateModuleField ? translateModuleField('record_type', "Record Type") : "Record Type",
      minWidth: 120,
      format: (value: string) => getRecordTypeChip(value)
    },
    {
      id: 'veterinarian',
      label: translateModuleField ? translateModuleField('veterinarian', "Veterinarian") : "Veterinarian",
      minWidth: 150
    },
    {
      id: 'diagnosis',
      label: translateModuleField ? translateModuleField('diagnosis_treatment', "Diagnosis/Treatment") : "Diagnosis/Treatment",
      minWidth: 200,
      format: (value: string, row: any) => (
        <Typography variant="body2" sx={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {value || row.treatment || 'N/A'}
        </Typography>
      )
    },
    {
      id: 'followUpDate',
      label: translateModuleField ? translateModuleField('follow_up', "Follow-up") : "Follow-up",
      minWidth: 120,
      format: (value: string) => value ? formatDate(value) : 'None'
    }
  ];

  // Transform health records for the table
  const healthRecordsData = healthRecords.map(record => ({
    id: record.id,
    animal: { name: record.animalName, breed: record.breed },
    date: record.date,
    recordType: record.recordType,
    veterinarian: record.veterinarian,
    diagnosis: record.diagnosis,
    treatment: record.treatment,
    followUpDate: record.followUpDate
  }));

  // Define tabs for the dashboard
  const dashboardTabs = [
    { label: translateModuleField ? translateModuleField('tab_all_records', "All Records") : "All Records", icon: <LocalHospital /> },
    { label: translateModuleField ? translateModuleField('tab_examinations', "Examinations") : "Examinations", icon: <MedicalServices /> },
    { label: translateModuleField ? translateModuleField('tab_treatments', "Treatments") : "Treatments", icon: <Healing /> },
    { label: translateModuleField ? translateModuleField('tab_vaccinations', "Vaccinations") : "Vaccinations", icon: <Vaccines /> },
    { label: translateModuleField ? translateModuleField('tab_surgeries', "Surgeries") : "Surgeries", icon: <LocalHospital /> },
    { label: translateModuleField ? translateModuleField('tab_tests', "Tests") : "Tests", icon: <Assessment /> }
  ];

  return (
    <SubModuleDashboard
      title={translateSubModule ? translateSubModule('title', "Health Records") : "Health Records"}
      subtitle={translateSubModule ? translateSubModule('subtitle', "Comprehensive health history and medical records for your livestock") : "Comprehensive health history and medical records for your livestock"}
      parentModule="health"
      subModule="records"
      icon={<LocalHospital />}
      metrics={metrics}
      actions={actions}
      tabs={dashboardTabs}
      activeTab={tabValue}
      onTabChange={handleTabChange}
      isLoading={false}
      onRefresh={() => console.log("Refreshing health records")}
      onBack={() => navigate('/health')}
    >
      {/* Time Range Selector */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'flex-end' }}>
        <Paper sx={{ p: 0.5, borderRadius: 2 }}>
          <Tabs
            value={timeRange}
            onChange={(e, newValue) => setTimeRange(newValue)}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              minHeight: 'unset',
              '& .MuiTab-root': {
                minHeight: 'unset',
                py: 0.5,
                px: 2,
                minWidth: 'unset',
                borderRadius: 1,
                fontSize: '0.875rem',
                fontWeight: 'medium',
                textTransform: 'none',
                '&.Mui-selected': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                }
              }
            }}
          >
            <Tab label="Week" value="week" />
            <Tab label="Month" value="month" />
            <Tab label="Quarter" value="quarter" />
            <Tab label="Year" value="year" />
          </Tabs>
        </Paper>
      </Box>

      {/* Health Records Table */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <SubModuleDataTable
            title={translateModuleField ? translateModuleField('health_records', "Health Records") : "Health Records"}
            columns={healthRecordsColumns}
            data={getFilteredRecords().map(record => ({
              id: record.id,
              animal: { name: record.animalName, breed: record.breed },
              date: record.date,
              recordType: record.recordType,
              veterinarian: record.veterinarian,
              diagnosis: record.diagnosis,
              treatment: record.treatment,
              followUpDate: record.followUpDate
            }))}
            onView={(row) => handleEdit(row)}
            onEdit={(row) => handleEdit(row)}
            onDelete={(row) => handleDelete(row.id)}
            onExport={() => console.log("Export records")}
            onPrint={() => console.log("Print records")}
            onShare={() => console.log("Share records")}
            moduleColor={theme.palette.primary.main}
            emptyStateMessage={translateModuleField ? translateModuleField('no_records', "No health records found") : "No health records found"}
          />
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <SubModuleChart
            title={translateModuleField ? translateModuleField('records_by_type', "Health Records by Type") : "Health Records by Type"}
            description={translateModuleField ? translateModuleField('records_by_type_desc', "Distribution of health records by category") : "Distribution of health records by category"}
            type="pie"
            data={healthRecordsByTypeData}
            height={350}
            moduleColor={theme.palette.primary.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SubModuleChart
            title={translateModuleField ? translateModuleField('abnormal_test_results', "Abnormal Test Results") : "Abnormal Test Results"}
            description={translateModuleField ? translateModuleField('abnormal_test_results_desc', "Recent abnormal test results requiring attention") : "Recent abnormal test results requiring attention"}
            type="bar"
            data={{
              labels: getRecentAbnormalResults().map(r => r.animalName),
              datasets: [{
                label: 'Abnormal Tests',
                data: getRecentAbnormalResults().map(r => r.testResults?.filter(t => t.status !== 'normal').length || 0),
                backgroundColor: theme.palette.error.main
              }]
            }}
            height={350}
            moduleColor={theme.palette.error.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
      </Grid>

      {/* Monthly Health Activities Chart */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <SubModuleChart
            title={translateModuleField ? translateModuleField('monthly_health_activities', "Monthly Health Activities") : "Monthly Health Activities"}
            description={translateModuleField ? translateModuleField('monthly_health_activities_desc', "Track vaccinations, treatments, and checkups over time") : "Track vaccinations, treatments, and checkups over time"}
            type="line"
            data={monthlyHealthActivitiesData}
            height={400}
            moduleColor={theme.palette.info.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
      </Grid>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedRecord
            ? (translateModuleField ? translateModuleField('edit_health_record', "Edit Health Record") : "Edit Health Record")
            : (translateModuleField ? translateModuleField('add_new_health_record', "Add New Health Record") : "Add New Health Record")
          }
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="animal-select-label">Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  label="Animal"
                  defaultValue={selectedRecord?.animalId || ''}
                >
                  {mockAnimals.map(animal => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} ({animal.breed})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="record-type-label">Record Type</InputLabel>
                <Select
                  labelId="record-type-label"
                  id="record-type"
                  label="Record Type"
                  defaultValue={selectedRecord?.recordType || 'examination'}
                >
                  <MenuItem value="examination">Examination</MenuItem>
                  <MenuItem value="treatment">Treatment</MenuItem>
                  <MenuItem value="vaccination">Vaccination</MenuItem>
                  <MenuItem value="surgery">Surgery</MenuItem>
                  <MenuItem value="test">Test</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="date"
                label="Date"
                type="date"
                defaultValue={selectedRecord?.date || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="veterinarian"
                label="Veterinarian"
                defaultValue={selectedRecord?.veterinarian || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="diagnosis"
                label="Diagnosis"
                defaultValue={selectedRecord?.diagnosis || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="treatment"
                label="Treatment"
                defaultValue={selectedRecord?.treatment || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="medication"
                label="Medication"
                defaultValue={selectedRecord?.medication || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="dosage"
                label="Dosage"
                defaultValue={selectedRecord?.dosage || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="followUpDate"
                label="Follow-up Date"
                type="date"
                defaultValue={selectedRecord?.followUpDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={3}
                defaultValue={selectedRecord?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>
            {translate ? translate('common.cancel', { fallback: "Cancel" }) : "Cancel"}
          </CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedRecord
              ? (translate ? translate('common.update', { fallback: "Update" }) : "Update")
              : (translate ? translate('common.save', { fallback: "Save" }) : "Save")
            }
          </CustomButton>
        </DialogActions>
      </Dialog>
    </SubModuleDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(Records, 'health', 'records');
