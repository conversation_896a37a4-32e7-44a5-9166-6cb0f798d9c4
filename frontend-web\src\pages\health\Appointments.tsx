import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar, List, ListItem, ListItemText, ListItemAvatar, Divider, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  LocalHospital,
  Healing,
  Notifications,
  Event,
  Person,
  LocationOn,
  AccessTime
 } from '../../utils/iconImports';
import { mockAnimals } from '../../mocks/animalData';
import { formatCurrency } from '../../utils/formatters';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

interface Appointment {
  id: string;
  title: string;
  date: string;
  time: string;
  type: 'routine' | 'emergency' | 'follow-up' | 'vaccination';
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
  veterinarian: string;
  location: string;
  animals: string[];
  notes: string;
  estimatedCost?: number;
}

const Appointments: React.FC = () => {
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [appointments, setAppointments] = useState<Appointment[]>([
    {
      id: 'APT001',
      title: 'Quarterly Herd Health Check',
      date: '2024-03-25',
      time: '09:00',
      type: 'routine',
      status: 'scheduled',
      veterinarian: 'Dr. van Niekerk',
      location: 'On-farm visit',
      animals: ['Entire herd'],
      notes: 'Regular quarterly health assessment for all animals',
      estimatedCost: 5000
    },
    {
      id: 'APT002',
      title: 'Vaccination Program',
      date: '2024-04-05',
      time: '08:30',
      type: 'vaccination',
      status: 'scheduled',
      veterinarian: 'Dr. Botha',
      location: 'On-farm visit',
      animals: ['Cattle herd'],
      notes: 'Annual vaccination against Anthrax and Botulism',
      estimatedCost: 12000
    },
    {
      id: 'APT003',
      title: 'Follow-up for Heartwater Treatment',
      date: '2024-03-10',
      time: '14:00',
      type: 'follow-up',
      status: 'completed',
      veterinarian: 'Dr. van Niekerk',
      location: 'On-farm visit',
      animals: ['Umfazi (ANM001)'],
      notes: 'Follow-up assessment after treatment for Heartwater'
    },
    {
      id: 'APT004',
      title: 'Emergency Call - Difficult Calving',
      date: '2024-02-28',
      time: '22:15',
      type: 'emergency',
      status: 'completed',
      veterinarian: 'Dr. Mkhize',
      location: 'On-farm visit',
      animals: ['Ithemba (ANM010)'],
      notes: 'Emergency assistance with difficult calving'
    },
    {
      id: 'APT005',
      title: 'Pregnancy Testing',
      date: '2024-04-15',
      time: '10:00',
      type: 'routine',
      status: 'scheduled',
      veterinarian: 'Dr. Viljoen',
      location: 'On-farm visit',
      animals: ['Breeding females'],
      notes: 'Pregnancy testing for breeding herd',
      estimatedCost: 3500
    }
  ]);

  const handleAddNew = () => {
    setSelectedAppointment(null);
    setOpenDialog(true);
  };

  const handleEdit = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setAppointments(appointments.filter(appointment => appointment.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Chip label="Scheduled" color="primary" size="small" />;
      case 'completed':
        return <Chip label="Completed" color="success" size="small" />;
      case 'cancelled':
        return <Chip label="Cancelled" color="error" size="small" />;
      case 'no-show':
        return <Chip label="No Show" color="warning" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getTypeChip = (type: string) => {
    switch (type) {
      case 'routine':
        return <Chip label="Routine" color="default" size="small" />;
      case 'emergency':
        return <Chip label="Emergency" color="error" size="small" />;
      case 'follow-up':
        return <Chip label="Follow-up" color="info" size="small" />;
      case 'vaccination':
        return <Chip label="Vaccination" color="success" size="small" />;
      default:
        return <Chip label={type} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (dateString: string) => {
    const today = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getUpcomingAppointments = () => {
    return appointments
      .filter(appointment => appointment.status === 'scheduled')
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  };

  const getTotalEstimatedCost = () => {
    return appointments
      .filter(appointment => appointment.status === 'scheduled' && appointment.estimatedCost)
      .reduce((total, appointment) => total + (appointment.estimatedCost || 0), 0);
  };

  return (
    <Box>
      <ModuleHeader
        title="Veterinary Appointments"
        subtitle="Schedule and track veterinary appointments and health check-ups"
        module="health"
        submodule="appointments"
        actionLabel="Book Appointment"
        actionIcon={<Add />}
        onAction={handleAddNew}
      />

      <Box sx={{ px: 3, pb: 5 }}>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Appointment Status"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<CalendarToday />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {appointments.filter(appointment => appointment.status === 'scheduled').length}
                      </Typography>
                      <Typography variant="body2">Scheduled</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {appointments.filter(appointment => appointment.status === 'completed').length}
                      </Typography>
                      <Typography variant="body2">Completed</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Upcoming Costs
                  </Typography>
                  <Box textAlign="center" p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="secondary.main">
                      {formatCurrency(getTotalEstimatedCost())}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Estimated for scheduled appointments
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Upcoming Appointments"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<Event />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2}>
                <List>
                  {getUpcomingAppointments().slice(0, 3).map((appointment, index) => (
                    <React.Fragment key={appointment.id}>
                      <ListItem alignItems="flex-start">
                        <ListItemAvatar>
                          <Avatar sx={{ bgcolor: 'primary.main' }}>
                            <Event />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText
                          primary={
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="subtitle1" fontWeight="medium">
                                {appointment.title}
                              </Typography>
                              {getTypeChip(appointment.type)}
                            </Box>
                          }
                          secondary={
                            <React.Fragment>
                              <Box display="flex" alignItems="center" mt={0.5}>
                                <CalendarToday fontSize="small" sx={{ fontSize: 14, mr: 0.5 }} />
                                <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                  {formatDate(appointment.date)} at {appointment.time}
                                </Typography>
                                <Chip
                                  label={`${calculateDaysRemaining(appointment.date)} days`}
                                  size="small"
                                  color={calculateDaysRemaining(appointment.date) < 3 ? "warning" : "default"}
                                />
                              </Box>
                              <Box display="flex" alignItems="center" mt={0.5}>
                                <Person fontSize="small" sx={{ fontSize: 14, mr: 0.5 }} />
                                <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                  {appointment.veterinarian}
                                </Typography>
                                <LocationOn fontSize="small" sx={{ fontSize: 14, mr: 0.5 }} />
                                <Typography variant="body2" color="text.secondary">
                                  {appointment.location}
                                </Typography>
                              </Box>
                              <Typography variant="body2" color="text.secondary" mt={0.5}>
                                Animals: {appointment.animals.join(', ')}
                              </Typography>
                            </React.Fragment>
                          }
                        />
                      </ListItem>
                      {index < getUpcomingAppointments().slice(0, 3).length - 1 && <Divider variant="inset" component="li" />}
                    </React.Fragment>
                  ))}
                  {getUpcomingAppointments().length === 0 && (
                    <ListItem>
                      <ListItemText primary="No upcoming appointments" />
                    </ListItem>
                  )}
                </List>
                {getUpcomingAppointments().length > 3 && (
                  <Box display="flex" justifyContent="center" mt={2}>
                    <CustomButton variant="outlined" size="small">
                      View All ({getUpcomingAppointments().length})
                    </CustomButton>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent sx={{ p: 0 }}>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                  <TableCell sx={{ color: 'white' }}>Title</TableCell>
                  <TableCell sx={{ color: 'white' }}>Date & Time</TableCell>
                  <TableCell sx={{ color: 'white' }}>Type</TableCell>
                  <TableCell sx={{ color: 'white' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white' }}>Veterinarian</TableCell>
                  <TableCell sx={{ color: 'white' }}>Location</TableCell>
                  <TableCell sx={{ color: 'white' }}>Animals</TableCell>
                  <TableCell sx={{ color: 'white' }}>Est. Cost</TableCell>
                  <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {appointments.map((appointment) => (
                  <TableRow key={appointment.id} hover>
                    <TableCell>{appointment.title}</TableCell>
                    <TableCell>{formatDate(appointment.date)} {appointment.time}</TableCell>
                    <TableCell>{getTypeChip(appointment.type)}</TableCell>
                    <TableCell>{getStatusChip(appointment.status)}</TableCell>
                    <TableCell>{appointment.veterinarian}</TableCell>
                    <TableCell>{appointment.location}</TableCell>
                    <TableCell>{appointment.animals.join(', ')}</TableCell>
                    <TableCell>
                      {appointment.estimatedCost
                        ? formatCurrency(appointment.estimatedCost)
                        : appointment.status === 'completed'
                          ? 'Billed'
                          : '-'}
                    </TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(appointment)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(appointment.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          </CardContent>
        </Card>
      </Box>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedAppointment ? 'Edit Appointment' : 'Book New Appointment'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="title"
                label="Appointment Title"
                defaultValue={selectedAppointment?.title || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="date"
                label="Date"
                type="date"
                defaultValue={selectedAppointment?.date || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="time"
                label="Time"
                type="time"
                defaultValue={selectedAppointment?.time || '09:00'}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="type-select-label">Appointment Type</InputLabel>
                <Select
                  labelId="type-select-label"
                  id="type-select"
                  label="Appointment Type"
                  defaultValue={selectedAppointment?.type || 'routine'}
                >
                  <MenuItem value="routine">Routine</MenuItem>
                  <MenuItem value="emergency">Emergency</MenuItem>
                  <MenuItem value="follow-up">Follow-up</MenuItem>
                  <MenuItem value="vaccination">Vaccination</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedAppointment?.status || 'scheduled'}
                >
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                  <MenuItem value="no-show">No Show</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="veterinarian"
                label="Veterinarian"
                defaultValue={selectedAppointment?.veterinarian || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="location"
                label="Location"
                defaultValue={selectedAppointment?.location || 'On-farm visit'}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="animals-select-label">Animals</InputLabel>
                <Select
                  labelId="animals-select-label"
                  id="animals-select"
                  label="Animals"
                  defaultValue="entire-herd"
                >
                  <MenuItem value="entire-herd">Entire Herd</MenuItem>
                  <MenuItem value="cattle-herd">Cattle Herd</MenuItem>
                  <MenuItem value="sheep-herd">Sheep Herd</MenuItem>
                  <MenuItem value="breeding-females">Breeding Females</MenuItem>
                  <MenuItem value="specific-animals">Specific Animals</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="estimatedCost"
                label="Estimated Cost (ZAR)"
                type="number"
                defaultValue={selectedAppointment?.estimatedCost || ''}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedAppointment?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedAppointment ? 'Update' : 'Book'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Appointments;
