/**
 * Utility function to get the correct path for images in the public folder
 * This handles different environments and deployment scenarios
 */
export const getPublicImagePath = (imagePath: string): string => {
  // Remove leading slash if present
  const cleanPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
  
  // Use PUBLIC_URL if available, otherwise use relative path
  const baseUrl = process.env.PUBLIC_URL || '';
  
  // Combine paths and ensure there's a single slash between them
  return `${baseUrl}/${cleanPath}`.replace(/([^:]\/)\/+/g, '$1');
};

/**
 * Utility function specifically for sponsor images
 */
export const getSponsorImagePath = (imageName: string): string => {
  return getPublicImagePath(`images/sponsors/${imageName}`);
};
