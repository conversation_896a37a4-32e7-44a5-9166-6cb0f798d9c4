import React, { useEffect, useState } from 'react';
import { Box } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { moduleBackgrounds, getAllModuleImages } from '../../utils/localImages';

interface ModuleBackgroundRotatorProps {
  module: string;
  children?: React.ReactNode;
  interval?: number;
  opacity?: number;
}

/**
 * A component that rotates through background images for a specific module
 */
const ModuleBackgroundRotator: React.FC<ModuleBackgroundRotatorProps> = ({
  module,
  children,
  interval = 10000, // 10 seconds by default
  opacity = 0.85
}) => {
  const { currentColor, availableColors } = useThemeContext();

  // Check if this is an excluded module (dashboard, login, commercial)
  const isExcludedModule = ['dashboard', 'login', 'commercial'].includes(module.toLowerCase());

  // Use the theme context color for excluded modules, force metallic blue for all others
  const themeColor = isExcludedModule
    ? availableColors[currentColor]
    : availableColors['metalBlue'];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Get module images
  const moduleKey = module.toLowerCase().replace('/', '') as keyof typeof moduleBackgrounds;

  // Use useMemo to prevent recreating the images array on every render
  const images = React.useMemo(() => {
    return moduleBackgrounds[moduleKey]
      ? getAllModuleImages(moduleKey)
      : [moduleBackgrounds.animals.main]; // Fallback to animals
  }, [moduleKey]); // Only recalculate when moduleKey changes

  useEffect(() => {
    // Only set up rotation if there are multiple images
    if (images.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [images, interval]);

  const currentImage = images[currentImageIndex];

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundImage: `linear-gradient(135deg, ${themeColor.primary}${Math.round(opacity * 99)}, ${themeColor.secondary}${Math.round(opacity * 85)}), url(${currentImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        transition: 'background-image 1s ease-in-out',
      }}
    >
      {children}
    </Box>
  );
};

export default ModuleBackgroundRotator;
