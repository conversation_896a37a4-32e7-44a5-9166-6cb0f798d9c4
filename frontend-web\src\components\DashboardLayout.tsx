import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { motion } from 'framer-motion';
import '../styles/backgrounds.css';
import '../styles/animations.css';
import { alpha, useMediaQuery } from '@mui/material';
import { OfflineIndicator, MongoDbStatus } from './common';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';

const DashboardLayout: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { theme } = useThemeContext();
  const { translate } = useLanguage();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // No need for getCurrentModule anymore as we're using solid colors

  useEffect(() => {
    // Simulate loading resources
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Close sidebar on mobile by default
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  // Toggle sidebar function
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // No need to update background image anymore as we're using solid colors

  return (
    <div className="flex h-screen">
      {isLoading ? (
        <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-primary-800">{translate('common.loading')} {translate('dashboard.welcome')}</h2>
            <p className="text-gray-600">{translate('dashboard.preparing')}</p>
          </div>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex w-full h-screen"
        >
          <Sidebar isOpen={sidebarOpen} onToggle={toggleSidebar} isMobile={isMobile} />
          <div className="flex-1 flex flex-col overflow-hidden">
            <Navbar onMenuClick={toggleSidebar} />
            <main
              className="flex-1 overflow-x-hidden overflow-y-auto custom-scrollbar relative"
            >
              {/* Enhanced Animated Background */}
              <div
                className="absolute inset-0 z-0 transition-all duration-1000 ease-in-out"
                style={{
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.95)}, ${alpha(theme.palette.primary.dark, 0.85)})`,
                  backgroundSize: '400% 400%',
                  backgroundPosition: 'center',
                  backgroundAttachment: 'fixed',
                  animation: 'gradientAnimation 15s ease infinite'
                }}
              />
              {/* Enhanced Gradient Overlay with Animation */}
              <div
                className="absolute inset-0 z-0 transition-all duration-1000 ease-in-out"
                style={{
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.15)}, ${alpha(theme.palette.secondary.light, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
                  backgroundSize: '200% 200%',
                  backdropFilter: 'blur(5px)',
                  animation: 'gradientAnimation 10s ease infinite'
                }}
              />
              {/* Enhanced Pattern Overlay */}
              <div
                className="absolute inset-0 z-0"
                style={{
                  backgroundImage: 'url(https://www.transparenttextures.com/patterns/cubes.png)',
                  opacity: 0.03
                }}
              />
              {/* Particle Effect Overlay */}
              <div
                className="absolute inset-0 z-0 overflow-hidden"
                style={{
                  background: 'radial-gradient(circle at 10% 20%, rgba(255,255,255,0.03) 0%, transparent 20%), radial-gradient(circle at 90% 80%, rgba(255,255,255,0.03) 0%, transparent 20%)',
                  opacity: 0.5
                }}
              />
              <div className="min-h-full relative z-10">
                <Outlet />
              </div>
              {/* Status Indicators */}
              <div className="fixed bottom-4 left-4 z-50 flex flex-col gap-2">
                <OfflineIndicator />
                <MongoDbStatus />
              </div>
            </main>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DashboardLayout;