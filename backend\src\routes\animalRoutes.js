const express = require('express');
const router = express.Router();
const animalController = require('../controllers/mongodb/animalController');

// Define routes
router.get('/', animalController.getAllAnimals);
router.get('/statistics', animalController.getAnimalStatistics);
router.get('/tag/:tagNumber', animalController.getAnimalByTagNumber);
router.get('/:id', animalController.getAnimalById);
router.post('/', animalController.createAnimal);
router.put('/:id', animalController.updateAnimal);
router.delete('/:id', animalController.deleteAnimal);

module.exports = router;