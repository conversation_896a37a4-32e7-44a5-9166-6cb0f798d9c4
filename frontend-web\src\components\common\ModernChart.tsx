import React, { useState, useRef, useEffect } from 'react';
import { Box, Paper, Typography, IconButton, Menu, MenuItem, useTheme, alpha, Tooltip, Divider, Chip, Select, FormControl, InputLabel, SelectChangeEvent } from '@mui/material';
import { createDebouncedResizeObserver } from '../../utils/resizeObserverFix';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Scatter,
  ScatterChart,
  ZAxis
} from 'recharts';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>List,
  Pie<PERSON><PERSON> as PieChartIcon,
  Bar<PERSON>hart as BarChartIcon,
  Show<PERSON>hart as LineChartIcon,
  Insert<PERSON>hart as AreaChartIcon,
  InsertChart,
  Download,
  Print,
  Share,
  Info,
  Refresh
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { ErrorBoundary } from '.';
import { getSafeElevation, getSafeAnimation } from '../../utils/themeDefaults';

type ChartType = 'pie' | 'bar' | 'line' | 'area' | 'radar' | 'scatter';
type TimeRange = 'day' | 'week' | 'month' | 'quarter' | 'year' | 'all';

export interface ModernChartProps {
  title: string;
  subtitle?: string;
  data: any[];
  type?: ChartType;
  dataKeys: string[];
  xAxisDataKey?: string;
  height?: number;
  colors?: string[];
  accentColor?: string;
  allowChartTypeChange?: boolean;
  allowTimeRangeChange?: boolean;
  onFilter?: () => void;
  onRefresh?: () => void;
  onDownload?: () => void;
  onClick?: (data: any) => void;
  tooltip?: string;
  module?: string;
  showLegend?: boolean;
  stacked?: boolean;
  formatValue?: (value: any) => string;
  formatAxis?: (value: any) => string;
  emptyMessage?: string;
  loading?: boolean;
}

const ModernChart: React.FC<ModernChartProps> = ({
  title,
  subtitle,
  data,
  type = 'bar',
  dataKeys,
  xAxisDataKey = 'name',
  height = 350,
  colors,
  accentColor,
  allowChartTypeChange = false,
  allowTimeRangeChange = false,
  onFilter,
  onRefresh,
  onDownload,
  onClick,
  tooltip,
  module,
  showLegend = true,
  stacked = false,
  formatValue,
  formatAxis,
  emptyMessage,
  loading = false
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const [chartType, setChartType] = useState<ChartType>(type);
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [chartTypeMenuAnchorEl, setChartTypeMenuAnchorEl] = useState<null | HTMLElement>(null);

  // Create a ref for the chart container
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // Apply debounced resize observer to prevent ResizeObserver loop errors
  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create a debounced resize observer
    const observer = createDebouncedResizeObserver(() => {
      // This is intentionally empty - we just want to debounce the resize events
      // to prevent ResizeObserver loop errors
    }, 100);

    // Observe the chart container
    observer.observe(chartContainerRef.current);

    // Clean up
    return () => {
      observer.disconnect();
    };
  }, []);

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return accentColor || theme.palette.primary.main;
    }
  };

  const mainColor = getModuleColor();

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
    '#8884d8',
    '#82ca9d',
    '#ffc658',
    '#ff8042'
  ];

  const chartColors = colors || defaultColors;

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleChartTypeMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setChartTypeMenuAnchorEl(event.currentTarget);
  };

  const handleChartTypeMenuClose = () => {
    setChartTypeMenuAnchorEl(null);
  };

  const handleChartTypeChange = (newType: ChartType) => {
    setChartType(newType);
    handleChartTypeMenuClose();
  };

  const handleTimeRangeChange = (event: SelectChangeEvent<TimeRange>) => {
    setTimeRange(event.target.value as TimeRange);
  };

  const getTimeRangeLabel = (range: TimeRange): string => {
    switch (range) {
      case 'day':
        return translate('chart.time_range.day');
      case 'week':
        return translate('chart.time_range.week');
      case 'month':
        return translate('chart.time_range.month');
      case 'quarter':
        return translate('chart.time_range.quarter');
      case 'year':
        return translate('chart.time_range.year');
      case 'all':
        return translate('chart.time_range.all');
      default:
        return translate('chart.time_range.month');
    }
  };

  const renderChart = () => {
    if (!data || data.length === 0) {
      return (
        <Box
          sx={{
            height: height,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
            color: 'text.secondary'
          }}
        >
          <InsertChart sx={{ fontSize: 48, opacity: 0.5, mb: 2 }} />
          <Typography variant="body1">
            {emptyMessage || translate('chart.no_data')}
          </Typography>
        </Box>
      );
    }

    switch (chartType) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={height / 4}
                innerRadius={height / 8}
                fill={mainColor}
                dataKey={dataKeys[0]}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                ))}
              </Pie>
              <RechartsTooltip
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value,
                  dataKeys[0]
                ]}
              />
              {showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        );

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <RechartsTooltip
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value
                ]}
                contentStyle={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: getSafeElevation(theme, 2)
                }}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={chartColors[index % chartColors.length]}
                  stackId={stacked ? "stack" : undefined}
                  radius={[4, 4, 0, 0]}
                  animationDuration={1500}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <RechartsTooltip
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value
                ]}
                contentStyle={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: getSafeElevation(theme, 2)
                }}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={chartColors[index % chartColors.length]}
                  strokeWidth={3}
                  dot={{ r: 4, strokeWidth: 2 }}
                  activeDot={{ r: 6, strokeWidth: 0 }}
                  animationDuration={1500}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
              <XAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <YAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <RechartsTooltip
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value
                ]}
                contentStyle={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: getSafeElevation(theme, 2)
                }}
              />
              {showLegend && <Legend />}
              {dataKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stackId={stacked ? "1" : undefined}
                  stroke={chartColors[index % chartColors.length]}
                  fill={alpha(chartColors[index % chartColors.length], 0.6)}
                  animationDuration={1500}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'radar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <RadarChart cx="50%" cy="50%" outerRadius={height / 3} data={data}>
              <PolarGrid stroke={alpha(theme.palette.divider, 0.3)} />
              <PolarAngleAxis
                dataKey={xAxisDataKey}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
              />
              <PolarRadiusAxis
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              {dataKeys.map((key, index) => (
                <Radar
                  key={key}
                  name={key}
                  dataKey={key}
                  stroke={chartColors[index % chartColors.length]}
                  fill={alpha(chartColors[index % chartColors.length], 0.6)}
                  animationDuration={1500}
                />
              ))}
              <RechartsTooltip
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value
                ]}
                contentStyle={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: getSafeElevation(theme, 2)
                }}
              />
              {showLegend && <Legend />}
            </RadarChart>
          </ResponsiveContainer>
        );

      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <ScatterChart
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
              <XAxis
                dataKey={dataKeys[0]}
                type="number"
                name={dataKeys[0]}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <YAxis
                dataKey={dataKeys[1]}
                type="number"
                name={dataKeys[1]}
                tick={{ fill: theme.palette.text.secondary, fontSize: 12 }}
                tickFormatter={formatAxis}
              />
              <ZAxis
                dataKey={dataKeys[2] || 'z'}
                range={[50, 400]}
                name={dataKeys[2] || 'z'}
              />
              <RechartsTooltip
                cursor={{ strokeDasharray: '3 3' }}
                formatter={(value: any) => [
                  formatValue ? formatValue(value) : value
                ]}
                contentStyle={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 8,
                  boxShadow: getSafeElevation(theme, 2)
                }}
              />
              {showLegend && <Legend />}
              <Scatter
                name={title}
                data={data}
                fill={mainColor}
                animationDuration={1500}
              />
            </ScatterChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <ErrorBoundary>
      <Paper
        elevation={0}
        ref={chartContainerRef}
        onClick={onClick ? () => onClick(data) : undefined}
        sx={{
          cursor: onClick ? 'pointer' : 'default',
        borderRadius: 3,
        overflow: 'hidden',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: getSafeAnimation(theme, 'medium'),
        '&:hover': {
          boxShadow: `0 8px 30px ${alpha(theme.palette.common.black, 0.1)}`,
          transform: 'translateY(-4px)',
        },
        position: 'relative',
        backgroundColor: 'transparent',
        backdropFilter: 'blur(10px)',
        border: 'none',
        background: `linear-gradient(135deg,
          ${alpha(mainColor, 0.15)},
          ${alpha(mainColor, 0.05)})`,
        boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`
      }}
    >
      {/* Chart Header */}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          borderBottom: 'none',
          background: `linear-gradient(135deg,
            ${alpha(mainColor, 0.2)},
            ${alpha(mainColor, 0.1)})`
        }}
      >
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              {title}
            </Typography>
            {tooltip && (
              <Tooltip title={tooltip} arrow>
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <Info fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {allowTimeRangeChange && (
            <FormControl size="small" sx={{ minWidth: 120, mr: 1 }}>
              <Select
                value={timeRange}
                onChange={handleTimeRangeChange}
                displayEmpty
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(theme.palette.divider, 0.3)
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(mainColor, 0.5)
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: mainColor
                  },
                  fontSize: '0.875rem'
                }}
              >
                <MenuItem value="day">{getTimeRangeLabel('day')}</MenuItem>
                <MenuItem value="week">{getTimeRangeLabel('week')}</MenuItem>
                <MenuItem value="month">{getTimeRangeLabel('month')}</MenuItem>
                <MenuItem value="quarter">{getTimeRangeLabel('quarter')}</MenuItem>
                <MenuItem value="year">{getTimeRangeLabel('year')}</MenuItem>
                <MenuItem value="all">{getTimeRangeLabel('all')}</MenuItem>
              </Select>
            </FormControl>
          )}

          {allowChartTypeChange && (
            <Tooltip title={translate('chart.change_type')}>
              <IconButton
                size="small"
                onClick={handleChartTypeMenuOpen}
                sx={{ mr: 1 }}
              >
                {chartType === 'pie' && <PieChartIcon fontSize="small" />}
                {chartType === 'bar' && <BarChartIcon fontSize="small" />}
                {chartType === 'line' && <LineChartIcon fontSize="small" />}
                {chartType === 'area' && <AreaChartIcon fontSize="small" />}
                {chartType === 'radar' && <PieChartIcon fontSize="small" />}
                {chartType === 'scatter' && <PieChartIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
          )}

          {onRefresh && (
            <Tooltip title={translate('common.refresh')}>
              <IconButton
                size="small"
                onClick={onRefresh}
                sx={{ mr: 1 }}
              >
                <Refresh fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {onFilter && (
            <Tooltip title={translate('common.filter')}>
              <IconButton
                size="small"
                onClick={onFilter}
                sx={{ mr: 1 }}
              >
                <FilterList fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          <Tooltip title={translate('common.more')}>
            <IconButton
              size="small"
              onClick={handleMenuOpen}
            >
              <MoreVert fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Chart Content */}
      <Box sx={{ p: 2, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ flex: 1, minHeight: height }}>
          {loading ? (
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column'
              }}
            >
              <motion.div
                animate={{
                  rotate: 360,
                  transition: {
                    duration: 2,
                    ease: "linear",
                    repeat: Infinity
                  }
                }}
              >
                <InsertChart sx={{ fontSize: 48, color: alpha(mainColor, 0.5) }} />
              </motion.div>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                {translate('common.loading')}
              </Typography>
            </Box>
          ) : (
            renderChart()
          )}
        </Box>
      </Box>

      {/* Chart Type Menu */}
      <Menu
        anchorEl={chartTypeMenuAnchorEl}
        open={Boolean(chartTypeMenuAnchorEl)}
        onClose={handleChartTypeMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              borderRadius: 2,
              minWidth: 180
            }
          }
        }}
      >
        <MenuItem onClick={() => handleChartTypeChange('bar')}>
          <BarChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.bar')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('line')}>
          <LineChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.line')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('area')}>
          <AreaChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.area')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('pie')}>
          <PieChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.pie')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('radar')}>
          <PieChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.radar')}
        </MenuItem>
        <MenuItem onClick={() => handleChartTypeChange('scatter')}>
          <PieChartIcon fontSize="small" sx={{ mr: 2 }} />
          {translate('chart.type.scatter')}
        </MenuItem>
      </Menu>

      {/* Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              borderRadius: 2,
              minWidth: 180
            }
          }
        }}
      >
        {onDownload && (
          <MenuItem onClick={() => { onDownload(); handleMenuClose(); }}>
            <Download fontSize="small" sx={{ mr: 2 }} />
            {translate('common.download')}
          </MenuItem>
        )}
        <MenuItem onClick={handleMenuClose}>
          <Print fontSize="small" sx={{ mr: 2 }} />
          {translate('common.print')}
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <Share fontSize="small" sx={{ mr: 2 }} />
          {translate('common.share')}
        </MenuItem>
      </Menu>
    </Paper>
    </ErrorBoundary>
  );
};

export default ModernChart;
