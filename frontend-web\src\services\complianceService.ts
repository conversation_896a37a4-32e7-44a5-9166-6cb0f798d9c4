interface Document {
  id: string;
  type: 'certificate' | 'permit' | 'license' | 'inspection';
  title: string;
  issueDate: string;
  expiryDate: string;
  status: 'valid' | 'expired' | 'pending';
  attachmentUrl: string;
  reminderDays: number;
}

interface Requirement {
  id: string;
  category: string;
  description: string;
  frequency: 'once' | 'monthly' | 'quarterly' | 'annually';
  nextDueDate: string;
  status: 'compliant' | 'non-compliant' | 'pending';
}

const complianceService = {
  getDocuments: async (): Promise<Document[]> => {
    // Implementation
    return [];
  },

  getRequirements: async (): Promise<Requirement[]> => {
    // Implementation
    return [];
  },

  uploadDocument: async (document: FormData): Promise<Document> => {
    return {
      id: String(Date.now()),
      type: 'certificate',
      title: 'New Document',
      issueDate: new Date().toISOString(),
      expiryDate: new Date().toISOString(),
      status: 'valid',
      attachmentUrl: '',
      reminderDays: 30
    };
  },
};

export default complianceService;
