import React, { useState } from 'react';
import CustomButton from '../components/common/CustomButton';
import { Box, Typography, Card, CardContent, Grid, Paper, Divider } from '@mui/material';
import { useLanguage, LANGUAGES, LANGUAGE_NAMES } from '../contexts/LanguageContext';

const TranslationTest: React.FC = () => {
  const { language, setLanguage, translate, isLoading } = useLanguage();
  const [selectedModule, setSelectedModule] = useState('animals');

  const modules = [
    'animals',
    'breeding',
    'health',
    'feeding',
    'commercial',
    'financial',
    'reports',
    'compliance',
    'settings'
  ];

  const renderTranslationTest = (module: string) => {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {module.charAt(0).toUpperCase() + module.slice(1)} Module Translations
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Title:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {translate(`dashboard.${module}.title`, { fallback: `${module} Title` })}
                </Typography>

                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Description:
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {translate(`dashboard.${module}.description`, { fallback: `${module} Description` })}
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Stats:
                </Typography>
                <Typography variant="body2" gutterBottom>
                  {Object.keys(translate(`dashboard.${module}.stats`, { fallback: '{}' }) || {}).map(stat => (
                    <Box key={stat} sx={{ mb: 1 }}>
                      {translate(`dashboard.${module}.stats.${stat}`, { fallback: stat })}
                    </Box>
                  ))}
                </Typography>

                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Submodules:
                </Typography>
                <Typography variant="body2">
                  {Object.keys(translate(`dashboard.${module}.submodules`, { fallback: '{}' }) || {}).map(submodule => (
                    <Box key={submodule} sx={{ mb: 1 }}>
                      {translate(`dashboard.${module}.submodules.${submodule}`, { fallback: submodule })}
                    </Box>
                  ))}
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Translation Test Page
      </Typography>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current Language: {LANGUAGE_NAMES[language]} ({language})
          </Typography>

          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Select Language:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {Object.entries(LANGUAGES).map(([name, code]) => (
                <CustomButton
                  key={code}
                  variant={language === code ? 'contained' : 'outlined'}
                  onClick={() => setLanguage(code)}
                  size="small"
                >
                  {LANGUAGE_NAMES[code]}
                </CustomButton>
              ))}
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle1" gutterBottom>
            Select Module to Test:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {modules.map(module => (
              <CustomButton
                key={module}
                variant={selectedModule === module ? 'contained' : 'outlined'}
                onClick={() => setSelectedModule(module)}
                size="small"
              >
                {module.charAt(0).toUpperCase() + module.slice(1)}
              </CustomButton>
            ))}
          </Box>
        </CardContent>
      </Card>

      {isLoading ? (
        <Typography>Loading translations...</Typography>
      ) : (
        renderTranslationTest(selectedModule)
      )}
    </Box>
  );
};

export default TranslationTest;
