import React, { useState, useEffect } from 'react';
import CustomButton from '../../common/CustomButton';
import { Box, Card, CardContent, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Tooltip, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, Grid, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  FilterList,
  Download,
  Search,
  Clear,
  Info
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useFeedingData } from '../../../hooks/useFeedingData';
import { FeedingRecord, FeedInventory } from '../../../types/feeding';
import { format } from 'date-fns';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useSnackbar } from '../../../contexts/SnackbarContext';
import { useMongoDb } from '../../../contexts/SimpleMongoDbContext';

const FeedingRecordsList: React.FC = () => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();
  const { isConnected } = useMongoDb();

  const {
    feedingRecords,
    feedInventory,
    loading,
    error,
    fetchFeedingData,
    addFeedingRecord,
    updateFeedingRecord,
    deleteFeedingRecord,
    generateExcelReport
  } = useFeedingData();

  const [filteredRecords, setFilteredRecords] = useState<FeedingRecord[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);
  const [dateRange, setDateRange] = useState<{
    startDate: Date | null;
    endDate: Date | null;
  }>({
    startDate: null,
    endDate: null
  });
  const [selectedFeedType, setSelectedFeedType] = useState<string>('');
  const [selectedAnimalGroup, setSelectedAnimalGroup] = useState<string>('');

  // Dialog states
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogMode, setDialogMode] = useState<'add' | 'edit'>('add');
  const [currentRecord, setCurrentRecord] = useState<FeedingRecord | null>(null);
  const [formData, setFormData] = useState<Partial<FeedingRecord>>({
    date: new Date(),
    feedId: '',
    quantity: 0,
    animalGroupId: '',
    notes: '',
    location: ''
  });

  // Delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<string | null>(null);

  // Export loading state
  const [exportLoading, setExportLoading] = useState(false);

  // Filter records when search term or filters change
  useEffect(() => {
    if (!loading && feedingRecords.length > 0) {
      let filtered = [...feedingRecords];

      // Apply search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filtered = filtered.filter(record =>
          record.feedType?.toLowerCase().includes(term) ||
          (record.animalGroup && record.animalGroup.toLowerCase().includes(term)) ||
          record.location?.toLowerCase().includes(term) ||
          record.notes?.toLowerCase().includes(term)
        );
      }

      // Apply date range filter
      if (dateRange.startDate) {
        filtered = filtered.filter(record =>
          new Date(record.date) >= new Date(dateRange.startDate!)
        );
      }

      if (dateRange.endDate) {
        filtered = filtered.filter(record =>
          new Date(record.date) <= new Date(dateRange.endDate!)
        );
      }

      // Apply feed type filter
      if (selectedFeedType) {
        filtered = filtered.filter(record =>
          record.feedType === selectedFeedType
        );
      }

      // Apply animal group filter
      if (selectedAnimalGroup) {
        filtered = filtered.filter(record =>
          record.animalGroupId === selectedAnimalGroup
        );
      }

      // Sort by date (newest first)
      filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setFilteredRecords(filtered);
    } else {
      setFilteredRecords([]);
    }
  }, [loading, feedingRecords, searchTerm, dateRange, selectedFeedType, selectedAnimalGroup]);

  // Get unique feed types and animal groups for filters
  const uniqueFeedTypes = Array.from(new Set(feedingRecords.map(record => record.feedType)));
  const uniqueAnimalGroups = Array.from(new Set(feedingRecords.filter(record => record.animalGroup).map(record => record.animalGroup as string)));

  // Handle dialog open for add/edit
  const handleOpenDialog = (mode: 'add' | 'edit', record?: FeedingRecord) => {
    setDialogMode(mode);

    if (mode === 'edit' && record) {
      setCurrentRecord(record);
      setFormData({
        date: new Date(record.date),
        feedId: record.feedId,
        quantity: record.quantity,
        animalGroupId: record.animalGroupId,
        notes: record.notes || '',
        location: record.location || ''
      });
    } else {
      setCurrentRecord(null);
      setFormData({
        date: new Date(),
        feedId: '',
        quantity: 0,
        animalGroupId: '',
        notes: '',
        location: ''
      });
    }

    setDialogOpen(true);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? parseFloat(value) : value
    }));
  };

  // Handle select changes
  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle date change
  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({
      ...prev,
      date: date || new Date()
    }));
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (!formData.feedId) {
        showSnackbar('Please select a feed type', 'error');
        return;
      }

      if (!formData.animalGroupId) {
        showSnackbar('Please select an animal group', 'error');
        return;
      }

      if (!formData.quantity || formData.quantity <= 0) {
        showSnackbar('Please enter a valid quantity', 'error');
        return;
      }

      // Get feed details
      const selectedFeed = feedInventory.find(feed => feed.id === formData.feedId);
      if (!selectedFeed) {
        showSnackbar('Selected feed not found', 'error');
        return;
      }

      // Check if there's enough inventory
      if (dialogMode === 'add' && selectedFeed.quantity < formData.quantity!) {
        showSnackbar(`Not enough ${selectedFeed.name} in inventory`, 'error');
        return;
      }

      // Prepare record data
      const recordData: any = {
        ...formData,
        feedType: selectedFeed.name,
        unit: selectedFeed.unit,
        unitCost: selectedFeed.costPerUnit,
        totalCost: (selectedFeed.costPerUnit || 0) * (formData.quantity || 0),
        animalGroup: feedingRecords.find(r => r.animalGroupId === formData.animalGroupId)?.animalGroup || 'Unknown Group'
      };

      if (dialogMode === 'add') {
        await addFeedingRecord(recordData as Omit<FeedingRecord, 'id'>);
        showSnackbar('Feeding record added successfully', 'success');
      } else if (dialogMode === 'edit' && currentRecord) {
        await updateFeedingRecord(currentRecord.id, recordData);
        showSnackbar('Feeding record updated successfully', 'success');
      }

      setDialogOpen(false);
      await fetchFeedingData();
    } catch (err) {
      console.error('Error submitting feeding record:', err);
      showSnackbar('Failed to save feeding record', 'error');
    }
  };

  // Handle record deletion
  const handleDeleteRecord = async () => {
    if (!recordToDelete) return;

    try {
      await deleteFeedingRecord(recordToDelete);
      showSnackbar('Feeding record deleted successfully', 'success');
      setDeleteDialogOpen(false);
      setRecordToDelete(null);
      await fetchFeedingData();
    } catch (err) {
      console.error('Error deleting feeding record:', err);
      showSnackbar('Failed to delete feeding record', 'error');
    }
  };

  // Handle export to Excel
  const handleExport = async () => {
    try {
      setExportLoading(true);
      const filename = await generateExcelReport('records');
      showSnackbar(`Report generated: ${filename}`, 'success');
    } catch (err) {
      console.error('Error generating report:', err);
      showSnackbar('Failed to generate report', 'error');
    } finally {
      setExportLoading(false);
    }
  };

  // Reset filters
  const handleResetFilters = () => {
    setSearchTerm('');
    setDateRange({ startDate: null, endDate: null });
    setSelectedFeedType('');
    setSelectedAnimalGroup('');
    setFilterDialogOpen(false);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3
          }}
        >
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Feeding Records
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage and track all feeding activities
            </Typography>
          </Box>
          <Box>
            <CustomButton
              variant="outlined"
              startIcon={<FilterList />}
              onClick={() => setFilterDialogOpen(true)}
              sx={{ mr: 1 }}
            >
              Filter
            </CustomButton>
            <CustomButton
              variant="outlined"
              startIcon={exportLoading ? <CircularProgress size={20} /> : <Download />}
              onClick={handleExport}
              disabled={exportLoading || loading}
              sx={{ mr: 1 }}
            >
              Export
            </CustomButton>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('add')}
            >
              Add Record
            </CustomButton>
          </Box>
        </Box>

        {/* Search Bar */}
        <Card
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            background: alpha(theme.palette.primary.main, 0.05)
          }}
        >
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Search sx={{ color: 'text.secondary', mr: 1 }} />
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Search by feed type, animal group, location or notes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                InputProps={{
                  endAdornment: searchTerm ? (
                    <IconButton size="small" onClick={() => setSearchTerm('')}>
                      <Clear fontSize="small" />
                    </IconButton>
                  ) : null
                }}
              />
            </Box>

            {/* Active Filters */}
            {(dateRange.startDate || dateRange.endDate || selectedFeedType || selectedAnimalGroup) && (
              <Box sx={{ mt: 2, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                  Active filters:
                </Typography>

                {dateRange.startDate && (
                  <Chip
                    size="small"
                    label={`From: ${format(dateRange.startDate, 'dd MMM yyyy')}`}
                    onDelete={() => setDateRange(prev => ({ ...prev, startDate: null }))}
                  />
                )}

                {dateRange.endDate && (
                  <Chip
                    size="small"
                    label={`To: ${format(dateRange.endDate, 'dd MMM yyyy')}`}
                    onDelete={() => setDateRange(prev => ({ ...prev, endDate: null }))}
                  />
                )}

                {selectedFeedType && (
                  <Chip
                    size="small"
                    label={`Feed: ${selectedFeedType}`}
                    onDelete={() => setSelectedFeedType('')}
                  />
                )}

                {selectedAnimalGroup && (
                  <Chip
                    size="small"
                    label={`Group: ${selectedAnimalGroup}`}
                    onDelete={() => setSelectedAnimalGroup('')}
                  />
                )}

                <CustomButton
                  size="small"
                  variant="text"
                  onClick={handleResetFilters}
                >
                  Clear All
                </CustomButton>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Records Table */}
        <Card
          sx={{
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            background: alpha(theme.palette.primary.main, 0.05)
          }}
        >
          <CardContent>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="error">{error}</Typography>
                <CustomButton
                  variant="outlined"
                  onClick={() => fetchFeedingData()}
                  sx={{ mt: 2 }}
                >
                  Retry
                </CustomButton>
              </Box>
            ) : filteredRecords.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography>No feeding records found</Typography>
                <CustomButton
                  variant="outlined"
                  onClick={() => handleOpenDialog('add')}
                  sx={{ mt: 2 }}
                >
                  Add Record
                </CustomButton>
              </Box>
            ) : (
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Feed Type</TableCell>
                      <TableCell>Animal Group</TableCell>
                      <TableCell align="right">Quantity</TableCell>
                      <TableCell align="right">Cost</TableCell>
                      <TableCell>Location</TableCell>
                      <TableCell>Notes</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredRecords.map((record) => (
                      <TableRow key={record.id}>
                        <TableCell>
                          {record.date instanceof Date
                            ? format(record.date, 'dd MMM yyyy')
                            : format(new Date(record.date), 'dd MMM yyyy')}
                        </TableCell>
                        <TableCell>{record.feedType}</TableCell>
                        <TableCell>{record.animalGroup || 'Unknown Group'}</TableCell>
                        <TableCell align="right">
                          {record.quantity} {record.unit}
                        </TableCell>
                        <TableCell align="right">
                          R {record.totalCost?.toLocaleString() || 'N/A'}
                        </TableCell>
                        <TableCell>{record.location}</TableCell>
                        <TableCell>
                          {record.notes ? (
                            <Tooltip title={record.notes}>
                              <Info fontSize="small" color="action" />
                            </Tooltip>
                          ) : (
                            '-'
                          )}
                        </TableCell>
                        <TableCell align="right">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog('edit', record)}
                            color="primary"
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => {
                              setRecordToDelete(record.id);
                              setDeleteDialogOpen(true);
                            }}
                            color="error"
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>

        {/* Filter Dialog */}
        <Dialog
          open={filterDialogOpen}
          onClose={() => setFilterDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>Filter Feeding Records</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Start Date"
                  value={dateRange.startDate}
                  onChange={(date) => setDateRange(prev => ({ ...prev, startDate: date }))}
                  slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="End Date"
                  value={dateRange.endDate}
                  onChange={(date) => setDateRange(prev => ({ ...prev, endDate: date }))}
                  slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Feed Type</InputLabel>
                  <Select
                    value={selectedFeedType}
                    onChange={(e) => setSelectedFeedType(e.target.value)}
                    label="Feed Type"
                  >
                    <MenuItem value="">All Feed Types</MenuItem>
                    {uniqueFeedTypes.map((type) => (
                      <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Animal Group</InputLabel>
                  <Select
                    value={selectedAnimalGroup}
                    onChange={(e) => setSelectedAnimalGroup(e.target.value)}
                    label="Animal Group"
                  >
                    <MenuItem value="">All Animal Groups</MenuItem>
                    {uniqueAnimalGroups.map((group) => (
                      <MenuItem key={group} value={group}>{group}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <CustomButton onClick={handleResetFilters}>Reset</CustomButton>
            <CustomButton onClick={() => setFilterDialogOpen(false)}>Apply</CustomButton>
          </DialogActions>
        </Dialog>

        {/* Add/Edit Record Dialog */}
        <Dialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            {dialogMode === 'add' ? 'Add Feeding Record' : 'Edit Feeding Record'}
          </DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Date"
                  value={formData.date}
                  onChange={handleDateChange}
                  slotProps={{ textField: { fullWidth: true, size: 'small' } }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Feed</InputLabel>
                  <Select
                    name="feedId"
                    value={formData.feedId}
                    onChange={handleSelectChange}
                    label="Feed"
                  >
                    <MenuItem value="">Select Feed</MenuItem>
                    {feedInventory.map((feed) => (
                      <MenuItem key={feed.id} value={feed.id}>
                        {feed.name} ({feed.quantity} {feed.unit} available)
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Quantity"
                  name="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={handleInputChange}
                  size="small"
                  InputProps={{
                    endAdornment: formData.feedId ? (
                      <Typography variant="caption" color="text.secondary">
                        {feedInventory.find(feed => feed.id === formData.feedId)?.unit || ''}
                      </Typography>
                    ) : null
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>Animal Group</InputLabel>
                  <Select
                    name="animalGroupId"
                    value={formData.animalGroupId}
                    onChange={handleSelectChange}
                    label="Animal Group"
                  >
                    <MenuItem value="">Select Animal Group</MenuItem>
                    {/* Get unique animal group IDs and names */}
                    {Array.from(new Set(feedingRecords.map(record => record.animalGroupId))).map(groupId => {
                      const groupName = feedingRecords.find(record => record.animalGroupId === groupId)?.animalGroup || 'Unknown';
                      return (
                        <MenuItem key={groupId} value={groupId}>{groupName}</MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Location"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  size="small"
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <CustomButton onClick={() => setDialogOpen(false)}>Cancel</CustomButton>
            <CustomButton onClick={handleSubmit} variant="contained">Save</CustomButton>
          </DialogActions>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={deleteDialogOpen}
          onClose={() => setDeleteDialogOpen(false)}
        >
          <DialogTitle>Confirm Delete</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete this feeding record? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <CustomButton onClick={() => setDeleteDialogOpen(false)}>Cancel</CustomButton>
            <CustomButton onClick={handleDeleteRecord} color="error">Delete</CustomButton>
          </DialogActions>
        </Dialog>
      </motion.div>
    </LocalizationProvider>
  );
};

export default FeedingRecordsList;
