import { useAuth } from '../contexts/AuthContext';

// Define permission types
export type Permission = 
  | 'dashboard:view'
  | 'animals:view' | 'animals:create' | 'animals:edit' | 'animals:delete'
  | 'health:view' | 'health:create' | 'health:edit' | 'health:delete'
  | 'breeding:view' | 'breeding:create' | 'breeding:edit' | 'breeding:delete'
  | 'feed:view' | 'feed:create' | 'feed:edit' | 'feed:delete'
  | 'commercial:view' | 'commercial:create' | 'commercial:edit' | 'commercial:delete'
  | 'compliance:view' | 'compliance:create' | 'compliance:edit' | 'compliance:delete'
  | 'reports:view' | 'reports:create' | 'reports:export'
  | 'financial:view' | 'financial:create' | 'financial:edit' | 'financial:delete'
  | 'resources:view' | 'resources:create' | 'resources:edit' | 'resources:delete'
  | 'settings:view' | 'settings:edit' | 'users:manage';

// Define role types
export type Role = 'admin' | 'manager' | 'staff' | 'veterinarian' | 'viewer';

// Define role-based permissions
const rolePermissions: Record<Role, Permission[]> = {
  admin: [
    'dashboard:view',
    'animals:view', 'animals:create', 'animals:edit', 'animals:delete',
    'health:view', 'health:create', 'health:edit', 'health:delete',
    'breeding:view', 'breeding:create', 'breeding:edit', 'breeding:delete',
    'feed:view', 'feed:create', 'feed:edit', 'feed:delete',
    'commercial:view', 'commercial:create', 'commercial:edit', 'commercial:delete',
    'compliance:view', 'compliance:create', 'compliance:edit', 'compliance:delete',
    'reports:view', 'reports:create', 'reports:export',
    'financial:view', 'financial:create', 'financial:edit', 'financial:delete',
    'resources:view', 'resources:create', 'resources:edit', 'resources:delete',
    'settings:view', 'settings:edit', 'users:manage'
  ],
  manager: [
    'dashboard:view',
    'animals:view', 'animals:create', 'animals:edit',
    'health:view', 'health:create', 'health:edit',
    'breeding:view', 'breeding:create', 'breeding:edit',
    'feed:view', 'feed:create', 'feed:edit',
    'commercial:view', 'commercial:create', 'commercial:edit',
    'compliance:view', 'compliance:create', 'compliance:edit',
    'reports:view', 'reports:create', 'reports:export',
    'financial:view', 'financial:create', 'financial:edit',
    'resources:view', 'resources:create', 'resources:edit',
    'settings:view'
  ],
  staff: [
    'dashboard:view',
    'animals:view', 'animals:create', 'animals:edit',
    'health:view', 'health:create',
    'breeding:view', 'breeding:create',
    'feed:view', 'feed:create',
    'commercial:view',
    'compliance:view',
    'reports:view',
    'financial:view',
    'resources:view'
  ],
  veterinarian: [
    'dashboard:view',
    'animals:view',
    'health:view', 'health:create', 'health:edit', 'health:delete',
    'breeding:view',
    'feed:view',
    'reports:view', 'reports:create', 'reports:export'
  ],
  viewer: [
    'dashboard:view',
    'animals:view',
    'health:view',
    'breeding:view',
    'feed:view',
    'commercial:view',
    'compliance:view',
    'reports:view',
    'financial:view',
    'resources:view'
  ]
};

/**
 * Hook for checking user permissions
 * @returns Object with permission checking functions
 */
export const usePermissions = () => {
  const { user } = useAuth();
  
  /**
   * Check if user has a specific permission
   * @param permission Permission to check
   * @returns Boolean indicating if user has permission
   */
  const hasPermission = (permission: Permission): boolean => {
    // If no user or role, deny access
    if (!user || !user.role) {
      return false;
    }
    
    // For development, admin has all permissions
    if (user.role === 'admin') {
      return true;
    }
    
    // Check if user's role has the requested permission
    const userRole = user.role as Role;
    return rolePermissions[userRole]?.includes(permission) || false;
  };
  
  /**
   * Check if user has any of the specified permissions
   * @param permissions Array of permissions to check
   * @returns Boolean indicating if user has any of the permissions
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };
  
  /**
   * Check if user has all of the specified permissions
   * @param permissions Array of permissions to check
   * @returns Boolean indicating if user has all of the permissions
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };
  
  /**
   * Get all permissions for the current user
   * @returns Array of permissions the user has
   */
  const getUserPermissions = (): Permission[] => {
    if (!user || !user.role) {
      return [];
    }
    
    const userRole = user.role as Role;
    return rolePermissions[userRole] || [];
  };
  
  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserPermissions
  };
};

export default usePermissions;
