import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';

/**
 * A wrapper component that ensures a safe theme is always provided
 * This helps prevent "Cannot read properties of undefined (reading 'dark')" errors
 */
const ThemeSafeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Create a fallback theme to use if the main theme context is not available
  const fallbackTheme = createTheme({
    palette: {
      mode: 'dark',
      primary: {
        main: '#3AA99F',
        light: '#4FBEB4',
        dark: '#2A8A82',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#38B2AC',
        light: '#4FD1CB',
        dark: '#2C8A84',
        contrastText: '#ffffff',
      },
      background: {
        default: '#121212',
        paper: '#1e1e1e',
      },
      text: {
        primary: '#e2e8f0',
        secondary: '#94a3b8',
      },
    },
  });

  return (
    <ThemeProvider theme={fallbackTheme}>
      {children}
    </ThemeProvider>
  );
};

export default ThemeSafeWrapper;
