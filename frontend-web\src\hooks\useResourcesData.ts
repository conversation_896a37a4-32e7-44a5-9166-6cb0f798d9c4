import { useState, useEffect, useCallback } from 'react';
import { useMockData } from '../utils/config';
import api from '../services/apiService';

// Define types for resources data
export interface ResourceGuide {
  id: string;
  title: string;
  category: string;
  description: string;
  fileUrl?: string;
  thumbnailUrl?: string;
  dateAdded: string;
  author: string;
  tags: string[];
  viewCount: number;
  downloadCount: number;
}

export interface TrainingResource {
  id: string;
  title: string;
  type: 'video' | 'document' | 'course' | 'webinar';
  description: string;
  duration?: string;
  instructor?: string;
  dateAdded: string;
  url?: string;
  thumbnailUrl?: string;
  tags: string[];
  viewCount: number;
  completionCount: number;
}

export interface SupportResource {
  id: string;
  title: string;
  type: 'faq' | 'contact' | 'troubleshooting';
  description: string;
  content: string;
  dateAdded: string;
  lastUpdated: string;
  contactInfo?: {
    name: string;
    email: string;
    phone?: string;
  };
  tags: string[];
  viewCount: number;
}

export interface ResourcesStats {
  totalGuides: number;
  totalTrainingResources: number;
  totalSupportResources: number;
  mostViewedGuide: string;
  mostViewedTraining: string;
  recentlyAdded: number;
}

// Create mock data
const mockGuides: ResourceGuide[] = Array.from({ length: 12 }, (_, i) => {
  const categories = ['livestock', 'farming', 'health', 'breeding', 'financial', 'compliance'];
  const authors = [
    'Department of Agriculture',
    'South African Agricultural Research Council',
    'National Wool Growers Association',
    'Red Meat Producers Organization',
    'Milk Producers Organization'
  ];
  const tags = ['cattle', 'sheep', 'goats', 'farming', 'health', 'breeding', 'nutrition', 'management'];
  
  const dateAdded = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  
  return {
    id: `GUIDE-${(i + 1).toString().padStart(3, '0')}`,
    title: `Guide to ${categories[i % categories.length].charAt(0).toUpperCase() + categories[i % categories.length].slice(1)} Management ${i + 1}`,
    category: categories[i % categories.length],
    description: `Comprehensive guide to ${categories[i % categories.length]} management for South African farmers.`,
    fileUrl: `https://example.com/guides/guide-${i + 1}.pdf`,
    thumbnailUrl: `https://example.com/thumbnails/guide-${i + 1}.jpg`,
    dateAdded: dateAdded.toISOString().split('T')[0],
    author: authors[i % authors.length],
    tags: [tags[i % tags.length], tags[(i + 1) % tags.length], tags[(i + 2) % tags.length]],
    viewCount: Math.floor(Math.random() * 1000),
    downloadCount: Math.floor(Math.random() * 500)
  };
});

const mockTrainingResources: TrainingResource[] = Array.from({ length: 10 }, (_, i) => {
  const types: TrainingResource['type'][] = ['video', 'document', 'course', 'webinar'];
  const titles = [
    'Introduction to Livestock Management',
    'Advanced Breeding Techniques',
    'Animal Health and Disease Prevention',
    'Financial Management for Farmers',
    'Sustainable Farming Practices',
    'Pasture Management',
    'Livestock Nutrition',
    'Farm Safety',
    'Marketing Your Farm Products',
    'Record Keeping for Farmers'
  ];
  const instructors = [
    'Dr. van der Merwe',
    'Prof. Naidoo',
    'Dr. Molefe',
    'Prof. Botha',
    'Dr. Ndlovu'
  ];
  const tags = ['training', 'education', 'skills', 'development', 'learning'];
  
  const dateAdded = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  const type = types[i % types.length];
  
  return {
    id: `TRAIN-${(i + 1).toString().padStart(3, '0')}`,
    title: titles[i % titles.length],
    type,
    description: `${type.charAt(0).toUpperCase() + type.slice(1)} training resource for ${titles[i % titles.length].toLowerCase()}.`,
    duration: type === 'video' || type === 'webinar' ? `${Math.floor(Math.random() * 120) + 30} minutes` : undefined,
    instructor: type === 'video' || type === 'course' || type === 'webinar' ? instructors[i % instructors.length] : undefined,
    dateAdded: dateAdded.toISOString().split('T')[0],
    url: `https://example.com/training/${type}-${i + 1}`,
    thumbnailUrl: `https://example.com/thumbnails/training-${i + 1}.jpg`,
    tags: [tags[i % tags.length], tags[(i + 1) % tags.length]],
    viewCount: Math.floor(Math.random() * 800),
    completionCount: Math.floor(Math.random() * 300)
  };
});

const mockSupportResources: SupportResource[] = Array.from({ length: 8 }, (_, i) => {
  const types: SupportResource['type'][] = ['faq', 'contact', 'troubleshooting'];
  const titles = [
    'Frequently Asked Questions',
    'Contact Support',
    'Troubleshooting Common Issues',
    'Technical Support',
    'Customer Service',
    'Help Center',
    'Getting Started Guide',
    'System Requirements'
  ];
  const tags = ['support', 'help', 'assistance', 'guidance', 'troubleshooting'];
  
  const dateAdded = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
  const lastUpdated = new Date(dateAdded);
  lastUpdated.setDate(lastUpdated.getDate() + Math.floor(Math.random() * 30));
  
  const type = types[i % types.length];
  
  return {
    id: `SUPP-${(i + 1).toString().padStart(3, '0')}`,
    title: titles[i % titles.length],
    type,
    description: `Support resource for ${titles[i % titles.length].toLowerCase()}.`,
    content: `Detailed content for ${titles[i % titles.length].toLowerCase()}.`,
    dateAdded: dateAdded.toISOString().split('T')[0],
    lastUpdated: lastUpdated.toISOString().split('T')[0],
    contactInfo: type === 'contact' ? {
      name: 'Support Team',
      email: '<EMAIL>',
      phone: '+27 12 345 6789'
    } : undefined,
    tags: [tags[i % tags.length], tags[(i + 1) % tags.length]],
    viewCount: Math.floor(Math.random() * 1200)
  };
});

// Find the most viewed guide and training
const mostViewedGuide = mockGuides.reduce((prev, current) => (prev.viewCount > current.viewCount) ? prev : current);
const mostViewedTraining = mockTrainingResources.reduce((prev, current) => (prev.viewCount > current.viewCount) ? prev : current);

const mockStats: ResourcesStats = {
  totalGuides: mockGuides.length,
  totalTrainingResources: mockTrainingResources.length,
  totalSupportResources: mockSupportResources.length,
  mostViewedGuide: mostViewedGuide.title,
  mostViewedTraining: mostViewedTraining.title,
  recentlyAdded: 5
};

// Create the hook
export const useResourcesData = () => {
  const [guides, setGuides] = useState<ResourceGuide[]>([]);
  const [trainingResources, setTrainingResources] = useState<TrainingResource[]>([]);
  const [supportResources, setSupportResources] = useState<SupportResource[]>([]);
  const [stats, setStats] = useState<ResourcesStats>({
    totalGuides: 0,
    totalTrainingResources: 0,
    totalSupportResources: 0,
    mostViewedGuide: '',
    mostViewedTraining: '',
    recentlyAdded: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchResourcesData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setGuides(mockGuides);
        setTrainingResources(mockTrainingResources);
        setSupportResources(mockSupportResources);
        setStats(mockStats);
      } else {
        // Use real API
        const [guidesRes, trainingRes, supportRes, statsRes] = await Promise.all([
          api.get('/resources/guides'),
          api.get('/resources/training'),
          api.get('/resources/support'),
          api.get('/resources/stats')
        ]);

        setGuides(guidesRes.data);
        setTrainingResources(trainingRes.data);
        setSupportResources(supportRes.data);
        setStats(statsRes.data);
      }
    } catch (err) {
      console.error('Error fetching resources data:', err);
      setError('Failed to fetch resources data');
    } finally {
      setLoading(false);
    }
  }, []);

  // CRUD operations for guides
  const addGuide = async (guide: Omit<ResourceGuide, 'id' | 'viewCount' | 'downloadCount'>) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const newGuide: ResourceGuide = {
          ...guide,
          id: `GUIDE-${(guides.length + 1).toString().padStart(3, '0')}`,
          viewCount: 0,
          downloadCount: 0
        };
        
        setGuides(prev => [...prev, newGuide]);
        updateStats();
        
        return newGuide;
      } else {
        const response = await api.post('/resources/guides', guide);
        await fetchResourcesData();
        return response.data;
      }
    } catch (err) {
      console.error('Error adding guide:', err);
      setError('Failed to add guide');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateGuide = async (id: string, guide: Partial<ResourceGuide>) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setGuides(prev => prev.map(g => g.id === id ? { ...g, ...guide } : g));
        updateStats();
        
        return guides.find(g => g.id === id);
      } else {
        const response = await api.put(`/resources/guides/${id}`, guide);
        await fetchResourcesData();
        return response.data;
      }
    } catch (err) {
      console.error('Error updating guide:', err);
      setError('Failed to update guide');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteGuide = async (id: string) => {
    try {
      setLoading(true);
      
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setGuides(prev => prev.filter(g => g.id !== id));
        updateStats();
      } else {
        await api.delete(`/resources/guides/${id}`);
        await fetchResourcesData();
      }
    } catch (err) {
      console.error('Error deleting guide:', err);
      setError('Failed to delete guide');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Similar CRUD operations for training and support resources...

  // Update stats
  const updateStats = () => {
    // Find the most viewed guide and training
    const mostViewedGuide = guides.reduce((prev, current) => (prev.viewCount > current.viewCount) ? prev : current, guides[0]);
    const mostViewedTraining = trainingResources.reduce((prev, current) => (prev.viewCount > current.viewCount) ? prev : current, trainingResources[0]);
    
    // Count recently added resources (in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentlyAdded = [
      ...guides,
      ...trainingResources,
      ...supportResources
    ].filter(resource => {
      const dateAdded = new Date(resource.dateAdded);
      return dateAdded >= thirtyDaysAgo;
    }).length;
    
    setStats({
      totalGuides: guides.length,
      totalTrainingResources: trainingResources.length,
      totalSupportResources: supportResources.length,
      mostViewedGuide: mostViewedGuide?.title || '',
      mostViewedTraining: mostViewedTraining?.title || '',
      recentlyAdded
    });
  };

  // Initial data fetch
  useEffect(() => {
    fetchResourcesData();
  }, [fetchResourcesData]);

  return {
    guides,
    trainingResources,
    supportResources,
    stats,
    loading,
    error,
    fetchResourcesData,
    addGuide,
    updateGuide,
    deleteGuide
    // Add other CRUD operations as needed
  };
};
