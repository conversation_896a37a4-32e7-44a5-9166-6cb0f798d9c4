const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class ReportingService {
  constructor() {
    this.reportTypes = {
      INVENTORY: 'inventory',
      HEALTH: 'health',
      FINANCIAL: 'financial',
      BREEDING: 'breeding',
      GROWTH: 'growth'
    };
  }

  async generateReport(type, data, format = 'excel') {
    try {
      const reportDir = path.join('reports', type);
      await fs.mkdir(reportDir, { recursive: true });

      const filename = `${type}_report_${Date.now()}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
      const filepath = path.join(reportDir, filename);

      if (format === 'excel') {
        await this.generateExcelReport(type, data, filepath);
      } else {
        await this.generatePDFReport(type, data, filepath);
      }

      return filepath;
    } catch (error) {
      logger.error('Report generation error:', error);
      throw new Error('Failed to generate report');
    }
  }

  async generateExcelReport(type, data, filepath) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(type);

    switch (type) {
      case this.reportTypes.INVENTORY:
        this.formatInventoryWorksheet(worksheet, data);
        break;
      case this.reportTypes.FINANCIAL:
        this.formatFinancialWorksheet(worksheet, data);
        break;
      // Add more report types...
    }

    await workbook.xlsx.writeFile(filepath);
  }

  async generatePDFReport(type, data, filepath) {
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(filepath);

    return new Promise((resolve, reject) => {
      doc.pipe(stream);

      switch (type) {
        case this.reportTypes.HEALTH:
          this.formatHealthPDF(doc, data);
          break;
        case this.reportTypes.BREEDING:
          this.formatBreedingPDF(doc, data);
          break;
        // Add more report types...
      }

      doc.end();
      stream.on('finish', resolve);
      stream.on('error', reject);
    });
  }

  formatInventoryWorksheet(worksheet, data) {
    worksheet.columns = [
      { header: 'Item ID', key: 'id', width: 15 },
      { header: 'Name', key: 'name', width: 20 },
      { header: 'Category', key: 'category', width: 15 },
      { header: 'Quantity', key: 'quantity', width: 10 },
      { header: 'Unit', key: 'unit', width: 10 },
      { header: 'Location', key: 'location', width: 20 },
      { header: 'Last Updated', key: 'lastUpdated', width: 20 }
    ];

    worksheet.addRows(data);

    // Add styling
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD3D3D3' }
    };
  }

  formatFinancialWorksheet(worksheet, data) {
    worksheet.columns = [
      { header: 'Date', key: 'date', width: 15 },
      { header: 'Type', key: 'type', width: 15 },
      { header: 'Category', key: 'category', width: 20 },
      { header: 'Amount', key: 'amount', width: 15 },
      { header: 'Description', key: 'description', width: 30 }
    ];

    worksheet.addRows(data);

    // Add formatting for currency
    worksheet.getColumn('amount').numFmt = '"R"#,##0.00;[Red]-"R"#,##0.00';
  }
}

module.exports = new ReportingService();