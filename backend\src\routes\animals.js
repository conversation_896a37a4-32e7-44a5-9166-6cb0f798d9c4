const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const animalController = require('../controllers/animalController');

// Get all animals with pagination and filtering
/**
 * @swagger
 * /api/animals:
 *   get:
 *     summary: Get all animals
 *     tags: [Animals]
 *     responses:
 *       200:
 *         description: List of animals
 *       500:
 *         description: Server error
 */
router.get('/', animalController.getAllAnimals);

// Get animal statistics
router.get('/statistics', authenticateToken, animalController.getAnimalStatistics);

// Get animal by ID
router.get('/:id', authenticateToken, animalController.getAnimalById);

// Get animal by tag number
router.get('/tag/:tagNumber', authenticateToken, animalController.getAnimalByTagNumber);

// Create new animal
router.post('/', authenticateToken, animalController.createAnimal);

// Update animal
router.put('/:id', authenticateToken, animalController.updateAnimal);

// Delete animal
router.delete('/:id', authenticateToken, animalController.deleteAnimal);

module.exports = router;
