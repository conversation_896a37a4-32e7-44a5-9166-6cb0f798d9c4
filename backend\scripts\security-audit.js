const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * Security Audit Script for AgriIntel Livestock Management System
 * 
 * This script performs a comprehensive security audit to identify:
 * - Hardcoded credentials
 * - Weak JWT secrets
 * - Missing environment variables
 * - Insecure configurations
 * - Potential vulnerabilities
 */

class SecurityAuditor {
  constructor() {
    this.issues = [];
    this.warnings = [];
    this.recommendations = [];
    this.projectRoot = path.resolve(__dirname, '../..');
  }

  /**
   * Run complete security audit
   */
  async runAudit() {
    console.log('🔒 Starting Security Audit for AgriIntel...\n');
    
    this.checkEnvironmentVariables();
    this.checkHardcodedCredentials();
    this.checkJWTSecrets();
    this.checkFilePermissions();
    this.checkDependencyVulnerabilities();
    this.checkCORSConfiguration();
    this.checkPasswordPolicies();
    this.checkLoggingConfiguration();
    
    this.generateReport();
  }

  /**
   * Check environment variables configuration
   */
  checkEnvironmentVariables() {
    console.log('📋 Checking Environment Variables...');
    
    const requiredEnvVars = [
      'MONGODB_URI',
      'JWT_SECRET',
      'NODE_ENV',
      'PORT'
    ];
    
    const productionEnvVars = [
      'SMTP_HOST',
      'SMTP_USER',
      'SMTP_PASS',
      'CORS_ORIGIN',
      'LOG_LEVEL'
    ];
    
    // Check if .env file exists
    const envPath = path.join(this.projectRoot, 'backend/.env');
    if (!fs.existsSync(envPath)) {
      this.issues.push('❌ Missing .env file in backend directory');
    }
    
    // Check required environment variables
    requiredEnvVars.forEach(envVar => {
      if (!process.env[envVar]) {
        this.issues.push(`❌ Missing required environment variable: ${envVar}`);
      }
    });
    
    // Check production environment variables
    if (process.env.NODE_ENV === 'production') {
      productionEnvVars.forEach(envVar => {
        if (!process.env[envVar]) {
          this.warnings.push(`⚠️ Missing production environment variable: ${envVar}`);
        }
      });
    }
    
    console.log('✅ Environment variables check completed\n');
  }

  /**
   * Check for hardcoded credentials in source files
   */
  checkHardcodedCredentials() {
    console.log('🔍 Scanning for Hardcoded Credentials...');
    
    const suspiciousPatterns = [
      /password\s*[:=]\s*['"][^'"]{3,}['"]/gi,
      /api[_-]?key\s*[:=]\s*['"][^'"]{10,}['"]/gi,
      /secret\s*[:=]\s*['"][^'"]{10,}['"]/gi,
      /token\s*[:=]\s*['"][^'"]{20,}['"]/gi,
      /mongodb:\/\/[^:]+:[^@]+@/gi,
      /mysql:\/\/[^:]+:[^@]+@/gi,
      /postgres:\/\/[^:]+:[^@]+@/gi
    ];
    
    const excludePatterns = [
      /\.env\.example/,
      /\.env\.template/,
      /node_modules/,
      /\.git/,
      /build/,
      /dist/
    ];
    
    this.scanDirectory(this.projectRoot, suspiciousPatterns, excludePatterns);
    
    console.log('✅ Hardcoded credentials scan completed\n');
  }

  /**
   * Check JWT secret strength
   */
  checkJWTSecrets() {
    console.log('🔐 Checking JWT Secret Strength...');
    
    const jwtSecret = process.env.JWT_SECRET;
    
    if (jwtSecret) {
      if (jwtSecret.length < 32) {
        this.issues.push('❌ JWT secret is too short (minimum 32 characters recommended)');
      }
      
      if (jwtSecret === 'your-secret-key' || jwtSecret === 'secret' || jwtSecret === 'agriintel-secret-key-2024') {
        this.issues.push('❌ JWT secret appears to be a default/weak value');
      }
      
      // Check entropy
      const entropy = this.calculateEntropy(jwtSecret);
      if (entropy < 4.0) {
        this.warnings.push('⚠️ JWT secret has low entropy (consider using a more random string)');
      }
    }
    
    console.log('✅ JWT secret check completed\n');
  }

  /**
   * Check file permissions
   */
  checkFilePermissions() {
    console.log('📁 Checking File Permissions...');
    
    const sensitiveFiles = [
      'backend/.env',
      'backend/.env.production',
      'backend/src/config/database.js',
      'backend/src/services/authService.js'
    ];
    
    sensitiveFiles.forEach(filePath => {
      const fullPath = path.join(this.projectRoot, filePath);
      if (fs.existsSync(fullPath)) {
        try {
          const stats = fs.statSync(fullPath);
          const mode = stats.mode & parseInt('777', 8);
          
          // Check if file is world-readable
          if (mode & parseInt('004', 8)) {
            this.warnings.push(`⚠️ Sensitive file ${filePath} is world-readable`);
          }
          
          // Check if file is world-writable
          if (mode & parseInt('002', 8)) {
            this.issues.push(`❌ Sensitive file ${filePath} is world-writable`);
          }
        } catch (error) {
          this.warnings.push(`⚠️ Could not check permissions for ${filePath}`);
        }
      }
    });
    
    console.log('✅ File permissions check completed\n');
  }

  /**
   * Check for dependency vulnerabilities
   */
  checkDependencyVulnerabilities() {
    console.log('📦 Checking Dependency Vulnerabilities...');
    
    const packageJsonPath = path.join(this.projectRoot, 'backend/package.json');
    
    if (fs.existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        // Check for known vulnerable packages
        const vulnerablePackages = [
          'lodash@4.17.15',
          'moment@2.24.0',
          'axios@0.18.0'
        ];
        
        Object.keys(dependencies).forEach(pkg => {
          const version = dependencies[pkg];
          const pkgVersion = `${pkg}@${version}`;
          
          if (vulnerablePackages.includes(pkgVersion)) {
            this.issues.push(`❌ Vulnerable dependency detected: ${pkgVersion}`);
          }
        });
        
        this.recommendations.push('💡 Run "npm audit" regularly to check for vulnerabilities');
        this.recommendations.push('💡 Consider using "npm audit fix" to automatically fix vulnerabilities');
        
      } catch (error) {
        this.warnings.push('⚠️ Could not parse package.json for vulnerability check');
      }
    }
    
    console.log('✅ Dependency vulnerabilities check completed\n');
  }

  /**
   * Check CORS configuration
   */
  checkCORSConfiguration() {
    console.log('🌐 Checking CORS Configuration...');
    
    const corsOrigin = process.env.CORS_ORIGIN;
    
    if (!corsOrigin) {
      this.warnings.push('⚠️ CORS_ORIGIN not configured');
    } else if (corsOrigin === '*') {
      this.issues.push('❌ CORS configured to allow all origins (security risk)');
    } else if (corsOrigin.includes('localhost') && process.env.NODE_ENV === 'production') {
      this.warnings.push('⚠️ CORS configured for localhost in production environment');
    }
    
    console.log('✅ CORS configuration check completed\n');
  }

  /**
   * Check password policies
   */
  checkPasswordPolicies() {
    console.log('🔑 Checking Password Policies...');
    
    // This would typically check your password validation rules
    this.recommendations.push('💡 Ensure password policy requires: 8+ chars, uppercase, lowercase, numbers, symbols');
    this.recommendations.push('💡 Implement account lockout after failed login attempts');
    this.recommendations.push('💡 Consider implementing 2FA for admin accounts');
    
    console.log('✅ Password policies check completed\n');
  }

  /**
   * Check logging configuration
   */
  checkLoggingConfiguration() {
    console.log('📝 Checking Logging Configuration...');
    
    const logLevel = process.env.LOG_LEVEL;
    
    if (!logLevel) {
      this.warnings.push('⚠️ LOG_LEVEL not configured');
    } else if (logLevel === 'debug' && process.env.NODE_ENV === 'production') {
      this.issues.push('❌ Debug logging enabled in production (may expose sensitive data)');
    }
    
    this.recommendations.push('💡 Ensure sensitive data is not logged (passwords, tokens, etc.)');
    this.recommendations.push('💡 Implement log rotation to prevent disk space issues');
    this.recommendations.push('💡 Consider centralized logging for production environments');
    
    console.log('✅ Logging configuration check completed\n');
  }

  /**
   * Scan directory for suspicious patterns
   */
  scanDirectory(dir, patterns, excludePatterns) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const relativePath = path.relative(this.projectRoot, filePath);
      
      // Skip excluded patterns
      if (excludePatterns.some(pattern => pattern.test(relativePath))) {
        return;
      }
      
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        this.scanDirectory(filePath, patterns, excludePatterns);
      } else if (stat.isFile() && this.isTextFile(filePath)) {
        try {
          const content = fs.readFileSync(filePath, 'utf8');
          
          patterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
              matches.forEach(match => {
                this.issues.push(`❌ Potential hardcoded credential in ${relativePath}: ${match.substring(0, 50)}...`);
              });
            }
          });
        } catch (error) {
          // Skip files that can't be read
        }
      }
    });
  }

  /**
   * Check if file is a text file
   */
  isTextFile(filePath) {
    const textExtensions = ['.js', '.ts', '.json', '.env', '.md', '.txt', '.yml', '.yaml', '.xml', '.html', '.css', '.sql'];
    const ext = path.extname(filePath).toLowerCase();
    return textExtensions.includes(ext);
  }

  /**
   * Calculate entropy of a string
   */
  calculateEntropy(str) {
    const freq = {};
    str.split('').forEach(char => {
      freq[char] = (freq[char] || 0) + 1;
    });
    
    let entropy = 0;
    const len = str.length;
    
    Object.values(freq).forEach(count => {
      const p = count / len;
      entropy -= p * Math.log2(p);
    });
    
    return entropy;
  }

  /**
   * Generate security audit report
   */
  generateReport() {
    console.log('📊 SECURITY AUDIT REPORT');
    console.log('========================\n');
    
    console.log(`🔍 Issues Found: ${this.issues.length}`);
    console.log(`⚠️ Warnings: ${this.warnings.length}`);
    console.log(`💡 Recommendations: ${this.recommendations.length}\n`);
    
    if (this.issues.length > 0) {
      console.log('🚨 CRITICAL ISSUES (Must Fix):');
      this.issues.forEach(issue => console.log(`  ${issue}`));
      console.log('');
    }
    
    if (this.warnings.length > 0) {
      console.log('⚠️ WARNINGS (Should Fix):');
      this.warnings.forEach(warning => console.log(`  ${warning}`));
      console.log('');
    }
    
    if (this.recommendations.length > 0) {
      console.log('💡 RECOMMENDATIONS:');
      this.recommendations.forEach(rec => console.log(`  ${rec}`));
      console.log('');
    }
    
    // Generate security score
    const totalChecks = this.issues.length + this.warnings.length + this.recommendations.length;
    const criticalIssues = this.issues.length;
    const score = Math.max(0, 100 - (criticalIssues * 20) - (this.warnings.length * 5));
    
    console.log(`🏆 SECURITY SCORE: ${score}/100`);
    
    if (score >= 90) {
      console.log('✅ Excellent security posture!');
    } else if (score >= 70) {
      console.log('⚠️ Good security, but improvements needed');
    } else {
      console.log('❌ Security improvements required before production deployment');
    }
    
    console.log('\n========================');
    console.log('Security audit completed!');
  }
}

// Run the audit
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.runAudit().catch(console.error);
}

module.exports = SecurityAuditor;
