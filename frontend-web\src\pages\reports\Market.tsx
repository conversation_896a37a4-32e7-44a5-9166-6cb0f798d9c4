import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, useTheme, Tabs, Tab, Divider, Avatar, List, ListItem, ListItemText, ListItemAvatar, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CheckCircle,
  Warning,
  CalendarToday,
  FilterList,
  Search,
  ArrowForward,
  MoreVert,
  TrendingUp,
  TrendingDown,
  BarChart,
  PieChart,
  Timeline,
  AttachMoney,
  ShowChart,
  Download,
  Print,
  Share
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { format, parseISO, addDays, isBefore, isAfter } from 'date-fns';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { formatCurrency } from '../../utils/formatters';
import { generateReport, ReportParams, getSavedReports } from '../../services/reportService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Mock data for market prices
const mockMarketPrices = [
  { month: 'Jan', beef: 42.50, mutton: 65.75, pork: 38.20 },
  { month: 'Feb', beef: 43.25, mutton: 66.50, pork: 37.80 },
  { month: 'Mar', beef: 44.00, mutton: 67.25, pork: 38.50 },
  { month: 'Apr', beef: 45.75, mutton: 68.00, pork: 39.25 },
  { month: 'May', beef: 46.50, mutton: 69.75, pork: 40.00 },
  { month: 'Jun', beef: 47.25, mutton: 70.50, pork: 41.75 },
  { month: 'Jul', beef: 48.00, mutton: 71.25, pork: 42.50 },
  { month: 'Aug', beef: 47.50, mutton: 70.75, pork: 41.25 },
  { month: 'Sep', beef: 46.75, mutton: 69.50, pork: 40.75 },
  { month: 'Oct', beef: 45.50, mutton: 68.25, pork: 39.50 },
  { month: 'Nov', beef: 44.25, mutton: 67.00, pork: 38.75 },
  { month: 'Dec', beef: 43.75, mutton: 66.25, pork: 38.00 }
];

// Mock data for market trends
const mockMarketTrends = [
  { id: 'TR001', category: 'Beef', currentPrice: 'R45.50/kg', change: '+2.5%', forecast: 'Increasing', demand: 'High' },
  { id: 'TR002', category: 'Mutton', currentPrice: 'R68.25/kg', change: '-1.2%', forecast: 'Stable', demand: 'Medium' },
  { id: 'TR003', category: 'Pork', currentPrice: 'R39.50/kg', change: '+3.8%', forecast: 'Increasing', demand: 'High' },
  { id: 'TR004', category: 'Dairy', currentPrice: 'R12.75/L', change: '+0.8%', forecast: 'Stable', demand: 'Medium' },
  { id: 'TR005', category: 'Wool', currentPrice: 'R85.00/kg', change: '-2.5%', forecast: 'Decreasing', demand: 'Low' }
];

// Mock data for market opportunities
const mockMarketOpportunities = [
  {
    id: 'OPP001',
    title: 'Export to Namibia',
    category: 'Beef',
    potentialRevenue: 'R250,000',
    requirements: 'Health certification, export permit',
    deadline: '2024-06-30',
    status: 'open'
  },
  {
    id: 'OPP002',
    title: 'Local Supermarket Chain',
    category: 'Pork',
    potentialRevenue: 'R180,000',
    requirements: 'Quality certification, consistent supply',
    deadline: '2024-05-15',
    status: 'open'
  },
  {
    id: 'OPP003',
    title: 'Restaurant Supplier',
    category: 'Mutton',
    potentialRevenue: 'R120,000',
    requirements: 'Premium quality, weekly delivery',
    deadline: '2024-04-30',
    status: 'closed'
  },
  {
    id: 'OPP004',
    title: 'Botswana Export',
    category: 'Beef',
    potentialRevenue: 'R320,000',
    requirements: 'Health certification, export permit, quality assurance',
    deadline: '2024-07-15',
    status: 'open'
  },
  {
    id: 'OPP005',
    title: 'Wool Processing Company',
    category: 'Wool',
    potentialRevenue: 'R85,000',
    requirements: 'Quality certification, minimum quantity',
    deadline: '2024-05-30',
    status: 'open'
  }
];

const Market: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('year');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [savedReports, setSavedReports] = useState<any[]>([]);

  useEffect(() => {
    // Load saved reports when component mounts
    const loadSavedReports = async () => {
      try {
        const reports = await getSavedReports('market');
        setSavedReports(reports);
      } catch (error) {
        console.error('Error loading saved reports:', error);
      }
    };

    loadSavedReports();
  }, []);

  const handleGenerateReport = async () => {
    try {
      setIsGenerating(true);

      const params: ReportParams = {
        type: 'market',
        format: 'pdf',
        timePeriod: timeRange as any,
      };

      await generateReport(params);

      // Refresh the saved reports list
      const reports = await getSavedReports('market');
      setSavedReports(reports);
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Calculate market statistics
  const latestBeefPrice = mockMarketPrices[mockMarketPrices.length - 1].beef;
  const previousBeefPrice = mockMarketPrices[mockMarketPrices.length - 2].beef;
  const beefPriceChange = ((latestBeefPrice - previousBeefPrice) / previousBeefPrice) * 100;

  const latestMuttonPrice = mockMarketPrices[mockMarketPrices.length - 1].mutton;
  const previousMuttonPrice = mockMarketPrices[mockMarketPrices.length - 2].mutton;
  const muttonPriceChange = ((latestMuttonPrice - previousMuttonPrice) / previousMuttonPrice) * 100;

  const latestPorkPrice = mockMarketPrices[mockMarketPrices.length - 1].pork;
  const previousPorkPrice = mockMarketPrices[mockMarketPrices.length - 2].pork;
  const porkPriceChange = ((latestPorkPrice - previousPorkPrice) / previousPorkPrice) * 100;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenDialog = (opportunity?: any) => {
    setSelectedOpportunity(opportunity || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedOpportunity(null);
  };

  const handleSave = () => {
    // Save logic would go here
    handleCloseDialog();
  };

  return (
    <Box>
      <ModuleHeader
        title="Livestock Market Intelligence"
        subtitle="Track market trends, prices, and identify optimal sales opportunities for your livestock"
        module="reports"
        submodule="market"
        actionLabel={isGenerating ? "Generating..." : "Export Market Report"}
        actionIcon={<Download />}
        onAction={handleGenerateReport}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Market Intelligence Dashboard */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Paper
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  mr: 2
                }}
              >
                <TrendingUp />
              </Box>
              <Typography variant="h5" fontWeight="bold">
                Market Intelligence Dashboard
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="primary.main">
                    Price Forecast Alert
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Beef prices expected to rise 8.5%</strong> in the next 30 days based on seasonal patterns and supply constraints. Consider delaying sales of ready-to-market cattle.
                  </Typography>
                  <CustomButton variant="outlined" size="small" sx={{ mt: 1 }}>
                    View Price Forecast
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="success.main">
                    Market Opportunity
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Premium prices available for A-grade beef</strong> in Gauteng region. Current premium of R5.50/kg above national average for quality stock.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="success" sx={{ mt: 1 }}>
                    View Buyers
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="warning.main">
                    Supply Chain Alert
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>Transport costs increasing by 12%</strong> next month due to fuel price increases. Consider coordinating shipments with neighboring farms to share costs.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="warning" sx={{ mt: 1 }}>
                    View Logistics Options
                  </CustomButton>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
            Current Market Prices
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Beef"
                  subtitle={`Current Price: R${latestBeefPrice.toFixed(2)}/kg`}
                  module="reports"
                  uniqueId="market-beef"
                  icon={beefPriceChange >= 0 ? <TrendingUp /> : <TrendingDown />}
                  height={160}
                  accentColor={beefPriceChange >= 0 ? theme.palette.success.main : theme.palette.error.main}
                  content={
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {beefPriceChange >= 0 ? '+' : ''}{beefPriceChange.toFixed(1)}% from last month
                    </Typography>
                  }
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Mutton"
                  subtitle={`Current Price: R${latestMuttonPrice.toFixed(2)}/kg`}
                  module="reports"
                  uniqueId="market-mutton"
                  icon={muttonPriceChange >= 0 ? <TrendingUp /> : <TrendingDown />}
                  height={160}
                  accentColor={muttonPriceChange >= 0 ? theme.palette.success.main : theme.palette.error.main}
                  content={
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {muttonPriceChange >= 0 ? '+' : ''}{muttonPriceChange.toFixed(1)}% from last month
                    </Typography>
                  }
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Pork"
                  subtitle={`Current Price: R${latestPorkPrice.toFixed(2)}/kg`}
                  module="reports"
                  uniqueId="market-pork"
                  icon={porkPriceChange >= 0 ? <TrendingUp /> : <TrendingDown />}
                  height={160}
                  accentColor={porkPriceChange >= 0 ? theme.palette.success.main : theme.palette.error.main}
                  content={
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {porkPriceChange >= 0 ? '+' : ''}{porkPriceChange.toFixed(1)}% from last month
                    </Typography>
                  }
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Price Trends Chart */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Price Trends
          </Typography>

          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title="Market Price Trends"
              subtitle="12-month price trends for major livestock categories"
              module="reports"
              uniqueId="market-trends"
              height={450}
              accentColor={theme.palette.primary.main}
              action={
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Time Range</InputLabel>
                  <Select
                    value={timeRange}
                    label="Time Range"
                    onChange={(e) => setTimeRange(e.target.value as 'month' | 'quarter' | 'year')}
                    sx={{ color: 'white', '.MuiOutlinedInput-notchedOutline': { borderColor: 'rgba(255,255,255,0.5)' } }}
                  >
                    <MenuItem value="month">1 Month</MenuItem>
                    <MenuItem value="quarter">3 Months</MenuItem>
                    <MenuItem value="year">12 Months</MenuItem>
                  </Select>
                </FormControl>
              }
              content={
                <Box sx={{ height: 350, mt: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={mockMarketPrices}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`R${value}/kg`, '']} />
                      <Legend />
                      <Line type="monotone" dataKey="beef" name="Beef" stroke={theme.palette.error.main} strokeWidth={2} activeDot={{ r: 8 }} />
                      <Line type="monotone" dataKey="mutton" name="Mutton" stroke={theme.palette.primary.main} strokeWidth={2} activeDot={{ r: 8 }} />
                      <Line type="monotone" dataKey="pork" name="Pork" stroke={theme.palette.success.main} strokeWidth={2} activeDot={{ r: 8 }} />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              }
            />
          </motion.div>
        </motion.div>

        {/* Market Trends Table */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Market Analysis
          </Typography>

          <motion.div variants={itemVariants}>
            <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px', mb: 4 }}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                  <TableRow>
                    <TableCell><strong>Category</strong></TableCell>
                    <TableCell><strong>Current Price</strong></TableCell>
                    <TableCell><strong>Change</strong></TableCell>
                    <TableCell><strong>Forecast</strong></TableCell>
                    <TableCell><strong>Demand</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockMarketTrends.map((trend) => (
                    <TableRow key={trend.id} hover>
                      <TableCell>{trend.category}</TableCell>
                      <TableCell>{trend.currentPrice}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', color: trend.change.startsWith('+') ? 'success.main' : 'error.main' }}>
                          {trend.change.startsWith('+') ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                          {trend.change}
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trend.forecast}
                          color={
                            trend.forecast === 'Increasing' ? 'success' :
                            trend.forecast === 'Decreasing' ? 'error' : 'info'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={trend.demand}
                          color={
                            trend.demand === 'High' ? 'success' :
                            trend.demand === 'Low' ? 'error' : 'warning'
                          }
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>
        </motion.div>

        {/* Market Opportunities */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 4, mb: 2 }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 0 }}>
              Market Opportunities
            </Typography>
            <CustomButton
              variant="outlined"
              startIcon={<Add />}
              onClick={() => handleOpenDialog()}
            >
              Add Opportunity
            </CustomButton>
          </Box>

          <motion.div variants={itemVariants}>
            <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                  <TableRow>
                    <TableCell><strong>Title</strong></TableCell>
                    <TableCell><strong>Category</strong></TableCell>
                    <TableCell><strong>Potential Revenue</strong></TableCell>
                    <TableCell><strong>Requirements</strong></TableCell>
                    <TableCell><strong>Deadline</strong></TableCell>
                    <TableCell><strong>Status</strong></TableCell>
                    <TableCell><strong>Actions</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockMarketOpportunities.map((opportunity) => (
                    <TableRow key={opportunity.id} hover>
                      <TableCell>{opportunity.title}</TableCell>
                      <TableCell>{opportunity.category}</TableCell>
                      <TableCell>{opportunity.potentialRevenue}</TableCell>
                      <TableCell>{opportunity.requirements}</TableCell>
                      <TableCell>{format(parseISO(opportunity.deadline), 'dd MMM yyyy')}</TableCell>
                      <TableCell>
                        <Chip
                          label={opportunity.status === 'open' ? 'Open' : 'Closed'}
                          color={opportunity.status === 'open' ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton size="small" onClick={() => handleOpenDialog(opportunity)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small">
                          <Delete fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>
        </motion.div>
      </Box>

      {/* Add/Edit Opportunity Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedOpportunity ? 'Edit Market Opportunity' : 'Add New Market Opportunity'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Title"
                fullWidth
                margin="normal"
                defaultValue={selectedOpportunity?.title || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Category</InputLabel>
                <Select
                  label="Category"
                  defaultValue={selectedOpportunity?.category || ''}
                >
                  <MenuItem value="Beef">Beef</MenuItem>
                  <MenuItem value="Mutton">Mutton</MenuItem>
                  <MenuItem value="Pork">Pork</MenuItem>
                  <MenuItem value="Dairy">Dairy</MenuItem>
                  <MenuItem value="Wool">Wool</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Potential Revenue"
                fullWidth
                margin="normal"
                defaultValue={selectedOpportunity?.potentialRevenue || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Deadline"
                type="date"
                fullWidth
                margin="normal"
                defaultValue={selectedOpportunity?.deadline || format(new Date(), 'yyyy-MM-dd')}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Status</InputLabel>
                <Select
                  label="Status"
                  defaultValue={selectedOpportunity?.status || 'open'}
                >
                  <MenuItem value="open">Open</MenuItem>
                  <MenuItem value="closed">Closed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Requirements"
                fullWidth
                multiline
                rows={3}
                margin="normal"
                defaultValue={selectedOpportunity?.requirements || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Notes"
                fullWidth
                multiline
                rows={3}
                margin="normal"
                defaultValue={selectedOpportunity?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton onClick={handleSave} variant="contained" color="primary">
            {selectedOpportunity ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Market;
