import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, Grid, FormControl, InputLabel, Select, MenuItem, IconButton, Typography, Box, Divider, CircularProgress, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete  } from '../../utils/iconImports';
import { DatePicker } from '@mui/x-date-pickers';
import { FeedingRecord, FeedInventory } from '../../types/feeding';
import dayjs from 'dayjs';

interface FeedingRecordModalProps {
  open: boolean;
  onClose: () => void;
  record?: FeedingRecord | null;
  feedInventory: FeedInventory[];
  onSave: (record: Omit<FeedingRecord, 'id'>) => void;
  onUpdate?: (id: string, record: Partial<FeedingRecord>) => void;
  onDelete?: (id: string) => void;
  loading?: boolean;
}

const FeedingRecordModal: React.FC<FeedingRecordModalProps> = ({
  open,
  onClose,
  record,
  feedInventory,
  onSave,
  onUpdate,
  onDelete,
  loading = false,
}) => {
  const isEditMode = !!record;

  // Form state
  const [formData, setFormData] = useState<Partial<FeedingRecord>>({
    date: new Date(),
    feedId: '',
    feedType: '',
    quantity: 0,
    costPerUnit: 0,
    totalCost: 0,
    animalGroupId: '',
    notes: '',
    unit: 'kg',
    location: '',
  });
  
  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when record changes
  useEffect(() => {
    if (record) {
      setFormData({
        date: new Date(record.date),
        feedId: record.feedId,
        feedType: record.feedType,
        quantity: record.quantity,
        costPerUnit: record.costPerUnit,
        totalCost: record.totalCost,
        animalGroupId: record.animalGroupId,
        notes: record.notes,
        unit: record.unit || 'kg',
        location: record.location || '',
      });
    } else {
      // Reset form for new record
      setFormData({
        date: new Date(),
        feedId: '',
        feedType: '',
        quantity: 0,
        costPerUnit: 0,
        totalCost: 0,
        animalGroupId: '',
        notes: '',
        unit: 'kg',
        location: '',
      });
    }
    setErrors({});
  }, [record, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // Update total cost if quantity or costPerUnit changes
    if (name === 'quantity' || name === 'costPerUnit') {
      const quantity = name === 'quantity' ? parseFloat(value) : formData.quantity || 0;
      const costPerUnit = name === 'costPerUnit' ? parseFloat(value) : formData.costPerUnit || 0;
      setFormData(prev => ({ ...prev, totalCost: quantity * costPerUnit }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // If feed is selected, update feed type and cost per unit
    if (name === 'feedId' && value) {
      const selectedFeed = feedInventory.find(feed => feed.id === value);
      if (selectedFeed) {
        setFormData(prev => ({ 
          ...prev, 
          feedType: selectedFeed.type,
          costPerUnit: selectedFeed.costPerUnit,
          unit: selectedFeed.unit,
          totalCost: (prev.quantity || 0) * selectedFeed.costPerUnit,
        }));
      }
    }
  };

  // Handle date changes
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date: date.toDate() }));
      
      // Clear error when field is edited
      if (errors.date) {
        setErrors(prev => ({ ...prev, date: '' }));
      }
    }
  };

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.feedId) newErrors.feedId = 'Feed is required';
    if (!formData.animalGroupId) newErrors.animalGroupId = 'Animal group is required';
    
    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than zero';
    }
    
    if (!formData.costPerUnit || formData.costPerUnit < 0) {
      newErrors.costPerUnit = 'Cost per unit must be a positive number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;
    
    const recordData = {
      ...formData,
      totalCost: (formData.quantity || 0) * (formData.costPerUnit || 0),
    } as Omit<FeedingRecord, 'id'>;
    
    if (isEditMode && record && onUpdate) {
      onUpdate(record.id, recordData);
    } else {
      onSave(recordData);
    }
  };

  // Handle record deletion
  const handleDelete = () => {
    if (isEditMode && record && onDelete) {
      onDelete(record.id);
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{ 
        sx: { 
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        } 
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {isEditMode ? 'Edit Feeding Record' : 'New Feeding Record'}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Basic Information
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Feeding Date"
              value={formData.date ? dayjs(formData.date) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.date,
                  helperText: errors.date
                }
              }}
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.feedId}>
              <InputLabel>Feed</InputLabel>
              <Select
                name="feedId"
                value={formData.feedId || ''}
                onChange={handleSelectChange}
                label="Feed"
              >
                {feedInventory.map((feed) => (
                  <MenuItem key={feed.id} value={feed.id}>
                    {feed.name} ({feed.type})
                  </MenuItem>
                ))}
              </Select>
              {errors.feedId && (
                <Typography variant="caption" color="error">
                  {errors.feedId}
                </Typography>
              )}
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Animal Group ID"
              name="animalGroupId"
              value={formData.animalGroupId || ''}
              onChange={handleChange}
              error={!!errors.animalGroupId}
              helperText={errors.animalGroupId}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Location"
              name="location"
              value={formData.location || ''}
              onChange={handleChange}
            />
          </Grid>
          
          {/* Quantity and Cost */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
              Quantity and Cost
            </Typography>
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Quantity"
              name="quantity"
              type="number"
              inputProps={{ min: 0, step: 0.01 }}
              value={formData.quantity || ''}
              onChange={handleChange}
              error={!!errors.quantity}
              helperText={errors.quantity}
              required
            />
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Unit"
              name="unit"
              value={formData.unit || 'kg'}
              onChange={handleChange}
              disabled={!!formData.feedId} // Disable if feed is selected
            />
          </Grid>
          
          <Grid item xs={12} sm={4}>
            <TextField
              fullWidth
              label="Cost Per Unit"
              name="costPerUnit"
              type="number"
              inputProps={{ min: 0, step: 0.01 }}
              value={formData.costPerUnit || ''}
              onChange={handleChange}
              error={!!errors.costPerUnit}
              helperText={errors.costPerUnit}
              required
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Total Cost"
              type="number"
              value={formData.totalCost || 0}
              InputProps={{ readOnly: true }}
            />
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Notes"
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              multiline
              rows={3}
            />
          </Grid>
        </Grid>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3 }}>
        {isEditMode && onDelete && (
          <CustomButton 
            onClick={handleDelete} 
            color="error" 
            startIcon={<Delete />}
            disabled={loading}
          >
            Delete
          </CustomButton>
        )}
        <Box flexGrow={1} />
        <CustomButton onClick={onClose} disabled={loading}>
          Cancel
        </CustomButton>
        <CustomButton 
          onClick={handleSubmit} 
          variant="contained" 
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            isEditMode ? 'Update' : 'Save'
          )}
        </CustomButton>
      </DialogActions>
    </Dialog>
  );
};

export default FeedingRecordModal;
