import React, { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  useTheme,
  alpha,
  Container,
  Tabs,
  Tab,
  Divider,
  Paper
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  BusinessCenter,
  TrendingUp,
  Assessment,
  Lightbulb,
  ShowChart,
  CompareArrows,
  BarChart,
  Pie<PERSON>hart,
  Refresh
} from '@mui/icons-material';
import StrategyIcon from '@mui/icons-material/AutoGraph';
import { useLanguage } from '../../contexts/LanguageContext';
import { useThemeContext } from '../../contexts/ThemeContext';
import { useBusinessStrategy } from '../../hooks/useBusinessStrategy';
import { useBusinessPredictions } from '../../hooks/useBusinessPredictions';
import { useNavigate } from 'react-router-dom';

// Import custom components
import BusinessAnalyticsButton from '../../components/analytics/BusinessAnalyticsButton';
import BusinessAnalyticsCard from '../../components/analytics/BusinessAnalyticsCard';
import BusinessMetricsCard from '../../components/analytics/BusinessMetricsCard';
import BusinessPredictionCard from '../../components/analytics/BusinessPredictionCard';
import BusinessStrategyCard from '../../components/analytics/BusinessStrategyCard';
import BusinessInsightCard from '../../components/analytics/BusinessInsightCard';

const BusinessStrategy: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { themeColor } = useThemeContext();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);

  // Business strategy and predictions hooks
  const { strategyData, loading: strategyLoading } = useBusinessStrategy();
  const { predictions, insights, loading: predictionsLoading } = useBusinessPredictions();

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  // Handle refresh data
  const handleRefreshData = useCallback(() => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Render vision and mission section
  const renderVisionMission = () => {
    return (
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <BusinessAnalyticsCard
            title={translate('dashboard.vision')}
            icon={<BusinessCenter />}
            delay={0.1}
          >
            <Box p={2}>
              <Typography variant="body1">
                {strategyData.vision}
              </Typography>
            </Box>
          </BusinessAnalyticsCard>
        </Grid>
        <Grid item xs={12} md={6}>
          <BusinessAnalyticsCard
            title={translate('dashboard.mission')}
            icon={<Assessment />}
            delay={0.2}
          >
            <Box p={2}>
              <Typography variant="body1">
                {strategyData.mission}
              </Typography>
            </Box>
          </BusinessAnalyticsCard>
        </Grid>
      </Grid>
    );
  };

  // Render strategic goals section
  const renderStrategicGoals = () => {
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.strategic_goals')}
        </Typography>
        <Grid container spacing={3}>
          {strategyData.goals.map((goal, index) => (
            <Grid item xs={12} md={6} lg={4} key={goal.id}>
              <BusinessAnalyticsCard
                title={goal.name}
                subtitle={`Target: ${goal.targetDate}`}
                icon={<TrendingUp />}
                delay={0.1 + (index * 0.05)}
              >
                <Box p={2}>
                  <Typography variant="body1" paragraph>
                    {goal.description}
                  </Typography>

                  <Box sx={{ mt: 2, mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Progress
                      </Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {goal.progress}%
                      </Typography>
                    </Box>
                    <Box sx={{
                      width: '100%',
                      height: 8,
                      backgroundColor: alpha(theme.palette.primary.main, 0.2),
                      borderRadius: 4
                    }}>
                      <Box sx={{
                        width: `${goal.progress}%`,
                        height: '100%',
                        backgroundColor: goal.status === 'on-track'
                          ? theme.palette.success.main
                          : goal.status === 'at-risk'
                            ? theme.palette.warning.main
                            : theme.palette.error.main,
                        borderRadius: 4
                      }} />
                    </Box>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Status
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: goal.status === 'on-track'
                          ? alpha(theme.palette.success.main, 0.1)
                          : goal.status === 'at-risk'
                            ? alpha(theme.palette.warning.main, 0.1)
                            : alpha(theme.palette.error.main, 0.1),
                        color: goal.status === 'on-track'
                          ? theme.palette.success.main
                          : goal.status === 'at-risk'
                            ? theme.palette.warning.main
                            : theme.palette.error.main
                      }}>
                        {goal.status}
                      </Box>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Owner
                      </Typography>
                      <Typography variant="body2">
                        {goal.owner}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </BusinessAnalyticsCard>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // Render strategic initiatives section
  const renderStrategicInitiatives = () => {
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.strategic_initiatives')}
        </Typography>
        <Grid container spacing={3}>
          {strategyData.initiatives.map((initiative, index) => (
            <Grid item xs={12} md={6} key={initiative.id}>
              <BusinessAnalyticsCard
                title={initiative.name}
                subtitle={`${initiative.startDate} - ${initiative.endDate}`}
                icon={<ShowChart />}
                delay={0.1 + (index * 0.05)}
              >
                <Box p={2}>
                  <Typography variant="body1" paragraph>
                    {initiative.description}
                  </Typography>

                  <Box sx={{ mt: 2, mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Progress
                      </Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {initiative.progress}%
                      </Typography>
                    </Box>
                    <Box sx={{
                      width: '100%',
                      height: 8,
                      backgroundColor: alpha(theme.palette.primary.main, 0.2),
                      borderRadius: 4
                    }}>
                      <Box sx={{
                        width: `${initiative.progress}%`,
                        height: '100%',
                        backgroundColor: theme.palette.primary.main,
                        borderRadius: 4
                      }} />
                    </Box>
                  </Box>

                  <Grid container spacing={2} sx={{ mt: 2 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Budget
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        R {initiative.budget.toLocaleString()}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Actual Spend
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        R {initiative.actualSpend.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Status
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: alpha(theme.palette.info.main, 0.1),
                        color: theme.palette.info.main
                      }}>
                        {initiative.status}
                      </Box>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Owner
                      </Typography>
                      <Typography variant="body2">
                        {initiative.owner}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </BusinessAnalyticsCard>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // Render strategic opportunities section
  const renderStrategicOpportunities = () => {
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.strategic_opportunities')}
        </Typography>
        <Grid container spacing={3}>
          {strategyData.opportunities.map((opportunity, index) => (
            <Grid item xs={12} md={6} key={opportunity.id}>
              <BusinessStrategyCard
                strategy={{
                  id: opportunity.id,
                  name: opportunity.name,
                  description: opportunity.description,
                  potentialValue: opportunity.potentialValue,
                  implementationTimeframe: '6-12 months',
                  implementationStrategy: opportunity.implementationPlan,
                  feasibility: opportunity.feasibility,
                  opportunityScore: opportunity.opportunityScore,
                  tags: ['Strategy', 'Growth', 'Innovation'],
                  category: opportunity.status
                }}
                icon={<Lightbulb />}
                delay={0.1 + (index * 0.05)}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // Render SWOT analysis section
  const renderSWOTAnalysis = () => {
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.swot_analysis')}
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <BusinessAnalyticsCard
              title={translate('dashboard.strengths')}
              icon={<TrendingUp />}
              accentColor={theme.palette.success.main}
              delay={0.1}
            >
              <Box p={2}>
                {strategyData.swot.strengths.map((strength, index) => (
                  <Box
                    key={strength.id}
                    sx={{
                      mb: 2,
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.success.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.success.main, 0.1)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">
                        {strength.description}
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: alpha(theme.palette.success.main, 0.1),
                        color: theme.palette.success.main
                      }}>
                        {strength.impact}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </BusinessAnalyticsCard>
          </Grid>
          <Grid item xs={12} md={6}>
            <BusinessAnalyticsCard
              title={translate('dashboard.weaknesses')}
              icon={<TrendingUp />}
              accentColor={theme.palette.error.main}
              delay={0.2}
            >
              <Box p={2}>
                {strategyData.swot.weaknesses.map((weakness, index) => (
                  <Box
                    key={weakness.id}
                    sx={{
                      mb: 2,
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.error.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.error.main, 0.1)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">
                        {weakness.description}
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: alpha(theme.palette.error.main, 0.1),
                        color: theme.palette.error.main
                      }}>
                        {weakness.impact}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </BusinessAnalyticsCard>
          </Grid>
          <Grid item xs={12} md={6}>
            <BusinessAnalyticsCard
              title={translate('dashboard.opportunities')}
              icon={<TrendingUp />}
              accentColor={theme.palette.info.main}
              delay={0.3}
            >
              <Box p={2}>
                {strategyData.swot.opportunities.map((opportunity, index) => (
                  <Box
                    key={opportunity.id}
                    sx={{
                      mb: 2,
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.info.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">
                        {opportunity.description}
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: alpha(theme.palette.info.main, 0.1),
                        color: theme.palette.info.main
                      }}>
                        {opportunity.impact}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </BusinessAnalyticsCard>
          </Grid>
          <Grid item xs={12} md={6}>
            <BusinessAnalyticsCard
              title={translate('dashboard.threats')}
              icon={<TrendingUp />}
              accentColor={theme.palette.warning.main}
              delay={0.4}
            >
              <Box p={2}>
                {strategyData.swot.threats.map((threat, index) => (
                  <Box
                    key={threat.id}
                    sx={{
                      mb: 2,
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: alpha(theme.palette.warning.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">
                        {threat.description}
                      </Typography>
                      <Box sx={{
                        display: 'inline-block',
                        px: 1.5,
                        py: 0.5,
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        fontWeight: 'bold',
                        textTransform: 'capitalize',
                        backgroundColor: alpha(theme.palette.warning.main, 0.1),
                        color: theme.palette.warning.main
                      }}>
                        {threat.impact}
                      </Box>
                    </Box>
                  </Box>
                ))}
              </Box>
            </BusinessAnalyticsCard>
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <Box>
            {renderVisionMission()}
            {renderStrategicGoals()}
            {renderStrategicOpportunities()}
          </Box>
        );
      case 'goals':
        return renderStrategicGoals();
      case 'initiatives':
        return renderStrategicInitiatives();
      case 'opportunities':
        return renderStrategicOpportunities();
      case 'swot':
        return renderSWOTAnalysis();
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <Box sx={{ mb: 4 }}>
          <motion.div variants={itemVariants}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {translate('dashboard.business_strategy')}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {translate('dashboard.business_strategy_description')}
            </Typography>
          </motion.div>
        </Box>

        <Box sx={{ mb: 4 }}>
          <motion.div variants={itemVariants}>
            <Paper
              elevation={0}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                background: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(10px)'
              }}
            >
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  px: 2,
                  pt: 2,
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  '& .MuiTab-root': {
                    minWidth: 'auto',
                    px: 3,
                    py: 1.5,
                    borderRadius: 2,
                    fontWeight: 'medium',
                    textTransform: 'none',
                    fontSize: '0.9rem',
                    transition: 'all 0.2s',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.05)
                    }
                  },
                  '& .Mui-selected': {
                    color: `${theme.palette.primary.main} !important`,
                    fontWeight: 'bold',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none'
                  }
                }}
              >
                <Tab value="overview" label={translate('dashboard.overview')} icon={<Assessment />} iconPosition="start" />
                <Tab value="goals" label={translate('dashboard.goals')} icon={<TrendingUp />} iconPosition="start" />
                <Tab value="initiatives" label={translate('dashboard.initiatives')} icon={<ShowChart />} iconPosition="start" />
                <Tab value="opportunities" label={translate('dashboard.opportunities')} icon={<Lightbulb />} iconPosition="start" />
                <Tab value="swot" label={translate('dashboard.swot')} icon={<CompareArrows />} iconPosition="start" />
              </Tabs>
              <Box sx={{ p: 3 }}>
                {isLoading || strategyLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        border: `3px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                        borderTop: `3px solid ${theme.palette.primary.main}`,
                        animation: 'spin 1s linear infinite',
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' }
                        }
                      }}
                    />
                  </Box>
                ) : (
                  renderContent()
                )}
              </Box>
            </Paper>
          </motion.div>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <motion.div variants={itemVariants}>
            <BusinessAnalyticsButton
              variant="contained"
              color="primary"
              onClick={handleRefreshData}
              startIcon={<Refresh />}
              disabled={isLoading}
            >
              {isLoading ? translate('common.refreshing') : translate('common.refresh')}
            </BusinessAnalyticsButton>
          </motion.div>
        </Box>
      </motion.div>
    </Container>
  );
};

export default BusinessStrategy;
