const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGODB_URI || 'your-mongodb-uri';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

const animals = [
  {
    tagNumber: 'CTL-2025-001',
    name: '<PERSON><PERSON><PERSON>',
    type: 'Cattle',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2023-01-15',
    weight: 500,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Farm A',
    purchaseDate: '2023-02-01',
    purchasePrice: 15000,
    notes: 'Healthy breeding cow.',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // ... Add 9 more animal objects with realistic data
];

async function injectDemoAnimals() {
  const client = new MongoClient(uri, { useNewUrlParser: true, useUnifiedTopology: true });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    const animalsCollection = db.collection('animals');

    console.log('Inserting demo animals...');
    const result = await animalsCollection.insertMany(animals);
    console.log(`${result.insertedCount} animals inserted successfully.`);

    // Add related records for health, breeding, and feeding
    console.log('Adding related records...');
    for (const animal of animals) {
      const healthRecords = [
        {
          animalId: animal.tagNumber,
          type: 'Vaccination',
          date: new Date(),
          notes: 'Routine vaccination',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const breedingRecords = [
        {
          animalId: animal.tagNumber,
          type: 'Breeding',
          date: new Date(),
          notes: 'Artificial insemination',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const feedingRecords = [
        {
          animalId: animal.tagNumber,
          type: 'Feeding',
          date: new Date(),
          notes: 'Daily feed',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      await db.collection('health_records').insertMany(healthRecords);
      await db.collection('breeding_records').insertMany(breedingRecords);
      await db.collection('feeding_records').insertMany(feedingRecords);
    }

    console.log('Related records added successfully.');
  } catch (error) {
    console.error('Error injecting demo animals:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed.');
  }
}

injectDemoAnimals();