import React from 'react';
import { Chip } from '@mui/material';
import { MetricStatus } from '../../constants/routes';

interface StatusBadgeProps {
  status: string;
  type?: 'health' | 'general' | 'alert';
  size?: 'small' | 'medium';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  type = 'general',
  size = 'small' 
}) => {
  const getStatusConfig = () => {
    const statusLower = status.toLowerCase();
    
    // Health statuses
    if (type === 'health') {
      if (statusLower === 'healthy') return { color: 'success', label: status };
      if (statusLower === 'sick') return { color: 'error', label: status };
      if (statusLower === 'injured') return { color: 'warning', label: status };
      if (statusLower === 'pregnant') return { color: 'info', label: status };
    }
    
    // General statuses
    if (statusLower === 'active') return { color: 'success', label: status };
    if (statusLower === 'inactive') return { color: 'default', label: status };
    if (statusLower === 'pending') return { color: 'warning', label: status };
    if (statusLower === 'critical') return { color: 'error', label: status };
    
    return { color: 'default', label: status };
  };

  const config = getStatusConfig();

  return (
    <Chip
      label={config.label}
      color={config.color as any}
      size={size}
      className="font-medium"
    />
  );
};

export default StatusBadge;
