import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Tooltip,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import {
  Lock,
  Star,
  TrendingUp,
  Close,
  PlayArrow,
  Upgrade,
  Timer,
  CheckCircle,
  Warning
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface BetaFeatureGateProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showPreview?: boolean;
  previewDuration?: number;
  upgradeMessage?: string;
  onUpgradeClick?: () => void;
  onPreviewComplete?: () => void;
}

interface SubscriptionLimits {
  maxAnimals: number;
  currentAnimals: number;
  maxReports: number;
  currentReports: number;
  maxAlerts: number;
  currentAlerts: number;
  planName: string;
  isTrialExpiring: boolean;
  daysLeft: number;
}

const BetaFeatureGate: React.FC<BetaFeatureGateProps> = ({
  feature,
  children,
  fallback,
  showPreview = true,
  previewDuration = 30,
  upgradeMessage,
  onUpgradeClick,
  onPreviewComplete
}) => {
  const theme = useTheme();
  const [hasAccess, setHasAccess] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [previewTimeLeft, setPreviewTimeLeft] = useState(previewDuration);
  const [isPreviewActive, setIsPreviewActive] = useState(false);
  const [limits, setLimits] = useState<SubscriptionLimits | null>(null);
  const [accessReason, setAccessReason] = useState('');

  useEffect(() => {
    checkFeatureAccess();
  }, [feature]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isPreviewActive && previewTimeLeft > 0) {
      interval = setInterval(() => {
        setPreviewTimeLeft(prev => {
          if (prev <= 1) {
            setIsPreviewActive(false);
            setShowPreviewDialog(false);
            onPreviewComplete?.();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isPreviewActive, previewTimeLeft, onPreviewComplete]);

  const checkFeatureAccess = async () => {
    try {
      setIsLoading(true);
      
      // Mock API call - replace with actual API
      const response = await fetch('/api/subscription/check-access', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature })
      });
      
      const data = await response.json();
      setHasAccess(data.allowed);
      setAccessReason(data.reason || '');
      setLimits(data.limits);
      
      if (!data.allowed && data.upgradeRequired) {
        // Track limit hit for conversion metrics
        trackFeatureUsage(feature, 'limit_reached');
      }
    } catch (error) {
      console.error('Error checking feature access:', error);
      setHasAccess(false);
    } finally {
      setIsLoading(false);
    }
  };

  const trackFeatureUsage = async (featureName: string, action: string, metadata?: any) => {
    try {
      await fetch('/api/subscription/track-usage', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ feature: featureName, action, metadata })
      });
    } catch (error) {
      console.error('Error tracking feature usage:', error);
    }
  };

  const handlePreviewClick = () => {
    setShowPreviewDialog(true);
    trackFeatureUsage(feature, 'preview_requested');
  };

  const startPreview = () => {
    setIsPreviewActive(true);
    setPreviewTimeLeft(previewDuration);
    setShowPreviewDialog(false);
    trackFeatureUsage(feature, 'preview_started');
  };

  const handleUpgradeClick = () => {
    setShowUpgradeDialog(true);
    trackFeatureUsage(feature, 'upgrade_clicked');
    onUpgradeClick?.();
  };

  if (isLoading) {
    return (
      <Box sx={{ p: 2 }}>
        <LinearProgress />
        <Typography variant="body2" sx={{ mt: 1 }}>
          Checking access permissions...
        </Typography>
      </Box>
    );
  }

  if (hasAccess || isPreviewActive) {
    return (
      <>
        {children}
        {isPreviewActive && (
          <Box
            sx={{
              position: 'fixed',
              top: 16,
              right: 16,
              zIndex: 9999,
              backgroundColor: alpha(theme.palette.warning.main, 0.9),
              color: 'white',
              p: 2,
              borderRadius: 2,
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <Timer />
            <Typography variant="body2" fontWeight="bold">
              Preview: {Math.floor(previewTimeLeft / 60)}:{(previewTimeLeft % 60).toString().padStart(2, '0')}
            </Typography>
            <Button
              size="small"
              variant="contained"
              color="secondary"
              onClick={handleUpgradeClick}
            >
              Upgrade Now
            </Button>
          </Box>
        )}
      </>
    );
  }

  return (
    <>
      {fallback || (
        <Card
          sx={{
            border: `2px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
            backgroundColor: alpha(theme.palette.primary.main, 0.05),
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <CardContent sx={{ textAlign: 'center', py: 4 }}>
            <Box
              sx={{
                position: 'absolute',
                top: -20,
                right: -20,
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Lock sx={{ fontSize: 32, color: theme.palette.primary.main }} />
            </Box>

            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
              Premium Feature
            </Typography>

            <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
              {upgradeMessage || accessReason || 'This feature requires a premium subscription'}
            </Typography>

            {limits && (
              <Alert severity="warning" sx={{ mb: 3, textAlign: 'left' }}>
                <Typography variant="body2" fontWeight="bold">
                  Current Plan: {limits.planName}
                </Typography>
                {limits.maxAnimals > 0 && (
                  <Typography variant="body2">
                    Animals: {limits.currentAnimals}/{limits.maxAnimals}
                  </Typography>
                )}
                {limits.isTrialExpiring && (
                  <Typography variant="body2" color="error">
                    Trial expires in {limits.daysLeft} days
                  </Typography>
                )}
              </Alert>
            )}

            <Grid container spacing={2} justifyContent="center">
              {showPreview && (
                <Grid item>
                  <Button
                    variant="outlined"
                    startIcon={<PlayArrow />}
                    onClick={handlePreviewClick}
                    sx={{ minWidth: 140 }}
                  >
                    Preview (30s)
                  </Button>
                </Grid>
              )}
              <Grid item>
                <Button
                  variant="contained"
                  startIcon={<Upgrade />}
                  onClick={handleUpgradeClick}
                  sx={{ minWidth: 140 }}
                >
                  Upgrade Now
                </Button>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3 }}>
              <Chip
                icon={<Star />}
                label="Save R15,000 annually with Premium"
                color="primary"
                variant="outlined"
                size="small"
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Preview Dialog */}
      <Dialog
        open={showPreviewDialog}
        onClose={() => setShowPreviewDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PlayArrow color="primary" />
            Feature Preview
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Get a 30-second preview of this premium feature to see how it can benefit your farm operations.
          </Typography>
          <Alert severity="info">
            <Typography variant="body2">
              This preview will show you the full functionality for 30 seconds. 
              Upgrade to get unlimited access to this and all premium features.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPreviewDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={startPreview}>
            Start Preview
          </Button>
        </DialogActions>
      </Dialog>

      {/* Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Upgrade color="primary" />
              Upgrade Your Plan
            </Box>
            <IconButton onClick={() => setShowUpgradeDialog(false)}>
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', border: `2px solid ${theme.palette.primary.main}` }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    Professional
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                    R299<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Perfect for growing farms
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    {[
                      'Up to 200 animals',
                      'Unlimited history',
                      'Advanced analytics',
                      'Financial intelligence',
                      '50+ report types',
                      'Excel & PDF exports'
                    ].map((feature, index) => (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    ))}
                  </Box>
                  
                  <Button variant="contained" fullWidth>
                    Choose Professional
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card sx={{ height: '100%', border: `2px solid ${theme.palette.secondary.main}` }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Typography variant="h6" fontWeight="bold" color="secondary">
                      Enterprise
                    </Typography>
                    <Chip label="Most Popular" size="small" color="secondary" />
                  </Box>
                  <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                    R599<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    For serious livestock operations
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    {[
                      'Unlimited animals',
                      'AI Agent Mode',
                      'Priority support',
                      'API access',
                      'Custom reports',
                      'All export formats'
                    ].map((feature, index) => (
                      <Box key={index} sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                        <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                        <Typography variant="body2">{feature}</Typography>
                      </Box>
                    ))}
                  </Box>
                  
                  <Button variant="contained" color="secondary" fullWidth>
                    Choose Enterprise
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          <Alert severity="success" sx={{ mt: 3 }}>
            <Typography variant="body2" fontWeight="bold">
              Special Offer: First month free for beta users who upgrade within 30 days!
            </Typography>
          </Alert>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BetaFeatureGate;
