/**
 * Inject Breeding Data Script
 * 
 * This script injects realistic breeding records into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');
const { animals } = require('./inject-animal-data');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Breeding records to inject
const breedingRecords = [
  {
    _id: new ObjectId(),
    maleAnimal: animals[0]._id, // Thabo (bull)
    femaleAnimal: animals[1]._id, // Lesedi (cow)
    breedingDate: new Date('2023-06-15'),
    method: 'natural',
    location: 'South Paddock',
    status: 'successful',
    confirmedPregnant: true,
    confirmationDate: new Date('2023-07-20'),
    confirmationMethod: 'veterinary examination',
    expectedDueDate: new Date('2024-03-25'),
    notes: 'Successful breeding, pregnancy confirmed',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    maleAnimal: animals[2]._id, // Kgosi (ram)
    femaleAnimal: animals[3]._id, // Naledi (ewe)
    breedingDate: new Date('2023-09-10'),
    method: 'natural',
    location: 'East Paddock',
    status: 'successful',
    confirmedPregnant: true,
    confirmationDate: new Date('2023-10-15'),
    confirmationMethod: 'veterinary examination',
    expectedDueDate: new Date('2024-02-07'),
    notes: 'Successful breeding, pregnancy confirmed',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Birth records to inject
const birthRecords = [
  {
    _id: new ObjectId(),
    mother: animals[1]._id, // Lesedi (cow)
    father: animals[0]._id, // Thabo (bull)
    birthDate: new Date('2024-03-27'),
    location: 'South Paddock',
    birthType: 'natural',
    assistance: 'none',
    complications: 'none',
    breedingRecord: breedingRecords[0]._id,
    offspring: [
      {
        tagNumber: 'CTL-2024-003',
        gender: 'female',
        weight: 35,
        status: 'alive',
        notes: 'Healthy female calf'
      }
    ],
    totalOffspring: 1,
    aliveOffspring: 1,
    notes: 'Normal birth, healthy calf',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    mother: animals[3]._id, // Naledi (ewe)
    father: animals[2]._id, // Kgosi (ram)
    birthDate: new Date('2024-02-08'),
    location: 'East Paddock',
    birthType: 'natural',
    assistance: 'none',
    complications: 'none',
    breedingRecord: breedingRecords[1]._id,
    offspring: [
      {
        tagNumber: 'SHP-2024-003',
        gender: 'male',
        weight: 3.5,
        status: 'alive',
        notes: 'Healthy male lamb'
      },
      {
        tagNumber: 'SHP-2024-004',
        gender: 'female',
        weight: 3.2,
        status: 'alive',
        notes: 'Healthy female lamb'
      }
    ],
    totalOffspring: 2,
    aliveOffspring: 2,
    notes: 'Normal birth, twins, both healthy',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Heat detection records to inject
const heatDetectionRecords = [
  {
    _id: new ObjectId(),
    animal: animals[1]._id, // Lesedi (cow)
    detectionDate: new Date('2023-06-12'),
    signs: ['Mounting behavior', 'Restlessness', 'Mucus discharge'],
    intensity: 'strong',
    duration: 12, // hours
    action: 'scheduled for breeding',
    notes: 'Clear signs of heat, scheduled for breeding with Thabo',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[3]._id, // Naledi (ewe)
    detectionDate: new Date('2023-09-08'),
    signs: ['Restlessness', 'Tail wagging', 'Seeking ram'],
    intensity: 'moderate',
    duration: 24, // hours
    action: 'scheduled for breeding',
    notes: 'Clear signs of heat, scheduled for breeding with Kgosi',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[1]._id, // Lesedi (cow)
    detectionDate: new Date('2024-05-10'),
    signs: ['Mounting behavior', 'Restlessness', 'Mucus discharge'],
    intensity: 'strong',
    duration: 14, // hours
    action: 'monitoring',
    notes: 'Post-partum heat, monitoring for now',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject breeding data
async function injectBreedingData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const breedingRecordsCollection = db.collection('breedingrecords');
    const birthRecordsCollection = db.collection('birthrecords');
    const heatDetectionCollection = db.collection('heatdetections');
    const usersCollection = db.collection('users');
    const animalsCollection = db.collection('animals');

    // Get a user ID for the performer field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const performerId = adminUser ? adminUser._id : new ObjectId();

    // Insert or update breeding records
    for (const record of breedingRecords) {
      // Set the performer to an actual user
      record.performer = performerId;
      
      const existingRecord = await breedingRecordsCollection.findOne({ 
        maleAnimal: record.maleAnimal,
        femaleAnimal: record.femaleAnimal,
        breedingDate: record.breedingDate
      });

      if (existingRecord) {
        logger.info(`Updating breeding record: ${record._id}`);
        await breedingRecordsCollection.updateOne(
          { _id: existingRecord._id },
          { $set: { ...record, _id: existingRecord._id } }
        );
      } else {
        logger.info(`Inserting new breeding record: ${record._id}`);
        await breedingRecordsCollection.insertOne(record);
      }
    }

    // Insert or update birth records
    for (const record of birthRecords) {
      // Set the performer to an actual user
      record.performer = performerId;
      
      const existingRecord = await birthRecordsCollection.findOne({ 
        mother: record.mother,
        birthDate: record.birthDate
      });

      if (existingRecord) {
        logger.info(`Updating birth record: ${record._id}`);
        await birthRecordsCollection.updateOne(
          { _id: existingRecord._id },
          { $set: { ...record, _id: existingRecord._id } }
        );
      } else {
        logger.info(`Inserting new birth record: ${record._id}`);
        await birthRecordsCollection.insertOne(record);
        
        // Create new animal records for the offspring
        for (const offspring of record.offspring) {
          const newAnimal = {
            _id: new ObjectId(),
            tagNumber: offspring.tagNumber,
            name: offspring.tagNumber.includes('CTL') ? 'Calf-' + offspring.tagNumber.split('-')[2] : 
                  offspring.tagNumber.includes('SHP') ? 'Lamb-' + offspring.tagNumber.split('-')[2] : 
                  'Kid-' + offspring.tagNumber.split('-')[2],
            species: offspring.tagNumber.includes('CTL') ? 'Cattle' : 
                     offspring.tagNumber.includes('SHP') ? 'Sheep' : 'Goat',
            breed: offspring.tagNumber.includes('CTL') ? 'Nguni/Bonsmara' : 
                   offspring.tagNumber.includes('SHP') ? 'Dorper/Merino' : 'Boer Goat',
            gender: offspring.gender,
            birthDate: record.birthDate,
            acquisitionDate: record.birthDate,
            weight: offspring.weight,
            status: 'active',
            healthStatus: 'healthy',
            location: record.location,
            notes: offspring.notes,
            parentMale: record.father,
            parentFemale: record.mother,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          const existingAnimal = await animalsCollection.findOne({ tagNumber: offspring.tagNumber });
          
          if (!existingAnimal) {
            logger.info(`Inserting new animal from birth: ${offspring.tagNumber}`);
            await animalsCollection.insertOne(newAnimal);
          }
        }
      }
    }

    // Insert or update heat detection records
    for (const record of heatDetectionRecords) {
      // Set the performer to an actual user
      record.performer = performerId;
      
      const existingRecord = await heatDetectionCollection.findOne({ 
        animal: record.animal,
        detectionDate: record.detectionDate
      });

      if (existingRecord) {
        logger.info(`Updating heat detection record: ${record._id}`);
        await heatDetectionCollection.updateOne(
          { _id: existingRecord._id },
          { $set: { ...record, _id: existingRecord._id } }
        );
      } else {
        logger.info(`Inserting new heat detection record: ${record._id}`);
        await heatDetectionCollection.insertOne(record);
      }
    }

    logger.info('Breeding data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting breeding data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectBreedingData()
  .then(() => {
    logger.info('Breeding data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in breeding data injection script:', error);
  });

module.exports = { breedingRecords, birthRecords, heatDetectionRecords };
