import React, { useState, useEffect, forwardRef } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ert as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertProps,
  Typography,
  Box,
  useTheme,
  alpha
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircleOutline,
  ErrorOutline,
  Info as InfoIcon,
  WarningAmberOutlined
} from '@mui/icons-material';

// Custom Alert component with animation
const Alert = forwardRef<HTMLDivElement, AlertProps>((props, ref) => {
  const theme = useTheme();
  const { severity } = props;

  // Get the appropriate icon based on severity
  const getIcon = () => {
    switch (severity) {
      case 'success':
        return <CheckCircleOutline />;
      case 'error':
        return <ErrorOutline />;
      case 'warning':
        return <WarningAmberOutlined />;
      case 'info':
      default:
        return <InfoIcon />;
    }
  };

  // Get the appropriate background color based on severity
  const getBackgroundColor = () => {
    switch (severity) {
      case 'success':
        return alpha(theme.palette.success.main, 0.1);
      case 'error':
        return alpha(theme.palette.error.main, 0.1);
      case 'warning':
        return alpha(theme.palette.warning.main, 0.1);
      case 'info':
      default:
        return alpha(theme.palette.info.main, 0.1);
    }
  };

  // Get the appropriate border color based on severity
  const getBorderColor = () => {
    switch (severity) {
      case 'success':
        return theme.palette.success.main;
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
      default:
        return theme.palette.info.main;
    }
  };

  return (
    <MuiAlert
      elevation={0}
      ref={ref}
      variant="filled"
      {...props}
      icon={getIcon()}
      sx={{
        borderRadius: 2,
        backgroundColor: getBackgroundColor(),
        color: getBorderColor(),
        border: `1px solid ${alpha(getBorderColor(), 0.2)}`,
        '& .MuiAlert-icon': {
          color: getBorderColor(),
        },
        ...props.sx
      }}
    />
  );
});

export interface AlertSystemProps {
  open: boolean;
  message: string;
  severity?: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  autoHideDuration?: number;
  onClose: () => void;
  position?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'center' | 'right';
  };
}

const AlertSystem: React.FC<AlertSystemProps> = ({
  open,
  message,
  severity = 'info',
  title,
  autoHideDuration = 6000,
  onClose,
  position = { vertical: 'bottom', horizontal: 'right' }
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (open) {
      setIsVisible(true);
    }
  }, [open]);

  const handleClose = (event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }

    setIsVisible(false);

    // Delay the actual close to allow the exit animation to complete
    setTimeout(() => {
      onClose();
    }, 300);
  };

  return (
    <Snackbar
      open={isVisible}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      anchorOrigin={position}
      sx={{ maxWidth: '90%', width: 400 }}
    >
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity={severity}
              onClose={handleClose}
              sx={{ width: '100%', boxShadow: '0 4px 12px rgba(0,0,0,0.1)' }}
            >
              <Box>
                {title && (
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 0.5 }}>
                    {title}
                  </Typography>
                )}
                <Typography variant="body2">{message}</Typography>
              </Box>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>
    </Snackbar>
  );
};

export default AlertSystem;
