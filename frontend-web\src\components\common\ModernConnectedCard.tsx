import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { Lock, TrendingUp } from '@mui/icons-material';
import { alpha } from '@mui/material/styles';
import { motion } from 'framer-motion';
import OptimizedImage from './OptimizedImage';

interface ModernConnectedCardProps {
  id: string;
  name: string;
  description: string;
  icon: React.ReactElement;
  backgroundImage?: string;
  color: string;
  isLocked?: boolean;
  isPremium?: boolean;
  comingSoon?: boolean;
  onClick?: () => void;
  index?: number;
  size?: 'small' | 'medium' | 'large';
}

const ModernConnectedCard: React.FC<ModernConnectedCardProps> = ({
  id,
  name,
  description,
  icon,
  backgroundImage,
  color,
  isLocked = false,
  isPremium = false,
  comingSoon = false,
  onClick,
  index = 0,
  size = 'medium'
}) => {
  const getSizeConfig = () => {
    const configs = {
      small: { width: 260, height: 260, iconSize: 52 },
      medium: { width: 320, height: 320, iconSize: 60 },
      large: { width: 380, height: 380, iconSize: 68 }
    };
    return configs[size];
  };

  const sizeConfig = getSizeConfig();
  const isEven = index % 2 === 0;
  const connectionAngle = isEven ? 15 : -15;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        scale: isLocked ? 1 : 1.05,
        y: isLocked ? 0 : -15,
        rotateZ: isLocked ? 0 : connectionAngle * 0.3
      }}
      whileTap={{ scale: isLocked ? 1 : 0.95 }}
      style={{ 
        position: 'relative',
        cursor: isLocked ? 'not-allowed' : 'pointer'
      }}
    >
      {/* Connection Ring */}
      <motion.div
        initial={{ scale: 0, rotate: 0 }}
        animate={{ scale: 1, rotate: 360 }}
        transition={{ 
          duration: 2,
          delay: index * 0.2,
          ease: "easeOut"
        }}
        style={{
          position: 'absolute',
          top: '-20px',
          left: '-20px',
          right: '-20px',
          bottom: '-20px',
          borderRadius: '50%',
          border: `2px solid ${alpha(color, 0.2)}`,
          zIndex: 0
        }}
      />

      {/* Floating Connection Dots */}
      <motion.div
        animate={{ 
          rotate: [0, 360],
          scale: [1, 1.2, 1]
        }}
        transition={{ 
          duration: 8,
          repeat: Infinity,
          ease: "linear"
        }}
        style={{
          position: 'absolute',
          top: '10%',
          right: '-15px',
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          background: `linear-gradient(45deg, ${color}, ${alpha(color, 0.6)})`,
          zIndex: 3,
          boxShadow: `0 0 20px ${alpha(color, 0.6)}`
        }}
      />

      <motion.div
        animate={{ 
          rotate: [360, 0],
          scale: [1, 1.1, 1]
        }}
        transition={{ 
          duration: 6,
          repeat: Infinity,
          ease: "linear",
          delay: 1
        }}
        style={{
          position: 'absolute',
          bottom: '15%',
          left: '-10px',
          width: '8px',
          height: '8px',
          borderRadius: '50%',
          background: `linear-gradient(45deg, ${alpha(color, 0.8)}, transparent)`,
          zIndex: 3
        }}
      />

      {/* Main Card */}
      <Box
        onClick={onClick}
        sx={{
          width: sizeConfig.width,
          height: sizeConfig.height,
          borderRadius: '50%',
          position: 'relative',
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha('#2E7D32', 0.2)}, ${alpha('#4CAF50', 0.1)})`,
          backdropFilter: 'blur(30px)',
          WebkitBackdropFilter: 'blur(30px)',
          border: `3px solid ${alpha(color, 0.4)}`,
          boxShadow: `
            0 25px 70px ${alpha(color, 0.25)},
            0 15px 35px ${alpha('#1B5E20', 0.15)},
            inset 0 2px 0 ${alpha('#81C784', 0.3)},
            0 0 0 1px ${alpha('#4CAF50', 0.1)}
          `,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          opacity: isLocked ? 0.8 : 1,
          '&:hover': {
            boxShadow: isLocked 
              ? `0 25px 50px ${alpha('#000', 0.15)}`
              : `
                0 30px 80px ${alpha(color, 0.3)},
                0 15px 40px ${alpha('#000', 0.1)},
                inset 0 1px 0 ${alpha('#ffffff', 0.3)}
              `,
          }
        }}
      >
        {/* Background Image */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '50%',
              overflow: 'hidden',
              zIndex: 0
            }}
          >
            <OptimizedImage
              src={backgroundImage}
              alt={`${name} background`}
              width="100%"
              height="100%"
              opacity={0.3}
              filter="brightness(0.6) contrast(1.2) blur(1px)"
              overlay={alpha(color, 0.3)}
              borderRadius="50%"
            />
          </Box>
        )}

        {/* Lock Overlay */}
        {isLocked && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              borderRadius: '50%',
              background: `radial-gradient(circle, ${alpha('#000', 0.7)}, ${alpha('#000', 0.9)})`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 4
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              style={{ textAlign: 'center', color: 'white' }}
            >
              <Lock sx={{ fontSize: sizeConfig.iconSize, mb: 1, color }} />
              <Typography variant="h6" fontWeight="bold" color="white">
                {isPremium ? 'Premium' : 'Locked'}
              </Typography>
              <Typography variant="body2" sx={{ color: alpha('#fff', 0.8) }}>
                Upgrade Required
              </Typography>
            </motion.div>
          </Box>
        )}

        {/* Badges */}
        {isPremium && (
          <Chip
            label="PRO"
            size="small"
            sx={{
              position: 'absolute',
              top: 20,
              right: 20,
              background: `linear-gradient(45deg, ${color}, ${alpha(color, 0.8)})`,
              color: 'white',
              fontWeight: 'bold',
              zIndex: 5,
              boxShadow: `0 4px 12px ${alpha(color, 0.4)}`
            }}
          />
        )}

        {comingSoon && (
          <Chip
            label="Soon"
            size="small"
            sx={{
              position: 'absolute',
              top: 20,
              left: 20,
              background: `linear-gradient(45deg, #F59E0B, #FBBF24)`,
              color: 'white',
              fontWeight: 'bold',
              zIndex: 5,
              boxShadow: `0 4px 12px ${alpha('#F59E0B', 0.4)}`
            }}
          />
        )}

        {/* Content */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 3,
            textAlign: 'center',
            p: 3
          }}
        >
          {/* Icon */}
          <motion.div
            animate={!isLocked ? {
              scale: [1, 1.1, 1],
              rotate: [0, 10, 0]
            } : {}}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
          >
            <Box
              sx={{
                width: sizeConfig.iconSize + 16,
                height: sizeConfig.iconSize + 16,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${color}, ${alpha(color, 0.8)})`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mx: 'auto',
                mb: 2,
                color: 'white',
                boxShadow: `
                  0 8px 24px ${alpha(color, 0.4)},
                  inset 0 1px 0 ${alpha('#ffffff', 0.2)}
                `
              }}
            >
              {React.cloneElement(icon, { fontSize: 'large' })}
            </Box>
          </motion.div>

          {/* Text */}
          <Typography
            variant="h6"
            fontWeight="bold"
            sx={{
              color: 'white',
              mb: 1,
              textShadow: '0 2px 8px rgba(0,0,0,0.6)',
              fontSize: size === 'small' ? '1rem' : size === 'large' ? '1.3rem' : '1.1rem'
            }}
          >
            {name}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              color: alpha('#fff', 0.9),
              textShadow: '0 1px 4px rgba(0,0,0,0.6)',
              lineHeight: 1.4,
              fontSize: size === 'small' ? '0.8rem' : '0.9rem'
            }}
          >
            {description}
          </Typography>

          {/* Action Indicator */}
          {!isLocked && (
            <motion.div
              animate={{ y: [0, -5, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
              style={{ marginTop: 12 }}
            >
              <TrendingUp 
                sx={{ 
                  color, 
                  fontSize: 24,
                  filter: `drop-shadow(0 2px 4px ${alpha(color, 0.6)})`
                }} 
              />
            </motion.div>
          )}
        </Box>
      </Box>
    </motion.div>
  );
};

export default ModernConnectedCard;
