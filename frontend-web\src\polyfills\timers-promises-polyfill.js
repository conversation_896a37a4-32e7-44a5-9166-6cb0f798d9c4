/**
 * Polyfill for timers/promises module used by MongoDB
 * This provides basic implementations of the setTimeout and setImmediate promise-based functions
 */

// Promise-based setTimeout
function setTimeout(delay, value) {
  return new Promise((resolve) => {
    window.setTimeout(() => resolve(value), delay);
  });
}

// Promise-based setImmediate (using setTimeout with 0 delay as a fallback)
function setImmediate(value) {
  return new Promise((resolve) => {
    window.setTimeout(() => resolve(value), 0);
  });
}

module.exports = {
  setTimeout,
  setImmediate
};
