import React, { useState, useEffect } from 'react';
import { Box, Typography, Link, useTheme, alpha, IconButton, Tabs, Tab, Chip, Tooltip, Paper, Divider } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowBackIos,
  ArrowForwardIos,
  Launch,
  Agriculture,
  School,
  Gavel,
  Business,
  Info
} from '@mui/icons-material';
import LazyImage from './LazyImage';
import { Sponsor } from './SponsorCarousel';
import { ResourceLink } from './FarmerResourceLinks';

interface EnhancedSponsorCarouselProps {
  sponsors: Sponsor[];
  resources: ResourceLink[];
  autoRotateInterval?: number; // in milliseconds
  autoRotate?: boolean;
  rotationInterval?: number; // in milliseconds
  title?: string;
  subtitle?: string;
  height?: number;
  showControls?: boolean;
  showTitle?: boolean;
  overlay?: boolean;
}

const EnhancedSponsorCarousel: React.FC<EnhancedSponsorCarouselProps> = ({
  sponsors,
  resources,
  autoRotateInterval = 10000, // Default to 10 seconds as per requirement
  autoRotate = true,
  rotationInterval,
  title = "Our Partners",
  subtitle = "Useful resources for farmers",
  height = 180,
  showControls = true,
  showTitle = true,
  overlay = false
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();

  // Auto-rotate sponsors
  useEffect(() => {
    if (!autoRotate || sponsors.length <= 1 || isHovered) return;

    // Use rotationInterval if provided, otherwise fall back to autoRotateInterval
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsors.length);
    }, rotationInterval || autoRotateInterval);

    return () => clearInterval(interval);
  }, [sponsors.length, autoRotateInterval, rotationInterval, autoRotate, isHovered]);

  // Handle manual navigation
  const goToSponsor = (index: number) => {
    setCurrentIndex(index);
  };

  // Handle opening sponsor website
  const openSponsorWebsite = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'government':
        return <Agriculture fontSize="small" />;
      case 'education':
        return <School fontSize="small" />;
      case 'regulatory':
        return <Gavel fontSize="small" />;
      case 'financial':
        return <Business fontSize="small" />;
      default:
        return <Info fontSize="small" />;
    }
  };

  // Group resources by category
  const groupedResources = React.useMemo(() => {
    return resources.reduce((acc, resource) => {
      if (!acc[resource.category]) {
        acc[resource.category] = [];
      }
      acc[resource.category].push(resource);
      return acc;
    }, {} as Record<string, ResourceLink[]>);
  }, [resources]);

  // Get display name for category
  const getCategoryDisplayName = (category: string) => {
    return category.charAt(0).toUpperCase() + category.slice(1);
  };

  if (!sponsors || sponsors.length === 0) {
    return null;
  }

  return (
    <Paper
      elevation={3}
      sx={{
        width: '100%',
        overflow: 'hidden',
        borderRadius: '12px',
        bgcolor: '#ffffff',
        backdropFilter: 'blur(8px)',
        transition: 'all 0.3s ease',
        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
        '&:hover': {
          boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
          transform: 'translateY(-2px)',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.4)}`
        }
      }}
    >
      {showTitle && (
        <Box p={3} pb={1}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {subtitle}
          </Typography>
        </Box>
      )}

      <Tabs
        value={tabValue}
        onChange={(_, newValue) => setTabValue(newValue)}
        sx={{
          px: 2,
          '& .MuiTab-root': {
            minWidth: 'auto',
            px: 2,
            py: 1.5,
            fontWeight: 600,
            color: alpha(theme.palette.text.primary, 0.7),
            transition: 'all 0.3s ease',
            '&.Mui-selected': {
              color: theme.palette.primary.main,
              fontWeight: 700
            },
            '&:hover': {
              color: theme.palette.primary.main,
              backgroundColor: alpha(theme.palette.primary.main, 0.05)
            }
          },
          '& .MuiTabs-indicator': {
            height: 3,
            borderTopLeftRadius: 3,
            borderTopRightRadius: 3,
            backgroundColor: theme.palette.primary.main
          }
        }}
      >
        <Tab label="Partners" />
        <Tab label="Resources" />
      </Tabs>

      <Divider />

      <AnimatePresence mode="wait">
        {tabValue === 0 ? (
          <motion.div
            key="sponsors"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
          >
            <Box
              sx={{
                position: 'relative',
                height: height,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'hidden',
                p: 2
              }}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {showControls && (
                <>
                  <IconButton
                    sx={{
                      position: 'absolute',
                      left: 8,
                      zIndex: 2,
                      bgcolor: alpha(theme.palette.background.paper, 0.7),
                      '&:hover': { bgcolor: alpha(theme.palette.background.paper, 0.9) },
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      width: 36,
                      height: 36
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentIndex((prevIndex) => (prevIndex === 0 ? sponsors.length - 1 : prevIndex - 1));
                    }}
                  >
                    <ArrowBackIos fontSize="small" />
                  </IconButton>
                  <IconButton
                    sx={{
                      position: 'absolute',
                      right: 8,
                      zIndex: 2,
                      bgcolor: alpha(theme.palette.background.paper, 0.7),
                      '&:hover': { bgcolor: alpha(theme.palette.background.paper, 0.9) },
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                      width: 36,
                      height: 36
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCurrentIndex((prevIndex) => (prevIndex === sponsors.length - 1 ? 0 : prevIndex + 1));
                    }}
                  >
                    <ArrowForwardIos fontSize="small" />
                  </IconButton>
                </>
              )}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -50 }}
                  transition={{ duration: 0.5 }}
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    cursor: 'pointer',
                  }}
                  onClick={() => openSponsorWebsite(sponsors[currentIndex].websiteUrl)}
                >
                  <Box
                    sx={{
                      height: '100%',
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      p: 2,
                    }}
                  >
                    <Box
                      sx={{
                        height: height - 60,
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        padding: '8px'
                      }}
                    >
                      <img
                        src={sponsors[currentIndex].logoUrl}
                        alt={sponsors[currentIndex].name}
                        style={{
                          maxWidth: '100%',
                          maxHeight: '100%',
                          objectFit: 'contain',
                          display: 'block'
                        }}
                        onError={(e) => {
                          // Fallback if image doesn't load
                          const target = e.target as HTMLImageElement;
                          target.src = `/assets/images/sponsors/${sponsors[currentIndex].id}.png`;
                        }}
                      />
                    </Box>
                    <Typography variant="subtitle1" color="textPrimary" align="center" mt={1} fontWeight="medium">
                      {sponsors[currentIndex].name}
                    </Typography>
                    {sponsors[currentIndex].description && (
                      <Typography variant="caption" color="textSecondary" align="center" sx={{ mt: 0.5, maxWidth: '80%' }}>
                        {sponsors[currentIndex].description}
                      </Typography>
                    )}
                  </Box>
                </motion.div>
              </AnimatePresence>
            </Box>

            {/* Navigation dots */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                pb: 2,
              }}
            >
              {sponsors.map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    mx: 0.5,
                    bgcolor: index === currentIndex ? theme.palette.primary.main : alpha(theme.palette.primary.main, 0.3),
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  onClick={() => goToSponsor(index)}
                />
              ))}
            </Box>

            <Box pb={2} textAlign="center">
              <Link
                href={sponsors[currentIndex].websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                underline="hover"
                color="primary"
                sx={{
                  fontSize: '0.875rem',
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: 0.5
                }}
              >
                Visit Website <Launch fontSize="small" />
              </Link>
            </Box>
          </motion.div>
        ) : (
          <motion.div
            key="resources"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: 'easeOut' }}
            style={{ maxHeight: height + 100, overflowY: 'auto', padding: '16px' }}
          >
            <Box sx={{ mb: 2 }}>
              {Object.entries(groupedResources).map(([category, categoryResources]) => (
                <Box key={category} sx={{ mb: 2 }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <Chip
                      icon={getCategoryIcon(category)}
                      label={getCategoryDisplayName(category)}
                      color="primary"
                      variant="outlined"
                      size="small"
                      sx={{
                        mr: 1,
                        fontWeight: 600,
                        borderWidth: 2,
                        bgcolor: alpha(theme.palette.primary.main, 0.05),
                        '& .MuiChip-icon': {
                          color: theme.palette.primary.main
                        }
                      }}
                    />
                  </Box>

                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {categoryResources.map((resource) => (
                      <Tooltip key={resource.id} title={resource.description}>
                        <Chip
                          icon={getCategoryIcon(resource.category)}
                          label={resource.name}
                          variant="filled"
                          size="medium"
                          onClick={() => window.open(resource.url, '_blank')}
                          sx={{
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                            transition: 'all 0.3s ease',
                            fontWeight: 500,
                            '&:hover': {
                              bgcolor: alpha(theme.palette.primary.main, 0.2),
                              transform: 'translateY(-2px)',
                              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
                            },
                            cursor: 'pointer',
                            mb: 1,
                            px: 1
                          }}
                          deleteIcon={<Launch fontSize="small" sx={{ color: theme.palette.primary.main }} />}
                          onDelete={() => window.open(resource.url, '_blank')}
                        />
                      </Tooltip>
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
          </motion.div>
        )}
      </AnimatePresence>
    </Paper>
  );
};

export default EnhancedSponsorCarousel;
