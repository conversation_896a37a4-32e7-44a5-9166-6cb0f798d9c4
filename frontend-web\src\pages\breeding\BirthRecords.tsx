import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar } from '@mui/material';
import { 
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  Favorite,
  LocalHospital,
  Notifications,
  Male,
  Female
 } from '../../utils/iconImports';
import { mockAnimals } from '../../mocks/animalData';

interface Offspring {
  id: string;
  gender: 'male' | 'female';
  weight: number;
  health: 'healthy' | 'weak' | 'requires attention' | 'deceased';
  notes?: string;
}

interface BirthRecord {
  id: string;
  motherAnimalId: string;
  motherName: string;
  breed: string;
  birthDate: string;
  location: string;
  status: 'normal' | 'assisted' | 'complications' | 'stillborn';
  assistedBy?: string;
  offspring: Offspring[];
  notes: string;
}

const BirthRecords: React.FC = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<BirthRecord | null>(null);
  const [birthRecords, setBirthRecords] = useState<BirthRecord[]>([
    {
      id: 'BR001',
      motherAnimalId: 'ANM001',
      motherName: 'Umfazi',
      breed: 'Nguni',
      birthDate: '2024-03-10',
      location: 'Kraal 1',
      status: 'normal',
      offspring: [
        {
          id: 'OFF001',
          gender: 'female',
          weight: 32.5,
          health: 'healthy',
          notes: 'Strong calf, nursing well'
        }
      ],
      notes: 'Smooth birth with no complications'
    },
    {
      id: 'BR002',
      motherAnimalId: 'ANM003',
      motherName: 'Impilo',
      breed: 'Afrikaner',
      birthDate: '2024-02-25',
      location: 'Kraal 2',
      status: 'assisted',
      assistedBy: 'Dr. van Niekerk',
      offspring: [
        {
          id: 'OFF002',
          gender: 'male',
          weight: 35.2,
          health: 'healthy',
          notes: 'Required minor assistance, but healthy'
        }
      ],
      notes: 'Minor assistance required during delivery'
    },
    {
      id: 'BR003',
      motherAnimalId: 'ANM004',
      motherName: 'Imvula',
      breed: 'Dorper',
      birthDate: '2024-03-05',
      location: 'Boma 1',
      status: 'normal',
      offspring: [
        {
          id: 'OFF003',
          gender: 'female',
          weight: 4.2,
          health: 'healthy',
          notes: 'First lamb'
        },
        {
          id: 'OFF004',
          gender: 'male',
          weight: 4.5,
          health: 'healthy',
          notes: 'Second lamb'
        }
      ],
      notes: 'Twin lambs, both healthy and nursing well'
    },
    {
      id: 'BR004',
      motherAnimalId: 'ANM008',
      motherName: 'Umama',
      breed: 'Nguni',
      birthDate: '2024-02-15',
      location: 'Veld A',
      status: 'complications',
      assistedBy: 'Dr. Botha',
      offspring: [
        {
          id: 'OFF005',
          gender: 'female',
          weight: 28.5,
          health: 'weak',
          notes: 'Requires supplemental feeding'
        }
      ],
      notes: 'Difficult birth, mother and calf required veterinary intervention'
    },
    {
      id: 'BR005',
      motherAnimalId: 'ANM010',
      motherName: 'Ithemba',
      breed: 'Bonsmara',
      birthDate: '2024-03-12',
      location: 'Kraal 1',
      status: 'normal',
      offspring: [
        {
          id: 'OFF006',
          gender: 'male',
          weight: 36.8,
          health: 'healthy',
          notes: 'Large, healthy calf'
        }
      ],
      notes: 'Smooth birth, experienced mother'
    }
  ]);

  const handleAddNew = () => {
    setSelectedRecord(null);
    setOpenDialog(true);
  };

  const handleEdit = (record: BirthRecord) => {
    setSelectedRecord(record);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setBirthRecords(birthRecords.filter(record => record.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'normal':
        return <Chip label="Normal" color="success" size="small" />;
      case 'assisted':
        return <Chip label="Assisted" color="info" size="small" />;
      case 'complications':
        return <Chip label="Complications" color="warning" size="small" />;
      case 'stillborn':
        return <Chip label="Stillborn" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getHealthChip = (health: string) => {
    switch (health) {
      case 'healthy':
        return <Chip label="Healthy" color="success" size="small" />;
      case 'weak':
        return <Chip label="Weak" color="warning" size="small" />;
      case 'requires attention':
        return <Chip label="Requires Attention" color="error" size="small" />;
      case 'deceased':
        return <Chip label="Deceased" color="default" size="small" sx={{ bgcolor: '#000', color: '#fff' }} />;
      default:
        return <Chip label={health} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getTotalOffspring = () => {
    return birthRecords.reduce((total, record) => total + record.offspring.length, 0);
  };

  const getMaleFemaleRatio = () => {
    const males = birthRecords.reduce((count, record) => {
      return count + record.offspring.filter(offspring => offspring.gender === 'male').length;
    }, 0);
    
    const females = birthRecords.reduce((count, record) => {
      return count + record.offspring.filter(offspring => offspring.gender === 'female').length;
    }, 0);
    
    return { males, females };
  };

  const getHealthyPercentage = () => {
    const total = birthRecords.reduce((count, record) => count + record.offspring.length, 0);
    if (total === 0) return 0;
    
    const healthy = birthRecords.reduce((count, record) => {
      return count + record.offspring.filter(offspring => offspring.health === 'healthy').length;
    }, 0);
    
    return Math.round((healthy / total) * 100);
  };

  const { males, females } = getMaleFemaleRatio();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-6"
      style={{ background: 'linear-gradient(to right, #f8f0f4, #f0d7e3)' }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
          Birth Records
        </Typography>
        <CustomButton
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={handleAddNew}
          sx={{ borderRadius: '8px', boxShadow: '0 4px 10px rgba(0,0,0,0.1)' }}
        >
          Add Birth Record
        </CustomButton>
      </Box>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Birth Statistics
              </Typography>
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="primary">
                        {birthRecords.length}
                      </Typography>
                      <Typography variant="body2">Total Births</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {getTotalOffspring()}
                      </Typography>
                      <Typography variant="body2">Total Offspring</Typography>
                    </Box>
                  </Grid>
                </Grid>
                
                <Box mt={2}>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Gender Distribution
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box display="flex" alignItems="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                        <Male color="primary" />
                        <Box ml={1}>
                          <Typography variant="h6" fontWeight="bold">{males}</Typography>
                          <Typography variant="caption">Males</Typography>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box display="flex" alignItems="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                        <Female color="secondary" />
                        <Box ml={1}>
                          <Typography variant="h6" fontWeight="bold">{females}</Typography>
                          <Typography variant="caption">Females</Typography>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Health Status
              </Typography>
              <Box mt={2} textAlign="center">
                <Box position="relative" display="inline-block">
                  <Typography 
                    variant="h2" 
                    fontWeight="bold" 
                    color={getHealthyPercentage() > 80 ? "success.main" : "warning.main"}
                  >
                    {getHealthyPercentage()}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Healthy Offspring
                  </Typography>
                </Box>
                
                <Box mt={3}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box textAlign="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                        <Typography variant="h6" fontWeight="bold">
                          {birthRecords.filter(record => record.status === 'normal').length}
                        </Typography>
                        <Typography variant="body2">Normal Births</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box textAlign="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                        <Typography variant="h6" fontWeight="bold">
                          {birthRecords.filter(record => record.status === 'assisted' || record.status === 'complications').length}
                        </Typography>
                        <Typography variant="body2">Assisted Births</Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Recent Births
              </Typography>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Mother</TableCell>
                      <TableCell>Breed</TableCell>
                      <TableCell>Offspring</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {birthRecords
                      .sort((a, b) => new Date(b.birthDate).getTime() - new Date(a.birthDate).getTime())
                      .slice(0, 5)
                      .map(record => (
                        <TableRow key={record.id} hover>
                          <TableCell>{formatDate(record.birthDate)}</TableCell>
                          <TableCell>{record.motherName}</TableCell>
                          <TableCell>{record.breed}</TableCell>
                          <TableCell>
                            {record.offspring.map((offspring, index) => (
                              <Chip 
                                key={offspring.id}
                                icon={offspring.gender === 'male' ? <Male fontSize="small" /> : <Female fontSize="small" />}
                                label={`${offspring.weight} kg`}
                                size="small"
                                color={offspring.gender === 'male' ? "primary" : "secondary"}
                                sx={{ mr: 0.5, mb: 0.5 }}
                              />
                            ))}
                          </TableCell>
                          <TableCell>{getStatusChip(record.status)}</TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', overflow: 'hidden' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table>
              <TableHead sx={{ bgcolor: 'primary.main' }}>
                <TableRow>
                  <TableCell sx={{ color: 'white' }}>Date</TableCell>
                  <TableCell sx={{ color: 'white' }}>Mother</TableCell>
                  <TableCell sx={{ color: 'white' }}>Breed</TableCell>
                  <TableCell sx={{ color: 'white' }}>Location</TableCell>
                  <TableCell sx={{ color: 'white' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white' }}>Offspring</TableCell>
                  <TableCell sx={{ color: 'white' }}>Notes</TableCell>
                  <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {birthRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>{formatDate(record.birthDate)}</TableCell>
                    <TableCell>{record.motherName}</TableCell>
                    <TableCell>{record.breed}</TableCell>
                    <TableCell>{record.location}</TableCell>
                    <TableCell>{getStatusChip(record.status)}</TableCell>
                    <TableCell>
                      <Box>
                        {record.offspring.map((offspring, index) => (
                          <Box key={offspring.id} display="flex" alignItems="center" mb={index < record.offspring.length - 1 ? 1 : 0}>
                            {offspring.gender === 'male' ? 
                              <Male fontSize="small" color="primary" sx={{ mr: 1 }} /> : 
                              <Female fontSize="small" color="secondary" sx={{ mr: 1 }} />
                            }
                            <Typography variant="body2" mr={1}>
                              {offspring.weight} kg
                            </Typography>
                            {getHealthChip(offspring.health)}
                          </Box>
                        ))}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Tooltip title={record.notes}>
                        <Typography
                          variant="body2"
                          sx={{
                            maxWidth: 150,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {record.notes}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(record)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(record.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedRecord ? 'Edit Birth Record' : 'Add New Birth Record'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="mother-select-label">Mother</InputLabel>
                <Select
                  labelId="mother-select-label"
                  id="mother-select"
                  label="Mother"
                  defaultValue={selectedRecord?.motherAnimalId || ''}
                >
                  {mockAnimals
                    .filter(animal => animal.gender.toLowerCase() === 'female')
                    .map(animal => (
                      <MenuItem key={animal.id} value={animal.id}>
                        {animal.name} ({animal.breed})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="birthDate"
                label="Birth Date"
                type="date"
                defaultValue={selectedRecord?.birthDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="location"
                label="Location"
                defaultValue={selectedRecord?.location || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedRecord?.status || 'normal'}
                >
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="assisted">Assisted</MenuItem>
                  <MenuItem value="complications">Complications</MenuItem>
                  <MenuItem value="stillborn">Stillborn</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom mt={2}>
                Offspring Details
              </Typography>
              {/* Offspring details would go here - simplified for this example */}
              <Box p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel id="gender-select-label">Gender</InputLabel>
                      <Select
                        labelId="gender-select-label"
                        id="gender-select"
                        label="Gender"
                        defaultValue="male"
                      >
                        <MenuItem value="male">Male</MenuItem>
                        <MenuItem value="female">Female</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Weight (kg)"
                      type="number"
                      defaultValue="30"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel id="health-select-label">Health</InputLabel>
                      <Select
                        labelId="health-select-label"
                        id="health-select"
                        label="Health"
                        defaultValue="healthy"
                      >
                        <MenuItem value="healthy">Healthy</MenuItem>
                        <MenuItem value="weak">Weak</MenuItem>
                        <MenuItem value="requires attention">Requires Attention</MenuItem>
                        <MenuItem value="deceased">Deceased</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                <CustomButton 
                  variant="outlined" 
                  startIcon={<Add />} 
                  sx={{ mt: 2 }}
                >
                  Add Another Offspring
                </CustomButton>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedRecord?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedRecord ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default BirthRecords;
