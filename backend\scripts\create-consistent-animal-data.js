/**
 * MongoDB Consistent Animal Data Creation Script
 *
 * This script creates 10 animal records with Sotho, Tswana, and Afrikaans names
 * and establishes proper relationships between them across all collections.
 *
 * It ensures data consistency across all modules by creating related records
 * for each animal in health_records, breeding_records, birth_records,
 * feeding_records, and financial_records collections.
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object
const client = new MongoClient(uri, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
});

// South African names (Sotho, Tswana, and Afrikaans - no Zulu)
const animalNames = {
  male: [
    { name: 'Tumelo', meaning: 'Faith', origin: 'Tswana' },
    { name: 'Thabo', meaning: 'Joy', origin: 'Sotho' },
    { name: 'Kgosi', meaning: 'Chief', origin: 'Tswana' },
    { name: 'Tau', meaning: 'Lion', origin: 'Tswana' },
    { name: 'Pule', meaning: 'Rain', origin: 'Sotho' },
    { name: 'Katlego', meaning: 'Success', origin: 'Tswana' },
    { name: 'Lesedi', meaning: 'Light', origin: 'Tswana' },
    { name: 'Botha', meaning: 'Messenger', origin: 'Afrikaans' },
    { name: 'Willem', meaning: 'Determined protector', origin: 'Afrikaans' },
    { name: 'Pieter', meaning: 'Rock', origin: 'Afrikaans' }
  ],
  female: [
    { name: 'Tshepiso', meaning: 'Promise', origin: 'Tswana' },
    { name: 'Lerato', meaning: 'Love', origin: 'Sotho' },
    { name: 'Naledi', meaning: 'Star', origin: 'Tswana' },
    { name: 'Karabo', meaning: 'Answer', origin: 'Sotho' },
    { name: 'Phefo', meaning: 'Shelter', origin: 'Sotho' },
    { name: 'Bontle', meaning: 'Beauty', origin: 'Tswana' },
    { name: 'Dikeledi', meaning: 'Tears', origin: 'Sotho' },
    { name: 'Anika', meaning: 'Grace', origin: 'Afrikaans' },
    { name: 'Elise', meaning: 'Pledged to God', origin: 'Afrikaans' },
    { name: 'Marieke', meaning: 'Bitter', origin: 'Afrikaans' }
  ]
};

// Animal types and breeds
const animalTypes = {
  'Cattle': ['Nguni', 'Bonsmara', 'Afrikaner', 'Drakensberger', 'Brahman', 'Simbra', 'Beefmaster'],
  'Sheep': ['Dorper', 'Merino', 'Damara', 'Meatmaster', 'Dormer', 'Van Rooy'],
  'Goat': ['Boer', 'Kalahari Red', 'Savanna', 'Indigenous Veld', 'Angora'],
  'Game': ['Springbok', 'Impala', 'Kudu', 'Blesbok', 'Wildebeest'],
  'Horse': ['Boerperd', 'Nooitgedacht', 'Basuto Pony', 'SA Warmblood', 'Thoroughbred']
};

// Farm locations
const locations = ['Veld A', 'Veld B', 'Kraal 1', 'Kraal 2', 'Boma 1', 'Boma 2', 'Quarantine'];

// Health record types
const healthRecordTypes = ['Vaccination', 'Treatment', 'Examination', 'Surgery', 'Deworming'];

// Feed types
const feedTypes = ['Lucerne', 'Maize', 'Eragrostis', 'Teff', 'Molasses Meal', 'Protein Supplement', 'Mineral Lick', 'Veld Grazing'];

// Helper functions
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function generateTagNumber(type, year, sequence) {
  const typeCode = type.substring(0, 3).toUpperCase();
  return `${typeCode}-${year}-${sequence.toString().padStart(3, '0')}`;
}

function generateRfidTag(sequence) {
  return `RFID${sequence.toString().padStart(5, '0')}`;
}

// Main function to create consistent animal data
async function createConsistentAnimalData() {
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);

    // Clear existing collections to avoid duplicates
    const collections = [
      'animals',
      'health_records',
      'breeding_records',
      'birth_records',
      'feeding_records',
      'feed_inventory',
      'financial_records'
    ];

    for (const collectionName of collections) {
      await db.collection(collectionName).deleteMany({});
      console.log(`Cleared ${collectionName} collection`);
    }

    // Create 10 animals (5 male, 5 female)
    const animals = [];
    const animalIds = [];

    // Create 3 male and 3 female cattle (for breeding)
    for (let i = 0; i < 3; i++) {
      // Male cattle
      const maleId = new ObjectId();
      const maleInfo = animalNames.male[i];
      const maleYear = 2019 + i;
      const maleTagNumber = generateTagNumber('Cattle', maleYear, i + 1);

      const male = {
        _id: maleId,
        tagNumber: maleTagNumber,
        name: maleInfo.name,
        type: 'Cattle',
        species: 'Cattle',
        breed: getRandomElement(animalTypes['Cattle']),
        gender: 'Male',
        birthDate: `${maleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        weight: getRandomInt(600, 900),
        status: 'Active',
        healthStatus: 'healthy',
        location: getRandomElement(locations),
        purchaseDate: `${maleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        purchasePrice: getRandomInt(15000, 25000),
        notes: `${maleInfo.origin} name meaning "${maleInfo.meaning}". Strong bull with excellent genetics.`,
        rfidTag: generateRfidTag(i + 1),
        imageUrl: `/images/animals/cattle-small-${i + 1}.jpg`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(male);
      animalIds.push(maleId);

      // Female cattle
      const femaleId = new ObjectId();
      const femaleInfo = animalNames.female[i];
      const femaleYear = 2020 + i;
      const femaleTagNumber = generateTagNumber('Cattle', femaleYear, i + 4);

      const female = {
        _id: femaleId,
        tagNumber: femaleTagNumber,
        name: femaleInfo.name,
        type: 'Cattle',
        species: 'Cattle',
        breed: getRandomElement(animalTypes['Cattle']),
        gender: 'Female',
        birthDate: `${femaleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        weight: getRandomInt(450, 650),
        status: 'Active',
        healthStatus: 'healthy',
        location: getRandomElement(locations),
        purchaseDate: `${femaleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        purchasePrice: getRandomInt(12000, 18000),
        notes: `${femaleInfo.origin} name meaning "${femaleInfo.meaning}". Excellent breeding cow.`,
        rfidTag: generateRfidTag(i + 4),
        imageUrl: `/images/animals/cattle-small-${i + 4}.jpg`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(female);
      animalIds.push(femaleId);
    }

    // Create 2 male and 2 female of other animal types
    const otherTypes = ['Sheep', 'Goat'];
    for (let i = 0; i < 2; i++) {
      const type = otherTypes[i];

      // Male
      const maleId = new ObjectId();
      const maleInfo = animalNames.male[i + 3];
      const maleYear = 2021;
      const maleTagNumber = generateTagNumber(type, maleYear, i + 1);

      const male = {
        _id: maleId,
        tagNumber: maleTagNumber,
        name: maleInfo.name,
        type: type,
        species: type,
        breed: getRandomElement(animalTypes[type]),
        gender: 'Male',
        birthDate: `${maleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        weight: type === 'Sheep' ? getRandomInt(70, 100) : getRandomInt(60, 90),
        status: 'Active',
        healthStatus: 'healthy',
        location: getRandomElement(locations),
        purchaseDate: `${maleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        purchasePrice: type === 'Sheep' ? getRandomInt(4000, 6000) : getRandomInt(3500, 5500),
        notes: `${maleInfo.origin} name meaning "${maleInfo.meaning}". Breeding male.`,
        rfidTag: generateRfidTag(i + 7),
        imageUrl: `/images/animals/${type.toLowerCase()}-small-${i + 1}.jpg`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(male);
      animalIds.push(maleId);

      // Female
      const femaleId = new ObjectId();
      const femaleInfo = animalNames.female[i + 3];
      const femaleYear = 2021;
      const femaleTagNumber = generateTagNumber(type, femaleYear, i + 3);

      const female = {
        _id: femaleId,
        tagNumber: femaleTagNumber,
        name: femaleInfo.name,
        type: type,
        species: type,
        breed: getRandomElement(animalTypes[type]),
        gender: 'Female',
        birthDate: `${femaleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        weight: type === 'Sheep' ? getRandomInt(50, 70) : getRandomInt(40, 65),
        status: 'Active',
        healthStatus: 'healthy',
        location: getRandomElement(locations),
        purchaseDate: `${femaleYear}-${getRandomInt(1, 12).toString().padStart(2, '0')}-${getRandomInt(1, 28).toString().padStart(2, '0')}`,
        purchasePrice: type === 'Sheep' ? getRandomInt(3500, 5000) : getRandomInt(3000, 4500),
        notes: `${femaleInfo.origin} name meaning "${femaleInfo.meaning}". Breeding female.`,
        rfidTag: generateRfidTag(i + 9),
        imageUrl: `/images/animals/${type.toLowerCase()}-small-${i + 3}.jpg`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(female);
      animalIds.push(femaleId);
    }

    // Insert animals
    const animalResult = await db.collection('animals').insertMany(animals);
    console.log(`Inserted ${animalResult.insertedCount} animals`);

    // Create breeding records (for cattle)
    const breedingRecords = [];
    const birthRecords = [];

    // Create breeding pairs between cattle
    for (let i = 0; i < 3; i++) {
      const maleId = animals[i * 2]._id;
      const femaleId = animals[i * 2 + 1]._id;
      const maleTagNumber = animals[i * 2].tagNumber;
      const femaleTagNumber = animals[i * 2 + 1].tagNumber;
      const maleName = animals[i * 2].name;
      const femaleName = animals[i * 2 + 1].name;

      // Breeding date (about 9 months before birth)
      const breedingDate = new Date();
      breedingDate.setMonth(breedingDate.getMonth() - 9 - getRandomInt(0, 2));

      const breedingRecord = {
        _id: new ObjectId(),
        femaleId: femaleId.toString(),
        femaleTagNumber: femaleTagNumber,
        femaleName: femaleName,
        maleId: maleId.toString(),
        maleTagNumber: maleTagNumber,
        maleName: maleName,
        date: breedingDate.toISOString().split('T')[0],
        status: 'successful',
        method: 'natural',
        location: getRandomElement(locations),
        notes: `Breeding between ${maleName} and ${femaleName}`,
        expectedBirthDate: new Date(breedingDate.getTime() + 280 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      breedingRecords.push(breedingRecord);

      // Create offspring (birth record)
      const birthDate = new Date(breedingDate.getTime() + 280 * 24 * 60 * 60 * 1000);

      // Create offspring animal
      const offspringId = new ObjectId();
      const offspringGender = getRandomElement(['Male', 'Female']);
      const offspringInfo = offspringGender === 'Male'
        ? animalNames.male[i + 5]
        : animalNames.female[i + 5];

      const birthYear = birthDate.getFullYear();
      const offspringTagNumber = generateTagNumber('Cattle', birthYear, i + 7);

      const offspring = {
        _id: offspringId,
        tagNumber: offspringTagNumber,
        name: offspringInfo.name,
        type: 'Cattle',
        species: 'Cattle',
        breed: animals[i * 2 + 1].breed, // Same breed as mother
        gender: offspringGender,
        birthDate: birthDate.toISOString().split('T')[0],
        weight: getRandomInt(25, 35),
        status: 'Active',
        healthStatus: 'healthy',
        location: animals[i * 2 + 1].location, // Same location as mother
        purchaseDate: null,
        purchasePrice: null,
        notes: `${offspringInfo.origin} name meaning "${offspringInfo.meaning}". Born on farm, offspring of ${femaleName} and ${maleName}.`,
        rfidTag: generateRfidTag(i + 11),
        imageUrl: `/images/animals/cattle-small-${i + 7}.jpg`,
        parentId: femaleId.toString(),
        parentTagNumber: femaleTagNumber,
        sireId: maleId.toString(),
        damId: femaleId.toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      animals.push(offspring);
      animalIds.push(offspringId);

      // Birth record
      const birthRecord = {
        _id: new ObjectId(),
        breedingRecordId: breedingRecord._id.toString(),
        motherId: femaleId.toString(),
        motherTagNumber: femaleTagNumber,
        motherName: femaleName,
        fatherId: maleId.toString(),
        fatherTagNumber: maleTagNumber,
        fatherName: maleName,
        birthDate: birthDate.toISOString().split('T')[0],
        location: animals[i * 2 + 1].location,
        offspringId: offspringId.toString(),
        offspringTagNumber: offspringTagNumber,
        offspringName: offspringInfo.name,
        offspringGender: offspringGender,
        birthWeight: offspring.weight,
        birthStatus: 'normal',
        notes: `Normal birth of ${offspringInfo.name}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      birthRecords.push(birthRecord);
    }

    // Insert offspring animals
    const offspringResult = await db.collection('animals').insertMany(animals.slice(10));
    console.log(`Inserted ${offspringResult.insertedCount} offspring animals`);

    // Insert breeding records
    const breedingResult = await db.collection('breeding_records').insertMany(breedingRecords);
    console.log(`Inserted ${breedingResult.insertedCount} breeding records`);

    // Insert birth records
    const birthResult = await db.collection('birth_records').insertMany(birthRecords);
    console.log(`Inserted ${birthResult.insertedCount} birth records`);

    // Create health records for all animals
    const healthRecords = [];

    for (const animal of animals) {
      // Create 2-3 health records per animal
      const recordCount = getRandomInt(2, 3);

      for (let i = 0; i < recordCount; i++) {
        const recordType = getRandomElement(healthRecordTypes);
        const recordDate = new Date(new Date(animal.birthDate).getTime() + getRandomInt(30, 365) * 24 * 60 * 60 * 1000);

        const healthRecord = {
          _id: new ObjectId(),
          animalId: animal._id.toString(),
          animalTagNumber: animal.tagNumber,
          animalName: animal.name,
          recordType: recordType,
          description: `${recordType} for ${animal.name}`,
          date: recordDate.toISOString().split('T')[0],
          performedBy: getRandomElement(['Dr. Smith', 'Dr. Molefe', 'Dr. van der Merwe', 'Dr. Botha', 'Dr. Ndlovu']),
          status: 'completed',
          cost: getRandomInt(200, 1000),
          notes: `Routine ${recordType.toLowerCase()} with no complications`,
          followUpDate: new Date(recordDate.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          createdAt: new Date(),
          updatedAt: new Date()
        };

        healthRecords.push(healthRecord);
      }
    }

    // Insert health records
    const healthResult = await db.collection('health_records').insertMany(healthRecords);
    console.log(`Inserted ${healthResult.insertedCount} health records`);

    // Create feed inventory
    const feedInventory = [];

    for (let i = 0; i < 5; i++) {
      const feedType = feedTypes[i];

      const feed = {
        _id: new ObjectId(),
        name: feedType,
        type: feedType,
        quantity: getRandomInt(500, 5000),
        unit: getRandomElement(['kg', 'tons', 'bales']),
        location: getRandomElement(['Storage Barn', 'Feed Silo', 'Hay Shed', 'Feed Room']),
        purchaseDate: new Date(Date.now() - getRandomInt(1, 90) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        purchasePrice: getRandomInt(5000, 20000),
        supplier: getRandomElement(['Afgri Animal Feeds', 'Kaap Agri', 'Obaro', 'GWK', 'BKB', 'OVK']),
        expiryDate: new Date(Date.now() + getRandomInt(90, 365) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        nutritionalInfo: {
          protein: getRandomInt(10, 25),
          fiber: getRandomInt(5, 15),
          fat: getRandomInt(2, 8),
          minerals: getRandomInt(3, 10)
        },
        notes: `${feedType} for livestock feeding`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      feedInventory.push(feed);
    }

    // Insert feed inventory
    const feedInventoryResult = await db.collection('feed_inventory').insertMany(feedInventory);
    console.log(`Inserted ${feedInventoryResult.insertedCount} feed inventory items`);

    // Create feeding records
    const feedingRecords = [];

    for (const animal of animals) {
      // Create 2 feeding records per animal
      for (let i = 0; i < 2; i++) {
        const feed = getRandomElement(feedInventory);
        const feedingDate = new Date(Date.now() - getRandomInt(1, 30) * 24 * 60 * 60 * 1000);

        const feedingRecord = {
          _id: new ObjectId(),
          animalId: animal._id.toString(),
          animalTagNumber: animal.tagNumber,
          animalName: animal.name,
          groupId: null,
          groupName: null,
          feedType: feed.type,
          quantity: animal.type === 'Cattle' ? getRandomInt(10, 20) : getRandomInt(2, 8),
          unit: 'kg',
          feedingDate: feedingDate.toISOString().split('T')[0],
          feedingTime: `${getRandomInt(6, 18).toString().padStart(2, '0')}:${getRandomInt(0, 59).toString().padStart(2, '0')}`,
          location: animal.location,
          cost: getRandomInt(50, 200),
          notes: `Regular feeding of ${feed.type}`,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        feedingRecords.push(feedingRecord);
      }
    }

    // Insert feeding records
    const feedingResult = await db.collection('feeding_records').insertMany(feedingRecords);
    console.log(`Inserted ${feedingResult.insertedCount} feeding records`);

    // Create financial records
    const financialRecords = [];

    // Purchase records for animals
    for (const animal of animals.slice(0, 10)) { // Only for purchased animals
      if (animal.purchasePrice) {
        const financialRecord = {
          _id: new ObjectId(),
          recordType: 'Expense',
          category: 'Animal Purchase',
          amount: animal.purchasePrice,
          date: animal.purchaseDate,
          description: `Purchase of ${animal.name} (${animal.tagNumber})`,
          paymentMethod: getRandomElement(['Cash', 'Bank Transfer', 'Credit Card']),
          reference: `INV-${getRandomInt(1000, 9999)}`,
          relatedEntityType: 'Animal',
          relatedEntityId: animal._id.toString(),
          notes: `Initial purchase of ${animal.type} ${animal.gender.toLowerCase()}`,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        financialRecords.push(financialRecord);
      }
    }

    // Health expense records
    for (const record of healthRecords) {
      const financialRecord = {
        _id: new ObjectId(),
        recordType: 'Expense',
        category: 'Veterinary',
        amount: record.cost,
        date: record.date,
        description: `${record.recordType} for ${record.animalName} (${record.animalTagNumber})`,
        paymentMethod: getRandomElement(['Cash', 'Bank Transfer', 'Credit Card']),
        reference: `VET-${getRandomInt(1000, 9999)}`,
        relatedEntityType: 'Health',
        relatedEntityId: record._id.toString(),
        notes: `Veterinary expense for ${record.recordType.toLowerCase()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      financialRecords.push(financialRecord);
    }

    // Feed purchase records
    for (const feed of feedInventory) {
      const financialRecord = {
        _id: new ObjectId(),
        recordType: 'Expense',
        category: 'Feed',
        amount: feed.purchasePrice,
        date: feed.purchaseDate,
        description: `Purchase of ${feed.name}`,
        paymentMethod: getRandomElement(['Cash', 'Bank Transfer', 'Credit Card']),
        reference: `FEED-${getRandomInt(1000, 9999)}`,
        relatedEntityType: 'Feed',
        relatedEntityId: feed._id.toString(),
        notes: `Feed purchase from ${feed.supplier}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      financialRecords.push(financialRecord);
    }

    // Insert financial records
    const financialResult = await db.collection('financial_records').insertMany(financialRecords);
    console.log(`Inserted ${financialResult.insertedCount} financial records`);

    // Verify data was inserted correctly
    const animalCount = await db.collection('animals').countDocuments();
    const healthCount = await db.collection('health_records').countDocuments();
    const breedingCount = await db.collection('breeding_records').countDocuments();
    const birthCount = await db.collection('birth_records').countDocuments();
    const feedingCount = await db.collection('feeding_records').countDocuments();
    const feedInventoryCount = await db.collection('feed_inventory').countDocuments();
    const financialCount = await db.collection('financial_records').countDocuments();

    console.log('\nData verification:');
    console.log(`Animals: ${animalCount}`);
    console.log(`Health records: ${healthCount}`);
    console.log(`Breeding records: ${breedingCount}`);
    console.log(`Birth records: ${birthCount}`);
    console.log(`Feeding records: ${feedingCount}`);
    console.log(`Feed inventory: ${feedInventoryCount}`);
    console.log(`Financial records: ${financialCount}`);

    console.log('\nData creation completed successfully!');

  } catch (error) {
    console.error('Error creating consistent animal data:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the script
console.log('Starting script to create consistent animal data...');
createConsistentAnimalData()
  .then(() => console.log('Script completed successfully!'))
  .catch(error => {
    console.error('Script failed with error:', error);
    process.exit(1);
  });
