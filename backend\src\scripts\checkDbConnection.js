/**
 * Database Connection Check Script
 * 
 * This script checks the MongoDB connection and provides detailed error information
 * to help diagnose connection issues.
 */

const { connectDB, disconnectDB } = require('../config/database');
const logger = require('../utils/logger');

async function checkDatabaseConnection() {
  logger.info('Running database connection check...');
  
  try {
    // Try to connect to the database
    await connectDB();
    logger.info('✅ Database connection successful!');
    
    // Disconnect after successful connection
    await disconnectDB();
    return true;
  } catch (error) {
    // Log detailed error information
    logger.error('❌ Database connection failed!');
    
    // Check for common connection errors
    if (error.name === 'MongoNetworkError') {
      logger.error('Network error connecting to MongoDB. Please check:');
      logger.error('- MongoDB server is running');
      logger.error('- Connection string is correct');
      logger.error('- Network allows connection to MongoDB port');
    } else if (error.name === 'MongoServerSelectionError') {
      logger.error('Server selection error. Please check:');
      logger.error('- MongoDB server is running');
      logger.error('- Authentication credentials are correct');
      logger.error('- MongoDB server is accessible from this network');
    } else if (error.message.includes('timeout')) {
      logger.error('Connection timeout. Please check:');
      logger.error('- MongoDB server is running');
      logger.error('- Network latency is not too high');
      logger.error('- Firewall settings allow connection');
    } else if (error.message.includes('authentication')) {
      logger.error('Authentication failed. Please check:');
      logger.error('- Username and password are correct');
      logger.error('- User has appropriate permissions');
      logger.error('- Authentication database is correct');
    }
    
    // Log the original error for debugging
    logger.error('Original error:', error);
    
    return false;
  }
}

// Run the check if this script is executed directly
if (require.main === module) {
  checkDatabaseConnection()
    .then(success => {
      if (success) {
        process.exit(0);
      } else {
        process.exit(1);
      }
    })
    .catch(error => {
      logger.error('Unexpected error during connection check:', error);
      process.exit(1);
    });
}

module.exports = checkDatabaseConnection;
