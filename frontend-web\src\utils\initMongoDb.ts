import { getCollection } from './mongoDbClient';
import { mockFeedingRecords, mockFeedInventory, mockFeedingPlans } from '../mocks/feedingData';

/**
 * Initialize MongoDB collections with consistent data
 */
export const initMongoDb = async () => {
  try {
    console.log('Checking MongoDB collections...');

    // Check if collections exist and have data
    const collections = [
      'animals',
      'health_records',
      'breeding_records',
      'birth_records',
      'feeding_records',
      'feed_inventory',
      'financial_records',
      'inventory',
      'users'
    ];

    console.log('Using database: ampd_livestock');

    let allCollectionsHaveData = true;

    for (const collectionName of collections) {
      const collection = await getCollection(collectionName);
      const count = await collection.countDocuments({});

      console.log(`${collectionName} collection has ${count} documents`);

      if (count === 0) {
        allCollectionsHaveData = false;
      }
    }

    if (!allCollectionsHaveData) {
      console.log('Some collections are empty. Please run the migration script to populate the database.');
      console.log('Command: node backend/scripts/migrate-mongodb-data.js');
      return false;
    }

    console.log('All MongoDB collections have data');
    return true;
  } catch (error) {
    console.error('Error checking MongoDB collections:', error);
    return false;
  }
};

export default initMongoDb;
