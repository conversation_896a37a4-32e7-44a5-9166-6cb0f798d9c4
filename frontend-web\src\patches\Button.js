/**
 * This file overrides the MUI Button component to fix the "Cannot read properties of undefined (reading 'dark')" error.
 * It's a direct replacement for the MUI Button component.
 */

import * as React from 'react';
import PropTypes from 'prop-types';
import { Button as <PERSON><PERSON><PERSON>utton } from '@mui/material';

/**
 * Safe Button component that ensures theme properties exist
 */
const Button = React.forwardRef(function Button(props, ref) {
  // Create a safe version of the props
  const safeProps = { ...props };
  
  // Ensure the theme exists
  if (!safeProps.theme) {
    safeProps.theme = {
      palette: {
        primary: {
          main: '#3AA99F',
          light: '#4FBEB4',
          dark: '#2A8A82',
          contrastText: '#ffffff'
        },
        secondary: {
          main: '#38B2AC',
          light: '#4FD1CB',
          dark: '#2C8A84',
          contrastText: '#ffffff'
        }
      }
    };
  } else if (safeProps.theme.palette) {
    // Ensure primary palette exists
    if (!safeProps.theme.palette.primary) {
      safeProps.theme.palette.primary = {
        main: '#3AA99F',
        light: '#4FBEB4',
        dark: '#2A8A82',
        contrastText: '#ffffff'
      };
    } else {
      // Ensure primary.dark exists
      if (!safeProps.theme.palette.primary.dark) {
        safeProps.theme.palette.primary.dark = safeProps.theme.palette.primary.main || '#2A8A82';
      }
    }
    
    // Ensure secondary palette exists
    if (!safeProps.theme.palette.secondary) {
      safeProps.theme.palette.secondary = {
        main: '#38B2AC',
        light: '#4FD1CB',
        dark: '#2C8A84',
        contrastText: '#ffffff'
      };
    } else {
      // Ensure secondary.dark exists
      if (!safeProps.theme.palette.secondary.dark) {
        safeProps.theme.palette.secondary.dark = safeProps.theme.palette.secondary.main || '#2C8A84';
      }
    }
  }
  
  // Use the original MUI Button with the safe props
  return <MuiButton {...safeProps} ref={ref} />;
});

Button.propTypes = {
  ...MuiButton.propTypes
};

export default Button;
