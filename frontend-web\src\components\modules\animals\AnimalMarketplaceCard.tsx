import React from 'react';
import CustomButton from '../../common/CustomButton';
import { Card, CardContent, Typography, Box, Chip, useTheme, alpha, Rating } from '@mui/material';
import { motion } from 'framer-motion';
import { ShoppingCart, Visibility, Store } from '@mui/icons-material';
import { Animal } from '../../../types/animal';

interface AnimalMarketplaceCardProps {
  animal: Animal;
  onView?: (animal: Animal) => void;
  onAction?: (animal: Animal) => void;
  delay?: number;
}

/**
 * A marketplace-style card for displaying animals with blended image backgrounds
 */
const AnimalMarketplaceCard: React.FC<AnimalMarketplaceCardProps> = ({
  animal,
  onView,
  onAction,
  delay = 0
}) => {
  const theme = useTheme();

  // Helper function to get animal image based on species
  const getAnimalImage = () => {
    // If animal has an imageUrl, use it
    if (animal.imageUrl) {
      return animal.imageUrl;
    }

    // Otherwise, use a default image based on species
    const species = animal.species?.toLowerCase() || animal.type?.toLowerCase() || 'cattle';

    // Use the manually added images from UXUI LiveStock folder
    switch (species) {
      case 'cattle':
        return '/images/modules/animals/cattle-1.jpeg'; // Cows 1.jpeg
      case 'sheep':
        return '/images/modules/animals/cattle-2.avif'; // cows 2.avif
      case 'goat':
        return '/images/modules/animals/cattle-3.jpeg'; // cows 3.jpeg
      case 'game':
        return '/images/modules/animals/cattle-4.jpeg'; // cows 4.jpeg
      case 'horse':
        return '/images/modules/animals/cattle-5.avif'; // cows 5.avif
      default:
        return '/images/modules/animals/cattle-1.jpeg';
    }
  };

  // Calculate a mock price based on animal weight
  const calculatePrice = () => {
    const basePrice = animal.species === 'cattle' ? 15000 :
                     animal.species === 'sheep' ? 3500 :
                     animal.species === 'goat' ? 4200 :
                     animal.species === 'game' ? 25000 : 10000;

    // Add weight factor if available
    if (animal.weight) {
      return basePrice + (animal.weight * 50);
    }

    return basePrice;
  };

  // Generate a mock rating between 4.0 and 5.0
  const generateRating = () => {
    // Use animal ID to generate a consistent rating
    if (!animal.id) {
      // Return a default rating if ID is undefined
      return 4.5;
    }
    const hash = animal.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return 4.0 + ((hash % 10) / 10);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      style={{ height: '100%' }}
    >
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }}
      >
        {/* Image with overlay and price tag */}
        <Box sx={{ position: 'relative' }}>
          <Box
            sx={{
              height: 180,
              backgroundImage: `url(${getAnimalImage()})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.65)}, ${alpha(theme.palette.primary.dark, 0.7)})`,
                zIndex: 1
              }
            }}
          />

          {/* Price tag */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 10,
              right: 10,
              bgcolor: alpha(theme.palette.primary.main, 0.9),
              color: 'white',
              px: 1.5,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 'bold',
              zIndex: 2
            }}
          >
            R{calculatePrice().toLocaleString()}
          </Box>

          {/* Category tag */}
          <Chip
            label={animal.species || animal.type || 'Cattle'}
            size="small"
            sx={{
              position: 'absolute',
              top: 10,
              left: 10,
              bgcolor: alpha(theme.palette.background.paper, 0.8),
              zIndex: 2
            }}
          />

          {/* Tag number */}
          <Box
            sx={{
              position: 'absolute',
              top: 10,
              right: 10,
              bgcolor: alpha(theme.palette.primary.main, 0.9),
              color: 'white',
              px: 1.5,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 'bold',
              zIndex: 2
            }}
          >
            {animal.tagNumber}
          </Box>
        </Box>

        {/* Content */}
        <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h6" component="h2" gutterBottom>
            {animal.name || `${animal.species} ${animal.tagNumber}`}
          </Typography>

          <Typography variant="body2" color="text.secondary" gutterBottom>
            {animal.breed || 'Mixed Breed'} • {animal.gender || 'Unknown'} • {animal.weight ? `${animal.weight}kg` : 'Weight unknown'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Store sx={{ fontSize: 16, color: 'text.secondary', mr: 0.5 }} />
            <Typography variant="body2" color="text.secondary">
              {animal.location || 'Main Farm'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Rating value={generateRating()} precision={0.1} size="small" readOnly />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
              {generateRating().toFixed(1)}
            </Typography>
          </Box>

          <Chip
            label={animal.healthStatus || 'Healthy'}
            size="small"
            color={(animal.healthStatus === 'healthy' || !animal.healthStatus) ? 'success' : 'warning'}
            sx={{ mt: 2, alignSelf: 'flex-start' }}
          />

          {/* Action buttons */}
          <Box sx={{ mt: 'auto', pt: 2, display: 'flex', gap: 1 }}>
            <CustomButton
              size="small"
              startIcon={<Visibility />}
              onClick={() => onView && onView(animal)}
              sx={{ mr: 1 }}
            >
              View
            </CustomButton>

            <CustomButton
              size="small"
              variant="contained"
              startIcon={<ShoppingCart />}
              onClick={() => onAction && onAction(animal)}
              sx={{ flexGrow: 1 }}
            >
              Select
            </CustomButton>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimalMarketplaceCard;
