import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Grid, Typography, useTheme, alpha, Paper, Divider, Tab, Tabs, CircularProgress, Tooltip, IconButton } from '@mui/material';
import {
  MonetizationOn,
  TrendingUp,
  TrendingDown,
  AccountBalance,
  Receipt,
  AttachMoney,
  BarChart,
  PieChart,
  ShowChart,
  DateRange,
  Download,
  Print,
  Share,
  Info
} from '../../utils/iconImports';
import { 
  SubModuleDashboard,
  SubModuleMetricsCard,
  SubModuleActionPanel,
  SubModuleDataTable,
  SubModuleChart,
  withSubModuleTranslation
, CustomButton } from '../../components/common';
import { mockFinancialStats } from '../../mocks/financialData';
import { useFetchData, generateReport } from '../../utils/dataIntegration';
import {
  createGlassMorphism,
  createGradientBackground,
  createFloatingEffect,
  createGradientText,
  createGlowEffect
} from '../../utils/designEnhancements';

interface FinancialOverviewProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const FinancialOverview: React.FC<FinancialOverviewProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('month');
  const [isLoading, setIsLoading] = useState(false);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Define metrics for the dashboard
  const metrics = [
    {
      label: "Total Revenue",
      value: formatCurrency(mockFinancialStats.totalRevenue),
      icon: <MonetizationOn />,
      color: theme.palette.success.main,
      trend: {
        value: mockFinancialStats.revenueGrowth,
        isPositive: mockFinancialStats.revenueGrowth > 0,
        label: "since last month"
      }
    },
    {
      label: "Total Expenses",
      value: formatCurrency(mockFinancialStats.totalExpenses),
      icon: <TrendingDown />,
      color: theme.palette.error.main,
      trend: {
        value: 3.2,
        isPositive: false,
        label: "since last month"
      }
    },
    {
      label: "Net Profit",
      value: formatCurrency(mockFinancialStats.totalRevenue - mockFinancialStats.totalExpenses),
      icon: <TrendingUp />,
      color: theme.palette.primary.main,
      trend: {
        value: 7.5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: "Cash on Hand",
      value: formatCurrency(mockFinancialStats.cashOnHand),
      icon: <AccountBalance />,
      color: theme.palette.info.main,
      trend: {
        value: 5.3,
        isPositive: true,
        label: "since last month"
      }
    }
  ];

  // Define actions for the dashboard
  const actions = [
    {
      label: "Add Transaction",
      icon: <Receipt />,
      onClick: () => navigate('/financial/transactions'),
      color: theme.palette.success.main,
      description: "Record income or expense"
    },
    {
      label: "View Reports",
      icon: <BarChart />,
      onClick: () => navigate('/financial/reports'),
      color: theme.palette.info.main,
      description: "Financial statements and analysis"
    },
    {
      label: "Manage Budget",
      icon: <AttachMoney />,
      onClick: () => navigate('/financial/budgets'),
      color: theme.palette.warning.main,
      description: "Set and track budgets"
    },
    {
      label: "ROI Analysis",
      icon: <ShowChart />,
      onClick: () => navigate('/financial/roi'),
      color: theme.palette.secondary.main,
      description: "Investment performance"
    }
  ];

  // Define monthly performance chart data
  const monthlyPerformanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: [
      {
        label: 'Revenue',
        data: [42000, 49000, 53000, 57000, 62000, 67000, 72000, 78000, 82000, 86000, 91000, 95000],
        backgroundColor: theme.palette.success.main,
        borderColor: theme.palette.success.main
      },
      {
        label: 'Expenses',
        data: [35000, 38000, 40000, 45000, 48000, 51000, 55000, 59000, 63000, 67000, 70000, 74000],
        backgroundColor: theme.palette.error.main,
        borderColor: theme.palette.error.main
      },
      {
        label: 'Profit',
        data: [7000, 11000, 13000, 12000, 14000, 16000, 17000, 19000, 19000, 19000, 21000, 21000],
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.main
      }
    ]
  };

  // Define expense breakdown chart data
  const expenseBreakdownData = {
    labels: ['Feed', 'Labor', 'Veterinary', 'Equipment', 'Utilities', 'Marketing', 'Other'],
    datasets: [
      {
        label: 'Expenses by Category',
        data: [35, 25, 15, 10, 8, 5, 2],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.error.main,
          theme.palette.warning.main,
          theme.palette.info.main,
          theme.palette.success.main,
          alpha(theme.palette.text.primary, 0.5)
        ]
      }
    ]
  };

  // Define revenue sources chart data
  const revenueSourcesData = {
    labels: ['Livestock Sales', 'Milk Production', 'Crop Sales', 'Breeding Services', 'Other'],
    datasets: [
      {
        label: 'Revenue by Source',
        data: [45, 30, 15, 7, 3],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.info.main,
          alpha(theme.palette.text.primary, 0.5)
        ]
      }
    ]
  };

  // Define financial forecast chart data
  const financialForecastData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Projected Revenue',
        data: [95000, 98000, 102000, 105000, 110000, 115000],
        backgroundColor: alpha(theme.palette.success.main, 0.2),
        borderColor: theme.palette.success.main,
        fill: true
      },
      {
        label: 'Projected Expenses',
        data: [74000, 76000, 78000, 80000, 82000, 85000],
        backgroundColor: alpha(theme.palette.error.main, 0.2),
        borderColor: theme.palette.error.main,
        fill: true
      }
    ]
  };

  // Define recent transactions data
  const recentTransactionsColumns = [
    { id: 'date', label: 'Date', minWidth: 100 },
    { id: 'description', label: 'Description', minWidth: 200 },
    { id: 'type', label: 'Type', minWidth: 100,
      format: (value: string) => (
        <Typography
          variant="body2"
          color={value === 'income' ? 'success.main' : 'error.main'}
          fontWeight="medium"
        >
          {value.charAt(0).toUpperCase() + value.slice(1)}
        </Typography>
      )
    },
    { id: 'category', label: 'Category', minWidth: 120 },
    { id: 'amount', label: 'Amount', minWidth: 120, align: 'right',
      format: (value: number) => (
        <Typography
          variant="body2"
          fontWeight="medium"
        >
          {formatCurrency(value)}
        </Typography>
      )
    }
  ];

  const recentTransactionsData = [
    { id: '1', date: '2023-12-15', description: 'Livestock Sale - Cattle', type: 'income', category: 'Sales', amount: 25000 },
    { id: '2', date: '2023-12-12', description: 'Feed Purchase', type: 'expense', category: 'Feed', amount: 8500 },
    { id: '3', date: '2023-12-10', description: 'Veterinary Services', type: 'expense', category: 'Veterinary', amount: 3200 },
    { id: '4', date: '2023-12-05', description: 'Milk Sales', type: 'income', category: 'Sales', amount: 12000 },
    { id: '5', date: '2023-12-01', description: 'Equipment Maintenance', type: 'expense', category: 'Equipment', amount: 1800 }
  ];

  // Define tabs for the dashboard
  const dashboardTabs = [
    { label: "Overview", icon: <MonetizationOn /> },
    { label: "Income", icon: <TrendingUp /> },
    { label: "Expenses", icon: <TrendingDown /> },
    { label: "Forecast", icon: <ShowChart /> }
  ];

  return (
    <SubModuleDashboard
      title="Financial Overview"
      subtitle="Monitor financial performance, revenue, expenses, and profitability metrics"
      parentModule="financial"
      subModule="overview"
      icon={<MonetizationOn />}
      metrics={metrics}
      actions={actions}
      tabs={dashboardTabs}
      activeTab={0}
      onTabChange={(index) => console.log(`Tab changed to ${index}`)}
      isLoading={false}
      onRefresh={() => console.log("Refreshing financial data")}
      onBack={() => navigate('/financial')}
    >
      {/* Time Range Selector */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'flex-end' }}>
        <Paper
          sx={{
            p: 0.5,
            borderRadius: 2,
            ...createGlassMorphism(theme, theme.palette.primary.main, 1)
          }}
        >
          <Tabs
            value={timeRange}
            onChange={(e, newValue) => setTimeRange(newValue)}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              minHeight: 'unset',
              '& .MuiTab-root': {
                minHeight: 'unset',
                py: 0.5,
                px: 2,
                minWidth: 'unset',
                borderRadius: 1,
                fontSize: '0.875rem',
                fontWeight: 'medium',
                textTransform: 'none',
                transition: 'all 0.3s ease',
                '&.Mui-selected': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  transform: 'translateY(-2px)',
                  boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}`
                },
                '&:hover:not(.Mui-selected)': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                }
              }
            }}
          >
            <Tab
              label={translateSubModule ? translateSubModule('week', 'Week') : 'Week'}
              value="week"
              icon={<DateRange sx={{ fontSize: 16 }} />}
              iconPosition="start"
            />
            <Tab
              label={translateSubModule ? translateSubModule('month', 'Month') : 'Month'}
              value="month"
              icon={<DateRange sx={{ fontSize: 16 }} />}
              iconPosition="start"
            />
            <Tab
              label={translateSubModule ? translateSubModule('quarter', 'Quarter') : 'Quarter'}
              value="quarter"
              icon={<DateRange sx={{ fontSize: 16 }} />}
              iconPosition="start"
            />
            <Tab
              label={translateSubModule ? translateSubModule('year', 'Year') : 'Year'}
              value="year"
              icon={<DateRange sx={{ fontSize: 16 }} />}
              iconPosition="start"
            />
          </Tabs>
        </Paper>
      </Box>

      {/* Monthly Performance Chart */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <SubModuleChart
            title="Monthly Financial Performance"
            description="Track revenue, expenses, and profit over time"
            type="line"
            data={monthlyPerformanceData}
            height={400}
            moduleColor={theme.palette.primary.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
      </Grid>

      {/* Financial Distribution Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <SubModuleChart
            title="Expense Breakdown"
            description="Distribution of expenses by category"
            type="pie"
            data={expenseBreakdownData}
            height={350}
            moduleColor={theme.palette.error.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <SubModuleChart
            title="Revenue Sources"
            description="Distribution of revenue by source"
            type="pie"
            data={revenueSourcesData}
            height={350}
            moduleColor={theme.palette.success.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
      </Grid>

      {/* Financial Forecast */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <SubModuleChart
            title="Financial Forecast"
            description="Projected revenue and expenses for the next 6 months"
            type="area"
            data={financialForecastData}
            height={350}
            moduleColor={theme.palette.info.main}
            onExport={() => console.log("Exporting chart")}
            onPrint={() => console.log("Printing chart")}
          />
        </Grid>
      </Grid>

      {/* Recent Transactions */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <SubModuleDataTable
            title="Recent Transactions"
            columns={recentTransactionsColumns}
            data={recentTransactionsData}
            onView={(row) => console.log("View transaction", row)}
            onEdit={(row) => console.log("Edit transaction", row)}
            onDelete={(row) => console.log("Delete transaction", row)}
            onExport={() => console.log("Export transactions")}
            onPrint={() => console.log("Print transactions")}
            moduleColor={theme.palette.primary.main}
            emptyStateMessage="No recent transactions found"
          />
        </Grid>
      </Grid>

      {/* Financial Health Indicators */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper
            elevation={1}
            sx={{
              p: 3,
              borderRadius: 2,
              ...createGlassMorphism(theme, theme.palette.primary.main, 2),
              background: createGradientBackground(theme, theme.palette.primary.main, 0.03)
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
              <Typography
                variant="h6"
                fontWeight="bold"
                sx={{
                  ...createGradientText(theme.palette.primary.main, theme.palette.secondary.main)
                }}
              >
                {translateSubModule ? translateSubModule('financial_health_indicators', 'Financial Health Indicators') : 'Financial Health Indicators'}
              </Typography>
              <Tooltip title={translateSubModule ? translateSubModule('health_indicators_info', 'Key metrics that indicate the financial health of your operation') : 'Key metrics that indicate the financial health of your operation'}>
                <IconButton size="small">
                  <Info fontSize="small" color="primary" />
                </IconButton>
              </Tooltip>
            </Box>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    ...createFloatingEffect(theme, theme.palette.success.main),
                    background: createGradientBackground(theme, theme.palette.success.main, 0.05)
                  }}
                >
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {translateSubModule ? translateSubModule('profit_margin', 'Profit Margin') : 'Profit Margin'}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color={theme.palette.success.main}>
                    22.1%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {translateSubModule ? translateSubModule('healthy_status', 'Healthy (>20%)') : 'Healthy (>20%)'}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    ...createFloatingEffect(theme, theme.palette.warning.main),
                    background: createGradientBackground(theme, theme.palette.warning.main, 0.05)
                  }}
                >
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {translateSubModule ? translateSubModule('debt_to_asset_ratio', 'Debt-to-Asset Ratio') : 'Debt-to-Asset Ratio'}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color={theme.palette.warning.main}>
                    0.35
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {translateSubModule ? translateSubModule('good_status', 'Good (<0.4)') : 'Good (<0.4)'}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    ...createFloatingEffect(theme, theme.palette.info.main),
                    background: createGradientBackground(theme, theme.palette.info.main, 0.05)
                  }}
                >
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {translateSubModule ? translateSubModule('current_ratio', 'Current Ratio') : 'Current Ratio'}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color={theme.palette.info.main}>
                    1.8
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {translateSubModule ? translateSubModule('good_status_ratio', 'Good (>1.5)') : 'Good (>1.5)'}
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Box
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    ...createFloatingEffect(theme, theme.palette.primary.main),
                    background: createGradientBackground(theme, theme.palette.primary.main, 0.05)
                  }}
                >
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {translateSubModule ? translateSubModule('return_on_assets', 'Return on Assets') : 'Return on Assets'}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color={theme.palette.primary.main}>
                    8.5%
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {translateSubModule ? translateSubModule('average_status', 'Average (5-10%)') : 'Average (5-10%)'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <CustomButton
                variant="contained"
                color="primary"
                onClick={() => navigate('/financial/reports')}
                startIcon={<BarChart />}
                sx={{
                  ...createGlowEffect(theme.palette.primary.main, 0.3),
                  px: 3,
                  py: 1,
                  borderRadius: 2
                }}
              >
                {translateSubModule ? translateSubModule('view_detailed_reports', 'View Detailed Reports') : 'View Detailed Reports'}
              </CustomButton>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </SubModuleDashboard>
  );
};

export default withSubModuleTranslation(FinancialOverview, 'financial', 'overview');
