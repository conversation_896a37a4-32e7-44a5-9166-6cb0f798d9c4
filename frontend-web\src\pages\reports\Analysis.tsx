import React, { useState } from 'react';
import { Box, Typography, Grid, Tabs, Tab, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, FormControl, InputLabel, Select, MenuItem, TextField, IconButton, Divider } from '@mui/material';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import {
  Download,
  FilterList,
  Assessment,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  BarChart as BarChartIcon,
  Timeline,
  Print,
  Share,
  Add,
  Edit,
  Delete,
  Visibility
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { mockReports } from '../../mocks/reportsData';
import { formatCurrency } from '../../utils/formatters';
import { generateReport, generateReportFromAPI } from '../../utils/reportUtils';

// Define interface for report data
interface Report {
  id: string;
  title: string;
  type: 'livestock' | 'financial' | 'health' | 'breeding' | 'feed' | 'compliance' | 'inventory' | 'commercial';
  date: Date;
  status: 'generated' | 'pending' | 'failed';
  size: string;
  author: string;
  description: string;
  downloadUrl?: string;
  thumbnail?: string;
  tags?: string[];
  viewCount?: number;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Analysis: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [reportFormat, setReportFormat] = useState('pdf');
  const [timeRange, setTimeRange] = useState('month');
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  // Filter reports to show only 7 per category
  const limitedReports = mockReports.slice(0, 7);

  // Filter reports by tab/type
  const getFilteredReports = () => {
    const reportTypes = [
      'all', 'livestock', 'health', 'breeding',
      'feed', 'financial', 'compliance', 'commercial'
    ];

    const selectedType = reportTypes[tabValue];

    return limitedReports.filter(report =>
      (selectedType === 'all' || report.type === selectedType) &&
      (searchTerm === '' ||
        report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        report.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const [isGenerating, setIsGenerating] = useState(false);

  // We don't need to load saved reports anymore since we're using the new utility

  const handleGenerateReport = async (reportName: string) => {
    try {
      setIsGenerating(true);

      // Get the appropriate data based on the tab value
      let reportData: any[] = [];
      let reportType = '';

      switch (tabValue) {
        case 0: // All Reports
          reportData = mockReports;
          reportType = 'all-reports';
          break;
        case 1: // Livestock
          reportData = mockReports.filter(r => r.type === 'livestock');
          reportType = 'livestock';
          break;
        case 2: // Health
          reportData = healthData;
          reportType = 'health';
          break;
        case 3: // Breeding
          reportData = breedingData;
          reportType = 'breeding';
          break;
        case 4: // Feeding
          reportData = feedData;
          reportType = 'feeding';
          break;
        case 5: // Financial
          reportData = revenueData;
          reportType = 'financial';
          break;
        default:
          reportData = mockReports;
          reportType = 'all-reports';
      }

      // Generate the report using our utility function
      generateReport(
        reportData,
        `${reportType}_analysis_report`,
        reportFormat as 'csv' | 'excel' | 'pdf',
        reportName
      );

    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Mock data for charts
  const revenueData = [
    { month: 'Jan', revenue: 125000, expenses: 95000, profit: 30000 },
    { month: 'Feb', revenue: 140000, expenses: 100000, profit: 40000 },
    { month: 'Mar', revenue: 155000, expenses: 110000, profit: 45000 },
    { month: 'Apr', revenue: 170000, expenses: 115000, profit: 55000 },
    { month: 'May', revenue: 190000, expenses: 125000, profit: 65000 },
    { month: 'Jun', revenue: 210000, expenses: 140000, profit: 70000 },
    { month: 'Jul', revenue: 230000, expenses: 150000, profit: 80000 }
  ];

  const livestockDistribution = [
    { name: 'Cattle', value: 65 },
    { name: 'Sheep', value: 20 },
    { name: 'Goats', value: 10 },
    { name: 'Other', value: 5 }
  ];

  const healthData = [
    { month: 'Jan', vaccinations: 45, treatments: 12, checkups: 30 },
    { month: 'Feb', vaccinations: 38, treatments: 15, checkups: 25 },
    { month: 'Mar', vaccinations: 52, treatments: 8, checkups: 35 },
    { month: 'Apr', vaccinations: 40, treatments: 10, checkups: 28 },
    { month: 'May', vaccinations: 35, treatments: 14, checkups: 32 },
    { month: 'Jun', vaccinations: 48, treatments: 9, checkups: 38 },
    { month: 'Jul', vaccinations: 42, treatments: 11, checkups: 30 }
  ];

  const breedingData = [
    { month: 'Jan', successful: 12, unsuccessful: 3 },
    { month: 'Feb', successful: 15, unsuccessful: 2 },
    { month: 'Mar', successful: 10, unsuccessful: 4 },
    { month: 'Apr', successful: 14, unsuccessful: 1 },
    { month: 'May', successful: 16, unsuccessful: 2 },
    { month: 'Jun', successful: 13, unsuccessful: 3 },
    { month: 'Jul', successful: 18, unsuccessful: 2 }
  ];

  const feedData = [
    { month: 'Jan', hay: 2500, grain: 1200, supplements: 800 },
    { month: 'Feb', hay: 2300, grain: 1100, supplements: 750 },
    { month: 'Mar', hay: 2700, grain: 1300, supplements: 850 },
    { month: 'Apr', hay: 2400, grain: 1150, supplements: 780 },
    { month: 'May', hay: 2600, grain: 1250, supplements: 820 },
    { month: 'Jun', hay: 2800, grain: 1350, supplements: 870 },
    { month: 'Jul', hay: 2550, grain: 1220, supplements: 810 }
  ];

  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main
  ];

  return (
    <Box>
      <ModuleHeader
        title="Data Analysis Center"
        subtitle="Advanced analytics and insights for all farm operations"
        module="reports"
        submodule="analysis"
        actionLabel={isGenerating ? "Generating..." : "Export Analysis Report"}
        actionIcon={<Download />}
        onAction={() => handleGenerateReport('Comprehensive Analysis Report')}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* AI-Powered Insights */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Paper
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  mr: 2
                }}
              >
                <Assessment />
              </Box>
              <Typography variant="h5" fontWeight="bold">
                AI-Powered Farm Insights
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="primary.main">
                    Livestock Performance Trend
                  </Typography>
                  <Typography variant="body2" paragraph>
                    Your cattle weight gain is <strong>12% above</strong> the regional average. Consider maintaining your current feed mix which is showing excellent results.
                  </Typography>
                  <CustomButton variant="outlined" size="small" sx={{ mt: 1 }}>
                    View Detailed Analysis
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="warning.main">
                    Health Alert
                  </Typography>
                  <Typography variant="body2" paragraph>
                    Detected a <strong>27% increase</strong> in respiratory issues in the northern paddock. Consider veterinary inspection within the next 7 days.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="warning" sx={{ mt: 1 }}>
                    Schedule Inspection
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="success.main">
                    Market Opportunity
                  </Typography>
                  <Typography variant="body2" paragraph>
                    Beef prices projected to rise <strong>8.5% in the next 30 days</strong>. Consider delaying sales of your ready-to-market cattle for maximum profit.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="success" sx={{ mt: 1 }}>
                    View Price Forecast
                  </CustomButton>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </motion.div>
        {/* Report Filters */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={9}>
              <Paper sx={{ p: 2, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Time Range</InputLabel>
                    <Select
                      value={timeRange}
                      label="Time Range"
                      onChange={(e) => setTimeRange(e.target.value)}
                    >
                      <MenuItem value="week">Last Week</MenuItem>
                      <MenuItem value="month">Last Month</MenuItem>
                      <MenuItem value="quarter">Last Quarter</MenuItem>
                      <MenuItem value="year">Last Year</MenuItem>
                      <MenuItem value="custom">Custom Range</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Report Format</InputLabel>
                    <Select
                      value={reportFormat}
                      label="Report Format"
                      onChange={(e) => setReportFormat(e.target.value)}
                    >
                      <MenuItem value="pdf">PDF</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="csv">CSV</MenuItem>
                    </Select>
                  </FormControl>

                  <TextField
                    size="small"
                    label="Search Reports"
                    variant="outlined"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{ flexGrow: 1 }}
                  />

                  <CustomButton
                    variant="contained"
                    startIcon={<FilterList />}
                    sx={{ bgcolor: theme.palette.primary.main }}
                  >
                    Filter
                  </CustomButton>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Box display="flex" gap={2}>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Print />}
                    sx={{ borderColor: theme.palette.primary.main, color: theme.palette.primary.main }}
                  >
                    Print
                  </CustomButton>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Share />}
                    sx={{ borderColor: theme.palette.primary.main, color: theme.palette.primary.main }}
                  >
                    Share
                  </CustomButton>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </motion.div>

        {/* Report Categories Tabs */}
        <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.05),
              '& .MuiTab-root': {
                minWidth: 120,
                py: 2
              },
              '& .Mui-selected': {
                color: `${theme.palette.primary.main} !important`,
                fontWeight: 'bold'
              },
              '& .MuiTabs-indicator': {
                backgroundColor: theme.palette.primary.main,
                height: 3
              }
            }}
          >
            <Tab label="All Reports" />
            <Tab label="Livestock" />
            <Tab label="Health" />
            <Tab label="Breeding" />
            <Tab label="Feeding" />
            <Tab label="Financial" />
            <Tab label="Compliance" />
            <Tab label="Commercial" />
          </Tabs>
        </Paper>

        {/* Data Visualization Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h5" fontWeight="bold" mb={3}>
            Key Insights
          </Typography>

          <Grid container spacing={3} mb={4}>
            {/* Financial Chart */}
            <Grid item xs={12} md={8}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Financial Performance"
                  backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
                  icon={<Assessment />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={revenueData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `R${value / 1000}k`} />
                        <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                        <Legend />
                        <Bar dataKey="revenue" name="Revenue" fill={theme.palette.primary.main} />
                        <Bar dataKey="expenses" name="Expenses" fill={theme.palette.error.main} />
                        <Bar dataKey="profit" name="Profit" fill={theme.palette.success.main} />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            {/* Livestock Distribution */}
            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Livestock Distribution"
                  backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg"
                  icon={<PieChartIcon />}
                  accentColor={theme.palette.secondary.main}
                  secondaryColor={theme.palette.secondary.dark}
                  delay={0.2}
                  height="100%"
                >
                  <Box mt={2} height={300} display="flex" justifyContent="center">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={livestockDistribution}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {livestockDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `${value}%`} />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            {/* Health Statistics */}
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Health Statistics"
                  backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
                  icon={<Timeline />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  delay={0.3}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={healthData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="vaccinations" name="Vaccinations" stroke={theme.palette.primary.main} activeDot={{ r: 8 }} />
                        <Line type="monotone" dataKey="treatments" name="Treatments" stroke={theme.palette.error.main} />
                        <Line type="monotone" dataKey="checkups" name="Check-ups" stroke={theme.palette.info.main} />
                      </LineChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            {/* Breeding Success */}
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Breeding Success Rate"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  delay={0.4}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={breedingData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area type="monotone" dataKey="successful" name="Successful Breeding" stroke={theme.palette.success.main} fill={alpha(theme.palette.success.main, 0.6)} />
                        <Area type="monotone" dataKey="unsuccessful" name="Unsuccessful Breeding" stroke={theme.palette.error.main} fill={alpha(theme.palette.error.main, 0.6)} />
                      </AreaChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>

            {/* Feed Consumption */}
            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Feed Consumption"
                  backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2020/02/farm-finances.jpg"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  delay={0.5}
                  height="100%"
                >
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={feedData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis tickFormatter={(value) => `${value}kg`} />
                        <Tooltip formatter={(value) => `${value}kg`} />
                        <Legend />
                        <Bar dataKey="hay" name="Hay" fill={theme.palette.success.light} />
                        <Bar dataKey="grain" name="Grain" fill={theme.palette.warning.light} />
                        <Bar dataKey="supplements" name="Supplements" fill={theme.palette.info.light} />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </AnimatedBackgroundCard>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Reports List */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" fontWeight="bold">
              Available Reports
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => setOpenDialog(true)}
            >
              Generate New Report
            </CustomButton>
          </Box>

          <TableContainer component={Paper} sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', overflow: 'hidden' }}>
            <Table>
              <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                <TableRow>
                  <TableCell>Report Title</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Date Generated</TableCell>
                  <TableCell>Size</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getFilteredReports().map((report) => {
                  let statusColor = 'default';
                  if (report.status === 'generated') statusColor = 'success';
                  if (report.status === 'pending') statusColor = 'warning';
                  if (report.status === 'failed') statusColor = 'error';

                  return (
                    <TableRow key={report.id} hover>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Typography variant="body2" fontWeight="medium">
                            {report.title}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={report.type.charAt(0).toUpperCase() + report.type.slice(1)}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{report.date.toLocaleDateString()}</TableCell>
                      <TableCell>{report.size}</TableCell>
                      <TableCell>
                        <Chip
                          label={report.status.charAt(0).toUpperCase() + report.status.slice(1)}
                          size="small"
                          color={statusColor as any}
                        />
                      </TableCell>
                      <TableCell>
                        <Box display="flex">
                          <IconButton size="small" color="primary">
                            <Visibility fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="primary">
                            <Download fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="primary">
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error">
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </motion.div>
      </Box>
    </Box>
  );
};

export default Analysis;
