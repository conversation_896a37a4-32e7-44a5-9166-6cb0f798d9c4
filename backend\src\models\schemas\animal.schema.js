/**
 * Animal Schema
 * 
 * Defines the MongoDB schema for the animals collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const animalSchema = new Schema({
  tagNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    trim: true
  },
  species: {
    type: String,
    required: true,
    trim: true
  },
  breed: {
    type: String,
    trim: true
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'unknown'],
    default: 'unknown'
  },
  birthDate: {
    type: Date
  },
  acquisitionDate: {
    type: Date
  },
  weight: {
    type: Number,
    min: 0
  },
  status: {
    type: String,
    enum: ['active', 'sold', 'deceased', 'retired', 'other'],
    default: 'active'
  },
  healthStatus: {
    type: String,
    enum: ['healthy', 'sick', 'injured', 'recovering', 'quarantined', 'unknown'],
    default: 'unknown'
  },
  location: {
    type: String,
    trim: true
  },
  // Comprehensive Identification
  identification: {
    rfidTag: {
      type: String,
      trim: true,
      unique: true,
      sparse: true
    },
    microchipId: {
      type: String,
      trim: true,
      unique: true,
      sparse: true
    },
    earTagLeft: String,
    earTagRight: String,
    tattooId: String,
    brandMark: String,
    registrationNumber: String,
    passportNumber: String,
    nationalId: String
  },

  // Physical Characteristics
  physicalCharacteristics: {
    height: {
      value: Number,
      unit: { type: String, default: 'cm' }
    },
    length: {
      value: Number,
      unit: { type: String, default: 'cm' }
    },
    chestGirth: {
      value: Number,
      unit: { type: String, default: 'cm' }
    },
    colorMarkings: {
      primaryColor: String,
      secondaryColor: String,
      markings: [String],
      distinguishingFeatures: [String]
    },
    bodyConditionScore: {
      type: Number,
      min: 1,
      max: 9
    }
  },
  notes: {
    type: String
  },
  // Enhanced Genealogy Information
  genealogy: {
    sire: {
      id: { type: Schema.Types.ObjectId, ref: 'Animal' },
      tagNumber: String,
      name: String,
      breed: String,
      lineage: String,
      registrationNumber: String
    },
    dam: {
      id: { type: Schema.Types.ObjectId, ref: 'Animal' },
      tagNumber: String,
      name: String,
      breed: String,
      lineage: String,
      registrationNumber: String
    },
    grandparents: {
      paternalGrandfather: {
        id: { type: Schema.Types.ObjectId, ref: 'Animal' },
        tagNumber: String,
        name: String
      },
      paternalGrandmother: {
        id: { type: Schema.Types.ObjectId, ref: 'Animal' },
        tagNumber: String,
        name: String
      },
      maternalGrandfather: {
        id: { type: Schema.Types.ObjectId, ref: 'Animal' },
        tagNumber: String,
        name: String
      },
      maternalGrandmother: {
        id: { type: Schema.Types.ObjectId, ref: 'Animal' },
        tagNumber: String,
        name: String
      }
    },
    breedingHistory: [{
      mateId: { type: Schema.Types.ObjectId, ref: 'Animal' },
      mateTagNumber: String,
      breedingDate: Date,
      pregnancyConfirmed: Boolean,
      expectedBirthDate: Date,
      actualBirthDate: Date,
      offspring: [{ type: Schema.Types.ObjectId, ref: 'Animal' }],
      notes: String
    }],
    geneticTraits: {
      bloodline: String,
      geneticMarkers: [String],
      heritableTraits: [String],
      breedingValue: Number,
      inbreedingCoefficient: Number
    }
  },
  // Purchase and Acquisition Details
  acquisition: {
    purchasePrice: {
      type: Number,
      min: 0
    },
    purchaseDate: Date,
    vendor: {
      name: String,
      contact: String,
      address: String,
      registrationNumber: String
    },
    transportDetails: {
      method: String,
      cost: Number,
      date: Date,
      documents: [String]
    },
    healthCertificates: [{
      type: String,
      issueDate: Date,
      expiryDate: Date,
      issuingAuthority: String,
      documentUrl: String
    }],
    quarantinePeriod: {
      required: Boolean,
      startDate: Date,
      endDate: Date,
      location: String,
      notes: String
    }
  },

  // Lifecycle Management
  lifecycle: {
    currentStage: {
      type: String,
      enum: ['calf', 'juvenile', 'adult', 'breeding', 'retirement', 'deceased'],
      default: 'calf'
    },
    stageHistory: [{
      stage: String,
      startDate: Date,
      endDate: Date,
      autoTransitioned: Boolean,
      notes: String
    }],
    expectedRetirementDate: Date,
    retirementCriteria: {
      maxAge: Number,
      maxBreedingCycles: Number,
      performanceThresholds: {
        minWeight: Number,
        minProductivity: Number
      }
    },
    transferHistory: [{
      fromLocation: String,
      toLocation: String,
      transferDate: Date,
      reason: String,
      authorizedBy: String
    }]
  },

  // Financial Tracking
  financial: {
    estimatedValue: {
      type: Number,
      min: 0
    },
    depreciationRate: Number,
    maintenanceCosts: [{
      date: Date,
      category: String,
      amount: Number,
      description: String
    }],
    revenueGenerated: [{
      date: Date,
      source: String,
      amount: Number,
      description: String
    }],
    roi: Number,
    insuranceDetails: {
      provider: String,
      policyNumber: String,
      coverage: Number,
      premium: Number,
      expiryDate: Date
    }
  },
  images: [{
    url: String,
    caption: String,
    isPrimary: Boolean,
    uploadDate: Date
  }],
  metadata: {
    type: Map,
    of: Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
animalSchema.index({ tagNumber: 1 });
animalSchema.index({ species: 1 });
animalSchema.index({ status: 1 });
animalSchema.index({ healthStatus: 1 });

// Virtual fields for calculated properties
animalSchema.virtual('age').get(function() {
  if (!this.birthDate) return null;

  const ageDifMs = Date.now() - this.birthDate.getTime();
  const ageDate = new Date(ageDifMs);
  return Math.abs(ageDate.getUTCFullYear() - 1970);
});

animalSchema.virtual('ageInMonths').get(function() {
  if (!this.birthDate) return null;

  const now = new Date();
  const birth = new Date(this.birthDate);
  const months = (now.getFullYear() - birth.getFullYear()) * 12 + (now.getMonth() - birth.getMonth());
  return months;
});

animalSchema.virtual('expectedLifecycleStage').get(function() {
  const ageInMonths = this.ageInMonths;
  if (!ageInMonths) return 'unknown';

  // Default lifecycle stages (can be customized per species)
  if (ageInMonths < 6) return 'calf';
  if (ageInMonths < 12) return 'juvenile';
  if (ageInMonths < 96) return 'adult'; // 8 years
  return 'retirement';
});

animalSchema.virtual('shouldTransferToInventory').get(function() {
  const age = this.age;
  const currentStage = this.lifecycle?.currentStage;

  // Check retirement criteria
  if (age && age >= 10) return { reason: 'retirement_age', priority: 'high' };
  if (currentStage === 'deceased') return { reason: 'deceased', priority: 'immediate' };
  if (this.status === 'sold') return { reason: 'sold', priority: 'high' };

  return null;
});

// Pre-save middleware for automated lifecycle management
animalSchema.pre('save', function(next) {
  this.updatedAt = Date.now();

  // Auto-update lifecycle stage based on age
  const expectedStage = this.expectedLifecycleStage;
  const currentStage = this.lifecycle?.currentStage;

  if (expectedStage && expectedStage !== currentStage && expectedStage !== 'unknown') {
    // Initialize lifecycle if not exists
    if (!this.lifecycle) {
      this.lifecycle = {
        currentStage: expectedStage,
        stageHistory: []
      };
    }

    // Record stage transition
    if (currentStage && currentStage !== expectedStage) {
      this.lifecycle.stageHistory.push({
        stage: currentStage,
        startDate: this.lifecycle.stageHistory.length > 0 ?
          this.lifecycle.stageHistory[this.lifecycle.stageHistory.length - 1].endDate :
          this.birthDate,
        endDate: new Date(),
        autoTransitioned: true,
        notes: `Auto-transitioned from ${currentStage} to ${expectedStage} based on age`
      });
    }

    // Update current stage
    this.lifecycle.currentStage = expectedStage;
  }

  // Auto-calculate expected retirement date
  if (this.birthDate && !this.lifecycle?.expectedRetirementDate) {
    const retirementAge = this.lifecycle?.retirementCriteria?.maxAge || 10; // Default 10 years
    const retirementDate = new Date(this.birthDate);
    retirementDate.setFullYear(retirementDate.getFullYear() + retirementAge);

    if (!this.lifecycle) this.lifecycle = {};
    this.lifecycle.expectedRetirementDate = retirementDate;
  }

  next();
});

// Static method for bulk lifecycle updates
animalSchema.statics.updateLifecycleStages = async function() {
  const animals = await this.find({
    'lifecycle.currentStage': { $in: ['calf', 'juvenile', 'adult'] }
  });

  const updates = [];
  for (const animal of animals) {
    const expectedStage = animal.expectedLifecycleStage;
    if (expectedStage !== animal.lifecycle?.currentStage) {
      updates.push({
        updateOne: {
          filter: { _id: animal._id },
          update: {
            $set: {
              'lifecycle.currentStage': expectedStage,
              updatedAt: new Date()
            },
            $push: {
              'lifecycle.stageHistory': {
                stage: animal.lifecycle?.currentStage,
                startDate: animal.lifecycle?.stageHistory?.length > 0 ?
                  animal.lifecycle.stageHistory[animal.lifecycle.stageHistory.length - 1].endDate :
                  animal.birthDate,
                endDate: new Date(),
                autoTransitioned: true,
                notes: `Bulk auto-transition to ${expectedStage}`
              }
            }
          }
        }
      });
    }
  }

  if (updates.length > 0) {
    return await this.bulkWrite(updates);
  }

  return { modifiedCount: 0 };
};

// Create the model (prevent overwrite error)
let Animal;
try {
  Animal = mongoose.model('Animal');
} catch (error) {
  Animal = mongoose.model('Animal', animalSchema);
}

module.exports = Animal;
