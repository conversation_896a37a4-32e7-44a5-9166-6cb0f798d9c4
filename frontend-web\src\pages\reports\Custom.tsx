import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, useTheme, Tabs, Tab, Divider, Stepper, Step, StepLabel, Checkbox, FormControlLabel, FormGroup, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CheckCircle,
  Warning,
  CalendarToday,
  FilterList,
  Search,
  ArrowForward,
  MoreVert,
  BarChart,
  PieChart,
  Timeline,
  Download,
  Print,
  Share,
  Save,
  Description,
  Settings,
  Visibility,
  VisibilityOff
} from '../../utils/iconImports';
import {  <PERSON><PERSON><PERSON>Header, AnimatedB<PERSON>ground<PERSON>ard , CustomButton } from '../../components/common';
import { generateReport, ReportParams, getSavedReports } from '../../services/reportService';
import { format } from 'date-fns';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Mock data for saved custom reports
const mockCustomReports = [
  {
    id: 'CR001',
    name: 'Monthly Profit Analysis',
    description: 'Financial performance breakdown by livestock category',
    lastGenerated: '2024-03-01',
    format: 'PDF',
    createdBy: 'Admin',
    modules: ['Financial', 'Animals'],
    shared: true
  },
  {
    id: 'CR002',
    name: 'Breeding Efficiency Report',
    description: 'Success rates and cost analysis for breeding program',
    lastGenerated: '2024-02-15',
    format: 'Excel',
    createdBy: 'Admin',
    modules: ['Breeding', 'Financial'],
    shared: false
  },
  {
    id: 'CR003',
    name: 'Health Intervention Impact',
    description: 'Analysis of health treatments and their effect on productivity',
    lastGenerated: '2024-02-28',
    format: 'PDF',
    createdBy: 'Admin',
    modules: ['Health', 'Animals'],
    shared: true
  },
  {
    id: 'CR004',
    name: 'Feed Efficiency Analysis',
    description: 'Detailed analysis of feed conversion ratios and costs',
    lastGenerated: '2024-03-10',
    format: 'Excel',
    createdBy: 'Admin',
    modules: ['Feeding', 'Financial'],
    shared: false
  },
  {
    id: 'CR005',
    name: 'Market Opportunity Forecast',
    description: 'Projected market prices and optimal selling periods',
    lastGenerated: '2024-03-05',
    format: 'PDF',
    createdBy: 'Admin',
    modules: ['Market', 'Financial'],
    shared: true
  }
];

// Available modules for report generation
const availableModules = [
  { id: 'animals', name: 'Animals', fields: ['ID', 'Breed', 'Age', 'Weight', 'Gender', 'Status', 'Location', 'Purchase Date', 'Purchase Price'] },
  { id: 'breeding', name: 'Breeding', fields: ['Breeding ID', 'Female ID', 'Male ID', 'Breeding Date', 'Method', 'Status', 'Expected Due Date', 'Notes'] },
  { id: 'health', name: 'Health', fields: ['Record ID', 'Animal ID', 'Record Type', 'Date', 'Diagnosis', 'Treatment', 'Medication', 'Cost', 'Veterinarian'] },
  { id: 'feeding', name: 'Feeding', fields: ['Feed ID', 'Feed Type', 'Quantity', 'Cost', 'Date', 'Animal Group', 'Notes'] },
  { id: 'financial', name: 'Financial', fields: ['Transaction ID', 'Type', 'Amount', 'Date', 'Category', 'Description', 'Payment Method'] },
  { id: 'market', name: 'Market', fields: ['Market Price', 'Category', 'Date', 'Location', 'Trend', 'Forecast'] }
];

// Available chart types
const chartTypes = [
  { id: 'bar', name: 'Bar Chart', icon: <BarChart /> },
  { id: 'line', name: 'Line Chart', icon: <Timeline /> },
  { id: 'pie', name: 'Pie Chart', icon: <PieChart /> }
];

// Available report formats
const reportFormats = [
  { id: 'pdf', name: 'PDF' },
  { id: 'excel', name: 'Excel' },
  { id: 'csv', name: 'CSV' }
];

const Custom: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const [activeTab, setActiveTab] = useState(0);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [savedReports, setSavedReports] = useState<any[]>([]);

  useEffect(() => {
    // Load saved reports when component mounts
    const loadSavedReports = async () => {
      try {
        const reports = await getSavedReports('custom');
        setSavedReports(reports);
      } catch (error) {
        console.error('Error loading saved reports:', error);
      }
    };

    loadSavedReports();
  }, []);

  const handleGenerateReport = async () => {
    try {
      setIsGenerating(true);

      const params: ReportParams = {
        type: 'custom',
        format: 'pdf',
        timePeriod: 'month',
        customFields: ['livestock', 'health', 'financial']
      };

      await generateReport(params);

      // Refresh the saved reports list
      const reports = await getSavedReports('custom');
      setSavedReports(reports);
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };
  const [activeStep, setActiveStep] = useState(0);
  const [selectedModules, setSelectedModules] = useState<string[]>([]);
  const [selectedFields, setSelectedFields] = useState<{[key: string]: string[]}>({});
  const [reportName, setReportName] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  const [reportFormat, setReportFormat] = useState('pdf');
  const [selectedCharts, setSelectedCharts] = useState<string[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleOpenDialog = (report?: any) => {
    if (report) {
      setSelectedReport(report);
      setReportName(report.name);
      setReportDescription(report.description);
      setReportFormat(report.format.toLowerCase());
      setSelectedModules(report.modules.map((m: string) => m.toLowerCase()));
    } else {
      setSelectedReport(null);
      setReportName('');
      setReportDescription('');
      setReportFormat('pdf');
      setSelectedModules([]);
      setSelectedFields({});
      setSelectedCharts([]);
      setActiveStep(0);
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleModuleChange = (moduleId: string) => {
    setSelectedModules((prev) => {
      if (prev.includes(moduleId)) {
        // Remove module and its fields
        const newSelectedModules = prev.filter(id => id !== moduleId);
        const newSelectedFields = { ...selectedFields };
        delete newSelectedFields[moduleId];
        setSelectedFields(newSelectedFields);
        return newSelectedModules;
      } else {
        // Add module
        return [...prev, moduleId];
      }
    });
  };

  const handleFieldChange = (moduleId: string, field: string) => {
    setSelectedFields((prev) => {
      const moduleFields = prev[moduleId] || [];
      if (moduleFields.includes(field)) {
        // Remove field
        return {
          ...prev,
          [moduleId]: moduleFields.filter(f => f !== field)
        };
      } else {
        // Add field
        return {
          ...prev,
          [moduleId]: [...moduleFields, field]
        };
      }
    });
  };

  const handleChartChange = (chartId: string) => {
    setSelectedCharts((prev) => {
      if (prev.includes(chartId)) {
        return prev.filter(id => id !== chartId);
      } else {
        return [...prev, chartId];
      }
    });
  };

  const handleSave = () => {
    // Save logic would go here
    handleCloseDialog();
  };

  const handleGenerate = () => {
    // Generate report logic would go here
    handleCloseDialog();
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Basic Information
            </Typography>
            <TextField
              label="Report Name"
              fullWidth
              margin="normal"
              value={reportName}
              onChange={(e) => setReportName(e.target.value)}
            />
            <TextField
              label="Description"
              fullWidth
              multiline
              rows={3}
              margin="normal"
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Format</InputLabel>
              <Select
                value={reportFormat}
                label="Format"
                onChange={(e) => setReportFormat(e.target.value)}
              >
                {reportFormats.map((format) => (
                  <MenuItem key={format.id} value={format.id}>{format.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select Data Modules
            </Typography>
            <FormGroup>
              {availableModules.map((module) => (
                <FormControlLabel
                  key={module.id}
                  control={
                    <Checkbox
                      checked={selectedModules.includes(module.id)}
                      onChange={() => handleModuleChange(module.id)}
                    />
                  }
                  label={module.name}
                />
              ))}
            </FormGroup>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Select Fields
            </Typography>
            {selectedModules.length === 0 ? (
              <Typography color="text.secondary">
                Please select at least one module first.
              </Typography>
            ) : (
              selectedModules.map((moduleId) => {
                const module = availableModules.find(m => m.id === moduleId);
                if (!module) return null;

                return (
                  <Box key={moduleId} sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      {module.name}
                    </Typography>
                    <FormGroup>
                      <Grid container spacing={2}>
                        {module.fields.map((field) => (
                          <Grid item xs={12} sm={6} md={4} key={field}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={selectedFields[moduleId]?.includes(field) || false}
                                  onChange={() => handleFieldChange(moduleId, field)}
                                />
                              }
                              label={field}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </FormGroup>
                  </Box>
                );
              })
            )}
          </Box>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Visualization Options
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Select the chart types you want to include in your report.
            </Typography>
            <Grid container spacing={2}>
              {chartTypes.map((chart) => (
                <Grid item xs={12} sm={4} key={chart.id}>
                  <Card
                    sx={{
                      p: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      border: selectedCharts.includes(chart.id) ? `2px solid ${themeColor.primary}` : '1px solid #ddd',
                      cursor: 'pointer',
                      '&:hover': { boxShadow: 3 }
                    }}
                    onClick={() => handleChartChange(chart.id)}
                  >
                    <Box sx={{ fontSize: 40, color: selectedCharts.includes(chart.id) ? themeColor.primary : 'text.secondary' }}>
                      {chart.icon}
                    </Box>
                    <Typography variant="subtitle1" sx={{ mt: 1 }}>
                      {chart.name}
                    </Typography>
                    {selectedCharts.includes(chart.id) && (
                      <Chip
                        label="Selected"
                        sx={{
                          backgroundColor: themeColor.primary,
                          color: 'white',
                          mt: 1
                        }}
                        size="small"
                      />
                    )}
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        );
      case 4:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review & Generate
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Report Details
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Name:
                  </Typography>
                  <Typography variant="body1">
                    {reportName || 'Untitled Report'}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Description:
                  </Typography>
                  <Typography variant="body1">
                    {reportDescription || 'No description provided'}
                  </Typography>
                </Box>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Format:
                  </Typography>
                  <Typography variant="body1">
                    {reportFormats.find(f => f.id === reportFormat)?.name || 'PDF'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Selected Modules
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {selectedModules.length > 0 ? (
                    selectedModules.map((moduleId) => {
                      const module = availableModules.find(m => m.id === moduleId);
                      return module ? (
                        <Chip
                          key={moduleId}
                          label={module.name}
                          color="primary"
                          variant="outlined"
                        />
                      ) : null;
                    })
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No modules selected
                    </Typography>
                  )}
                </Box>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Visualizations
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {selectedCharts.length > 0 ? (
                    selectedCharts.map((chartId) => {
                      const chart = chartTypes.find(c => c.id === chartId);
                      return chart ? (
                        <Chip
                          key={chartId}
                          label={chart.name}
                          color="secondary"
                          variant="outlined"
                          icon={chart.icon}
                        />
                      ) : null;
                    })
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No visualizations selected
                    </Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Custom Report Builder"
        subtitle="Design, create, and schedule personalized reports tailored to your specific needs"
        module="reports"
        submodule="custom"
        actionLabel={isGenerating ? "Generating..." : "Export Custom Report"}
        actionIcon={<Download />}
        onAction={handleGenerateReport}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Report Builder Dashboard */}
        <Paper
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}
        >
          <Box display="flex" alignItems="center" mb={2}>
            <Box
              sx={{
                p: 1.5,
                borderRadius: 2,
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                mr: 2
              }}
            >
              <Description />
            </Box>
            <Typography variant="h5" fontWeight="bold">
              Report Builder Dashboard
            </Typography>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="primary.main">
                  Quick Report
                </Typography>
                <Typography variant="body2" paragraph>
                  Generate a report instantly using one of our pre-configured templates. Select modules and data points to include.
                </Typography>
                <CustomButton variant="outlined" size="small" sx={{ mt: 1 }} onClick={() => handleOpenDialog()}>
                  Start Quick Report
                </CustomButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="success.main">
                  Scheduled Reports
                </Typography>
                <Typography variant="body2" paragraph>
                  Set up automatic report generation on daily, weekly, or monthly schedules. Reports can be emailed to specified recipients.
                </Typography>
                <CustomButton variant="outlined" size="small" color="success" sx={{ mt: 1 }}>
                  Manage Schedules
                </CustomButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="warning.main">
                  Advanced Builder
                </Typography>
                <Typography variant="body2" paragraph>
                  Create complex custom reports with advanced filtering, calculations, and visualizations. Full control over layout and content.
                </Typography>
                <CustomButton variant="outlined" size="small" color="warning" sx={{ mt: 1 }}>
                  Open Advanced Builder
                </CustomButton>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="My Reports" />
          <Tab label="Templates" />
          <Tab label="Scheduled" />
        </Tabs>

        {activeTab === 0 && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" fontWeight="bold">
                My Custom Reports
              </Typography>
              <CustomButton
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog()}
              >
                Create Report
              </CustomButton>
            </Box>

            <motion.div variants={itemVariants}>
              <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px', mb: 4 }}>
                <Table>
                  <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableRow>
                      <TableCell><strong>Report Name</strong></TableCell>
                      <TableCell><strong>Description</strong></TableCell>
                      <TableCell><strong>Last Generated</strong></TableCell>
                      <TableCell><strong>Format</strong></TableCell>
                      <TableCell><strong>Modules</strong></TableCell>
                      <TableCell><strong>Shared</strong></TableCell>
                      <TableCell><strong>Actions</strong></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockCustomReports.map((report) => (
                      <TableRow key={report.id} hover>
                        <TableCell>{report.name}</TableCell>
                        <TableCell>{report.description}</TableCell>
                        <TableCell>{report.lastGenerated}</TableCell>
                        <TableCell>{report.format}</TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {report.modules.map((module) => (
                              <Chip key={module} label={module} size="small" />
                            ))}
                          </Box>
                        </TableCell>
                        <TableCell>
                          {report.shared ? (
                            <Visibility color="primary" fontSize="small" />
                          ) : (
                            <VisibilityOff color="disabled" fontSize="small" />
                          )}
                        </TableCell>
                        <TableCell>
                          <IconButton size="small" onClick={() => handleOpenDialog(report)}>
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small">
                            <Delete fontSize="small" />
                          </IconButton>
                          <IconButton size="small">
                            <Download fontSize="small" />
                          </IconButton>
                          <IconButton size="small">
                            <Share fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
                Recently Generated Reports
              </Typography>

              <Grid container spacing={3}>
                {mockCustomReports.slice(0, 3).map((report) => (
                  <Grid item xs={12} md={4} key={report.id}>
                    <Card sx={{ height: '100%', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Description color="primary" sx={{ mr: 1 }} />
                          <Typography variant="h6" fontWeight="bold">
                            {report.name}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          {report.description}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                          <Typography variant="caption" color="text.secondary">
                            Generated: {report.lastGenerated}
                          </Typography>
                          <Box>
                            <IconButton size="small">
                              <Download fontSize="small" />
                            </IconButton>
                            <IconButton size="small">
                              <Print fontSize="small" />
                            </IconButton>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          </motion.div>
        )}

        {activeTab === 1 && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Report Templates
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <Card sx={{ height: '100%', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <BarChart color="primary" sx={{ mr: 1 }} />
                        <Typography variant="h6" fontWeight="bold">
                          Financial Performance
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Comprehensive financial analysis with income, expenses, and profitability metrics.
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                        <Chip label="Financial" size="small" />
                        <Chip label="Animals" size="small" />
                      </Box>
                      <CustomButton
                        variant="outlined"
                        fullWidth
                        onClick={() => handleOpenDialog()}
                      >
                        Use Template
                      </CustomButton>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>

              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <Card sx={{ height: '100%', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Timeline color="secondary" sx={{ mr: 1 }} />
                        <Typography variant="h6" fontWeight="bold">
                          Health & Productivity
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Analysis of health interventions and their impact on animal productivity.
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                        <Chip label="Health" size="small" />
                        <Chip label="Animals" size="small" />
                        <Chip label="Feeding" size="small" />
                      </Box>
                      <CustomButton
                        variant="outlined"
                        fullWidth
                        onClick={() => handleOpenDialog()}
                      >
                        Use Template
                      </CustomButton>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>

              <Grid item xs={12} md={4}>
                <motion.div variants={itemVariants}>
                  <Card sx={{ height: '100%', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <PieChart color="success" sx={{ mr: 1 }} />
                        <Typography variant="h6" fontWeight="bold">
                          Breeding Performance
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        Detailed analysis of breeding success rates, costs, and genetic improvements.
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                        <Chip label="Breeding" size="small" />
                        <Chip label="Animals" size="small" />
                        <Chip label="Financial" size="small" />
                      </Box>
                      <CustomButton
                        variant="outlined"
                        fullWidth
                        onClick={() => handleOpenDialog()}
                      >
                        Use Template
                      </CustomButton>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        )}
      </Box>

      {/* Report Builder Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedReport ? 'Edit Report' : 'Create Custom Report'}
        </DialogTitle>
        <DialogContent dividers>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            <Step>
              <StepLabel>Basic Info</StepLabel>
            </Step>
            <Step>
              <StepLabel>Select Modules</StepLabel>
            </Step>
            <Step>
              <StepLabel>Select Fields</StepLabel>
            </Step>
            <Step>
              <StepLabel>Visualizations</StepLabel>
            </Step>
            <Step>
              <StepLabel>Review</StepLabel>
            </Step>
          </Stepper>

          {getStepContent(activeStep)}
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          {activeStep > 0 && (
            <CustomButton onClick={handleBack}>
              Back
            </CustomButton>
          )}
          {activeStep < 4 ? (
            <CustomButton onClick={handleNext} variant="contained" color="primary">
              Next
            </CustomButton>
          ) : (
            <>
              <CustomButton onClick={handleSave} variant="outlined" color="primary" startIcon={<Save />}>
                Save Template
              </CustomButton>
              <CustomButton onClick={handleGenerate} variant="contained" color="primary">
                Generate Report
              </CustomButton>
            </>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Custom;
