import { alpha, Theme } from '@mui/material';
import { getConsistentBackground } from './backgroundThemes';

/**
 * Get a consistent background image for a card based on its ID
 * @param id The unique identifier for the card
 * @param prefix Optional prefix to add to the ID for different card types
 * @returns A URL to a background image
 */
export const getCardBackground = (id: string, prefix?: string): string => {
  const fullId = prefix ? `${prefix}-${id}` : id;
  return getConsistentBackground(fullId);
};

/**
 * Create a gradient overlay style for a card
 * @param theme The Material-UI theme
 * @param primaryColor Optional primary color (defaults to theme.palette.primary.main)
 * @param secondaryColor Optional secondary color (defaults to theme.palette.primary.dark)
 * @param opacity The opacity of the gradient (0-1)
 * @returns A CSS style object for the gradient overlay
 */
export const getCardGradientOverlay = (
  theme: Theme,
  primaryColor?: string,
  secondaryColor?: string,
  opacity: number = 0.7
): React.CSSProperties => {
  const cardPrimaryColor = primaryColor || theme.palette.primary.main;
  const cardSecondaryColor = secondaryColor || theme.palette.primary.dark;
  
  return {
    background: `linear-gradient(135deg, ${alpha(cardPrimaryColor, opacity)}, ${alpha(cardSecondaryColor, opacity)})`,
    backdropFilter: 'blur(2px)'
  };
};

/**
 * Create a card background style with image and gradient overlay
 * @param backgroundImage The background image URL
 * @param theme The Material-UI theme
 * @param primaryColor Optional primary color
 * @param secondaryColor Optional secondary color
 * @param opacity The opacity of the gradient overlay
 * @returns A CSS style object for the card background
 */
export const getCardBackgroundStyle = (
  backgroundImage: string,
  theme: Theme,
  primaryColor?: string,
  secondaryColor?: string,
  opacity: number = 0.7
): React.CSSProperties => {
  return {
    position: 'relative',
    backgroundImage: `url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      ...getCardGradientOverlay(theme, primaryColor, secondaryColor, opacity),
      zIndex: 1
    }
  } as React.CSSProperties;
};

/**
 * Get a hover effect style for cards
 * @param elevation The elevation level for the shadow
 * @returns A CSS style object for the hover effect
 */
export const getCardHoverEffect = (elevation: number = 5): React.CSSProperties => {
  return {
    transform: 'translateY(-5px)',
    boxShadow: `0 ${elevation * 2}px ${elevation * 5}px rgba(0,0,0,${0.03 * elevation})`
  } as React.CSSProperties;
};

/**
 * Create a consistent card style that can be applied to any card component
 * @param theme The Material-UI theme
 * @param options Optional configuration options
 * @returns A CSS style object for the card
 */
export const getConsistentCardStyle = (
  theme: Theme,
  options?: {
    borderRadius?: number;
    elevation?: number;
    primaryColor?: string;
    secondaryColor?: string;
  }
): React.CSSProperties => {
  const {
    borderRadius = 12,
    elevation = 2,
    primaryColor = theme.palette.primary.main,
    secondaryColor = theme.palette.primary.dark
  } = options || {};
  
  return {
    borderRadius: `${borderRadius}px`,
    boxShadow: `0 ${elevation}px ${elevation * 4}px rgba(0,0,0,${0.02 * elevation})`,
    overflow: 'hidden',
    border: `1px solid ${alpha(primaryColor, 0.1)}`,
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
    '&:hover': getCardHoverEffect(elevation),
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '4px',
      background: `linear-gradient(to right, ${primaryColor}, ${secondaryColor})`,
      zIndex: 3,
      opacity: 0.9
    }
  } as React.CSSProperties;
};
