import { fetchData, postData, updateData, deleteData } from './apiService';
import { Animal, AnimalStats } from '../types/animal';
import { Animal as HookAnimal } from '../hooks/useAnimalData';
import { mockAnimals, mockAnimalStats } from '../mocks/animalData';

// Import from config to ensure consistency
import { useMockData } from '../utils/config';

export const getAnimals = async (): Promise<Animal[]> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return mockAnimals as unknown as Animal[];
  }
  return fetchData('/animals');
};

export const getAnimalStats = async (): Promise<AnimalStats> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    // Ensure the returned object matches the AnimalStats interface
    return mockAnimalStats as AnimalStats;
  }
  return fetchData('/animals/stats');
};

export const getAnimalById = async (id: string): Promise<Animal> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    const animal = mockAnimals.find(a => a.id === id);
    if (!animal) throw new Error('Animal not found');
    return animal as unknown as Animal;
  }
  return fetchData(`/animals/${id}`);
};

export const createAnimal = async (animal: Omit<Animal, 'id'>): Promise<Animal> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      ...animal,
      id: `${mockAnimals.length + 1}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as Animal;
  }
  return postData('/animals', animal);
};

export const updateAnimal = async (id: string, animal: Partial<Animal>): Promise<Animal> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    const index = mockAnimals.findIndex(a => a.id === id);
    if (index === -1) throw new Error('Animal not found');
    return {
      ...mockAnimals[index],
      ...animal,
      updatedAt: new Date().toISOString()
    } as Animal;
  }
  return updateData(`/animals/${id}`, animal);
};

export const deleteAnimal = async (id: string): Promise<void> => {
  if (useMockData) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    const index = mockAnimals.findIndex(a => a.id === id);
    if (index === -1) throw new Error('Animal not found');
    return;
  }
  return deleteData(`/animals/${id}`);
};
