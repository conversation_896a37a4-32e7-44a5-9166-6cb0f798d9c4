import React, { createContext, useContext, ReactNode } from 'react';
import { useTheme, alpha } from '@mui/material';
// Removed useThemeContext import as it was deleted

interface GlobalThemeContextType {
  getModuleColor: (module: string) => string;
  getModuleSecondaryColor: (module: string) => string;
  getModuleGradient: (module: string, opacity?: number) => string;
  getModuleBackgroundPattern: (module: string, pattern?: string, opacity?: number) => string;
  getModuleCardStyle: (module: string) => React.CSSProperties;
  getModuleHeaderStyle: (module: string) => React.CSSProperties;
}

const GlobalThemeContext = createContext<GlobalThemeContextType | undefined>(undefined);

export const useGlobalTheme = () => {
  const context = useContext(GlobalThemeContext);
  if (!context) {
    throw new Error('useGlobalTheme must be used within a GlobalThemeProvider');
  }
  return context;
};

interface GlobalThemeProviderProps {
  children: ReactNode;
}

export const GlobalThemeProvider: React.FC<GlobalThemeProviderProps> = ({ children }) => {
  const theme = useTheme();
  
  // Get primary color for a module
  const getModuleColor = (module: string): string => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      case 'analytics':
        return theme.palette.primary.dark;
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Get secondary color for a module
  const getModuleSecondaryColor = (module: string): string => {
    const primaryColor = getModuleColor(module);
    return theme.palette.mode === 'dark' ? 
      alpha(primaryColor, 0.7) : 
      alpha(primaryColor, 0.5);
  };
  
  // Get gradient for a module
  const getModuleGradient = (module: string, opacity: number = 0.8): string => {
    const primaryColor = getModuleColor(module);
    const secondaryColor = getModuleSecondaryColor(module);
    
    return `linear-gradient(135deg, ${alpha(primaryColor, opacity)}, ${alpha(secondaryColor, opacity)})`;
  };
  
  // Get background pattern for a module
  const getModuleBackgroundPattern = (module: string, pattern: string = 'radial', opacity: number = 0.05): string => {
    const primaryColor = getModuleColor(module);
    const secondaryColor = getModuleSecondaryColor(module);
    
    switch (pattern) {
      case 'gradient':
        return `linear-gradient(135deg, ${alpha(primaryColor, opacity)}, ${alpha(secondaryColor, opacity)})`;
      case 'radial':
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 40%), 
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColor, opacity)}, transparent 40%)`;
      case 'mesh':
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 30%), 
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColor, opacity)}, transparent 30%),
                radial-gradient(circle at 30% 70%, ${alpha(primaryColor, opacity/2)}, transparent 20%),
                radial-gradient(circle at 70% 30%, ${alpha(secondaryColor, opacity/2)}, transparent 20%)`;
      case 'dots':
        return `radial-gradient(circle at 25% 25%, ${alpha(primaryColor, opacity)} 1%, transparent 1%),
                radial-gradient(circle at 75% 75%, ${alpha(secondaryColor, opacity)} 1%, transparent 1%),
                radial-gradient(circle at 25% 75%, ${alpha(primaryColor, opacity/2)} 1%, transparent 1%),
                radial-gradient(circle at 75% 25%, ${alpha(secondaryColor, opacity/2)} 1%, transparent 1%)`;
      case 'waves':
        return `linear-gradient(45deg, ${alpha(primaryColor, opacity)} 25%, transparent 25%),
                linear-gradient(-45deg, ${alpha(primaryColor, opacity)} 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, ${alpha(secondaryColor, opacity)} 75%),
                linear-gradient(-45deg, transparent 75%, ${alpha(secondaryColor, opacity)} 75%)`;
      default:
        return `radial-gradient(circle at 10% 10%, ${alpha(primaryColor, opacity)}, transparent 40%), 
                radial-gradient(circle at 90% 90%, ${alpha(secondaryColor, opacity)}, transparent 40%)`;
    }
  };
  
  // Get card style for a module
  const getModuleCardStyle = (module: string): React.CSSProperties => {
    const primaryColor = getModuleColor(module);
    
    return {
      borderRadius: '12px',
      overflow: 'hidden',
      background: alpha(theme.palette.background.paper, 0.8),
      backdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(primaryColor, 0.1)}`,
      boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
      transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    };
  };
  
  // Get header style for a module
  const getModuleHeaderStyle = (module: string): React.CSSProperties => {
    const primaryColor = getModuleColor(module);
    
    return {
      marginBottom: '24px',
      padding: '16px',
      borderRadius: '12px',
      background: `linear-gradient(135deg, ${alpha(primaryColor, 0.15)}, ${alpha(primaryColor, 0.05)})`,
      backdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(primaryColor, 0.1)}`,
      boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
    };
  };
  
  const contextValue: GlobalThemeContextType = {
    getModuleColor,
    getModuleSecondaryColor,
    getModuleGradient,
    getModuleBackgroundPattern,
    getModuleCardStyle,
    getModuleHeaderStyle,
  };
  
  return (
    <GlobalThemeContext.Provider value={contextValue}>
      {children}
    </GlobalThemeContext.Provider>
  );
};

export default GlobalThemeProvider;
