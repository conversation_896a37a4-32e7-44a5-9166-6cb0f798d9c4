import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Grid, Typography, Card, CardContent, TextField, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, useTheme, alpha, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, Stepper, Step, StepLabel, Divider, Tooltip, Avatar, List, ListItem, ListItemText, ListItemAvatar, InputAdornment, Collapse } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { motion } from 'framer-motion';
import {
  Add,
  Edit,
  Delete,
  Search,
  FilterList,
  ShoppingCart,
  LocalShipping,
  Inventory,
  Receipt,
  AttachMoney,
  CheckCircle,
  Warning,
  ExpandMore,
  ExpandLess,
  Print,
  Download,
  Close,
  Refresh
} from '../../utils/iconImports';
import {  <PERSON><PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

// Mock data for orders
const MOCK_ORDERS = [
  {
    id: 'ORD-2023-001',
    date: new Date('2023-11-15'),
    customer: 'Green Valley Farm',
    items: [
      { id: 'ITEM001', name: 'Nguni Cattle (Female)', quantity: 5, price: 12000, total: 60000 },
      { id: 'ITEM002', name: 'Cattle Feed - Premium Mix', quantity: 10, price: 450, total: 4500 }
    ],
    totalAmount: 64500,
    status: 'delivered',
    paymentStatus: 'paid',
    shippingAddress: '123 Farm Road, Stellenbosch, Western Cape',
    trackingNumber: 'TRK78945612',
    deliveryDate: new Date('2023-11-20')
  },
  {
    id: 'ORD-2023-002',
    date: new Date('2023-11-18'),
    customer: 'Sunrise Livestock',
    items: [
      { id: 'ITEM003', name: 'Dorper Sheep (Breeding Pair)', quantity: 2, price: 4500, total: 9000 },
      { id: 'ITEM004', name: 'Sheep Vaccines Pack', quantity: 1, price: 1200, total: 1200 }
    ],
    totalAmount: 10200,
    status: 'shipped',
    paymentStatus: 'paid',
    shippingAddress: '45 Karoo Road, Graaff-Reinet, Eastern Cape',
    trackingNumber: 'TRK45678923',
    deliveryDate: new Date('2023-11-25')
  },
  {
    id: 'ORD-2023-003',
    date: new Date('2023-11-20'),
    customer: 'Highland Breeders',
    items: [
      { id: 'ITEM005', name: 'Bonsmara Bull (Premium)', quantity: 1, price: 35000, total: 35000 },
      { id: 'ITEM006', name: 'Cattle Mineral Supplements', quantity: 5, price: 350, total: 1750 }
    ],
    totalAmount: 36750,
    status: 'processing',
    paymentStatus: 'pending',
    shippingAddress: '78 Mountain View, Harrismith, Free State',
    trackingNumber: null,
    deliveryDate: null
  },
  {
    id: 'ORD-2023-004',
    date: new Date('2023-11-22'),
    customer: 'Grassland Farms',
    items: [
      { id: 'ITEM007', name: 'Lucerne Hay Bales', quantity: 50, price: 85, total: 4250 },
      { id: 'ITEM008', name: 'Maize Feed Mix', quantity: 20, price: 320, total: 6400 }
    ],
    totalAmount: 10650,
    status: 'pending',
    paymentStatus: 'paid',
    shippingAddress: '12 Plains Road, Middelburg, Mpumalanga',
    trackingNumber: null,
    deliveryDate: null
  },
  {
    id: 'ORD-2023-005',
    date: new Date('2023-11-25'),
    customer: 'Savanna Livestock',
    items: [
      { id: 'ITEM009', name: 'Boer Goats (Female)', quantity: 10, price: 3200, total: 32000 },
      { id: 'ITEM010', name: 'Goat Feed - Special Mix', quantity: 15, price: 280, total: 4200 }
    ],
    totalAmount: 36200,
    status: 'pending',
    paymentStatus: 'pending',
    shippingAddress: '34 Bushveld Lane, Polokwane, Limpopo',
    trackingNumber: null,
    deliveryDate: null
  }
];

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount);
};

// Status chip colors and icons
const STATUS_CONFIG = {
  pending: { color: 'warning', icon: <Warning fontSize="small" />, label: 'Pending' },
  processing: { color: 'info', icon: <Inventory fontSize="small" />, label: 'Processing' },
  shipped: { color: 'primary', icon: <LocalShipping fontSize="small" />, label: 'Shipped' },
  delivered: { color: 'success', icon: <CheckCircle fontSize="small" />, label: 'Delivered' },
  cancelled: { color: 'error', icon: <Close fontSize="small" />, label: 'Cancelled' }
};

// Payment status chip colors
const PAYMENT_STATUS_CONFIG = {
  pending: { color: 'warning', label: 'Payment Pending' },
  paid: { color: 'success', label: 'Paid' },
  partial: { color: 'info', label: 'Partially Paid' },
  refunded: { color: 'error', label: 'Refunded' }
};

const Orders: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  // State for filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<any | null>(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);

  // Filter orders based on search term and status filter
  const filteredOrders = MOCK_ORDERS.filter(order => {
    const matchesSearch =
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Handle pagination change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle order expansion
  const handleExpandOrder = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId);
  };

  // Handle order details dialog
  const handleOpenOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  };

  // Handle close order details dialog
  const handleCloseOrderDetails = () => {
    setOrderDetailsOpen(false);
  };

  // Calculate order statistics
  const orderStats = {
    total: MOCK_ORDERS.length,
    pending: MOCK_ORDERS.filter(o => o.status === 'pending').length,
    processing: MOCK_ORDERS.filter(o => o.status === 'processing').length,
    shipped: MOCK_ORDERS.filter(o => o.status === 'shipped').length,
    delivered: MOCK_ORDERS.filter(o => o.status === 'delivered').length,
    totalValue: MOCK_ORDERS.reduce((sum, order) => sum + order.totalAmount, 0)
  };

  return (
    <Box>
      <ModuleHeader
        title="Order Management"
        subtitle="Track and manage your livestock, feed, and supply orders"
        module="commercial"
        submodule="orders"
        actionLabel="New Order"
        actionIcon={<Add />}
        onAction={() => navigate('/commercial/marketplace')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Status Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Active Orders"
              subtitle={`${orderStats.pending + orderStats.processing + orderStats.shipped} orders in progress`}
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<ShoppingCart />}
              accentColor={theme.palette.primary.main}
              delay={0.1}
              height={140}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Completed Orders"
              subtitle={`${orderStats.delivered} orders delivered`}
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<CheckCircle />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.2}
              height={140}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Total Order Value"
              subtitle={formatCurrency(orderStats.totalValue)}
              backgroundImage="https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg"
              icon={<AttachMoney />}
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              delay={0.3}
              height={140}
            />
          </Grid>
        </Grid>

        {/* Orders Table */}
        <Card sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
        }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight="bold">
                Orders
              </Typography>
              <CustomButton
                variant="contained"
                color="primary"
                startIcon={<Add />}
                onClick={() => navigate('/commercial/marketplace')}
                sx={{ borderRadius: '8px' }}
              >
                New Order
              </CustomButton>
            </Box>

            {/* Filters */}
            <Box display="flex" gap={2} mb={3} flexWrap="wrap">
              <TextField
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                sx={{ minWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
              <TextField
                select
                label="Status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                size="small"
                sx={{ minWidth: 150 }}
                SelectProps={{
                  native: true,
                }}
              >
                <option value="all">All Orders</option>
                <option value="pending">Pending</option>
                <option value="processing">Processing</option>
                <option value="shipped">Shipped</option>
                <option value="delivered">Delivered</option>
                <option value="cancelled">Cancelled</option>
              </TextField>
              <CustomButton
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                }}
              >
                Reset Filters
              </CustomButton>
            </Box>

            {/* Table */}
            <TableContainer component={Paper} sx={{ boxShadow: 'none', mb: 2 }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableCell>Order ID</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Customer</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Payment</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredOrders
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((order) => {
                      const statusConfig = STATUS_CONFIG[order.status as keyof typeof STATUS_CONFIG];
                      const paymentConfig = PAYMENT_STATUS_CONFIG[order.paymentStatus as keyof typeof PAYMENT_STATUS_CONFIG];

                      return (
                        <React.Fragment key={order.id}>
                          <TableRow hover>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleExpandOrder(order.id)}
                                  sx={{ mr: 1 }}
                                >
                                  {expandedOrder === order.id ? <ExpandLess /> : <ExpandMore />}
                                </IconButton>
                                {order.id}
                              </Box>
                            </TableCell>
                            <TableCell>{order.date.toLocaleDateString('en-ZA')}</TableCell>
                            <TableCell>{order.customer}</TableCell>
                            <TableCell align="right">{formatCurrency(order.totalAmount)}</TableCell>
                            <TableCell>
                              <Chip
                                icon={statusConfig.icon}
                                label={statusConfig.label}
                                color={statusConfig.color as any}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={paymentConfig.label}
                                color={paymentConfig.color as any}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell align="right">
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  onClick={() => handleOpenOrderDetails(order)}
                                  sx={{ color: theme.palette.primary.main }}
                                >
                                  <Receipt fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Print Invoice">
                                <IconButton
                                  size="small"
                                  sx={{ color: theme.palette.text.secondary }}
                                >
                                  <Print fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>

                          {/* Expanded Order Details */}
                          <TableRow>
                            <TableCell colSpan={7} sx={{ p: 0, borderBottom: 'none' }}>
                              <Collapse in={expandedOrder === order.id} timeout="auto" unmountOnExit>
                                <Box sx={{ py: 2, px: 3, backgroundColor: alpha(theme.palette.background.default, 0.5) }}>
                                  <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                                    Order Items
                                  </Typography>
                                  <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                                    <Table size="small">
                                      <TableHead>
                                        <TableRow>
                                          <TableCell>Item</TableCell>
                                          <TableCell align="right">Quantity</TableCell>
                                          <TableCell align="right">Unit Price</TableCell>
                                          <TableCell align="right">Total</TableCell>
                                        </TableRow>
                                      </TableHead>
                                      <TableBody>
                                        {order.items.map((item) => (
                                          <TableRow key={item.id}>
                                            <TableCell>{item.name}</TableCell>
                                            <TableCell align="right">{item.quantity}</TableCell>
                                            <TableCell align="right">{formatCurrency(item.price)}</TableCell>
                                            <TableCell align="right">{formatCurrency(item.total)}</TableCell>
                                          </TableRow>
                                        ))}
                                        <TableRow>
                                          <TableCell colSpan={3} align="right" sx={{ fontWeight: 'bold' }}>
                                            Total:
                                          </TableCell>
                                          <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                                            {formatCurrency(order.totalAmount)}
                                          </TableCell>
                                        </TableRow>
                                      </TableBody>
                                    </Table>
                                  </TableContainer>

                                  <Grid container spacing={2}>
                                    <Grid item xs={12} md={6}>
                                      <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                                        Shipping Information
                                      </Typography>
                                      <Paper variant="outlined" sx={{ p: 2 }}>
                                        <Typography variant="body2" paragraph>
                                          <strong>Address:</strong> {order.shippingAddress}
                                        </Typography>
                                        {order.trackingNumber && (
                                          <Typography variant="body2">
                                            <strong>Tracking Number:</strong> {order.trackingNumber}
                                          </Typography>
                                        )}
                                        {order.deliveryDate && (
                                          <Typography variant="body2">
                                            <strong>Delivery Date:</strong> {order.deliveryDate.toLocaleDateString('en-ZA')}
                                          </Typography>
                                        )}
                                      </Paper>
                                    </Grid>
                                    <Grid item xs={12} md={6}>
                                      <Typography variant="subtitle2" gutterBottom fontWeight="bold">
                                        Order Status
                                      </Typography>
                                      <Paper variant="outlined" sx={{ p: 2 }}>
                                        <Stepper activeStep={
                                          order.status === 'pending' ? 0 :
                                          order.status === 'processing' ? 1 :
                                          order.status === 'shipped' ? 2 :
                                          order.status === 'delivered' ? 3 : 0
                                        } orientation="vertical" sx={{ minHeight: 100 }}>
                                          <Step>
                                            <StepLabel>Order Placed</StepLabel>
                                          </Step>
                                          <Step>
                                            <StepLabel>Processing</StepLabel>
                                          </Step>
                                          <Step>
                                            <StepLabel>Shipped</StepLabel>
                                          </Step>
                                          <Step>
                                            <StepLabel>Delivered</StepLabel>
                                          </Step>
                                        </Stepper>
                                      </Paper>
                                    </Grid>
                                  </Grid>
                                </Box>
                              </Collapse>
                            </TableCell>
                          </TableRow>
                        </React.Fragment>
                      );
                    })}
                  {filteredOrders.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          No orders found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredOrders.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </CardContent>
        </Card>
      </Box>

      {/* Order Details Dialog */}
      <Dialog
        open={orderDetailsOpen}
        onClose={handleCloseOrderDetails}
        maxWidth="md"
        fullWidth
      >
        {selectedOrder && (
          <>
            <DialogTitle>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">
                  Order Details - {selectedOrder.id}
                </Typography>
                <IconButton onClick={handleCloseOrderDetails} size="small">
                  <Close />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Order Information
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                    <Typography variant="body2" gutterBottom>
                      <strong>Order Date:</strong> {selectedOrder.date.toLocaleDateString('en-ZA')}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Customer:</strong> {selectedOrder.customer}
                    </Typography>
                    <Typography variant="body2" gutterBottom>
                      <strong>Total Amount:</strong> {formatCurrency(selectedOrder.totalAmount)}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                      <Chip
                        icon={STATUS_CONFIG[selectedOrder.status as keyof typeof STATUS_CONFIG].icon}
                        label={STATUS_CONFIG[selectedOrder.status as keyof typeof STATUS_CONFIG].label}
                        color={STATUS_CONFIG[selectedOrder.status as keyof typeof STATUS_CONFIG].color as any}
                        size="small"
                      />
                      <Chip
                        label={PAYMENT_STATUS_CONFIG[selectedOrder.paymentStatus as keyof typeof PAYMENT_STATUS_CONFIG].label}
                        color={PAYMENT_STATUS_CONFIG[selectedOrder.paymentStatus as keyof typeof PAYMENT_STATUS_CONFIG].color as any}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  </Paper>

                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Shipping Information
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Typography variant="body2" paragraph>
                      <strong>Address:</strong> {selectedOrder.shippingAddress}
                    </Typography>
                    {selectedOrder.trackingNumber && (
                      <Typography variant="body2" gutterBottom>
                        <strong>Tracking Number:</strong> {selectedOrder.trackingNumber}
                      </Typography>
                    )}
                    {selectedOrder.deliveryDate && (
                      <Typography variant="body2" gutterBottom>
                        <strong>Delivery Date:</strong> {selectedOrder.deliveryDate.toLocaleDateString('en-ZA')}
                      </Typography>
                    )}
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Order Items
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 0, mb: 3 }}>
                    <List disablePadding>
                      {selectedOrder.items.map((item: any) => (
                        <ListItem key={item.id} divider>
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                              <Inventory />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={item.name}
                            secondary={`Quantity: ${item.quantity} × ${formatCurrency(item.price)}`}
                          />
                          <Typography variant="body2" fontWeight="bold">
                            {formatCurrency(item.total)}
                          </Typography>
                        </ListItem>
                      ))}
                      <ListItem sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                        <ListItemText
                          primary="Total"
                          primaryTypographyProps={{ fontWeight: 'bold' }}
                        />
                        <Typography variant="body1" fontWeight="bold" color="primary">
                          {formatCurrency(selectedOrder.totalAmount)}
                        </Typography>
                      </ListItem>
                    </List>
                  </Paper>

                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Order Status
                  </Typography>
                  <Paper variant="outlined" sx={{ p: 2 }}>
                    <Stepper activeStep={
                      selectedOrder.status === 'pending' ? 0 :
                      selectedOrder.status === 'processing' ? 1 :
                      selectedOrder.status === 'shipped' ? 2 :
                      selectedOrder.status === 'delivered' ? 3 : 0
                    } orientation="vertical">
                      <Step>
                        <StepLabel>Order Placed</StepLabel>
                      </Step>
                      <Step>
                        <StepLabel>Processing</StepLabel>
                      </Step>
                      <Step>
                        <StepLabel>Shipped</StepLabel>
                      </Step>
                      <Step>
                        <StepLabel>Delivered</StepLabel>
                      </Step>
                    </Stepper>
                  </Paper>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions sx={{ px: 3, py: 2 }}>
              <CustomButton
                variant="outlined"
                startIcon={<Print />}
              >
                Print Invoice
              </CustomButton>
              <CustomButton
                variant="outlined"
                startIcon={<Download />}
              >
                Download PDF
              </CustomButton>
              <CustomButton
                variant="contained"
                onClick={handleCloseOrderDetails}
              >
                Close
              </CustomButton>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default Orders;
