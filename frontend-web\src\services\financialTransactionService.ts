import { dataIntegrationService } from './dataIntegrationService';
import { Transaction } from '../mocks/financialData';

/**
 * Service for financial transaction management
 */
class FinancialTransactionService {
  /**
   * Get all transactions
   * @param limit Maximum number of transactions to return (default: 5)
   * @returns Transactions
   */
  async getAllTransactions(limit: number = 5): Promise<Transaction[]> {
    try {
      const transactions = await dataIntegrationService.getDocuments('financial_records', {}, limit);
      return transactions.map(this.mapToTransaction);
    } catch (error) {
      console.error('Error getting transactions:', error);
      throw error;
    }
  }

  /**
   * Get transaction by ID
   * @param id Transaction ID
   * @returns Transaction
   */
  async getTransactionById(id: string): Promise<Transaction | null> {
    try {
      const transaction = await dataIntegrationService.getDocument('financial_records', id);
      return transaction ? this.mapToTransaction(transaction) : null;
    } catch (error) {
      console.error('Error getting transaction:', error);
      throw error;
    }
  }

  /**
   * Get transactions by type
   * @param type Transaction type
   * @param limit Maximum number of transactions to return (default: 5)
   * @returns Transactions
   */
  async getTransactionsByType(type: 'income' | 'expense', limit: number = 5): Promise<Transaction[]> {
    try {
      const transactions = await dataIntegrationService.getDocuments(
        'financial_records',
        { type },
        limit
      );
      return transactions.map(this.mapToTransaction);
    } catch (error) {
      console.error('Error getting transactions by type:', error);
      throw error;
    }
  }

  /**
   * Get transactions by category
   * @param category Transaction category
   * @param limit Maximum number of transactions to return (default: 5)
   * @returns Transactions
   */
  async getTransactionsByCategory(category: string, limit: number = 5): Promise<Transaction[]> {
    try {
      const transactions = await dataIntegrationService.getDocuments(
        'financial_records',
        { category },
        limit
      );
      return transactions.map(this.mapToTransaction);
    } catch (error) {
      console.error('Error getting transactions by category:', error);
      throw error;
    }
  }

  /**
   * Get transactions by related entity
   * @param entityType Entity type
   * @param entityId Entity ID
   * @param limit Maximum number of transactions to return (default: 5)
   * @returns Transactions
   */
  async getTransactionsByRelatedEntity(
    entityType: string,
    entityId: string,
    limit: number = 5
  ): Promise<Transaction[]> {
    try {
      const transactions = await dataIntegrationService.getDocuments(
        'financial_records',
        {
          'relatedEntity.type': entityType,
          'relatedEntity.id': entityId
        },
        limit
      );
      return transactions.map(this.mapToTransaction);
    } catch (error) {
      console.error('Error getting transactions by related entity:', error);
      throw error;
    }
  }

  /**
   * Create transaction
   * @param transaction Transaction data
   * @returns Created transaction
   */
  async createTransaction(transaction: Omit<Transaction, 'id'>): Promise<Transaction> {
    try {
      const createdTransaction = await dataIntegrationService.createDocument('financial_records', transaction);
      return this.mapToTransaction(createdTransaction);
    } catch (error) {
      console.error('Error creating transaction:', error);
      throw error;
    }
  }

  /**
   * Update transaction
   * @param id Transaction ID
   * @param transaction Transaction data
   * @returns Updated transaction
   */
  async updateTransaction(id: string, transaction: Partial<Transaction>): Promise<Transaction> {
    try {
      const updatedTransaction = await dataIntegrationService.updateDocument('financial_records', id, transaction);
      return this.mapToTransaction(updatedTransaction);
    } catch (error) {
      console.error('Error updating transaction:', error);
      throw error;
    }
  }

  /**
   * Delete transaction
   * @param id Transaction ID
   * @returns Deletion result
   */
  async deleteTransaction(id: string): Promise<boolean> {
    try {
      return await dataIntegrationService.deleteDocument('financial_records', id);
    } catch (error) {
      console.error('Error deleting transaction:', error);
      throw error;
    }
  }

  /**
   * Get financial summary
   * @returns Financial summary
   */
  async getFinancialSummary(): Promise<{
    totalRevenue: number;
    totalExpenses: number;
    netProfit: number;
  }> {
    try {
      // Get all transactions
      const transactions = await dataIntegrationService.getDocuments('financial_records', {});
      
      // Calculate totals
      const totalRevenue = transactions
        .filter(transaction => transaction.type === 'income')
        .reduce((sum, transaction) => sum + transaction.amount, 0);
      
      const totalExpenses = transactions
        .filter(transaction => transaction.type === 'expense')
        .reduce((sum, transaction) => sum + transaction.amount, 0);
      
      const netProfit = totalRevenue - totalExpenses;
      
      return {
        totalRevenue,
        totalExpenses,
        netProfit
      };
    } catch (error) {
      console.error('Error getting financial summary:', error);
      throw error;
    }
  }

  /**
   * Map MongoDB transaction document to Transaction type
   * @param doc MongoDB transaction document
   * @returns Transaction object
   */
  private mapToTransaction(doc: any): Transaction {
    return {
      id: doc._id.toString(),
      date: new Date(doc.date),
      type: doc.type,
      category: doc.category,
      amount: doc.amount,
      description: doc.description,
      paymentMethod: doc.paymentMethod,
      status: doc.status,
      relatedEntity: doc.relatedEntity,
      attachments: doc.attachments,
      notes: doc.notes
    };
  }
}

// Export singleton instance
export const financialTransactionService = new FinancialTransactionService();
