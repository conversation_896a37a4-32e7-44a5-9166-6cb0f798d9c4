import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import FinancialDashboard from './FinancialDashboard';
import FinancialOverview from './FinancialOverview';
import FinancialTransactions from './FinancialTransactions';
import FinancialReports from './FinancialReports';
import FinancialBudgets from './FinancialBudgets';
import ROI from './ROI';
import Forecast from './Forecast';
import Invoices from './Invoices';

const Financial: React.FC = () => {
  return (
    <Routes>
      <Route index element={<FinancialDashboard />} />
      <Route path="overview" element={<FinancialOverview />} />
      <Route path="transactions" element={<FinancialTransactions />} />
      <Route path="reports" element={<FinancialReports />} />
      <Route path="budgets" element={<FinancialBudgets />} />
      <Route path="roi" element={<ROI />} />
      <Route path="forecast" element={<Forecast />} />
      <Route path="invoices" element={<Invoices />} />
      <Route path="*" element={
        <Box p={4}>
          <Typography variant="h4">Financial Management</Typography>
          <Typography variant="body1" mt={2}>
            Please select a specific financial view from the navigation.
          </Typography>
        </Box>
      } />
    </Routes>
  );
};

export default Financial;