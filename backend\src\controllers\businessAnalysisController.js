/**
 * Business Analysis Controller
 *
 * This controller handles Business Analysis operations for the API.
 */

const { getDB, getCollection } = require('../config/db');
const logger = require('../utils/logger');

const businessAnalysisController = {
  // Get all KPIs
  getBusinessKPIs: async (req, res) => {
    try {
      const { category } = req.query;
      const collection = await getCollection('business_kpis');
      
      let query = {};
      if (category) query.category = category;
      
      const kpis = await collection.find(query).toArray();
      res.json(kpis);
    } catch (error) {
      logger.error('Error fetching business KPIs from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all predictions
  getBusinessPredictions: async (req, res) => {
    try {
      const { category } = req.query;
      const collection = await getCollection('business_predictions');
      
      let query = {};
      if (category) query.category = category;
      
      const predictions = await collection.find(query).toArray();
      res.json(predictions);
    } catch (error) {
      logger.error('Error fetching business predictions from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all insights
  getBusinessInsights: async (req, res) => {
    try {
      const { category, impact } = req.query;
      const collection = await getCollection('business_insights');
      
      let query = {};
      if (category) query.category = category;
      if (impact) query.impact = impact;
      
      const insights = await collection.find(query).toArray();
      res.json(insights);
    } catch (error) {
      logger.error('Error fetching business insights from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all risks
  getBusinessRisks: async (req, res) => {
    try {
      const { category } = req.query;
      const collection = await getCollection('business_risks');
      
      let query = {};
      if (category) query.category = category;
      
      const risks = await collection.find(query).toArray();
      res.json(risks);
    } catch (error) {
      logger.error('Error fetching business risks from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all opportunities
  getBusinessOpportunities: async (req, res) => {
    try {
      const { category } = req.query;
      const collection = await getCollection('business_opportunities');
      
      let query = {};
      if (category) query.category = category;
      
      const opportunities = await collection.find(query).toArray();
      res.json(opportunities);
    } catch (error) {
      logger.error('Error fetching business opportunities from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all strategic goals
  getStrategicGoals: async (req, res) => {
    try {
      const { category, status } = req.query;
      const collection = await getCollection('strategic_goals');
      
      let query = {};
      if (category) query.category = category;
      if (status) query.status = status;
      
      const goals = await collection.find(query).toArray();
      res.json(goals);
    } catch (error) {
      logger.error('Error fetching strategic goals from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all strategic initiatives
  getStrategicInitiatives: async (req, res) => {
    try {
      const { category, status } = req.query;
      const collection = await getCollection('strategic_initiatives');
      
      let query = {};
      if (category) query.category = category;
      if (status) query.status = status;
      
      const initiatives = await collection.find(query).toArray();
      res.json(initiatives);
    } catch (error) {
      logger.error('Error fetching strategic initiatives from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get SWOT analysis
  getSWOTAnalysis: async (req, res) => {
    try {
      const collection = await getCollection('swot_analysis');
      const swotAnalysis = await collection.findOne({});
      res.json(swotAnalysis || {});
    } catch (error) {
      logger.error('Error fetching SWOT analysis from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get all strategic scenarios
  getStrategicScenarios: async (req, res) => {
    try {
      const collection = await getCollection('strategic_scenarios');
      const scenarios = await collection.find({}).toArray();
      res.json(scenarios);
    } catch (error) {
      logger.error('Error fetching strategic scenarios from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get business analytics dashboard data
  getBusinessAnalyticsDashboard: async (req, res) => {
    try {
      // Get database
      const db = getDB();
      
      // Get collections
      const kpisCollection = db.collection('business_kpis');
      const predictionsCollection = db.collection('business_predictions');
      const insightsCollection = db.collection('business_insights');
      const risksCollection = db.collection('business_risks');
      const opportunitiesCollection = db.collection('business_opportunities');
      
      // Fetch data in parallel
      const [kpis, predictions, insights, risks, opportunities] = await Promise.all([
        kpisCollection.find({}).toArray(),
        predictionsCollection.find({}).toArray(),
        insightsCollection.find({}).limit(5).toArray(),
        risksCollection.find({}).sort({ severity: -1 }).limit(3).toArray(),
        opportunitiesCollection.find({}).sort({ opportunityScore: -1 }).limit(3).toArray()
      ]);
      
      // Get animal data for charts
      const animalsCollection = db.collection('animals');
      const animals = await animalsCollection.find({}).toArray();
      
      // Calculate animal distribution
      const animalTypes = {};
      animals.forEach(animal => {
        const type = animal.type || 'Unknown';
        animalTypes[type] = (animalTypes[type] || 0) + 1;
      });
      
      const animalDistributionData = Object.entries(animalTypes).map(([name, value]) => ({
        name,
        value
      }));
      
      // Return dashboard data
      res.json({
        kpis,
        predictions,
        insights,
        risks,
        opportunities,
        animalDistributionData,
        // Add more chart data as needed
      });
    } catch (error) {
      logger.error('Error fetching business analytics dashboard data from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Get business strategy data
  getBusinessStrategy: async (req, res) => {
    try {
      // Get database
      const db = getDB();
      
      // Get collections
      const goalsCollection = db.collection('strategic_goals');
      const initiativesCollection = db.collection('strategic_initiatives');
      const swotCollection = db.collection('swot_analysis');
      const opportunitiesCollection = db.collection('business_opportunities');
      const scenariosCollection = db.collection('strategic_scenarios');
      
      // Fetch data in parallel
      const [goals, initiatives, swotArray, opportunities, scenarios] = await Promise.all([
        goalsCollection.find({}).toArray(),
        initiativesCollection.find({}).toArray(),
        swotCollection.find({}).toArray(),
        opportunitiesCollection.find({}).sort({ opportunityScore: -1 }).toArray(),
        scenariosCollection.find({}).toArray()
      ]);
      
      // Get SWOT analysis (first item or empty object)
      const swot = swotArray.length > 0 ? swotArray[0] : {
        strengths: [],
        weaknesses: [],
        opportunities: [],
        threats: []
      };
      
      // Return strategy data
      res.json({
        vision: 'To be the leading sustainable livestock farm in South Africa, recognized for excellence in animal welfare, environmental stewardship, and community impact.',
        mission: 'We are committed to producing high-quality livestock through ethical farming practices, innovative technology, and responsible resource management while supporting local communities and preserving the environment for future generations.',
        goals,
        initiatives,
        swot,
        opportunities,
        scenarios
      });
    } catch (error) {
      logger.error('Error fetching business strategy data from MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Create a new KPI
  createBusinessKPI: async (req, res) => {
    try {
      const kpiData = req.body;
      const collection = await getCollection('business_kpis');
      
      // Add timestamps
      kpiData.createdAt = new Date();
      kpiData.updatedAt = new Date();
      
      const result = await collection.insertOne(kpiData);
      
      res.status(201).json({
        _id: result.insertedId,
        ...kpiData
      });
    } catch (error) {
      logger.error('Error creating business KPI in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Update a KPI
  updateBusinessKPI: async (req, res) => {
    try {
      const { id } = req.params;
      const kpiData = req.body;
      const collection = await getCollection('business_kpis');
      
      // Update timestamp
      kpiData.updatedAt = new Date();
      
      const result = await collection.updateOne(
        { id },
        { $set: kpiData }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'KPI not found' });
      }
      
      res.json({
        id,
        ...kpiData
      });
    } catch (error) {
      logger.error('Error updating business KPI in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Create a new insight
  createBusinessInsight: async (req, res) => {
    try {
      const insightData = req.body;
      const collection = await getCollection('business_insights');
      
      // Add timestamps
      insightData.createdAt = new Date();
      insightData.updatedAt = new Date();
      
      const result = await collection.insertOne(insightData);
      
      res.status(201).json({
        _id: result.insertedId,
        ...insightData
      });
    } catch (error) {
      logger.error('Error creating business insight in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },
  
  // Update SWOT analysis
  updateSWOTAnalysis: async (req, res) => {
    try {
      const swotData = req.body;
      const collection = await getCollection('swot_analysis');
      
      // Update timestamp
      swotData.updatedAt = new Date();
      
      // Check if SWOT analysis exists
      const existingSWOT = await collection.findOne({});
      
      if (existingSWOT) {
        // Update existing SWOT analysis
        const result = await collection.updateOne(
          { _id: existingSWOT._id },
          { $set: swotData }
        );
        
        res.json({
          _id: existingSWOT._id,
          ...swotData
        });
      } else {
        // Create new SWOT analysis
        swotData.createdAt = new Date();
        const result = await collection.insertOne(swotData);
        
        res.status(201).json({
          _id: result.insertedId,
          ...swotData
        });
      }
    } catch (error) {
      logger.error('Error updating SWOT analysis in MongoDB:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = businessAnalysisController;
