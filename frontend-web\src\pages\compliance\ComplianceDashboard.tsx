import React, { useState, useEffect, useRef } from 'react';
import { useComplianceDataContext } from '../../contexts/DataContext';
import { Grid, Card, CardContent, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, List, ListItem, ListItemText, ListItemIcon, Box, Tab, Tabs, MenuItem, Select, FormControl, InputLabel, CircularProgress, Tooltip, useTheme } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CheckCircle,
  Warning,
  Error as ErrorIcon,
  Description,
  CloudDownload,
  Assignment,
  EventNote,
  VerifiedUser
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import AnimatedBackgroundCard from '../../components/common/AnimatedBackgroundCard';
import ModuleHeader from '../../components/common/ModuleHeader';
import EnhancedDataTable from '../../components/common/EnhancedDataTable';
import AccessibleWrapper from '../../components/common/AccessibleWrapper';
import { useAccessibility } from '../../hooks/useAccessibility';
import {  StandardDashboard, ModernChart , CustomButton } from '../../components/common';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const ComplianceDashboard: React.FC = () => {
  const theme = useTheme();
  const { documents, certifications, inspections, stats, loading, error, fetchComplianceData } = useComplianceDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState<'certification' | 'inspection' | 'document'>('certification');
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [activeTab, setActiveTab] = useState(0);
  const { handleKeyboardNavigation, getAriaAttributes, setFocus } = useAccessibility();
  const dialogRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Fetch compliance data when component mounts
    fetchComplianceData && fetchComplianceData();
  }, [fetchComplianceData]);

  const handleOpenDialog = (type: 'certification' | 'inspection' | 'document', item?: any) => {
    setDialogType(type);
    setSelectedItem(item || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedItem(null);
  };

  const handleDelete = (id: string) => {
    // Implement delete functionality
    console.log(`Delete item with ID: ${id}`);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
      case 'valid':
      case 'passed':
      case 'current':
        return 'success';
      case 'expiring soon':
      case 'passed with recommendations':
      case 'pending':
      case 'needs review':
        return 'warning';
      case 'expired':
      case 'failed':
      case 'needs update':
        return 'error';
      default:
        return 'default';
    }
  };

  // Define columns for data tables
  const certificationColumns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'name', label: 'Name', minWidth: 200 },
    { id: 'issuingBody', label: 'Issuing Body', minWidth: 200 },
    { id: 'issueDate', label: 'Issue Date', minWidth: 120 },
    { id: 'expiryDate', label: 'Expiry Date', minWidth: 120 },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={getStatusColor(value)}
        />
      )
    }
  ];

  const inspectionColumns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'type', label: 'Type', minWidth: 150 },
    { id: 'date', label: 'Date', minWidth: 120 },
    { id: 'inspector', label: 'Inspector', minWidth: 150 },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={getStatusColor(value)}
        />
      )
    },
    { id: 'findings', label: 'Findings', minWidth: 200 }
  ];

  const documentColumns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    { id: 'type', label: 'Type', minWidth: 120 },
    { id: 'issueDate', label: 'Issue Date', minWidth: 120 },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={getStatusColor(value)}
        />
      )
    },
    { id: 'issuedBy', label: 'Issued By', minWidth: 200 }
  ];

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: 'Valid Certifications',
      value: stats?.validCertifications || 0,
      icon: <VerifiedUser />,
      color: theme.palette.primary.main,
      trend: {
        value: 2,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Upcoming Inspections',
      value: stats?.upcomingInspections || 0,
      icon: <Assignment />,
      color: theme.palette.warning.main,
      trend: {
        value: 1,
        isPositive: false,
        label: "since last month"
      }
    },
    {
      label: 'Valid Documents',
      value: stats?.validDocuments || 0,
      icon: <Description />,
      color: theme.palette.success.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Expired Documents',
      value: stats?.expiredDocuments || 0,
      icon: <ErrorIcon />,
      color: theme.palette.error.main,
      trend: {
        value: 2,
        isPositive: false,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: 'Add Certification',
      icon: <Add />,
      onClick: () => handleOpenDialog('certification'),
      color: 'primary'
    },
    {
      label: 'Add Inspection',
      icon: <Add />,
      onClick: () => handleOpenDialog('inspection'),
      color: 'secondary'
    }
  ];

  return (
    <StandardDashboard
      title="Compliance Management"
      subtitle="Track and manage all compliance requirements for your livestock operation"
      icon={<VerifiedUser />}
      stats={dashboardStats}
      actions={dashboardActions}
      isLoading={loading}
      loadingMessage="Loading compliance data..."
      onRefresh={fetchComplianceData}
      module="compliance"
      tabs={[
        { label: 'Certifications', icon: <VerifiedUser /> },
        { label: 'Inspections', icon: <Assignment /> },
        { label: 'Documents', icon: <Description /> }
      ]}
      activeTab={activeTab}
      onTabChange={handleTabChange}
    >

      {/* Dashboard Stats */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="mb-8"
      >
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Certifications"
                subtitle="Track your compliance certifications"
                module="compliance"
                uniqueId="certifications-card"
                icon={<VerifiedUser />}
                actionLabel="View All"
                onAction={() => setActiveTab(0)}
                delay={0.1}
                height={180}
              >
                {loading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                    <CircularProgress size={24} />
                  </Box>
                ) : error ? (
                  <Typography color="error">Error loading data</Typography>
                ) : stats ? (
                  <Box mt={2}>
                    <Typography variant="body1">
                      <span className="font-bold text-success">{stats.validCertifications}</span> Valid
                    </Typography>
                    <Typography variant="body1">
                      <span className="font-bold text-warning">{stats.expiringCertifications}</span> Expiring Soon
                    </Typography>
                  </Box>
                ) : null}
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Inspections"
                subtitle="Track inspection records and findings"
                module="compliance"
                uniqueId="inspections-card"
                icon={<Assignment />}
                actionLabel="View All"
                onAction={() => setActiveTab(1)}
                delay={0.2}
                height={180}
              >
                {loading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                    <CircularProgress size={24} />
                  </Box>
                ) : error ? (
                  <Typography color="error">Error loading data</Typography>
                ) : stats ? (
                  <Box mt={2}>
                    <Typography variant="body1">
                      <span className="font-bold text-primary">{stats.upcomingInspections}</span> Upcoming
                    </Typography>
                  </Box>
                ) : null}
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={4}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Documents"
                subtitle="Manage compliance documentation"
                module="compliance"
                uniqueId="documents-card"
                icon={<Description />}
                actionLabel="View All"
                onAction={() => setActiveTab(2)}
                delay={0.3}
                height={180}
              >
                {loading ? (
                  <Box display="flex" justifyContent="center" alignItems="center" height="100%">
                    <CircularProgress size={24} />
                  </Box>
                ) : error ? (
                  <Typography color="error">Error loading data</Typography>
                ) : stats ? (
                  <Box mt={2}>
                    <Typography variant="body1">
                      <span className="font-bold text-success">{stats.validDocuments}</span> Valid
                    </Typography>
                    <Typography variant="body1">
                      <span className="font-bold text-error">{stats.expiredDocuments}</span> Expired
                    </Typography>
                  </Box>
                ) : null}
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>
        </Grid>
      </motion.div>

      {/* Tabs for different compliance areas */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          aria-label="compliance tabs"
          role="tablist"
        >
          <Tab
            label="Certifications"
            id="compliance-tab-0"
            aria-controls="compliance-tabpanel-0"
            role="tab"
          />
          <Tab
            label="Inspections"
            id="compliance-tab-1"
            aria-controls="compliance-tabpanel-1"
            role="tab"
          />
          <Tab
            label="Documents"
            id="compliance-tab-2"
            aria-controls="compliance-tabpanel-2"
            role="tab"
          />
        </Tabs>
      </Box>

      {/* Certifications Tab */}
      {activeTab === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          role="tabpanel"
          id="compliance-tabpanel-0"
          aria-labelledby="compliance-tab-0"
        >
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('certification')}
              aria-label="Add new certification"
            >
              Add Certification
            </CustomButton>
          </Box>

          <EnhancedDataTable
            title="Certifications"
            subtitle="Manage your compliance certifications"
            columns={certificationColumns}
            data={certifications || []}
            keyField="id"
            loading={loading}
            onRefresh={fetchComplianceData}
            onView={(row: any) => handleOpenDialog('certification', row)}
            onEdit={(row: any) => handleOpenDialog('certification', row)}
            onDelete={(row: any) => handleDelete(row.id)}
            searchable={true}
            searchFields={['name', 'issuingBody', 'status']}
            emptyMessage="No certifications found"
          />
        </motion.div>
      )}

      {/* Inspections Tab */}
      {activeTab === 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          role="tabpanel"
          id="compliance-tabpanel-1"
          aria-labelledby="compliance-tab-1"
        >
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('inspection')}
              aria-label="Add new inspection"
            >
              Add Inspection
            </CustomButton>
          </Box>

          <EnhancedDataTable
            title="Inspections"
            subtitle="Track inspection records and findings"
            columns={inspectionColumns}
            data={inspections || []}
            keyField="id"
            loading={loading}
            onRefresh={fetchComplianceData}
            onView={(row: any) => handleOpenDialog('inspection', row)}
            onEdit={(row: any) => handleOpenDialog('inspection', row)}
            onDelete={(row: any) => handleDelete(row.id)}
            searchable={true}
            searchFields={['type', 'inspector', 'status', 'findings']}
            emptyMessage="No inspections found"
          />
        </motion.div>
      )}

      {/* Documents Tab */}
      {activeTab === 2 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          role="tabpanel"
          id="compliance-tabpanel-2"
          aria-labelledby="compliance-tab-2"
        >
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => handleOpenDialog('document')}
              aria-label="Add new document"
            >
              Add Document
            </CustomButton>
          </Box>

          <EnhancedDataTable
            title="Documents"
            subtitle="Manage compliance documentation"
            columns={documentColumns}
            data={documents || []}
            keyField="id"
            loading={loading}
            onRefresh={fetchComplianceData}
            onView={(row: any) => handleOpenDialog('document', row)}
            onEdit={(row: any) => handleOpenDialog('document', row)}
            onDelete={(row: any) => handleDelete(row.id)}
            searchable={true}
            searchFields={['title', 'type', 'status', 'issuedBy']}
            emptyMessage="No documents found"
          />
        </motion.div>
      )}

      {/* Dialog for adding/editing items */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        ref={dialogRef}
        aria-labelledby="compliance-dialog-title"
        aria-describedby="compliance-dialog-description"
      >
        <DialogTitle id="compliance-dialog-title">
          {selectedItem ? `Edit ${dialogType}` : `Add New ${dialogType}`}
        </DialogTitle>
        <DialogContent dividers id="compliance-dialog-description">
          {dialogType === 'certification' && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Name"
                  fullWidth
                  defaultValue={selectedItem?.name || ''}
                  aria-required="true"
                  inputProps={{
                    'aria-label': 'Certification name'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Issuing Body"
                  fullWidth
                  defaultValue={selectedItem?.issuingBody || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    label="Status"
                    defaultValue={selectedItem?.status || 'valid'}
                  >
                    <MenuItem value="valid">Valid</MenuItem>
                    <MenuItem value="expired">Expired</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Issue Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.issueDate || new Date().toISOString().split('T')[0]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Expiry Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.expiryDate || ''}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Notes"
                  multiline
                  rows={4}
                  fullWidth
                  defaultValue={selectedItem?.notes || ''}
                />
              </Grid>
            </Grid>
          )}

          {dialogType === 'inspection' && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Type"
                  fullWidth
                  defaultValue={selectedItem?.type || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Inspector"
                  fullWidth
                  defaultValue={selectedItem?.inspector || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.date || new Date().toISOString().split('T')[0]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    label="Status"
                    defaultValue={selectedItem?.status || 'passed'}
                  >
                    <MenuItem value="passed">Passed</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Findings"
                  multiline
                  rows={4}
                  fullWidth
                  defaultValue={selectedItem?.findings || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Follow-up Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.followUpDate || ''}
                />
              </Grid>
            </Grid>
          )}

          {dialogType === 'document' && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  label="Title"
                  fullWidth
                  defaultValue={selectedItem?.title || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Type"
                  fullWidth
                  defaultValue={selectedItem?.type || ''}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    label="Status"
                    defaultValue={selectedItem?.status || 'valid'}
                  >
                    <MenuItem value="valid">Valid</MenuItem>
                    <MenuItem value="expired">Expired</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Issue Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.issueDate || new Date().toISOString().split('T')[0]}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Expiry Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  defaultValue={selectedItem?.expiryDate || ''}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Issued By"
                  fullWidth
                  defaultValue={selectedItem?.issuedBy || ''}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Notes"
                  multiline
                  rows={4}
                  fullWidth
                  defaultValue={selectedItem?.notes || ''}
                />
              </Grid>
              <Grid item xs={12}>
                <CustomButton
                  variant="outlined"
                  startIcon={<CloudDownload />}
                  fullWidth
                >
                  Upload Document
                </CustomButton>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <CustomButton
            onClick={handleCloseDialog}
            aria-label="Cancel and close dialog"
          >
            Cancel
          </CustomButton>
          <CustomButton
            variant="contained"
            onClick={handleCloseDialog}
            aria-label={selectedItem ? 'Save changes' : 'Add new item'}
          >
            {selectedItem ? 'Save Changes' : 'Add'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </StandardDashboard>
  );
};

export default ComplianceDashboard;
