import { useState, useEffect, useCallback } from 'react';
import useApiError from './useApiError';

interface FetchState<T> {
  data: T;
  loading: boolean;
  error: string | null;
  errors: Record<string, string> | null;
  isError: boolean;
  refetch: () => Promise<void>;
  clearError: () => void;
}

export function useDataFetching<T>(
  fetchFunction: () => Promise<T>,
  initialData: T,
  dependencies: any[] = []
): FetchState<T> {
  const [data, setData] = useState<T>(initialData);
  const [loading, setLoading] = useState<boolean>(true);
  const { error, errors, isError, handleError, clearError } = useApiError();

  const fetchData = useCallback(async () => {
    setLoading(true);
    clearError();
    try {
      const result = await fetchFunction();
      setData(result);
    } catch (err) {
      handleError(err);
      console.error('Error fetching data:', err);
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, clearError, handleError]);

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, dependencies);

  return {
    data,
    loading,
    error,
    errors,
    isError,
    refetch: fetchData,
    clearError
  };
}