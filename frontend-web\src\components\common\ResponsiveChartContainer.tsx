import React, { ReactNode, useState } from 'react';
import { <PERSON>, Card, CardContent, CardHeader, Typography, IconButton, Menu, MenuItem, useTheme, alpha, Skeleton } from '@mui/material';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import GetAppIcon from '@mui/icons-material/GetApp';
import RefreshIcon from '@mui/icons-material/Refresh';

interface TimeRange {
  label: string;
  value: string;
  days?: number;
}

interface ResponsiveChartContainerProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  height?: number | string;
  loading?: boolean;
  error?: string;
  timeRanges?: TimeRange[];
  onTimeRangeChange?: (range: TimeRange) => void;
  onRefresh?: () => void;
  onDownload?: () => void;
  onFullscreen?: () => void;
  accentColor?: string;
  backgroundImage?: string;
}

/**
 * ResponsiveChartContainer component
 * Provides a consistent, responsive container for charts and data visualizations
 */
const ResponsiveChartContainer: React.FC<ResponsiveChartContainerProps> = ({
  title,
  subtitle,
  children,
  height = 300,
  loading = false,
  error,
  timeRanges,
  onTimeRangeChange,
  onRefresh,
  onDownload,
  onFullscreen,
  accentColor,
  backgroundImage
}) => {
  const theme = useTheme();
  const { isMobile } = useResponsive();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange | null>(
    timeRanges && timeRanges.length > 0 ? timeRanges[0] : null
  );

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Time range change handler
  const handleTimeRangeChange = (range: TimeRange) => {
    setSelectedTimeRange(range);
    if (onTimeRangeChange) {
      onTimeRangeChange(range);
    }
    handleMenuClose();
  };

  // Calculate dynamic height based on screen size
  const chartHeight = typeof height === 'number' 
    ? isMobile ? height * 0.8 : height 
    : height;

  // Get primary color
  const primaryColor = accentColor || theme.palette.primary.main;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card
        sx={{
          height: '100%',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: `linear-gradient(to right, ${primaryColor}, ${alpha(primaryColor, 0.7)})`,
            zIndex: 1
          }
        }}
      >
        {/* Background Image with Overlay */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.05,
              zIndex: 0
            }}
          />
        )}

        {/* Card Header */}
        <CardHeader
          title={
            <Typography 
              variant="h6" 
              sx={{ 
                fontWeight: 600,
                fontSize: { xs: '1rem', sm: '1.1rem' }
              }}
            >
              {title}
            </Typography>
          }
          subheader={
            subtitle && (
              <Typography 
                variant="body2" 
                color="text.secondary"
                sx={{ mt: 0.5 }}
              >
                {subtitle}
              </Typography>
            )
          }
          action={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {/* Time Range Selector */}
              {timeRanges && timeRanges.length > 0 && (
                <Box sx={{ mr: 1 }}>
                  <Typography 
                    variant="body2" 
                    component="span" 
                    sx={{ 
                      cursor: 'pointer',
                      fontWeight: 500,
                      color: primaryColor,
                      display: 'flex',
                      alignItems: 'center',
                      '&:hover': { textDecoration: 'underline' }
                    }}
                    onClick={handleMenuOpen}
                  >
                    {selectedTimeRange?.label || 'Time Range'}
                  </Typography>
                  <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleMenuClose}
                  >
                    {timeRanges.map((range) => (
                      <MenuItem 
                        key={range.value} 
                        onClick={() => handleTimeRangeChange(range)}
                        selected={selectedTimeRange?.value === range.value}
                      >
                        {range.label}
                      </MenuItem>
                    ))}
                  </Menu>
                </Box>
              )}

              {/* Action Buttons */}
              {onRefresh && (
                <IconButton size="small" onClick={onRefresh} sx={{ ml: 0.5 }}>
                  <RefreshIcon fontSize="small" />
                </IconButton>
              )}
              {onDownload && (
                <IconButton size="small" onClick={onDownload} sx={{ ml: 0.5 }}>
                  <GetAppIcon fontSize="small" />
                </IconButton>
              )}
              {onFullscreen && (
                <IconButton size="small" onClick={onFullscreen} sx={{ ml: 0.5 }}>
                  <FullscreenIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          }
          sx={{
            position: 'relative',
            zIndex: 1,
            pb: 0
          }}
        />

        {/* Card Content */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 1,
            height: chartHeight,
            pt: 2,
            pb: '16px !important', // Override MUI's default padding
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          {loading ? (
            <Box sx={{ width: '100%', height: '100%' }}>
              <Skeleton variant="rectangular" width="100%" height="100%" animation="wave" />
            </Box>
          ) : error ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                width: '100%',
                p: 3,
                textAlign: 'center'
              }}
            >
              <Typography color="error" gutterBottom>
                {error}
              </Typography>
              {onRefresh && (
                <IconButton onClick={onRefresh} color="primary" sx={{ mt: 1 }}>
                  <RefreshIcon />
                </IconButton>
              )}
            </Box>
          ) : (
            <Box sx={{ width: '100%', height: '100%' }}>
              {children}
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ResponsiveChartContainer;
