import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, CardHeader, CircularProgress, Divider, List, ListItem, ListItemIcon, ListItemText, Typography, Alert } from '@mui/material';
import {
  Pets,
  Favorite,
  LocalHospital,
  CheckCircle,
  Error,
  ArrowForward
} from '@mui/icons-material';
import { migrateAnimalData, migrateBreedingData, migrateHealthData, migrateAllData } from '../../utils/dataMigration';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';

const DataMigration: React.FC = () => {
  const { isConnected } = useMongoDb();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [migrationStatus, setMigrationStatus] = useState({
    animals: false,
    breeding: false,
    health: false
  });

  const handleMigrateAnimals = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      await migrateAnimalData();
      setMigrationStatus(prev => ({ ...prev, animals: true }));
      setSuccess('Animal data migration completed successfully');
    } catch (err: unknown) {
      // Use type assertion to handle unknown error type
      const errorMessage = (err as Error).message || String(err);
      setError(`Error migrating animal data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateBreeding = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      await migrateBreedingData();
      setMigrationStatus(prev => ({ ...prev, breeding: true }));
      setSuccess('Breeding data migration completed successfully');
    } catch (err: unknown) {
      // Use type assertion to handle unknown error type
      const errorMessage = (err as Error).message || String(err);
      setError(`Error migrating breeding data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateHealth = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      await migrateHealthData();
      setMigrationStatus(prev => ({ ...prev, health: true }));
      setSuccess('Health data migration completed successfully');
    } catch (err: unknown) {
      // Use type assertion to handle unknown error type
      const errorMessage = (err as Error).message || String(err);
      setError(`Error migrating health data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateAll = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);
      await migrateAllData();
      setMigrationStatus({
        animals: true,
        breeding: true,
        health: true
      });
      setSuccess('All data migration completed successfully');
    } catch (err: unknown) {
      // Use type assertion to handle unknown error type
      const errorMessage = (err as Error).message || String(err);
      setError(`Error migrating data: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  if (!isConnected) {
    return (
      <Alert severity="warning">
        MongoDB is not connected. Please check your connection settings.
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Data Migration"
        subheader="Migrate mock data to MongoDB"
      />
      <Divider />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            {success}
          </Alert>
        )}

        <Typography variant="body1" paragraph>
          Use the buttons below to migrate mock data to MongoDB. This will only add data if the collection is empty.
        </Typography>

        <List>
          <ListItem>
            <ListItemIcon>
              {migrationStatus.animals ? <CheckCircle color="success" /> : <Pets />}
            </ListItemIcon>
            <ListItemText
              primary="Animal Data"
              secondary="Migrate animal profiles and records"
            />
            <CustomButton
              variant="contained"
              color="primary"
              onClick={handleMigrateAnimals}
              disabled={loading || migrationStatus.animals}
              endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
            >
              Migrate
            </CustomButton>
          </ListItem>

          <ListItem>
            <ListItemIcon>
              {migrationStatus.breeding ? <CheckCircle color="success" /> : <Favorite />}
            </ListItemIcon>
            <ListItemText
              primary="Breeding Data"
              secondary="Migrate breeding records and schedules"
            />
            <CustomButton
              variant="contained"
              color="primary"
              onClick={handleMigrateBreeding}
              disabled={loading || migrationStatus.breeding}
              endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
            >
              Migrate
            </CustomButton>
          </ListItem>

          <ListItem>
            <ListItemIcon>
              {migrationStatus.health ? <CheckCircle color="success" /> : <LocalHospital />}
            </ListItemIcon>
            <ListItemText
              primary="Health Data"
              secondary="Migrate health records and treatments"
            />
            <CustomButton
              variant="contained"
              color="primary"
              onClick={handleMigrateHealth}
              disabled={loading || migrationStatus.health}
              endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
            >
              Migrate
            </CustomButton>
          </ListItem>
        </List>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
          <CustomButton
            variant="contained"
            color="secondary"
            size="large"
            onClick={handleMigrateAll}
            disabled={loading || (migrationStatus.animals && migrationStatus.breeding && migrationStatus.health)}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {loading ? 'Migrating...' : 'Migrate All Data'}
          </CustomButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default DataMigration;
