/**
 * Create Admin User
 * 
 * This script creates a default admin user in the MongoDB database.
 * Run with: node scripts/create-admin-user.js
 */

require('dotenv').config();
const mongodb = require('../src/config/mongodb');
const bcrypt = require('bcryptjs');
const logger = require('../src/utils/logger');

// Default admin user
const adminUser = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'Admin@123',
  firstName: 'May',
  lastName: 'Rakgama',
  role: 'admin',
  permissions: ['*'], // All permissions
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
};

// Hash password
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

// Create admin user
async function createAdminUser() {
  try {
    logger.info('Creating admin user...');
    
    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB');
    
    // Check if users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      logger.info('Users collection does not exist, creating...');
      await db.createCollection('users');
      
      // Create index on username and email
      await db.collection('users').createIndex({ username: 1 }, { unique: true });
      await db.collection('users').createIndex({ email: 1 }, { unique: true });
    }
    
    // Check if admin user already exists
    const existingUser = await db.collection('users').findOne({ username: adminUser.username });
    if (existingUser) {
      logger.info(`Admin user '${adminUser.username}' already exists`);
      
      // Update admin user if needed
      if (existingUser.role !== 'admin' || !existingUser.isActive) {
        logger.info('Updating admin user role and status...');
        await db.collection('users').updateOne(
          { username: adminUser.username },
          { $set: { role: 'admin', isActive: true, updatedAt: new Date() } }
        );
        logger.info('Admin user updated successfully');
      }
    } else {
      // Hash password
      adminUser.password = await hashPassword(adminUser.password);
      
      // Insert admin user
      await db.collection('users').insertOne(adminUser);
      logger.info(`Admin user '${adminUser.username}' created successfully`);
    }
    
    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    
    return true;
  } catch (error) {
    logger.error('Error creating admin user:', error);
    return false;
  }
}

// Run the script
createAdminUser()
  .then(success => {
    if (success) {
      logger.info('Admin user creation completed successfully');
      process.exit(0);
    } else {
      logger.error('Admin user creation failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during admin user creation:', error);
    process.exit(1);
  });
