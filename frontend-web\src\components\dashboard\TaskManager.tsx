import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  <PERSON>Field, 
  IconButton, 
  Chip, 
  Divider, 
  useTheme, 
  alpha, 
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Checkbox,
  FormControlLabel,
  Grid,
  Tooltip,
  InputAdornment
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { motion, AnimatePresence } from 'framer-motion';
// Removed useThemeContext import as it was deleted
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  RadioButtonUnchecked as UncheckedIcon,
  Flag as FlagIcon,
  Sort as SortIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  CalendarToday as CalendarIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { CustomButton } from '../common';

// Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  dueDate: Date | null;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  category: string;
  assignedTo?: string;
  createdAt: Date;
}

// Props interface
interface TaskManagerProps {
  initialTasks?: Task[];
  onTaskUpdate?: (tasks: Task[]) => void;
}

/**
 * TaskManager Component
 * 
 * A comprehensive task management system for the dashboard
 */
const TaskManager: React.FC<TaskManagerProps> = ({ 
  initialTasks = [],
  onTaskUpdate
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // State for tasks
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>(initialTasks);
  
  // State for task dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [newTask, setNewTask] = useState<Partial<Task>>({
    title: '',
    description: '',
    dueDate: null,
    priority: 'medium',
    completed: false,
    category: 'general',
    createdAt: new Date()
  });
  
  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [showCompleted, setShowCompleted] = useState(true);
  const [sortBy, setSortBy] = useState<'dueDate' | 'priority' | 'createdAt'>('dueDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // Categories for tasks
  const categories = [
    'general',
    'animals',
    'health',
    'breeding',
    'feeding',
    'financial',
    'maintenance'
  ];
  
  // Effect to apply filters and sorting
  useEffect(() => {
    let result = [...tasks];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(task => 
        task.title.toLowerCase().includes(query) || 
        task.description.toLowerCase().includes(query)
      );
    }
    
    // Apply priority filter
    if (priorityFilter !== 'all') {
      result = result.filter(task => task.priority === priorityFilter);
    }
    
    // Apply category filter
    if (categoryFilter !== 'all') {
      result = result.filter(task => task.category === categoryFilter);
    }
    
    // Apply completed filter
    if (!showCompleted) {
      result = result.filter(task => !task.completed);
    }
    
    // Apply sorting
    result.sort((a, b) => {
      if (sortBy === 'dueDate') {
        if (!a.dueDate) return sortDirection === 'asc' ? 1 : -1;
        if (!b.dueDate) return sortDirection === 'asc' ? -1 : 1;
        return sortDirection === 'asc' 
          ? a.dueDate.getTime() - b.dueDate.getTime()
          : b.dueDate.getTime() - a.dueDate.getTime();
      } else if (sortBy === 'priority') {
        const priorityValues = { low: 1, medium: 2, high: 3 };
        return sortDirection === 'asc'
          ? priorityValues[a.priority] - priorityValues[b.priority]
          : priorityValues[b.priority] - priorityValues[a.priority];
      } else {
        return sortDirection === 'asc'
          ? a.createdAt.getTime() - b.createdAt.getTime()
          : b.createdAt.getTime() - a.createdAt.getTime();
      }
    });
    
    setFilteredTasks(result);
  }, [tasks, searchQuery, priorityFilter, categoryFilter, showCompleted, sortBy, sortDirection]);
  
  // Notify parent component when tasks change
  useEffect(() => {
    if (onTaskUpdate) {
      onTaskUpdate(tasks);
    }
  }, [tasks, onTaskUpdate]);
  
  // Handle opening the dialog for a new task
  const handleAddTask = () => {
    setEditingTask(null);
    setNewTask({
      title: '',
      description: '',
      dueDate: null,
      priority: 'medium',
      completed: false,
      category: 'general',
      createdAt: new Date()
    });
    setDialogOpen(true);
  };
  
  // Handle opening the dialog for editing a task
  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setNewTask({ ...task });
    setDialogOpen(true);
  };
  
  // Handle saving a task (new or edited)
  const handleSaveTask = () => {
    if (!newTask.title) return;
    
    if (editingTask) {
      // Update existing task
      setTasks(prevTasks => 
        prevTasks.map(task => 
          task.id === editingTask.id 
            ? { ...task, ...newTask as Task } 
            : task
        )
      );
    } else {
      // Add new task
      const task: Task = {
        id: Date.now().toString(),
        title: newTask.title || '',
        description: newTask.description || '',
        dueDate: newTask.dueDate,
        priority: newTask.priority as 'low' | 'medium' | 'high',
        completed: newTask.completed || false,
        category: newTask.category || 'general',
        createdAt: new Date()
      };
      setTasks(prevTasks => [...prevTasks, task]);
    }
    
    setDialogOpen(false);
  };
  
  // Handle deleting a task
  const handleDeleteTask = (id: string) => {
    setTasks(prevTasks => prevTasks.filter(task => task.id !== id));
  };
  
  // Handle toggling task completion
  const handleToggleComplete = (id: string) => {
    setTasks(prevTasks => 
      prevTasks.map(task => 
        task.id === id 
          ? { ...task, completed: !task.completed } 
          : task
      )
    );
  };
  
  // Handle input changes for the new/edited task
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewTask(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle select changes for the new/edited task
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setNewTask(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle checkbox changes for the new/edited task
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setNewTask(prev => ({ ...prev, [name]: checked }));
  };
  
  // Handle date changes for the new/edited task
  const handleDateChange = (date: Date | null) => {
    setNewTask(prev => ({ ...prev, dueDate: date }));
  };
  
  // Get color for priority
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return theme.palette.error.main;
      case 'medium': return theme.palette.warning.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.info.main;
    }
  };
  
  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString();
  };
  
  // Get days remaining until due date
  const getDaysRemaining = (dueDate: Date | null) => {
    if (!dueDate) return null;
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const due = new Date(dueDate);
    due.setHours(0, 0, 0, 0);
    
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays;
  };
  
  return (
    <Box>
      {/* Task controls */}
      <Box sx={{ mb: 2, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 1, alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <TextField
            placeholder={translate ? translate('tasks.search', { fallback: 'Search tasks...' }) : 'Search tasks...'}
            size="small"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 200 }}
          />
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="priority-filter-label">
              {translate ? translate('tasks.priority', { fallback: 'Priority' }) : 'Priority'}
            </InputLabel>
            <Select
              labelId="priority-filter-label"
              value={priorityFilter}
              label={translate ? translate('tasks.priority', { fallback: 'Priority' }) : 'Priority'}
              onChange={(e) => setPriorityFilter(e.target.value)}
            >
              <MenuItem value="all">{translate ? translate('tasks.all', { fallback: 'All' }) : 'All'}</MenuItem>
              <MenuItem value="high">{translate ? translate('tasks.high', { fallback: 'High' }) : 'High'}</MenuItem>
              <MenuItem value="medium">{translate ? translate('tasks.medium', { fallback: 'Medium' }) : 'Medium'}</MenuItem>
              <MenuItem value="low">{translate ? translate('tasks.low', { fallback: 'Low' }) : 'Low'}</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="category-filter-label">
              {translate ? translate('tasks.category', { fallback: 'Category' }) : 'Category'}
            </InputLabel>
            <Select
              labelId="category-filter-label"
              value={categoryFilter}
              label={translate ? translate('tasks.category', { fallback: 'Category' }) : 'Category'}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <MenuItem value="all">{translate ? translate('tasks.all', { fallback: 'All' }) : 'All'}</MenuItem>
              {categories.map(category => (
                <MenuItem key={category} value={category}>
                  {translate ? translate(`tasks.categories.${category}`, { fallback: category.charAt(0).toUpperCase() + category.slice(1) }) : category.charAt(0).toUpperCase() + category.slice(1)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
          <FormControlLabel
            control={
              <Checkbox 
                checked={showCompleted} 
                onChange={(e) => setShowCompleted(e.target.checked)}
                size="small"
              />
            }
            label={
              <Typography variant="body2">
                {translate ? translate('tasks.show_completed', { fallback: 'Show Completed' }) : 'Show Completed'}
              </Typography>
            }
          />
          
          <Tooltip title={translate ? translate('tasks.add', { fallback: 'Add Task' }) : 'Add Task'}>
            <CustomButton
              startIcon={<AddIcon />}
              onClick={handleAddTask}
              size="small"
            >
              {translate ? translate('tasks.add', { fallback: 'Add Task' }) : 'Add Task'}
            </CustomButton>
          </Tooltip>
        </Box>
      </Box>
      
      {/* Task list */}
      <Box>
        {filteredTasks.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="text.secondary">
              {translate ? translate('tasks.no_tasks', { fallback: 'No tasks found' }) : 'No tasks found'}
            </Typography>
          </Box>
        ) : (
          <AnimatePresence>
            {filteredTasks.map((task) => (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Box
                  sx={{
                    mb: 1,
                    p: 2,
                    borderRadius: 2,
                    bgcolor: task.completed 
                      ? alpha(theme.palette.success.main, 0.05)
                      : alpha(theme.palette.background.paper, 0.5),
                    border: `1px solid ${alpha(
                      task.completed ? theme.palette.success.main : getPriorityColor(task.priority),
                      0.1
                    )}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.background.paper, 0.8),
                      transform: 'translateY(-2px)',
                      boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.05)}`
                    },
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {/* Priority indicator */}
                  <Box
                    sx={{
                      position: 'absolute',
                      left: 0,
                      top: 0,
                      bottom: 0,
                      width: 4,
                      bgcolor: task.completed ? theme.palette.success.main : getPriorityColor(task.priority)
                    }}
                  />
                  
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                    {/* Completion toggle */}
                    <IconButton 
                      size="small" 
                      onClick={() => handleToggleComplete(task.id)}
                      sx={{ 
                        color: task.completed ? theme.palette.success.main : 'text.secondary',
                        mt: 0.5
                      }}
                    >
                      {task.completed ? <CheckCircleIcon /> : <UncheckedIcon />}
                    </IconButton>
                    
                    {/* Task content */}
                    <Box sx={{ flex: 1 }}>
                      <Typography 
                        variant="subtitle1" 
                        sx={{ 
                          textDecoration: task.completed ? 'line-through' : 'none',
                          color: task.completed ? 'text.secondary' : 'text.primary',
                          fontWeight: task.completed ? 'normal' : 'medium'
                        }}
                      >
                        {task.title}
                      </Typography>
                      
                      {task.description && (
                        <Typography 
                          variant="body2" 
                          color="text.secondary"
                          sx={{ 
                            mt: 0.5,
                            textDecoration: task.completed ? 'line-through' : 'none',
                          }}
                        >
                          {task.description}
                        </Typography>
                      )}
                      
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                        {/* Category chip */}
                        <Chip
                          label={translate ? translate(`tasks.categories.${task.category}`, { fallback: task.category.charAt(0).toUpperCase() + task.category.slice(1) }) : task.category.charAt(0).toUpperCase() + task.category.slice(1)}
                          size="small"
                          sx={{ 
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main
                          }}
                        />
                        
                        {/* Priority chip */}
                        <Chip
                          icon={<FlagIcon fontSize="small" />}
                          label={translate ? translate(`tasks.${task.priority}`, { fallback: task.priority.charAt(0).toUpperCase() + task.priority.slice(1) }) : task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                          size="small"
                          sx={{ 
                            bgcolor: alpha(getPriorityColor(task.priority), 0.1),
                            color: getPriorityColor(task.priority)
                          }}
                        />
                        
                        {/* Due date chip */}
                        {task.dueDate && (
                          <Chip
                            icon={<CalendarIcon fontSize="small" />}
                            label={formatDate(task.dueDate)}
                            size="small"
                            sx={{ 
                              bgcolor: alpha(theme.palette.info.main, 0.1),
                              color: theme.palette.info.main
                            }}
                          />
                        )}
                        
                        {/* Days remaining */}
                        {task.dueDate && !task.completed && (
                          <Chip
                            label={
                              getDaysRemaining(task.dueDate) === 0
                                ? translate ? translate('tasks.due_today', { fallback: 'Due Today' }) : 'Due Today'
                                : getDaysRemaining(task.dueDate) && getDaysRemaining(task.dueDate)! < 0
                                  ? `${Math.abs(getDaysRemaining(task.dueDate)!)} ${translate ? translate('tasks.days_overdue', { fallback: 'days overdue' }) : 'days overdue'}`
                                  : `${getDaysRemaining(task.dueDate)} ${translate ? translate('tasks.days_remaining', { fallback: 'days remaining' }) : 'days remaining'}`
                            }
                            size="small"
                            sx={{ 
                              bgcolor: alpha(
                                getDaysRemaining(task.dueDate) === 0
                                  ? theme.palette.warning.main
                                  : getDaysRemaining(task.dueDate) && getDaysRemaining(task.dueDate)! < 0
                                    ? theme.palette.error.main
                                    : getDaysRemaining(task.dueDate) && getDaysRemaining(task.dueDate)! <= 2
                                      ? theme.palette.warning.main
                                      : theme.palette.success.main,
                                0.1
                              ),
                              color: getDaysRemaining(task.dueDate) === 0
                                ? theme.palette.warning.main
                                : getDaysRemaining(task.dueDate) && getDaysRemaining(task.dueDate)! < 0
                                  ? theme.palette.error.main
                                  : getDaysRemaining(task.dueDate) && getDaysRemaining(task.dueDate)! <= 2
                                    ? theme.palette.warning.main
                                    : theme.palette.success.main
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                    
                    {/* Task actions */}
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <IconButton 
                        size="small" 
                        onClick={() => handleEditTask(task)}
                        sx={{ color: 'text.secondary' }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton 
                        size="small" 
                        onClick={() => handleDeleteTask(task.id)}
                        sx={{ color: theme.palette.error.main }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                </Box>
              </motion.div>
            ))}
          </AnimatePresence>
        )}
      </Box>
      
      {/* Task dialog */}
      <Dialog 
        open={dialogOpen} 
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingTask 
            ? translate ? translate('tasks.edit_task', { fallback: 'Edit Task' }) : 'Edit Task'
            : translate ? translate('tasks.new_task', { fallback: 'New Task' }) : 'New Task'
          }
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 0.5 }}>
            <Grid item xs={12}>
              <TextField
                name="title"
                label={translate ? translate('tasks.title', { fallback: 'Title' }) : 'Title'}
                value={newTask.title || ''}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label={translate ? translate('tasks.description', { fallback: 'Description' }) : 'Description'}
                value={newTask.description || ''}
                onChange={handleInputChange}
                fullWidth
                multiline
                rows={3}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="priority-label">
                  {translate ? translate('tasks.priority', { fallback: 'Priority' }) : 'Priority'}
                </InputLabel>
                <Select
                  labelId="priority-label"
                  name="priority"
                  value={newTask.priority || 'medium'}
                  label={translate ? translate('tasks.priority', { fallback: 'Priority' }) : 'Priority'}
                  onChange={handleSelectChange}
                >
                  <MenuItem value="high">{translate ? translate('tasks.high', { fallback: 'High' }) : 'High'}</MenuItem>
                  <MenuItem value="medium">{translate ? translate('tasks.medium', { fallback: 'Medium' }) : 'Medium'}</MenuItem>
                  <MenuItem value="low">{translate ? translate('tasks.low', { fallback: 'Low' }) : 'Low'}</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="category-label">
                  {translate ? translate('tasks.category', { fallback: 'Category' }) : 'Category'}
                </InputLabel>
                <Select
                  labelId="category-label"
                  name="category"
                  value={newTask.category || 'general'}
                  label={translate ? translate('tasks.category', { fallback: 'Category' }) : 'Category'}
                  onChange={handleSelectChange}
                >
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>
                      {translate ? translate(`tasks.categories.${category}`, { fallback: category.charAt(0).toUpperCase() + category.slice(1) }) : category.charAt(0).toUpperCase() + category.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DatePicker
                  label={translate ? translate('tasks.due_date', { fallback: 'Due Date' }) : 'Due Date'}
                  value={newTask.dueDate}
                  onChange={handleDateChange}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </LocalizationProvider>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox 
                    name="completed" 
                    checked={newTask.completed || false} 
                    onChange={handleCheckboxChange}
                  />
                }
                label={translate ? translate('tasks.completed', { fallback: 'Completed' }) : 'Completed'}
                sx={{ height: '100%', display: 'flex', alignItems: 'center' }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)} color="inherit">
            {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
          </Button>
          <Button onClick={handleSaveTask} variant="contained" color="primary">
            {translate ? translate('common.save', { fallback: 'Save' }) : 'Save'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TaskManager;
