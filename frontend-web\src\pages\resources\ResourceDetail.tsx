import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Paper } from '@mui/material';

const ResourceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <Box p={4}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5">Resource Detail</Typography>
        <Typography variant="body1" mt={2}>
          Viewing details for resource ID: {id}
        </Typography>
      </Paper>
    </Box>
  );
};

export default ResourceDetail;
