/**
 * Payment Service for AgriIntel
 * Handles subscription payments, billing, and payment processing
 */

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account' | 'mobile_money';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
  billingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
}

interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  animalLimit: number;
  storageLimit: string;
  supportLevel: 'basic' | 'premium' | 'enterprise';
  popular?: boolean;
}

interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'processing' | 'succeeded' | 'canceled';
  clientSecret: string;
  description?: string;
  metadata?: Record<string, string>;
}

interface Invoice {
  id: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible';
  dueDate: string;
  paidAt?: string;
  invoiceUrl: string;
  lineItems: {
    description: string;
    amount: number;
    quantity: number;
  }[];
}

interface Subscription {
  id: string;
  planId: string;
  status: 'active' | 'past_due' | 'canceled' | 'unpaid';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  trialEnd?: string;
  paymentMethodId?: string;
}

class PaymentService {
  private stripePublishableKey: string;
  private apiUrl: string;

  constructor() {
    this.stripePublishableKey = process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_demo';
    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
  }

  /**
   * Initialize Stripe
   */
  async initializeStripe() {
    if (typeof window !== 'undefined' && window.Stripe) {
      return window.Stripe(this.stripePublishableKey);
    }
    
    // Load Stripe.js dynamically
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    document.head.appendChild(script);
    
    return new Promise((resolve) => {
      script.onload = () => {
        resolve(window.Stripe(this.stripePublishableKey));
      };
    });
  }

  /**
   * Get available subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/plans`);
      if (!response.ok) {
        throw new Error('Failed to fetch subscription plans');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      return this.getMockPlans();
    }
  }

  /**
   * Create payment intent for subscription
   */
  async createPaymentIntent(planId: string, paymentMethodId?: string): Promise<PaymentIntent> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/create-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          planId,
          paymentMethodId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create payment intent');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating payment intent:', error);
      throw error;
    }
  }

  /**
   * Confirm payment
   */
  async confirmPayment(paymentIntentId: string, paymentMethodId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const stripe = await this.initializeStripe();
      
      const { error, paymentIntent } = await stripe.confirmCardPayment(paymentIntentId, {
        payment_method: paymentMethodId
      });

      if (error) {
        return { success: false, error: error.message };
      }

      if (paymentIntent.status === 'succeeded') {
        return { success: true };
      }

      return { success: false, error: 'Payment not completed' };
    } catch (error) {
      console.error('Error confirming payment:', error);
      return { success: false, error: 'Payment confirmation failed' };
    }
  }

  /**
   * Get user's payment methods
   */
  async getPaymentMethods(): Promise<PaymentMethod[]> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/payment-methods`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payment methods');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching payment methods:', error);
      return [];
    }
  }

  /**
   * Add new payment method
   */
  async addPaymentMethod(paymentMethodData: any): Promise<{ success: boolean; paymentMethod?: PaymentMethod; error?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/payment-methods`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(paymentMethodData)
      });

      if (!response.ok) {
        throw new Error('Failed to add payment method');
      }

      const paymentMethod = await response.json();
      return { success: true, paymentMethod };
    } catch (error) {
      console.error('Error adding payment method:', error);
      return { success: false, error: 'Failed to add payment method' };
    }
  }

  /**
   * Get current subscription
   */
  async getCurrentSubscription(): Promise<Subscription | null> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/subscription`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No subscription
        }
        throw new Error('Failed to fetch subscription');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching subscription:', error);
      return null;
    }
  }

  /**
   * Subscribe to a plan
   */
  async subscribeToPlan(planId: string, paymentMethodId: string): Promise<{ success: boolean; subscription?: Subscription; error?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/subscribe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          planId,
          paymentMethodId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create subscription');
      }

      const subscription = await response.json();
      return { success: true, subscription };
    } catch (error) {
      console.error('Error creating subscription:', error);
      return { success: false, error: 'Failed to create subscription' };
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/subscription/${subscriptionId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          cancelAtPeriodEnd
        })
      });

      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }

      return { success: true };
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return { success: false, error: 'Failed to cancel subscription' };
    }
  }

  /**
   * Get billing history
   */
  async getBillingHistory(): Promise<Invoice[]> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/invoices`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch billing history');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching billing history:', error);
      return [];
    }
  }

  /**
   * Update subscription plan
   */
  async updateSubscriptionPlan(subscriptionId: string, newPlanId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/subscription/${subscriptionId}/update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          planId: newPlanId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update subscription');
      }

      return { success: true };
    } catch (error) {
      console.error('Error updating subscription:', error);
      return { success: false, error: 'Failed to update subscription' };
    }
  }

  /**
   * Process mobile money payment (for South African market)
   */
  async processMobileMoneyPayment(amount: number, phoneNumber: string, provider: 'vodacom' | 'mtn' | 'cellc'): Promise<{ success: boolean; transactionId?: string; error?: string }> {
    try {
      const response = await fetch(`${this.apiUrl}/payments/mobile-money`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          amount,
          phoneNumber,
          provider
        })
      });

      if (!response.ok) {
        throw new Error('Failed to process mobile money payment');
      }

      const result = await response.json();
      return { success: true, transactionId: result.transactionId };
    } catch (error) {
      console.error('Error processing mobile money payment:', error);
      return { success: false, error: 'Mobile money payment failed' };
    }
  }

  /**
   * Get mock subscription plans for demo
   */
  private getMockPlans(): SubscriptionPlan[] {
    return [
      {
        id: 'plan_beta',
        name: 'Beta',
        description: 'Perfect for small farms getting started',
        price: 0,
        currency: 'ZAR',
        interval: 'month',
        features: [
          'Up to 50 animals',
          'Basic health tracking',
          'Simple reports',
          'Email support'
        ],
        animalLimit: 50,
        storageLimit: '1GB',
        supportLevel: 'basic'
      },
      {
        id: 'plan_professional',
        name: 'Professional',
        description: 'For growing farms with advanced needs',
        price: 299,
        currency: 'ZAR',
        interval: 'month',
        features: [
          'Unlimited animals',
          'Advanced analytics',
          'Custom reports',
          'Priority support',
          'Mobile app access'
        ],
        animalLimit: -1,
        storageLimit: '10GB',
        supportLevel: 'premium',
        popular: true
      },
      {
        id: 'plan_enterprise',
        name: 'Enterprise',
        description: 'For large operations with premium features',
        price: 599,
        currency: 'ZAR',
        interval: 'month',
        features: [
          'Everything in Professional',
          'AI-powered insights',
          'API access',
          'Dedicated support',
          'Custom integrations',
          'Multi-farm management'
        ],
        animalLimit: -1,
        storageLimit: 'Unlimited',
        supportLevel: 'enterprise'
      }
    ];
  }
}

// Create singleton instance
const paymentService = new PaymentService();

export default paymentService;
export type { PaymentMethod, SubscriptionPlan, PaymentIntent, Invoice, Subscription };
