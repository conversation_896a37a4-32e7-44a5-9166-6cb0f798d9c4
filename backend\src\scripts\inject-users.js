/**
 * <PERSON>ript to inject users into MongoDB
 *
 * This script creates default users in the MongoDB database
 * Run with: node src/scripts/inject-users.js
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const DB_NAME = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Update hashPassword function to use bcrypt
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
};

// Users to inject
const users = [
  // Admin Users
  {
    username: 'mayrakgama',
    password: '<PERSON><PERSON>@1951ke*#',
    email: '<EMAIL>',
    firstName: 'May',
    lastName: 'Rakgama',
    role: 'admin',
    status: 'active',
    permissions: ['all'],
    department: 'Management',
    position: 'Owner',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    username: 'peterbotha',
    password: 'Admin@2023',
    email: '<EMAIL>',
    firstName: 'Peter',
    lastName: 'Botha',
    role: 'admin',
    status: 'active',
    permissions: ['all'],
    department: 'IT',
    position: 'System Administrator',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Manager Users
  {
    username: 'johnsmith',
    password: 'Manager@2023',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Smith',
    role: 'manager',
    status: 'active',
    permissions: [
      'animals:read', 'animals:write',
      'health:read', 'health:write',
      'breeding:read', 'breeding:write',
      'feeding:read', 'feeding:write',
      'financial:read', 'financial:write',
      'reports:read', 'reports:write'
    ],
    department: 'Operations',
    position: 'Operations Manager',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    username: 'davidmokoena',
    password: 'Manager@2024',
    email: '<EMAIL>',
    firstName: 'David',
    lastName: 'Mokoena',
    role: 'manager',
    status: 'active',
    permissions: [
      'animals:read', 'animals:write',
      'health:read', 'health:write',
      'breeding:read', 'breeding:write',
      'feeding:read', 'feeding:write',
      'financial:read', 'financial:write',
      'reports:read', 'reports:write'
    ],
    department: 'Farm Management',
    position: 'Farm Manager',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Super Users
  {
    username: 'sarahndlovu',
    password: 'Super@2023',
    email: '<EMAIL>',
    firstName: 'Sarah',
    lastName: 'Ndlovu',
    role: 'super_user',
    status: 'active',
    permissions: [
      'animals:read', 'animals:write',
      'health:read', 'health:write',
      'breeding:read', 'breeding:write',
      'feeding:read', 'feeding:write',
      'financial:read',
      'reports:read'
    ],
    department: 'Animal Health',
    position: 'Senior Supervisor',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    username: 'thembaventer',
    password: 'Super@2024',
    email: '<EMAIL>',
    firstName: 'Themba',
    lastName: 'Venter',
    role: 'super_user',
    status: 'active',
    permissions: [
      'animals:read', 'animals:write',
      'health:read', 'health:write',
      'breeding:read', 'breeding:write',
      'feeding:read', 'feeding:write',
      'financial:read',
      'reports:read'
    ],
    department: 'Breeding',
    position: 'Breeding Specialist',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    username: 'lindiwemolefe',
    password: 'Super@2025',
    email: '<EMAIL>',
    firstName: 'Lindiwe',
    lastName: 'Molefe',
    role: 'super_user',
    status: 'active',
    permissions: [
      'animals:read', 'animals:write',
      'health:read', 'health:write',
      'breeding:read', 'breeding:write',
      'feeding:read', 'feeding:write',
      'financial:read',
      'reports:read'
    ],
    department: 'Feeding',
    position: 'Nutrition Specialist',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // Default System User (for backward compatibility)
  {
    username: 'AMPD',
    password: '123',
    email: '<EMAIL>',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin',
    status: 'active',
    permissions: ['all'],
    department: 'System',
    position: 'System Administrator',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  // MayCaiphus User (for backward compatibility)
  {
    username: 'MayCaiphus',
    password: '123',
    email: '<EMAIL>',
    firstName: 'MayCaiphus',
    lastName: 'System',
    role: 'admin',
    status: 'active',
    permissions: ['all'],
    department: 'System',
    position: 'System Administrator',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Update user passwords to use bcrypt hashing
(async () => {
  for (const user of users) {
    user.password = await hashPassword(user.password);
  }
})();

// Connect to MongoDB and inject users
async function injectUsers() {
  let client;

  try {
    // Connect to MongoDB
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database and collection
    const db = client.db(DB_NAME);
    const usersCollection = db.collection('users');

    // Check if users already exist
    const existingUsers = await usersCollection.find({}).toArray();
    if (existingUsers.length > 0) {
      logger.info(`Found ${existingUsers.length} existing users`);
    }

    // Insert or update each user
    for (const user of users) {
      const existingUser = await usersCollection.findOne({ username: user.username });

      if (existingUser) {
        // Update existing user
        await usersCollection.updateOne(
          { username: user.username },
          { $set: { ...user, updatedAt: new Date() } }
        );
        logger.info(`Updated user: ${user.username}`);
      } else {
        // Insert new user
        await usersCollection.insertOne(user);
        logger.info(`Inserted user: ${user.username}`);
      }
    }

    logger.info('User injection completed successfully');
  } catch (error) {
    logger.error('Error injecting users:', error);
  } finally {
    // Close MongoDB connection
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection
injectUsers()
  .then(() => {
    console.log('User injection completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error during user injection:', error);
    process.exit(1);
  });
