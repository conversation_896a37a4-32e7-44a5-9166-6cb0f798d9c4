/**
 * Weather-Based Livestock Advisor
 * 
 * This component provides weather-based recommendations for livestock management
 * Features:
 * - Real-time weather data integration
 * - Livestock-specific weather alerts
 * - Seasonal management recommendations
 * - Climate adaptation strategies
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  LinearProgress,
  Avatar
} from '@mui/material';
import {
  WaterDrop,
  Thermostat,
  Warning,
  CheckCircle,
  Agriculture,
  Pets,
  Umbrella,
  WbSunny,
  AcUnit,
  Air
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface WeatherData {
  temperature: number;
  humidity: number;
  rainfall: number;
  windSpeed: number;
  condition: string;
  forecast: string[];
}

interface LivestockRecommendation {
  id: string;
  type: 'feeding' | 'shelter' | 'health' | 'breeding' | 'grazing';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  action: string;
  icon: React.ReactNode;
  weatherTrigger: string;
}

interface WeatherLivestockAdvisorProps {
  compact?: boolean;
  animalTypes?: string[];
}

const WeatherLivestockAdvisor: React.FC<WeatherLivestockAdvisorProps> = ({
  compact = false,
  animalTypes = ['cattle', 'sheep', 'goats']
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [recommendations, setRecommendations] = useState<LivestockRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Mock weather data (in production, this would come from a weather API)
  useEffect(() => {
    const fetchWeatherData = () => {
      // Simulate weather API call
      const mockWeather: WeatherData = {
        temperature: 28 + Math.random() * 10, // 28-38°C
        humidity: 60 + Math.random() * 30, // 60-90%
        rainfall: Math.random() * 20, // 0-20mm
        windSpeed: 5 + Math.random() * 15, // 5-20 km/h
        condition: ['sunny', 'cloudy', 'rainy', 'windy'][Math.floor(Math.random() * 4)],
        forecast: ['Hot', 'Humid', 'Dry', 'Windy', 'Mild']
      };

      setWeatherData(mockWeather);
      generateRecommendations(mockWeather);
      setIsLoading(false);
    };

    fetchWeatherData();
    // Update every 30 minutes
    const interval = setInterval(fetchWeatherData, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const generateRecommendations = (weather: WeatherData) => {
    const newRecommendations: LivestockRecommendation[] = [];

    // Temperature-based recommendations
    if (weather.temperature > 35) {
      newRecommendations.push({
        id: 'heat-stress',
        type: 'shelter',
        priority: 'high',
        title: 'Heat Stress Prevention',
        description: `Temperature is ${weather.temperature.toFixed(1)}°C - risk of heat stress`,
        action: 'Provide extra shade and increase water availability',
        icon: <Thermostat />,
        weatherTrigger: 'High Temperature'
      });
    }

    if (weather.temperature < 10) {
      newRecommendations.push({
        id: 'cold-protection',
        type: 'shelter',
        priority: 'high',
        title: 'Cold Weather Protection',
        description: `Low temperature of ${weather.temperature.toFixed(1)}°C requires protection`,
        action: 'Provide windbreaks and additional bedding',
        icon: <AcUnit />,
        weatherTrigger: 'Low Temperature'
      });
    }

    // Humidity-based recommendations
    if (weather.humidity > 80) {
      newRecommendations.push({
        id: 'high-humidity',
        type: 'health',
        priority: 'medium',
        title: 'High Humidity Management',
        description: `Humidity at ${weather.humidity.toFixed(0)}% increases disease risk`,
        action: 'Improve ventilation and monitor for respiratory issues',
        icon: <WaterDrop />,
        weatherTrigger: 'High Humidity'
      });
    }

    // Rainfall recommendations
    if (weather.rainfall > 15) {
      newRecommendations.push({
        id: 'heavy-rain',
        type: 'grazing',
        priority: 'medium',
        title: 'Heavy Rainfall Management',
        description: `${weather.rainfall.toFixed(1)}mm rainfall affects pasture conditions`,
        action: 'Move animals to higher ground, avoid muddy areas',
        icon: <Umbrella />,
        weatherTrigger: 'Heavy Rainfall'
      });
    } else if (weather.rainfall < 2) {
      newRecommendations.push({
        id: 'drought-conditions',
        type: 'feeding',
        priority: 'high',
        title: 'Drought Management',
        description: 'Low rainfall requires water conservation measures',
        action: 'Supplement feed and monitor water sources closely',
        icon: <WbSunny />,
        weatherTrigger: 'Drought Conditions'
      });
    }

    // Wind-based recommendations
    if (weather.windSpeed > 25) {
      newRecommendations.push({
        id: 'strong-winds',
        type: 'shelter',
        priority: 'medium',
        title: 'Strong Wind Protection',
        description: `Wind speed of ${weather.windSpeed.toFixed(1)} km/h requires shelter`,
        action: 'Secure loose structures and provide windbreaks',
        icon: <Air />,
        weatherTrigger: 'Strong Winds'
      });
    }

    // Seasonal recommendations
    const month = new Date().getMonth();
    if (month >= 5 && month <= 7) { // Winter months in South Africa
      newRecommendations.push({
        id: 'winter-feeding',
        type: 'feeding',
        priority: 'medium',
        title: 'Winter Nutrition',
        description: 'Winter season requires adjusted feeding strategy',
        action: 'Increase energy-rich feed and provide mineral supplements',
        icon: <Agriculture />,
        weatherTrigger: 'Winter Season'
      });
    }

    setRecommendations(newRecommendations);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return <WbSunny sx={{ color: '#FFA726' }} />;
      case 'cloudy': return <WbSunny sx={{ color: '#90A4AE' }} />;
      case 'rainy': return <Umbrella sx={{ color: '#42A5F5' }} />;
      case 'windy': return <Air sx={{ color: '#66BB6A' }} />;
      default: return <WbSunny />;
    }
  };

  if (isLoading) {
    return (
      <Card sx={{ height: compact ? 300 : 400 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Weather Advisor
          </Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      sx={{ 
        height: compact ? 300 : 400,
        background: `linear-gradient(135deg, ${alpha(availableColors[currentColor].primary, 0.02)}, ${alpha(availableColors[currentColor].secondary, 0.01)})`,
        border: `1px solid ${alpha(availableColors[currentColor].primary, 0.1)}`
      }}
    >
      <CardContent sx={{ height: '100%', overflow: 'hidden' }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            {weatherData && getWeatherIcon(weatherData.condition)}
            <Typography variant="h6" sx={{ ml: 1 }}>
              Weather Advisor
            </Typography>
          </Box>
          {weatherData && (
            <Chip 
              label={`${weatherData.temperature.toFixed(0)}°C`}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>

        {weatherData && (
          <Box sx={{ mb: 2 }}>
            <Box display="flex" justifyContent="space-between" mb={1}>
              <Typography variant="caption" color="text.secondary">
                Humidity: {weatherData.humidity.toFixed(0)}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Wind: {weatherData.windSpeed.toFixed(0)} km/h
              </Typography>
            </Box>
            <Box display="flex" justifyContent="space-between">
              <Typography variant="caption" color="text.secondary">
                Rainfall: {weatherData.rainfall.toFixed(1)}mm
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Condition: {weatherData.condition}
              </Typography>
            </Box>
          </Box>
        )}

        <Typography variant="subtitle2" gutterBottom>
          Livestock Recommendations
        </Typography>

        <Box sx={{ height: compact ? 180 : 280, overflow: 'auto' }}>
          <List dense>
            {recommendations.map((rec, index) => (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <ListItem
                  sx={{
                    mb: 1,
                    borderRadius: 2,
                    bgcolor: alpha(getPriorityColor(rec.priority), 0.05),
                    border: `1px solid ${alpha(getPriorityColor(rec.priority), 0.2)}`,
                  }}
                >
                  <ListItemIcon>
                    <Avatar sx={{ 
                      bgcolor: getPriorityColor(rec.priority), 
                      width: 32, 
                      height: 32 
                    }}>
                      {rec.icon}
                    </Avatar>
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Typography variant="subtitle2" noWrap>
                          {rec.title}
                        </Typography>
                        <Chip
                          label={rec.priority}
                          size="small"
                          sx={{
                            bgcolor: getPriorityColor(rec.priority),
                            color: 'white',
                            fontSize: '0.7rem',
                            height: 20
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          {rec.description}
                        </Typography>
                        <Typography variant="caption" display="block" sx={{ mt: 0.5, fontWeight: 500 }}>
                          Action: {rec.action}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              </motion.div>
            ))}
            
            {recommendations.length === 0 && (
              <ListItem>
                <ListItemIcon>
                  <CheckCircle color="success" />
                </ListItemIcon>
                <ListItemText
                  primary="Optimal Conditions"
                  secondary="Current weather conditions are favorable for livestock"
                />
              </ListItem>
            )}
          </List>
        </Box>
      </CardContent>
    </Card>
  );
};

export default WeatherLivestockAdvisor;
