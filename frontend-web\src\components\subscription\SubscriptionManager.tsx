/**
 * Comprehensive Subscription Manager
 * Handles subscription tiers, upgrades, and payment flows
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Divider,
  Alert,
  LinearProgress,
  useTheme,
  alpha
} from '@mui/material';
import {
  Check,
  Close,
  Star,
  Lock,
  Upgrade,
  Payment,
  Security,
  Support,
  Analytics,
  Inventory,
  TrendingUp,
  AccountBalance,
  Assessment,
  Business
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { subscriptionPlans, isFeatureAvailable } from '../../utils/betaAccessControl';

interface SubscriptionPlan {
  id: string;
  name: string;
  price: string;
  duration: string;
  popular?: boolean;
  features: string[];
  limitations: string[];
  color: string;
  icon: React.ReactElement;
}

const subscriptionTiers: SubscriptionPlan[] = [
  {
    id: 'beta',
    name: 'Beta Access',
    price: 'Free',
    duration: '30 days trial',
    features: [
      'Dashboard Overview',
      'Basic Animal Management (50 animals max)',
      'Basic Health Monitoring',
      'Resources & Information Access',
      'Community Support',
      'Mobile App Access'
    ],
    limitations: [
      'Limited to 50 animals',
      'Basic features only',
      'No advanced analytics',
      'No financial management',
      'No breeding management',
      'Community support only'
    ],
    color: '#4CAF50',
    icon: <Star />
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 'R299',
    duration: 'per month',
    popular: true,
    features: [
      'Everything in Beta Access',
      'Unlimited Animals',
      'Advanced Breeding Management',
      'Financial Management & Reports',
      'Inventory Management',
      'Commercial Operations',
      'Advanced Health Analytics',
      'Feeding Management',
      'Email Support',
      'Data Export (PDF/Excel)',
      'Custom Reports',
      'Multi-user Access (5 users)'
    ],
    limitations: [
      'No AI Analytics',
      'No Compliance Tracking',
      'Limited integrations',
      'Standard support only'
    ],
    color: '#2196F3',
    icon: <Business />
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 'R599',
    duration: 'per month',
    features: [
      'Everything in Professional',
      'AI Analytics & Predictions',
      'Compliance Tracking',
      'Advanced Integrations',
      'Custom Reports & Dashboards',
      'Priority Support (24/7)',
      'Multi-farm Management',
      'API Access',
      'Custom Training',
      'Dedicated Account Manager',
      'Advanced Security Features',
      'Unlimited Users',
      'White-label Options'
    ],
    limitations: [],
    color: '#9C27B0',
    icon: <Assessment />
  }
];

interface SubscriptionManagerProps {
  open: boolean;
  onClose: () => void;
  currentPlan?: string;
  lockedFeature?: string;
}

const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({
  open,
  onClose,
  currentPlan = 'beta',
  lockedFeature
}) => {
  const theme = useTheme();
  const { user } = useAuth();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showPayment, setShowPayment] = useState(false);
  const [processing, setProcessing] = useState(false);

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleUpgrade = async (planId: string) => {
    if (planId === 'beta') {
      onClose();
      return;
    }

    setProcessing(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real application, you would:
      // 1. Create payment intent with Stripe/PayPal
      // 2. Process payment
      // 3. Update user subscription in database
      // 4. Send confirmation email
      
      alert(`Successfully upgraded to ${planId} plan!`);
      onClose();
    } catch (error) {
      alert('Payment failed. Please try again.');
    } finally {
      setProcessing(false);
    }
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'beta': return <Star />;
      case 'professional': return <Business />;
      case 'enterprise': return <Assessment />;
      default: return <Star />;
    }
  };

  const isPlanCurrent = (planId: string) => {
    return planId === currentPlan;
  };

  const isPlanUpgrade = (planId: string) => {
    const planOrder = ['beta', 'professional', 'enterprise'];
    const currentIndex = planOrder.indexOf(currentPlan);
    const targetIndex = planOrder.indexOf(planId);
    return targetIndex > currentIndex;
  };

  const renderFeatureList = (features: string[], limitations: string[] = []) => (
    <List dense>
      {features.map((feature, index) => (
        <ListItem key={index} sx={{ py: 0.5 }}>
          <ListItemIcon sx={{ minWidth: 32 }}>
            <Check color="success" fontSize="small" />
          </ListItemIcon>
          <ListItemText 
            primary={feature} 
            primaryTypographyProps={{ variant: 'body2' }}
          />
        </ListItem>
      ))}
      {limitations.map((limitation, index) => (
        <ListItem key={`limit-${index}`} sx={{ py: 0.5 }}>
          <ListItemIcon sx={{ minWidth: 32 }}>
            <Close color="error" fontSize="small" />
          </ListItemIcon>
          <ListItemText 
            primary={limitation}
            primaryTypographyProps={{ 
              variant: 'body2',
              color: 'text.secondary',
              sx: { textDecoration: 'line-through' }
            }}
          />
        </ListItem>
      ))}
    </List>
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <Typography variant="h4" gutterBottom>
          Choose Your Plan
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Unlock the full potential of AgriIntel livestock management
        </Typography>
        {lockedFeature && (
          <Alert severity="info" sx={{ mt: 2 }}>
            Upgrade required to access: <strong>{lockedFeature}</strong>
          </Alert>
        )}
      </DialogTitle>

      <DialogContent>
        {processing && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
            <Typography variant="body2" textAlign="center" sx={{ mt: 1 }}>
              Processing your upgrade...
            </Typography>
          </Box>
        )}

        <Grid container spacing={3}>
          {subscriptionTiers.map((plan) => (
            <Grid item xs={12} md={4} key={plan.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  border: isPlanCurrent(plan.id) ? `2px solid ${plan.color}` : '1px solid',
                  borderColor: isPlanCurrent(plan.id) ? plan.color : 'divider',
                  transform: selectedPlan === plan.id ? 'scale(1.02)' : 'scale(1)',
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  '&:hover': {
                    transform: 'scale(1.02)',
                    boxShadow: theme.shadows[8]
                  }
                }}
                onClick={() => handlePlanSelect(plan.id)}
              >
                {plan.popular && (
                  <Chip
                    label="Most Popular"
                    color="primary"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -10,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      zIndex: 1
                    }}
                  />
                )}

                {isPlanCurrent(plan.id) && (
                  <Chip
                    label="Current Plan"
                    color="success"
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: -10,
                      right: 16,
                      zIndex: 1
                    }}
                  />
                )}

                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Box
                    sx={{
                      width: 60,
                      height: 60,
                      borderRadius: '50%',
                      backgroundColor: alpha(plan.color, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 16px',
                      color: plan.color
                    }}
                  >
                    {React.cloneElement(plan.icon, { fontSize: 'large' })}
                  </Box>

                  <Typography variant="h5" gutterBottom sx={{ color: plan.color }}>
                    {plan.name}
                  </Typography>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="h3" component="span" sx={{ color: plan.color }}>
                      {plan.price}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {plan.duration}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  {renderFeatureList(plan.features, plan.limitations)}
                </CardContent>

                <CardActions sx={{ p: 2, pt: 0 }}>
                  <Button
                    fullWidth
                    variant={isPlanCurrent(plan.id) ? "outlined" : "contained"}
                    color={isPlanCurrent(plan.id) ? "inherit" : "primary"}
                    disabled={isPlanCurrent(plan.id) || processing}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUpgrade(plan.id);
                    }}
                    startIcon={
                      isPlanCurrent(plan.id) ? <Check /> : 
                      isPlanUpgrade(plan.id) ? <Upgrade /> : 
                      <Star />
                    }
                    sx={{
                      background: !isPlanCurrent(plan.id) ? 
                        `linear-gradient(45deg, ${plan.color}, ${alpha(plan.color, 0.8)})` : 
                        undefined
                    }}
                  >
                    {isPlanCurrent(plan.id) ? 'Current Plan' : 
                     isPlanUpgrade(plan.id) ? 'Upgrade Now' : 
                     'Select Plan'}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Why Choose AgriIntel?
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Security color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Secure & Reliable</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Support color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">24/7 Support</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Analytics color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Advanced Analytics</Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <TrendingUp color="primary" sx={{ fontSize: 40, mb: 1 }} />
                <Typography variant="subtitle2">Proven Results</Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} disabled={processing}>
          Cancel
        </Button>
        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1, textAlign: 'center' }}>
          30-day money-back guarantee • Cancel anytime
        </Typography>
      </DialogActions>
    </Dialog>
  );
};

export default SubscriptionManager;
