# MongoDB Setup and Integration Guide

This guide explains how to set up and use MongoDB with the AMPD Livestock Management System.

## Overview

The AMPD Livestock Management System uses MongoDB as its primary database. The application is designed to work with either MongoDB or mock data, but MongoDB is recommended for production use.

## Prerequisites

- Node.js (v14 or higher)
- MongoDB Atlas account or local MongoDB installation
- Git

## MongoDB Connection

The application is configured to connect to MongoDB using the following connection string:

```
mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

You'll need to replace `<db_password>` with your actual MongoDB password.

This connection string is stored in the `.env` file in the backend directory. You can modify this connection string to use your own MongoDB instance.

## Setting Up MongoDB

### Option 1: Using MongoDB Atlas (Recommended)

1. Create a MongoDB Atlas account at https://www.mongodb.com/cloud/atlas
2. Create a new cluster
3. Create a database user with read/write permissions
4. Get the connection string from the Atlas dashboard
5. Update the `.env` file in the backend directory with your connection string

### Option 2: Using Local MongoDB

1. Install MongoDB locally following the instructions at https://docs.mongodb.com/manual/installation/
2. Start the MongoDB server
3. Update the `.env` file in the backend directory with your local connection string:

```
MONGODB_URI=mongodb://localhost:27017/ampd_livestock
```

## Populating MongoDB with Data

The application includes scripts to populate MongoDB with realistic data. To run these scripts:

### Option 1: Using the Sync Script (Recommended)

1. Navigate to the scripts directory:
   ```
   cd scripts
   ```

2. Run the MongoDB sync script:
   ```
   node sync-mongodb.js
   ```

This script will:
1. Connect to MongoDB
2. Check if collections exist and have data
3. If a collection is empty, populate it with mock data from the frontend

### Option 2: Using the Backend Population Script

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Run the MongoDB population script:
   ```
   npm run populate-mongodb
   ```

This script will:
1. Connect to MongoDB
2. Create all necessary collections
3. Populate the collections with realistic data
4. Ensure data consistency across collections

## Verifying MongoDB Setup

To verify that MongoDB is properly set up and populated:

1. Start the backend server:
   ```
   cd backend
   npm start
   ```

2. Open a web browser and navigate to:
   ```
   http://localhost:3001/api/db-status
   ```

This endpoint will return information about the MongoDB connection status and the number of documents in each collection.

## Testing MongoDB Integration

The application includes scripts to test the MongoDB integration:

### Option 1: Using the New Test Script (Recommended)

1. Run the MongoDB test script with your password:
   ```
   node test-mongodb-connection.js YOUR_PASSWORD
   ```
   Replace `YOUR_PASSWORD` with your actual MongoDB password.

2. This script will directly connect to MongoDB Atlas using the provided password.

### Option 2: Using the Set Password Script

1. Run the password setup script:
   ```
   node set-db-password.js YOUR_PASSWORD
   ```
   Replace `YOUR_PASSWORD` with your actual MongoDB password.

2. This script will update all .env files with your password.

3. Then run the backend test script:
   ```
   cd backend
   node src/scripts/test-mongodb-connection.js
   ```

This script will:
1. Connect to MongoDB
2. List all collections
3. Count the number of documents in each collection
4. Show a sample document from each collection

### Option 2: Using the Backend Test Script

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Run the MongoDB integrity test script:
   ```
   npm run test-mongodb
   ```

This script will:
1. Connect to MongoDB
2. Check that all collections exist and have data
3. Verify data relationships between collections
4. Report any issues found

## Ensuring Data Consistency

To ensure data consistency across all collections:

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Run the data consistency script:
   ```
   node scripts/ensure-data-consistency.js
   ```

This script will:
1. Connect to MongoDB
2. Check for and fix any inconsistencies in the data
3. Ensure all references between collections are valid
4. Add timestamps to any documents missing them

## Switching Between MongoDB and Mock Data

The application can use either MongoDB or mock data. To switch between them:

1. Update the `.env` file in the backend directory:
   ```
   # Use MongoDB
   USE_MOCK_DATA=false

   # Use mock data
   USE_MOCK_DATA=true
   ```

2. Restart the backend server

## Troubleshooting

If you encounter issues with the MongoDB connection:

1. Check that the MongoDB server is running
2. Verify that the connection string in the `.env` file is correct
3. Make sure you've replaced `<db_password>` with your actual MongoDB password
4. Check that the database user has the correct permissions
5. Look for error messages in the backend server logs
6. Run the MongoDB test script with your password to diagnose connection issues:
   ```
   node test-mongodb-connection.js YOUR_PASSWORD
   ```
7. If you're experiencing timeout issues, try increasing the connection timeout in the `.env` files:
   ```
   MONGODB_CONNECTION_TIMEOUT=60000
   MONGODB_SOCKET_TIMEOUT=75000
   ```
8. If you still can't connect, try using mock data by setting:
   ```
   REACT_APP_USE_MOCK_DATA=true
   ```
   in the `frontend-web/.env` file.

## Collections and Schema

The application uses the following MongoDB collections:

- `animals`: Animal records
- `health_records`: Animal health records
- `vaccinations`: Vaccination records
- `breeding_records`: Breeding records
- `calving_records`: Calving records
- `feeding_records`: Feeding records
- `feed_inventory`: Feed inventory
- `inventory`: General inventory
- `users`: User accounts
- `business_kpis`: Business KPIs
- `business_predictions`: Business predictions
- `business_insights`: Business insights
- `business_risks`: Business risks
- `business_opportunities`: Business opportunities
- `strategic_goals`: Strategic goals
- `strategic_initiatives`: Strategic initiatives
- `swot_analysis`: SWOT analysis
- `strategic_scenarios`: Strategic scenarios

Each collection has a defined schema that ensures data consistency. The schemas are defined in the `backend/src/models` directory.

## Backup and Restore

To backup the MongoDB database:

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Run the backup script:
   ```
   node scripts/backup-mongodb.js
   ```

This will create a backup of the database in the `backend/backups` directory.

To restore the database from a backup:

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Run the restore script:
   ```
   node scripts/restore-mongodb.js --backup=<backup-file>
   ```

Replace `<backup-file>` with the path to the backup file.

## Conclusion

MongoDB provides a flexible and scalable database solution for the MayCaiphus Livestock Management System. By following this guide, you should be able to set up and use MongoDB with the application.
