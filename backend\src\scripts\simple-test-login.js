/**
 * Simple Test Login Script
 */

const axios = require('axios');

// Test login for mayrakgama
async function testLogin() {
  try {
    console.log('Testing login for mayrakgama...');
    
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      username: 'mayrak<PERSON><PERSON>',
      password: '<PERSON><PERSON>@1951ke*#'
    });
    
    console.log('Login successful!');
    console.log('User:', response.data.user);
    console.log('Token:', response.data.token.substring(0, 20) + '...');
  } catch (error) {
    console.error('Login failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testLogin();
