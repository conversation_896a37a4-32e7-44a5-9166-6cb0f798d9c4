import React from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, LinearProgress, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';

interface AnimatedDashboardCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  color?: string;
  secondaryColor?: string;
  backgroundImage?: string;
  progress?: number;
  trend?: {
    value: number;
    label: string;
    positive: boolean;
  };
  footer?: {
    label: string;
    value: string | number;
  };
  actionLabel?: string;
  onAction?: () => void;
  delay?: number;
}

const AnimatedDashboardCard: React.FC<AnimatedDashboardCardProps> = ({
  title,
  value,
  icon,
  color,
  secondaryColor,
  backgroundImage,
  progress,
  trend,
  footer,
  actionLabel,
  onAction,
  delay = 0
}) => {
  const theme = useTheme();

  const cardColor = color || theme.palette.primary.main;
  const cardSecondaryColor = secondaryColor || theme.palette.primary.dark;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
    >
      <Card
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          height: '100%',
          position: 'relative',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          }
        }}
      >
        {/* Background Image with Overlay */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(135deg, ${alpha(cardColor, 0.65)}, ${alpha(cardSecondaryColor, 0.7)})`,
                zIndex: 1
              },
              zIndex: 0
            }}
          />
        )}

        {/* Card Content */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            p: 3,
            background: backgroundImage ? 'transparent' : `linear-gradient(135deg, ${cardColor}, ${cardSecondaryColor})`,
            color: backgroundImage || theme.palette.getContrastText(cardColor)
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Typography
              variant="subtitle1"
              fontWeight="medium"
              sx={{
                opacity: 0.9,
                fontSize: { xs: '1rem', sm: '1.1rem', md: '1.15rem' },
                lineHeight: 1.3
              }}
            >
              {title}
            </Typography>
            {icon && (
              <Box
                sx={{
                  p: { xs: 1, sm: 1.2, md: 1.5 },
                  borderRadius: '50%',
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {icon}
              </Box>
            )}
          </Box>

          <Typography
            variant="h4"
            fontWeight="bold"
            mb={1}
            sx={{
              fontSize: { xs: '1.8rem', sm: '2rem', md: '2.2rem' },
              lineHeight: 1.2
            }}
          >
            {value}
          </Typography>

          {trend && (
            <Box display="flex" alignItems="center" mb={1}>
              <Typography
                variant="body2"
                sx={{
                  color: trend.positive ? 'success.light' : 'error.light',
                  display: 'flex',
                  alignItems: 'center',
                  fontWeight: 'medium',
                  fontSize: { xs: '0.9rem', sm: '1rem', md: '1.05rem' }
                }}
              >
                {trend.positive ? '↑' : '↓'} {trend.value}% {trend.label}
              </Typography>
            </Box>
          )}

          {progress !== undefined && (
            <Box mt={1} mb={2}>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                  }
                }}
              />
            </Box>
          )}

          {footer && (
            <Box
              mt="auto"
              pt={2}
              sx={{
                borderTop: '1px solid rgba(255, 255, 255, 0.2)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  opacity: 0.8,
                  fontSize: { xs: '0.9rem', sm: '1rem', md: '1.05rem' }
                }}
              >
                {footer.label}
              </Typography>
              <Typography
                variant="body2"
                fontWeight="bold"
                sx={{
                  fontSize: { xs: '0.9rem', sm: '1rem', md: '1.05rem' }
                }}
              >
                {footer.value}
              </Typography>
            </Box>
          )}

          {actionLabel && onAction && (
            <Box mt={footer ? 2 : 'auto'} pt={footer ? 0 : 2}>
              <CustomButton
                variant="outlined"
                size="small"
                onClick={onAction}
                sx={{
                  color: 'white',
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' },
                  padding: { xs: '6px 12px', sm: '6px 16px', md: '8px 20px' },
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                {actionLabel}
              </CustomButton>
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimatedDashboardCard;
