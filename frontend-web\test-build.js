const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Function to run a command and return its output
function runCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8' });
    return { success: true, output };
  } catch (error) {
    return { success: false, error: error.message, stdout: error.stdout, stderr: error.stderr };
  }
}

// Create a temporary file to test the build
const tempFile = path.join(__dirname, 'src', 'temp-test.js');
fs.writeFileSync(tempFile, `
// Test file to check if the build works
console.log('Test file loaded successfully');
export default function test() {
  return 'Test function';
}
`);

// Try to build the application
console.log('Attempting to build the application...');
const buildResult = runCommand('npm run build:original');

// Clean up the temporary file
fs.unlinkSync(tempFile);

// Output the result
if (buildResult.success) {
  console.log('Build succeeded!');
  console.log(buildResult.output);
} else {
  console.log('Build failed!');
  console.log(buildResult.error);
  console.log('STDOUT:', buildResult.stdout);
  console.log('STDERR:', buildResult.stderr);
}
