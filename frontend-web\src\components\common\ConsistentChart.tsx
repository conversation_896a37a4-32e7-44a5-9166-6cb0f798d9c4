import React from 'react';
import {
  Re<PERSON>onsive<PERSON><PERSON><PERSON>,
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Label
} from 'recharts';
import { useTheme, alpha } from '@mui/material';
import { useResponsive } from '../../hooks';

export type ChartType = 'line' | 'bar' | 'pie' | 'area';

interface ChartDataset {
  id: string;
  label: string;
  data: number[];
  color?: string;
  stack?: string;
}

interface ChartProps {
  type: ChartType;
  data: any[];
  datasets: ChartDataset[];
  xAxisKey: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
  height?: number;
  stacked?: boolean;
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  showLabels?: boolean;
  customColors?: string[];
  formatXAxis?: (value: any) => string;
  formatYAxis?: (value: any) => string;
  formatTooltip?: (value: any) => string;
}

/**
 * ConsistentChart component
 * Provides a consistent styling for all chart types
 */
const ConsistentChart: React.FC<ChartProps> = ({
  type,
  data,
  datasets,
  xAxisKey,
  xAxisLabel,
  yAxisLabel,
  height = 300,
  stacked = false,
  showGrid = true,
  showLegend = true,
  showTooltip = true,
  showLabels = false,
  customColors,
  formatXAxis,
  formatYAxis,
  formatTooltip
}) => {
  const theme = useTheme();
  const responsive = useResponsive();
  const isMobile = responsive.isMobile;

  // Default chart colors based on theme
  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
    alpha(theme.palette.primary.main, 0.7),
    alpha(theme.palette.secondary.main, 0.7),
    alpha(theme.palette.success.main, 0.7),
    alpha(theme.palette.error.main, 0.7)
  ];

  // Use custom colors or default colors
  const chartColors = customColors || defaultColors;

  // Adjust chart height for mobile
  const chartHeight = isMobile ? height * 0.8 : height;

  // Common chart props
  const commonCartesianProps = {
    data,
    margin: { top: 10, right: 30, left: 0, bottom: 10 },
    height: chartHeight
  };

  // Common axis props
  const commonXAxisProps = {
    dataKey: xAxisKey,
    tick: { fontSize: 12, fill: theme.palette.text.secondary },
    tickLine: { stroke: theme.palette.divider },
    axisLine: { stroke: theme.palette.divider },
    tickFormatter: formatXAxis
  };

  const commonYAxisProps = {
    tick: { fontSize: 12, fill: theme.palette.text.secondary },
    tickLine: { stroke: theme.palette.divider },
    axisLine: { stroke: theme.palette.divider },
    tickFormatter: formatYAxis
  };

  // Render the appropriate chart type
  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart {...commonCartesianProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />}
            <XAxis {...commonXAxisProps}>
              {xAxisLabel && <Label value={xAxisLabel} position="bottom" style={{ fill: theme.palette.text.secondary }} />}
            </XAxis>
            <YAxis {...commonYAxisProps}>
              {yAxisLabel && <Label value={yAxisLabel} angle={-90} position="left" style={{ fill: theme.palette.text.secondary }} />}
            </YAxis>
            {showTooltip && <Tooltip formatter={formatTooltip} contentStyle={{ backgroundColor: theme.palette.background.paper, borderColor: theme.palette.divider }} />}
            {showLegend && <Legend wrapperStyle={{ fontSize: 12, color: theme.palette.text.secondary }} />}
            {datasets.map((dataset, index) => (
              <Line
                key={dataset.id}
                type="monotone"
                dataKey={dataset.id}
                name={dataset.label}
                stroke={dataset.color || chartColors[index % chartColors.length]}
                strokeWidth={2}
                dot={{ r: 3, fill: dataset.color || chartColors[index % chartColors.length] }}
                activeDot={{ r: 5 }}
              />
            ))}
          </LineChart>
        );

      case 'bar':
        return (
          <BarChart {...commonCartesianProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />}
            <XAxis {...commonXAxisProps}>
              {xAxisLabel && <Label value={xAxisLabel} position="bottom" style={{ fill: theme.palette.text.secondary }} />}
            </XAxis>
            <YAxis {...commonYAxisProps}>
              {yAxisLabel && <Label value={yAxisLabel} angle={-90} position="left" style={{ fill: theme.palette.text.secondary }} />}
            </YAxis>
            {showTooltip && <Tooltip formatter={formatTooltip} contentStyle={{ backgroundColor: theme.palette.background.paper, borderColor: theme.palette.divider }} />}
            {showLegend && <Legend wrapperStyle={{ fontSize: 12, color: theme.palette.text.secondary }} />}
            {datasets.map((dataset, index) => (
              <Bar
                key={dataset.id}
                dataKey={dataset.id}
                name={dataset.label}
                fill={dataset.color || chartColors[index % chartColors.length]}
                stackId={stacked ? 'stack' : dataset.stack}
              />
            ))}
          </BarChart>
        );

      case 'area':
        return (
          <AreaChart {...commonCartesianProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.5)} />}
            <XAxis {...commonXAxisProps}>
              {xAxisLabel && <Label value={xAxisLabel} position="bottom" style={{ fill: theme.palette.text.secondary }} />}
            </XAxis>
            <YAxis {...commonYAxisProps}>
              {yAxisLabel && <Label value={yAxisLabel} angle={-90} position="left" style={{ fill: theme.palette.text.secondary }} />}
            </YAxis>
            {showTooltip && <Tooltip formatter={formatTooltip} contentStyle={{ backgroundColor: theme.palette.background.paper, borderColor: theme.palette.divider }} />}
            {showLegend && <Legend wrapperStyle={{ fontSize: 12, color: theme.palette.text.secondary }} />}
            {datasets.map((dataset, index) => (
              <Area
                key={dataset.id}
                type="monotone"
                dataKey={dataset.id}
                name={dataset.label}
                stroke={dataset.color || chartColors[index % chartColors.length]}
                fill={alpha(dataset.color || chartColors[index % chartColors.length], 0.2)}
                stackId={stacked ? 'stack' : dataset.stack}
              />
            ))}
          </AreaChart>
        );

      case 'pie':
        // Transform data for pie chart
        const pieData = datasets[0].data.map((value, index) => ({
          name: data[index][xAxisKey],
          value
        }));

        return (
          <PieChart width={isMobile ? 300 : 400} height={chartHeight}>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={showLabels}
              label={showLabels ? ({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%` : undefined}
              outerRadius={isMobile ? 80 : 100}
              innerRadius={isMobile ? 40 : 60}
              fill={theme.palette.primary.main}
              dataKey="value"
              nameKey="name"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
              ))}
            </Pie>
            {showTooltip && <Tooltip formatter={formatTooltip} contentStyle={{ backgroundColor: theme.palette.background.paper, borderColor: theme.palette.divider }} />}
            {showLegend && <Legend wrapperStyle={{ fontSize: 12, color: theme.palette.text.secondary }} />}
          </PieChart>
        );

      default:
        return null;
    }
  };

  const chart = renderChart();

  return (
    <ResponsiveContainer width="100%" height={chartHeight}>
      {chart || <div>No chart data available</div>}
    </ResponsiveContainer>
  );
};

export default ConsistentChart;
