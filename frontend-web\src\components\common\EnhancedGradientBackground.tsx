/**
 * Enhanced Gradient Background Component
 * 
 * This component provides beautiful animated gradient backgrounds with:
 * - Dynamic color transitions
 * - Particle effects
 * - Module-specific themes
 * - Responsive design
 * - Performance optimized animations
 */

import React, { useEffect, useRef, useState } from 'react';
import { Box, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface EnhancedGradientBackgroundProps {
  module?: string;
  variant?: 'subtle' | 'vibrant' | 'mesh' | 'animated' | 'particles';
  children?: React.ReactNode;
  height?: string | number;
  opacity?: number;
  enableParticles?: boolean;
  enableAnimation?: boolean;
  className?: string;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  opacity: number;
  color: string;
}

const EnhancedGradientBackground: React.FC<EnhancedGradientBackgroundProps> = ({
  module = 'dashboard',
  variant = 'subtle',
  children,
  height = '100vh',
  opacity = 0.8,
  enableParticles = false,
  enableAnimation = true,
  className
}) => {
  const { currentColor, availableColors } = useThemeContext();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);
  const [particles, setParticles] = useState<Particle[]>([]);
  const [gradientPhase, setGradientPhase] = useState(0);

  // Get theme colors
  const colorConfig = availableColors[currentColor];
  const primaryColor = colorConfig.primary;
  const secondaryColor = colorConfig.secondary;
  const lightColor = colorConfig.light;

  // Create particles for particle effect
  useEffect(() => {
    if (!enableParticles) return;

    const createParticles = () => {
      const newParticles: Particle[] = [];
      for (let i = 0; i < 50; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * window.innerWidth,
          y: Math.random() * window.innerHeight,
          size: Math.random() * 3 + 1,
          speedX: (Math.random() - 0.5) * 0.5,
          speedY: (Math.random() - 0.5) * 0.5,
          opacity: Math.random() * 0.5 + 0.1,
          color: Math.random() > 0.5 ? primaryColor : secondaryColor
        });
      }
      setParticles(newParticles);
    };

    createParticles();
  }, [enableParticles, primaryColor, secondaryColor]);

  // Animate particles
  useEffect(() => {
    if (!enableParticles || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const animate = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;

        // Wrap around screen
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.y > canvas.height) particle.y = 0;
        if (particle.y < 0) particle.y = canvas.height;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = alpha(particle.color, particle.opacity);
        ctx.fill();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [particles, enableParticles]);

  // Animate gradient phase for dynamic effects
  useEffect(() => {
    if (!enableAnimation) return;

    const interval = setInterval(() => {
      setGradientPhase(prev => (prev + 1) % 360);
    }, 100);

    return () => clearInterval(interval);
  }, [enableAnimation]);

  // Get gradient based on variant and module
  const getGradient = () => {
    const baseOpacity = opacity;

    // Module-specific opacity adjustments
    const moduleOpacityMultiplier = module === 'dashboard' ? 1.2 :
                                   module === 'analytics' ? 1.1 :
                                   module === 'reports' ? 0.9 : 1.0;
    
    switch (variant) {
      case 'subtle':
        return `linear-gradient(135deg,
          ${alpha(primaryColor, baseOpacity * 0.1 * moduleOpacityMultiplier)},
          ${alpha(secondaryColor, baseOpacity * 0.05 * moduleOpacityMultiplier)})`;

      case 'vibrant':
        return `linear-gradient(135deg,
          ${alpha(primaryColor, baseOpacity * moduleOpacityMultiplier)},
          ${alpha(secondaryColor, baseOpacity * 0.8 * moduleOpacityMultiplier)})`;

      case 'mesh':
        return `
          radial-gradient(circle at 10% 10%, ${alpha(primaryColor, baseOpacity * 0.3 * moduleOpacityMultiplier)}, transparent 30%),
          radial-gradient(circle at 90% 90%, ${alpha(secondaryColor, baseOpacity * 0.3 * moduleOpacityMultiplier)}, transparent 30%),
          radial-gradient(circle at 30% 70%, ${alpha(lightColor, baseOpacity * 0.2 * moduleOpacityMultiplier)}, transparent 20%),
          radial-gradient(circle at 70% 30%, ${alpha(primaryColor, baseOpacity * 0.15 * moduleOpacityMultiplier)}, transparent 25%)`;
      
      case 'animated':
        const phase1 = (Math.sin(gradientPhase * 0.01) + 1) * 0.5;
        const phase2 = (Math.cos(gradientPhase * 0.015) + 1) * 0.5;
        return `linear-gradient(${gradientPhase}deg, 
          ${alpha(primaryColor, baseOpacity * (0.3 + phase1 * 0.2))}, 
          ${alpha(secondaryColor, baseOpacity * (0.2 + phase2 * 0.3))})`;
      
      case 'particles':
        return `linear-gradient(135deg, 
          ${alpha(primaryColor, baseOpacity * 0.05)}, 
          ${alpha(secondaryColor, baseOpacity * 0.03)})`;
      
      default:
        return `linear-gradient(135deg, 
          ${alpha(primaryColor, baseOpacity * 0.1)}, 
          ${alpha(secondaryColor, baseOpacity * 0.05)})`;
    }
  };

  return (
    <Box
      className={className}
      sx={{
        position: 'relative',
        height,
        width: '100%',
        overflow: 'hidden',
        background: getGradient(),
        transition: 'background 0.3s ease-in-out',
      }}
    >
      {/* Particle Canvas */}
      {enableParticles && (
        <Box
          component="canvas"
          ref={canvasRef}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 1,
          }}
        />
      )}

      {/* Animated Overlay for extra effects */}
      {enableAnimation && variant === 'animated' && (
        <Box
          component={motion.div}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `radial-gradient(circle at 50% 50%, ${alpha(lightColor, 0.1)}, transparent 70%)`,
            zIndex: 1,
          }}
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          height: '100%',
          width: '100%',
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default EnhancedGradientBackground;
