# MayCaiphus Livestock Management System - Backend

This is the backend API for the MayCaiphus Livestock Management System.

## Prerequisites

- Node.js (v14 or higher)
- MongoDB Atlas account or local MongoDB installation

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file in the root directory with the following variables:
   ```
   # Application Configuration
   NODE_ENV=development
   PORT=5000

   # MongoDB Configuration
   MONGODB_URI=mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
   MONGODB_DB_NAME=AMPD_Live_Stock

   # JWT Configuration
   JWT_SECRET=maycaiphus_secret_key_for_jwt_tokens
   JWT_EXPIRES_IN=7d

   # Logging Configuration
   LOG_LEVEL=debug

   # Frontend URL (for CORS)
   FRONTEND_URL=http://localhost:3000

   # Use Mock Data (true/false)
   USE_MOCK_DATA=false
   ```

## Database Setup

The application uses MongoDB for data storage. You can use the provided scripts to set up the database:

1. Test MongoDB connection:
   ```
   npm run test-mongodb
   ```

2. Create admin user:
   ```
   npm run create-admin
   ```

3. Populate database with sample data:
   ```
   npm run populate-mongodb
   ```

4. Or run all database setup steps at once:
   ```
   npm run setup-db
   ```

## Running the Application

1. Start the server in development mode:
   ```
   npm run dev
   ```

2. Start the server in production mode:
   ```
   npm start
   ```

## API Endpoints

The API provides the following endpoints:

### Authentication

- `POST /api/auth/login` - Login user
- `POST /api/auth/register` - Register user (admin only)
- `GET /api/auth/me` - Get current user
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/refresh-token` - Refresh token
- `POST /api/auth/logout` - Logout user

### Animals

- `GET /api/animals` - Get all animals
- `GET /api/animals/:id` - Get animal by ID
- `POST /api/animals` - Create a new animal
- `PUT /api/animals/:id` - Update an animal
- `DELETE /api/animals/:id` - Delete an animal
- `GET /api/animals/stats` - Get animal statistics

### Health

- `GET /api/health` - Get all health records
- `GET /api/health/:id` - Get health record by ID
- `POST /api/health` - Create a new health record
- `PUT /api/health/:id` - Update a health record
- `DELETE /api/health/:id` - Delete a health record

### Breeding

- `GET /api/breeding` - Get all breeding records
- `GET /api/breeding/:id` - Get breeding record by ID
- `POST /api/breeding` - Create a new breeding record
- `PUT /api/breeding/:id` - Update a breeding record
- `DELETE /api/breeding/:id` - Delete a breeding record

### Financial

- `GET /api/financial` - Get all financial records
- `GET /api/financial/:id` - Get financial record by ID
- `POST /api/financial` - Create a new financial record
- `PUT /api/financial/:id` - Update a financial record
- `DELETE /api/financial/:id` - Delete a financial record

### Inventory

- `GET /api/inventory` - Get all inventory items
- `GET /api/inventory/:id` - Get inventory item by ID
- `POST /api/inventory` - Create a new inventory item
- `PUT /api/inventory/:id` - Update an inventory item
- `DELETE /api/inventory/:id` - Delete an inventory item

### Users

- `GET /api/users` - Get all users (admin only)
- `GET /api/users/:id` - Get user by ID (admin only)
- `POST /api/users` - Create a new user (admin only)
- `PUT /api/users/:id` - Update a user (admin only)
- `DELETE /api/users/:id` - Delete a user (admin only)

## Default Admin Credentials

- Username: `admin`
- Password: `Admin@123`

## Testing

Run tests with:
```
npm test
```

## License

ISC
