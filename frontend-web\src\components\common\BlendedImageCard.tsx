import React from 'react';
import { CustomButton } from '../common';
import { Card, CardContent, Typography, Box, Chip, useTheme, alpha, CardProps, Rating } from '@mui/material';
import { motion } from 'framer-motion';

interface BlendedImageCardProps extends Omit<CardProps, 'children'> {
  title: string;
  description?: string;
  image: string;
  price?: number | string;
  priceLabel?: string;
  category?: string;
  rating?: number;
  supplier?: string;
  status?: string;
  statusColor?: 'success' | 'error' | 'warning' | 'info' | 'primary' | 'secondary';
  primaryAction?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
  };
  secondaryAction?: {
    label: string;
    icon?: React.ReactNode;
    onClick: () => void;
  };
  delay?: number;
}

/**
 * A Card component with a blended background image and gradient overlay,
 * designed to display product-like items with images, prices, and actions
 */
const BlendedImageCard: React.FC<BlendedImageCardProps> = ({
  title,
  description,
  image,
  price,
  priceLabel = 'each',
  category,
  rating,
  supplier,
  status,
  statusColor = 'success',
  primaryAction,
  secondaryAction,
  delay = 0,
  sx = {},
  ...props
}) => {
  const theme = useTheme();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay }}
      style={{ height: '100%' }}
    >
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          transition: 'transform 0.3s ease, box-shadow 0.3s ease',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          },
          ...sx
        }}
        {...props}
      >
        {/* Image with overlay and price tag */}
        <Box sx={{ position: 'relative' }}>
          <Box
            sx={{
              height: 180,
              backgroundImage: `url(${image})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.65)}, ${alpha(theme.palette.primary.dark, 0.7)})`,
                zIndex: 1
              }
            }}
          />

          {/* Price tag */}
          {price && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 10,
                right: 10,
                bgcolor: alpha(theme.palette.primary.main, 0.9),
                color: 'white',
                px: 1.5,
                py: 0.5,
                borderRadius: 1,
                fontWeight: 'bold',
                zIndex: 2
              }}
            >
              {typeof price === 'number' ? `R${price.toLocaleString()}` : price} {priceLabel}
            </Box>
          )}

          {/* Category tag */}
          {category && (
            <Chip
              label={category}
              size="small"
              sx={{
                position: 'absolute',
                top: 10,
                left: 10,
                bgcolor: alpha(theme.palette.background.paper, 0.8),
                zIndex: 2
              }}
            />
          )}
        </Box>

        {/* Content */}
        <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          <Typography variant="h6" component="h2" gutterBottom>
            {title}
          </Typography>

          {description && (
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {description}
            </Typography>
          )}

          {supplier && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Supplier: {supplier}
            </Typography>
          )}

          {rating !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Rating value={rating} precision={0.5} size="small" readOnly />
              <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                {rating}
              </Typography>
            </Box>
          )}

          {status && (
            <Chip
              label={status}
              size="small"
              color={statusColor}
              sx={{ mt: 2 }}
            />
          )}

          {/* Action buttons */}
          <Box sx={{ mt: 'auto', pt: 2, display: 'flex', gap: 1 }}>
            {secondaryAction && (
              <CustomButton
                size="small"
                startIcon={secondaryAction.icon}
                onClick={secondaryAction.onClick}
                sx={{ flexGrow: secondaryAction && !primaryAction ? 1 : 0 }}
              >
                {secondaryAction.label}
              </CustomButton>
            )}
            
            {primaryAction && (
              <CustomButton
                size="small"
                variant="contained"
                startIcon={primaryAction.icon}
                onClick={primaryAction.onClick}
                sx={{ flexGrow: 1 }}
              >
                {primaryAction.label}
              </CustomButton>
            )}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default BlendedImageCard;
