/**
 * This file provides mock implementations for MongoDB-related functionality
 * to avoid the "Cannot read properties of undefined (reading 'onDemand')" error
 */

// Mock MongoDB ObjectId
export class ObjectId {
  id: string;
  
  constructor(id?: string) {
    this.id = id || 'mock-id-' + Date.now();
  }
  
  toString() {
    return this.id;
  }
  
  toHexString() {
    return this.id;
  }
}

// Mock MongoDB Collection
export class Collection {
  name: string;
  data: any[];
  
  constructor(name: string) {
    this.name = name;
    this.data = [];
  }
  
  find(query = {}) {
    console.log(`Mock find for ${this.name} with query:`, query);
    return {
      toArray: async () => this.data
    };
  }
  
  findOne(query: any) {
    console.log(`Mock findOne for ${this.name} with query:`, query);
    return Promise.resolve(this.data[0] || null);
  }
  
  insertOne(doc: any) {
    console.log(`Mock insertOne for ${this.name}:`, doc);
    const id = new ObjectId();
    const newDoc = { ...doc, _id: id };
    this.data.push(newDoc);
    return Promise.resolve({ insertedId: id });
  }
  
  updateOne(filter: any, update: any) {
    console.log(`Mock updateOne for ${this.name}:`, filter, update);
    return Promise.resolve({ modifiedCount: 1 });
  }
  
  deleteOne(filter: any) {
    console.log(`Mock deleteOne for ${this.name}:`, filter);
    return Promise.resolve({ deletedCount: 1 });
  }
  
  countDocuments(query = {}) {
    console.log(`Mock countDocuments for ${this.name} with query:`, query);
    return Promise.resolve(this.data.length);
  }
}

// Mock MongoDB Database
export class Database {
  name: string;
  collections: Record<string, Collection>;
  
  constructor(name: string) {
    this.name = name;
    this.collections = {};
  }
  
  collection(name: string) {
    if (!this.collections[name]) {
      this.collections[name] = new Collection(name);
    }
    return this.collections[name];
  }
  
  command(command: any) {
    console.log(`Mock command for ${this.name}:`, command);
    return Promise.resolve({ ok: 1 });
  }
}

// Mock MongoDB Client
export class MongoClient {
  uri: string;
  databases: Record<string, Database>;
  
  constructor(uri: string) {
    this.uri = uri;
    this.databases = {};
  }
  
  connect() {
    console.log('Mock MongoDB connect');
    return Promise.resolve(this);
  }
  
  close() {
    console.log('Mock MongoDB close');
    return Promise.resolve();
  }
  
  db(name: string) {
    if (!this.databases[name]) {
      this.databases[name] = new Database(name);
    }
    return this.databases[name];
  }
}

// Export mock MongoDB
export const mongodb = {
  MongoClient,
  ObjectId,
  ServerApiVersion: {
    v1: '1'
  }
};
