import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar, Rating } from '@mui/material';
import { 
  Add,
  Edit,
  Delete,
  Phone,
  Email,
  LocationOn,
  Business,
  LocalShipping,
  AttachMoney,
  Star,
  Search
 } from '../../utils/iconImports';
import { formatCurrency } from '../../utils/formatters';

interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  products: string[];
  rating: number;
  lastOrderDate?: string;
  totalSpent: number;
  paymentTerms: string;
  notes: string;
  status: 'active' | 'inactive';
}

const Suppliers: React.FC = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([
    {
      id: 'SUP001',
      name: 'Afgri Animal Feeds',
      contactPerson: 'Johan Pretorius',
      phone: '************',
      email: '<EMAIL>',
      address: '123 Industrial Road, Pretoria, Gauteng',
      products: ['Lucerne', 'Maize', 'Protein Supplement', 'Mineral Lick'],
      rating: 4.5,
      lastOrderDate: '2024-03-01',
      totalSpent: 125000,
      paymentTerms: '30 days',
      notes: 'Reliable supplier with good quality products',
      status: 'active'
    },
    {
      id: 'SUP002',
      name: 'Kaap Agri',
      contactPerson: 'Pieter van Wyk',
      phone: '************',
      email: '<EMAIL>',
      address: '456 Farm Road, Paarl, Western Cape',
      products: ['Eragrostis', 'Teff', 'Mineral Lick'],
      rating: 4,
      lastOrderDate: '2024-02-15',
      totalSpent: 85000,
      paymentTerms: '30 days',
      notes: 'Good prices on hay products',
      status: 'active'
    },
    {
      id: 'SUP003',
      name: 'Obaro',
      contactPerson: 'Anele Dlamini',
      phone: '************',
      email: '<EMAIL>',
      address: '789 Main Street, Brits, North West',
      products: ['Lucerne', 'Maize', 'Molasses Meal'],
      rating: 4.2,
      lastOrderDate: '2024-02-28',
      totalSpent: 110000,
      paymentTerms: '14 days',
      notes: 'Competitive pricing on bulk orders',
      status: 'active'
    },
    {
      id: 'SUP004',
      name: 'GWK',
      contactPerson: 'Willem Botha',
      phone: '************',
      email: '<EMAIL>',
      address: '321 Agricultural Avenue, Kimberley, Northern Cape',
      products: ['Lucerne', 'Protein Supplement', 'Mineral Lick'],
      rating: 3.8,
      lastOrderDate: '2024-01-20',
      totalSpent: 65000,
      paymentTerms: '30 days',
      notes: 'Good quality lucerne',
      status: 'active'
    },
    {
      id: 'SUP005',
      name: 'BKB',
      contactPerson: 'Sarah Johnson',
      phone: '************',
      email: '<EMAIL>',
      address: '654 Wool Street, Port Elizabeth, Eastern Cape',
      products: ['Eragrostis', 'Teff', 'Protein Supplement'],
      rating: 4.1,
      lastOrderDate: '2024-03-05',
      totalSpent: 95000,
      paymentTerms: '30 days',
      notes: 'Specializes in sheep feed products',
      status: 'active'
    },
    {
      id: 'SUP006',
      name: 'OVK',
      contactPerson: 'Thabo Mokoena',
      phone: '************',
      email: '<EMAIL>',
      address: '987 Farm Road, Ladybrand, Free State',
      products: ['Lucerne', 'Maize', 'Molasses Meal', 'Mineral Lick'],
      rating: 4.3,
      lastOrderDate: '2024-02-10',
      totalSpent: 105000,
      paymentTerms: '30 days',
      notes: 'Reliable delivery, even to remote areas',
      status: 'active'
    },
    {
      id: 'SUP007',
      name: 'Meadow Feeds',
      contactPerson: 'David Smith',
      phone: '************',
      email: '<EMAIL>',
      address: '246 Industrial Park, Durban, KwaZulu-Natal',
      products: ['Protein Supplement', 'Maize', 'Molasses Meal'],
      rating: 3.9,
      lastOrderDate: '2023-12-15',
      totalSpent: 75000,
      paymentTerms: '14 days',
      notes: 'Specializes in custom feed mixes',
      status: 'inactive'
    }
  ]);

  const handleAddNew = () => {
    setSelectedSupplier(null);
    setOpenDialog(true);
  };

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setSuppliers(suppliers.filter(supplier => supplier.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="success" size="small" />;
      case 'inactive':
        return <Chip label="Inactive" color="default" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getFilteredSuppliers = () => {
    if (!searchTerm) return suppliers;
    
    const term = searchTerm.toLowerCase();
    return suppliers.filter(supplier => 
      supplier.name.toLowerCase().includes(term) ||
      supplier.contactPerson.toLowerCase().includes(term) ||
      supplier.products.some(product => product.toLowerCase().includes(term)) ||
      supplier.email.toLowerCase().includes(term) ||
      supplier.notes.toLowerCase().includes(term)
    );
  };

  const getTotalSpent = () => {
    return suppliers.reduce((total, supplier) => total + supplier.totalSpent, 0);
  };

  const getTopSuppliers = () => {
    return [...suppliers]
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 3);
  };

  const getProductCategories = () => {
    const products = new Set<string>();
    suppliers.forEach(supplier => {
      supplier.products.forEach(product => products.add(product));
    });
    return Array.from(products);
  };

  const getSuppliersForProduct = (product: string) => {
    return suppliers.filter(supplier => 
      supplier.products.includes(product) && supplier.status === 'active'
    ).length;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-6"
      style={{ background: 'linear-gradient(to right, #f0f7ff, #e6f0ff)' }}
    >
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
          Feed Suppliers
        </Typography>
        <CustomButton
          variant="contained"
          color="primary"
          startIcon={<Add />}
          onClick={handleAddNew}
          sx={{ borderRadius: '8px', boxShadow: '0 4px 10px rgba(0,0,0,0.1)' }}
        >
          Add Supplier
        </CustomButton>
      </Box>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Supplier Summary
              </Typography>
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {suppliers.filter(s => s.status === 'active').length}
                      </Typography>
                      <Typography variant="body2">Active Suppliers</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {formatCurrency(getTotalSpent())}
                      </Typography>
                      <Typography variant="body2">Total Spent</Typography>
                    </Box>
                  </Grid>
                </Grid>
                
                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Top Suppliers
                  </Typography>
                  {getTopSuppliers().map(supplier => (
                    <Box key={supplier.id} display="flex" justifyContent="space-between" p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1} mb={1}>
                      <Box>
                        <Typography variant="subtitle2" fontWeight="bold">
                          {supplier.name}
                        </Typography>
                        <Box display="flex" alignItems="center">
                          <Rating value={supplier.rating} precision={0.5} size="small" readOnly />
                        </Box>
                      </Box>
                      <Typography variant="subtitle2" fontWeight="bold" color="primary">
                        {formatCurrency(supplier.totalSpent)}
                      </Typography>
                    </Box>
                  ))}
                </Box>
                
                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Product Categories
                  </Typography>
                  <Grid container spacing={1}>
                    {getProductCategories().map(product => (
                      <Grid item xs={6} key={product}>
                        <Box display="flex" justifyContent="space-between" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                          <Typography variant="body2">{product}</Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {getSuppliersForProduct(product)}
                          </Typography>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={8}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6" fontWeight="bold">
                  Supplier Directory
                </Typography>
                <TextField
                  size="small"
                  placeholder="Search suppliers..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: <Search fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                  sx={{ width: 250 }}
                />
              </Box>
              
              <Grid container spacing={2}>
                {getFilteredSuppliers().map(supplier => (
                  <Grid item xs={12} md={6} key={supplier.id}>
                    <Card variant="outlined" sx={{ borderRadius: 2 }}>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                          <Box>
                            <Typography variant="h6" fontWeight="bold">
                              {supplier.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {supplier.contactPerson}
                            </Typography>
                          </Box>
                          {getStatusChip(supplier.status)}
                        </Box>
                        
                        <Box display="flex" alignItems="center" mt={1}>
                          <Rating value={supplier.rating} precision={0.5} size="small" readOnly />
                          <Typography variant="body2" color="text.secondary" ml={1}>
                            ({supplier.rating})
                          </Typography>
                        </Box>
                        
                        <Box mt={2}>
                          <Box display="flex" alignItems="center" mb={0.5}>
                            <Phone fontSize="small" color="action" sx={{ mr: 1 }} />
                            <Typography variant="body2">{supplier.phone}</Typography>
                          </Box>
                          <Box display="flex" alignItems="center" mb={0.5}>
                            <Email fontSize="small" color="action" sx={{ mr: 1 }} />
                            <Typography variant="body2">{supplier.email}</Typography>
                          </Box>
                          <Box display="flex" alignItems="flex-start" mb={0.5}>
                            <LocationOn fontSize="small" color="action" sx={{ mr: 1, mt: 0.3 }} />
                            <Typography variant="body2">{supplier.address}</Typography>
                          </Box>
                        </Box>
                        
                        <Box mt={2}>
                          <Typography variant="body2" fontWeight="medium">
                            Products:
                          </Typography>
                          <Box display="flex" flexWrap="wrap" gap={0.5} mt={0.5}>
                            {supplier.products.map((product, idx) => (
                              <Chip 
                                key={idx}
                                label={product}
                                size="small"
                                color="primary"
                                variant="outlined"
                              />
                            ))}
                          </Box>
                        </Box>
                        
                        <Box display="flex" justifyContent="space-between" mt={2}>
                          <Typography variant="body2" color="text.secondary">
                            Last Order: {formatDate(supplier.lastOrderDate)}
                          </Typography>
                          <Typography variant="body2" fontWeight="bold" color="primary">
                            {formatCurrency(supplier.totalSpent)}
                          </Typography>
                        </Box>
                        
                        <Box display="flex" justifyContent="flex-end" mt={1}>
                          <IconButton size="small" color="primary" onClick={() => handleEdit(supplier)}>
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" color="error" onClick={() => handleDelete(supplier.id)}>
                            <Delete fontSize="small" />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
                {getFilteredSuppliers().length === 0 && (
                  <Grid item xs={12}>
                    <Box display="flex" alignItems="center" justifyContent="center" height={200}>
                      <Typography variant="body1" color="text.secondary">
                        No suppliers found matching your search
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', overflow: 'hidden' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table>
              <TableHead sx={{ bgcolor: 'primary.main' }}>
                <TableRow>
                  <TableCell sx={{ color: 'white' }}>Name</TableCell>
                  <TableCell sx={{ color: 'white' }}>Contact Person</TableCell>
                  <TableCell sx={{ color: 'white' }}>Phone</TableCell>
                  <TableCell sx={{ color: 'white' }}>Email</TableCell>
                  <TableCell sx={{ color: 'white' }}>Products</TableCell>
                  <TableCell sx={{ color: 'white' }}>Rating</TableCell>
                  <TableCell sx={{ color: 'white' }}>Last Order</TableCell>
                  <TableCell sx={{ color: 'white' }}>Total Spent</TableCell>
                  <TableCell sx={{ color: 'white' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getFilteredSuppliers().map((supplier) => (
                  <TableRow key={supplier.id} hover>
                    <TableCell>{supplier.name}</TableCell>
                    <TableCell>{supplier.contactPerson}</TableCell>
                    <TableCell>{supplier.phone}</TableCell>
                    <TableCell>{supplier.email}</TableCell>
                    <TableCell>
                      {supplier.products.map((product, idx) => (
                        <Chip 
                          key={idx}
                          label={product}
                          size="small"
                          sx={{ mr: 0.5, mb: 0.5 }}
                        />
                      ))}
                    </TableCell>
                    <TableCell>
                      <Rating value={supplier.rating} precision={0.5} size="small" readOnly />
                    </TableCell>
                    <TableCell>{formatDate(supplier.lastOrderDate)}</TableCell>
                    <TableCell>{formatCurrency(supplier.totalSpent)}</TableCell>
                    <TableCell>{getStatusChip(supplier.status)}</TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(supplier)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(supplier.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedSupplier ? 'Edit Supplier' : 'Add New Supplier'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="name"
                label="Supplier Name"
                defaultValue={selectedSupplier?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="contactPerson"
                label="Contact Person"
                defaultValue={selectedSupplier?.contactPerson || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="phone"
                label="Phone"
                defaultValue={selectedSupplier?.phone || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="email"
                label="Email"
                defaultValue={selectedSupplier?.email || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="address"
                label="Address"
                defaultValue={selectedSupplier?.address || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Products
              </Typography>
              <Grid container spacing={1}>
                {getProductCategories().map(product => (
                  <Grid item key={product}>
                    <Chip 
                      label={product}
                      color={selectedSupplier?.products.includes(product) ? "primary" : "default"}
                      onClick={() => {/* Toggle selection */}}
                    />
                  </Grid>
                ))}
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box mt={2}>
                <Typography component="legend">Rating</Typography>
                <Rating
                  name="supplier-rating"
                  defaultValue={selectedSupplier?.rating || 0}
                  precision={0.5}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedSupplier?.status || 'active'}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="lastOrderDate"
                label="Last Order Date"
                type="date"
                defaultValue={selectedSupplier?.lastOrderDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="totalSpent"
                label="Total Spent (ZAR)"
                type="number"
                defaultValue={selectedSupplier?.totalSpent || 0}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="paymentTerms"
                label="Payment Terms"
                defaultValue={selectedSupplier?.paymentTerms || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedSupplier?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedSupplier ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default Suppliers;
