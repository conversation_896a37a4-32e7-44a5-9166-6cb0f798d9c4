import React from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';

interface ThemeBackgroundWrapperProps {
  children: React.ReactNode;
  applyToBody?: boolean;
}

const ThemeBackgroundWrapper: React.FC<ThemeBackgroundWrapperProps> = ({ 
  children, 
  applyToBody = true 
}) => {
  const theme = useTheme();
  const { blendMode, currentColor, availableColors } = useThemeContext();

  const themeColor = availableColors[currentColor];

  // Function to get background based on blend mode
  const getBackground = () => {
    switch (blendMode) {
      case 'lightBlended':
        return `linear-gradient(135deg, ${alpha('#f8fafc', 0.95)}, ${alpha(themeColor.lighter, 0.1)})`;

      case 'darkBlended':
        return `linear-gradient(135deg, ${alpha('#121212', 0.95)}, ${alpha(themeColor.primary, 0.1)})`;

      case 'day':
        return `linear-gradient(135deg, ${alpha('#fef3c7', 0.9)}, ${alpha(themeColor.lighter, 0.2)}, ${alpha('#fbbf24', 0.1)})`;

      case 'night':
        return `linear-gradient(135deg, ${alpha('#1e1b4b', 0.95)}, ${alpha('#312e81', 0.8)}, ${alpha(themeColor.primary, 0.1)})`;

      case 'auto':
        const hour = new Date().getHours();
        const isDayTime = hour >= 6 && hour < 18;
        return isDayTime 
          ? `linear-gradient(135deg, ${alpha('#fef3c7', 0.9)}, ${alpha(themeColor.lighter, 0.2)})`
          : `linear-gradient(135deg, ${alpha('#1e1b4b', 0.95)}, ${alpha(themeColor.primary, 0.1)})`;

      case 'light':
        return theme.palette.background.default;

      case 'dark':
        return theme.palette.background.default;

      default:
        return theme.palette.background.default;
    }
  };

  // Apply background to body element
  React.useEffect(() => {
    if (applyToBody) {
      const background = getBackground();
      document.body.style.background = background;
      document.body.style.minHeight = '100vh';
      
      // Also apply to html element for full coverage
      document.documentElement.style.background = background;
      
      return () => {
        // Cleanup on unmount
        document.body.style.background = '';
        document.documentElement.style.background = '';
      };
    }
  }, [blendMode, currentColor, applyToBody]);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: getBackground(),
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: getBackground(),
          zIndex: -1,
          pointerEvents: 'none'
        }
      }}
    >
      {children}
    </Box>
  );
};

export default ThemeBackgroundWrapper;
