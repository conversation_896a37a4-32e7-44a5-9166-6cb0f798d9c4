
// Mock data for the application
export const mockData = {
  users: [
    { _id: '1', name: '<PERSON>', email: '<EMAIL>', role: 'admin' },
    { _id: '2', name: '<PERSON>', email: '<EMAIL>', role: 'user' }
  ],
  animals: [
    { _id: '1', name: 'Tshe<PERSON><PERSON>', type: 'cattle', age: 4, status: 'healthy', tagNumber: 'CTL-2020-001' },
    { _id: '2', name: '<PERSON><PERSON><PERSON>', type: 'cattle', age: 5, status: 'healthy', tagNumber: 'CTL-2019-002' },
    { _id: '3', name: '<PERSON><PERSON>', type: 'cattle', age: 3, status: 'pregnant', tagNumber: 'CTL-2021-003' },
    { _id: '4', name: 'Botha', type: 'cattle', age: 4, status: 'healthy', tagNumber: 'CTL-2020-004' },
    { _id: '5', name: '<PERSON><PERSON><PERSON>', type: 'cattle', age: 2, status: 'healthy', tagNumber: 'CTL-2022-005' }
  ],
  feeds: [
    { _id: '1', name: '<PERSON>', quantity: 500, unit: 'kg', cost: 1000 },
    { _id: '2', name: 'Grain', quantity: 200, unit: 'kg', cost: 800 }
  ],
  health: [
    { _id: '1', animalId: '1', animalName: 'Tshepiso', condition: 'Vaccination', date: '2023-01-15', notes: 'Annual vaccination against Brucellosis' },
    { _id: '2', animalId: '2', animalName: 'Tumelo', condition: 'Check-up', date: '2023-02-20', notes: 'Regular health check' },
    { _id: '3', animalId: '3', animalName: 'Lerato', condition: 'Pregnancy Check', date: '2023-03-10', notes: 'Confirmed pregnancy' },
    { _id: '4', animalId: '4', animalName: 'Botha', condition: 'Treatment', date: '2023-04-05', notes: 'Minor wound treatment' },
    { _id: '5', animalId: '5', animalName: 'Naledi', condition: 'Vaccination', date: '2023-05-15', notes: 'Booster shot for Anthrax prevention' }
  ],
  breeding: [
    { _id: '1', femaleId: '1', femaleName: 'Tshepiso', maleId: '2', maleName: 'Tumelo', date: '2023-03-10', status: 'successful', expectedDueDate: '2023-12-15' },
    { _id: '2', femaleId: '3', femaleName: 'Lerato', maleId: '2', maleName: 'Tumelo', date: '2023-06-20', status: 'confirmed', expectedDueDate: '2024-03-25' },
    { _id: '3', femaleId: '5', femaleName: 'Naledi', maleId: '4', maleName: 'Botha', date: '2023-09-05', status: 'pending', expectedDueDate: '2024-06-10' }
  ],
  commercial: [
    { _id: '1', product: 'Milk', quantity: 100, unit: 'liters', price: 500, date: '2023-04-05', animalId: '1', animalName: 'Tshepiso' },
    { _id: '2', product: 'Wool', quantity: 50, unit: 'kg', price: 1000, date: '2023-04-10', animalId: null, animalName: null },
    { _id: '3', product: 'Beef', quantity: 250, unit: 'kg', price: 12500, date: '2023-05-15', animalId: '4', animalName: 'Botha' },
    { _id: '4', product: 'Milk', quantity: 80, unit: 'liters', price: 400, date: '2023-06-20', animalId: '3', animalName: 'Lerato' },
    { _id: '5', product: 'Leather', quantity: 30, unit: 'sq.m', price: 6000, date: '2023-07-25', animalId: null, animalName: null }
  ]
};
