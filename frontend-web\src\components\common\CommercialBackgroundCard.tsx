import React, { useState, useEffect } from 'react';
import { Box, Card, CardProps, alpha, useTheme } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { getSelectableStyles } from '../../utils/selectionUtils';

interface CommercialBackgroundCardProps extends CardProps {
  images?: string[];
  interval?: number;
  opacity?: number;
  primaryColor?: string;
  secondaryColor?: string;
  height?: string | number;
  borderRadius?: string | number;
}

/**
 * A card component with rotating commercial background images
 * This component is designed to display commercial images as card backgrounds
 */
const CommercialBackgroundCard: React.FC<CommercialBackgroundCardProps> = ({
  images = [],
  interval = 10000,
  opacity = 0.7,
  primaryColor,
  secondaryColor,
  height = '100%',
  borderRadius = '12px',
  children,
  sx = {},
  ...props
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Use theme colors if not provided
  const cardPrimaryColor = primaryColor || themeColor.primary;
  const cardSecondaryColor = secondaryColor || themeColor.secondary;

  // Default commercial images if none provided
  const defaultImages = [
    '/images/modules/commercial/commercial-main.webp',
    '/images/modules/commercial/commercial-1.jpeg',
    '/images/modules/commercial/commercial-2.jpeg',
    '/images/modules/commercial/commercial-3.jpeg',
  ];

  const displayImages = images.length > 0 ? images : defaultImages;

  useEffect(() => {
    // Only set up rotation if there are multiple images
    if (displayImages.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % displayImages.length);
    }, interval);

    return () => clearInterval(timer);
  }, [displayImages, interval]);

  const currentImage = displayImages[currentImageIndex];

  return (
    <Card
      sx={{
        height,
        borderRadius,
        overflow: 'hidden',
        position: 'relative',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: `1px solid ${alpha(cardPrimaryColor, 0.1)}`,
        transition: 'transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease',
        '&:hover': {
          transform: 'translateY(-5px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
          borderColor: alpha(cardPrimaryColor, 0.3)
        },
        ...getSelectableStyles(),
        ...sx
      }}
      {...props}
    >
      {/* Background Image with Gradient Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${currentImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          transition: 'background-image 1s ease-in-out',
          filter: 'brightness(0.95)',
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `linear-gradient(135deg, ${alpha(cardPrimaryColor, opacity)}, ${alpha(cardSecondaryColor, opacity)})`,
            backdropFilter: 'blur(2px)',
            zIndex: 1
          }
        }}
      />

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          p: 3,
          color: 'white',
          ...getSelectableStyles()
        }}
      >
        {children}
      </Box>
    </Card>
  );
};

export default CommercialBackgroundCard;
