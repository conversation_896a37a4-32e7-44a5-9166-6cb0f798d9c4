/**
 * MongoDB Atlas SQL Federated Database Service
 * 
 * This service provides SQL-like querying capabilities for complex analytics
 * and reporting using MongoDB Atlas SQL Interface.
 */

const { MongoClient } = require('mongodb');
const logger = require('../utils/logger');

class MongoAtlasSqlService {
  constructor() {
    this.client = null;
    this.db = null;
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_SQL_URI;
    this.dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';
  }

  /**
   * Connect to MongoDB Atlas SQL Interface
   */
  async connect() {
    try {
      if (this.isConnected && this.client) {
        return this.db;
      }

      logger.info('Connecting to MongoDB Atlas SQL Interface...');
      
      this.client = new MongoClient(this.connectionString, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        ssl: true,
        authSource: 'admin'
      });

      await this.client.connect();
      this.db = this.client.db(this.dbName);
      this.isConnected = true;

      logger.info('Successfully connected to MongoDB Atlas SQL Interface');
      return this.db;
    } catch (error) {
      logger.error('Failed to connect to MongoDB Atlas SQL Interface:', error);
      throw error;
    }
  }

  /**
   * Execute SQL-like aggregation queries for analytics
   */
  async executeAnalyticsQuery(collection, pipeline) {
    try {
      await this.connect();
      
      const result = await this.db.collection(collection).aggregate(pipeline).toArray();
      logger.info(`Analytics query executed on ${collection}, returned ${result.length} results`);
      
      return result;
    } catch (error) {
      logger.error(`Error executing analytics query on ${collection}:`, error);
      throw error;
    }
  }

  /**
   * Get livestock performance analytics
   */
  async getLivestockPerformanceAnalytics() {
    const pipeline = [
      {
        $lookup: {
          from: 'health_records',
          localField: '_id',
          foreignField: 'animalId',
          as: 'healthRecords'
        }
      },
      {
        $lookup: {
          from: 'breeding_records',
          localField: '_id',
          foreignField: 'animalId',
          as: 'breedingRecords'
        }
      },
      {
        $addFields: {
          healthScore: {
            $avg: '$healthRecords.healthScore'
          },
          breedingSuccess: {
            $size: {
              $filter: {
                input: '$breedingRecords',
                cond: { $eq: ['$$this.status', 'successful'] }
              }
            }
          },
          totalBreedings: { $size: '$breedingRecords' }
        }
      },
      {
        $project: {
          animalId: '$_id',
          tagNumber: 1,
          breed: 1,
          age: 1,
          weight: 1,
          healthScore: 1,
          breedingSuccess: 1,
          breedingSuccessRate: {
            $cond: {
              if: { $gt: ['$totalBreedings', 0] },
              then: { $divide: ['$breedingSuccess', '$totalBreedings'] },
              else: 0
            }
          }
        }
      }
    ];

    return await this.executeAnalyticsQuery('animals', pipeline);
  }

  /**
   * Get financial performance analytics
   */
  async getFinancialAnalytics(startDate, endDate) {
    const pipeline = [
      {
        $match: {
          date: {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
          }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            category: '$category'
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $group: {
          _id: {
            year: '$_id.year',
            month: '$_id.month'
          },
          categories: {
            $push: {
              category: '$_id.category',
              amount: '$totalAmount',
              count: '$count'
            }
          },
          totalMonthly: { $sum: '$totalAmount' }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ];

    return await this.executeAnalyticsQuery('financial_records', pipeline);
  }

  /**
   * Get health trends analytics
   */
  async getHealthTrendsAnalytics() {
    const pipeline = [
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' },
            condition: '$condition'
          },
          cases: { $sum: 1 },
          avgSeverity: { $avg: '$severity' }
        }
      },
      {
        $group: {
          _id: {
            year: '$_id.year',
            month: '$_id.month'
          },
          conditions: {
            $push: {
              condition: '$_id.condition',
              cases: '$cases',
              avgSeverity: '$avgSeverity'
            }
          },
          totalCases: { $sum: '$cases' }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ];

    return await this.executeAnalyticsQuery('health_records', pipeline);
  }

  /**
   * Get breeding efficiency analytics
   */
  async getBreedingEfficiencyAnalytics() {
    const pipeline = [
      {
        $lookup: {
          from: 'animals',
          localField: 'animalId',
          foreignField: '_id',
          as: 'animal'
        }
      },
      {
        $unwind: '$animal'
      },
      {
        $group: {
          _id: {
            breed: '$animal.breed',
            year: { $year: '$breedingDate' }
          },
          totalBreedings: { $sum: 1 },
          successfulBreedings: {
            $sum: {
              $cond: [{ $eq: ['$status', 'successful'] }, 1, 0]
            }
          },
          avgGestationPeriod: {
            $avg: {
              $cond: [
                { $eq: ['$status', 'successful'] },
                '$gestationPeriod',
                null
              ]
            }
          }
        }
      },
      {
        $addFields: {
          successRate: {
            $divide: ['$successfulBreedings', '$totalBreedings']
          }
        }
      },
      {
        $sort: { '_id.year': -1, successRate: -1 }
      }
    ];

    return await this.executeAnalyticsQuery('breeding_records', pipeline);
  }

  /**
   * Get feed efficiency analytics
   */
  async getFeedEfficiencyAnalytics() {
    const pipeline = [
      {
        $lookup: {
          from: 'animals',
          localField: 'animalId',
          foreignField: '_id',
          as: 'animal'
        }
      },
      {
        $unwind: '$animal'
      },
      {
        $group: {
          _id: {
            animalId: '$animalId',
            feedType: '$feedType',
            month: { $month: '$date' },
            year: { $year: '$date' }
          },
          totalFeedConsumed: { $sum: '$quantity' },
          feedingDays: { $sum: 1 },
          avgWeightGain: { $avg: '$animal.weight' }
        }
      },
      {
        $addFields: {
          feedEfficiency: {
            $divide: ['$avgWeightGain', '$totalFeedConsumed']
          }
        }
      },
      {
        $sort: { feedEfficiency: -1 }
      }
    ];

    return await this.executeAnalyticsQuery('feeding_records', pipeline);
  }

  /**
   * Close the connection
   */
  async disconnect() {
    try {
      if (this.client) {
        await this.client.close();
        this.isConnected = false;
        logger.info('Disconnected from MongoDB Atlas SQL Interface');
      }
    } catch (error) {
      logger.error('Error disconnecting from MongoDB Atlas SQL Interface:', error);
    }
  }
}

module.exports = new MongoAtlasSqlService();
