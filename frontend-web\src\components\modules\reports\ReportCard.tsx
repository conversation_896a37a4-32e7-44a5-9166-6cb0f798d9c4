import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme, alpha } from '@mui/material';
import { getSelectableStyles } from '../../../utils/selectionUtils';
import useReportItemSelection from '../../../hooks/useReportItemSelection';
import ReportButton from './ReportButton';

interface ReportCardProps {
  report: {
    id: string;
    title: string;
    type: string;
    date: string;
    summary: string;
    status: 'new' | 'viewed' | 'archived';
    metrics?: {
      label: string;
      value: string;
      change?: string;
      trend?: 'up' | 'down' | 'neutral';
    }[];
  };
  onSelect?: (report: any) => void;
  onView?: (report: any) => void;
  selected?: boolean;
}

const ReportCard: React.FC<ReportCardProps> = ({ report, onSelect, onView, selected = false }) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  // Use the report item selection hook to ensure the card is selectable
  const cardRef = useReportItemSelection();

  const getTrendColor = (trend?: 'up' | 'down' | 'neutral') => {
    const colors = {
      up: 'text-green-600',
      down: 'text-red-600',
      neutral: 'text-gray-600'
    };
    return colors[trend || 'neutral'];
  };

  // Apply selectable styles
  const selectableStyles = getSelectableStyles();

  // Get primary color from theme
  const primaryColor = theme.palette.primary.main;
  const primaryLightColor = alpha(primaryColor, 0.1);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5, boxShadow: '0 10px 20px rgba(0,0,0,0.1)' }}
      transition={{ duration: 0.3 }}
      className={`rounded-xl shadow-md p-6 cursor-pointer transition-all duration-300 relative z-10`}
      style={{
        ...selectableStyles,
        backgroundColor: selected ? primaryLightColor : 'white',
        border: selected ? `1px solid ${primaryColor}` : '1px solid transparent',
        transform: isHovered ? 'translateY(-5px)' : 'translateY(0)',
        boxShadow: isHovered ? '0 10px 20px rgba(0,0,0,0.1)' : '0 4px 6px rgba(0,0,0,0.05)',
        pointerEvents: 'auto'
      }}
      ref={cardRef}
      onClick={() => onSelect && onSelect(report)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold">{report.title}</h3>
          <p className="text-gray-500 text-sm mt-1">Generated on {report.date}</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
            {report.type}
          </span>
          {report.status === 'new' && (
            <span className="px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-800">
              New
            </span>
          )}
        </div>
      </div>

      <p className="text-gray-600 mt-4">{report.summary}</p>

      {report.metrics && (
        <div className="mt-6 grid grid-cols-2 gap-4">
          {report.metrics.map((metric, index) => (
            <div key={index} className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">{metric.label}</p>
              <div className="flex items-baseline mt-1">
                <span className="text-lg font-semibold">{metric.value}</span>
                {metric.change && (
                  <span className={`ml-2 text-sm ${getTrendColor(metric.trend)}`}>
                    {metric.change}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 flex space-x-3">
        <ReportButton
          variant="contained"
          color="primary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation(); // Prevent triggering the card's onClick
            onView && onView(report);
          }}
          style={{
            flex: 1
          }}
        >
          View Full Report
        </ReportButton>
        <ReportButton
          variant="text"
          color="primary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation(); // Prevent triggering the card's onClick
            // Download functionality would go here
            console.log('Download report', report.id);
          }}
          style={{
            minWidth: '40px',
            padding: '8px'
          }}
        >
          <span>⬇️</span>
        </ReportButton>
        <ReportButton
          variant="text"
          color="primary"
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            e.stopPropagation(); // Prevent triggering the card's onClick
            // Share functionality would go here
            console.log('Share report', report.id);
          }}
          style={{
            minWidth: '40px',
            padding: '8px'
          }}
        >
          <span>📤</span>
        </ReportButton>
      </div>
    </motion.div>
  );
};

export default ReportCard;