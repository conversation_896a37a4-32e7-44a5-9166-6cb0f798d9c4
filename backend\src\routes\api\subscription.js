/**
 * Subscription Routes
 * 
 * API routes for subscription management
 */

const express = require('express');
const router = express.Router();
const SubscriptionController = require('../../controllers/subscriptionController');
const { authenticateToken: authenticate } = require('../../middleware/auth');
const { requirePermission } = require('../../middleware/authMiddleware');

// Public routes (no authentication required)

// Get available subscription plans
router.get('/plans', SubscriptionController.getPlans);

// Get province-specific demo data
router.get('/province/:province', SubscriptionController.getProvinceData);

// Protected routes (authentication required)

// Get current user subscription
router.get('/current', authenticate, SubscriptionController.getCurrentSubscription);

// Check feature access
router.post('/check-access', authenticate, SubscriptionController.checkAccess);

// Track feature usage
router.post('/track-usage', authenticate, SubscriptionController.trackUsage);

// Update usage statistics
router.post('/update-usage', authenticate, SubscriptionController.updateUsage);

// Upgrade subscription
router.post('/upgrade', authenticate, SubscriptionController.upgradeSubscription);

// Cancel subscription
router.post('/cancel', authenticate, SubscriptionController.cancelSubscription);

// Get conversion metrics for current user
router.get('/conversion-metrics', authenticate, SubscriptionController.getConversionMetrics);

// Admin routes (admin access required)

// Initialize subscription system
router.post('/initialize', authenticate, SubscriptionController.initializeSystem);

// Get conversion prospects
router.get('/prospects', authenticate, SubscriptionController.getConversionProspects);

// Get subscription analytics
router.get('/analytics', authenticate, SubscriptionController.getAnalytics);

module.exports = router;
