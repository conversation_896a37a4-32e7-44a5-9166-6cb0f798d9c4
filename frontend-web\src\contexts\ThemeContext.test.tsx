import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, useThemeContext } from './ThemeContext';

// Test component that uses the theme context
const TestComponent = () => {
  const { mode, toggleMode, currentColor, setThemeColor } = useThemeContext();
  
  return (
    <div>
      <div data-testid="theme-mode">{mode}</div>
      <div data-testid="theme-color">{currentColor}</div>
      <button data-testid="toggle-mode" onClick={toggleMode}>Toggle Mode</button>
      <button data-testid="set-blue" onClick={() => setThemeColor('blue')}>Set Blue</button>
      <button data-testid="set-purple" onClick={() => setThemeColor('purple')}>Set Purple</button>
    </div>
  );
};

describe('ThemeContext', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  test('provides default theme values', () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );
    
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('light');
    expect(screen.getByTestId('theme-color')).toHaveTextContent('neonTeal');
  });

  test('toggles between light and dark mode', () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );
    
    // Initial mode should be light
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('light');
    
    // Toggle to dark mode
    fireEvent.click(screen.getByTestId('toggle-mode'));
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('dark');
    
    // Toggle back to light mode
    fireEvent.click(screen.getByTestId('toggle-mode'));
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('light');
  });

  test('changes theme color', () => {
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );
    
    // Initial color should be neonTeal
    expect(screen.getByTestId('theme-color')).toHaveTextContent('neonTeal');
    
    // Change to blue
    fireEvent.click(screen.getByTestId('set-blue'));
    expect(screen.getByTestId('theme-color')).toHaveTextContent('blue');
    
    // Change to purple
    fireEvent.click(screen.getByTestId('set-purple'));
    expect(screen.getByTestId('theme-color')).toHaveTextContent('purple');
  });

  test('persists theme preferences in localStorage', () => {
    const { unmount } = render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );
    
    // Change theme settings
    fireEvent.click(screen.getByTestId('toggle-mode'));
    fireEvent.click(screen.getByTestId('set-purple'));
    
    // Unmount and remount to test persistence
    unmount();
    
    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );
    
    // Settings should be persisted
    expect(screen.getByTestId('theme-mode')).toHaveTextContent('dark');
    expect(screen.getByTestId('theme-color')).toHaveTextContent('purple');
  });
});
