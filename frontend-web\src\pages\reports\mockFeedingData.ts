// Feed Schedule Type
export interface FeedSchedule {
  id: string;
  groupId: string;
  groupName: string;
  feedType: string;
  time: string;
  quantity: number;
  unit: string;
  frequency: string;
  status: string;
}

// Nutrition Plan Component Type
export interface NutritionComponent {
  feedType: string;
  percentage: number;
  purpose: string;
}

// Nutrition Plan Type
export interface NutritionPlan {
  id: string;
  name: string;
  animalType: string;
  components: NutritionComponent[];
  dailyAmount: number;
  unit: string;
}

// Mock Feed Schedules
export const mockFeedSchedules: FeedSchedule[] = [
  {
    id: 'FS001',
    groupId: 'G001',
    groupName: 'Kgomo <PERSON>e (Dairy Cattle)',
    feedType: 'Mixed Feed',
    time: '06:00',
    quantity: 250,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS002',
    groupId: 'G002',
    groupName: 'Kgomo Tsa Nama (Beef Cattle)',
    feedType: 'Hay and Supplements',
    time: '15:00',
    quantity: 180,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS003',
    groupId: 'G003',
    groupName: 'Dinku (Sheep)',
    feedType: 'Grain Mix',
    time: '07:30',
    quantity: 120,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS004',
    groupId: 'G004',
    groupName: 'Dipodi (Goats)',
    feedType: 'Forage Mix',
    time: '16:30',
    quantity: 90,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  },
  {
    id: 'FS005',
    groupId: 'G005',
    groupName: 'Kgomo Tsa Mashi (Calves)',
    feedType: 'Calf Starter',
    time: '08:00',
    quantity: 75,
    unit: 'kg',
    frequency: 'Daily',
    status: 'Active'
  }
];

// Mock Nutrition Plans
export const mockNutritionPlans: NutritionPlan[] = [
  {
    id: 'NP001',
    name: 'Leano la Phepo ya Kgomo Tsa Lebese',
    animalType: 'Kgomo Tsa Lebese (Dairy Cattle)',
    components: [
      { feedType: 'Alfalfa Hay', percentage: 40, purpose: 'Fiber source' },
      { feedType: 'Grain Mix', percentage: 35, purpose: 'Energy source' },
      { feedType: 'Protein Supplement', percentage: 15, purpose: 'Protein source' },
      { feedType: 'Minerals', percentage: 10, purpose: 'Nutritional balance' }
    ],
    dailyAmount: 25,
    unit: 'kg'
  },
  {
    id: 'NP002',
    name: 'Leano la Phepo ya Kgomo Tsa Nama',
    animalType: 'Kgomo Tsa Nama (Beef Cattle)',
    components: [
      { feedType: 'Grass Hay', percentage: 45, purpose: 'Base feed' },
      { feedType: 'Corn Silage', percentage: 30, purpose: 'Energy source' },
      { feedType: 'Protein Mix', percentage: 20, purpose: 'Growth support' },
      { feedType: 'Mineral Mix', percentage: 5, purpose: 'Mineral supplementation' }
    ],
    dailyAmount: 20,
    unit: 'kg'
  },
  {
    id: 'NP003',
    name: 'Leano la Phepo ya Dinku',
    animalType: 'Dinku (Sheep)',
    components: [
      { feedType: 'Pasture Grass', percentage: 50, purpose: 'Primary feed' },
      { feedType: 'Grain Mix', percentage: 25, purpose: 'Energy supplement' },
      { feedType: 'Protein Pellets', percentage: 15, purpose: 'Protein source' },
      { feedType: 'Mineral Block', percentage: 10, purpose: 'Essential minerals' }
    ],
    dailyAmount: 3,
    unit: 'kg'
  },
  {
    id: 'NP004',
    name: 'Leano la Phepo ya Dipodi',
    animalType: 'Dipodi (Goats)',
    components: [
      { feedType: 'Browse Vegetation', percentage: 55, purpose: 'Natural diet' },
      { feedType: 'Alfalfa Pellets', percentage: 20, purpose: 'Protein source' },
      { feedType: 'Grain Mix', percentage: 15, purpose: 'Energy supplement' },
      { feedType: 'Mineral Mix', percentage: 10, purpose: 'Nutritional balance' }
    ],
    dailyAmount: 2.5,
    unit: 'kg'
  },
  {
    id: 'NP005',
    name: 'Leano la Phepo ya Kgomo Tsa Mashi',
    animalType: 'Kgomo Tsa Mashi (Calves)',
    components: [
      { feedType: 'Milk Replacer', percentage: 40, purpose: 'Primary nutrition' },
      { feedType: 'Calf Starter', percentage: 35, purpose: 'Rumen development' },
      { feedType: 'Fine Hay', percentage: 20, purpose: 'Fiber introduction' },
      { feedType: 'Vitamin Mix', percentage: 5, purpose: 'Growth support' }
    ],
    dailyAmount: 5,
    unit: 'kg'
  }
];
