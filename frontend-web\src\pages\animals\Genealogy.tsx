import React, { useState } from 'react';

interface Animal {
  id: string;
  name: string;
  tag: string;
  breed: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female';
}

interface GenealogyRecord {
  id: string;
  animal: Animal;
  sire?: Animal;
  dam?: Animal;
  offspring: Animal[];
}

const mockGenealogy: GenealogyRecord[] = [
  {
    id: 'GEN001',
    animal: {
      id: 'ANM001',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      tag: 'CTL-2024-001',
      breed: '<PERSON><PERSON>',
      dateOfBirth: '2022-03-15',
      gender: 'Female'
    },
    sire: {
      id: 'ANM007',
      name: '<PERSON><PERSON><PERSON>',
      tag: 'CTL-2020-007',
      breed: '<PERSON><PERSON>',
      dateOfBirth: '2020-01-10',
      gender: 'Male'
    },
    dam: {
      id: 'ANM008',
      name: '<PERSON><PERSON><PERSON>',
      tag: 'CTL-2019-008',
      breed: 'Nguni',
      dateOfBirth: '2019-05-20',
      gender: 'Female'
    },
    offspring: []
  },
  {
    id: 'GEN002',
    animal: {
      id: 'ANM002',
      name: '<PERSON><PERSON><PERSON>',
      tag: 'CTL-2024-002',
      breed: 'Bon<PERSON><PERSON>',
      dateOfBirth: '2021-06-20',
      gender: 'Male'
    },
    sire: {
      id: 'ANM009',
      name: 'Thabo',
      tag: 'CTL-2018-009',
      breed: 'Bonsmara',
      dateOfBirth: '2018-03-15',
      gender: 'Male'
    },
    dam: {
      id: 'ANM010',
      name: 'Dineo',
      tag: 'CTL-2019-010',
      breed: 'Bonsmara',
      dateOfBirth: '2019-07-12',
      gender: 'Female'
    },
    offspring: []
  },
  {
    id: 'GEN003',
    animal: {
      id: 'ANM003',
      name: 'Lesedi',
      tag: 'CTL-2024-003',
      breed: 'Afrikaner',
      dateOfBirth: '2020-08-12',
      gender: 'Female'
    },
    sire: {
      id: 'ANM011',
      name: 'Tau',
      tag: 'CTL-2017-011',
      breed: 'Afrikaner',
      dateOfBirth: '2017-04-22',
      gender: 'Male'
    },
    dam: {
      id: 'ANM012',
      name: 'Bontle',
      tag: 'CTL-2018-012',
      breed: 'Afrikaner',
      dateOfBirth: '2018-09-30',
      gender: 'Female'
    },
    offspring: [
      {
        id: 'ANM013',
        name: 'Mpho',
        tag: 'CTL-2024-013',
        breed: 'Afrikaner',
        dateOfBirth: '2024-01-15',
        gender: 'Female'
      }
    ]
  }
];

const AnimalGenealogy: React.FC = () => {
  const [selectedAnimal, setSelectedAnimal] = useState('ANM001');

  const genealogy = mockGenealogy.find(g => g.animal.id === selectedAnimal);

  const AnimalCard: React.FC<{ animal: Animal; role: string }> = ({ animal, role }) => (
    <div className="bg-white rounded-lg shadow-md p-4">
      <h3 className="text-lg font-semibold mb-2">{role}</h3>
      <div className="space-y-2">
        <p><span className="font-medium">Name:</span> {animal.name}</p>
        <p><span className="font-medium">Tag:</span> {animal.tag}</p>
        <p><span className="font-medium">Breed:</span> {animal.breed}</p>
        <p><span className="font-medium">Born:</span> {animal.dateOfBirth}</p>
        <p><span className="font-medium">Gender:</span> {animal.gender}</p>
      </div>
    </div>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Animal Genealogy</h1>
        <select
          className="border rounded-lg p-2"
          value={selectedAnimal}
          onChange={(e) => setSelectedAnimal(e.target.value)}
        >
          {mockGenealogy.map(g => (
            <option key={g.animal.id} value={g.animal.id}>
              {g.animal.name} ({g.animal.tag})
            </option>
          ))}
        </select>
      </div>

      {genealogy && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {genealogy.sire && (
              <AnimalCard animal={genealogy.sire} role="Sire" />
            )}
            <AnimalCard animal={genealogy.animal} role="Animal" />
            {genealogy.dam && (
              <AnimalCard animal={genealogy.dam} role="Dam" />
            )}
          </div>

          {genealogy.offspring.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Offspring</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {genealogy.offspring.map(offspring => (
                  <AnimalCard
                    key={offspring.id}
                    animal={offspring}
                    role="Offspring"
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AnimalGenealogy;