# AMPD Livestock Management System

## Overview
AMPD Livestock is a comprehensive farm management system designed for South African farmers. The application provides a modern, user-friendly interface for managing livestock, breeding, health records, financial data, and more.

## Features
- **Dashboard**: Overview of farm operations with key metrics and visualizations
- **Animal Management**: Track animal profiles, genealogy, and movements
- **Health Module**: Monitor animal health records, treatments, and disease tracking
- **Breeding Module**: Manage breeding programs with heat detection and birth alerts
- **Financial Module**: Track income, expenses, and generate financial reports
- **RFID Integration**: Track animals with RFID technology and Google Maps integration
- **Reports & Analysis**: Generate comprehensive reports across all modules

## Technology Stack
- **Frontend**: React, TypeScript, Material UI
- **State Management**: React Context API
- **Data Visualization**: Recharts
- **Mapping**: Google Maps API
- **Database**: MongoDB
- **Notifications**: SMS alerts via Clickatell

## Getting Started

### Prerequisites
- Node.js v18.x or v16.x (recommended)
- MongoDB account

### Installation
1. Clone the repository
2. Navigate to the frontend-web directory
3. Install dependencies:
```
npm install --legacy-peer-deps
```
4. Start the development server:
```
npm start
```

### Environment Variables
Create a `.env` file in the frontend-web directory with the following variables:
```
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyA9JZ_PcJQ4H7mmXWnUXVsCbDEEBgGEPWs
REACT_APP_MONGODB_URI=mongodb://atlas-sql-67f8f98a45471a2186bc6fe4-ew3ee.a.query.mongodb.net/ampd_livestock?ssl=true&authSource=admin
```

## Project Structure
- `src/components`: Reusable UI components
- `src/pages`: Main application pages organized by module
- `src/services`: API and data services
- `src/contexts`: React context providers
- `src/hooks`: Custom React hooks
- `src/utils`: Utility functions
- `src/types`: TypeScript type definitions
- `src/assets`: Static assets like images
- `src/styles`: Global styles and theme configuration

## Key Features

### Animal Management
- Comprehensive animal profiles
- Genealogy tracking
- Location tracking with Google Maps
- RFID integration

### Health Module
- Health records with Sotho/Tswana animal names
- Disease tracking
- Treatment management
- Vaccination schedules

### Breeding Module
- Heat detection calendar
- Breeding records
- Birth alerts via SMS, voice calls, and WhatsApp
- Genealogy visualization

### Financial Module
- Income and expense tracking in ZAR currency
- Financial reports and analysis
- Budget management

### Reports & Analysis
- Comprehensive reporting across all modules
- Data visualization with charts and graphs
- Exportable reports in multiple formats

## Accessibility Features
- Larger fonts and high contrast for elderly users
- Mobile-compatible design
- Offline capabilities for areas with poor connectivity

## Design Principles
- Modern UI inspired by Dashkit Bulma
- Consistent metallic blue color scheme
- Transitional effects and motion animations
- Responsive design for all screen sizes

## License
Proprietary - All rights reserved

## Contact
For support or inquiries, please contact the development team.
