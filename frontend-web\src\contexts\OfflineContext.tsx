import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import offlineSync from '../utils/offlineSync';

interface OfflineContextType {
  isOnline: boolean;
  pendingChanges: number;
  lastSyncTime: number | null;
  syncInProgress: boolean;
  synchronize: () => Promise<number>;
  clearCache: () => Promise<void>;
}

const OfflineContext = createContext<OfflineContextType | undefined>(undefined);

interface OfflineProviderProps {
  children: ReactNode;
}

export const OfflineProvider: React.FC<OfflineProviderProps> = ({ children }) => {
  const [isOnline, setIsOnline] = useState<boolean>(offlineSync.isOnline());
  const [pendingChanges, setPendingChanges] = useState<number>(0);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [syncInProgress, setSyncInProgress] = useState<boolean>(false);

  // Initialize sync status
  useEffect(() => {
    const initSyncStatus = async () => {
      try {
        const status = await offlineSync.getSyncStatus();
        setPendingChanges(status.pendingChanges);
        setLastSyncTime(status.lastSyncTime || null);
        setSyncInProgress(status.syncInProgress);
      } catch (error) {
        console.error('Error initializing sync status:', error);
      }
    };

    initSyncStatus();
  }, []);

  // Register connectivity listeners
  useEffect(() => {
    const cleanup = offlineSync.registerConnectivityListeners(
      // Online callback
      () => {
        setIsOnline(true);
        // Auto-sync when coming back online
        if (pendingChanges > 0) {
          synchronize();
        }
      },
      // Offline callback
      () => {
        setIsOnline(false);
      }
    );

    // Poll for sync status changes
    const statusInterval = setInterval(async () => {
      try {
        const status = await offlineSync.getSyncStatus();
        setPendingChanges(status.pendingChanges);
        setLastSyncTime(status.lastSyncTime || null);
        setSyncInProgress(status.syncInProgress);
      } catch (error) {
        console.error('Error polling sync status:', error);
      }
    }, 5000);

    return () => {
      cleanup();
      clearInterval(statusInterval);
    };
  }, [pendingChanges]);

  // Synchronize pending requests
  const synchronize = async (): Promise<number> => {
    if (!isOnline) {
      throw new Error('Cannot synchronize while offline');
    }

    try {
      setSyncInProgress(true);
      const syncCount = await offlineSync.synchronize();
      
      // Update status after sync
      const status = await offlineSync.getSyncStatus();
      setPendingChanges(status.pendingChanges);
      setLastSyncTime(status.lastSyncTime || null);
      setSyncInProgress(status.syncInProgress);
      
      return syncCount;
    } catch (error) {
      console.error('Error during synchronization:', error);
      setSyncInProgress(false);
      throw error;
    }
  };

  // Clear cache
  const clearCache = async (): Promise<void> => {
    try {
      await offlineSync.clearCache();
    } catch (error) {
      console.error('Error clearing cache:', error);
      throw error;
    }
  };

  const value: OfflineContextType = {
    isOnline,
    pendingChanges,
    lastSyncTime,
    syncInProgress,
    synchronize,
    clearCache
  };

  return (
    <OfflineContext.Provider value={value}>
      {children}
    </OfflineContext.Provider>
  );
};

export const useOffline = (): OfflineContextType => {
  const context = useContext(OfflineContext);
  if (context === undefined) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

export default OfflineContext;
