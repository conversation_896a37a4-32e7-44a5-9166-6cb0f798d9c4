/**
 * Utility functions to help with translations across the application
 */

/**
 * Translates a field in a sub-module page
 * @param translate The translate function from useLanguage
 * @param module The main module name (e.g., 'animals', 'breeding')
 * @param submodule The sub-module name (e.g., 'profiles', 'schedule')
 * @param field The field name to translate (e.g., 'title', 'subtitle')
 * @param fallback The fallback text if translation is not found
 * @returns The translated text
 */
export const translateSubModuleField = (
  translate: (key: string, params?: Record<string, string | number>) => string,
  module: string,
  submodule: string,
  field: string,
  fallback: string
): string => {
  // Try specific translation key first (e.g., animals.profiles.title)
  const specificKey = `${module}.${submodule}.${field}`;
  
  // Try generic key next (e.g., animals.profiles)
  const genericKey = field === 'title' ? `${module}.${submodule}` : specificKey;
  
  return translate(specificKey, { fallback: translate(genericKey, { fallback }) });
};

/**
 * Translates a common field across the application
 * @param translate The translate function from useLanguage
 * @param field The field name to translate (e.g., 'tag', 'weight')
 * @param module Optional module name for context-specific translations
 * @param fallback The fallback text if translation is not found
 * @returns The translated text
 */
export const translateField = (
  translate: (key: string, params?: Record<string, string | number>) => string,
  field: string,
  module?: string,
  fallback?: string
): string => {
  // If module is provided, try module-specific translation first
  if (module) {
    const moduleKey = `${module}.${field}`;
    return translate(moduleKey, { fallback: translate(field, { fallback: fallback || field }) });
  }
  
  // Otherwise just use the field name as the translation key
  return translate(field, { fallback: fallback || field });
};
