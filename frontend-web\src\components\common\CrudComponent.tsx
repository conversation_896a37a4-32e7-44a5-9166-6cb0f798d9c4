/**
 * Standardized CRUD Component
 * 
 * This component provides a consistent interface for CRUD operations
 * across all modules in the application.
 */

import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, CircularProgress, Alert, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Snackbar, useTheme, alpha } from '@mui/material';
import { Add, Close, Delete, Edit, Refresh } from '../../utils/iconImports';
import { useTranslation } from '../../hooks/useTranslation';
import { useSnackbar } from '../../contexts/SnackbarContext';

interface CrudComponentProps<T> {
  title: string;
  subtitle?: string;
  endpoint: string;
  fetchItems: () => Promise<T[]>;
  createItem?: (data: Omit<T, 'id'>) => Promise<T>;
  updateItem?: (id: string, data: Partial<T>) => Promise<T>;
  deleteItem?: (id: string) => Promise<void>;
  renderList: (items: T[], onEdit: (item: T) => void, onDelete: (id: string) => void) => React.ReactNode;
  renderForm: (
    item: Partial<T> | null,
    onChange: (field: keyof T, value: any) => void,
    formData: Partial<T>,
    errors: Record<string, string>
  ) => React.ReactNode;
  validateForm?: (data: Partial<T>) => Record<string, string>;
  initialFormData: Partial<T>;
  itemName: string;
  disableAdd?: boolean;
  disableEdit?: boolean;
  disableDelete?: boolean;
  onRefreshData?: () => void;
}

function CrudComponent<T extends { id: string }>({
  title,
  subtitle,
  endpoint,
  fetchItems,
  createItem,
  updateItem,
  deleteItem,
  renderList,
  renderForm,
  validateForm,
  initialFormData,
  itemName,
  disableAdd = false,
  disableEdit = false,
  disableDelete = false,
  onRefreshData
}: CrudComponentProps<T>) {
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formOpen, setFormOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<T>>(initialFormData);
  const [editingItem, setEditingItem] = useState<T | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<string | null>(null);
  const { translate } = useTranslation();
  const { showSnackbar } = useSnackbar();
  const theme = useTheme();

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Load data from API
  const loadData = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await fetchItems();
      setItems(data);
    } catch (err) {
      console.error(`Error loading ${itemName} data:`, err);
      setError(`Failed to load ${itemName} data. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle form field changes
  const handleChange = (field: keyof T, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field if it exists
    if (formErrors[field as string]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  };

  // Open form for creating new item
  const handleAddNew = () => {
    setFormData(initialFormData);
    setEditingItem(null);
    setFormErrors({});
    setFormOpen(true);
  };

  // Open form for editing existing item
  const handleEdit = (item: T) => {
    setFormData(item);
    setEditingItem(item);
    setFormErrors({});
    setFormOpen(true);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (id: string) => {
    setItemToDelete(id);
    setDeleteConfirmOpen(true);
  };

  // Validate form and submit
  const handleSubmit = async () => {
    // Validate form if validation function is provided
    if (validateForm) {
      const errors = validateForm(formData);
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }
    }

    setLoading(true);
    try {
      if (editingItem && updateItem) {
        // Update existing item
        const updated = await updateItem(editingItem.id, formData);
        setItems(prev => prev.map(item => item.id === editingItem.id ? updated : item));
        showSnackbar(`${itemName} updated successfully`, 'success');
      } else if (createItem) {
        // Create new item
        const created = await createItem(formData as Omit<T, 'id'>);
        setItems(prev => [...prev, created]);
        showSnackbar(`${itemName} created successfully`, 'success');
      }
      setFormOpen(false);
    } catch (err) {
      console.error(`Error saving ${itemName}:`, err);
      showSnackbar(`Failed to save ${itemName}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Confirm and execute delete
  const handleConfirmDelete = async () => {
    if (!itemToDelete || !deleteItem) return;
    
    setLoading(true);
    try {
      await deleteItem(itemToDelete);
      setItems(prev => prev.filter(item => item.id !== itemToDelete));
      showSnackbar(`${itemName} deleted successfully`, 'success');
    } catch (err) {
      console.error(`Error deleting ${itemName}:`, err);
      showSnackbar(`Failed to delete ${itemName}`, 'error');
    } finally {
      setLoading(false);
      setDeleteConfirmOpen(false);
      setItemToDelete(null);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    if (onRefreshData) {
      onRefreshData();
    } else {
      loadData();
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          mb: 3,
          px: 3
        }}
      >
        <Box>
          <Typography variant="h5" component="h1" gutterBottom>
            {title}
          </Typography>
          {subtitle && (
            <Typography variant="subtitle1" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <CustomButton 
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={loading}
          >
            {translate ? translate('common.refresh', { fallback: 'Refresh' }) : 'Refresh'}
          </CustomButton>
          {!disableAdd && (
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddNew}
              disabled={loading}
            >
              {translate ? translate('common.add', { fallback: 'Add' }) : 'Add'} {itemName}
            </CustomButton>
          )}
        </Box>
      </Box>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* List of items */}
      {!loading && items.length === 0 ? (
        <Paper 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            bgcolor: alpha(theme.palette.background.paper, 0.8),
            borderRadius: 2
          }}
        >
          <Typography variant="h6">
            {translate ? translate('common.no_items', { fallback: 'No items found' }) : 'No items found'}
          </Typography>
          {!disableAdd && (
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddNew}
              sx={{ mt: 2 }}
            >
              {translate ? translate('common.add_first', { fallback: 'Add your first' }) : 'Add your first'} {itemName}
            </CustomButton>
          )}
        </Paper>
      ) : (
        <Box sx={{ mb: 4 }}>
          {renderList(items, handleEdit, handleDeleteClick)}
        </Box>
      )}

      {/* Form dialog */}
      <Dialog 
        open={formOpen} 
        onClose={() => setFormOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingItem 
            ? `${translate ? translate('common.edit', { fallback: 'Edit' }) : 'Edit'} ${itemName}`
            : `${translate ? translate('common.add', { fallback: 'Add' }) : 'Add'} ${itemName}`
          }
          <IconButton
            aria-label="close"
            onClick={() => setFormOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {renderForm(editingItem, handleChange, formData, formErrors)}
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setFormOpen(false)} disabled={loading}>
            {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
          </CustomButton>
          <CustomButton 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            disabled={loading}
          >
            {loading 
              ? (translate ? translate('common.saving', { fallback: 'Saving...' }) : 'Saving...') 
              : (translate ? translate('common.save', { fallback: 'Save' }) : 'Save')
            }
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>
          {translate ? translate('common.confirm_delete', { fallback: 'Confirm Delete' }) : 'Confirm Delete'}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {translate 
              ? translate('common.delete_confirmation', { 
                  fallback: `Are you sure you want to delete this ${itemName}? This action cannot be undone.` 
                }, { itemName }) 
              : `Are you sure you want to delete this ${itemName}? This action cannot be undone.`
            }
          </Typography>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setDeleteConfirmOpen(false)} disabled={loading}>
            {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
          </CustomButton>
          <CustomButton 
            onClick={handleConfirmDelete} 
            color="error" 
            variant="contained"
            disabled={loading}
            startIcon={<Delete />}
          >
            {loading 
              ? (translate ? translate('common.deleting', { fallback: 'Deleting...' }) : 'Deleting...') 
              : (translate ? translate('common.delete', { fallback: 'Delete' }) : 'Delete')
            }
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default CrudComponent;
