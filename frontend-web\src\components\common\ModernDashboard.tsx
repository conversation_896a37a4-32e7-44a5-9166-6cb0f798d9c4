import React, { ReactNode } from 'react';
import { Box, Grid, Typography, Paper, useTheme, alpha, Divider, Chip, IconButton, Tooltip } from '@mui/material';
import SafeButton from './SafeButton';
import { motion } from 'framer-motion';
import {
  Refresh,
  MoreVert,
  Download,
  Print,
  Share,
  FilterList,
  Info
} from '../../utils/iconImports';
import { useLanguage } from '../../contexts/LanguageContext';
import { LoadingOverlay, ErrorBoundary } from '.';
import { getSafeElevation, getSafeAnimation } from '../../utils/themeDefaults';

export interface DashboardStat {
  label: string;
  value: string | number;
  icon?: ReactNode;
  color?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
}

export interface DashboardAction {
  label: string;
  icon: ReactNode;
  onClick: () => void;
  color?: string;
  disabled?: boolean;
}

export interface DashboardTab {
  label: string;
  content: ReactNode;
  icon?: ReactNode;
}

export interface ModernDashboardProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  stats?: DashboardStat[];
  actions?: DashboardAction[];
  tabs?: DashboardTab[];
  activeTab?: number;
  onTabChange?: (index: number) => void;
  isLoading?: boolean;
  loadingMessage?: string;
  onRefresh?: () => void;
  children?: ReactNode;
  module?: string;
  extraHeaderContent?: ReactNode;
}

const ModernDashboard: React.FC<ModernDashboardProps> = ({
  title,
  subtitle,
  icon,
  stats,
  actions,
  tabs,
  activeTab = 0,
  onTabChange,
  isLoading = false,
  loadingMessage,
  onRefresh,
  children,
  module,
  extraHeaderContent
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      default:
        return theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  return (
    <ErrorBoundary>
      <LoadingOverlay loading={isLoading} message={loadingMessage || translate('common.loading')}>
        <Box sx={{ p: 3, minHeight: '100%', overflow: 'visible' }}>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 4,
              position: 'relative',
              p: 2,
              borderRadius: '12px',
              backdropFilter: 'blur(10px)',
              backgroundColor: 'transparent',
              background: `linear-gradient(135deg,
                ${alpha(theme.palette.background.paper, 0.4)},
                ${alpha(theme.palette.background.paper, 0.2)})`,
              border: 'none',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -10,
                left: 0,
                width: '100%',
                height: '1px',
                background: 'transparent',
                opacity: 0
              }
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {icon && (
                <Box
                  className="glow-pulse"
                  sx={{
                    mr: 2,
                    color: moduleColor,
                    backgroundColor: alpha(typeof moduleColor === 'string' && (moduleColor.startsWith('#') || moduleColor.startsWith('rgb') || moduleColor.startsWith('hsl')) ? moduleColor : theme.palette.primary.main, 0.1),
                    borderRadius: '50%',
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: `0 0 10px ${alpha(typeof moduleColor === 'string' && (moduleColor.startsWith('#') || moduleColor.startsWith('rgb') || moduleColor.startsWith('hsl')) ? moduleColor : theme.palette.primary.main, 0.3)}`,
                    border: 'none'
                  }}
                >
                  {icon}
                </Box>
              )}
              <Box>
                <Typography
                  variant="h4"
                  fontWeight="bold"
                  gutterBottom
                  sx={{
                    background: `linear-gradient(135deg, ${moduleColor}, ${theme.palette.text.primary})`,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    letterSpacing: '-0.5px'
                  }}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography
                    variant="subtitle1"
                    color="text.secondary"
                    sx={{
                      maxWidth: '800px',
                      lineHeight: 1.5
                    }}
                  >
                    {subtitle}
                  </Typography>
                )}
              </Box>
            </Box>

            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              {/* Extra header content */}
              {extraHeaderContent && (
                <Box sx={{ mr: 1 }}>
                  {extraHeaderContent}
                </Box>
              )}

              {onRefresh && (
                <Tooltip title={translate('common.refresh')}>
                  <IconButton
                    onClick={onRefresh}
                    color="primary"
                    className="color-transition"
                    sx={{
                      backgroundColor: alpha(typeof moduleColor === 'string' && (moduleColor.startsWith('#') || moduleColor.startsWith('rgb') || moduleColor.startsWith('hsl')) ? moduleColor : theme.palette.primary.main, 0.1),
                      '&:hover': {
                        backgroundColor: alpha(typeof moduleColor === 'string' && (moduleColor.startsWith('#') || moduleColor.startsWith('rgb') || moduleColor.startsWith('hsl')) ? moduleColor : theme.palette.primary.main, 0.2),
                        transform: 'rotate(30deg)'
                      },
                      transition: 'transform 0.3s ease'
                    }}
                  >
                    <Refresh />
                  </IconButton>
                </Tooltip>
              )}

              {actions && actions.map((action, index) => (
                <SafeButton
                  key={index}
                  variant="contained"
                  startIcon={action.icon}
                  onClick={action.onClick}
                  disabled={action.disabled}
                  color={action.color as any || "primary"}
                  className="color-transition"
                  sx={{
                    ml: 1,
                    px: 2,
                    py: 1,
                    borderRadius: '8px',
                    boxShadow: `0 4px 14px rgba(0, 0, 0, 0.25)`,
                    '&:hover': {
                      boxShadow: `0 6px 20px rgba(0, 0, 0, 0.35)`,
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  {action.label}
                </SafeButton>
              ))}
            </Box>
          </Box>
        </motion.div>

        {/* Stats */}
        {stats && stats.length > 0 && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {stats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div variants={itemVariants}>
                    <Paper
                      elevation={2}
                      className="color-transition"
                      sx={{
                        p: 3,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        position: 'relative',
                        overflow: 'hidden',
                        borderLeft: 'none',
                        background: `linear-gradient(135deg,
                          ${alpha(stat.color || moduleColor, 0.15)},
                          ${alpha(stat.color || moduleColor, 0.05)})`,
                        backdropFilter: 'blur(10px)',
                        '&:hover': {
                          boxShadow: `0 8px 25px ${alpha(typeof stat.color === 'string' && (stat.color.startsWith('#') || stat.color.startsWith('rgb') || stat.color.startsWith('hsl')) ? stat.color : moduleColor, 0.15)}`,
                          transform: 'translateY(-4px)',
                          background: `linear-gradient(135deg,
                            ${alpha(stat.color || moduleColor, 0.2)},
                            ${alpha(stat.color || moduleColor, 0.1)})`,
                        },
                        transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          right: 0,
                          width: 80,
                          height: 80,
                          borderRadius: '0 0 0 100%',
                          backgroundColor: alpha(typeof stat.color === 'string' && (stat.color.startsWith('#') || stat.color.startsWith('rgb') || stat.color.startsWith('hsl')) ? stat.color : moduleColor, 0.05),
                          zIndex: 0
                        }}
                      />

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, zIndex: 1 }}>
                        <Typography variant="subtitle2" color="text.secondary">
                          {stat.label}
                        </Typography>
                        {stat.icon && (
                          <Box
                            sx={{
                              color: stat.color || moduleColor,
                              backgroundColor: alpha(typeof stat.color === 'string' && (stat.color.startsWith('#') || stat.color.startsWith('rgb') || stat.color.startsWith('hsl')) ? stat.color : moduleColor, 0.1),
                              borderRadius: '50%',
                              p: 0.5,
                              display: 'flex'
                            }}
                          >
                            {stat.icon}
                          </Box>
                        )}
                      </Box>

                      <Typography variant="h4" fontWeight="bold" sx={{ mb: 1, zIndex: 1 }}>
                        {stat.value}
                      </Typography>

                      {stat.trend && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto', zIndex: 1 }}>
                          <Chip
                            size="small"
                            label={`${stat.trend.value}%`}
                            color={stat.trend.isPositive ? "success" : "error"}
                            sx={{
                              height: 24,
                              fontWeight: 'bold',
                              mr: 1
                            }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            {stat.trend.label}
                          </Typography>
                        </Box>
                      )}
                    </Paper>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        )}

        {/* Tabs */}
        {tabs && tabs.length > 0 && (
          <Box sx={{ mb: 4, position: 'relative', zIndex: 5, pointerEvents: 'auto' }}>
            <Paper
              elevation={1}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                mb: 3,
                position: 'relative',
                zIndex: 10,
                pointerEvents: 'auto'
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  borderBottom: 'none',
                  overflowX: 'auto',
                  position: 'relative',
                  zIndex: 20,
                  pointerEvents: 'auto',
                  '&::-webkit-scrollbar': {
                    height: 4
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    borderRadius: 2
                  }
                }}
              >
                {tabs.map((tab, index) => (
                  <Box
                    key={index}
                    onClick={(e) => {
                      console.log('Tab clicked:', index, tab.label);

                      // Force tab change regardless of onTabChange prop
                      if (onTabChange) {
                        console.log('Calling onTabChange with index:', index);
                        onTabChange(index);
                      } else {
                        console.warn('onTabChange is not defined');
                      }

                      // Direct DOM manipulation as a fallback
                      const tabContent = document.getElementById(`tab-content-${index}`);
                      if (tabContent) {
                        console.log('Found tab content element, showing it');
                        // Hide all tab contents
                        document.querySelectorAll('[id^="tab-content-"]').forEach(el => {
                          (el as HTMLElement).style.display = 'none';
                        });
                        // Show the selected tab content
                        tabContent.style.display = 'block';
                      }
                    }}
                    style={{
                      position: 'relative',
                      zIndex: 100,
                      pointerEvents: 'auto'
                    }}
                    sx={{
                      py: 2,
                      px: 3,
                      display: 'flex',
                      alignItems: 'center',
                      cursor: 'pointer',
                      borderBottom: index === activeTab
                        ? `3px solid ${moduleColor}`
                        : '3px solid transparent',
                      color: index === activeTab
                        ? moduleColor
                        : theme.palette.text.secondary,
                      fontWeight: index === activeTab ? 600 : 400,
                      transition: getSafeAnimation(theme, 'short'),
                      '&:hover': {
                        backgroundColor: alpha(typeof moduleColor === 'string' && (moduleColor.startsWith('#') || moduleColor.startsWith('rgb') || moduleColor.startsWith('hsl')) ? moduleColor : theme.palette.primary.main, 0.05)
                      },
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {tab.icon && (
                      <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                        {tab.icon}
                      </Box>
                    )}
                    <Typography variant="subtitle2">
                      {tab.label}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Paper>

            {tabs.map((tab, index) => (
              <motion.div
                key={index}
                id={`tab-content-${index}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{
                  opacity: index === activeTab ? 1 : 0,
                  y: index === activeTab ? 0 : 10,
                  display: index === activeTab ? 'block' : 'none'
                }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
                style={{
                  display: index === activeTab ? 'block' : 'none',
                  position: 'relative',
                  zIndex: 5,
                  pointerEvents: 'auto'
                }}
              >
                {tab.content}
              </motion.div>
            ))}
          </Box>
        )}

        {/* Main Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {children}
        </motion.div>
      </Box>
      </LoadingOverlay>
    </ErrorBoundary>
  );
};

export default ModernDashboard;
