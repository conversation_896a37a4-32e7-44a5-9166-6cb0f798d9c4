import React, { useState } from 'react';
import CustomButton from '../../common/CustomButton';
import { useNavigate } from 'react-router-dom';
import { Grid, Box, Typography, TextField, InputAdornment, useTheme, Chip, CircularProgress } from '@mui/material';
import { motion } from 'framer-motion';
import { Search, FilterList, Add } from '@mui/icons-material';
import AnimalMarketplaceCard from './AnimalMarketplaceCard';
import { Animal } from '../../../types/animal';
import { ROUTES } from '../../../constants/routes';

interface AnimalMarketplaceGridProps {
  animals: Animal[];
  loading?: boolean;
  title?: string;
  showControls?: boolean;
  maxItems?: number;
}

const AnimalMarketplaceGrid: React.FC<AnimalMarketplaceGridProps> = ({
  animals,
  loading = false,
  title = 'Featured Animals',
  showControls = true,
  maxItems = 8
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');

  // Filter animals based on search term and active filter
  const filteredAnimals = animals
    .filter(animal => {
      if (searchTerm === '') return true;

      return (
        animal.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        animal.tagNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        animal.breed?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        animal.species?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    })
    .filter(animal => {
      if (activeFilter === 'all') return true;
      return animal.species?.toLowerCase() === activeFilter.toLowerCase();
    })
    .slice(0, maxItems);

  // Get unique species for filter chips
  const uniqueSpecies = animals.map(animal => animal.species?.toLowerCase() || 'unknown');
  const species = ['all', ...Array.from(new Set(uniqueSpecies))];

  // Handle view animal
  const handleViewAnimal = (animal: Animal) => {
    navigate(`${ROUTES.ANIMALS}/${animal.id}`);
  };

  // Handle edit animal
  const handleEditAnimal = (animal: Animal) => {
    navigate(`${ROUTES.ANIMALS}/edit/${animal.id}`);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="200px">
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header with title and controls */}
      {showControls && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, flexWrap: 'wrap', gap: 2 }}>
          <Typography variant="h5" fontWeight="bold">
            {title}
          </Typography>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <TextField
              placeholder="Search animals..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
              sx={{ width: { xs: '100%', sm: 220 } }}
            />

            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate(`${ROUTES.ANIMALS}/new`)}
            >
              Add Animal
            </CustomButton>
          </Box>
        </Box>
      )}

      {/* Filter chips */}
      {showControls && (
        <Box sx={{ display: 'flex', gap: 1, mb: 3, flexWrap: 'wrap' }}>
          {species.map((item) => (
            <Chip
              key={item}
              label={item === 'all' ? 'All Animals' : item.charAt(0).toUpperCase() + item.slice(1)}
              onClick={() => setActiveFilter(item)}
              color={activeFilter === item ? 'primary' : 'default'}
              variant={activeFilter === item ? 'filled' : 'outlined'}
            />
          ))}
        </Box>
      )}

      {/* Animal Cards Grid */}
      {filteredAnimals.length > 0 ? (
        <Grid container spacing={3}>
          {filteredAnimals.map((animal, index) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={animal.id}>
              <motion.div variants={itemVariants}>
                <AnimalMarketplaceCard
                  animal={animal}
                  onView={handleViewAnimal}
                  onAction={handleEditAnimal}
                  delay={index * 0.05}
                />
              </motion.div>
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="text.secondary">
            No animals found matching your criteria
          </Typography>
          <CustomButton
            variant="outlined"
            color="primary"
            sx={{ mt: 2 }}
            onClick={() => {
              setSearchTerm('');
              setActiveFilter('all');
            }}
          >
            Clear Filters
          </CustomButton>
        </Box>
      )}

      {/* View all button */}
      {showControls && filteredAnimals.length > 0 && animals.length > maxItems && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CustomButton
            variant="outlined"
            onClick={() => navigate(`${ROUTES.ANIMALS}/list`)}
          >
            View All Animals
          </CustomButton>
        </Box>
      )}
    </motion.div>
  );
};

export default AnimalMarketplaceGrid;
