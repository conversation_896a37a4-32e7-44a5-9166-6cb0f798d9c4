import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { BkbAuctionCalendarMonth, BkbAuctionFilter } from '../../types/commercial/bkbAuction';
import bkbAuctionService from '../../services/commercial/BkbAuctionService';

/**
 * BkbAuction Context Type Definition
 */
interface BkbAuctionContextType {
  currentMonth: BkbAuctionCalendarMonth | null;
  loading: boolean;
  error: string | null;
  filter: BkbAuctionFilter;
  setFilter: (filter: BkbAuctionFilter) => void;
  fetchMonth: (year: number, month: number) => Promise<void>;
  nextMonth: () => void;
  prevMonth: () => void;
  currentDate: Date;
  refreshData: () => Promise<void>;
  subscribeToCalendar: () => string;
}

/**
 * Create the context with undefined default value
 */
const BkbAuctionContext = createContext<BkbAuctionContextType | undefined>(undefined);

/**
 * BkbAuction Provider Component
 */
export const BkbAuctionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // State
  const [currentMonth, setCurrentMonth] = useState<BkbAuctionCalendarMonth | null>(null);
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<BkbAuctionFilter>({});

  /**
   * Fetch auction data for a specific month
   */
  const fetchMonth = useCallback(async (year: number, month: number) => {
    setLoading(true);
    setError(null);

    try {
      const result = await bkbAuctionService.fetchMonthlyAuctions(year, month);

      if (result.success && result.data) {
        setCurrentMonth(result.data);

        // If using fallback data, show a warning
        if (result.message) {
          console.warn(`[BkbAuctionContext] ${result.message}`);
        }
      } else {
        throw new Error(result.message || 'Failed to fetch auction data');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(`Failed to fetch BKB auction data: ${errorMessage}`);
      console.error('Error in BkbAuctionContext:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Initialize with current month data
   */
  useEffect(() => {
    const now = new Date();
    fetchMonth(now.getFullYear(), now.getMonth());
  }, [fetchMonth]);

  /**
   * Navigate to next month
   */
  const nextMonth = useCallback(() => {
    const next = new Date(currentDate);
    next.setMonth(next.getMonth() + 1);
    setCurrentDate(next);
    fetchMonth(next.getFullYear(), next.getMonth());
  }, [currentDate, fetchMonth]);

  /**
   * Navigate to previous month
   */
  const prevMonth = useCallback(() => {
    const prev = new Date(currentDate);
    prev.setMonth(prev.getMonth() - 1);
    setCurrentDate(prev);
    fetchMonth(prev.getFullYear(), prev.getMonth());
  }, [currentDate, fetchMonth]);

  /**
   * Refresh current month data
   */
  const refreshData = useCallback(async () => {
    await fetchMonth(currentDate.getFullYear(), currentDate.getMonth());
  }, [currentDate, fetchMonth]);

  /**
   * Get calendar subscription URL
   */
  const subscribeToCalendar = useCallback(() => {
    return bkbAuctionService.getCalendarSubscriptionUrl();
  }, []);

  /**
   * Context value
   */
  const value = {
    currentMonth,
    loading,
    error,
    filter,
    setFilter,
    fetchMonth,
    nextMonth,
    prevMonth,
    currentDate,
    refreshData,
    subscribeToCalendar
  };

  return (
    <BkbAuctionContext.Provider value={value}>
      {children}
    </BkbAuctionContext.Provider>
  );
};

/**
 * Hook to use the BkbAuction context
 * @throws Error if used outside of a BkbAuctionProvider
 */
export const useBkbAuctions = (): BkbAuctionContextType => {
  const context = useContext(BkbAuctionContext);

  if (context === undefined) {
    throw new Error('useBkbAuctions must be used within a BkbAuctionProvider');
  }

  return context;
};
