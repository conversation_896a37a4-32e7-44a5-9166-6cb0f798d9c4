# Manual Start Guide for MayCaiphus Livestock Management System

This guide provides instructions for manually starting the MayCaiphus Livestock Management System in VS Code.

## Prerequisites

- Node.js (v14 or higher)
- MongoDB Atlas account with proper connection string
- VS Code with Terminal

## Step 1: Start the Backend Server

1. Open VS Code
2. Open a new terminal (Terminal > New Terminal)
3. Navigate to the backend directory:
   ```
   cd backend
   ```
4. Install dependencies if needed:
   ```
   npm install
   ```
5. Start the backend server:
   ```
   set PORT=3001
   npm run dev
   ```

## Step 2: Start the Frontend Server

1. Open a new terminal in VS Code (Terminal > New Terminal)
2. Navigate to the frontend-web directory:
   ```
   cd frontend-web
   ```
3. Install dependencies if needed:
   ```
   npm install
   ```
4. Start the frontend server:
   ```
   set PORT=3000
   npm run start:dev
   ```

## Step 3: Access the Application

1. Open your browser
2. Navigate to: http://localhost:3000
3. Login with the following credentials:
   - Username: admin
   - Password: Admin@123

## Troubleshooting

If you encounter any issues:

1. Check that MongoDB is properly configured in the `.env` file in the backend directory
2. Ensure all dependencies are installed
3. Check the terminal for any error messages
4. Restart the servers if needed

## Database Configuration

The MongoDB connection string should be configured in the backend/.env file:

```
MONGODB_URI=mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGODB_DB_NAME=AMPD Live Stock
```

## Stopping the Servers

To stop the servers:

1. Press Ctrl+C in each terminal window
2. Confirm with Y if prompted

## Additional Notes

- The backend server runs on port 3001
- The frontend server runs on port 3000
- Admin user has full access to all modules and pages
