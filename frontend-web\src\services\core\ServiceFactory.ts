import ServiceRegistry from './ServiceRegistry';

/**
 * Service Factory - A factory pattern implementation for creating and accessing services
 * This helps avoid circular dependencies by providing a central factory for services
 */
export class ServiceFactory {
  /**
   * Get a service instance from the registry
   * If the service doesn't exist, it will be created using the factory function
   * 
   * @param serviceKey Unique identifier for the service
   * @param factory Factory function to create the service if it doesn't exist
   * @returns The service instance
   */
  public static getOrCreate<T>(serviceKey: string, factory: () => T): T {
    if (ServiceRegistry.has(serviceKey)) {
      const service = ServiceRegistry.get<T>(serviceKey);
      if (service) {
        return service;
      }
    }
    
    // Create the service
    const service = factory();
    ServiceRegistry.register(serviceKey, service);
    return service;
  }

  /**
   * Get a service instance from the registry
   * @param serviceKey Unique identifier for the service
   * @returns The service instance or null if not found
   */
  public static get<T>(serviceKey: string): T | null {
    return ServiceRegistry.get<T>(serviceKey);
  }

  /**
   * Check if a service exists in the registry
   * @param serviceKey Unique identifier for the service
   * @returns True if the service exists
   */
  public static has(serviceKey: string): boolean {
    return ServiceRegistry.has(serviceKey);
  }
}

export default ServiceFactory;
