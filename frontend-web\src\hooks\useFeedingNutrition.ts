import { useState, useEffect } from 'react';
import { useMockData } from '../utils/config';
import api from '../services/apiService';

export interface NutrientData {
  name: string;
  value: number;
  recommended: number;
  unit: string;
}

export interface FeedComposition {
  name: string;
  percentage: number;
}

export interface NutrientTrend {
  month: string;
  protein: number;
  fiber: number;
  fat: number;
}

export interface NutritionData {
  nutrients: NutrientData[];
  feedComposition: FeedComposition[];
  trends: NutrientTrend[];
}

export const useFeedingNutrition = () => {
  const [nutritionData, setNutritionData] = useState<NutritionData>({
    nutrients: [],
    feedComposition: [],
    trends: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNutritionData = async () => {
    try {
      setLoading(true);

      if (useMockData) {
        // Use mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock nutrient data
        const mockNutrientData = [
          { name: 'Protein', value: 18, recommended: 16, unit: '%' },
          { name: 'Fiber', value: 22, recommended: 25, unit: '%' },
          { name: 'Fat', value: 4, recommended: 5, unit: '%' },
          { name: 'Calcium', value: 0.8, recommended: 0.7, unit: '%' },
          { name: 'Phosphorus', value: 0.5, recommended: 0.4, unit: '%' },
          { name: 'Vitamin A', value: 4200, recommended: 4000, unit: 'IU/kg' },
          { name: 'Vitamin D', value: 1100, recommended: 1200, unit: 'IU/kg' },
          { name: 'Vitamin E', value: 25, recommended: 20, unit: 'IU/kg' },
        ];

        const mockFeedComposition = [
          { name: 'Hay', percentage: 40 },
          { name: 'Silage', percentage: 30 },
          { name: 'Grain', percentage: 20 },
          { name: 'Supplements', percentage: 10 },
        ];

        const mockNutrientTrends = [
          { month: 'Jan', protein: 16, fiber: 24, fat: 4.5 },
          { month: 'Feb', protein: 17, fiber: 23, fat: 4.2 },
          { month: 'Mar', protein: 17.5, fiber: 22, fat: 4.0 },
          { month: 'Apr', protein: 18, fiber: 22, fat: 4.0 },
          { month: 'May', protein: 18.5, fiber: 21, fat: 3.8 },
          { month: 'Jun', protein: 18, fiber: 22, fat: 4.0 },
        ];

        setNutritionData({
          nutrients: mockNutrientData,
          feedComposition: mockFeedComposition,
          trends: mockNutrientTrends
        });
      } else {
        // Use API
        const [nutrientsRes, compositionRes, trendsRes] = await Promise.all([
          api.get('/feeding/nutrition/nutrients'),
          api.get('/feeding/nutrition/composition'),
          api.get('/feeding/nutrition/trends')
        ]);

        setNutritionData({
          nutrients: nutrientsRes.data,
          feedComposition: compositionRes.data,
          trends: trendsRes.data
        });
      }
    } catch (err) {
      setError('Failed to fetch nutrition data');
      console.error('Error fetching nutrition data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNutritionData();
  }, []);

  return { nutritionData, loading, error, fetchNutritionData };
};
