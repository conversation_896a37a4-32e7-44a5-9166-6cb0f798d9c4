import { generateMockData, randomDate, randomNumber, randomChoice } from '../utils/mockDataGenerator';

const categories = ['Feed', 'Medicine', 'Equipment', 'Supplies', 'Tools'];
const units = ['kg', 'liters', 'pieces', 'bottles', 'boxes', 'bales'];
const locations = ['Warehouse A', 'Warehouse B', 'Medical Storage', 'Equipment Room', 'Feed Storage', 'Supply Closet'];
const suppliers = ['Farm Supply Co.', 'VetMed Supplies', 'Livestock Equipment Inc.', 'Rural Distributors', 'Agri-Products Ltd.'];

export interface InventoryItem {
  id: string;
  category: 'Feed' | 'Medicine' | 'Equipment' | 'Supplies';
  name: string;
  quantity: number;
  unit: string;
  minThreshold: number;
  location: string;
  lastRestocked: Date;
  expiryDate?: Date;
  supplier: string;
  cost: number;
}

export const mockInventory = generateMockData(100, (i) => {
  const category = randomChoice(categories);
  const unit = randomChoice(units);
  const quantity = randomNumber(10, 5000);
  const minThreshold = Math.floor(quantity * 0.2);
  const cost = randomNumber(0.5, 100, 2);
  const lastRestocked = randomDate(new Date(2023, 0, 1), new Date());
  
  let name;
  switch(category) {
    case 'Feed': 
      name = randomChoice(['Alfalfa Hay', 'Grain Mix', 'Protein Supplement', 'Mineral Block', 'Silage', 'Corn Feed']);
      break;
    case 'Medicine': 
      name = randomChoice(['Antibiotics', 'Vaccines', 'Dewormers', 'Anti-inflammatory', 'Vitamins', 'Disinfectant']);
      break;
    case 'Equipment': 
      name = randomChoice(['Milking Machine Parts', 'Fencing Materials', 'Water Troughs', 'Feed Bins', 'Ear Tags']);
      break;
    case 'Supplies': 
      name = randomChoice(['Gloves', 'Syringes', 'Bandages', 'Cleaning Supplies', 'Bedding Material']);
      break;
    case 'Tools': 
      name = randomChoice(['Hoof Trimmers', 'Shears', 'Brushes', 'Shovels', 'Rakes', 'Buckets']);
      break;
    default: name = `Item ${i+1}`;
  }
  
  return {
    id: `INV${(i + 1).toString().padStart(3, '0')}`,
    category,
    name,
    quantity,
    unit,
    minThreshold,
    location: randomChoice(locations),
    lastRestocked,
    expiryDate: category === 'Medicine' ? randomDate(new Date(), new Date(2026, 11, 31)) : undefined,
    supplier: randomChoice(suppliers),
    cost
  };
});

export const mockInventoryStats = {
  totalItems: mockInventory.length,
  lowStock: mockInventory.filter(item => item.quantity <= item.minThreshold).length,
  outOfStock: mockInventory.filter(item => item.quantity === 0).length,
  expiringThisMonth: mockInventory.filter(item => 
    item.expiryDate && 
    new Date(item.expiryDate).getMonth() === new Date().getMonth() && 
    new Date(item.expiryDate).getFullYear() === new Date().getFullYear()
  ).length,
  totalValue: mockInventory.reduce((sum, item) => sum + (item.quantity * item.cost), 0)
};
