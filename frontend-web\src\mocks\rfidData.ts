export interface LocationHistoryPoint {
  timestamp: string;
  signalStrength: number;
  temperature: number;
}

export interface RFIDTag {
  id: string;
  animalId: string;
  batteryLevel: number;
  lastRead: string;
  temperature: number;
  humidity: number;
  status: 'active' | 'inactive';
}

export const mockRFIDTags: RFIDTag[] = [
  {
    id: 'RFID001',
    animalId: 'NGN001',
    batteryLevel: 85,
    lastRead: new Date().toISOString(),
    temperature: 23.5,
    humidity: 65,
    status: 'active'
  },
  {
    id: 'RFID002',
    animalId: 'NGN002',
    batteryLevel: 72,
    lastRead: new Date().toISOString(),
    temperature: 24.1,
    humidity: 63,
    status: 'active'
  },
  {
    id: 'RFID003',
    animalId: 'NGN003',
    batteryLevel: 15,
    lastRead: new Date().toISOString(),
    temperature: 22.8,
    humidity: 67,
    status: 'active'
  },
  {
    id: 'RFID004',
    animalId: 'BNS001',
    batteryLevel: 92,
    lastRead: new Date().toISOString(),
    temperature: 25.2,
    humidity: 58,
    status: 'active'
  },
  {
    id: 'RFID005',
    animalId: 'BNS002',
    batteryLevel: 45,
    lastRead: new Date().toISOString(),
    temperature: 26.1,
    humidity: 60,
    status: 'active'
  },
  {
    id: 'RFID006',
    animalId: 'DRK001',
    batteryLevel: 88,
    lastRead: new Date().toISOString(),
    temperature: 24.5,
    humidity: 62,
    status: 'active'
  },
  {
    id: 'RFID007',
    animalId: 'DRK002',
    batteryLevel: 18,
    lastRead: new Date().toISOString(),
    temperature: 23.9,
    humidity: 64,
    status: 'active'
  },
  {
    id: 'RFID008',
    animalId: 'AFR001',
    batteryLevel: 76,
    lastRead: new Date().toISOString(),
    temperature: 22.5,
    humidity: 68,
    status: 'active'
  },
  {
    id: 'RFID009',
    animalId: 'AFR002',
    batteryLevel: 65,
    lastRead: new Date().toISOString(),
    temperature: 23.2,
    humidity: 66,
    status: 'active'
  },
  {
    id: 'RFID010',
    animalId: 'BRH001',
    batteryLevel: 12,
    lastRead: new Date().toISOString(),
    temperature: 25.8,
    humidity: 59,
    status: 'active'
  }
];

export const generateMockLocationHistory = (tagId: string, hours: number = 24): LocationHistoryPoint[] => {
  const history = [];
  const now = new Date();

  for (let i = hours; i >= 0; i--) {
    const timestamp = new Date(now.getTime() - (i * 3600000));
    history.push({
      timestamp: timestamp.toISOString(),
      signalStrength: Math.floor(Math.random() * (100 - 60) + 60),
      temperature: Math.random() * (25 - 20) + 20
    });
  }

  return history;
};
