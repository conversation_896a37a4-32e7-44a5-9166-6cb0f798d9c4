/**
 * Inject Health Data Script
 * 
 * This script injects realistic health records into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');
const { animals } = require('./inject-animal-data');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Health records to inject
const healthRecords = [
  {
    _id: new ObjectId(),
    animal: animals[0]._id, // Thabo
    recordType: 'examination',
    date: new Date('2024-01-15'),
    performer: new ObjectId(), // Will be updated with actual user ID
    veterinarian: {
      name: 'Dr. Sipho Mkhize',
      contact: '+27 82 123 4567',
      clinic: 'Farmland Veterinary Clinic'
    },
    diagnosis: 'Healthy, routine checkup',
    symptoms: [],
    temperature: {
      value: 38.5,
      unit: 'C'
    },
    weight: {
      value: 450,
      unit: 'kg'
    },
    medications: [],
    procedures: [
      {
        name: 'General health examination',
        description: 'Routine health check',
        cost: 500
      }
    ],
    labResults: [],
    images: [],
    documents: [],
    followUp: {
      required: false
    },
    cost: 500,
    notes: 'Animal is in excellent health',
    status: 'resolved',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[1]._id, // Lesedi
    recordType: 'vaccination',
    date: new Date('2024-02-10'),
    performer: new ObjectId(), // Will be updated with actual user ID
    veterinarian: {
      name: 'Dr. Sipho Mkhize',
      contact: '+27 82 123 4567',
      clinic: 'Farmland Veterinary Clinic'
    },
    diagnosis: '',
    symptoms: [],
    temperature: {
      value: 38.7,
      unit: 'C'
    },
    weight: {
      value: 380,
      unit: 'kg'
    },
    medications: [
      {
        name: 'Bovine Viral Diarrhea Vaccine',
        dosage: '5',
        unit: 'ml',
        route: 'intramuscular',
        frequency: 'once',
        startDate: new Date('2024-02-10'),
        endDate: new Date('2024-02-10'),
        notes: 'Annual vaccination'
      }
    ],
    procedures: [
      {
        name: 'Vaccination',
        description: 'Annual BVD vaccination',
        cost: 350
      }
    ],
    labResults: [],
    images: [],
    documents: [],
    followUp: {
      required: false
    },
    cost: 350,
    notes: 'Annual vaccination completed',
    status: 'resolved',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[2]._id, // Kgosi
    recordType: 'treatment',
    date: new Date('2024-03-05'),
    performer: new ObjectId(), // Will be updated with actual user ID
    veterinarian: {
      name: 'Dr. Thandi Nkosi',
      contact: '+27 83 987 6543',
      clinic: 'Rural Veterinary Services'
    },
    diagnosis: 'Minor skin infection',
    symptoms: ['Skin irritation', 'Mild scratching'],
    temperature: {
      value: 39.1,
      unit: 'C'
    },
    weight: {
      value: 65,
      unit: 'kg'
    },
    medications: [
      {
        name: 'Oxytetracycline',
        dosage: '10',
        unit: 'ml',
        route: 'intramuscular',
        frequency: 'once daily',
        startDate: new Date('2024-03-05'),
        endDate: new Date('2024-03-12'),
        notes: 'Administer for 7 days'
      }
    ],
    procedures: [
      {
        name: 'Skin examination',
        description: 'Examination of affected area',
        cost: 200
      }
    ],
    labResults: [],
    images: [],
    documents: [],
    followUp: {
      required: true,
      date: new Date('2024-03-12'),
      notes: 'Check if infection has cleared'
    },
    cost: 450,
    notes: 'Mild skin infection, should respond well to antibiotics',
    status: 'resolved',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[3]._id, // Naledi
    recordType: 'vaccination',
    date: new Date('2024-01-25'),
    performer: new ObjectId(), // Will be updated with actual user ID
    veterinarian: {
      name: 'Dr. Thandi Nkosi',
      contact: '+27 83 987 6543',
      clinic: 'Rural Veterinary Services'
    },
    diagnosis: '',
    symptoms: [],
    temperature: {
      value: 39.0,
      unit: 'C'
    },
    weight: {
      value: 45,
      unit: 'kg'
    },
    medications: [
      {
        name: 'Clostridial Vaccine',
        dosage: '2',
        unit: 'ml',
        route: 'subcutaneous',
        frequency: 'once',
        startDate: new Date('2024-01-25'),
        endDate: new Date('2024-01-25'),
        notes: 'Annual vaccination'
      }
    ],
    procedures: [
      {
        name: 'Vaccination',
        description: 'Annual clostridial vaccination',
        cost: 250
      }
    ],
    labResults: [],
    images: [],
    documents: [],
    followUp: {
      required: false
    },
    cost: 250,
    notes: 'Annual vaccination completed',
    status: 'resolved',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[4]._id, // Tumelo
    recordType: 'examination',
    date: new Date('2024-02-20'),
    performer: new ObjectId(), // Will be updated with actual user ID
    veterinarian: {
      name: 'Dr. Sipho Mkhize',
      contact: '+27 82 123 4567',
      clinic: 'Farmland Veterinary Clinic'
    },
    diagnosis: 'Healthy, routine checkup',
    symptoms: [],
    temperature: {
      value: 39.2,
      unit: 'C'
    },
    weight: {
      value: 70,
      unit: 'kg'
    },
    medications: [],
    procedures: [
      {
        name: 'General health examination',
        description: 'Routine health check',
        cost: 400
      }
    ],
    labResults: [],
    images: [],
    documents: [],
    followUp: {
      required: false
    },
    cost: 400,
    notes: 'Animal is in excellent health',
    status: 'resolved',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject health data
async function injectHealthData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const healthRecordsCollection = db.collection('healthrecords');
    const usersCollection = db.collection('users');

    // Get a user ID for the performer field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const performerId = adminUser ? adminUser._id : new ObjectId();

    // Check if health records already exist
    const existingRecords = await healthRecordsCollection.find({}).toArray();
    logger.info(`Found ${existingRecords.length} existing health records`);

    // Insert or update health records
    for (const record of healthRecords) {
      // Set the performer to an actual user
      record.performer = performerId;
      
      const existingRecord = await healthRecordsCollection.findOne({ 
        animal: record.animal,
        date: record.date,
        recordType: record.recordType
      });

      if (existingRecord) {
        logger.info(`Updating health record for animal: ${record.animal}`);
        await healthRecordsCollection.updateOne(
          { _id: existingRecord._id },
          { $set: { ...record, _id: existingRecord._id } }
        );
      } else {
        logger.info(`Inserting new health record for animal: ${record.animal}`);
        await healthRecordsCollection.insertOne(record);
      }
    }

    logger.info('Health data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting health data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectHealthData()
  .then(() => {
    logger.info('Health data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in health data injection script:', error);
  });

module.exports = { healthRecords };
