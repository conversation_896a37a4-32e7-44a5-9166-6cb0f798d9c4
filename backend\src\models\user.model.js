const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const logger = require('../utils/logger');

/**
 * User Schema
 * Defines the structure for users in MongoDB
 */
const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 50
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 50
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'staff', 'veterinarian', 'viewer'],
    default: 'viewer'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  lastLogin: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
userSchema.pre('save', async function(next) {
  this.updatedAt = Date.now();
  
  // Only hash the password if it's modified or new
  if (!this.isModified('password')) return next();
  
  try {
    // Generate salt
    const salt = await bcrypt.genSalt(10);
    // Hash password
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
userSchema.methods.comparePassword = async function(candidatePassword) {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    throw error;
  }
};

// Static method to find user by username
userSchema.statics.findByUsername = async function(username) {
  try {
    return await this.findOne({ username });
  } catch (error) {
    logger.error('Error finding user by username:', error);
    throw error;
  }
};

// Static method to find user by email
userSchema.statics.findByEmail = async function(email) {
  try {
    return await this.findOne({ email });
  } catch (error) {
    logger.error('Error finding user by email:', error);
    throw error;
  }
};

// Static method to authenticate user
userSchema.statics.authenticate = async function(username, password) {
  try {
    // Find user by username
    const user = await this.findOne({ username });
    
    if (!user) {
      return null;
    }
    
    // Check if user is active
    if (user.status !== 'active') {
      return { error: 'Account is not active' };
    }
    
    // Compare passwords
    const isMatch = await user.comparePassword(password);
    
    if (!isMatch) {
      return null;
    }
    
    // Update last login
    user.lastLogin = Date.now();
    await user.save();
    
    // Return user without password
    const userObject = user.toObject();
    delete userObject.password;
    
    return userObject;
  } catch (error) {
    logger.error('Error authenticating user:', error);
    throw error;
  }
};

// Create the model
const User = mongoose.model('User', userSchema);

module.exports = User;
