import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, IconButton, Typography, FormControl, InputLabel, Select, CircularProgress, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete  } from '../../utils/iconImports';
import { DatePicker } from '@mui/x-date-pickers';
import { useHealthRecords } from '../../hooks/useHealthRecords';
import { useSnackbar } from '../../contexts/SnackbarContext';
import { HealthRecord } from '../../types/health';
import dayjs from 'dayjs';

interface HealthRecordModalProps {
  open: boolean;
  onClose: () => void;
  record?: HealthRecord | null;
  onSave?: () => void;
}

const HealthRecordModal: React.FC<HealthRecordModalProps> = ({
  open,
  onClose,
  record,
  onSave,
}) => {
  const { addHealthRecord, updateHealthRecord, deleteHealthRecord } = useHealthRecords();
  const { showSnackbar } = useSnackbar();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<HealthRecord>>(
    record || {
      animalId: '',
      date: new Date().toISOString().split('T')[0],
      type: 'checkup',
      description: '',
      performedBy: '',
      cost: 0,
      notes: '',
      status: 'scheduled',
    }
  );

  // Update form data when record changes
  useEffect(() => {
    if (record) {
      setFormData(record);
    } else {
      setFormData({
        animalId: '',
        date: new Date().toISOString().split('T')[0],
        type: 'checkup',
        description: '',
        performedBy: '',
        cost: 0,
        notes: '',
        status: 'scheduled',
      });
    }
  }, [record]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date: date.format('YYYY-MM-DD') }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (record && record.id) {
        // Update existing record
        await updateHealthRecord(record.id, formData);
      } else {
        // Create new record
        await addHealthRecord(formData);
      }

      if (onSave) {
        onSave();
      }

      onClose();
    } catch (error) {
      showSnackbar('Error saving health record', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!record || !record.id) return;

    setLoading(true);
    try {
      await deleteHealthRecord(record.id);
      if (onSave) {
        onSave();
      }
      onClose();
    } catch (error) {
      showSnackbar('Error deleting health record', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle className="flex justify-between items-center">
        <Typography variant="h6">
          {record ? 'Edit Health Record' : 'New Health Record'}
        </Typography>
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Animal Tag Number"
                fullWidth
                required
                name="animalId"
                value={formData.animalId || ''}
                onChange={handleChange}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel id="type-label">Record Type</InputLabel>
                <Select
                  labelId="type-label"
                  name="type"
                  value={formData.type || ''}
                  onChange={handleChange}
                  label="Record Type"
                >

                <MenuItem value="vaccination">Vaccination</MenuItem>
                <MenuItem value="treatment">Treatment</MenuItem>
                <MenuItem value="checkup">Checkup</MenuItem>
                <MenuItem value="surgery">Surgery</MenuItem>
                <MenuItem value="quarantine">Quarantine</MenuItem>
              </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Date"
                value={formData.date ? dayjs(formData.date) : null}
                onChange={handleDateChange}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel id="status-label">Status</InputLabel>
                <Select
                  labelId="status-label"
                  name="status"
                  value={formData.status || 'scheduled'}
                  onChange={handleChange}
                  label="Status"
                >
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Cost"
                name="cost"
                type="number"
                fullWidth
                value={formData.cost || 0}
                onChange={handleChange}
                InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                margin="normal"
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                fullWidth
                multiline
                rows={2}
                value={formData.description || ''}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Notes"
                name="notes"
                fullWidth
                multiline
                rows={4}
                value={formData.notes || ''}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Performed By"
                name="performedBy"
                fullWidth
                required
                value={formData.performedBy || ''}
                onChange={handleChange}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Notes"
                name="notes"
                fullWidth
                multiline
                rows={4}
                value={formData.notes || ''}
                onChange={handleChange}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions className="p-4">
          <CustomButton onClick={onClose}>Cancel</CustomButton>
          <CustomButton
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<Save />}
          >
            Save Record
          </CustomButton>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default HealthRecordModal;
