# Production Environment Configuration
NODE_ENV=production

# Server Configuration
PORT=3001
HOST=0.0.0.0

# MongoDB Configuration - REPLACE WITH YOUR PRODUCTION VALUES
MONGODB_URI=***********************************************************************************************
MONGODB_DB_NAME=ampd_livestock_production
MONGODB_SSL=true
MONGODB_CONNECTION_TIMEOUT=30000
MONGODB_SOCKET_TIMEOUT=45000
MONGODB_MAX_POOL_SIZE=50
MONGODB_MIN_POOL_SIZE=5
MONGODB_RETRY_WRITES=true
MONGODB_RETRY_READS=true

# JWT Configuration - GENERATE STRONG SECRETS FOR PRODUCTION
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_64_characters_for_production_use
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_super_secure_refresh_token_secret_key_minimum_64_characters
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=your_super_secure_session_secret_key_minimum_64_characters
CORS_ORIGIN=https://your-production-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/production.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Email Configuration (for notifications and password reset)
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# SMS Configuration (for notifications)
SMS_PROVIDER=clickatell
SMS_API_KEY=your_sms_api_key
SMS_FROM_NUMBER=your_sms_number

# External API Configuration
WEATHER_API_KEY=your_weather_api_key
GOVERNMENT_API_KEY=your_government_data_api_key

# Monitoring and Analytics
SENTRY_DSN=your_sentry_dsn_for_error_tracking
ANALYTICS_API_KEY=your_analytics_api_key

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# Cache Configuration
REDIS_URL=redis://your-redis-instance:6379
CACHE_TTL=3600

# SSL Configuration
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/certificate.crt
SSL_KEY_PATH=/path/to/ssl/private.key

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# Performance Configuration
CLUSTER_MODE=true
WORKER_PROCESSES=auto
COMPRESSION_ENABLED=true
GZIP_LEVEL=6

# Database Backup Configuration
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 3 * * *
DB_BACKUP_RETENTION=7

# API Rate Limiting
API_RATE_LIMIT_ENABLED=true
API_RATE_LIMIT_WINDOW=15
API_RATE_LIMIT_MAX=100

# Security Headers
HELMET_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_BACKUP=true
FEATURE_MONITORING=true

# Third-party Integrations
PAYMENT_GATEWAY_ENABLED=false
PAYMENT_GATEWAY_API_KEY=your_payment_gateway_api_key
PAYMENT_GATEWAY_SECRET=your_payment_gateway_secret

# Subscription Management
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Mobile App Configuration
MOBILE_APP_ENABLED=true
PUSH_NOTIFICATION_KEY=your_push_notification_key
FIREBASE_CONFIG=your_firebase_config_json

# Data Retention Policies
DATA_RETENTION_ENABLED=true
LOG_RETENTION_DAYS=90
USER_DATA_RETENTION_YEARS=7
ANALYTICS_DATA_RETENTION_MONTHS=24

# Compliance and Audit
AUDIT_LOGGING_ENABLED=true
GDPR_COMPLIANCE_ENABLED=true
DATA_ENCRYPTION_ENABLED=true
ENCRYPTION_KEY=your_data_encryption_key_minimum_32_characters

# Load Balancer Configuration
LOAD_BALANCER_ENABLED=false
STICKY_SESSIONS=true
SESSION_AFFINITY=ip_hash

# CDN Configuration
CDN_ENABLED=false
CDN_URL=https://your-cdn-domain.com
STATIC_ASSETS_CDN=true

# Microservices Configuration
MICROSERVICES_ENABLED=false
SERVICE_DISCOVERY_URL=http://your-service-discovery:8500
SERVICE_NAME=agriintel-api

# Container Configuration
CONTAINER_MEMORY_LIMIT=512m
CONTAINER_CPU_LIMIT=1
HEALTH_CHECK_PATH=/health
READINESS_CHECK_PATH=/ready

# Development vs Production Flags
DEBUG_MODE=false
VERBOSE_LOGGING=false
PERFORMANCE_MONITORING=true
ERROR_STACK_TRACES=false
