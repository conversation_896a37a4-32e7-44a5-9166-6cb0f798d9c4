/**
 * Breeding Management API Routes
 *
 * This module provides API routes for breeding record management.
 */

const express = require('express');
const router = express.Router();
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');
const breedingController = require('../../controllers/mongodb/breedingController');

/**
 * @route GET /api/breeding
 * @desc Get breeding overview
 * @access Private
 */
router.get('/', breedingController.getBreeding);

/**
 * @route GET /api/breeding/records
 * @desc Get all breeding records
 * @access Private
 */
router.get('/records', authenticate, breedingController.getBreedingRecords);

/**
 * @route GET /api/breeding/records/:id
 * @desc Get breeding record by ID
 * @access Private
 */
router.get('/records/:id', authenticate, breedingController.getBreedingRecordById);

/**
 * @route POST /api/breeding/records
 * @desc Create new breeding record
 * @access Private
 */
router.post('/records', authenticate, breedingController.createBreedingRecord);

/**
 * @route PUT /api/breeding/records/:id
 * @desc Update breeding record
 * @access Private
 */
router.put('/records/:id', authenticate, breedingController.updateBreedingRecord);

/**
 * @route DELETE /api/breeding/records/:id
 * @desc Delete breeding record
 * @access Private
 */
router.delete('/records/:id', authenticate, breedingController.deleteBreedingRecord);

/**
 * @route GET /api/breeding/statistics
 * @desc Get breeding statistics
 * @access Private
 */
router.get('/statistics', authenticate, breedingController.getBreedingStatistics);

/**
 * @route GET /api/breeding/records/:id
 * @desc Get breeding record by ID
 * @access Private
 */
router.get('/records/:id', (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.params.id,
      femaleId: 'A001',
      maleId: 'B001',
      breedingDate: '2024-12-01',
      status: 'confirmed_pregnant'
    }
  });
});

/**
 * @route POST /api/breeding/records
 * @desc Create a new breeding record
 * @access Private
 */
router.post('/records', breedingController.createBreedingRecord);

/**
 * @route PUT /api/breeding/records/:id
 * @desc Update a breeding record
 * @access Private
 */
router.put('/records/:id', breedingController.updateBreedingRecord);

/**
 * @route DELETE /api/breeding/records/:id
 * @desc Delete a breeding record
 * @access Private
 */
router.delete('/records/:id', breedingController.deleteBreedingRecord);

/**
 * @route GET /api/breeding/births
 * @desc Get all birth records
 * @access Private
 */
router.get('/births', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Birth records feature coming soon'
  });
});

/**
 * @route POST /api/breeding/births
 * @desc Create a new birth record
 * @access Private
 */
router.post('/births', (req, res) => {
  res.json({
    success: true,
    message: 'Birth record creation feature coming soon'
  });
});

/**
 * @route GET /api/breeding/heat
 * @desc Get heat cycle records
 * @access Private
 */
router.get('/heat', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Heat cycle tracking feature coming soon'
  });
});

/**
 * @route GET /api/breeding/stats
 * @desc Get breeding statistics
 * @access Private
 */
router.get('/stats', breedingController.getBreedingStatistics);

module.exports = router;
