const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const logger = require('../utils/logger');

const security = {
  // Rate limiting configuration
  rateLimiter: rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later',
    handler: (req, res) => {
      logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
      res.status(429).json({
        error: 'Too many requests',
        retryAfter: Math.ceil(req.rateLimit.resetTime / 1000)
      });
    }
  }),

  // API Key validation
  apiKeyAuth: (req, res, next) => {
    const apiKey = req.headers['x-api-key'];
    if (!apiKey || !isValidApiKey(apiKey)) {
      logger.warn(`Invalid API key attempt from IP: ${req.ip}`);
      return res.status(401).json({ error: 'Invalid API key' });
    }
    next();
  },

  // Request logging
  requestLogger: (req, res, next) => {
    logger.info(`${req.method} ${req.path} - IP: ${req.ip}`);
    next();
  },

  // Error handling
  errorHandler: (err, req, res, next) => {
    logger.error('Unhandled error:', err);
    res.status(500).json({
      error: 'Internal server error',
      requestId: req.id
    });
  }
};

// Configure security middleware
const configureSecurityMiddleware = (app) => {
  app.use(helmet()); // Secure HTTP headers
  app.use(mongoSanitize()); // Prevent NoSQL injection
  app.use(security.requestLogger);
  app.use(security.rateLimiter);
  app.use('/api', security.apiKeyAuth);
  app.use(security.errorHandler);
};

module.exports = {
  security,
  configureSecurityMiddleware
};