import React, { useState } from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, FormControl, InputLabel, Select, MenuItem, TextField, IconButton } from '@mui/material';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line
} from 'recharts';
import { Download, FilterList, LocalHospital, Assessment, Timeline } from '../../utils/iconImports';
import {  <PERSON><PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

// Mock data for health reports
const diseaseData = [
  { name: 'Respiratory', count: 24, percentage: 32 },
  { name: 'Parasitic', count: 18, percentage: 24 },
  { name: 'Digestive', count: 12, percentage: 16 },
  { name: 'Reproductive', count: 9, percentage: 12 },
  { name: '<PERSON>eness', count: 6, percentage: 8 },
  { name: 'Other', count: 6, percentage: 8 }
];

const monthlyHealthData = [
  { month: 'Jan', cases: 5, vaccinations: 45, treatments: 8 },
  { month: 'Feb', cases: 7, vaccinations: 12, treatments: 10 },
  { month: 'Mar', cases: 10, vaccinations: 8, treatments: 12 },
  { month: 'Apr', cases: 8, vaccinations: 10, treatments: 9 },
  { month: 'May', cases: 12, vaccinations: 15, treatments: 14 },
  { month: 'Jun', cases: 15, vaccinations: 50, treatments: 18 },
  { month: 'Jul', cases: 8, vaccinations: 5, treatments: 10 },
  { month: 'Aug', cases: 6, vaccinations: 8, treatments: 7 },
  { month: 'Sep', cases: 9, vaccinations: 40, treatments: 11 },
  { month: 'Oct', cases: 11, vaccinations: 10, treatments: 13 },
  { month: 'Nov', cases: 7, vaccinations: 5, treatments: 9 },
  { month: 'Dec', cases: 5, vaccinations: 48, treatments: 6 }
];

const mortalityData = [
  { month: 'Jan', rate: 0.5 },
  { month: 'Feb', rate: 0.7 },
  { month: 'Mar', rate: 0.4 },
  { month: 'Apr', rate: 0.3 },
  { month: 'May', rate: 0.6 },
  { month: 'Jun', rate: 0.8 },
  { month: 'Jul', rate: 0.5 },
  { month: 'Aug', rate: 0.3 },
  { month: 'Sep', rate: 0.4 },
  { month: 'Oct', rate: 0.5 },
  { month: 'Nov', rate: 0.6 },
  { month: 'Dec', rate: 0.4 }
];

const treatmentSuccessData = [
  { name: 'Successful', value: 85 },
  { name: 'Partial', value: 10 },
  { name: 'Unsuccessful', value: 5 }
];

const COLORS = ['#4caf50', '#ff9800', '#f44336'];

const healthMetrics = [
  { metric: 'Total Health Cases', value: 75, change: '+5%', period: 'vs. last year' },
  { metric: 'Vaccination Rate', value: '95%', change: '+2%', period: 'vs. target' },
  { metric: 'Avg. Recovery Time', value: '7.2 days', change: '-1.5 days', period: 'vs. last year' },
  { metric: 'Mortality Rate', value: '0.5%', change: '-0.2%', period: 'vs. industry avg.' }
];

const Health: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState('year');
  const [animalType, setAnimalType] = useState('all');

  const handleTimeRangeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setTimeRange(event.target.value as string);
  };

  const handleAnimalTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setAnimalType(event.target.value as string);
  };

  return (
    <Box>
      <ModuleHeader
        title="Health Reports"
        subtitle="Comprehensive health analytics and statistics for your livestock"
        module="reports"
        submodule="health"
        actionLabel="Download Report"
        actionIcon={<Download />}
        onAction={() => console.log('Download health report')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Filters */}
        <Card
          sx={{
            p: 2,
            mb: 3,
            borderRadius: '12px',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <FilterList color="primary" />
            </Grid>
            <Grid item xs={12} sm={3} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="time-range-label">Time Range</InputLabel>
                <Select
                  labelId="time-range-label"
                  id="time-range"
                  value={timeRange}
                  label="Time Range"
                  onChange={handleTimeRangeChange as any}
                >
                  <MenuItem value="month">This Month</MenuItem>
                  <MenuItem value="quarter">This Quarter</MenuItem>
                  <MenuItem value="year">This Year</MenuItem>
                  <MenuItem value="custom">Custom Range</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="animal-type-label">Animal Type</InputLabel>
                <Select
                  labelId="animal-type-label"
                  id="animal-type"
                  value={animalType}
                  label="Animal Type"
                  onChange={handleAnimalTypeChange as any}
                >
                  <MenuItem value="all">All Animals</MenuItem>
                  <MenuItem value="cattle">Cattle</MenuItem>
                  <MenuItem value="sheep">Sheep</MenuItem>
                  <MenuItem value="goats">Goats</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {timeRange === 'custom' && (
              <>
                <Grid item xs={12} sm={3} md={2}>
                  <TextField
                    fullWidth
                    id="start-date"
                    label="Start Date"
                    type="date"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    defaultValue="2024-01-01"
                  />
                </Grid>
                <Grid item xs={12} sm={3} md={2}>
                  <TextField
                    fullWidth
                    id="end-date"
                    label="End Date"
                    type="date"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    defaultValue="2024-12-31"
                  />
                </Grid>
              </>
            )}
            <Grid item>
              <CustomButton variant="contained" color="primary" size="small">
                Apply Filters
              </CustomButton>
            </Grid>
          </Grid>
        </Card>

        {/* Key Metrics */}
        <Grid container spacing={3} mb={4}>
          {healthMetrics.map((item, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {item.metric}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {item.value}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color={item.change.startsWith('+') ? 'success.main' : 'error.main'}
                      fontWeight="medium"
                    >
                      {item.change}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                      {item.period}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Monthly Health Incidents"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<Timeline />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2} height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyHealthData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="cases" name="Disease Cases" fill={theme.palette.error.main} />
                    <Bar dataKey="vaccinations" name="Vaccinations" fill={theme.palette.success.main} />
                    <Bar dataKey="treatments" name="Treatments" fill={theme.palette.info.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Disease Distribution"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<Assessment />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2} height={300} display="flex" justifyContent="center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={diseaseData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="percentage"
                    >
                      {diseaseData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={theme.palette.primary.light} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Treatment Outcomes"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<LocalHospital />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2} height={300} display="flex" justifyContent="center">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={treatmentSuccessData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value }) => `${name}: ${value}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {treatmentSuccessData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Mortality Rate Trend"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<Timeline />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.4}
              height="100%"
            >
              <Box mt={2} height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={mortalityData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    <Line type="monotone" dataKey="rate" name="Mortality Rate" stroke={theme.palette.error.main} activeDot={{ r: 8 }} />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Detailed Disease Table */}
        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">
                Detailed Disease Breakdown
              </Typography>
              <IconButton size="small" color="primary">
                <Download fontSize="small" />
              </IconButton>
            </Box>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white' }}>Disease Category</TableCell>
                    <TableCell sx={{ color: 'white' }}>Cases</TableCell>
                    <TableCell sx={{ color: 'white' }}>% of Total</TableCell>
                    <TableCell sx={{ color: 'white' }}>Avg. Treatment Cost</TableCell>
                    <TableCell sx={{ color: 'white' }}>Avg. Recovery Time</TableCell>
                    <TableCell sx={{ color: 'white' }}>Trend</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow hover>
                    <TableCell>Respiratory</TableCell>
                    <TableCell>24</TableCell>
                    <TableCell>32%</TableCell>
                    <TableCell>R850</TableCell>
                    <TableCell>8.5 days</TableCell>
                    <TableCell><Chip label="Increasing" color="error" size="small" /></TableCell>
                  </TableRow>
                  <TableRow hover>
                    <TableCell>Parasitic</TableCell>
                    <TableCell>18</TableCell>
                    <TableCell>24%</TableCell>
                    <TableCell>R450</TableCell>
                    <TableCell>5.2 days</TableCell>
                    <TableCell><Chip label="Stable" color="primary" size="small" /></TableCell>
                  </TableRow>
                  <TableRow hover>
                    <TableCell>Digestive</TableCell>
                    <TableCell>12</TableCell>
                    <TableCell>16%</TableCell>
                    <TableCell>R650</TableCell>
                    <TableCell>6.8 days</TableCell>
                    <TableCell><Chip label="Decreasing" color="success" size="small" /></TableCell>
                  </TableRow>
                  <TableRow hover>
                    <TableCell>Reproductive</TableCell>
                    <TableCell>9</TableCell>
                    <TableCell>12%</TableCell>
                    <TableCell>R1,200</TableCell>
                    <TableCell>12.5 days</TableCell>
                    <TableCell><Chip label="Stable" color="primary" size="small" /></TableCell>
                  </TableRow>
                  <TableRow hover>
                    <TableCell>Lameness</TableCell>
                    <TableCell>6</TableCell>
                    <TableCell>8%</TableCell>
                    <TableCell>R750</TableCell>
                    <TableCell>14.2 days</TableCell>
                    <TableCell><Chip label="Decreasing" color="success" size="small" /></TableCell>
                  </TableRow>
                  <TableRow hover>
                    <TableCell>Other</TableCell>
                    <TableCell>6</TableCell>
                    <TableCell>8%</TableCell>
                    <TableCell>R550</TableCell>
                    <TableCell>7.5 days</TableCell>
                    <TableCell><Chip label="Stable" color="primary" size="small" /></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default Health;
