/**
 * API Routes Index
 *
 * This module exports all API routes.
 */

const express = require('express');
const router = express.Router();
// const { logRequest } = require('../../middleware/authMiddleware');
// const logger = require('../../utils/logger');

// Apply global middleware
// router.use(logRequest);

// Log all API requests
router.use((req, res, next) => {
  // Get original send function
  const originalSend = res.send;

  // Get request start time
  const startTime = Date.now();

  // Override send function to log response
  res.send = function(body) {
    // Calculate request duration
    const duration = Date.now() - startTime;

    // Get response status code
    const statusCode = res.statusCode;

    // Log response
    console.log(`Response: ${req.method} ${req.originalUrl} - Status: ${statusCode}, Duration: ${duration}ms`);

    // Call original send function
    return originalSend.call(this, body);
  };

  // Continue to the next middleware
  next();
});

// Import consolidated route modules (API routes only)
const animalRoutes = require('./animals');
const authRoutes = require('./auth');
const userRoutes = require('./users');
const healthRoutes = require('./health');
const breedingRoutes = require('./breeding');
const financialRoutes = require('./financial');
const inventoryRoutes = require('./inventory');
const businessRoutes = require('./business');
const reportsRoutes = require('./reports');
const resourcesRoutes = require('./resources');
const feedingRoutes = require('./feeding');
const complianceRoutes = require('./compliance');
const subscriptionRoutes = require('./subscription');
const lifecycleRoutes = require('./lifecycle');

// Mount API routes with proper error handling
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/animals', animalRoutes);
router.use('/health', healthRoutes);
router.use('/breeding', breedingRoutes);
router.use('/feeding', feedingRoutes);
router.use('/financial', financialRoutes);
router.use('/inventory', inventoryRoutes);
router.use('/business', businessRoutes);
router.use('/reports', reportsRoutes);
router.use('/resources', resourcesRoutes);
router.use('/compliance', complianceRoutes);
router.use('/subscription', subscriptionRoutes);
router.use('/lifecycle', lifecycleRoutes);

// API status route
router.get('/status', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'API is running',
    version: '1.0.0',
    timestamp: new Date()
  });
});

// Database status route
router.get('/db-status', async (req, res) => {
  try {
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Check if MongoDB is connected
    const connectionStatus = mongodb.getConnectionStatus();

    // Get database stats
    const dbStats = await db.command({ dbStats: 1 });

    // Get collections
    const collections = await db.listCollections().toArray();
    const collectionStats = [];

    // Get document count for each collection
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      collectionStats.push({
        name: collection.name,
        count
      });
    }

    res.status(200).json({
      success: true,
      message: 'Database is connected',
      connectionStatus,
      dbStats: {
        name: dbStats.db,
        collections: dbStats.collections,
        documents: dbStats.objects,
        storageSize: Math.round(dbStats.storageSize / 1024 / 1024 * 100) / 100 + ' MB'
      },
      collections: collectionStats,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Database status error:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection error',
      error: error.message
    });
  }
});

module.exports = router;
