import React, { useState, useEffect } from 'react';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Tabs, Tab, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import {
  Timeline,
  TrendingUp,
  TrendingDown,
  BarChart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Download,
  Share
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { generateReport, ReportParams, getSavedReports } from '../../services/reportService';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

// Mock data for performance metrics
const performanceMetrics = [
  { metric: 'Average Daily Gain', value: '0.85 kg', change: '+0.12 kg', period: 'vs. last year', trend: 'up' },
  { metric: 'Feed Conversion Ratio', value: '4.2:1', change: '-0.3', period: 'vs. last year', trend: 'up' },
  { metric: 'Mortality Rate', value: '1.2%', change: '-0.5%', period: 'vs. industry avg.', trend: 'up' },
  { metric: 'Calving Rate', value: '92%', change: '+3%', period: 'vs. last year', trend: 'up' }
];

// Mock data for monthly performance
const monthlyPerformanceData = [
  { month: 'Jan', adg: 0.78, fcr: 4.5, mortality: 1.5 },
  { month: 'Feb', adg: 0.80, fcr: 4.4, mortality: 1.4 },
  { month: 'Mar', adg: 0.82, fcr: 4.3, mortality: 1.3 },
  { month: 'Apr', adg: 0.85, fcr: 4.2, mortality: 1.2 },
  { month: 'May', adg: 0.87, fcr: 4.1, mortality: 1.1 },
  { month: 'Jun', adg: 0.90, fcr: 4.0, mortality: 1.0 },
  { month: 'Jul', adg: 0.88, fcr: 4.1, mortality: 1.1 },
  { month: 'Aug', adg: 0.86, fcr: 4.2, mortality: 1.2 },
  { month: 'Sep', adg: 0.84, fcr: 4.3, mortality: 1.3 },
  { month: 'Oct', adg: 0.82, fcr: 4.4, mortality: 1.4 },
  { month: 'Nov', adg: 0.80, fcr: 4.5, mortality: 1.5 },
  { month: 'Dec', adg: 0.78, fcr: 4.6, mortality: 1.6 }
];

// Mock data for breed performance comparison
const breedPerformanceData = [
  { breed: 'Angus', adg: 0.95, fcr: 4.0, mortality: 1.0, calving: 94 },
  { breed: 'Hereford', adg: 0.90, fcr: 4.1, mortality: 1.1, calving: 93 },
  { breed: 'Brahman', adg: 0.85, fcr: 4.2, mortality: 1.2, calving: 92 },
  { breed: 'Simmental', adg: 0.92, fcr: 4.0, mortality: 1.0, calving: 93 },
  { breed: 'Bonsmara', adg: 0.88, fcr: 4.1, mortality: 1.1, calving: 92 },
  { breed: 'Nguni', adg: 0.75, fcr: 4.3, mortality: 0.9, calving: 95 }
];

// Mock data for performance by age group
const ageGroupPerformanceData = [
  { name: '0-6 months', value: 25 },
  { name: '6-12 months', value: 35 },
  { name: '12-18 months', value: 20 },
  { name: '18-24 months', value: 15 },
  { name: '24+ months', value: 5 }
];

// Mock data for industry comparison
const industryComparisonData = [
  { metric: 'Average Daily Gain (kg)', farm: 0.85, regional: 0.76, national: 0.72 },
  { metric: 'Feed Conversion Ratio', farm: 4.2, regional: 4.5, national: 4.7 },
  { metric: 'Mortality Rate (%)', farm: 1.2, regional: 1.8, national: 2.1 },
  { metric: 'Calving Rate (%)', farm: 92, regional: 88, national: 85 },
  { metric: 'Weaning Weight (kg)', farm: 245, regional: 230, national: 225 }
];

// Mock data for seasonal performance
const seasonalPerformanceData = [
  { season: 'Summer', adg: 0.92, fcr: 4.0, mortality: 1.0 },
  { season: 'Autumn', adg: 0.88, fcr: 4.1, mortality: 1.1 },
  { season: 'Winter', adg: 0.75, fcr: 4.5, mortality: 1.5 },
  { season: 'Spring', adg: 0.85, fcr: 4.2, mortality: 1.2 }
];

const COLORS = ['#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0'];

const Performance: React.FC = () => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('year');
  const [isGenerating, setIsGenerating] = useState(false);
  const [savedReports, setSavedReports] = useState<any[]>([]);

  useEffect(() => {
    // Load saved reports when component mounts
    const loadSavedReports = async () => {
      try {
        const reports = await getSavedReports('performance');
        setSavedReports(reports);
      } catch (error) {
        console.error('Error loading saved reports:', error);
      }
    };

    loadSavedReports();
  }, []);

  const handleGenerateReport = async () => {
    try {
      setIsGenerating(true);

      const params: ReportParams = {
        type: 'performance',
        format: 'pdf',
        timePeriod: timeRange as any,
      };

      await generateReport(params);

      // Refresh the saved reports list
      const reports = await getSavedReports('performance');
      setSavedReports(reports);
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate report. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleTimeRangeChange = (range: 'month' | 'quarter' | 'year') => {
    setTimeRange(range);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box>
      <ModuleHeader
        title="Livestock Performance Analytics"
        subtitle="Comprehensive tracking and analysis of growth, feed efficiency, and productivity metrics"
        module="reports"
        submodule="performance"
        actionLabel={isGenerating ? "Generating..." : "Export Performance Report"}
        actionIcon={<Download />}
        onAction={handleGenerateReport}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Performance Intelligence Dashboard */}
        <Paper
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(themeColor.primary, 0.05)}, ${alpha(themeColor.secondary, 0.05)})`,
            border: `1px solid ${alpha(themeColor.primary, 0.1)}`
          }}
        >
          <Box display="flex" alignItems="center" mb={2}>
            <Box
              sx={{
                p: 1.5,
                borderRadius: 2,
                bgcolor: alpha(themeColor.primary, 0.1),
                color: themeColor.primary,
                mr: 2
              }}
            >
              <BarChartIcon />
            </Box>
            <Typography variant="h5" fontWeight="bold">
              Performance Intelligence Dashboard
            </Typography>
          </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ color: themeColor.primary }}>
                  Growth Rate Analysis
                </Typography>
                <Typography variant="body2" paragraph>
                  Your average daily gain of <strong>0.85 kg</strong> is 12% above regional average. Current feed mix is showing excellent results for weight gain efficiency.
                </Typography>
                <CustomButton
                  variant="outlined"
                  size="small"
                  sx={{ mt: 1, borderColor: themeColor.primary, color: themeColor.primary }}
                >
                  View Growth Trends
                </CustomButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="success.main">
                  Feed Efficiency
                </Typography>
                <Typography variant="body2" paragraph>
                  Feed conversion ratio of <strong>4.2:1</strong> represents a 7% improvement over last year. Continue current feeding program for optimal results.
                </Typography>
                <CustomButton variant="outlined" size="small" color="success" sx={{ mt: 1 }}>
                  View Feed Analysis
                </CustomButton>
              </Box>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="warning.main">
                  Breed Performance
                </Typography>
                <Typography variant="body2" paragraph>
                  <strong>Angus and Simmental</strong> breeds showing superior growth rates. Consider focusing breeding program on these genetics for improved performance.
                </Typography>
                <CustomButton variant="outlined" size="small" color="warning" sx={{ mt: 1 }}>
                  View Breed Comparison
                </CustomButton>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Time range selector */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <CustomButton
            variant={timeRange === 'month' ? 'contained' : 'outlined'}
            size="small"
            onClick={() => handleTimeRangeChange('month')}
            sx={{ mr: 1 }}
          >
            Month
          </CustomButton>
          <CustomButton
            variant={timeRange === 'quarter' ? 'contained' : 'outlined'}
            size="small"
            onClick={() => handleTimeRangeChange('quarter')}
            sx={{ mr: 1 }}
          >
            Quarter
          </CustomButton>
          <CustomButton
            variant={timeRange === 'year' ? 'contained' : 'outlined'}
            size="small"
            onClick={() => handleTimeRangeChange('year')}
          >
            Year
          </CustomButton>
        </Box>

        {/* Key metrics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            {performanceMetrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={metric.metric}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={metric.metric}
                    subtitle={metric.value}
                    backgroundImage="https://th.bing.com/th/id/OIP.TM1iOYX0L32k_18oig5WJwHaEC?rs=1&pid=ImgDetMain"
                    icon={metric.trend === 'up' ? <TrendingUp /> : <TrendingDown />}
                    accentColor={metric.trend === 'up' ? theme.palette.success.main : theme.palette.error.main}
                    secondaryColor={metric.trend === 'up' ? theme.palette.success.dark : theme.palette.error.dark}
                    delay={index * 0.1}
                    height={160}
                    overlay="gradient"
                    content={
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        {metric.change} {metric.period}
                      </Typography>
                    }
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Tabs for different performance views */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minWidth: 120,
                py: 2
              }
            }}
          >
            <Tab label="Growth Performance" />
            <Tab label="Breed Comparison" />
            <Tab label="Age Groups" />
            <Tab label="Comparative Analysis" />
            <Tab label="Seasonal Trends" />
          </Tabs>
        </Box>

        {/* Growth Performance Tab */}
        {tabValue === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <AnimatedBackgroundCard
                  title="Monthly Growth Performance"
                  backgroundImage="https://th.bing.com/th/id/R.********************************?rik=WINm%2fw4uIdi%2fQQ&pid=ImgRaw&r=0"
                  icon={<Timeline />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  content={
                    <Box sx={{ height: 350, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={monthlyPerformanceData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line type="monotone" dataKey="adg" name="Avg. Daily Gain (kg)" stroke={themeColor.primary} strokeWidth={2} activeDot={{ r: 8 }} />
                          <Line type="monotone" dataKey="fcr" name="Feed Conversion Ratio" stroke={themeColor.secondary} strokeWidth={2} activeDot={{ r: 8 }} />
                          <Line type="monotone" dataKey="mortality" name="Mortality Rate (%)" stroke={theme.palette.error.main} strokeWidth={2} activeDot={{ r: 8 }} />
                        </LineChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <AnimatedBackgroundCard
                  title="Performance Insights"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                  content={
                    <Box sx={{ mt: 2 }}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Positive Trend
                        </Typography>
                        <Typography variant="body2">
                          Average daily gain has improved by 12% compared to last year, indicating better feed efficiency.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="warning.main" fontWeight="bold">
                          Area for Improvement
                        </Typography>
                        <Typography variant="body2">
                          Feed conversion ratio for younger animals shows room for improvement. Consider adjusting feed formulation.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1 }}>
                        <Typography variant="subtitle2" color="info.main" fontWeight="bold">
                          Recommendation
                        </Typography>
                        <Typography variant="body2">
                          Implement weight monitoring every 2 weeks for more accurate growth tracking and earlier intervention.
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </Grid>
            </Grid>
          </motion.div>
        )}

        {/* Breed Comparison Tab */}
        {tabValue === 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px', mb: 4 }}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(themeColor.primary, 0.1) }}>
                  <TableRow>
                    <TableCell><strong>Breed</strong></TableCell>
                    <TableCell><strong>Avg. Daily Gain (kg)</strong></TableCell>
                    <TableCell><strong>Feed Conversion Ratio</strong></TableCell>
                    <TableCell><strong>Mortality Rate (%)</strong></TableCell>
                    <TableCell><strong>Calving Rate (%)</strong></TableCell>
                    <TableCell><strong>Performance Rating</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {breedPerformanceData.map((breed) => (
                    <TableRow key={breed.breed} hover>
                      <TableCell>{breed.breed}</TableCell>
                      <TableCell>{breed.adg}</TableCell>
                      <TableCell>{breed.fcr}</TableCell>
                      <TableCell>{breed.mortality}</TableCell>
                      <TableCell>{breed.calving}</TableCell>
                      <TableCell>
                        <Chip
                          label={breed.adg > 0.9 ? 'Excellent' : breed.adg > 0.8 ? 'Good' : 'Average'}
                          color={breed.adg > 0.9 ? 'success' : breed.adg > 0.8 ? 'primary' : 'default'}
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <AnimatedBackgroundCard
                  title="Breed Performance Comparison"
                  backgroundImage="https://th.bing.com/th/id/OIP.TM1iOYX0L32k_18oig5WJwHaEC?rs=1&pid=ImgDetMain"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  content={
                    <Box sx={{ height: 350, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={breedPerformanceData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="breed" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="adg" name="Avg. Daily Gain (kg)" fill={themeColor.primary} />
                          <Bar dataKey="calving" name="Calving Rate (%)" fill={themeColor.secondary} />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <AnimatedBackgroundCard
                  title="Breed Recommendations"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<TrendingUp />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                  content={
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Top Performing Breeds
                      </Typography>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Angus
                        </Typography>
                        <Typography variant="body2">
                          Best overall performance with highest average daily gain and excellent feed conversion ratio.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Nguni
                        </Typography>
                        <Typography variant="body2">
                          Excellent disease resistance and calving rate, with lower feed requirements.
                        </Typography>
                      </Box>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ mt: 3 }}>
                        Breeding Strategy Recommendation
                      </Typography>
                      <Typography variant="body2">
                        Consider crossbreeding Angus with Nguni to combine high growth rates with disease resistance and adaptability to local conditions.
                      </Typography>
                    </Box>
                  }
                />
              </Grid>
            </Grid>
          </motion.div>
        )}

        {/* Age Groups Tab */}
        {tabValue === 2 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <AnimatedBackgroundCard
                  title="Performance by Age Group"
                  backgroundImage="https://th.bing.com/th/id/R.********************************?rik=WINm%2fw4uIdi%2fQQ&pid=ImgRaw&r=0"
                  icon={<PieChartIcon />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  content={
                    <Box sx={{ height: 350, mt: 2, display: 'flex', justifyContent: 'center' }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={ageGroupPerformanceData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {ageGroupPerformanceData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => [`${value}%`, 'Percentage']} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <AnimatedBackgroundCard
                  title="Age Group Analysis"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                  content={
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Key Observations
                      </Typography>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="info.main" fontWeight="bold">
                          Growth Rate Patterns
                        </Typography>
                        <Typography variant="body2">
                          The 6-12 month age group shows the highest growth rate, making it the most critical period for nutrition management.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="warning.main" fontWeight="bold">
                          Feed Efficiency
                        </Typography>
                        <Typography variant="body2">
                          Feed efficiency decreases after 18 months, suggesting potential for earlier marketing to optimize feed costs.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Recommendation
                        </Typography>
                        <Typography variant="body2">
                          Focus resources on the 6-12 month age group to maximize growth potential and consider marketing animals between 18-24 months for optimal economic returns.
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </Grid>
            </Grid>
          </motion.div>
        )}

        {/* Comparative Analysis Tab */}
        {tabValue === 3 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Paper sx={{ p: 3, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 3 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Performance Benchmarking
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    Compare your farm's performance metrics against regional and national averages to identify strengths and areas for improvement.
                  </Typography>

                  <TableContainer>
                    <Table>
                      <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                        <TableRow>
                          <TableCell><strong>Performance Metric</strong></TableCell>
                          <TableCell align="center"><strong>Your Farm</strong></TableCell>
                          <TableCell align="center"><strong>Regional Average</strong></TableCell>
                          <TableCell align="center"><strong>National Average</strong></TableCell>
                          <TableCell align="center"><strong>Comparison</strong></TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {industryComparisonData.map((row) => {
                          // Determine if farm performance is better or worse than benchmarks
                          // For FCR and mortality, lower is better; for others, higher is better
                          const isBetterMetric = row.metric.includes('Feed Conversion') || row.metric.includes('Mortality');
                          const vsRegional = isBetterMetric ? row.regional - row.farm : row.farm - row.regional;
                          const vsNational = isBetterMetric ? row.national - row.farm : row.farm - row.national;
                          const regionalPercentage = ((Math.abs(vsRegional) / row.regional) * 100).toFixed(1);
                          const nationalPercentage = ((Math.abs(vsNational) / row.national) * 100).toFixed(1);

                          return (
                            <TableRow key={row.metric} hover>
                              <TableCell>{row.metric}</TableCell>
                              <TableCell align="center"><strong>{row.farm}</strong></TableCell>
                              <TableCell align="center">{row.regional}</TableCell>
                              <TableCell align="center">{row.national}</TableCell>
                              <TableCell align="center">
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                  <Chip
                                    label={vsRegional > 0 ? `${regionalPercentage}% better than regional` : `${regionalPercentage}% worse than regional`}
                                    color={vsRegional > 0 ? 'success' : 'error'}
                                    size="small"
                                    sx={{ fontSize: '0.75rem' }}
                                  />
                                  <Chip
                                    label={vsNational > 0 ? `${nationalPercentage}% better than national` : `${nationalPercentage}% worse than national`}
                                    color={vsNational > 0 ? 'success' : 'error'}
                                    size="small"
                                    sx={{ fontSize: '0.75rem' }}
                                  />
                                </Box>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Paper>
              </Grid>

              <Grid item xs={12} md={7}>
                <AnimatedBackgroundCard
                  title="Performance Comparison Chart"
                  backgroundImage="https://th.bing.com/th/id/OIP.TM1iOYX0L32k_18oig5WJwHaEC?rs=1&pid=ImgDetMain"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  content={
                    <Box sx={{ height: 400, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={industryComparisonData}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="metric" angle={-45} textAnchor="end" height={80} />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="farm" name="Your Farm" fill={theme.palette.primary.main} />
                          <Bar dataKey="regional" name="Regional Average" fill={theme.palette.info.main} />
                          <Bar dataKey="national" name="National Average" fill={theme.palette.warning.main} />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </Grid>

              <Grid item xs={12} md={5}>
                <AnimatedBackgroundCard
                  title="Performance Insights"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<TrendingUp />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                  content={
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Key Strengths
                      </Typography>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Superior Growth Rate
                        </Typography>
                        <Typography variant="body2">
                          Your average daily gain is 11.8% higher than the regional average, indicating excellent nutrition management.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, mb: 3 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Exceptional Calving Rate
                        </Typography>
                        <Typography variant="body2">
                          Your calving rate of 92% exceeds both regional and national averages, demonstrating superior breeding management.
                        </Typography>
                      </Box>

                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Areas for Improvement
                      </Typography>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1 }}>
                        <Typography variant="subtitle2" color="warning.main" fontWeight="bold">
                          Seasonal Consistency
                        </Typography>
                        <Typography variant="body2">
                          While overall performance is strong, winter performance drops significantly. Consider implementing improved winter feeding strategies.
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </Grid>
            </Grid>
          </motion.div>
        )}

        {/* Seasonal Trends Tab */}
        {tabValue === 4 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <AnimatedBackgroundCard
                  title="Seasonal Performance Trends"
                  backgroundImage="https://th.bing.com/th/id/R.********************************?rik=WINm%2fw4uIdi%2fQQ&pid=ImgRaw&r=0"
                  icon={<Timeline />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                  delay={0.1}
                  height="100%"
                  content={
                    <Box sx={{ height: 350, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={seasonalPerformanceData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="season" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="adg" name="Avg. Daily Gain (kg)" fill={theme.palette.primary.main} />
                          <Bar dataKey="fcr" name="Feed Conversion Ratio" fill={theme.palette.secondary.main} />
                          <Bar dataKey="mortality" name="Mortality Rate (%)" fill={theme.palette.error.main} />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <AnimatedBackgroundCard
                  title="Seasonal Strategy"
                  backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                  icon={<BarChartIcon />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  delay={0.2}
                  height="100%"
                  overlay="gradient"
                  content={
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        Seasonal Recommendations
                      </Typography>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="info.main" fontWeight="bold">
                          Summer Strategy
                        </Typography>
                        <Typography variant="body2">
                          Maximize summer growth advantage by ensuring adequate shade and water access. Consider increasing protein content in summer feed by 2%.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1, mb: 2 }}>
                        <Typography variant="subtitle2" color="warning.main" fontWeight="bold">
                          Winter Strategy
                        </Typography>
                        <Typography variant="body2">
                          Implement winter supplementation program with increased energy content to offset the 18% drop in winter growth rates.
                        </Typography>
                      </Box>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1 }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold">
                          Marketing Timing
                        </Typography>
                        <Typography variant="body2">
                          Align marketing schedule with seasonal performance peaks. Consider finishing more animals during summer/autumn when feed efficiency is optimal.
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </Grid>

              <Grid item xs={12}>
                <Paper sx={{ p: 3, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    Seasonal Impact Analysis
                  </Typography>
                  <Typography variant="body2" paragraph>
                    Understanding seasonal variations in livestock performance is crucial for optimizing management practices throughout the year. The data shows significant performance differences across seasons, with summer showing the best overall performance and winter showing the most challenges.
                  </Typography>

                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    <Grid item xs={12} md={3}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.success.main, 0.1), borderRadius: 1, height: '100%' }}>
                        <Typography variant="subtitle2" color="success.main" fontWeight="bold" gutterBottom>
                          Summer (Dec-Feb)
                        </Typography>
                        <Typography variant="body2" paragraph>
                          Peak performance period with highest ADG (0.92 kg) and best FCR (4.0).
                        </Typography>
                        <Typography variant="body2">
                          <strong>Key Focus:</strong> Heat stress management and maximizing growth potential.
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={3}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.info.main, 0.1), borderRadius: 1, height: '100%' }}>
                        <Typography variant="subtitle2" color="info.main" fontWeight="bold" gutterBottom>
                          Autumn (Mar-May)
                        </Typography>
                        <Typography variant="body2" paragraph>
                          Strong performance with ADG of 0.88 kg and good feed efficiency.
                        </Typography>
                        <Typography variant="body2">
                          <strong>Key Focus:</strong> Transitioning feeding programs and parasite control.
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={3}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.warning.main, 0.1), borderRadius: 1, height: '100%' }}>
                        <Typography variant="subtitle2" color="warning.main" fontWeight="bold" gutterBottom>
                          Winter (Jun-Aug)
                        </Typography>
                        <Typography variant="body2" paragraph>
                          Challenging period with lowest ADG (0.75 kg) and poorest FCR (4.5).
                        </Typography>
                        <Typography variant="body2">
                          <strong>Key Focus:</strong> Supplementary feeding and shelter management.
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={3}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.primary.main, 0.1), borderRadius: 1, height: '100%' }}>
                        <Typography variant="subtitle2" color="primary.main" fontWeight="bold" gutterBottom>
                          Spring (Sep-Nov)
                        </Typography>
                        <Typography variant="body2" paragraph>
                          Recovery period with improving ADG (0.85 kg) as pasture quality improves.
                        </Typography>
                        <Typography variant="body2">
                          <strong>Key Focus:</strong> Grazing management and breeding preparation.
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>
            </Grid>
          </motion.div>
        )}
      </Box>
    </Box>
  );
};

export default Performance;
