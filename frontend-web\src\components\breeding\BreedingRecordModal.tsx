import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, FormControl, InputLabel, Select, CircularProgress, Box, Typography, Divider, IconButton } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { BreedingRecord, FollowUpDate } from '../../types/breeding';
import { Save, Delete, Close } from '@mui/icons-material';
import { useSnackbar } from '../../contexts/SnackbarContext';
import dayjs from 'dayjs';

// Extended BreedingRecord interface to include the fields used in the form
interface ExtendedBreedingRecord extends BreedingRecord {
  damId?: string;
  damName?: string;
  sireId?: string;
  sireName?: string;
}

interface BreedingRecordModalProps {
  open: boolean;
  onClose: () => void;
  record: BreedingRecord | null;
  onSave?: (record: Partial<ExtendedBreedingRecord>) => void;
  onUpdate?: (id: string, record: Partial<ExtendedBreedingRecord>) => void;
  onDelete?: (id: string) => void;
}

const BreedingRecordModal: React.FC<BreedingRecordModalProps> = ({
  open,
  onClose,
  record,
  onSave,
  onUpdate,
  onDelete
}) => {
  const snackbarContext = useSnackbar();
  const showSnackbar = snackbarContext ? snackbarContext.showSnackbar : () => {};
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<ExtendedBreedingRecord>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const isEditMode = !!record;

  // Initialize form data when record changes
  useEffect(() => {
    if (record) {
      setFormData({
        ...record,
        breedingDate: record.breedingDate,
        expectedDueDate: record.expectedDueDate
      });
    } else {
      // Reset form for new record
      setFormData({
        damId: '',
        damName: '',
        sireId: '',
        sireName: '',
        breedingDate: new Date().toISOString().split('T')[0],
        expectedDueDate: '',
        status: 'scheduled',
        method: 'natural',
        notes: ''
      });
    }
    setErrors({});
  }, [record, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | { target: { name: string; value: unknown } }) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({ ...prev, [name]: value }));
      // Clear error when field is edited
      if (errors[name]) {
        setErrors(prev => ({ ...prev, [name]: '' }));
      }
    }
  };

  // Handle date changes
  const handleDateChange = (date: Date | null, fieldName: string) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        [fieldName]: date.toISOString().split('T')[0]
      }));
      // Clear error when field is edited
      if (errors[fieldName]) {
        setErrors(prev => ({ ...prev, [fieldName]: '' }));
      }
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.damId) newErrors.damId = 'Dam ID is required';
    if (!formData.damName) newErrors.damName = 'Dam name is required';
    if (!formData.sireId) newErrors.sireId = 'Sire ID is required';
    if (!formData.sireName) newErrors.sireName = 'Sire name is required';
    if (!formData.breedingDate) newErrors.breedingDate = 'Breeding date is required';
    if (!formData.method) newErrors.method = 'Breeding method is required';
    if (!formData.status) newErrors.status = 'Status is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setLoading(true);

      if (isEditMode && record && onUpdate) {
        await onUpdate(record.id, formData);
        showSnackbar('Breeding record updated successfully', 'success');
      } else if (onSave) {
        await onSave(formData);
        showSnackbar('Breeding record created successfully', 'success');
      }

      onClose();
    } catch (error) {
      showSnackbar('Error saving breeding record', 'error');
      console.error('Error saving breeding record:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle record deletion
  const handleDelete = async () => {
    if (!isEditMode || !record || !onDelete) return;

    try {
      setLoading(true);
      await onDelete(record.id);
      showSnackbar('Breeding record deleted successfully', 'success');
      onClose();
    } catch (error) {
      showSnackbar('Error deleting breeding record', 'error');
      console.error('Error deleting breeding record:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">
            {isEditMode ? 'Edit Breeding Record' : 'Add New Breeding Record'}
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <Divider />

      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Dam ID"
                name="damId"
                value={formData.damId || ''}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.damId}
                helperText={errors.damId}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Dam Name"
                name="damName"
                value={formData.damName || ''}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.damName}
                helperText={errors.damName}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Sire ID"
                name="sireId"
                value={formData.sireId || ''}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.sireId}
                helperText={errors.sireId}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                label="Sire Name"
                name="sireName"
                value={formData.sireName || ''}
                onChange={handleChange}
                fullWidth
                required
                error={!!errors.sireName}
                helperText={errors.sireName}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Breeding Date"
                value={formData.breedingDate ? dayjs(formData.breedingDate) : null}
                onChange={(date) => handleDateChange(date?.toDate() || null, 'breedingDate')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                    error: !!errors.breedingDate,
                    helperText: errors.breedingDate
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Expected Due Date"
                value={formData.expectedDueDate ? dayjs(formData.expectedDueDate) : null}
                onChange={(date) => handleDateChange(date?.toDate() || null, 'expectedDueDate')}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.expectedDueDate,
                    helperText: errors.expectedDueDate
                  }
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.method}>
                <InputLabel>Breeding Method</InputLabel>
                <Select
                  name="method"
                  value={formData.method || ''}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, method: e.target.value as "natural" | "artificial" }));
                  }}
                  label="Breeding Method"
                >
                  <MenuItem value="natural">Natural</MenuItem>
                  <MenuItem value="artificial insemination">Artificial Insemination</MenuItem>
                  <MenuItem value="embryo transfer">Embryo Transfer</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required error={!!errors.status}>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={formData.status || ''}
                  onChange={(e) => {
                    setFormData(prev => ({ ...prev, status: e.target.value as "scheduled" | "pending" | "confirmed" | "unsuccessful" }));
                  }}
                  label="Status"
                >
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="in-progress">In Progress</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                label="Notes"
                name="notes"
                value={formData.notes || ''}
                onChange={handleChange}
                fullWidth
                multiline
                rows={4}
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, pb: 3 }}>
          {isEditMode && onDelete && (
            <CustomButton
              onClick={handleDelete}
              color="error"
              startIcon={<Delete />}
              disabled={loading}
            >
              Delete
            </CustomButton>
          )}
          <Box flexGrow={1} />
          <CustomButton onClick={onClose} disabled={loading}>
            Cancel
          </CustomButton>
          <CustomButton
            type="submit"
            variant="contained"
            color="primary"
            startIcon={<Save />}
            disabled={loading}
          >
            {loading ? (
              <CircularProgress size={24} />
            ) : (
              isEditMode ? 'Update' : 'Save'
            )}
          </CustomButton>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default BreedingRecordModal;
