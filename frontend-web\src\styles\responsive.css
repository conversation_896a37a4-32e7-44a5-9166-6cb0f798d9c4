/* Enhanced Responsive styles for AgriIntel Livestock Management System */

/* Base styles for all devices */
html {
  font-size: 18px; /* Increased base font size for better readability */
}

body {
  overflow-x: hidden;
  font-family: 'Inter', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

/* Full screen utilization */
.MuiContainer-root {
  max-width: 100% !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
}

/* Enhanced contrast and readability */
.MuiTypography-root {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Card enhancements for better visibility */
.MuiCard-root {
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Better button contrast */
.MuiButton-root {
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  html {
    font-size: 16px; /* Maintain larger font size for mobile readability */
  }

  body {
    font-size: 1.1rem; /* Larger body text for mobile */
  }

  /* Full width utilization on mobile */
  .MuiContainer-root {
    padding-left: 8px !important;
    padding-right: 8px !important;
    margin: 0 !important;
    max-width: 100% !important;
  }

  /* Enhanced mobile card spacing */
  .MuiCard-root {
    margin-bottom: 16px !important;
    padding: 16px !important;
  }

  /* Larger touch targets for mobile */
  .MuiButton-root {
    min-height: 48px !important;
    font-size: 1.1rem !important;
    padding: 12px 24px !important;
  }

  /* Better mobile typography */
  .MuiTypography-h1 {
    font-size: 2rem !important;
  }

  .MuiTypography-h2 {
    font-size: 1.75rem !important;
  }

  .MuiTypography-h3 {
    font-size: 1.5rem !important;
  }

  .MuiTypography-h4 {
    font-size: 1.25rem !important;
  }

  .MuiTypography-h5 {
    font-size: 1.125rem !important;
  }

  .MuiTypography-h6 {
    font-size: 1rem !important;
  }

  .MuiTypography-body1 {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }

  .MuiTypography-body2 {
    font-size: 0.95rem !important;
    line-height: 1.5 !important;
  }

  /* Make tables responsive */
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  /* Adjust grid layouts */
  .grid-cols-2 {
    grid-template-columns: 1fr !important;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr !important;
  }

  .grid-cols-4 {
    grid-template-columns: 1fr !important;
  }
}

/* Medium devices (tablets, 768px and down) */
@media only screen and (max-width: 768px) {
  html {
    font-size: 17px; /* Slightly larger for tablets */
  }

  /* Better tablet spacing */
  .MuiContainer-root {
    padding-left: 12px !important;
    padding-right: 12px !important;
  }

  /* Tablet-optimized cards */
  .MuiCard-root {
    margin-bottom: 20px !important;
    padding: 20px !important;
  }

  /* Tablet typography */
  .MuiTypography-h3 {
    font-size: 1.75rem !important;
  }

  .MuiTypography-h4 {
    font-size: 1.5rem !important;
  }

  .MuiTypography-h6 {
    font-size: 1.125rem !important;
  }

  /* Grid adjustments for tablets */
  .MuiGrid-container {
    margin: 0 !important;
    width: 100% !important;
  }

  .MuiGrid-item {
    padding: 8px !important;
  }
}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {
  html {
    font-size: 18px; /* Larger font for better readability on large screens */
  }

  /* Full screen utilization for desktop */
  .MuiContainer-root {
    max-width: 100% !important;
    padding-left: 24px !important;
    padding-right: 24px !important;
  }

  /* Desktop-optimized cards */
  .MuiCard-root {
    margin-bottom: 24px !important;
    padding: 24px !important;
  }

  /* Desktop typography */
  .MuiTypography-h1 {
    font-size: 3rem !important;
  }

  .MuiTypography-h2 {
    font-size: 2.5rem !important;
  }

  .MuiTypography-h3 {
    font-size: 2rem !important;
  }

  .MuiTypography-h4 {
    font-size: 1.75rem !important;
  }

  .MuiTypography-h5 {
    font-size: 1.5rem !important;
  }

  .MuiTypography-h6 {
    font-size: 1.25rem !important;
  }

  .MuiTypography-body1 {
    font-size: 1.125rem !important;
    line-height: 1.7 !important;
  }

  .MuiTypography-body2 {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }

  /* Better grid spacing for desktop */
  .MuiGrid-item {
    padding: 12px !important;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  html {
    font-size: 20px; /* Even larger font for very large screens */
  }

  /* Ultra-wide screen optimization */
  .MuiContainer-root {
    padding-left: 32px !important;
    padding-right: 32px !important;
  }

  /* Extra large screen cards */
  .MuiCard-root {
    padding: 32px !important;
    margin-bottom: 32px !important;
  }

  /* Ultra-large typography */
  .MuiTypography-h1 {
    font-size: 3.5rem !important;
  }

  .MuiTypography-h2 {
    font-size: 3rem !important;
  }

  .MuiTypography-h3 {
    font-size: 2.5rem !important;
  }

  .MuiTypography-body1 {
    font-size: 1.25rem !important;
    line-height: 1.8 !important;
  }

  /* Better spacing for ultra-wide screens */
  .MuiGrid-item {
    padding: 16px !important;
  }
}

/* Sidebar responsive behavior */
@media only screen and (max-width: 768px) {
  .sidebar-closed {
    transform: translateX(-100%);
  }

  .sidebar-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0 !important;
    width: 100% !important;
  }

  .main-content-with-sidebar {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Responsive typography */
@media only screen and (max-width: 600px) {
  h1 {
    font-size: 1.75rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  h3 {
    font-size: 1.25rem !important;
  }

  h4 {
    font-size: 1.1rem !important;
  }

  h5, h6 {
    font-size: 1rem !important;
  }

  p {
    font-size: 0.9rem !important;
  }
}

/* Responsive form elements */
@media only screen and (max-width: 768px) {
  input, select, textarea {
    font-size: 16px !important; /* Prevents iOS zoom on focus */
  }

  .form-grid {
    grid-template-columns: 1fr !important;
  }

  .form-row {
    flex-direction: column !important;
  }

  .form-row > * {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }
}

/* Responsive buttons */
@media only screen and (max-width: 600px) {
  .btn-group {
    flex-direction: column !important;
    width: 100% !important;
  }

  .btn-group > * {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  button, .btn {
    padding: 0.5rem 1rem !important;
  }
}

/* Responsive charts and data visualizations */
@media only screen and (max-width: 768px) {
  .chart-container {
    height: 300px !important;
  }
}

/* Responsive tables */
@media only screen and (max-width: 768px) {
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
  }

  .responsive-table thead {
    display: none;
  }

  .responsive-table tbody tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.5rem;
  }

  .responsive-table tbody td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
    padding: 0.5rem;
  }

  .responsive-table tbody td:before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 1rem;
  }
}
