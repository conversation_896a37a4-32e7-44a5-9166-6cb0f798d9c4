# AMPD Livestock Notification System

This document provides instructions for setting up and using the notification system for AMPD Livestock, which includes SMS, voice calls, and other notification methods.

## Overview

The notification system supports multiple notification channels:

- **SMS**: Text message alerts via GSM modem
- **Voice Calls**: Automated voice calls via GSM modem
- **WhatsApp**: WhatsApp messages (simulated)
- **Email**: Email notifications (simulated)
- **In-App**: Notifications within the application

## Hardware Requirements

For SMS and voice call functionality, you'll need:

- GSM USB Modem (e.g., Huawei E303 or ZTE MF190)
- SIM Card with airtime
- USB Port on your server/computer

## Software Requirements

- Node.js (for the backend server)
- Python 3.x (for text-to-speech and Gammu integration)
- Gammu (for SMS functionality)
- pyttsx3 (for text-to-speech)
- ffmpeg (for audio conversion)

## Installation

### 1. Install Gammu

#### On Ubuntu/Debian:

```bash
sudo apt update
sudo apt install gammu gammu-smsd
```

#### On Windows:

1. Download Gammu from [https://wammu.eu/download/](https://wammu.eu/download/)
2. Install Gammu
3. Add Gammu to your PATH

### 2. Install Python Dependencies

```bash
pip install gammu pyttsx3
```

### 3. Install ffmpeg

#### On Ubuntu/Debian:

```bash
sudo apt install ffmpeg
```

#### On Windows:

1. Download ffmpeg from [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Extract the files
3. Add the bin directory to your PATH

### 4. Configure Gammu

Create a configuration file at `backend/config/gammurc` with the following content:

```ini
[gammu]
device = /dev/ttyUSB0  # Update this based on your GSM modem
connection = at115200
```

On Windows, the device might be something like `COM3`.

## Testing the Notification System

### 1. Test SMS Functionality

```bash
cd backend/scripts
node test_notification.js 0794484159
```

Replace `0794484159` with the phone number you want to test with.

### 2. Schedule a Test Notification

To schedule a notification for a specific time:

```bash
cd backend/scripts
node schedule_notification.js --notification-time 17:00 --recipient-phone 0794484159
```

This will schedule a birth alert notification to be sent at 17:00 to the specified phone number.

### 3. Test Voice Call Functionality

```bash
cd backend/scripts
python voice_call.py --phone 0794484159 --message "This is a test voice call from AMPD Livestock."
```

## API Endpoints

The notification system exposes the following API endpoints:

- `POST /api/notifications/sms`: Send an SMS
- `POST /api/notifications/voice`: Make a voice call
- `POST /api/notifications/whatsapp`: Send a WhatsApp message
- `POST /api/notifications/email`: Send an email
- `POST /api/notifications/birth-alert`: Send a birth prediction alert using multiple notification methods
- `GET /api/notifications/test`: Test the notification service

## Frontend Integration

The frontend application includes components for configuring and testing the notification system:

1. **NotificationConfig**: Configure notification settings
2. **NotificationHistory**: View notification history
3. **TestNotification**: Test sending notifications

## Troubleshooting

### GSM Modem Not Detected

1. Check if the modem is properly connected
2. Run `dmesg | grep tty` to see if the modem is detected
3. Update the device path in the Gammu configuration file

### SMS Not Sending

1. Check if the SIM card has airtime
2. Check if the SIM card is properly inserted
3. Check if the SIM card is not PIN-locked
4. Run `gammu --identify` to check if Gammu can detect the modem

### Voice Calls Not Working

1. Check if pyttsx3 is properly installed
2. Check if ffmpeg is properly installed
3. Check if the GSM modem supports voice calls

## Advanced Configuration

### Customizing Voice Properties

You can customize the voice properties in the `voice_call.py` script:

```python
engine.setProperty('rate', 150)  # Speed of speech
engine.setProperty('volume', 0.9)  # Volume (0.0 to 1.0)
```

### Scheduling Notifications

You can use cron jobs to schedule regular checks for upcoming births:

```bash
# Check for upcoming births every hour
0 * * * * cd /path/to/ampd-livestock/backend && node scripts/check_upcoming_births.js
```

## Security Considerations

- Ensure that the SIM card is secured with a PIN
- Store sensitive information (phone numbers, etc.) securely
- Implement rate limiting to prevent abuse
- Implement authentication for API endpoints

## Future Enhancements

- Integration with cloud-based SMS and voice call services
- Support for more notification channels
- Advanced scheduling and prioritization
- Delivery receipts and read receipts
- Fallback mechanisms for failed notifications
