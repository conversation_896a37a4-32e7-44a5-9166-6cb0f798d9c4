/**
 * <PERSON><PERSON>t to test login functionality
 *
 * This script tests the login functionality with different users
 * Run with: node src/scripts/test-login.js
 */

require('dotenv').config();
const axios = require('axios');

// API URL
const API_URL = process.env.API_URL || 'http://localhost:3001/api';

// Test users
const testUsers = [
  { username: 'mayrak<PERSON><PERSON>', password: '<PERSON>rok@1951ke*#', role: 'admin' },
  { username: 'peterbotha', password: 'Admin@2023', role: 'admin' },
  { username: 'johnsmith', password: 'Manager@2023', role: 'manager' },
  { username: 'da<PERSON><PERSON><PERSON><PERSON>', password: 'Manager@2024', role: 'manager' },
  { username: 'sa<PERSON><PERSON><PERSON><PERSON>', password: 'Super@2023', role: 'super_user' },
  { username: 'thembaventer', password: 'Super@2024', role: 'super_user' },
  { username: 'lindiwe<PERSON>le<PERSON>', password: 'Super@2025', role: 'super_user' },
  { username: '<PERSON><PERSON>', password: '123', role: 'admin' },
  { username: '<PERSON><PERSON><PERSON><PERSON>', password: '123', role: 'admin' }
];

// Test login functionality
async function testLogin() {
  console.log('Testing login functionality...');
  console.log(`API URL: ${API_URL}`);

  // Test if the server is running
  try {
    console.log('Testing server connection...');
    const response = await axios.get(`${API_URL}/db-status`);
    console.log('Server is running!');
    console.log('Response:', response.data);
  } catch (error) {
    console.log('Server connection failed:');
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log('Response:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
  }

  for (const user of testUsers) {
    try {
      console.log(`\nTesting login for ${user.username} (${user.role})...`);

      // Call the login API
      const response = await axios.post(`${API_URL}/auth/login`, {
        username: user.username,
        password: user.password
      });

      // Check response
      if (response.data && response.data.token && response.data.user) {
        console.log('✅ Login successful!');
        console.log(`Token: ${response.data.token.substring(0, 20)}...`);
        console.log(`User ID: ${response.data.user.id}`);
        console.log(`Name: ${response.data.user.firstName} ${response.data.user.lastName}`);
        console.log(`Role: ${response.data.user.role}`);
        console.log(`Permissions: ${response.data.user.permissions ? response.data.user.permissions.length : 0} permissions`);
      } else {
        console.log('❌ Login failed: Invalid response format');
        console.log('Response:', response.data);
      }
    } catch (error) {
      console.log('❌ Login failed:');
      if (error.response) {
        console.log(`Status: ${error.response.status}`);
        console.log('Response:', error.response.data);
      } else {
        console.log('Error:', error.message);
      }
    }
  }
}

// Run the test
testLogin()
  .then(() => {
    console.log('\nLogin tests completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('\nLogin tests failed:', error);
    process.exit(1);
  });
