# Unified Design Implementation Plan

This document outlines the new minimalist color palette for the ampd-livestock web application and provides a detailed plan for its implementation across the UI.

## 1. New Color Palette

The new color palette is designed to be professional, minimalist, and cohesive, drawing inspiration from modern web design trends.

| Role                      | Color Name      | Hex Code  |
| ------------------------- | --------------- | --------- |
| **Primary**               | Midnight Blue   | `#2c3e50` |
| **Secondary**             | Wet Asphalt     | `#34495e` |
| **Accent**                | Turquoise       | `#1abc9c` |
| **Text (Dark)**           | Midnight Blue   | `#2c3e50` |
| **Text (Light)**          | Clouds          | `#ecf0f1` |
| **Background (Light)**    | Clouds          | `#ecf0f1` |
| **Background (Dark)**     | Midnight Blue   | `#2c3e50` |
| **Success**               | Emerald         | `#2ecc71` |
| **Error**                 | Alizarin        | `#e74c3c` |
| **Warning**               | Sun Flower      | `#f1c40f` |

## 2. Implementation Plan

This section details how the new color palette will be applied to various UI elements to ensure a consistent and professional look and feel.

### 2.1 Global Styles

*   **Body Background:** `Background (Light)` - `#ecf0f1`
*   **Default Text Color:** `Text (Dark)` - `#2c3e50`
*   **Default Link Color:** `Accent` - `#1abc9c`
*   **Link Hover Color:** A slightly darkened version of `Accent`.

### 2.2 Typography

*   **Headings (h1, h2, h3, etc.):** `Primary` - `#2c3e50`
*   **Body Text:** `Text (Dark)` - `#2c3e50`
*   **Subtle Text (e.g., placeholders, hints):** A lighter shade of `Text (Dark)`.

### 2.3 Buttons

*   **Primary Buttons:**
    *   Background: `Accent` - `#1abc9c`
    *   Text: `Text (Light)` - `#ecf0f1`
    *   Hover/Focus: A slightly darkened version of `Accent`.
*   **Secondary Buttons:**
    *   Background: `Secondary` - `#34495e`
    *   Text: `Text (Light)` - `#ecf0f1`
    *   Hover/Focus: A slightly lightened version of `Secondary`.
*   **Disabled Buttons:**
    *   Background: A light gray.
    *   Text: A medium gray.

### 2.4 Forms

*   **Input Fields & Text Areas:**
    *   Background: `White`
    *   Border: A light gray.
    *   Focus Border: `Accent` - `#1abc9c`
*   **Labels:** `Text (Dark)` - `#2c3e50`

### 2.5 Navigation & Headers

*   **Main Header/Navbar:**
    *   Background: `Background (Dark)` - `#2c3e50`
    *   Text/Links: `Text (Light)` - `#ecf0f1`
*   **Side Navigation:**
    *   Background: `Secondary` - `#34495e`
    *   Text/Links: `Text (Light)` - `#ecf0f1`
    *   Active Link: `Accent` - `#1abc9c`

### 2.6 Cards & Containers

*   **Card Background:** `White`
*   **Card Header:**
    *   Background: `Background (Light)` - `#ecf0f1`
    *   Text: `Primary` - `#2c3e50`
*   **Card Border/Shadow:** A subtle shadow to create depth.

### 2.7 Modals & Pop-ups

*   **Header:**
    *   Background: `Primary` - `#2c3e50`
    *   Text: `Text (Light)` - `#ecf0f1`
*   **Body:** `White` background, `Text (Dark)` for content.
*   **Footer:** `Background (Light)` - `#ecf0f1`

### 2.8 Tables

*   **Header Row:**
    *   Background: `Secondary` - `#34495e`
    *   Text: `Text (Light)` - `#ecf0f1`
*   **Row Hover:** A light shade of `Background (Light)`.
*   **Striped Rows:** Alternate between `White` and a very light gray.

### 2.9 Alerts & Notifications

*   **Success:**
    *   Background: `Success` - `#2ecc71`
    *   Text: `Text (Light)` - `#ecf0f1`
*   **Error:**
    *   Background: `Error` - `#e74c3c`
    *   Text: `Text (Light)` - `#ecf0f1`
*   **Warning:**
    *   Background: `Warning` - `#f1c40f`
    *   Text: `Text (Dark)` - `#2c3e50`
*   **Informational:**
    *   Background: `Secondary` - `#34495e`
    *   Text: `Text (Light)` - `#ecf0f1`
