const { spawn } = require('child_process');
const path = require('path');

// Set environment variables
process.env.PORT = 3000;

// Start the React application
const reactApp = spawn('npm', ['run', 'start'], {
  stdio: 'inherit',
  shell: true,
  env: { ...process.env }
});

reactApp.on('error', (error) => {
  console.error('Failed to start React application:', error);
  process.exit(1);
});

reactApp.on('close', (code) => {
  console.log(`React application exited with code ${code}`);
  process.exit(code);
});
