/**
 * Subscription Controller
 * 
 * Handles subscription-related API endpoints
 */

const SubscriptionService = require('../services/subscriptionService');
const logger = require('../utils/logger');

class SubscriptionController {
  
  /**
   * Get current user subscription
   */
  static async getCurrentSubscription(req, res) {
    try {
      const userId = req.user.id;
      const subscription = await SubscriptionService.getUserSubscription(userId);
      
      res.json({
        success: true,
        data: subscription
      });
    } catch (error) {
      logger.error('Error getting current subscription:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription',
        error: error.message
      });
    }
  }

  /**
   * Get available subscription plans
   */
  static async getPlans(req, res) {
    try {
      const plans = await SubscriptionService.getAvailablePlans();
      
      res.json({
        success: true,
        data: plans
      });
    } catch (error) {
      logger.error('Error getting subscription plans:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription plans',
        error: error.message
      });
    }
  }

  /**
   * Check feature access
   */
  static async checkAccess(req, res) {
    try {
      const userId = req.user.id;
      const { feature, currentUsage } = req.body;
      
      const accessResult = await SubscriptionService.canAccessFeature(userId, feature, currentUsage);
      
      // Get subscription limits for UI display
      const subscription = await SubscriptionService.getUserSubscription(userId);
      const limits = {
        maxAnimals: subscription.planId.features.maxAnimals,
        currentAnimals: subscription.usage.currentAnimals,
        maxReports: subscription.planId.features.maxReports,
        currentReports: subscription.usage.monthlyReports,
        maxAlerts: subscription.planId.features.maxAlerts,
        currentAlerts: subscription.usage.monthlyAlerts,
        planName: subscription.planId.displayName,
        isTrialExpiring: subscription.status === 'trial' && 
          new Date(subscription.trialEndDate) - new Date() < 7 * 24 * 60 * 60 * 1000,
        daysLeft: Math.ceil((new Date(subscription.endDate) - new Date()) / (24 * 60 * 60 * 1000))
      };
      
      res.json({
        success: true,
        ...accessResult,
        limits
      });
    } catch (error) {
      logger.error('Error checking feature access:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to check feature access',
        error: error.message
      });
    }
  }

  /**
   * Track feature usage
   */
  static async trackUsage(req, res) {
    try {
      const userId = req.user.id;
      const { feature, action, metadata } = req.body;
      
      await SubscriptionService.trackFeatureUsage(userId, feature, action, metadata);
      
      res.json({
        success: true,
        message: 'Usage tracked successfully'
      });
    } catch (error) {
      logger.error('Error tracking feature usage:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to track usage',
        error: error.message
      });
    }
  }

  /**
   * Update usage statistics
   */
  static async updateUsage(req, res) {
    try {
      const userId = req.user.id;
      const { usageType, increment } = req.body;
      
      const updatedSubscription = await SubscriptionService.updateUsage(userId, usageType, increment);
      
      res.json({
        success: true,
        data: updatedSubscription
      });
    } catch (error) {
      logger.error('Error updating usage:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update usage',
        error: error.message
      });
    }
  }

  /**
   * Upgrade subscription
   */
  static async upgradeSubscription(req, res) {
    try {
      const userId = req.user.id;
      const { planName, billingCycle } = req.body;
      
      const updatedSubscription = await SubscriptionService.upgradeSubscription(userId, planName, billingCycle);
      
      res.json({
        success: true,
        data: updatedSubscription,
        message: 'Subscription upgraded successfully'
      });
    } catch (error) {
      logger.error('Error upgrading subscription:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to upgrade subscription',
        error: error.message
      });
    }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(req, res) {
    try {
      const userId = req.user.id;
      const { reason, feedback } = req.body;
      
      const cancelledSubscription = await SubscriptionService.cancelSubscription(userId, reason, feedback);
      
      res.json({
        success: true,
        data: cancelledSubscription,
        message: 'Subscription cancelled successfully'
      });
    } catch (error) {
      logger.error('Error cancelling subscription:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription',
        error: error.message
      });
    }
  }

  /**
   * Get conversion metrics for current user
   */
  static async getConversionMetrics(req, res) {
    try {
      const userId = req.user.id;
      const metrics = await SubscriptionService.updateConversionMetrics(userId, 'dashboard', 'view');
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      logger.error('Error getting conversion metrics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get conversion metrics',
        error: error.message
      });
    }
  }

  /**
   * Get province-specific data for demo
   */
  static async getProvinceData(req, res) {
    try {
      const { province } = req.params;
      const data = await SubscriptionService.getProvinceData(province);
      
      res.json({
        success: true,
        data
      });
    } catch (error) {
      logger.error('Error getting province data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get province data',
        error: error.message
      });
    }
  }

  /**
   * Get high-value conversion prospects (admin only)
   */
  static async getConversionProspects(req, res) {
    try {
      // Check if user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      const { limit = 50 } = req.query;
      const prospects = await SubscriptionService.getConversionProspects(parseInt(limit));
      
      res.json({
        success: true,
        data: prospects
      });
    } catch (error) {
      logger.error('Error getting conversion prospects:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get conversion prospects',
        error: error.message
      });
    }
  }

  /**
   * Initialize subscription system (admin only)
   */
  static async initializeSystem(req, res) {
    try {
      // Check if user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      await SubscriptionService.initializeDefaultPlans();
      
      res.json({
        success: true,
        message: 'Subscription system initialized successfully'
      });
    } catch (error) {
      logger.error('Error initializing subscription system:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to initialize subscription system',
        error: error.message
      });
    }
  }

  /**
   * Get subscription analytics (admin only)
   */
  static async getAnalytics(req, res) {
    try {
      // Check if user is admin
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }

      const { UserSubscription, BetaConversionMetrics } = require('../models/schemas/subscription.schema');
      
      // Get subscription statistics
      const totalSubscriptions = await UserSubscription.countDocuments();
      const activeSubscriptions = await UserSubscription.countDocuments({ status: 'active' });
      const trialSubscriptions = await UserSubscription.countDocuments({ status: 'trial' });
      const cancelledSubscriptions = await UserSubscription.countDocuments({ status: 'cancelled' });
      
      // Get conversion statistics
      const totalBetaUsers = await BetaConversionMetrics.countDocuments();
      const highValueProspects = await BetaConversionMetrics.countDocuments({ 
        conversionProbability: { $gte: 0.7 } 
      });
      
      // Get plan distribution
      const planDistribution = await UserSubscription.aggregate([
        {
          $lookup: {
            from: 'subscriptionplans',
            localField: 'planId',
            foreignField: '_id',
            as: 'plan'
          }
        },
        {
          $unwind: '$plan'
        },
        {
          $group: {
            _id: '$plan.name',
            count: { $sum: 1 }
          }
        }
      ]);

      res.json({
        success: true,
        data: {
          subscriptions: {
            total: totalSubscriptions,
            active: activeSubscriptions,
            trial: trialSubscriptions,
            cancelled: cancelledSubscriptions
          },
          conversion: {
            totalBetaUsers,
            highValueProspects,
            conversionRate: totalBetaUsers > 0 ? (activeSubscriptions / totalBetaUsers * 100).toFixed(2) : 0
          },
          planDistribution
        }
      });
    } catch (error) {
      logger.error('Error getting subscription analytics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription analytics',
        error: error.message
      });
    }
  }
}

module.exports = SubscriptionController;
