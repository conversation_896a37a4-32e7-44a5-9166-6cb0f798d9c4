import { cardBackgrounds } from './backgroundImages';

/**
 * Utility function to get a random background image from a specific category
 * @param category The category of background images to choose from
 * @returns A random background image URL from the specified category
 */
export const getRandomBackgroundImage = (category: string): string => {
  // Map category names to the correct property names
  const categoryMap: Record<string, keyof typeof cardBackgrounds> = {
    'health': 'healthImages',
    'breeding': 'breedingImages',
    'commercial': 'commercialImages',
    'animals': 'animals',
    'finance': 'finance',
    'resources': 'resources'
  };

  const mappedCategory = categoryMap[category] || category as keyof typeof cardBackgrounds;
  const images = cardBackgrounds[mappedCategory];
  if (!images || images.length === 0) {
    // Fallback to a default image if the category doesn't exist or is empty
    return 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg';
  }

  // Get a random image from the category
  const randomIndex = Math.floor(Math.random() * images.length);
  return images[randomIndex];
};

/**
 * Utility function to get a background image based on a unique identifier
 * This ensures the same component always gets the same background image
 * @param category The category of background images to choose from
 * @param id A unique identifier for the component
 * @returns A consistent background image URL for the specified component
 */
export const getConsistentBackgroundImage = (category: string, id: string): string => {
  // Map category names to the correct property names
  const categoryMap: Record<string, keyof typeof cardBackgrounds> = {
    'health': 'healthImages',
    'breeding': 'breedingImages',
    'commercial': 'commercialImages',
    'animals': 'animals',
    'finance': 'finance',
    'resources': 'resources'
  };

  const mappedCategory = categoryMap[category] || category as keyof typeof cardBackgrounds;
  const images = cardBackgrounds[mappedCategory];
  if (!images || images.length === 0) {
    // Fallback to a default image if the category doesn't exist or is empty
    return 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg';
  }

  // Use the hash of the ID to get a consistent index
  const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const index = hash % images.length;
  return images[index];
};

/**
 * Utility function to get a sequence of background images from a category
 * This ensures adjacent components get different background images
 * @param category The category of background images to choose from
 * @param count The number of images to return
 * @returns An array of background image URLs
 */
export const getSequentialBackgroundImages = (category: string, count: number): string[] => {
  // Map category names to the correct property names
  const categoryMap: Record<string, keyof typeof cardBackgrounds> = {
    'health': 'healthImages',
    'breeding': 'breedingImages',
    'commercial': 'commercialImages',
    'animals': 'animals',
    'finance': 'finance',
    'resources': 'resources'
  };

  const mappedCategory = categoryMap[category] || category as keyof typeof cardBackgrounds;
  const images = cardBackgrounds[mappedCategory];
  if (!images || images.length === 0) {
    // Fallback to a default image if the category doesn't exist or is empty
    return Array(count).fill('https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg');
  }

  // Create a shuffled copy of the images array
  const shuffled = [...images].sort(() => 0.5 - Math.random());

  // If we need more images than available, repeat the shuffled array
  if (count > shuffled.length) {
    const repeats = Math.ceil(count / shuffled.length);
    const extended = Array(repeats).fill(shuffled).flat();
    return extended.slice(0, count);
  }

  return shuffled.slice(0, count);
};
