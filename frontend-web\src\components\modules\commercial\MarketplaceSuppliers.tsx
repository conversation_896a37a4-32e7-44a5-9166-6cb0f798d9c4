import React, { useState } from 'react';
import CustomButton from '../../common/CustomButton';
import { Box, Typography, Card, CardContent, Grid, Chip, Rating, TextField, InputAdornment, useTheme, alpha, Avatar, List, ListItem, ListItemText, Divider, IconButton, Tooltip, Tab, Tabs } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Search,
  FilterList,
  Store,
  LocationOn,
  Phone,
  Email,
  Star,
  VerifiedUser as Verified,
  FilterList as Sort,
  ArrowUpward,
  ArrowDownward
} from '../../../utils/iconImports';
import AnimatedBackgroundCard from '../../common/AnimatedBackgroundCard';
import { getConsistentBackground } from '../../../utils/backgroundThemes';

// Mock suppliers data
const mockSuppliers = [
  {
    id: 1,
    name: 'Premium Livestock Breeders',
    description: 'Specializing in high-quality breeding stock with superior genetics.',
    location: 'Vryburg, North West Province',
    contact: '+27 82 123 4567',
    email: '<EMAIL>',
    rating: 4.9,
    verified: true,
    categories: ['Livestock', 'Breeding Stock'],
    productsCount: 24,
    joinedDate: '2020-05-15',
    featured: true
  },
  {
    id: 2,
    name: 'FarmFresh Supplies',
    description: 'Quality feed and supplements for all your livestock needs.',
    location: 'Bethlehem, Free State',
    contact: '+27 83 456 7890',
    email: '<EMAIL>',
    rating: 4.7,
    verified: true,
    categories: ['Feed', 'Supplements'],
    productsCount: 42,
    joinedDate: '2019-08-22',
    featured: true
  },
  {
    id: 3,
    name: 'AgriTech Innovations',
    description: 'Modern farming equipment and technology solutions.',
    location: 'Stellenbosch, Western Cape',
    contact: '+27 84 789 0123',
    email: '<EMAIL>',
    rating: 4.6,
    verified: true,
    categories: ['Equipment', 'Technology'],
    productsCount: 36,
    joinedDate: '2021-02-10',
    featured: false
  },
  {
    id: 4,
    name: 'VetMed Solutions',
    description: 'Complete range of veterinary medicines and health products.',
    location: 'Midrand, Gauteng',
    contact: '+27 82 345 6789',
    email: '<EMAIL>',
    rating: 4.8,
    verified: true,
    categories: ['Health', 'Medication'],
    productsCount: 58,
    joinedDate: '2018-11-05',
    featured: false
  },
  {
    id: 5,
    name: 'GreenFields Agri',
    description: 'Sustainable farming products and organic solutions.',
    location: 'Nelspruit, Mpumalanga',
    contact: '+27 83 567 8901',
    email: '<EMAIL>',
    rating: 4.5,
    verified: false,
    categories: ['Feed', 'Organic', 'Seeds'],
    productsCount: 31,
    joinedDate: '2022-01-18',
    featured: false
  },
  {
    id: 6,
    name: 'FarmEquip Pro',
    description: 'Heavy-duty farm equipment and transportation solutions.',
    location: 'Bloemfontein, Free State',
    contact: '+27 84 678 9012',
    email: '<EMAIL>',
    rating: 4.4,
    verified: true,
    categories: ['Equipment', 'Transport'],
    productsCount: 27,
    joinedDate: '2020-09-30',
    featured: false
  }
];

// Featured suppliers for the top section
const featuredSuppliers = [
  {
    id: 1,
    title: 'Premium Livestock Breeders',
    subtitle: 'Top-rated supplier of quality breeding stock',
    rating: 4.9,
    module: 'commercial',
    uniqueId: 'supplier-1',
    icon: <Store />
  },
  {
    id: 2,
    title: 'FarmFresh Supplies',
    subtitle: 'Comprehensive range of feed and supplements',
    rating: 4.7,
    module: 'commercial',
    uniqueId: 'supplier-2',
    icon: <Store />
  },
  {
    id: 3,
    title: 'VetMed Solutions',
    subtitle: 'Complete veterinary and health products',
    rating: 4.8,
    module: 'commercial',
    uniqueId: 'supplier-3',
    icon: <Store />
  }
];

const MarketplaceSuppliers: React.FC = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleSortToggle = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  // Filter suppliers based on search query and active tab
  const filteredSuppliers = mockSuppliers
    .filter(supplier => {
      if (!searchQuery) return true;
      return (
        supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        supplier.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        supplier.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        supplier.categories.some(cat => cat.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    })
    .filter(supplier => {
      if (activeTab === 0) return true; // All suppliers
      if (activeTab === 1) return supplier.featured; // Featured suppliers
      if (activeTab === 2) return supplier.verified; // Verified suppliers
      return true;
    })
    .sort((a, b) => {
      if (sortOrder === 'asc') {
        return a.rating - b.rating;
      } else {
        return b.rating - a.rating;
      }
    });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <Box sx={{ px: 3, pb: 5 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Featured Suppliers */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 5 }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Featured Suppliers
            </Typography>
            <Grid container spacing={3}>
              {featuredSuppliers.map((supplier, index) => (
                <Grid item xs={12} md={4} key={supplier.id}>
                  <AnimatedBackgroundCard
                    title={supplier.title}
                    subtitle={supplier.subtitle}
                    module={supplier.module}
                    uniqueId={supplier.uniqueId}
                    icon={supplier.icon}
                    accentColor={theme.palette.primary.main}
                    secondaryColor={theme.palette.primary.dark}
                    actionLabel="View Supplier"
                    delay={index * 0.1}
                    height={180}
                  >
                    <Box
                      sx={{
                        mt: 2,
                        display: 'flex',
                        alignItems: 'center',
                        px: 2,
                        py: 1,
                        bgcolor: 'rgba(255, 255, 255, 0.2)',
                        borderRadius: 2,
                        width: 'fit-content'
                      }}
                    >
                      <Rating value={supplier.rating} precision={0.1} size="small" readOnly />
                      <Typography variant="body2" sx={{ ml: 1, color: 'white', fontWeight: 'bold' }}>
                        {supplier.rating}
                      </Typography>
                    </Box>
                  </AnimatedBackgroundCard>
                </Grid>
              ))}
            </Grid>
          </Box>
        </motion.div>

        {/* Search and Filter */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 4, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
            <TextField
              placeholder="Search suppliers..."
              variant="outlined"
              fullWidth
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ flex: 1 }}
            />
            <CustomButton
              variant="outlined"
              startIcon={<FilterList />}
              sx={{ minWidth: 120 }}
            >
              Filter
            </CustomButton>
            <CustomButton
              variant="outlined"
              startIcon={sortOrder === 'asc' ? <ArrowUpward /> : <ArrowDownward />}
              onClick={handleSortToggle}
              sx={{ minWidth: 120 }}
            >
              Rating
            </CustomButton>
          </Box>
        </motion.div>

        {/* Category Tabs */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mb: 4, borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
            >
              <Tab label="All Suppliers" />
              <Tab label="Featured" />
              <Tab label="Verified" />
            </Tabs>
          </Box>
        </motion.div>

        {/* Suppliers List */}
        <motion.div variants={itemVariants}>
          <Grid container spacing={3}>
            {filteredSuppliers.length > 0 ? (
              filteredSuppliers.map((supplier, index) => (
                <Grid item xs={12} md={6} key={supplier.id}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                        },
                        position: 'relative',
                        overflow: 'hidden'
                      }}
                    >
                      {supplier.featured && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            bgcolor: theme.palette.warning.main,
                            color: 'white',
                            px: 2,
                            py: 0.5,
                            borderBottomLeftRadius: 8,
                            fontWeight: 'bold',
                            zIndex: 2
                          }}
                        >
                          Featured
                        </Box>
                      )}
                      <Box
                        sx={{
                          height: 100,
                          backgroundImage: `url(/images/modules/commercial/commercial-${(supplier.id % 4) + 1}.jpeg)`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center',
                          position: 'relative',
                          '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.65)}, ${alpha(theme.palette.primary.dark, 0.7)})`,
                            zIndex: 1
                          }
                        }}
                      />
                      <CardContent sx={{ flexGrow: 1, pt: 3, position: 'relative' }}>
                        <Avatar
                          sx={{
                            width: 60,
                            height: 60,
                            bgcolor: theme.palette.primary.main,
                            position: 'absolute',
                            top: -30,
                            left: 24,
                            border: `3px solid ${theme.palette.background.paper}`,
                            boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
                          }}
                        >
                          {supplier.name.charAt(0)}
                        </Avatar>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Box sx={{ ml: 8 }}>
                            <Typography variant="h6" component="h2" gutterBottom>
                              {supplier.name}
                              {supplier.verified && (
                                <Tooltip title="Verified Supplier">
                                  <Verified color="primary" sx={{ ml: 1, fontSize: 18 }} />
                                </Tooltip>
                              )}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Rating value={supplier.rating} precision={0.1} size="small" readOnly />
                              <Typography variant="body2" sx={{ ml: 1 }}>
                                {supplier.rating}
                              </Typography>
                            </Box>
                          </Box>
                          <Box>
                            <Chip
                              label={`${supplier.productsCount} Products`}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          </Box>
                        </Box>

                        <Typography variant="body2" color="text.secondary" paragraph>
                          {supplier.description}
                        </Typography>

                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                          {supplier.categories.map((category, idx) => (
                            <Chip key={idx} label={category} size="small" />
                          ))}
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationOn sx={{ fontSize: 18, color: 'text.secondary', mr: 1 }} />
                          <Typography variant="body2">{supplier.location}</Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Phone sx={{ fontSize: 18, color: 'text.secondary', mr: 1 }} />
                          <Typography variant="body2">{supplier.contact}</Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Email sx={{ fontSize: 18, color: 'text.secondary', mr: 1 }} />
                          <Typography variant="body2">{supplier.email}</Typography>
                        </Box>

                        <CustomButton
                          variant="contained"
                          fullWidth
                          sx={{ mt: 'auto' }}
                        >
                          View Supplier
                        </CustomButton>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))
            ) : (
              <Box sx={{ width: '100%', py: 5, textAlign: 'center' }}>
                <Typography variant="h6" color="text.secondary">
                  No suppliers found matching your criteria
                </Typography>
                <CustomButton
                  variant="outlined"
                  sx={{ mt: 2 }}
                  onClick={() => {
                    setSearchQuery('');
                    setActiveTab(0);
                  }}
                >
                  Clear Filters
                </CustomButton>
              </Box>
            )}
          </Grid>
        </motion.div>
      </motion.div>
    </Box>
  );
};

export default MarketplaceSuppliers;
