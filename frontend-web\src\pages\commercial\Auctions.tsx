import React, { useState } from 'react';
import { Box, Grid, Typo<PERSON>, Card, CardContent, TextField, InputAdornment, Chip, IconButton, useTheme, alpha, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem, Tooltip, Badge, SelectChangeEvent, CircularProgress, Tab, Tabs } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Add,
  Edit,
  Delete,
  Search,
  Gavel,
  Event,
  LocationOn,
  Group,
  AttachMoney,
  Visibility,
  Close,
  CheckCircle,
  Cancel,
  Schedule,
  ArrowUpward,
  ArrowDownward,
  Warehouse,
  CalendarMonth
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { getConsistentBackground } from '../../utils/backgroundThemes';
import { useCommercialData } from '../../hooks/useCommercialData';
import { Auction } from '../../types/commercial';
import AuctionModal from '../../components/commercial/AuctionModal';
import { BkbAuctionProvider } from '../../contexts/commercial/BkbAuctionContext';
import BkbAuctionCalendar from '../../components/commercial/BkbAuctionCalendar';

// Import utility functions
import { formatCurrency, formatDate } from '../../utils/commercialUtils';

// Tab interface
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`auction-tabpanel-${index}`}
      aria-labelledby={`auction-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `auction-tab-${index}`,
    'aria-controls': `auction-tabpanel-${index}`,
  };
}

const Auctions: React.FC = () => {
  const theme = useTheme();
  const {
    auctions,
    loading,
    error,
    addAuction,
    updateAuction,
    deleteAuction
  } = useCommercialData();

  // State for filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [selectedAuction, setSelectedAuction] = useState<Auction | null>(null);
  const [auctionModalOpen, setAuctionModalOpen] = useState(false);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle pagination change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle status filter change
  const handleStatusFilterChange = (event: SelectChangeEvent) => {
    setStatusFilter(event.target.value);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle auction modal open
  const handleAuctionModalOpen = (auction?: Auction) => {
    setSelectedAuction(auction || null);
    setAuctionModalOpen(true);
  };

  // Handle auction details modal open
  const handleDetailsModalOpen = (auction: Auction) => {
    setSelectedAuction(auction);
    setDetailsModalOpen(true);
  };

  // Handle auction save
  const handleAuctionSave = async (auction: Omit<Auction, 'id'>) => {
    try {
      setActionLoading(true);
      if (selectedAuction && selectedAuction.id) {
        await updateAuction(selectedAuction.id, auction);
      } else {
        await addAuction(auction);
      }
      setAuctionModalOpen(false);
    } catch (error) {
      console.error('Error saving auction:', error);
    } finally {
      setActionLoading(false);
    }
  };

  // Handle auction delete
  const handleAuctionDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this auction?')) {
      try {
        await deleteAuction(id);
      } catch (error) {
        console.error('Error deleting auction:', error);
      }
    }
  };

  // Filter and sort auctions
  const filteredAuctions = auctions
    .filter(auction => {
      // Filter by status
      if (statusFilter !== 'all' && auction.status !== statusFilter) {
        return false;
      }

      // Filter by search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        return (
          auction.title.toLowerCase().includes(term) ||
          auction.location.toLowerCase().includes(term) ||
          auction.description.toLowerCase().includes(term)
        );
      }

      return true;
    })
    .sort((a, b) => {
      // Sort by selected field
      if (sortField === 'date') {
        return sortDirection === 'asc'
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortField === 'title') {
        return sortDirection === 'asc'
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      } else if (sortField === 'bidders') {
        return sortDirection === 'asc'
          ? a.registeredBidders - b.registeredBidders
          : b.registeredBidders - a.registeredBidders;
      } else if (sortField === 'items') {
        return sortDirection === 'asc'
          ? a.items.length - b.items.length
          : b.items.length - a.items.length;
      }
      return 0;
    });

  // Calculate auction statistics
  const auctionStats = {
    total: auctions.length,
    upcoming: auctions.filter(a => a.status === 'upcoming').length,
    active: auctions.filter(a => a.status === 'active').length,
    completed: auctions.filter(a => a.status === 'completed').length,
    totalItems: auctions.reduce((sum, auction) => sum + auction.items.length, 0),
    totalBidders: auctions.reduce((sum, auction) => sum + auction.registeredBidders, 0)
  };

  // Get status chip color
  const getStatusChipProps = (status: string) => {
    switch (status) {
      case 'upcoming':
        return { color: 'primary', icon: <Schedule fontSize="small" /> };
      case 'active':
        return { color: 'success', icon: <Gavel fontSize="small" /> };
      case 'completed':
        return { color: 'default', icon: <CheckCircle fontSize="small" /> };
      case 'cancelled':
        return { color: 'error', icon: <Cancel fontSize="small" /> };
      default:
        return { color: 'default', icon: null };
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Livestock Auctions"
        subtitle="Manage and participate in livestock auctions across South Africa"
        module="commercial"
        submodule="auctions"
        actionLabel="New Auction"
        actionIcon={<Add />}
        onAction={handleAuctionModalOpen}
      />

      {/* Auction Tabs */}
      <Box sx={{ px: 3, mb: 2 }}>
        <Paper
          elevation={0}
          sx={{
            borderRadius: 2,
            boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
            overflow: 'hidden'
          }}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                minHeight: 64,
                fontWeight: 'bold',
                transition: 'all 0.3s ease',
              },
              '& .Mui-selected': {
                color: theme.palette.primary.main,
              },
              '& .MuiTabs-indicator': {
                height: 3,
                borderTopLeftRadius: 3,
                borderTopRightRadius: 3,
              }
            }}
          >
            <Tab
              label="Farm Auctions"
              icon={<Gavel />}
              iconPosition="start"
              {...a11yProps(0)}
            />
            <Tab
              label="BKB Auctions"
              icon={<CalendarMonth />}
              iconPosition="start"
              {...a11yProps(1)}
            />
          </Tabs>
        </Paper>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {/* Featured Auctions */}
        <Box sx={{ px: 3, mb: 4 }}>
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3 }}>
            Featured Auctions
          </Typography>
          <Grid container spacing={3}>
            {auctions.filter(a => a.featured).slice(0, 3).map((auction, index) => (
              <Grid item xs={12} md={4} key={auction.id}>
                <AnimatedBackgroundCard
                  title={auction.title}
                  subtitle={`${formatDate(auction.date)} • ${auction.location}`}
                  module="commercial"
                  uniqueId={`auction-${auction.id}`}
                  icon={<Gavel />}
                  actionLabel="View Details"
                  onAction={() => handleDetailsModalOpen(auction)}
                  delay={index * 0.1}
                  height={180}
                >
                  <Box
                    sx={{
                      mt: 2,
                      display: 'inline-block',
                      px: 2,
                      py: 1,
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      borderRadius: 2,
                      fontWeight: 'bold'
                    }}
                  >
                    {auction.status === 'upcoming' ? 'Starting Soon' :
                     auction.status === 'active' ? 'Live Now' : 'Completed'}
                  </Box>
                </AnimatedBackgroundCard>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Box sx={{ px: 3, pb: 5 }}>
          {/* Statistics Cards */}
          <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Upcoming Auctions"
              subtitle={`${auctionStats.upcoming} auctions scheduled`}
              module="commercial"
              uniqueId="stats-upcoming"
              icon={<Event />}
              accentColor={theme.palette.primary.main}
              delay={0.1}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {auctionStats.upcoming}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Active Auctions"
              subtitle="Currently running auctions"
              module="commercial"
              uniqueId="stats-active"
              icon={<Gavel />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.2}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {auctionStats.active}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Total Items"
              subtitle="Livestock and equipment for auction"
              module="commercial"
              uniqueId="stats-completed"
              icon={<AttachMoney />}
              accentColor={theme.palette.warning.main}
              secondaryColor={theme.palette.warning.dark}
              delay={0.3}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {auctionStats.totalItems}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
          <TextField
            placeholder="Search auctions..."
            variant="outlined"
            fullWidth
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flex: 1 }}
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              value={statusFilter}
              label="Status"
              onChange={handleStatusFilterChange}
            >
              <MenuItem value="all">All Statuses</MenuItem>
              <MenuItem value="upcoming">Upcoming</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>

          <CustomButton
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleAuctionModalOpen()}
            sx={{ minWidth: 180 }}
          >
            Create Auction
          </CustomButton>
        </Box>

        {/* Auctions Table */}
        <Card sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
        }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight="bold">
                Auctions
              </Typography>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Box sx={{ py: 4, textAlign: 'center' }}>
                <Typography color="error">Error loading auctions. Please try again.</Typography>
              </Box>
            ) : (
              <>
                <TableContainer component={Paper} sx={{ boxShadow: 'none', mb: 2 }}>
                  <Table
                  sx={{
                    '& .MuiTableRow-root:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      transition: 'background-color 0.3s ease'
                    }
                  }}
                >
                    <TableHead>
                      <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                        <TableCell
                          onClick={() => handleSortChange('title')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Title
                            {sortField === 'title' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell
                          onClick={() => handleSortChange('date')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Date
                            {sortField === 'date' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell
                          onClick={() => handleSortChange('items')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Items
                            {sortField === 'items' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell
                          onClick={() => handleSortChange('bidders')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Bidders
                            {sortField === 'bidders' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredAuctions
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((auction, index) => {
                          const statusProps = getStatusChipProps(auction.status);

                          return (
                            <TableRow
                              key={auction.id}
                              hover
                              component={motion.tr}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.05 }}
                            >
                              <TableCell>
                                <Typography variant="body2" fontWeight="medium">
                                  {auction.title}
                                </Typography>
                              </TableCell>
                              <TableCell>{formatDate(auction.date)}</TableCell>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <LocationOn fontSize="small" sx={{ mr: 0.5, color: theme.palette.text.secondary }} />
                                  <Typography variant="body2">{auction.location}</Typography>
                                </Box>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  badgeContent={auction.items.length}
                                  color="primary"
                                  sx={{ '& .MuiBadge-badge': { fontSize: '0.7rem', height: '18px', minWidth: '18px' } }}
                                >
                                  <Warehouse fontSize="small" />
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Badge
                                  badgeContent={auction.registeredBidders}
                                  color="info"
                                  sx={{ '& .MuiBadge-badge': { fontSize: '0.7rem', height: '18px', minWidth: '18px' } }}
                                >
                                  <Group fontSize="small" />
                                </Badge>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  icon={statusProps.icon || undefined}
                                  label={auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
                                  color={statusProps.color as any}
                                  size="small"
                                />
                              </TableCell>
                              <TableCell align="right">
                                <Tooltip title="View Details">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDetailsModalOpen(auction)}
                                    sx={{ mr: 1 }}
                                  >
                                    <Visibility fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Edit">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleAuctionModalOpen(auction)}
                                    sx={{ mr: 1 }}
                                  >
                                    <Edit fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Delete">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleAuctionDelete(auction.id)}
                                    color="error"
                                  >
                                    <Delete fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      {filteredAuctions.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                            <Typography variant="body1" color="text.secondary">
                              No auctions found
                            </Typography>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={filteredAuctions.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </>
            )}
          </CardContent>
        </Card>
        </Box>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Box sx={{ px: 3, pb: 5 }}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)'
            }}
          >
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" color="text.secondary" paragraph>
                BKB is a leading agricultural services company in South Africa that hosts regular livestock auctions.
                View upcoming auctions and plan your participation.
              </Typography>
            </Box>

            <BkbAuctionProvider>
              <BkbAuctionCalendar />
            </BkbAuctionProvider>
          </Paper>
        </Box>
      </TabPanel>

      {/* Auction Modal */}
      <AuctionModal
        open={auctionModalOpen}
        onClose={() => setAuctionModalOpen(false)}
        auction={selectedAuction}
        onSave={handleAuctionSave}
        loading={actionLoading}
      />

      {/* Auction Details Modal */}
      <Dialog
        open={detailsModalOpen}
        onClose={() => setDetailsModalOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            overflow: 'hidden'
          }
        }}
      >
        {selectedAuction && (
          <>
            <DialogTitle>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{selectedAuction.title}</Typography>
                <IconButton onClick={() => setDetailsModalOpen(false)} size="small">
                  <Close fontSize="small" />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent dividers>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 1,
                      mb: 2,
                      backgroundImage: `url(${getConsistentBackground(`auction-${selectedAuction.id}`)})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.85)}, ${alpha(theme.palette.primary.dark, 0.9)})`,
                        borderRadius: 1,
                      }
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ color: 'white', position: 'relative', zIndex: 1 }}>
                      Auction Details
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Date</Typography>
                        <Typography variant="body1">{formatDate(selectedAuction.date)}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Status</Typography>
                        <Chip
                          icon={getStatusChipProps(selectedAuction.status).icon || undefined}
                          label={selectedAuction.status.charAt(0).toUpperCase() + selectedAuction.status.slice(1)}
                          color={getStatusChipProps(selectedAuction.status).color as any}
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Location</Typography>
                        <Typography variant="body1">{selectedAuction.location}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Registered Bidders</Typography>
                        <Typography variant="body1">{selectedAuction.registeredBidders}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Minimum Bid</Typography>
                        <Typography variant="body1">{formatCurrency(selectedAuction.minimumBid)}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Description</Typography>
                        <Typography variant="body1">{selectedAuction.description}</Typography>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 1,
                      mb: 2,
                      backgroundImage: `url(${getConsistentBackground(`auction-items-${selectedAuction.id}`)})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.85)}, ${alpha(theme.palette.secondary.dark, 0.9)})`,
                        borderRadius: 1,
                      }
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ color: 'white', position: 'relative', zIndex: 1 }}>
                      Auction Items
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 1,
                      mb: 2,
                      backgroundImage: `url(${getConsistentBackground(`auction-schedule-${selectedAuction.id}`)})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                      position: 'relative',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.85)}, ${alpha(theme.palette.info.dark, 0.9)})`,
                        borderRadius: 1,
                      }
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom sx={{ color: 'white', position: 'relative', zIndex: 1 }}>
                      Auction Schedule
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Start Time</Typography>
                        <Typography variant="body1">
                          {new Date(selectedAuction.date).toLocaleTimeString('en-ZA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">End Time</Typography>
                        <Typography variant="body1">
                          {new Date(selectedAuction.endTime).toLocaleTimeString('en-ZA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Auction Items ({selectedAuction.items.length})
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table
                  size="small"
                  sx={{
                    '& .MuiTableRow-root:hover': {
                      backgroundColor: 'rgba(0, 0, 0, 0.04)',
                      transition: 'background-color 0.3s ease'
                    }
                  }}
                >
                      <TableHead>
                        <TableRow>
                          <TableCell>Type</TableCell>
                          <TableCell>Description</TableCell>
                          <TableCell align="right">Starting Price</TableCell>
                          <TableCell align="right">Current Bid</TableCell>
                          <TableCell>Status</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {selectedAuction.items.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} align="center">
                              <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
                                No items in this auction
                              </Typography>
                            </TableCell>
                          </TableRow>
                        ) : (
                          selectedAuction.items.map((item) => (
                            <TableRow key={item.id}>
                              <TableCell>
                                <Chip
                                  label={item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                                  size="small"
                                  color={
                                    item.type === 'livestock' ? 'success' :
                                    item.type === 'equipment' ? 'info' :
                                    item.type === 'feed' ? 'warning' : 'default'
                                  }
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell>{item.description}</TableCell>
                              <TableCell align="right">{formatCurrency(item.startingPrice)}</TableCell>
                              <TableCell align="right">
                                {item.currentBid ? formatCurrency(item.currentBid) : '-'}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                                  size="small"
                                  color={
                                    item.status === 'available' ? 'primary' :
                                    item.status === 'sold' ? 'success' : 'default'
                                  }
                                />
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <CustomButton onClick={() => setDetailsModalOpen(false)}>Close</CustomButton>
              {selectedAuction.status === 'upcoming' && (
                <CustomButton
                  variant="contained"
                  color="primary"
                  startIcon={<Edit />}
                  onClick={() => {
                    setDetailsModalOpen(false);
                    handleAuctionModalOpen(selectedAuction);
                  }}
                >
                  Edit Auction
                </CustomButton>
              )}
              {selectedAuction.status === 'active' && (
                <CustomButton
                  variant="contained"
                  color="success"
                  startIcon={<Gavel />}
                >
                  Join Auction
                </CustomButton>
              )}
            </DialogActions>
          </>
        )}
      </Dialog>
    </Box>
  );
};

export default Auctions;
