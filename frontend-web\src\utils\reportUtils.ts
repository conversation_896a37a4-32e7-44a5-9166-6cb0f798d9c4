import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import { exportToCSV, exportToExcel, exportToPDF } from './export';
import API_ROUTES from './apiRoutes';
import axios from 'axios';
import { formatCurrency } from './formatters';

// Add this to fix the TypeScript error
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

/**
 * Generate and download a report
 * @param data The data to include in the report
 * @param filename The name of the file (without extension)
 * @param format The format of the report (csv, excel, pdf)
 * @param title Optional title for the report
 */
export const generateReport = (
  data: any[],
  filename: string,
  format: 'csv' | 'excel' | 'pdf',
  title?: string
): void => {
  if (!data || data.length === 0) {
    console.error('No data provided for report generation');
    return;
  }

  // Add timestamp to filename
  const timestamp = new Date().toISOString().split('T')[0];
  const fullFilename = `${filename}_${timestamp}`;

  try {
    switch (format) {
      case 'csv':
        exportToCSV(data, fullFilename);
        break;
      case 'excel':
        exportToExcel(data, fullFilename);
        break;
      case 'pdf':
        exportToPDF(data, fullFilename);
        break;
      default:
        console.error(`Unsupported format: ${format}`);
    }
  } catch (error) {
    console.error('Error generating report:', error);
  }
};

/**
 * Generate and download a report from the API
 * @param module The module to generate the report for (e.g., 'animals', 'health')
 * @param reportType The type of report (e.g., 'inventory', 'health')
 * @param format The format of the report (csv, excel, pdf)
 * @param filters Optional filters to apply to the report data
 */
export const generateReportFromAPI = async (
  module: string,
  reportType: string,
  format: 'csv' | 'excel' | 'pdf',
  filters: Record<string, any> = {}
): Promise<string> => {
  try {
    // First try to get the report from the API
    const response = await axios({
      url: API_ROUTES.reports.generate,
      method: 'POST',
      responseType: 'blob',
      data: {
        module,
        reportType,
        format,
        filters
      }
    });

    // Get the filename from the Content-Disposition header
    const contentDisposition = response.headers['content-disposition'];
    let filename = `${module}_${reportType}_${new Date().toISOString().split('T')[0]}.${format}`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch && filenameMatch.length === 2) {
        filename = filenameMatch[1];
      }
    }

    // Download the file
    saveAs(new Blob([response.data]), filename);
    return filename;
  } catch (error) {
    console.error('Error generating report from API:', error);

    // Fallback to client-side generation if API fails
    if (error && typeof error === 'object' && 'response' in error && typeof error.response === 'object' && error.response && 'status' in error.response && error.response.status === 404) {
      console.warn('API endpoint not found, falling back to client-side generation');
      return generateReportFallback(module, reportType, format, filters);
    }

    throw error;
  }
};

/**
 * Fallback function to generate reports client-side when API is not available
 */
const generateReportFallback = async (
  module: string,
  reportType: string,
  format: 'csv' | 'excel' | 'pdf',
  filters: Record<string, any> = {}
): Promise<string> => {
  // This would normally fetch data from a local store or context
  // For now, we'll just generate some mock data
  const mockData = generateMockData(module, reportType);

  // Generate the filename
  const filename = `${module}_${reportType}_${new Date().toISOString().split('T')[0]}`;

  // Generate the report
  generateReport(mockData, filename, format);

  return `${filename}.${format}`;
};

/**
 * Generate mock data for reports
 */
const generateMockData = (module: string, reportType: string): any[] => {
  // Generate different mock data based on module and report type
  switch (module) {
    case 'animals':
      return [
        { id: 'ANM001', name: 'Bella', breed: 'Nguni', age: 3, weight: 450, status: 'healthy' },
        { id: 'ANM002', name: 'Themba', breed: 'Bonsmara', age: 4, weight: 520, status: 'healthy' },
        { id: 'ANM003', name: 'Nandi', breed: 'Brahman', age: 2, weight: 380, status: 'pregnant' },
        { id: 'ANM004', name: 'Sipho', breed: 'Afrikaner', age: 5, weight: 580, status: 'healthy' },
        { id: 'ANM005', name: 'Uthando', breed: 'Boer Goat', age: 1, weight: 45, status: 'sick' }
      ];
    case 'health':
      return [
        { id: 'HR001', animalId: 'ANM001', date: '2024-03-01', type: 'vaccination', description: 'Annual vaccination', cost: 250, formattedCost: formatCurrency(250) },
        { id: 'HR002', animalId: 'ANM002', date: '2024-03-02', type: 'checkup', description: 'Routine checkup', cost: 150, formattedCost: formatCurrency(150) },
        { id: 'HR003', animalId: 'ANM003', date: '2024-03-03', type: 'treatment', description: 'Parasite treatment', cost: 350, formattedCost: formatCurrency(350) },
        { id: 'HR004', animalId: 'ANM004', date: '2024-03-04', type: 'vaccination', description: 'Annual vaccination', cost: 250, formattedCost: formatCurrency(250) },
        { id: 'HR005', animalId: 'ANM005', date: '2024-03-05', type: 'treatment', description: 'Antibiotic treatment', cost: 450, formattedCost: formatCurrency(450) }
      ];
    case 'breeding':
      return [
        { id: 'BR001', damId: 'ANM001', sireId: 'ANM004', breedingDate: '2024-02-01', status: 'confirmed' },
        { id: 'BR002', damId: 'ANM003', sireId: 'ANM004', breedingDate: '2024-02-15', status: 'pending' },
        { id: 'BR003', damId: 'ANM005', sireId: 'ANM002', breedingDate: '2024-01-20', status: 'unsuccessful' }
      ];
    case 'financial':
      return [
        { id: 'FIN001', date: '2024-03-01', type: 'income', category: 'sales', amount: 15000, formattedAmount: formatCurrency(15000), description: 'Cattle sale' },
        { id: 'FIN002', date: '2024-03-02', type: 'expense', category: 'feed', amount: -5000, formattedAmount: formatCurrency(-5000), description: 'Monthly feed purchase' },
        { id: 'FIN003', date: '2024-03-03', type: 'expense', category: 'veterinary', amount: -2500, formattedAmount: formatCurrency(-2500), description: 'Veterinary services' },
        { id: 'FIN004', date: '2024-03-04', type: 'income', category: 'subsidy', amount: 7500, formattedAmount: formatCurrency(7500), description: 'Government subsidy' },
        { id: 'FIN005', date: '2024-03-05', type: 'expense', category: 'equipment', amount: -10000, formattedAmount: formatCurrency(-10000), description: 'New equipment' }
      ];
    case 'financial-summary':
      return [
        { category: 'Total Revenue', amount: 1250000, formattedAmount: formatCurrency(1250000), percentage: '100%' },
        { category: 'Total Expenses', amount: -875000, formattedAmount: formatCurrency(-875000), percentage: '70%' },
        { category: 'Net Profit', amount: 375000, formattedAmount: formatCurrency(375000), percentage: '30%' },
        { category: 'Cash on Hand', amount: 450000, formattedAmount: formatCurrency(450000), percentage: '36%' }
      ];
    case 'profit-loss':
      return [
        { month: 'January', revenue: 180000, expenses: -150000, profit: 30000, formattedRevenue: formatCurrency(180000), formattedExpenses: formatCurrency(-150000), formattedProfit: formatCurrency(30000) },
        { month: 'February', revenue: 195000, expenses: -145000, profit: 50000, formattedRevenue: formatCurrency(195000), formattedExpenses: formatCurrency(-145000), formattedProfit: formatCurrency(50000) },
        { month: 'March', revenue: 210000, expenses: -160000, profit: 50000, formattedRevenue: formatCurrency(210000), formattedExpenses: formatCurrency(-160000), formattedProfit: formatCurrency(50000) },
        { month: 'April', revenue: 205000, expenses: -155000, profit: 50000, formattedRevenue: formatCurrency(205000), formattedExpenses: formatCurrency(-155000), formattedProfit: formatCurrency(50000) },
        { month: 'May', revenue: 225000, expenses: -165000, profit: 60000, formattedRevenue: formatCurrency(225000), formattedExpenses: formatCurrency(-165000), formattedProfit: formatCurrency(60000) },
        { month: 'June', revenue: 235000, expenses: -170000, profit: 65000, formattedRevenue: formatCurrency(235000), formattedExpenses: formatCurrency(-170000), formattedProfit: formatCurrency(65000) }
      ];
    default:
      return [
        { id: '001', name: 'Sample Item 1', value: 100 },
        { id: '002', name: 'Sample Item 2', value: 200 },
        { id: '003', name: 'Sample Item 3', value: 300 },
        { id: '004', name: 'Sample Item 4', value: 400 },
        { id: '005', name: 'Sample Item 5', value: 500 }
      ];
  }
};
