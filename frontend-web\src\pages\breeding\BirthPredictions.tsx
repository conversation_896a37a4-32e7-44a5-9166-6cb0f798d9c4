import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { format, differenceInDays } from 'date-fns';
import { Box, Typography, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, CircularProgress, Tabs, Tab, FormControl, InputLabel, Select, MenuItem, Tooltip, IconButton, Divider } from '@mui/material';
import { getSelectableTabsStyles, getSelectableTabPanelStyles, getSelectableTabContentStyles } from '../../utils/tabSelectionUtils';
import useTabSelection from '../../hooks/useTabSelection';
import {
  Warning,
  CalendarToday,
  Add,
  Info,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  History as HistoryIcon,
  Phone as PhoneIcon,
  RecordVoiceOver as VoiceIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  NotificationsActive as NotificationsActiveIcon
} from '../../utils/iconImports';
import SendIcon from '@mui/icons-material/Send';
import {  ModuleHeader, AnimatedBackgroundCard, ResponsiveLayout , CustomButton } from '../../components/common';
import { useSMSService } from '../../services/smsService';
import { useSnackbar } from '../../hooks/useSnackbar';
import { useNotificationService, NotificationType, BirthNotificationConfig } from '../../services/notificationService';
import { notificationSchedulerService } from '../../services/notificationSchedulerService';
import { notificationHistoryService, NotificationHistoryEntry } from '../../services/notificationHistoryService';
import NotificationConfig from '../../components/breeding/NotificationConfig';
import NotificationHistory from '../../components/breeding/NotificationHistory';
import TestNotification from '../../components/breeding/TestNotification';

interface Prediction {
  id: string;
  animalId: string;
  animalName: string;
  breedingDate: Date;
  expectedDueDate: Date;
  daysRemaining: number;
  riskLevel: 'high' | 'medium' | 'low';
  specialCare: string[];
  lastCheckup: Date;
  vitals: {
    temperature: number;
    heartRate: number;
    respirationRate: number;
  };
  notes: string;
  notificationConfig?: BirthNotificationConfig;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`birth-predictions-tabpanel-${index}`}
      aria-labelledby={`birth-predictions-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3, ...getSelectableTabPanelStyles() }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const BirthPredictions: React.FC = () => {
  const theme = useTheme();
  const { sendBirthPredictionAlert } = useSMSService();
  const { showSnackbar } = useSnackbar();
  const { sendBirthPredictionAlert: sendMultiChannelAlert } = useNotificationService();
  const [tabValue, setTabValue] = useState(0);
  const [showTestNotification, setShowTestNotification] = useState(true);
  const tabContainerRef = useTabSelection();
  const [predictions] = useState<Prediction[]>([
    {
      id: 'BP001',
      animalId: 'CTL001',
      animalName: 'Bella',
      breedingDate: new Date('2023-09-15'),
      expectedDueDate: new Date('2024-06-15'),
      daysRemaining: 45,
      riskLevel: 'high',
      specialCare: ['Isolation required', 'Increased monitoring', 'Special diet'],
      lastCheckup: new Date('2024-04-01'),
      vitals: {
        temperature: 38.5,
        heartRate: 75,
        respirationRate: 30
      },
      notes: 'Showing early signs of labor'
    },
    {
      id: 'BP002',
      animalId: 'CTL005',
      animalName: 'Lerato',
      breedingDate: new Date('2023-10-10'),
      expectedDueDate: new Date('2024-07-10'),
      daysRemaining: 70,
      riskLevel: 'medium',
      specialCare: ['Regular monitoring', 'Balanced diet'],
      lastCheckup: new Date('2024-04-05'),
      vitals: {
        temperature: 38.2,
        heartRate: 70,
        respirationRate: 28
      },
      notes: 'Progressing normally'
    },
    {
      id: 'BP003',
      animalId: 'CTL008',
      animalName: 'Tumelo',
      breedingDate: new Date('2023-11-05'),
      expectedDueDate: new Date('2024-08-05'),
      daysRemaining: 96,
      riskLevel: 'low',
      specialCare: ['Regular checkups'],
      lastCheckup: new Date('2024-04-10'),
      vitals: {
        temperature: 38.0,
        heartRate: 68,
        respirationRate: 26
      },
      notes: 'Healthy pregnancy'
    }
  ]);

  const [alertDialogOpen, setAlertDialogOpen] = useState(false);
  const [selectedAnimal, setSelectedAnimal] = useState<Prediction | null>(null);
  const [staffPhone, setStaffPhone] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [notificationHistory, setNotificationHistory] = useState<NotificationHistoryEntry[]>([]);
  const [selectedNotificationTypes, setSelectedNotificationTypes] = useState<NotificationType[]>(['sms']);
  const [staffEmail, setStaffEmail] = useState('');
  const [staffName, setStaffName] = useState('');
  const [globalNotificationConfig, setGlobalNotificationConfig] = useState<BirthNotificationConfig>({
    enabled: true,
    notifyDaysBefore: [1, 3, 7, 14, 30],
    recipients: [],
    notificationTypes: ['sms', 'email', 'app'],
    escalationEnabled: true,
    escalationThresholds: {
      critical: 2,
      high: 7,
      medium: 14
    }
  });

  // Initialize notification history
  useEffect(() => {
    // In a real application, this would fetch from an API
    // For now, we'll generate some mock history
    const mockHistory: NotificationHistoryEntry[] = [];

    // Generate some mock history entries
    for (let i = 0; i < 20; i++) {
      const prediction = predictions[Math.floor(Math.random() * predictions.length)];
      const notificationType: NotificationType = ['sms', 'voice', 'email', 'whatsapp', 'app'][Math.floor(Math.random() * 5)] as NotificationType;
      const priority = ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any;
      const status = Math.random() > 0.2 ? 'success' : 'failed';

      mockHistory.push({
        id: `history-${i}`,
        timestamp: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000),
        animalId: prediction.animalId,
        animalName: prediction.animalName,
        notificationType,
        recipient: {
          name: 'Farm Manager',
          contact: notificationType === 'email' ? '<EMAIL>' : '+27123456789'
        },
        message: `BIRTH ALERT: ${prediction.animalName} (ID: ${prediction.animalId}) is expected to give birth in ${prediction.daysRemaining} days.`,
        priority,
        status,
        error: status === 'failed' ? 'Connection error' : undefined
      });
    }

    setNotificationHistory(mockHistory);
  }, [predictions]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getRiskColor = (risk: string) => {
    const colors = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    return colors[risk as keyof typeof colors];
  };

  const handleAlertStaff = (prediction: Prediction) => {
    setSelectedAnimal(prediction);
    setStaffPhone('');
    setStaffEmail('');
    setStaffName('Farm Manager');
    setSelectedNotificationTypes(['sms']);
    setAlertDialogOpen(true);
  };

  const handleSendAlert = async () => {
    if (!selectedAnimal || (!staffPhone && !staffEmail)) return;

    setIsSending(true);
    try {
      // Determine which notification methods to use
      if (selectedNotificationTypes.includes('sms') && staffPhone) {
        // Send SMS alert
        const smsResult = await sendBirthPredictionAlert(
          selectedAnimal.animalId,
          selectedAnimal.animalName,
          selectedAnimal.daysRemaining,
          staffPhone
        );

        if (smsResult.success) {
          // Add to notification history
          notificationHistoryService.addEntry({
            timestamp: new Date(),
            animalId: selectedAnimal.animalId,
            animalName: selectedAnimal.animalName,
            notificationType: 'sms',
            recipient: {
              name: staffName || 'Staff Member',
              contact: staffPhone
            },
            message: `BIRTH ALERT: ${selectedAnimal.animalName} (ID: ${selectedAnimal.animalId}) is expected to give birth in ${selectedAnimal.daysRemaining} days.`,
            priority: selectedAnimal.daysRemaining <= 2 ? 'critical' :
                     selectedAnimal.daysRemaining <= 7 ? 'high' :
                     selectedAnimal.daysRemaining <= 14 ? 'medium' : 'low',
            status: 'success'
          });
        }
      }

      // Send multi-channel alerts
      const recipient = {
        name: staffName || 'Staff Member',
        phone: staffPhone,
        email: staffEmail,
        notificationTypes: selectedNotificationTypes
      };

      const results = await sendMultiChannelAlert(
        selectedAnimal.animalId,
        selectedAnimal.animalName,
        selectedAnimal.daysRemaining,
        recipient
      );

      // Update notification history with results
      results.forEach(result => {
        if (result.result.success) {
          notificationHistoryService.addEntry({
            timestamp: new Date(),
            animalId: selectedAnimal.animalId,
            animalName: selectedAnimal.animalName,
            notificationType: result.type,
            recipient: {
              name: staffName || 'Staff Member',
              contact: result.type === 'email' ? staffEmail : staffPhone
            },
            message: `BIRTH ALERT: ${selectedAnimal.animalName} (ID: ${selectedAnimal.animalId}) is expected to give birth in ${selectedAnimal.daysRemaining} days.`,
            priority: selectedAnimal.daysRemaining <= 2 ? 'critical' :
                     selectedAnimal.daysRemaining <= 7 ? 'high' :
                     selectedAnimal.daysRemaining <= 14 ? 'medium' : 'low',
            status: 'success'
          });
        } else {
          notificationHistoryService.addEntry({
            timestamp: new Date(),
            animalId: selectedAnimal.animalId,
            animalName: selectedAnimal.animalName,
            notificationType: result.type,
            recipient: {
              name: staffName || 'Staff Member',
              contact: result.type === 'email' ? staffEmail : staffPhone
            },
            message: `BIRTH ALERT: ${selectedAnimal.animalName} (ID: ${selectedAnimal.animalId}) is expected to give birth in ${selectedAnimal.daysRemaining} days.`,
            priority: selectedAnimal.daysRemaining <= 2 ? 'critical' :
                     selectedAnimal.daysRemaining <= 7 ? 'high' :
                     selectedAnimal.daysRemaining <= 14 ? 'medium' : 'low',
            status: 'failed',
            error: result.result.error
          });
        }
      });

      // Update notification history state
      setNotificationHistory([...notificationHistoryService.getHistory()]);

      // Show success message and close dialog
      showSnackbar(`Birth alert for ${selectedAnimal.animalName} sent successfully`, 'success');
      setAlertDialogOpen(false);
    } catch (error) {
      showSnackbar('Failed to send alert. Please try again.', 'error');
    } finally {
      setIsSending(false);
    }
  };

  // Handle saving the global notification configuration
  const handleSaveGlobalConfig = (config: BirthNotificationConfig) => {
    setGlobalNotificationConfig(config);
    showSnackbar('Global notification settings saved', 'success');
  };

  // Get notification type icon
  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return <PhoneIcon fontSize="small" />;
      case 'voice':
        return <VoiceIcon fontSize="small" />;
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'whatsapp':
        return <WhatsAppIcon fontSize="small" />;
      case 'app':
        return <NotificationsIcon fontSize="small" />;
      default:
        return <NotificationsIcon fontSize="small" />;
    }
  };

  return (
    <Box ref={tabContainerRef}>
      <ModuleHeader
        title="Birth Predictions"
        subtitle="Monitor and track upcoming births with detailed forecasting"
        module="breeding"
        submodule="predictions"
        actionLabel="Add Prediction"
        actionIcon={<Add />}
        onAction={() => console.log('Add new birth prediction')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2, ...getSelectableTabsStyles() }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="birth predictions tabs"
            variant="scrollable"
            scrollButtons="auto"
          >
            <Tab
              label="Overview"
              icon={<Info />}
              iconPosition="start"
              id="birth-predictions-tab-0"
              aria-controls="birth-predictions-tabpanel-0"
            />
            <Tab
              label="Notification Settings"
              icon={<SettingsIcon />}
              iconPosition="start"
              id="birth-predictions-tab-1"
              aria-controls="birth-predictions-tabpanel-1"
            />
            <Tab
              label="Notification History"
              icon={<HistoryIcon />}
              iconPosition="start"
              id="birth-predictions-tab-2"
              aria-controls="birth-predictions-tabpanel-2"
            />
            <Tab
              label="Test Notification"
              icon={<NotificationsActiveIcon />}
              iconPosition="start"
              id="birth-predictions-tab-3"
              aria-controls="birth-predictions-tabpanel-3"
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>

        {/* Summary Cards */}
        <Grid container spacing={3} mb={4} sx={getSelectableTabContentStyles()}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Birth Overview"
              backgroundImage="https://images.unsplash.com/photo-1605493725784-56651e4c5f1a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80"
              icon={<Info />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {predictions.length}
                      </Typography>
                      <Typography variant="body2">Total</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="error.main">
                        {predictions.filter(p => p.riskLevel === 'high').length}
                      </Typography>
                      <Typography variant="body2">High Risk</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(255, 152, 0, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="warning.main">
                        {predictions.filter(p => p.daysRemaining <= 30).length}
                      </Typography>
                      <Typography variant="body2">&lt; 30 Days</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Upcoming Births"
              backgroundImage="https://images.unsplash.com/photo-1560009571-bbf933d6ce8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
              icon={<CalendarToday />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2}>
                {predictions
                  .sort((a, b) => a.daysRemaining - b.daysRemaining)
                  .slice(0, 3)
                  .map(prediction => (
                    <Box key={prediction.id} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {prediction.animalName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Due: {format(prediction.expectedDueDate, 'MMM dd, yyyy')}
                        </Typography>
                        <Chip
                          label={`${prediction.daysRemaining} days`}
                          size="small"
                          color={prediction.daysRemaining <= 7 ? "error" :
                                 prediction.daysRemaining <= 30 ? "warning" : "success"}
                        />
                      </Box>
                    </Box>
                  ))
                }
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Critical Alerts"
              backgroundImage="https://images.unsplash.com/photo-1560009571-bbf933d6ce8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
              icon={<Warning />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2}>
                {predictions
                  .filter(pred => pred.daysRemaining <= 7 || pred.riskLevel === 'high')
                  .map(prediction => (
                    <Box key={prediction.id} mb={2} p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        Imminent Birth Alert: {prediction.animalName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Due in {prediction.daysRemaining} days
                        </Typography>
                        <CustomButton
                          size="small"
                          variant="outlined"
                          color="error"
                          onClick={() => handleAlertStaff(prediction)}
                          startIcon={<SendIcon />}
                        >
                          Alert Staff
                        </CustomButton>
                      </Box>
                    </Box>
                  ))
                }
                {predictions.filter(pred => pred.daysRemaining <= 7 || pred.riskLevel === 'high').length === 0 && (
                  <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                    <Typography variant="body2" color="text.secondary">No critical alerts</Typography>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Main Predictions Table */}
        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent sx={{ p: 0 }}>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white' }}>Animal</TableCell>
                    <TableCell sx={{ color: 'white' }}>Due Date</TableCell>
                    <TableCell sx={{ color: 'white' }}>Days Remaining</TableCell>
                    <TableCell sx={{ color: 'white' }}>Risk Level</TableCell>
                    <TableCell sx={{ color: 'white' }}>Special Care</TableCell>
                    <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {predictions.map(prediction => (
                    <TableRow key={prediction.id} hover>
                      <TableCell>
                        <Box>
                          <Typography variant="body1">{prediction.animalName}</Typography>
                          <Typography variant="body2" color="text.secondary">ID: {prediction.animalId}</Typography>
                        </Box>
                      </TableCell>
                      <TableCell>{format(prediction.expectedDueDate, 'MMM dd, yyyy')}</TableCell>
                      <TableCell>{prediction.daysRemaining} days</TableCell>
                      <TableCell>
                        <Chip
                          label={prediction.riskLevel.toUpperCase()}
                          color={
                            prediction.riskLevel === 'high' ? 'error' :
                            prediction.riskLevel === 'medium' ? 'warning' : 'success'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {prediction.specialCare.map((care, index) => (
                          <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>• {care}</Typography>
                        ))}
                      </TableCell>
                      <TableCell>
                        <Box display="flex">
                          <CustomButton size="small" variant="outlined" color="primary" sx={{ mr: 1 }}>View Details</CustomButton>
                          <CustomButton size="small" variant="outlined" color="error">Alert Staff</CustomButton>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={getSelectableTabContentStyles()}>
            <NotificationConfig
              isGlobal={true}
              initialConfig={globalNotificationConfig}
              onSave={handleSaveGlobalConfig}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={getSelectableTabContentStyles()}>
            <NotificationHistory
              history={notificationHistory}
              onExport={() => console.log('Export notification history')}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box sx={getSelectableTabContentStyles()}>
            {showTestNotification && (
              <TestNotification
                onNotificationSent={() => {
                  // Update notification history when a test notification is sent
                  setNotificationHistory([...notificationHistoryService.getHistory()]);
                }}
              />
            )}
          </Box>
        </TabPanel>
      </Box>

      {/* Alert Staff Dialog */}
      <Dialog open={alertDialogOpen} onClose={() => setAlertDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Send Birth Alert for {selectedAnimal?.animalName}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body1" paragraph>
              Send alerts to staff about the upcoming birth:
            </Typography>
            <Box sx={{ bgcolor: 'background.default', p: 2, borderRadius: 1, mb: 3 }}>
              <Typography variant="body2" fontWeight="medium">
                <strong>Animal:</strong> {selectedAnimal?.animalName} (ID: {selectedAnimal?.animalId})
              </Typography>
              <Typography variant="body2">
                <strong>Expected Due Date:</strong> {selectedAnimal ? format(selectedAnimal.expectedDueDate, 'PPP') : ''}
              </Typography>
              <Typography variant="body2">
                <strong>Days Remaining:</strong> {selectedAnimal?.daysRemaining}
              </Typography>
              <Typography variant="body2">
                <strong>Risk Level:</strong> {selectedAnimal?.riskLevel.toUpperCase()}
              </Typography>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Notification Methods
            </Typography>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="notification-types-label">Notification Methods</InputLabel>
              <Select
                labelId="notification-types-label"
                multiple
                value={selectedNotificationTypes}
                onChange={(e) => setSelectedNotificationTypes(e.target.value as NotificationType[])}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {selected.map((value) => (
                      <Chip
                        key={value}
                        label={value.toUpperCase()}
                        size="small"
                        icon={getNotificationTypeIcon(value)}
                      />
                    ))}
                  </Box>
                )}
              >
                <MenuItem value="sms">
                  <Chip
                    icon={<PhoneIcon />}
                    label="SMS"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">Text message alerts</Typography>
                </MenuItem>
                <MenuItem value="voice">
                  <Chip
                    icon={<VoiceIcon />}
                    label="VOICE"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">Automated voice calls</Typography>
                </MenuItem>
                <MenuItem value="email">
                  <Chip
                    icon={<EmailIcon />}
                    label="EMAIL"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">Email notifications</Typography>
                </MenuItem>
                <MenuItem value="whatsapp">
                  <Chip
                    icon={<WhatsAppIcon />}
                    label="WHATSAPP"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">WhatsApp messages</Typography>
                </MenuItem>
                <MenuItem value="app">
                  <Chip
                    icon={<NotificationsIcon />}
                    label="APP"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  <Typography variant="body2">In-app notifications</Typography>
                </MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Staff Name"
              fullWidth
              value={staffName}
              onChange={(e) => setStaffName(e.target.value)}
              placeholder="Farm Manager"
              sx={{ mb: 2 }}
              required
            />

            {selectedNotificationTypes.some(type => type === 'sms' || type === 'voice' || type === 'whatsapp') && (
              <TextField
                label="Staff Phone Number"
                fullWidth
                value={staffPhone}
                onChange={(e) => setStaffPhone(e.target.value)}
                placeholder="+27 ************"
                helperText="Required for SMS, voice calls, and WhatsApp"
                required={selectedNotificationTypes.some(type => type === 'sms' || type === 'voice' || type === 'whatsapp')}
                sx={{ mb: 2 }}
              />
            )}

            {selectedNotificationTypes.includes('email') && (
              <TextField
                label="Staff Email Address"
                fullWidth
                value={staffEmail}
                onChange={(e) => setStaffEmail(e.target.value)}
                placeholder="<EMAIL>"
                helperText="Required for email notifications"
                required={selectedNotificationTypes.includes('email')}
                sx={{ mb: 2 }}
              />
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setAlertDialogOpen(false)} color="inherit">
            Cancel
          </CustomButton>
          <CustomButton
            onClick={handleSendAlert}
            color="primary"
            variant="contained"
            disabled={!staffName ||
              (selectedNotificationTypes.some(type => type === 'sms' || type === 'voice' || type === 'whatsapp') && !staffPhone) ||
              (selectedNotificationTypes.includes('email') && !staffEmail) ||
              isSending}
            startIcon={isSending ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          >
            {isSending ? 'Sending...' : 'Send Alert'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BirthPredictions;
