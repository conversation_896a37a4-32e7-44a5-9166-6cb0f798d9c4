const { executeQuery } = require('../config/db');
const bcrypt = require('bcryptjs');
const logger = require('../utils/logger');

/**
 * User model for authentication and user management
 */
class User {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} - Created user
   */
  static async create(userData) {
    try {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // SQL query to insert user
      const query = `
        INSERT INTO Users (username, email, password, first_name, last_name, role, created_at)
        OUTPUT INSERTED.id, INSERTED.username, INSERTED.email, INSERTED.first_name, 
               INSERTED.last_name, INSERTED.role, INSERTED.created_at
        VALUES (@username, @email, @password, @firstName, @lastName, @role, GETDATE())
      `;

      // Query parameters
      const params = {
        username: userData.username,
        email: userData.email,
        password: hashedPassword,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role || 'user'
      };

      // Execute query
      const result = await executeQuery(query, params);
      
      if (result.recordset && result.recordset.length > 0) {
        // Return user without password
        const user = result.recordset[0];
        delete user.password;
        return user;
      }
      
      throw new Error('Failed to create user');
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Find user by ID
   * @param {number} id - User ID
   * @returns {Promise<Object>} - User object
   */
  static async findById(id) {
    try {
      const query = `
        SELECT id, username, email, first_name, last_name, role, created_at, updated_at
        FROM Users
        WHERE id = @id
      `;
      
      const result = await executeQuery(query, { id });
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  /**
   * Find user by username
   * @param {string} username - Username
   * @returns {Promise<Object>} - User object including password for authentication
   */
  static async findByUsername(username) {
    try {
      const query = `
        SELECT id, username, email, password, first_name, last_name, role, created_at, updated_at
        FROM Users
        WHERE username = @username
      `;
      
      const result = await executeQuery(query, { username });
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      logger.error('Error finding user by username:', error);
      throw error;
    }
  }

  /**
   * Find user by email
   * @param {string} email - Email address
   * @returns {Promise<Object>} - User object
   */
  static async findByEmail(email) {
    try {
      const query = `
        SELECT id, username, email, first_name, last_name, role, created_at, updated_at
        FROM Users
        WHERE email = @email
      `;
      
      const result = await executeQuery(query, { email });
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      return null;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Update user information
   * @param {number} id - User ID
   * @param {Object} userData - User data to update
   * @returns {Promise<Object>} - Updated user
   */
  static async update(id, userData) {
    try {
      // Build update query dynamically based on provided fields
      let updateFields = [];
      const params = { id };

      if (userData.username) {
        updateFields.push('username = @username');
        params.username = userData.username;
      }

      if (userData.email) {
        updateFields.push('email = @email');
        params.email = userData.email;
      }

      if (userData.firstName) {
        updateFields.push('first_name = @firstName');
        params.firstName = userData.firstName;
      }

      if (userData.lastName) {
        updateFields.push('last_name = @lastName');
        params.lastName = userData.lastName;
      }

      if (userData.role) {
        updateFields.push('role = @role');
        params.role = userData.role;
      }

      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(userData.password, salt);
        updateFields.push('password = @password');
        params.password = hashedPassword;
      }

      // Add updated_at timestamp
      updateFields.push('updated_at = GETDATE()');

      // If no fields to update, return user
      if (updateFields.length === 0) {
        return this.findById(id);
      }

      // Create update query
      const query = `
        UPDATE Users
        SET ${updateFields.join(', ')}
        OUTPUT INSERTED.id, INSERTED.username, INSERTED.email, INSERTED.first_name, 
               INSERTED.last_name, INSERTED.role, INSERTED.created_at, INSERTED.updated_at
        WHERE id = @id
      `;

      // Execute query
      const result = await executeQuery(query, params);
      
      if (result.recordset && result.recordset.length > 0) {
        return result.recordset[0];
      }
      
      throw new Error('Failed to update user');
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete user
   * @param {number} id - User ID
   * @returns {Promise<boolean>} - Success status
   */
  static async delete(id) {
    try {
      const query = `
        DELETE FROM Users
        WHERE id = @id
      `;
      
      const result = await executeQuery(query, { id });
      
      return result.rowsAffected && result.rowsAffected[0] > 0;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * List all users with pagination
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {Promise<Object>} - Paginated users
   */
  static async findAll(page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      
      const query = `
        SELECT id, username, email, first_name, last_name, role, created_at, updated_at
        FROM Users
        ORDER BY id
        OFFSET @offset ROWS
        FETCH NEXT @limit ROWS ONLY;

        SELECT COUNT(*) as total FROM Users;
      `;
      
      const result = await executeQuery(query, { offset, limit });
      
      if (result.recordsets && result.recordsets.length === 2) {
        const users = result.recordsets[0];
        const total = result.recordsets[1][0].total;
        
        return {
          users,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit)
          }
        };
      }
      
      throw new Error('Failed to retrieve users');
    } catch (error) {
      logger.error('Error finding all users:', error);
      throw error;
    }
  }

  /**
   * Authenticate user
   * @param {string} username - Username
   * @param {string} password - Password
   * @returns {Promise<Object>} - Authenticated user
   */
  static async authenticate(username, password) {
    try {
      // Find user by username
      const user = await this.findByUsername(username);
      
      if (!user) {
        return null;
      }
      
      // Compare passwords
      const isMatch = await bcrypt.compare(password, user.password);
      
      if (!isMatch) {
        return null;
      }
      
      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Error authenticating user:', error);
      throw error;
    }
  }
}

module.exports = User;