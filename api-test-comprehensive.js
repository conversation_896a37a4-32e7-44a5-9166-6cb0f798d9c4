/**
 * Comprehensive API Testing Script
 * Tests all API routes and identifies issues
 */

const BASE_URL = 'http://localhost:3001/api';

// Test configuration
const testConfig = {
  timeout: 10000,
  retries: 3,
  verbose: true
};

// API routes to test
const apiRoutes = {
  // Authentication routes
  auth: [
    { method: 'POST', path: '/auth/login', requiresAuth: false },
    { method: 'POST', path: '/auth/register', requiresAuth: false },
    { method: 'POST', path: '/auth/logout', requiresAuth: true },
    { method: 'GET', path: '/auth/me', requiresAuth: true },
    { method: 'POST', path: '/auth/refresh', requiresAuth: false },
    { method: 'POST', path: '/auth/forgot-password', requiresAuth: false },
    { method: 'POST', path: '/auth/reset-password', requiresAuth: false }
  ],

  // User management routes
  users: [
    { method: 'GET', path: '/users', requiresAuth: true },
    { method: 'GET', path: '/users/profile', requiresAuth: true },
    { method: 'PUT', path: '/users/profile', requiresAuth: true },
    { method: 'GET', path: '/users/permissions', requiresAuth: true },
    { method: 'GET', path: '/users/stats', requiresAuth: true },
    { method: 'POST', path: '/users', requiresAuth: true },
    { method: 'PUT', path: '/users/1', requiresAuth: true }
  ],

  // Animal management routes
  animals: [
    { method: 'GET', path: '/animals', requiresAuth: true },
    { method: 'GET', path: '/animals/stats', requiresAuth: true },
    { method: 'POST', path: '/animals', requiresAuth: true },
    { method: 'GET', path: '/animals/1', requiresAuth: true },
    { method: 'PUT', path: '/animals/1', requiresAuth: true },
    { method: 'DELETE', path: '/animals/1', requiresAuth: true },
    { method: 'GET', path: '/animals/search', requiresAuth: true }
  ],

  // Health management routes
  health: [
    { method: 'GET', path: '/health', requiresAuth: true },
    { method: 'GET', path: '/health/records', requiresAuth: true },
    { method: 'POST', path: '/health/records', requiresAuth: true },
    { method: 'GET', path: '/health/vaccinations', requiresAuth: true },
    { method: 'POST', path: '/health/vaccinations', requiresAuth: true },
    { method: 'GET', path: '/health/stats', requiresAuth: true }
  ],

  // Breeding management routes
  breeding: [
    { method: 'GET', path: '/breeding', requiresAuth: true },
    { method: 'GET', path: '/breeding/records', requiresAuth: true },
    { method: 'POST', path: '/breeding/records', requiresAuth: true },
    { method: 'GET', path: '/breeding/births', requiresAuth: true },
    { method: 'POST', path: '/breeding/births', requiresAuth: true },
    { method: 'GET', path: '/breeding/heat', requiresAuth: true },
    { method: 'GET', path: '/breeding/stats', requiresAuth: true }
  ],

  // Financial management routes
  financial: [
    { method: 'GET', path: '/financial', requiresAuth: true },
    { method: 'GET', path: '/financial/transactions', requiresAuth: true },
    { method: 'POST', path: '/financial/transactions', requiresAuth: true },
    { method: 'GET', path: '/financial/budgets', requiresAuth: true },
    { method: 'POST', path: '/financial/budgets', requiresAuth: true },
    { method: 'GET', path: '/financial/stats', requiresAuth: true }
  ],

  // Feeding management routes
  feeding: [
    { method: 'GET', path: '/feeding', requiresAuth: true },
    { method: 'GET', path: '/feeding/records', requiresAuth: true },
    { method: 'POST', path: '/feeding/records', requiresAuth: true },
    { method: 'GET', path: '/feeding/schedules', requiresAuth: true },
    { method: 'POST', path: '/feeding/schedules', requiresAuth: true },
    { method: 'GET', path: '/feeding/stats', requiresAuth: true }
  ],

  // Inventory management routes
  inventory: [
    { method: 'GET', path: '/inventory', requiresAuth: true },
    { method: 'GET', path: '/inventory/items', requiresAuth: true },
    { method: 'POST', path: '/inventory/items', requiresAuth: true },
    { method: 'GET', path: '/inventory/transactions', requiresAuth: true },
    { method: 'POST', path: '/inventory/transactions', requiresAuth: true },
    { method: 'GET', path: '/inventory/suppliers', requiresAuth: true },
    { method: 'GET', path: '/inventory/stats', requiresAuth: true }
  ],

  // Business analysis routes
  business: [
    { method: 'GET', path: '/business', requiresAuth: true },
    { method: 'GET', path: '/business/kpis', requiresAuth: true },
    { method: 'GET', path: '/business/predictions', requiresAuth: true },
    { method: 'GET', path: '/business/insights', requiresAuth: true },
    { method: 'GET', path: '/business/risks', requiresAuth: true },
    { method: 'GET', path: '/business/opportunities', requiresAuth: true },
    { method: 'GET', path: '/business/strategy', requiresAuth: true },
    { method: 'GET', path: '/business/swot', requiresAuth: true },
    { method: 'GET', path: '/business/scenarios', requiresAuth: true },
    { method: 'GET', path: '/business/distribution', requiresAuth: true },
    { method: 'GET', path: '/business/initiatives', requiresAuth: true },
    { method: 'GET', path: '/business/goals', requiresAuth: true }
  ],

  // Reports routes
  reports: [
    { method: 'GET', path: '/reports', requiresAuth: true },
    { method: 'GET', path: '/reports/generate', requiresAuth: true },
    { method: 'POST', path: '/reports/generate', requiresAuth: true },
    { method: 'GET', path: '/reports/templates', requiresAuth: true },
    { method: 'GET', path: '/reports/stats', requiresAuth: true }
  ],

  // Resources routes
  resources: [
    { method: 'GET', path: '/resources', requiresAuth: true },
    { method: 'GET', path: '/resources/equipment', requiresAuth: true },
    { method: 'GET', path: '/resources/staff', requiresAuth: true },
    { method: 'GET', path: '/resources/suppliers', requiresAuth: true },
    { method: 'GET', path: '/resources/stats', requiresAuth: true }
  ],

  // Compliance routes
  compliance: [
    { method: 'GET', path: '/compliance', requiresAuth: true },
    { method: 'GET', path: '/compliance/documents', requiresAuth: true },
    { method: 'GET', path: '/compliance/inspections', requiresAuth: true },
    { method: 'GET', path: '/compliance/certifications', requiresAuth: true },
    { method: 'GET', path: '/compliance/stats', requiresAuth: true }
  ],

  // Subscription routes
  subscription: [
    { method: 'GET', path: '/subscription', requiresAuth: true },
    { method: 'GET', path: '/subscription/plans', requiresAuth: false },
    { method: 'POST', path: '/subscription/subscribe', requiresAuth: true },
    { method: 'POST', path: '/subscription/cancel', requiresAuth: true },
    { method: 'GET', path: '/subscription/status', requiresAuth: true }
  ]
};

// Test results storage
const testResults = {
  passed: [],
  failed: [],
  errors: [],
  summary: {}
};

// Helper function to make HTTP requests
async function makeRequest(method, url, data = null, headers = {}) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.json().catch(() => ({}));
    
    return {
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      headers: response.headers,
      ok: response.ok
    };
  } catch (error) {
    throw new Error(`Network error: ${error.message}`);
  }
}

// Test a single route
async function testRoute(category, route) {
  const url = `${BASE_URL}${route.path}`;
  const testName = `${route.method} ${route.path}`;
  
  try {
    console.log(`Testing: ${testName}`);
    
    // For routes that require auth, we'll test without auth first to see if they properly reject
    const headers = {};
    
    // Add sample data for POST/PUT requests
    let testData = null;
    if (route.method === 'POST' || route.method === 'PUT') {
      testData = getSampleData(category, route.path);
    }
    
    const response = await makeRequest(route.method, url, testData, headers);
    
    // Analyze response
    const result = {
      category,
      route: testName,
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      timestamp: new Date().toISOString()
    };
    
    // Determine if test passed
    if (response.ok || (route.requiresAuth && response.status === 401)) {
      testResults.passed.push(result);
      console.log(`✅ PASS: ${testName} - Status: ${response.status}`);
    } else {
      testResults.failed.push(result);
      console.log(`❌ FAIL: ${testName} - Status: ${response.status} - ${response.statusText}`);
    }
    
  } catch (error) {
    const errorResult = {
      category,
      route: testName,
      error: error.message,
      timestamp: new Date().toISOString()
    };
    
    testResults.errors.push(errorResult);
    console.log(`💥 ERROR: ${testName} - ${error.message}`);
  }
}

// Get sample data for POST/PUT requests
function getSampleData(category, path) {
  const sampleData = {
    animals: { name: 'Test Animal', breed: 'Holstein', gender: 'female', birthDate: '2023-01-01' },
    health: { animalId: '1', type: 'checkup', date: '2024-12-21', notes: 'Test health record' },
    breeding: { femaleId: '1', maleId: '2', breedingDate: '2024-12-21', method: 'natural' },
    financial: { type: 'expense', amount: 100, description: 'Test transaction', date: '2024-12-21' },
    feeding: { animalId: '1', feedType: 'hay', quantity: 10, date: '2024-12-21' },
    inventory: { name: 'Test Item', category: 'feed', quantity: 100, unit: 'kg' },
    reports: { type: 'health', format: 'pdf', dateRange: { start: '2024-01-01', end: '2024-12-21' } },
    auth: { email: '<EMAIL>', password: 'password123' },
    users: { name: 'Test User', email: '<EMAIL>', role: 'user' }
  };
  
  return sampleData[category] || {};
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting comprehensive API testing...\n');
  
  const startTime = Date.now();
  
  for (const [category, routes] of Object.entries(apiRoutes)) {
    console.log(`\n📂 Testing ${category.toUpperCase()} routes:`);
    
    for (const route of routes) {
      await testRoute(category, route);
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  const endTime = Date.now();
  const duration = (endTime - startTime) / 1000;
  
  // Generate summary
  const totalTests = testResults.passed.length + testResults.failed.length + testResults.errors.length;
  testResults.summary = {
    total: totalTests,
    passed: testResults.passed.length,
    failed: testResults.failed.length,
    errors: testResults.errors.length,
    duration: `${duration}s`,
    passRate: `${((testResults.passed.length / totalTests) * 100).toFixed(1)}%`
  };
  
  // Print summary
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Tests: ${testResults.summary.total}`);
  console.log(`✅ Passed: ${testResults.summary.passed}`);
  console.log(`❌ Failed: ${testResults.summary.failed}`);
  console.log(`💥 Errors: ${testResults.summary.errors}`);
  console.log(`⏱️  Duration: ${testResults.summary.duration}`);
  console.log(`📈 Pass Rate: ${testResults.summary.passRate}`);
  
  // Print failed tests
  if (testResults.failed.length > 0) {
    console.log('\n❌ FAILED TESTS:');
    testResults.failed.forEach(test => {
      console.log(`  - ${test.route} (${test.status}: ${test.statusText})`);
    });
  }
  
  // Print error tests
  if (testResults.errors.length > 0) {
    console.log('\n💥 ERROR TESTS:');
    testResults.errors.forEach(test => {
      console.log(`  - ${test.route} (${test.error})`);
    });
  }
  
  return testResults;
}

// Export for use in Node.js or browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testResults, apiRoutes };
} else {
  // Browser environment
  window.apiTester = { runAllTests, testResults, apiRoutes };
}

// Auto-run if this script is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests().then(results => {
    console.log('\n✅ Testing complete!');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 Testing failed:', error);
    process.exit(1);
  });
}
