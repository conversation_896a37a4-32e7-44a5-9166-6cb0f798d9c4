const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { isAdmin, isManagerOrAdmin } = require('../middleware/permissions');
const mongoUserController = require('../controllers/mongoUserController');

module.exports = (useMockData = false) => {
  const router = express.Router();

  // Protected routes
  router.get('/profile', authenticateToken, mongoUserController.getUserById);

  // Admin only routes
  router.post('/', authenticateToken, isAdmin, mongoUserController.createUser);
  router.get('/', authenticateToken, isManagerOrAdmin, mongoUserController.getAllUsers);
  router.get('/username/:username', authenticateToken, isManagerOrAdmin, mongoUserController.getUserByUsername);
  router.get('/:id', authenticateToken, isManagerOrAdmin, mongoUserController.getUserById);
  router.put('/:id', authenticateToken, isAdmin, mongoUserController.updateUser);
  router.delete('/:id', authenticateToken, isAdmin, mongoUserController.deleteUser);

  return router;
};
