/**
 * Supplier Model
 *
 * Stores information about agricultural suppliers integrated from their websites.
 */

const mongoose = require('mongoose');

const SupplierContactSchema = new mongoose.Schema({
  type: {
    type: String,
    enum: ['phone', 'email', 'website', 'address', 'social'],
    required: true
  },
  value: {
    type: String,
    required: true
  },
  label: {
    type: String
  }
});

const SupplierLocationSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  address: {
    type: String,
    required: true
  },
  coordinates: {
    latitude: Number,
    longitude: Number
  },
  phone: String,
  email: String,
  hours: String
});

const SupplierSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  name: {
    type: String,
    required: true
  },
  logo: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  shortDescription: {
    type: String
  },
  categories: [{
    type: String,
    enum: [
      'livestock-feed',
      'equipment',
      'seeds',
      'fertilizer',
      'chemicals',
      'irrigation',
      'animal-health',
      'fuel',
      'retail',
      'financial',
      'insurance',
      'other'
    ]
  }],
  contacts: [SupplierContactSchema],
  locations: [SupplierLocationSchema],
  website: {
    type: String,
    required: true
  },
  services: [String],
  established: Number,
  coverImage: String,
  rating: {
    type: Number,
    min: 0,
    max: 5
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  featuredProducts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SupplierProduct'
  }]
}, {
  timestamps: true
});

// Add text index for search functionality
SupplierSchema.index({
  name: 'text',
  description: 'text',
  shortDescription: 'text',
  'services': 'text'
});

// Check if the model already exists to prevent "Cannot overwrite model once compiled" error
module.exports = mongoose.models.Supplier || mongoose.model('Supplier', SupplierSchema);
