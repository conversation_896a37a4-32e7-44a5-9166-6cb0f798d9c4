/**
 * Service Registry - A simple service locator pattern implementation
 * This helps avoid circular dependencies by providing a central registry for services
 */

type ServiceConstructor<T> = new (...args: any[]) => T;

class ServiceRegistry {
  private static instance: ServiceRegistry;
  private services: Map<string, any> = new Map();

  private constructor() {}

  public static getInstance(): ServiceRegistry {
    if (!ServiceRegistry.instance) {
      ServiceRegistry.instance = new ServiceRegistry();
    }
    return ServiceRegistry.instance;
  }

  /**
   * Register a service instance with the registry
   * @param serviceKey Unique identifier for the service
   * @param serviceInstance The service instance
   */
  public register<T>(serviceKey: string, serviceInstance: T): void {
    if (this.services.has(serviceKey)) {
      console.warn(`Service with key ${serviceKey} is already registered. Overwriting.`);
    }
    this.services.set(serviceKey, serviceInstance);
  }

  /**
   * Get a service instance from the registry
   * @param serviceKey Unique identifier for the service
   * @returns The service instance or null if not found
   */
  public get<T>(serviceKey: string): T | null {
    const service = this.services.get(serviceKey) as T;
    if (!service) {
      console.warn(`Service with key ${serviceKey} not found in registry`);
      return null;
    }
    return service;
  }

  /**
   * Register a service class with the registry and create a singleton instance
   * @param serviceKey Unique identifier for the service
   * @param ServiceClass The service class constructor
   * @param args Arguments to pass to the constructor
   * @returns The created service instance
   */
  public registerClass<T>(
    serviceKey: string, 
    ServiceClass: ServiceConstructor<T>,
    ...args: any[]
  ): T {
    if (this.services.has(serviceKey)) {
      return this.services.get(serviceKey) as T;
    }
    
    const instance = new ServiceClass(...args);
    this.services.set(serviceKey, instance);
    return instance;
  }

  /**
   * Check if a service is registered
   * @param serviceKey Unique identifier for the service
   * @returns True if the service is registered
   */
  public has(serviceKey: string): boolean {
    return this.services.has(serviceKey);
  }

  /**
   * Remove a service from the registry
   * @param serviceKey Unique identifier for the service
   */
  public unregister(serviceKey: string): void {
    this.services.delete(serviceKey);
  }

  /**
   * Clear all services from the registry
   */
  public clear(): void {
    this.services.clear();
  }
}

export default ServiceRegistry.getInstance();
