/**
 * This CSS file overrides the MUI Button styles to fix the "Cannot read properties of undefined (reading 'dark')" error.
 * It provides direct CSS styles for buttons without relying on the theme.
 */

/* Base button styles */
.MuiButton-root {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  outline: 0;
  border: 0;
  margin: 0;
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.75;
  letter-spacing: 0.02857em;
  text-transform: uppercase;
  min-width: 64px;
  padding: 6px 16px;
  border-radius: 4px;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: rgba(0, 0, 0, 0.87);
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
}

/* Contained button styles */
.MuiButton-contained {
  color: #fff;
  background-color: #3AA99F;
  box-shadow: 0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12);
}

.MuiButton-contained:hover {
  background-color: #2A8A82;
  box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
}

.MuiButton-containedPrimary {
  color: #fff;
  background-color: #3AA99F;
}

.MuiButton-containedPrimary:hover {
  background-color: #2A8A82;
}

.MuiButton-containedSecondary {
  color: #fff;
  background-color: #38B2AC;
}

.MuiButton-containedSecondary:hover {
  background-color: #2C8A84;
}

/* Outlined button styles */
.MuiButton-outlined {
  border: 1px solid rgba(0, 0, 0, 0.23);
  padding: 5px 15px;
  color: #3AA99F;
  background-color: transparent;
}

.MuiButton-outlined:hover {
  background-color: rgba(58, 169, 159, 0.04);
  border: 1px solid #3AA99F;
}

.MuiButton-outlinedPrimary {
  color: #3AA99F;
  border: 1px solid rgba(58, 169, 159, 0.5);
}

.MuiButton-outlinedPrimary:hover {
  border: 1px solid #3AA99F;
  background-color: rgba(58, 169, 159, 0.04);
}

.MuiButton-outlinedSecondary {
  color: #38B2AC;
  border: 1px solid rgba(56, 178, 172, 0.5);
}

.MuiButton-outlinedSecondary:hover {
  border: 1px solid #38B2AC;
  background-color: rgba(56, 178, 172, 0.04);
}

/* Text button styles */
.MuiButton-text {
  padding: 6px 8px;
  color: #3AA99F;
  background-color: transparent;
}

.MuiButton-text:hover {
  background-color: rgba(58, 169, 159, 0.04);
}

.MuiButton-textPrimary {
  color: #3AA99F;
}

.MuiButton-textPrimary:hover {
  background-color: rgba(58, 169, 159, 0.04);
}

.MuiButton-textSecondary {
  color: #38B2AC;
}

.MuiButton-textSecondary:hover {
  background-color: rgba(56, 178, 172, 0.04);
}

/* Disabled button styles */
.MuiButton-root.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
  cursor: default;
  pointer-events: none;
}

.MuiButton-contained.Mui-disabled {
  color: rgba(0, 0, 0, 0.26);
  background-color: rgba(0, 0, 0, 0.12);
  box-shadow: none;
}

.MuiButton-outlined.Mui-disabled {
  border: 1px solid rgba(0, 0, 0, 0.12);
}

/* Size variants */
.MuiButton-sizeSmall {
  padding: 4px 10px;
  font-size: 0.8125rem;
}

.MuiButton-sizeLarge {
  padding: 8px 22px;
  font-size: 0.9375rem;
}

/* Full width */
.MuiButton-fullWidth {
  width: 100%;
}

/* Button label */
.MuiButton-label {
  width: 100%;
  display: inherit;
  align-items: inherit;
  justify-content: inherit;
}

/* Start and end icons */
.MuiButton-startIcon {
  display: inherit;
  margin-right: 8px;
  margin-left: -4px;
}

.MuiButton-startIcon.MuiButton-iconSizeSmall {
  margin-left: -2px;
}

.MuiButton-endIcon {
  display: inherit;
  margin-right: -4px;
  margin-left: 8px;
}

.MuiButton-endIcon.MuiButton-iconSizeSmall {
  margin-right: -2px;
}

.MuiButton-iconSizeSmall > *:first-child {
  font-size: 18px;
}

.MuiButton-iconSizeMedium > *:first-child {
  font-size: 20px;
}

.MuiButton-iconSizeLarge > *:first-child {
  font-size: 22px;
}
