const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * User Permission Schema
 * Defines the structure for user permissions in MongoDB
 */
const userPermissionSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true
  },
  username: {
    type: String,
    required: true
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'staff', 'veterinarian', 'viewer'],
    default: 'viewer'
  },
  permissions: {
    dashboard: {
      view: { type: Boolean, default: true }
    },
    animals: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    health: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    breeding: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    feeding: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    commercial: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    financial: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      export: { type: Boolean, default: false }
    },
    settings: {
      view: { type: Boolean, default: false },
      edit: { type: Boolean, default: false }
    },
    users: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
userPermissionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create default permissions based on role
userPermissionSchema.methods.setDefaultPermissionsByRole = function() {
  switch (this.role) {
    case 'admin':
      // Admin has all permissions
      Object.keys(this.permissions).forEach(module => {
        Object.keys(this.permissions[module]).forEach(action => {
          this.permissions[module][action] = true;
        });
      });
      break;
    case 'manager':
      // Manager has most permissions except user management and settings
      Object.keys(this.permissions).forEach(module => {
        if (module !== 'users' && module !== 'settings') {
          Object.keys(this.permissions[module]).forEach(action => {
            this.permissions[module][action] = true;
          });
        }
      });
      // Allow view access to settings
      this.permissions.settings.view = true;
      break;
    case 'staff':
      // Staff has view and create permissions for most modules
      Object.keys(this.permissions).forEach(module => {
        if (module !== 'users' && module !== 'settings' && module !== 'financial') {
          this.permissions[module].view = true;
          this.permissions[module].create = true;
          if (this.permissions[module].edit !== undefined) {
            this.permissions[module].edit = false;
          }
          if (this.permissions[module].delete !== undefined) {
            this.permissions[module].delete = false;
          }
        }
      });
      break;
    case 'veterinarian':
      // Veterinarian has full access to health and view access to animals
      this.permissions.health.view = true;
      this.permissions.health.create = true;
      this.permissions.health.edit = true;
      this.permissions.health.delete = true;
      
      this.permissions.animals.view = true;
      this.permissions.animals.create = false;
      this.permissions.animals.edit = true;
      this.permissions.animals.delete = false;
      
      this.permissions.breeding.view = true;
      break;
    case 'viewer':
    default:
      // Viewer has only view permissions
      Object.keys(this.permissions).forEach(module => {
        Object.keys(this.permissions[module]).forEach(action => {
          this.permissions[module][action] = action === 'view';
        });
      });
      break;
  }
};

// Static method to find permissions by userId
userPermissionSchema.statics.findByUserId = async function(userId) {
  try {
    return await this.findOne({ userId });
  } catch (error) {
    logger.error('Error finding user permissions by userId:', error);
    throw error;
  }
};

// Static method to create or update permissions
userPermissionSchema.statics.createOrUpdate = async function(permissionData) {
  try {
    const { userId } = permissionData;
    
    // Check if permissions exist
    const existingPermissions = await this.findOne({ userId });
    
    if (existingPermissions) {
      // Update existing permissions
      Object.assign(existingPermissions, permissionData);
      return await existingPermissions.save();
    } else {
      // Create new permissions
      const newPermissions = new this(permissionData);
      
      // Set default permissions based on role
      newPermissions.setDefaultPermissionsByRole();
      
      return await newPermissions.save();
    }
  } catch (error) {
    logger.error('Error creating or updating user permissions:', error);
    throw error;
  }
};

// Create the model
const UserPermission = mongoose.model('UserPermission', userPermissionSchema);

module.exports = UserPermission;
