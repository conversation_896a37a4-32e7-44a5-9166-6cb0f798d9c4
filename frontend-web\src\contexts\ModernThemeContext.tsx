import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider, CssBaseline, GlobalStyles } from '@mui/material';
import { createModernTheme, colors, gradients } from '../theme/modernTheme';

interface ModernThemeContextType {
  mode: 'light' | 'dark';
  toggleMode: () => void;
  accentColor: string;
  setAccentColor: (color: string) => void;
  gradientStyle: string;
  setGradientStyle: (gradient: string) => void;
  animationsEnabled: boolean;
  setAnimationsEnabled: (enabled: boolean) => void;
}

const ModernThemeContext = createContext<ModernThemeContextType | undefined>(undefined);

export const useModernTheme = () => {
  const context = useContext(ModernThemeContext);
  if (!context) {
    throw new Error('useModernTheme must be used within a ModernThemeProvider');
  }
  return context;
};

interface ModernThemeProviderProps {
  children: ReactNode;
}

export const ModernThemeProvider: React.FC<ModernThemeProviderProps> = ({ children }) => {
  const [mode, setMode] = useState<'light' | 'dark'>('light');
  const [accentColor, setAccentColor] = useState(colors.primary[500]);
  const [gradientStyle, setGradientStyle] = useState(gradients.primary);
  const [animationsEnabled, setAnimationsEnabled] = useState(true);

  // Load theme preferences from localStorage
  useEffect(() => {
    const savedMode = localStorage.getItem('agriintel-theme-mode') as 'light' | 'dark';
    const savedAccentColor = localStorage.getItem('agriintel-accent-color');
    const savedGradient = localStorage.getItem('agriintel-gradient');
    const savedAnimations = localStorage.getItem('agriintel-animations');

    if (savedMode) setMode(savedMode);
    if (savedAccentColor) setAccentColor(savedAccentColor);
    if (savedGradient) setGradientStyle(savedGradient);
    if (savedAnimations) setAnimationsEnabled(savedAnimations === 'true');
  }, []);

  // Save theme preferences to localStorage
  useEffect(() => {
    localStorage.setItem('agriintel-theme-mode', mode);
    localStorage.setItem('agriintel-accent-color', accentColor);
    localStorage.setItem('agriintel-gradient', gradientStyle);
    localStorage.setItem('agriintel-animations', animationsEnabled.toString());
  }, [mode, accentColor, gradientStyle, animationsEnabled]);

  const toggleMode = () => {
    setMode(prev => prev === 'light' ? 'dark' : 'light');
  };

  const theme = createModernTheme(mode);

  // Global styles for modern design
  const globalStyles = (
    <GlobalStyles
      styles={{
        '*': {
          boxSizing: 'border-box',
        },
        html: {
          WebkitFontSmoothing: 'antialiased',
          MozOsxFontSmoothing: 'grayscale',
          scrollBehavior: 'smooth',
        },
        body: {
          margin: 0,
          padding: 0,
          fontFamily: theme.typography.fontFamily,
          backgroundColor: theme.palette.background.default,
          color: theme.palette.text.primary,
          lineHeight: 1.6,
          overflowX: 'hidden',
        },
        '#root': {
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
        },
        // Custom scrollbar
        '::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '::-webkit-scrollbar-track': {
          background: theme.palette.background.paper,
          borderRadius: '4px',
        },
        '::-webkit-scrollbar-thumb': {
          background: `linear-gradient(135deg, ${colors.primary[400]}, ${colors.primary[600]})`,
          borderRadius: '4px',
          '&:hover': {
            background: `linear-gradient(135deg, ${colors.primary[500]}, ${colors.primary[700]})`,
          },
        },
        // Selection styles
        '::selection': {
          backgroundColor: colors.primary[200],
          color: colors.primary[900],
        },
        '::-moz-selection': {
          backgroundColor: colors.primary[200],
          color: colors.primary[900],
        },
        // Focus styles
        '*:focus-visible': {
          outline: `2px solid ${colors.primary[500]}`,
          outlineOffset: '2px',
          borderRadius: '4px',
        },
        // Animation utilities
        '.animate-fade-in': {
          animation: animationsEnabled ? 'fadeIn 0.6s ease-out' : 'none',
        },
        '.animate-slide-up': {
          animation: animationsEnabled ? 'slideUp 0.5s ease-out' : 'none',
        },
        '.animate-scale-in': {
          animation: animationsEnabled ? 'scaleIn 0.4s ease-out' : 'none',
        },
        // Keyframes
        '@keyframes fadeIn': {
          from: { opacity: 0, transform: 'translateY(20px)' },
          to: { opacity: 1, transform: 'translateY(0)' },
        },
        '@keyframes slideUp': {
          from: { opacity: 0, transform: 'translateY(30px)' },
          to: { opacity: 1, transform: 'translateY(0)' },
        },
        '@keyframes scaleIn': {
          from: { opacity: 0, transform: 'scale(0.9)' },
          to: { opacity: 1, transform: 'scale(1)' },
        },
        '@keyframes float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
        '@keyframes pulse': {
          '0%, 100%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(1.05)' },
        },
        // Utility classes
        '.gradient-text': {
          background: gradientStyle,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        },
        '.glass-effect': {
          background: `rgba(255, 255, 255, ${mode === 'light' ? 0.1 : 0.05})`,
          backdropFilter: 'blur(20px)',
          border: `1px solid rgba(255, 255, 255, ${mode === 'light' ? 0.2 : 0.1})`,
        },
        '.hover-lift': {
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: animationsEnabled ? 'translateY(-4px)' : 'none',
            boxShadow: `0 12px 40px rgba(0, 0, 0, ${mode === 'light' ? 0.15 : 0.3})`,
          },
        },
        '.hover-scale': {
          transition: 'transform 0.2s ease',
          '&:hover': {
            transform: animationsEnabled ? 'scale(1.02)' : 'none',
          },
        },
        // Farm-themed backgrounds
        '.farm-pattern': {
          backgroundImage: `
            radial-gradient(circle at 20% 80%, ${colors.farm.grass}20 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, ${colors.farm.sky}20 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, ${colors.primary[500]}10 0%, transparent 50%)
          `,
        },
        '.livestock-pattern': {
          backgroundImage: `
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='${colors.primary[500].replace('#', '%23')}' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
          `,
        },
        // Responsive design helpers
        '@media (max-width: 768px)': {
          '.hide-mobile': {
            display: 'none !important',
          },
          '.mobile-full-width': {
            width: '100% !important',
          },
        },
        '@media (min-width: 769px)': {
          '.hide-desktop': {
            display: 'none !important',
          },
        },
        // Print styles
        '@media print': {
          '*': {
            background: 'white !important',
            color: 'black !important',
            boxShadow: 'none !important',
          },
          '.no-print': {
            display: 'none !important',
          },
        },
        // High contrast mode
        '@media (prefers-contrast: high)': {
          '*': {
            borderColor: `${theme.palette.text.primary} !important`,
          },
        },
        // Reduced motion
        '@media (prefers-reduced-motion: reduce)': {
          '*': {
            animationDuration: '0.01ms !important',
            animationIterationCount: '1 !important',
            transitionDuration: '0.01ms !important',
          },
        },
        // Dark mode specific styles
        ...(mode === 'dark' && {
          '.dark-mode-shadow': {
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
          },
          '.dark-mode-border': {
            borderColor: `${colors.neutral[700]} !important`,
          },
        }),
        // Light mode specific styles
        ...(mode === 'light' && {
          '.light-mode-shadow': {
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
          },
          '.light-mode-border': {
            borderColor: `${colors.neutral[200]} !important`,
          },
        }),
      }}
    />
  );

  const contextValue: ModernThemeContextType = {
    mode,
    toggleMode,
    accentColor,
    setAccentColor,
    gradientStyle,
    setGradientStyle,
    animationsEnabled,
    setAnimationsEnabled,
  };

  return (
    <ModernThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        {globalStyles}
        {children}
      </ThemeProvider>
    </ModernThemeContext.Provider>
  );
};

export default ModernThemeProvider;
