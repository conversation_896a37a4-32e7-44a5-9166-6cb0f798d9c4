/**
 * Business Analytics Service
 *
 * Provides advanced business analytics and predictive insights
 * for the Business Analysis module.
 */

import axios from 'axios';
import { API_URL } from '../config/api';
import {
  BusinessKPI,
  BusinessPrediction,
  BusinessInsight,
  BusinessRisk,
  BusinessOpportunity,
  StrategicGoal,
  StrategicInitiative,
  SWOTAnalysis,
  StrategicScenario
} from '../types/businessAnalytics';

// Mock data imports (for fallback)
import {
  mockBusinessKPIs,
  mockBusinessPredictions,
  mockBusinessInsights,
  mockBusinessRisks,
  mockBusinessOpportunities,
  mockStrategicGoals,
  mockStrategicInitiatives,
  mockSWOTAnalysis,
  mockStrategicScenarios
} from '../mocks/businessAnalysisData';

// Check if mock data should be used
const useMockData = process.env.REACT_APP_USE_MOCK_DATA === 'true';

/**
 * Business Analytics Service
 */
const businessAnalyticsService = {
  /**
   * Get business analytics dashboard data
   * @returns Dashboard data including KPIs, predictions, insights, etc.
   */
  getDashboardData: async () => {
    try {
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          kpis: mockBusinessKPIs,
          predictions: mockBusinessPredictions,
          insights: mockBusinessInsights,
          risks: mockBusinessRisks,
          opportunities: mockBusinessOpportunities,
          animalDistributionData: [
            { name: 'Cattle', value: 120 },
            { name: 'Sheep', value: 200 },
            { name: 'Goats', value: 80 }
          ]
        };
      }

      // Get data from API
      const response = await axios.get(`${API_URL}/business-analysis/dashboard`);
      return response.data;
    } catch (error) {
      console.error('Error fetching business analytics dashboard data:', error);

      // Fallback to mock data
      return {
        kpis: mockBusinessKPIs,
        predictions: mockBusinessPredictions,
        insights: mockBusinessInsights,
        risks: mockBusinessRisks,
        opportunities: mockBusinessOpportunities,
        animalDistributionData: [
          { name: 'Cattle', value: 120 },
          { name: 'Sheep', value: 200 },
          { name: 'Goats', value: 80 }
        ]
      };
    }
  },

  /**
   * Get business strategy data
   * @returns Strategy data including goals, initiatives, SWOT analysis, etc.
   */
  getBusinessStrategy: async () => {
    try {
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          vision: 'To be the leading sustainable livestock farm in South Africa, recognized for excellence in animal welfare, environmental stewardship, and community impact.',
          mission: 'We are committed to producing high-quality livestock through ethical farming practices, innovative technology, and responsible resource management while supporting local communities and preserving the environment for future generations.',
          goals: mockStrategicGoals,
          initiatives: mockStrategicInitiatives,
          swot: mockSWOTAnalysis,
          opportunities: mockBusinessOpportunities,
          scenarios: mockStrategicScenarios
        };
      }

      // Get data from API
      const response = await axios.get(`${API_URL}/business-analysis/strategy`);
      return response.data;
    } catch (error) {
      console.error('Error fetching business strategy data:', error);

      // Fallback to mock data
      return {
        vision: 'To be the leading sustainable livestock farm in South Africa, recognized for excellence in animal welfare, environmental stewardship, and community impact.',
        mission: 'We are committed to producing high-quality livestock through ethical farming practices, innovative technology, and responsible resource management while supporting local communities and preserving the environment for future generations.',
        goals: mockStrategicGoals,
        initiatives: mockStrategicInitiatives,
        swot: mockSWOTAnalysis,
        opportunities: mockBusinessOpportunities,
        scenarios: mockStrategicScenarios
      };
    }
  },

  /**
   * Get predictive insights
   * @param category Optional category filter
   * @returns List of business predictions
   */
  getPredictiveInsights: async (category?: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding' | 'all'): Promise<BusinessPrediction[]> => {
    try {
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter by category if provided
        if (category && category !== 'all') {
          return mockBusinessPredictions.filter(prediction => prediction.category === category);
        }

        return mockBusinessPredictions;
      }

      // Get data from API
      const url = category && category !== 'all'
        ? `${API_URL}/business-analysis/predictions?category=${category}`
        : `${API_URL}/business-analysis/predictions`;

      const response = await axios.get(url);
      return response.data;
    } catch (error) {
      console.error('Error fetching predictive insights:', error);

      // Fallback to mock data
      if (category && category !== 'all') {
        return mockBusinessPredictions.filter(prediction => prediction.category === category);
      }

      return mockBusinessPredictions;
    }
  },

  /**
   * Generate predictive analysis report
   * @param data Report data
   * @param format Report format (pdf, excel, csv)
   * @returns Report URL
   */
  generatePredictiveReport: async (data: any, format: 'pdf' | 'excel' | 'csv' = 'pdf'): Promise<string> => {
    try {
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Return mock report URL
        return `/reports/predictive-analysis-${Date.now()}.${format}`;
      }

      // Generate report via API
      const response = await axios.post(`${API_URL}/business-analysis/reports/predictive`, {
        data,
        format
      });

      return response.data.reportUrl;
    } catch (error) {
      console.error('Error generating predictive report:', error);
      throw new Error('Failed to generate predictive report');
    }
  },

  /**
   * Get business insights
   * @param category Optional category filter
   * @param impact Optional impact filter
   * @returns List of business insights
   */
  getBusinessInsights: async (category?: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding' | 'all', impact?: 'high' | 'medium' | 'low' | 'all'): Promise<BusinessInsight[]> => {
    try {
      if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter by category and impact if provided
        let filteredInsights = [...mockBusinessInsights];

        if (category && category !== 'all') {
          filteredInsights = filteredInsights.filter(insight => insight.category === category);
        }

        if (impact && impact !== 'all') {
          filteredInsights = filteredInsights.filter(insight => insight.impact === impact);
        }

        return filteredInsights;
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (category && category !== 'all') params.append('category', category);
      if (impact && impact !== 'all') params.append('impact', impact);

      // Get data from API
      const url = `${API_URL}/business-analysis/insights${params.toString() ? `?${params.toString()}` : ''}`;
      const response = await axios.get(url);

      return response.data;
    } catch (error) {
      console.error('Error fetching business insights:', error);

      // Fallback to mock data
      let filteredInsights = [...mockBusinessInsights];

      if (category && category !== 'all') {
        filteredInsights = filteredInsights.filter(insight => insight.category === category);
      }

      if (impact && impact !== 'all') {
        filteredInsights = filteredInsights.filter(insight => insight.impact === impact);
      }

      return filteredInsights;
    }
  }
};

export default businessAnalyticsService;
