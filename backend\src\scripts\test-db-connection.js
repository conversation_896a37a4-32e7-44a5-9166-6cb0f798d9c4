/**
 * Test MongoDB Connection Script
 * 
 * This script tests the MongoDB connection and prints the database name
 * to verify that the correct database is being used.
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');
const logger = require('../utils/logger');

// MongoDB connection string
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'AMPD_Live_Stock';

console.log('Environment variables:');
console.log('MONGODB_URI:', process.env.MONGODB_URI ? process.env.MONGODB_URI.replace(/:[^:]*@/, ':****@') : 'Not set');
console.log('MONGODB_DB_NAME:', process.env.MONGODB_DB_NAME || 'Not set');
console.log('Using database name:', dbName);

async function testConnection() {
  console.log('Testing MongoDB connection...');
  console.log(`Using connection string: ${uri.replace(/:[^:]*@/, ':****@')}`);
  console.log(`Using database: ${dbName}`);
  
  const client = new MongoClient(uri, {
    connectTimeoutMS: 30000,
    socketTimeoutMS: 45000,
    retryWrites: true,
    w: 'majority',
    maxPoolSize: 10
  });
  
  try {
    await client.connect();
    console.log('Successfully connected to MongoDB!');
    
    const db = client.db(dbName);
    console.log(`Connected to database: ${db.databaseName}`);
    
    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    console.log('Database ping successful!');
    
    // List collections
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections in database ${db.databaseName}`);
    
    // Log collection names
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    return true;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    return false;
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the test
testConnection()
  .then(success => {
    console.log('Test completed with result:', success ? 'SUCCESS' : 'FAILURE');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
