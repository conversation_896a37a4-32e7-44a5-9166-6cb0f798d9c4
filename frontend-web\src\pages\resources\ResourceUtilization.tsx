import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress
} from '@mui/material';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Mock resource utilization data
const resourceUtilizationData = [
  { month: 'Jan', equipment: 65, vehicles: 45, buildings: 85, land: 90 },
  { month: 'Feb', equipment: 70, vehicles: 50, buildings: 85, land: 90 },
  { month: 'Mar', equipment: 75, vehicles: 55, buildings: 85, land: 90 },
  { month: 'Apr', equipment: 80, vehicles: 60, buildings: 85, land: 90 },
  { month: 'May', equipment: 85, vehicles: 65, buildings: 85, land: 90 },
  { month: 'Jun', equipment: 90, vehicles: 70, buildings: 85, land: 90 },
];

// Mock resource efficiency data
const resourceEfficiencyData = [
  { name: 'Tractor #1', utilization: 85, efficiency: 78, availability: 92 },
  { name: 'Tractor #2', utilization: 75, efficiency: 82, availability: 88 },
  { name: 'Feed Mixer', utilization: 60, efficiency: 90, availability: 95 },
  { name: 'Milking Machine', utilization: 95, efficiency: 88, availability: 90 },
  { name: 'Water Pump', utilization: 70, efficiency: 85, availability: 98 },
  { name: 'Barn A', utilization: 90, efficiency: 75, availability: 100 },
  { name: 'Storage Facility', utilization: 65, efficiency: 80, availability: 100 },
  { name: 'North Pasture', utilization: 95, efficiency: 90, availability: 100 },
];

// Mock resource allocation data
const resourceAllocationData = [
  { name: 'Dairy Operations', value: 45 },
  { name: 'Crop Production', value: 25 },
  { name: 'Breeding Program', value: 15 },
  { name: 'Maintenance', value: 10 },
  { name: 'Administrative', value: 5 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const ResourceUtilization: React.FC = () => {
  const [timeFrame, setTimeFrame] = useState('monthly');
  const [resourceType, setResourceType] = useState('all');

  const handleTimeFrameChange = (event: SelectChangeEvent) => {
    setTimeFrame(event.target.value);
  };

  const handleResourceTypeChange = (event: SelectChangeEvent) => {
    setResourceType(event.target.value);
  };

  // Filter resource efficiency data based on resource type
  const filteredResourceData = resourceType === 'all' 
    ? resourceEfficiencyData 
    : resourceEfficiencyData.filter(resource => {
        if (resourceType === 'equipment') return resource.name.includes('Tractor') || resource.name.includes('Mixer') || resource.name.includes('Machine') || resource.name.includes('Pump');
        if (resourceType === 'buildings') return resource.name.includes('Barn') || resource.name.includes('Storage');
        if (resourceType === 'land') return resource.name.includes('Pasture');
        return true;
      });

  return (
    <Box p={4}>
      <Typography variant="h4" fontWeight="bold" mb={3}>Resource Utilization</Typography>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Time Frame</InputLabel>
            <Select
              value={timeFrame}
              label="Time Frame"
              onChange={handleTimeFrameChange}
            >
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="quarterly">Quarterly</MenuItem>
              <MenuItem value="yearly">Yearly</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Resource Type</InputLabel>
            <Select
              value={resourceType}
              label="Resource Type"
              onChange={handleResourceTypeChange}
            >
              <MenuItem value="all">All Resources</MenuItem>
              <MenuItem value="equipment">Equipment</MenuItem>
              <MenuItem value="buildings">Buildings</MenuItem>
              <MenuItem value="land">Land</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Resource Utilization Trends</Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={resourceUtilizationData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    {resourceType === 'all' || resourceType === 'equipment' ? <Bar dataKey="equipment" name="Equipment" fill="#8884d8" /> : null}
                    {resourceType === 'all' || resourceType === 'equipment' ? <Bar dataKey="vehicles" name="Vehicles" fill="#82ca9d" /> : null}
                    {resourceType === 'all' || resourceType === 'buildings' ? <Bar dataKey="buildings" name="Buildings" fill="#ffc658" /> : null}
                    {resourceType === 'all' || resourceType === 'land' ? <Bar dataKey="land" name="Land" fill="#ff8042" /> : null}
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', p: 0 }}>
            <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white' }}>
              <Typography variant="h6" fontWeight="bold">Resource Efficiency Metrics</Typography>
            </Box>
            <TableContainer>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.light' }}>
                  <TableRow>
                    <TableCell>Resource Name</TableCell>
                    <TableCell>Utilization</TableCell>
                    <TableCell>Efficiency</TableCell>
                    <TableCell>Availability</TableCell>
                    <TableCell>Overall Rating</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredResourceData.map((resource) => {
                    const overallRating = Math.round((resource.utilization + resource.efficiency + resource.availability) / 3);
                    let ratingColor = 'success';
                    if (overallRating < 70) ratingColor = 'error';
                    else if (overallRating < 85) ratingColor = 'warning';
                    
                    return (
                      <TableRow key={resource.name} hover>
                        <TableCell>{resource.name}</TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Box width="70%" mr={1}>
                              <LinearProgress 
                                variant="determinate" 
                                value={resource.utilization} 
                                color="primary"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>
                            <Box minWidth={35}>
                              <Typography variant="body2" color="text.secondary">
                                {resource.utilization}%
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Box width="70%" mr={1}>
                              <LinearProgress 
                                variant="determinate" 
                                value={resource.efficiency} 
                                color="secondary"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>
                            <Box minWidth={35}>
                              <Typography variant="body2" color="text.secondary">
                                {resource.efficiency}%
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Box width="70%" mr={1}>
                              <LinearProgress 
                                variant="determinate" 
                                value={resource.availability} 
                                color="info"
                                sx={{ height: 8, borderRadius: 4 }}
                              />
                            </Box>
                            <Box minWidth={35}>
                              <Typography variant="body2" color="text.secondary">
                                {resource.availability}%
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={`${overallRating}%`} 
                            color={ratingColor as any}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Resource Allocation</Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={resourceAllocationData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {resourceAllocationData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={3}>Resource Utilization Summary</Typography>
              <Typography variant="body1" paragraph>
                Based on the current analysis, the following insights are available:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Equipment utilization</strong> has increased by 25% over the past 6 months, indicating improved operational efficiency.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Building utilization</strong> remains consistently high at 85%, suggesting optimal use of facility space.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Land utilization</strong> is at 90%, with potential for further optimization in crop rotation and grazing patterns.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>The milking machine</strong> shows the highest utilization rate (95%) among equipment, but may benefit from efficiency improvements.
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body1" mt={3} fontWeight="bold">
                Implementing targeted improvements in resource allocation and maintenance schedules could increase overall efficiency by approximately 10%.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ResourceUtilization;
