import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, TextField, InputAdornment, IconButton, Typography, List, ListItem, ListItemIcon, ListItemText, Divider, Paper, Chip, alpha, useTheme } from '@mui/material';
import { 
  Search, 
  Close, 
  Pets, 
  LocalHospital, 
  Agriculture, 
  Restaurant,
  Assessment,
  Store,
  Gavel,
  Settings,
  TrendingUp,
  History
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { ROUTES } from '../constants/routes';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'animal' | 'health' | 'breeding' | 'feeding' | 'financial' | 'commercial' | 'report' | 'setting' | 'compliance';
  path: string;
}

interface GlobalSearchProps {
  onClose: () => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const searchInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Focus search input on mount
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
    
    // Load recent searches from localStorage
    const savedSearches = localStorage.getItem('recentSearches');
    if (savedSearches) {
      setRecentSearches(JSON.parse(savedSearches));
    }
    
    // Add event listener for Escape key
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscKey);
    
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);
  
  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    
    if (query.trim() === '') {
      setSearchResults([]);
      return;
    }
    
    setIsLoading(true);
    
    // Simulate API call with setTimeout
    setTimeout(() => {
      // Mock search results
      const results: SearchResult[] = [
        {
          id: '1',
          title: 'Cattle-2023-001',
          description: 'Nguni Cattle, Female, 3 years old',
          type: 'animal',
          path: `${ROUTES.ANIMALS}/1`
        },
        {
          id: '2',
          title: 'Vaccination Schedule',
          description: 'Upcoming vaccinations for all animals',
          type: 'health',
          path: `${ROUTES.HEALTH}/vaccinations`
        },
        {
          id: '3',
          title: 'Breeding Program',
          description: 'Active breeding pairs and schedules',
          type: 'breeding',
          path: `${ROUTES.BREEDING}/schedule`
        },
        {
          id: '4',
          title: 'Feed Inventory',
          description: 'Current feed stock and supplies',
          type: 'feeding',
          path: `${ROUTES.FEED}/inventory`
        },
        {
          id: '5',
          title: 'Financial Report',
          description: 'Monthly financial performance',
          type: 'financial',
          path: `${ROUTES.FINANCIAL}/reports`
        }
      ].filter(result => 
        result.title.toLowerCase().includes(query.toLowerCase()) || 
        result.description.toLowerCase().includes(query.toLowerCase())
      );
      
      setSearchResults(results);
      setIsLoading(false);
    }, 500);
  };
  
  // Handle result click
  const handleResultClick = (result: SearchResult) => {
    // Save to recent searches
    const updatedSearches = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5);
    setRecentSearches(updatedSearches);
    localStorage.setItem('recentSearches', JSON.stringify(updatedSearches));
    
    // Navigate to result
    navigate(result.path);
    onClose();
  };
  
  // Handle recent search click
  const handleRecentSearchClick = (search: string) => {
    setSearchQuery(search);
    handleSearch(search);
  };
  
  // Clear recent searches
  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };
  
  // Get icon for result type
  const getIconForType = (type: string) => {
    switch (type) {
      case 'animal':
        return <Pets />;
      case 'health':
        return <LocalHospital />;
      case 'breeding':
        return <Agriculture />;
      case 'feeding':
        return <Restaurant />;
      case 'financial':
        return <TrendingUp />;
      case 'commercial':
        return <Store />;
      case 'report':
        return <Assessment />;
      case 'setting':
        return <Settings />;
      case 'compliance':
        return <Gavel />;
      default:
        return <Search />;
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: alpha(theme.palette.background.default, 0.8),
        backdropFilter: 'blur(10px)',
        zIndex: theme.zIndex.drawer + 2,
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
        padding: '80px 16px 16px 16px'
      }}
    >
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        style={{
          width: '100%',
          maxWidth: '600px'
        }}
      >
        <Paper
          elevation={4}
          sx={{
            borderRadius: 2,
            overflow: 'hidden',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}
        >
          {/* Search Input */}
          <Box sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.8) }}>
            <TextField
              inputRef={searchInputRef}
              fullWidth
              placeholder={translate('common.search_placeholder')}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search color="primary" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton edge="end" onClick={onClose}>
                      <Close />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: 2,
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(theme.palette.primary.main, 0.2)
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: alpha(theme.palette.primary.main, 0.5)
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme.palette.primary.main
                  }
                }
              }}
            />
          </Box>
          
          <Divider />
          
          {/* Search Results */}
          <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
            {isLoading ? (
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  {translate('common.searching')}...
                </Typography>
              </Box>
            ) : searchQuery.trim() !== '' ? (
              searchResults.length > 0 ? (
                <List>
                  {searchResults.map((result) => (
                    <ListItem 
                      key={result.id} 
                      button 
                      onClick={() => handleResultClick(result)}
                      sx={{
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {getIconForType(result.type)}
                      </ListItemIcon>
                      <ListItemText 
                        primary={result.title}
                        secondary={result.description}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Box sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {translate('common.no_results_found')}
                  </Typography>
                </Box>
              )
            ) : recentSearches.length > 0 ? (
              <Box sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    {translate('common.recent_searches')}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color="primary" 
                    sx={{ cursor: 'pointer' }}
                    onClick={clearRecentSearches}
                  >
                    {translate('common.clear_all')}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {recentSearches.map((search, index) => (
                    <Chip
                      key={index}
                      label={search}
                      icon={<History />}
                      onClick={() => handleRecentSearchClick(search)}
                      sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.2)
                        }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            ) : (
              <Box sx={{ p: 4, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  {translate('common.start_searching')}
                </Typography>
              </Box>
            )}
          </Box>
          
          {/* Search Tips */}
          <Box sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.5) }}>
            <Typography variant="caption" color="text.secondary">
              {translate('common.search_tips')}
            </Typography>
          </Box>
        </Paper>
      </motion.div>
    </motion.div>
  );
};

export default GlobalSearch;
