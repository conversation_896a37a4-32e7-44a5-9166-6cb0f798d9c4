/**
 * Rural Notification Service for MayCaiphus Livestock Frontend
 * Designed for farms in rural or poor network areas
 */

import axios from 'axios';
import { NotificationType } from './notificationService';

// Configuration
const config = {
  // API endpoints
  api: {
    baseUrl: process.env.REACT_APP_API_URL || 'http://localhost:3005/api',
    endpoints: {
      sms: '/notifications/sms',
      voice: '/notifications/voice',
      whatsapp: '/notifications/whatsapp',
      birthAlert: '/notifications/birth-alert'
    }
  },

  // Default phone numbers (fallback)
  defaultPhones: {
    primary: '0794484159',
    additional: ['0761246783', '0828808204', '0766535887']
  },

  // Offline mode settings
  offline: {
    enabled: false,
    simulateDelay: true,
    delayMs: 1500
  }
};

/**
 * Format phone number to E.164 format
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} - Formatted phone number
 */
function formatPhoneNumber(phoneNumber: string): string {
  // Remove any non-digit characters
  let digits = phoneNumber.replace(/\D/g, '');

  // If the number starts with 0, replace it with the default country code
  if (digits.startsWith('0')) {
    digits = '+27' + digits.substring(1);
  }

  // If the number doesn't have a country code, add the default country code
  if (!digits.startsWith('+')) {
    if (!digits.startsWith('27')) {
      digits = '+27' + digits;
    } else {
      digits = '+' + digits;
    }
  }

  return digits;
}

/**
 * Simulate network delay
 * @param {number} ms - Milliseconds to delay
 * @returns {Promise<void>}
 */
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Rural Notification Service
 */
export const useRuralNotificationService = () => {
  /**
   * Send an SMS message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  const sendSMS = async (
    to: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Simulate network delay in offline mode
      if (config.offline.enabled && config.offline.simulateDelay) {
        await delay(config.offline.delayMs);
      }

      // In offline mode, simulate a successful response
      if (config.offline.enabled) {
        return {
          success: true,
          messageId: `sms-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      // Send the SMS via the API
      const response = await axios.post(`${config.api.baseUrl}${config.api.endpoints.sms}`, {
        to: formattedTo,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending SMS:', error);

      // In case of error, return a simulated success in offline mode
      if (config.offline.enabled) {
        return {
          success: true,
          messageId: `sms-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending SMS'
      };
    }
  };

  /**
   * Make a voice call
   * @param {string} to - Recipient phone number
   * @param {string} message - Message to speak
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  const makeVoiceCall = async (
    to: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'high'
  ): Promise<{ success: boolean; callId?: string; error?: string }> => {
    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Simulate network delay in offline mode
      if (config.offline.enabled && config.offline.simulateDelay) {
        await delay(config.offline.delayMs * 1.5); // Voice calls take longer
      }

      // In offline mode, simulate a successful response
      if (config.offline.enabled) {
        return {
          success: true,
          callId: `call-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      // Make the voice call via the API
      const response = await axios.post(`${config.api.baseUrl}${config.api.endpoints.voice}`, {
        to: formattedTo,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error making voice call:', error);

      // In case of error, return a simulated success in offline mode
      if (config.offline.enabled) {
        return {
          success: true,
          callId: `call-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error making voice call'
      };
    }
  };

  /**
   * Send a WhatsApp message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  const sendWhatsApp = async (
    to: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Simulate network delay in offline mode
      if (config.offline.enabled && config.offline.simulateDelay) {
        await delay(config.offline.delayMs);
      }

      // In offline mode, simulate a successful response
      if (config.offline.enabled) {
        return {
          success: true,
          messageId: `whatsapp-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      // Send the WhatsApp message via the API
      const response = await axios.post(`${config.api.baseUrl}${config.api.endpoints.whatsapp}`, {
        to: formattedTo,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);

      // In case of error, return a simulated success in offline mode
      if (config.offline.enabled) {
        return {
          success: true,
          messageId: `whatsapp-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending WhatsApp message'
      };
    }
  };

  /**
   * Send a birth prediction alert using multiple notification methods
   * @param {object} params - Alert parameters
   * @returns {Promise<Array>} - Results for each notification type
   */
  const sendBirthPredictionAlert = async ({
    animalId,
    animalName,
    daysRemaining,
    expectedDueDate,
    riskLevel,
    specialCare,
    recipient,
    notificationTypes,
    customMessage,
    priority
  }: {
    animalId: string;
    animalName: string;
    daysRemaining: number;
    expectedDueDate?: Date;
    riskLevel?: string;
    specialCare?: string[];
    recipient: {
      name?: string;
      phone?: string;
      email?: string;
    };
    notificationTypes: NotificationType[];
    customMessage?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
  }): Promise<Array<{ type: NotificationType; result: any }>> => {
    try {
      // Determine priority based on days remaining if not specified
      if (!priority) {
        if (daysRemaining <= 1) {
          priority = 'critical';
        } else if (daysRemaining <= 3) {
          priority = 'high';
        } else if (daysRemaining <= 7) {
          priority = 'medium';
        } else {
          priority = 'low';
        }
      }

      // Simulate network delay in offline mode
      if (config.offline.enabled && config.offline.simulateDelay) {
        await delay(config.offline.delayMs * 2); // Birth alerts take longer
      }

      // In offline mode, simulate a successful response
      if (config.offline.enabled) {
        const results: Array<{ type: NotificationType; result: any }> = [];

        for (const type of notificationTypes) {
          let result;

          switch (type) {
            case 'sms':
              result = {
                success: true,
                messageId: `sms-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            case 'voice':
              result = {
                success: true,
                callId: `call-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            case 'whatsapp':
              result = {
                success: true,
                messageId: `whatsapp-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            default:
              result = {
                success: true,
                messageId: `notification-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
          }

          results.push({ type, result });
        }

        return results;
      }

      // Send the birth alert via the API
      const response = await axios.post(`${config.api.baseUrl}${config.api.endpoints.birthAlert}`, {
        animalId,
        animalName,
        daysRemaining,
        expectedDueDate: expectedDueDate?.toISOString(),
        riskLevel,
        specialCare,
        recipient: {
          name: recipient.name,
          phone: recipient.phone ? formatPhoneNumber(recipient.phone) : undefined,
          email: recipient.email
        },
        notificationTypes,
        customMessage,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending birth prediction alert:', error);

      // In case of error, return a simulated success in offline mode
      if (config.offline.enabled) {
        const results: Array<{ type: NotificationType; result: any }> = [];

        for (const type of notificationTypes) {
          let result;

          switch (type) {
            case 'sms':
              result = {
                success: true,
                messageId: `sms-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            case 'voice':
              result = {
                success: true,
                callId: `call-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            case 'whatsapp':
              result = {
                success: true,
                messageId: `whatsapp-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
              break;
            default:
              result = {
                success: true,
                messageId: `notification-offline-${Date.now()}-${Math.floor(Math.random() * 1000)}`
              };
          }

          results.push({ type, result });
        }

        return results;
      }

      // Return error for all notification types
      return notificationTypes.map(type => ({
        type,
        result: {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error sending birth prediction alert'
        }
      }));
    }
  };

  // Enable offline mode by default for rural areas with poor connectivity
  config.offline.enabled = true;

  return {
    sendSMS,
    makeVoiceCall,
    sendWhatsApp,
    sendBirthPredictionAlert,

    // Configuration functions
    setOfflineMode: (enabled: boolean) => {
      config.offline.enabled = enabled;
    },

    isOfflineMode: () => config.offline.enabled
  };
};

export default useRuralNotificationService;
