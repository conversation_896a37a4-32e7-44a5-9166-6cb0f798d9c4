/**
 * Authentication Controller
 *
 * Handles user authentication and authorization
 */

const database = require('../config/database');
const logger = require('../utils/logger');
const authService = require('../services/authService');

const authController = {
  /**
   * Login user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  login: async (req, res) => {
    try {
      logger.info('Login attempt received');

      const { username, password } = req.body;

      if (!username || !password) {
        return res.status(400).json({ error: 'Username and password are required' });
      }

      logger.info(`Login attempt for username: ${username}`);

      // Use the authentication service to authenticate the user
      const result = await authService.authenticateUser(username, password);

      if (!result.success) {
        logger.warn(`Login failed for user ${username}: ${result.message}`);
        return res.status(401).json({ error: result.message || 'Invalid credentials' });
      }

      logger.info(`User logged in successfully: ${username}`);

      // Return user data and token
      res.json({
        user: result.user,
        token: result.token
      });
    } catch (error) {
      logger.error('Login error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get current user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getCurrentUser: async (req, res) => {
    try {
      // User ID comes from the JWT token via auth middleware
      const userId = req.user.id;

      if (!userId) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      // Get users collection
      const usersCollection = await database.getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by ID
      let user;
      try {
        const { ObjectId } = require('mongodb');
        user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        user = await usersCollection.findOne({ _id: userId });
      }

      if (!user) {
        logger.warn(`User not found: ${userId}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Return user data
      const userData = {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        permissions: user.permissions || [],
        status: user.status || 'active',
        lastLogin: user.lastLogin,
        department: user.department,
        position: user.position,
        phoneNumber: user.phoneNumber,
        profileImage: user.profileImage
      };

      res.json(userData);
    } catch (error) {
      logger.error('Get current user error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Request password reset
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  requestPasswordReset: async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'Email is required' });
      }

      // Get users collection
      const usersCollection = await database.getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by email
      const user = await usersCollection.findOne({ email });
      if (!user) {
        // Don't reveal that the email doesn't exist
        return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
      }

      // Generate reset token
      const resetToken = authService.generateResetToken();
      const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour

      // Save reset token to user
      await usersCollection.updateOne(
        { _id: user._id },
        {
          $set: {
            resetToken,
            resetTokenExpiry
          }
        }
      );

      // In a real application, send an email with the reset link
      // For now, just log it
      logger.info(`Password reset token for ${email}: ${resetToken}`);

      res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
    } catch (error) {
      logger.error('Password reset request error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Reset password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  resetPassword: async (req, res) => {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        return res.status(400).json({ error: 'Token and new password are required' });
      }

      // Get users collection
      const usersCollection = await database.getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by reset token
      const user = await usersCollection.findOne({
        resetToken: token,
        resetTokenExpiry: { $gt: new Date() }
      });

      if (!user) {
        return res.status(400).json({ error: 'Invalid or expired token' });
      }

      // Hash new password
      const hashedPassword = await authService.hashPassword(newPassword);

      // Update user password and remove reset token
      await usersCollection.updateOne(
        { _id: user._id },
        {
          $set: { password: hashedPassword },
          $unset: { resetToken: "", resetTokenExpiry: "" }
        }
      );

      res.status(200).json({ message: 'Password reset successful' });
    } catch (error) {
      logger.error('Password reset error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = authController;
