<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MongoDB Polyfill Solution</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
    }
    h1, h2, h3 {
      color: #333;
    }
    .solution {
      border-left: 4px solid #4CAF50;
      padding-left: 15px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>MongoDB Polyfill Solution for React</h1>
  
  <p>This document explains how to fix the MongoDB Node.js core modules polyfill issues in a React application.</p>

  <h2>The Problem</h2>
  <p>When using MongoDB in a React application, you may encounter errors like:</p>
  <pre><code>ERROR in ../../node_modules/mongodb/lib/client-side-encryption/auto_encrypter.js 8:12-26
Module not found: Error: Can't resolve 'net' in '...node_modules/mongodb/lib/client-side-encryption'

BREAKING CHANGE: webpack < 5 used to include polyfills for node.js core modules by default.
This is no longer the case. Verify if you need this module and configure a polyfill for it.</code></pre>

  <h2>The Solution</h2>
  <div class="solution">
    <h3>1. Create a browser-compatible version of MongoDB modules</h3>
    <p>Create a file called <code>mongodb-browser.js</code> in your src directory:</p>
    <pre><code>// This file provides browser-compatible versions of Node.js modules used by MongoDB
// It's used as a polyfill for webpack

// Mock crypto module
export const crypto = {
  randomBytes: (size) => {
    const array = new Uint8Array(size);
    window.crypto.getRandomValues(array);
    return array;
  },
  createHash: () => ({
    update: () => ({
      digest: () => 'mock-hash'
    })
  })
};

// Mock fs module
export const fs = {
  readFileSync: () => '',
  existsSync: () => false,
  readdirSync: () => []
};

// Mock http module
export const http = {
  request: () => ({
    on: () => {},
    end: () => {}
  })
};

// Mock stream module
export const stream = {
  Readable: class {
    pipe() { return this; }
    on() { return this; }
  },
  Writable: class {
    write() { return true; }
    end() {}
  }
};

// Mock timers module
export const timers = {
  setTimeout,
  clearTimeout,
  setInterval,
  clearInterval
};

// Mock zlib module
export const zlib = {
  createGzip: () => ({
    on: () => {},
    pipe: () => {}
  }),
  createGunzip: () => ({
    on: () => {},
    pipe: () => {}
  })
};

// Mock dns module
export const dns = {
  lookup: (hostname, options, callback) => {
    if (typeof options === 'function') {
      callback = options;
    }
    callback(null, '127.0.0.1', 4);
  },
  resolve: (hostname, callback) => {
    callback(null, ['127.0.0.1']);
  }
};

// Mock net module
export const net = {
  connect: () => ({
    on: () => {},
    end: () => {}
  }),
  Socket: class {
    connect() { return this; }
    on() { return this; }
    end() {}
  }
};

// Mock tls module
export const tls = {
  connect: () => ({
    on: () => {},
    end: () => {}
  }),
  TLSSocket: class {
    connect() { return this; }
    on() { return this; }
    end() {}
  }
};

// Mock child_process module
export const child_process = {
  spawn: () => ({
    on: () => {},
    stdout: { on: () => {} },
    stderr: { on: () => {} }
  }),
  exec: (cmd, callback) => {
    callback(null, '', '');
  }
};

// Export all modules
export default {
  crypto,
  fs,
  http,
  stream,
  timers,
  zlib,
  dns,
  net,
  tls,
  child_process
};</code></pre>

    <h3>2. Update webpack.config.js</h3>
    <p>Update your webpack.config.js file to use the browser-compatible versions:</p>
    <pre><code>const path = require('path');
const webpack = require('webpack');

module.exports = {
  resolve: {
    fallback: {
      // Use browser-compatible versions for Node.js modules
      "crypto": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "fs": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "http": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "stream": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "timers": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "zlib": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "dns": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "net": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "tls": path.resolve(__dirname, 'src/mongodb-browser.js'),
      "child_process": path.resolve(__dirname, 'src/mongodb-browser.js'),
      
      // These can be set to false as they're not directly used
      "os": false,
      "https": false,
      "querystring": false,
      "fs/promises": false,
      "timers/promises": false
    }
  },
  plugins: [
    // Provide process and Buffer polyfills
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    })
  ]
};</code></pre>

    <h3>3. Create a Mock MongoDB Implementation</h3>
    <p>Create a mock implementation of MongoDB for the frontend:</p>
    <pre><code>// mockMongoDb.ts
export class ObjectId {
  id: string;
  
  constructor(id?: string) {
    this.id = id || 'mock-id-' + Date.now();
  }
  
  toString() {
    return this.id;
  }
  
  toHexString() {
    return this.id;
  }
}

export class Collection {
  name: string;
  data: any[];
  
  constructor(name: string) {
    this.name = name;
    this.data = [];
  }
  
  find(query = {}) {
    console.log(`Mock find for ${this.name} with query:`, query);
    return {
      toArray: async () => this.data
    };
  }
  
  findOne(query: any) {
    console.log(`Mock findOne for ${this.name} with query:`, query);
    return Promise.resolve(this.data[0] || null);
  }
  
  insertOne(doc: any) {
    console.log(`Mock insertOne for ${this.name}:`, doc);
    const id = new ObjectId();
    const newDoc = { ...doc, _id: id };
    this.data.push(newDoc);
    return Promise.resolve({ insertedId: id });
  }
  
  updateOne(filter: any, update: any) {
    console.log(`Mock updateOne for ${this.name}:`, filter, update);
    return Promise.resolve({ modifiedCount: 1 });
  }
  
  deleteOne(filter: any) {
    console.log(`Mock deleteOne for ${this.name}:`, filter);
    return Promise.resolve({ deletedCount: 1 });
  }
  
  countDocuments(query = {}) {
    console.log(`Mock countDocuments for ${this.name} with query:`, query);
    return Promise.resolve(this.data.length);
  }
}</code></pre>

    <h3>4. Use the Mock MongoDB in Your Services</h3>
    <p>Update your MongoDB service to use the mock implementation:</p>
    <pre><code>// mongoDbClient.ts
import { MockMongoService } from '../services/mockMongoService';

// Always use mock data in the frontend to avoid Node.js polyfill issues
const useMockMongo = true;

export const getCollection = async (collectionName: string) => {
  if (useMockMongo) {
    console.log(`Using mock MongoDB collection: ${collectionName}`);
    return new MockMongoService(collectionName);
  }
  
  // This code will never run in the browser
  return new MockMongoService(collectionName);
};</code></pre>
  </div>

  <h2>Alternative Approach</h2>
  <p>The best approach for a React application is to avoid using MongoDB directly in the frontend. Instead:</p>
  <ol>
    <li>Create a backend API that interacts with MongoDB</li>
    <li>Use the API from your React application</li>
    <li>For offline or mock data, create a separate mock service that mimics the API responses</li>
  </ol>

  <h2>Conclusion</h2>
  <p>By using the approach outlined above, you can avoid the Node.js polyfill issues when using MongoDB in a React application. The key is to use mock implementations for MongoDB in the frontend and handle real MongoDB operations in your backend API.</p>
</body>
</html>
