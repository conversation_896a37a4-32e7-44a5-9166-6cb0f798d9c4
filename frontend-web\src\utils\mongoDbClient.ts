import { MockMongoDbService } from '../services/mockMongoDbService';

// Check if we should use mock data
const useMockMongo = process.env.REACT_APP_USE_MOCK_DATA === 'true';

// Create a dummy client for TypeScript compatibility when using mock data
const mockClient = {
  connect: async () => {},
  close: async () => {},
  db: (_name: string) => ({
    collection: (collectionName: string) => new MockMongoDbService(collectionName),
    command: async (_command: any) => ({ ok: 1 })
  })
};

// Use mock client by default
let client = mockClient;

// Connection state
let isConnected = false;

/**
 * Connect to MongoDB
 */
export const connectToDatabase = async () => {
  if (isConnected) {
    console.log('Already connected to MongoDB');
    return { client, db: client.db('ampd_livestock') };
  }

  try {
    await client.connect();
    isConnected = true;
    console.log('Successfully connected to MongoDB');
    return { client, db: client.db('ampd_livestock') };
  } catch (error) {
    console.error('Failed to connect to MongoDB', error);
    throw error;
  }
};

/**
 * Disconnect from MongoDB
 */
export const disconnectFromDatabase = async () => {
  if (!isConnected) {
    return;
  }

  try {
    await client.close();
    isConnected = false;
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Failed to disconnect from MongoDB', error);
    throw error;
  }
};

/**
 * Get MongoDB database instance
 */
export const getDatabase = async () => {
  const { db } = await connectToDatabase();
  return db;
};

/**
 * Get a collection from the database
 * @param collectionName The name of the collection
 */
export const getCollection = (collectionName: string) => {
  try {
    if (useMockMongo) {
      console.log(`Using mock MongoDB collection: ${collectionName}`);
      const mockService = new MockMongoDbService(collectionName);

      // Verify that the mock service has the necessary methods
      if (!mockService || typeof mockService.find !== 'function') {
        throw new Error(`Invalid mock service for collection ${collectionName}`);
      }

      return mockService;
    } else {
      // Return a mock service for now, but in a real implementation this would connect to MongoDB
      console.log(`Using mock MongoDB collection: ${collectionName}`);
      return new MockMongoDbService(collectionName);
    }
  } catch (error) {
    console.error(`Critical error getting collection ${collectionName}:`, error);

    // Return a minimal mock collection with empty data and working toArray method
    return {
      find: () => ({
        toArray: async () => []
      }),
      findOne: async () => null,
      insertOne: async () => ({ insertedId: 'error' }),
      updateOne: async () => ({ modifiedCount: 0 }),
      deleteOne: async () => ({ deletedCount: 0 }),
      countDocuments: async () => 0
    };
  }
};

// Test connection on startup (optional)
export const testConnection = async () => {
  try {
    if (useMockMongo) {
      console.log('Using mock MongoDB connection');
      return true;
    }

    // Try to connect to the backend API
    const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    const baseUrl = apiUrl.replace('/api', '');
    const response = await fetch(`${baseUrl}/health`);
    if (response.ok) {
      console.log('Successfully connected to backend API');
      return true;
    } else {
      throw new Error('Failed to connect to backend API');
    }
  } catch (error) {
    console.error('Failed to connect to MongoDB or backend API', error);
    // Don't throw error, just return false
    return false;
  }
};
