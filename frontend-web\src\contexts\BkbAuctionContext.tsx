/**
 * This file is deprecated. Use contexts/commercial/BkbAuctionContext.tsx instead.
 *
 * This file is kept for backward compatibility with existing code.
 * It re-exports the context and hooks from the new location to avoid breaking changes.
 */

import React, { createContext, useContext } from 'react';
import { BkbAuctionProvider as CommercialBkbAuctionProvider, useBkbAuctions as useCommercialBkbAuctions } from './commercial/BkbAuctionContext';
import { BkbAuctionCalendarMonth, BkbAuctionFilter } from '../types/commercial/bkbAuction';

/**
 * Re-export the context type for backward compatibility
 */
interface BkbAuctionContextType {
  currentMonth: BkbAuctionCalendarMonth | null;
  loading: boolean;
  error: string | null;
  filter: BkbAuctionFilter;
  setFilter: (filter: BkbAuctionFilter) => void;
  fetchMonth: (year: number, month: number) => Promise<void>;
  nextMonth: () => void;
  prevMonth: () => void;
  currentDate: Date;
  refreshData: () => Promise<void>;
  subscribeToCalendar: () => string;
}

/**
 * Create an empty context for type safety
 */
const BkbAuctionContext = createContext<BkbAuctionContextType | undefined>(undefined);

/**
 * Re-export the provider from the commercial module
 */
export const BkbAuctionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <CommercialBkbAuctionProvider>{children}</CommercialBkbAuctionProvider>;
};

/**
 * Re-export the hook from the commercial module
 */
export const useBkbAuctions = useCommercialBkbAuctions;
