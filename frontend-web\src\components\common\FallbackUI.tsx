import React from 'react';
import { CustomButton } from '../common';
import { Box, Typography, CircularProgress, Paper, alpha, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { ErrorOutline, Refresh, SentimentDissatisfied } from '@mui/icons-material';
import { getSafeElevation } from '../../utils/themeDefaults';

export type FallbackType = 'loading' | 'error' | 'empty' | 'noData';

interface FallbackUIProps {
  type: FallbackType;
  message?: string;
  title?: string;
  icon?: React.ReactNode;
  onRetry?: () => void;
  height?: string | number;
  module?: string;
}

/**
 * A reusable fallback UI component for different states (loading, error, empty, no data)
 */
const FallbackUI: React.FC<FallbackUIProps> = ({
  type,
  message,
  title,
  icon,
  onRetry,
  height = 300,
  module
}) => {
  const theme = useTheme();
  
  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();
  
  // Default messages and titles
  const getDefaultTitle = () => {
    switch (type) {
      case 'loading':
        return 'Loading...';
      case 'error':
        return 'Something went wrong';
      case 'empty':
        return 'No items found';
      case 'noData':
        return 'No data available';
      default:
        return '';
    }
  };
  
  const getDefaultMessage = () => {
    switch (type) {
      case 'loading':
        return 'Please wait while we load the data';
      case 'error':
        return 'An error occurred while fetching data. Please try again.';
      case 'empty':
        return 'There are no items to display at this time.';
      case 'noData':
        return 'No data is available for the selected criteria.';
      default:
        return '';
    }
  };
  
  // Default icons
  const getDefaultIcon = () => {
    switch (type) {
      case 'loading':
        return <CircularProgress size={48} color="inherit" />;
      case 'error':
        return <ErrorOutline style={{ fontSize: 48 }} />;
      case 'empty':
      case 'noData':
        return <SentimentDissatisfied style={{ fontSize: 48 }} />;
      default:
        return null;
    }
  };
  
  const displayTitle = title || getDefaultTitle();
  const displayMessage = message || getDefaultMessage();
  const displayIcon = icon || getDefaultIcon();
  
  // Get color based on type
  const getColor = () => {
    switch (type) {
      case 'loading':
        return moduleColor;
      case 'error':
        return theme.palette.error.main;
      case 'empty':
      case 'noData':
        return theme.palette.text.secondary;
      default:
        return moduleColor;
    }
  };
  
  const color = getColor();
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        elevation={0}
        sx={{
          height,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
          borderRadius: 2,
          backgroundColor: alpha(color, 0.05),
          border: `1px solid ${alpha(color, 0.1)}`,
          boxShadow: getSafeElevation(theme, 1)
        }}
      >
        <Box
          sx={{
            color,
            mb: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {displayIcon}
        </Box>
        
        <Typography
          variant="h6"
          component="h3"
          color={color}
          gutterBottom
          sx={{ fontWeight: 600, textAlign: 'center' }}
        >
          {displayTitle}
        </Typography>
        
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ textAlign: 'center', maxWidth: 400, mb: 3 }}
        >
          {displayMessage}
        </Typography>
        
        {type === 'error' && onRetry && (
          <CustomButton
            variant="outlined"
            color="primary"
            startIcon={<Refresh />}
            onClick={onRetry}
          >
            Try Again
          </CustomButton>
        )}
      </Paper>
    </motion.div>
  );
};

export default FallbackUI;
