const { spawn } = require('child_process');

// Set environment variables
process.env.PORT = 3000;
process.env.BROWSER = 'none';

console.log('Starting React development server on port 3000...');

// Start the React application using react-scripts directly
const reactApp = spawn('npx', ['react-scripts', 'start'], {
  stdio: 'inherit',
  shell: true,
  env: { ...process.env }
});

reactApp.on('error', (error) => {
  console.error('Failed to start React application:', error);
  process.exit(1);
});

reactApp.on('close', (code) => {
  console.log(`React application exited with code ${code}`);
  process.exit(code);
});
