@echo off
echo ========================================
echo AgriIntel Production Deployment Script
echo ========================================
echo.

:: Set deployment variables
set DEPLOYMENT_ENV=production
set NODE_ENV=production
set BUILD_DIR=build
set BACKUP_DIR=backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%

echo [INFO] Starting production deployment...
echo [INFO] Environment: %DEPLOYMENT_ENV%
echo [INFO] Build directory: %BUILD_DIR%
echo [INFO] Backup directory: %BACKUP_DIR%
echo.

:: Check if Node.js is installed
echo [STEP 1/10] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo [OK] Node.js is installed

:: Check if npm is installed
echo [STEP 2/10] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not installed or not in PATH
    pause
    exit /b 1
)
echo [OK] npm is installed

:: Run security audit
echo [STEP 3/10] Running security audit...
cd backend
echo [INFO] Auditing backend dependencies...
npm audit --audit-level=high
if %errorlevel% neq 0 (
    echo [WARNING] Security vulnerabilities found in backend
    echo [INFO] Attempting to fix automatically...
    npm audit fix --force
)

cd ..\frontend-web
echo [INFO] Auditing frontend dependencies...
npm audit --audit-level=high
if %errorlevel% neq 0 (
    echo [WARNING] Security vulnerabilities found in frontend
    echo [INFO] Attempting to fix automatically...
    npm audit fix --force
)
cd ..

:: Run security cleanup
echo [STEP 4/10] Running security cleanup...
cd backend
echo [INFO] Running security cleanup script...
node scripts/security-cleanup.js
if %errorlevel% neq 0 (
    echo [ERROR] Security cleanup failed
    pause
    exit /b 1
)
echo [OK] Security cleanup completed

:: Run security audit again
echo [STEP 5/10] Running final security audit...
node scripts/security-audit.js
echo [INFO] Security audit completed - check output above

:: Create production environment file
echo [STEP 6/10] Setting up production environment...
if not exist .env.production (
    echo [INFO] Creating production environment file...
    copy .env.secure .env.production
    echo [WARNING] Please update .env.production with your production values
) else (
    echo [OK] Production environment file exists
)

:: Install production dependencies
echo [STEP 7/10] Installing production dependencies...
echo [INFO] Installing backend dependencies...
npm ci --only=production
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)

cd ..\frontend-web
echo [INFO] Installing frontend dependencies...
npm ci
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)

:: Build frontend for production
echo [STEP 8/10] Building frontend for production...
echo [INFO] Building React application...
set REACT_APP_API_URL=https://your-api-domain.com/api
set REACT_APP_WS_URL=wss://your-api-domain.com
npm run build
if %errorlevel% neq 0 (
    echo [ERROR] Frontend build failed
    pause
    exit /b 1
)
echo [OK] Frontend build completed

:: Run tests
echo [STEP 9/10] Running tests...
cd ..\backend
echo [INFO] Running backend tests...
npm test
if %errorlevel% neq 0 (
    echo [WARNING] Some backend tests failed
    echo [INFO] Continuing with deployment...
)

cd ..\frontend-web
echo [INFO] Running frontend tests...
npm test -- --coverage --watchAll=false
if %errorlevel% neq 0 (
    echo [WARNING] Some frontend tests failed
    echo [INFO] Continuing with deployment...
)

:: Create deployment package
echo [STEP 10/10] Creating deployment package...
cd ..
echo [INFO] Creating deployment directory structure...

if exist deployment rmdir /s /q deployment
mkdir deployment
mkdir deployment\backend
mkdir deployment\frontend
mkdir deployment\config
mkdir deployment\scripts

:: Copy backend files
echo [INFO] Copying backend files...
xcopy backend\src deployment\backend\src\ /e /i /q
xcopy backend\package.json deployment\backend\ /q
xcopy backend\package-lock.json deployment\backend\ /q
xcopy backend\.env.production deployment\backend\.env /q
xcopy backend\scripts deployment\backend\scripts\ /e /i /q

:: Copy frontend build
echo [INFO] Copying frontend build...
xcopy frontend-web\build deployment\frontend\ /e /i /q

:: Copy configuration files
echo [INFO] Copying configuration files...
copy docker-compose.prod.yml deployment\config\ 2>nul
copy nginx.conf deployment\config\ 2>nul
copy ecosystem.config.js deployment\config\ 2>nul

:: Create deployment scripts
echo [INFO] Creating deployment scripts...

:: Create start script
echo @echo off > deployment\scripts\start.bat
echo echo Starting AgriIntel Production Server... >> deployment\scripts\start.bat
echo cd ..\backend >> deployment\scripts\start.bat
echo set NODE_ENV=production >> deployment\scripts\start.bat
echo npm start >> deployment\scripts\start.bat

:: Create stop script
echo @echo off > deployment\scripts\stop.bat
echo echo Stopping AgriIntel Production Server... >> deployment\scripts\stop.bat
echo taskkill /f /im node.exe >> deployment\scripts\stop.bat

:: Create health check script
echo @echo off > deployment\scripts\health-check.bat
echo echo Checking AgriIntel Health... >> deployment\scripts\health-check.bat
echo curl -f http://localhost:3001/api/health ^|^| exit 1 >> deployment\scripts\health-check.bat

:: Create backup script
echo @echo off > deployment\scripts\backup.bat
echo echo Creating backup... >> deployment\scripts\backup.bat
echo set BACKUP_DATE=%%date:~-4,4%%%%date:~-10,2%%%%date:~-7,2%% >> deployment\scripts\backup.bat
echo mkdir backup_%%BACKUP_DATE%% >> deployment\scripts\backup.bat
echo xcopy deployment backup_%%BACKUP_DATE%%\ /e /i /q >> deployment\scripts\backup.bat
echo echo Backup created: backup_%%BACKUP_DATE%% >> deployment\scripts\backup.bat

:: Create deployment README
echo # AgriIntel Production Deployment > deployment\README.md
echo. >> deployment\README.md
echo ## Quick Start >> deployment\README.md
echo. >> deployment\README.md
echo 1. Update backend/.env with your production values >> deployment\README.md
echo 2. Run: cd backend ^&^& npm install --production >> deployment\README.md
echo 3. Run: scripts/start.bat >> deployment\README.md
echo. >> deployment\README.md
echo ## Health Check >> deployment\README.md
echo Run: scripts/health-check.bat >> deployment\README.md
echo. >> deployment\README.md
echo ## Backup >> deployment\README.md
echo Run: scripts/backup.bat >> deployment\README.md
echo. >> deployment\README.md
echo ## Environment Variables Required >> deployment\README.md
echo - MONGODB_URI >> deployment\README.md
echo - JWT_SECRET >> deployment\README.md
echo - SMTP_HOST, SMTP_USER, SMTP_PASS >> deployment\README.md
echo - CORS_ORIGIN >> deployment\README.md
echo - WEATHER_API_KEY >> deployment\README.md
echo - STRIPE_SECRET_KEY >> deployment\README.md

:: Create deployment info file
echo Deployment Information > deployment\deployment-info.txt
echo ===================== >> deployment\deployment-info.txt
echo. >> deployment\deployment-info.txt
echo Deployment Date: %date% %time% >> deployment\deployment-info.txt
echo Environment: %DEPLOYMENT_ENV% >> deployment\deployment-info.txt
echo Node Version: >> deployment\deployment-info.txt
node --version >> deployment\deployment-info.txt
echo npm Version: >> deployment\deployment-info.txt
npm --version >> deployment\deployment-info.txt
echo. >> deployment\deployment-info.txt
echo Security Audit Status: >> deployment\deployment-info.txt
echo - Backend: Completed >> deployment\deployment-info.txt
echo - Frontend: Completed >> deployment\deployment-info.txt
echo - Security Cleanup: Completed >> deployment\deployment-info.txt
echo. >> deployment\deployment-info.txt
echo Build Status: >> deployment\deployment-info.txt
echo - Frontend Build: Success >> deployment\deployment-info.txt
echo - Backend Dependencies: Installed >> deployment\deployment-info.txt
echo. >> deployment\deployment-info.txt
echo Next Steps: >> deployment\deployment-info.txt
echo 1. Review and update deployment/backend/.env >> deployment\deployment-info.txt
echo 2. Deploy to your production server >> deployment\deployment-info.txt
echo 3. Run health checks >> deployment\deployment-info.txt
echo 4. Monitor logs and performance >> deployment\deployment-info.txt

echo.
echo ========================================
echo Production Deployment Package Created!
echo ========================================
echo.
echo [SUCCESS] Deployment package created in 'deployment' folder
echo.
echo IMPORTANT NEXT STEPS:
echo 1. Review deployment/backend/.env and update with production values
echo 2. Upload the 'deployment' folder to your production server
echo 3. On production server, run: cd deployment/backend && npm install --production
echo 4. Start the application: scripts/start.bat
echo 5. Run health check: scripts/health-check.bat
echo.
echo SECURITY REMINDERS:
echo - Change all default passwords and secrets
echo - Enable HTTPS in production
echo - Configure firewall rules
echo - Set up monitoring and logging
echo - Regular security updates
echo.
echo For detailed instructions, see deployment/README.md
echo.
pause
