const express = require('express');
const cors = require('cors');
const path = require('path');
const dotenv = require('dotenv');
const logger = require('./utils/logger');
const mongodb = require('./config/mongodb');
const schedulerService = require('./services/schedulerService');

// Load environment variables (from .env file)
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Environment variables logging (secure)
console.log('Environment variables loaded:');
if (process.env.MONGODB_URI) {
  console.log('MONGODB_URI:', process.env.MONGODB_URI.replace(/:[^:]*@/, ':****@'));
} else {
  console.warn('MONGODB_URI not set in environment variables');
}
console.log('MONGODB_DB_NAME:', process.env.MONGODB_DB_NAME || 'ampd_livestock');
console.log('PORT:', process.env.PORT || '3001');

const app = express();
const PORT = process.env.PORT || 3007; // Use environment port or default to 3007
console.log(`Using port: ${PORT}`);

// Configure CORS
const corsOptions = {
  origin: '*', // Allow all origins for testing
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
console.log('CORS configured with options:', corsOptions);
app.use(cors(corsOptions));
app.use(express.json());

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Add a simple health check route
app.get('/health', (req, res) => {
  logger.info('Health check endpoint called');
  console.log('Health check endpoint called');
  res.status(200).json({ status: 'ok' });
});

// Add a root route for testing
app.get('/', (req, res) => {
  logger.info('Root endpoint called');
  console.log('Root endpoint called');
  res.status(200).json({ message: 'AMPD Livestock Management API' });
});

// Add an API status endpoint
app.get('/api/status', (req, res) => {
  logger.info('API status endpoint called');
  console.log('API status endpoint called');
  res.status(200).json({ status: 'ok', message: 'API is running' });
});

// Handle favicon requests to prevent 404 errors
app.get('/favicon.ico', (req, res) => {
  res.status(204).end(); // No content
});

// Import error middleware
const errorMiddleware = require('./middleware/errorMiddleware');

// Initialize routes
try {
  // Always use real data from MongoDB
  const useMockData = false;
  logger.info(`Initializing routes with real data (mock data disabled)`);
  require('./routes')(app, useMockData);
} catch (error) {
  logger.error('Error initializing routes:', error);
  process.exit(1);
}

// Apply error middleware (after routes)
app.use(errorMiddleware.notFound);
app.use(errorMiddleware.validationErrorHandler);
app.use(errorMiddleware.duplicateKeyErrorHandler);
app.use(errorMiddleware.castErrorHandler);
app.use(errorMiddleware.jwtErrorHandler);
app.use(errorMiddleware.rateLimitErrorHandler);
app.use(errorMiddleware.permissionErrorHandler);
app.use(errorMiddleware.dbConnectionErrorHandler);
app.use(errorMiddleware.errorHandler);

// Add a route to check MongoDB connection status
app.get('/api/db-status', async (req, res) => {
  try {
    // Check connection
    const isConnected = await mongodb.checkConnection();

    if (!isConnected) {
      return res.status(500).json({
        status: 'disconnected',
        error: 'Failed to connect to MongoDB',
        useMockData: false
      });
    }

    // Get database instance
    const db = mongodb.getDB();
    const collections = await db.listCollections().toArray();

    // Get document counts for each collection
    const collectionStats = await Promise.all(
      collections.map(async (collection) => {
        const count = await db.collection(collection.name).countDocuments();
        return {
          name: collection.name,
          count
        };
      })
    );

    res.json({
      status: 'connected',
      collections: collectionStats,
      useMockData: false,
      databaseName: process.env.MONGODB_DB_NAME || 'ampd_livestock'
    });
  } catch (error) {
    logger.error('Error checking database status:', error);
    res.status(500).json({
      status: 'disconnected',
      error: error.message,
      useMockData: false
    });
  }
});

app.listen(PORT, '0.0.0.0', async () => {
  console.log(`Server listening on 0.0.0.0:${PORT}`);
  logger.info(`Server running on port ${PORT}`);

  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    const { db } = await mongodb.connectDB();

    // Create indexes
    await mongodb.createIndexes();

    // Check if collections exist and have data
    const collections = await db.listCollections().toArray();
    logger.info(`Found ${collections.length} collections in database`);

    // Log collection counts
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      logger.info(`Collection ${collection.name} has ${count} documents`);
    }

    // Initialize scheduled tasks
    schedulerService.initializeScheduledTasks();
    logger.info('Scheduled tasks initialized');
  } catch (err) {
    logger.error('Error connecting to database:', err);
    logger.warn('Application starting without database connection');
    logger.warn('Database connection is required for the application to function properly');
  }
});

module.exports = app; // Export for testing
