/**
 * Unified API Service
 *
 * This service provides standardized CRUD operations for all modules
 * to ensure consistent API calls and error handling across the application.
 */

import axios, { AxiosRequestConfig } from 'axios';
import { API_URL } from '../utils/config';

// API base URL from environment variable or config
const API_BASE_URL = API_URL || process.env.REACT_APP_API_URL || 'http://localhost:3005/api';

// Define endpoints for different modules
export const ENDPOINTS = {
  // Animal Management
  ANIMALS: '/animals',
  ANIMAL_BY_ID: (id: string) => `/animals/${id}`,

  // Health Management
  HEALTH_RECORDS: '/health/records',
  HEALTH_RECORD_BY_ID: (id: string) => `/health/records/${id}`,
  VACCINATIONS: '/health/vaccinations',

  // Breeding Management
  BREEDING_RECORDS: '/breeding/records',
  BREEDING_RECORD_BY_ID: (id: string) => `/breeding/records/${id}`,
  CALVING_RECORDS: '/breeding/calving',
  CALVING_RECORD_BY_ID: (id: string) => `/breeding/calving/${id}`,

  // Feeding Management
  FEEDING_RECORDS: '/feeding/records',
  FEEDING_RECORD_BY_ID: (id: string) => `/feeding/records/${id}`,
  FEED_INVENTORY: '/feeding/inventory',
  FEED_INVENTORY_BY_ID: (id: string) => `/feeding/inventory/${id}`,

  // Financial Management
  FINANCIAL_TRANSACTIONS: '/financial/transactions',
  FINANCIAL_TRANSACTION_BY_ID: (id: string) => `/financial/transactions/${id}`,
  BUDGETS: '/financial/budgets',
  BUDGET_BY_ID: (id: string) => `/financial/budgets/${id}`,

  // Resource Management
  INVENTORY: '/inventory/items',
  INVENTORY_BY_ID: (id: string) => `/inventory/items/${id}`,
  ASSETS: '/assets',
  ASSET_BY_ID: (id: string) => `/assets/${id}`,

  // User Management
  USERS: '/users',
  USER_BY_ID: (id: string) => `/users/${id}`,
  USER_LOGIN: '/auth/login',
  USER_PROFILE: '/users/profile',
};

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    // Try to get token from localStorage
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    // If token exists directly, use it
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    // Otherwise try to get it from user object
    else if (user) {
      try {
        const { token: userToken } = JSON.parse(user);
        if (userToken) {
          config.headers.Authorization = `Bearer ${userToken}`;
        }
      } catch (e) {
        console.error('Error parsing user from localStorage:', e);
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle offline mode
    if (!navigator.onLine) {
      console.log('Network error detected, app is offline');
      // Could dispatch to an offline queue here
      return Promise.reject({ isOffline: true, originalError: error });
    }

    // Handle authentication errors
    if (error.response && error.response.status === 401) {
      // Clear auth data but don't automatically redirect
      // Let the AuthContext handle the redirect logic
      console.log('401 error detected, clearing auth data');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // Don't automatically redirect - let components handle this
    }

    return Promise.reject(error);
  }
);

// Export the api instance
export default api;

// CRUD operations
export const fetchData = async (endpoint: string, params?: Record<string, any>) => {
  try {
    const config: AxiosRequestConfig = {};
    if (params) {
      config.params = params;
    }
    const response = await api.get(endpoint, config);
    return response.data;
  } catch (error) {
    console.error(`Error fetching data from ${endpoint}:`, error);
    throw error;
  }
};

export const postData = async (endpoint: string, data: any) => {
  try {
    const response = await api.post(endpoint, data);
    return response.data;
  } catch (error) {
    console.error(`Error posting data to ${endpoint}:`, error);
    throw error;
  }
};

export const updateData = async (endpoint: string, data: any) => {
  try {
    const response = await api.put(endpoint, data);
    return response.data;
  } catch (error) {
    console.error(`Error updating data at ${endpoint}:`, error);
    throw error;
  }
};

export const deleteData = async (endpoint: string) => {
  try {
    const response = await api.delete(endpoint);
    return response.data;
  } catch (error) {
    console.error(`Error deleting data at ${endpoint}:`, error);
    throw error;
  }
};
