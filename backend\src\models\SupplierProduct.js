/**
 * SupplierProduct Model
 * 
 * Stores information about products offered by agricultural suppliers.
 */

const mongoose = require('mongoose');

const SupplierProductSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  supplierId: {
    type: String,
    required: true,
    ref: 'Supplier'
  },
  name: {
    type: String,
    required: true
  },
  category: {
    type: String,
    enum: [
      'livestock-feed',
      'equipment',
      'seeds',
      'fertilizer',
      'chemicals',
      'irrigation',
      'animal-health',
      'fuel',
      'retail',
      'financial',
      'insurance',
      'other'
    ],
    required: true
  },
  description: {
    type: String,
    required: true
  },
  imageUrl: String,
  price: Number,
  currency: {
    type: String,
    default: 'ZAR'
  },
  availability: {
    type: String,
    enum: ['in-stock', 'limited', 'out-of-stock'],
    default: 'in-stock'
  },
  url: String,
  lastUpdated: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Add text index for search functionality
SupplierProductSchema.index({
  name: 'text',
  description: 'text'
});

module.exports = mongoose.model('SupplierProduct', SupplierProductSchema);
