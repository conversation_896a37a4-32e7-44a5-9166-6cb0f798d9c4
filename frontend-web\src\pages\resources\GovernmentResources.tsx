import React from 'react';
import { Box, Typography, Grid, Card, CardContent, CardMedia, Link, Divider, useTheme, alpha, Paper } from '@mui/material';
import {
  Public,
  Article,
  School,
  Agriculture,
  LocalLibrary,
  Launch
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  Mo<PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Mock government resources data
const governmentResources = [
  {
    id: 'gov001',
    name: 'Department of Agriculture, Land Reform and Rural Development',
    description: 'Official website of the South African Department of Agriculture, Land Reform and Rural Development.',
    url: 'https://www.dalrrd.gov.za',
    logo: '/assets/images/dalrrd-logo.png',
    category: 'government',
    tags: ['official', 'government', 'agriculture']
  },
  {
    id: 'gov002',
    name: 'Agricultural Research Council (ARC)',
    description: 'The Agricultural Research Council (ARC) is the principal agricultural research institution in South Africa.',
    url: 'https://www.arc.agric.za',
    logo: '/assets/images/arc-logo.png',
    category: 'research',
    tags: ['research', 'agriculture', 'science']
  },
  {
    id: 'gov003',
    name: 'National Agricultural Marketing Council (NAMC)',
    description: 'The NAMC provides strategic advice to the Minister of Agriculture on all agricultural marketing issues.',
    url: 'https://www.namc.co.za',
    logo: '/assets/images/namc-logo.png',
    category: 'marketing',
    tags: ['marketing', 'trade', 'economics']
  }
];

// Mock educational resources
const educationalResources = [
  {
    id: 'edu001',
    name: 'Livestock Production Guidelines',
    description: 'Comprehensive guidelines for livestock production in South Africa.',
    url: 'https://www.dalrrd.gov.za/livestock-guidelines',
    category: 'education',
    tags: ['livestock', 'guidelines', 'production']
  },
  {
    id: 'edu002',
    name: 'Animal Health and Disease Prevention',
    description: 'Resources for animal health management and disease prevention.',
    url: 'https://www.dalrrd.gov.za/animal-health',
    category: 'education',
    tags: ['health', 'disease', 'prevention']
  },
  {
    id: 'edu003',
    name: 'Sustainable Farming Practices',
    description: 'Information on sustainable and environmentally friendly farming practices.',
    url: 'https://www.dalrrd.gov.za/sustainable-farming',
    category: 'education',
    tags: ['sustainable', 'environment', 'farming']
  }
];

// Mock regulatory resources
const regulatoryResources = [
  {
    id: 'reg001',
    name: 'Animal Identification Regulations',
    description: 'Regulations regarding animal identification and traceability.',
    url: 'https://www.dalrrd.gov.za/animal-identification',
    category: 'regulatory',
    tags: ['regulations', 'identification', 'traceability']
  },
  {
    id: 'reg002',
    name: 'Livestock Export Standards',
    description: 'Standards and requirements for livestock exports from South Africa.',
    url: 'https://www.dalrrd.gov.za/export-standards',
    category: 'regulatory',
    tags: ['exports', 'standards', 'international']
  },
  {
    id: 'reg003',
    name: 'Animal Welfare Legislation',
    description: 'Legislation and guidelines for animal welfare in South Africa.',
    url: 'https://www.dalrrd.gov.za/animal-welfare',
    category: 'regulatory',
    tags: ['welfare', 'legislation', 'protection']
  }
];

const GovernmentResources: React.FC = () => {
  const theme = useTheme();

  return (
    <Box>
      <ModuleHeader
        title="Government & Educational Resources"
        subtitle="Access official resources from South African government departments and educational institutions"
        module="resources"
        submodule="government"
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Featured Government Department */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Paper
            elevation={3}
            sx={{
              p: 3,
              mb: 4,
              borderRadius: '12px',
              background: `linear-gradient(135deg, ${alpha('#3AA99F', 0.05)}, ${alpha('#2C8C85', 0.1)})`,
              border: `1px solid ${alpha('#3AA99F', 0.1)}`
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={3}>
                <Box
                  component="img"
                  src="/src/assets/dalrrd-logo.svg"
                  alt="Department of Agriculture, Land Reform and Rural Development"
                  sx={{
                    width: '100%',
                    maxWidth: 250,
                    height: 'auto',
                    display: 'block',
                    mx: 'auto'
                  }}
                />
              </Grid>
              <Grid item xs={12} md={9}>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Department of Agriculture, Land Reform and Rural Development
                </Typography>
                <Typography variant="body1" paragraph>
                  The Department of Agriculture, Land Reform and Rural Development (DALRRD) is responsible for
                  agricultural production, land reform, and rural development in South Africa. The department
                  provides resources, guidelines, and support for farmers and agricultural businesses.
                </Typography>
                <Box display="flex" gap={2}>
                  <CustomButton
                    variant="contained"
                    color="primary"
                    startIcon={<Public />}
                    href="https://www.dalrrd.gov.za"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Visit Official Website
                  </CustomButton>
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    startIcon={<Article />}
                    href="https://www.dalrrd.gov.za/publications"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    View Publications
                  </CustomButton>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </motion.div>

        {/* Government Resources */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h5" fontWeight="bold" mb={3}>
            Government Resources
          </Typography>

          <Grid container spacing={3} mb={4}>
            {governmentResources.map((resource, index) => (
              <Grid item xs={12} md={4} key={resource.id}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={resource.name}
                    subtitle={resource.description}
                    backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
                    icon={<Agriculture />}
                    accentColor={theme.palette.primary.main}
                    secondaryColor={theme.palette.primary.dark}
                    delay={0.1 * index}
                    height="100%"
                    actionLabel="Visit Website"
                    actionIcon={<Launch />}
                    onAction={() => window.open(resource.url, '_blank')}
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Educational Resources */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h5" fontWeight="bold" mb={3}>
            Educational Resources
          </Typography>

          <Grid container spacing={3} mb={4}>
            {educationalResources.map((resource, index) => (
              <Grid item xs={12} md={4} key={resource.id}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={resource.name}
                    subtitle={resource.description}
                    backgroundImage="https://storage.needpix.com/rsynced_images/cow-3258490_1280.jpg"
                    icon={<School />}
                    accentColor={theme.palette.secondary.main}
                    secondaryColor={theme.palette.secondary.dark}
                    delay={0.1 * index}
                    height="100%"
                    actionLabel="Learn More"
                    actionIcon={<Launch />}
                    onAction={() => window.open(resource.url, '_blank')}
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Regulatory Resources */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h5" fontWeight="bold" mb={3}>
            Regulatory Resources
          </Typography>

          <Grid container spacing={3} mb={4}>
            {regulatoryResources.map((resource, index) => (
              <Grid item xs={12} md={4} key={resource.id}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={resource.name}
                    subtitle={resource.description}
                    backgroundImage="https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg"
                    icon={<LocalLibrary />}
                    accentColor={theme.palette.info.main}
                    secondaryColor={theme.palette.info.dark}
                    delay={0.1 * index}
                    height="100%"
                    actionLabel="View Details"
                    actionIcon={<Launch />}
                    onAction={() => window.open(resource.url, '_blank')}
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>

        {/* Additional Resources */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: '12px',
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.dark, 0.1)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Need More Resources?
            </Typography>
            <Typography variant="body1" paragraph>
              If you need additional resources or have specific questions about agricultural regulations,
              policies, or guidelines, please contact the Department of Agriculture, Land Reform and Rural
              Development directly or visit their website for the most up-to-date information.
            </Typography>
            <Box display="flex" gap={2}>
              <CustomButton
                variant="contained"
                color="primary"
                startIcon={<Public />}
                href="https://www.dalrrd.gov.za/contact-us"
                target="_blank"
                rel="noopener noreferrer"
              >
                Contact DALRRD
              </CustomButton>
            </Box>
          </Paper>
        </motion.div>
      </Box>
    </Box>
  );
};

export default GovernmentResources;
