/**
 * Database Migration Script
 *
 * This script initializes the database schema and loads initial data.
 * It reads SQL files from the sql directory and executes them in order.
 */

const fs = require('fs');
const path = require('path');
const mssql = require('mssql');
const dotenv = require('dotenv');
const logger = require('../src/utils/logger');

// Load environment variables
dotenv.config();

// SQL Server configuration
const dbConfig = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'ampd_livestock',
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    trustServerCertificate: true,
    enableArithAbort: true,
    encrypt: false
  }
};

// If username and password are provided, use SQL authentication
// Otherwise, use Windows authentication
if (process.env.DB_USER && process.env.DB_PASSWORD) {
  dbConfig.user = process.env.DB_USER;
  dbConfig.password = process.env.DB_PASSWORD;
  logger.info('Using SQL Server Authentication');
} else {
  // Windows Authentication
  dbConfig.authentication = {
    type: 'default',
    options: {
      trustedConnection: true
    }
  };
  logger.info('Using Windows Authentication for SQL Server connection');
}

// SQL files to execute in order
const sqlFiles = [
  '../sql/01_schema.sql',
  '../sql/02_initial_data.sql',
  '../sql/03_stored_procedures.sql'
];

/**
 * Execute SQL file
 * @param {string} filePath - Path to SQL file
 * @param {mssql.ConnectionPool} pool - SQL connection pool
 */
async function executeSqlFile(filePath, pool) {
  try {
    const fullPath = path.join(__dirname, filePath);
    const sqlContent = fs.readFileSync(fullPath, 'utf8');
    
    // Split the SQL file by GO statements (SQL Server batch separator)
    const batches = sqlContent.split(/^\s*GO\s*$/m);
    
    for (const batch of batches) {
      if (batch.trim()) {
        await pool.request().batch(batch);
      }
    }
    
    logger.info(`Successfully executed SQL file: ${path.basename(filePath)}`);
  } catch (error) {
    logger.error(`Error executing SQL file ${filePath}:`, error);
    throw error;
  }
}

/**
 * Run database migration
 */
async function runMigration() {
  let pool;
  
  try {
    logger.info('Starting database migration...');
    
    // Create connection pool
    pool = await mssql.connect(dbConfig);
    logger.info(`Connected to SQL Server: ${dbConfig.server}, Database: ${dbConfig.database}`);
    
    // Execute SQL files in order
    for (const file of sqlFiles) {
      await executeSqlFile(file, pool);
    }
    
    logger.info('Database migration completed successfully');
  } catch (error) {
    logger.error('Database migration failed:', error);
    process.exit(1);
  } finally {
    if (pool) {
      await pool.close();
      logger.info('Database connection closed');
    }
  }
}

// Run migration
runMigration();
require('dotenv').config();
const sql = require('mssql');

const config = {
    server: 'DESKTOP-48MI1BR',  // Your server name
    database: 'ampd_livestock',
    options: {
        encrypt: false,
        trustServerCertificate: true,
        enableArithAbort: true,
        instanceName: 'SQLEXPRESS',  // Add this if you're using SQL Express
        trustedConnection: true
    },
    port: 1433,  // Default SQL Server port
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

// Test connection before proceeding
async function testConnection() {
    try {
        console.log('Testing connection...');
        console.log('Connection config:', {
            server: config.server,
            database: config.database,
            options: config.options
        });
        
        await sql.connect(config);
        console.log('Connection successful!');
        return true;
    } catch (err) {
        console.error('Connection test failed:', err);
        return false;
    }
}

const mockData = {
    animals: [
        {
            tag_number: 'A001',
            breed: 'Angus',
            birth_date: '2022-01-01',
            gender: 'Female',
            status: 'Active'
        },
        {
            tag_number: 'A002',
            breed: 'Hereford',
            birth_date: '2022-02-15',
            gender: 'Male',
            status: 'Active'
        }
    ]
};

async function createTables() {
    try {
        const pool = await sql.connect(config);
        
        // Create Animals table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='animals' and xtype='U')
            BEGIN
                CREATE TABLE animals (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    tag_number VARCHAR(50) NOT NULL UNIQUE,
                    breed VARCHAR(100) NOT NULL,
                    birth_date DATE NOT NULL,
                    gender VARCHAR(20) NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    created_at DATETIME DEFAULT GETDATE(),
                    updated_at DATETIME DEFAULT GETDATE()
                )
                PRINT 'Animals table created successfully'
            END
            ELSE
                PRINT 'Animals table already exists'
        `);

        console.log('Tables created successfully');
        return pool;
    } catch (err) {
        console.error('Error creating tables:', err);
        throw err;
    }
}

async function insertMockData(pool) {
    try {
        // Clear existing data
        await pool.request().query('DELETE FROM animals');
        console.log('Cleared existing data from animals table');

        // Insert Animals
        for (const animal of mockData.animals) {
            await pool.request()
                .input('tag_number', sql.VarChar, animal.tag_number)
                .input('breed', sql.VarChar, animal.breed)
                .input('birth_date', sql.Date, new Date(animal.birth_date))
                .input('gender', sql.VarChar, animal.gender)
                .input('status', sql.VarChar, animal.status)
                .query(`
                    INSERT INTO animals (tag_number, breed, birth_date, gender, status)
                    VALUES (@tag_number, @breed, @birth_date, @gender, @status)
                `);
            console.log(`Inserted animal with tag number: ${animal.tag_number}`);
        }

        console.log('Mock data inserted successfully');
    } catch (err) {
        console.error('Error inserting mock data:', err);
        throw err;
    }
}

async function migrate() {
    let pool;
    try {
        // Test connection first
        const connectionSuccessful = await testConnection();
        if (!connectionSuccessful) {
            console.error('Connection test failed. Stopping migration.');
            process.exit(1);
        }

        pool = await createTables();
        await insertMockData(pool);
        console.log('Migration completed successfully');
    } catch (err) {
        console.error('Migration failed:', err);
        process.exit(1);
    } finally {
        if (pool) {
            await pool.close();
            console.log('Database connection closed');
        }
    }
}

migrate();
