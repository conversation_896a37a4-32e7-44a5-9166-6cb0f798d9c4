import { useSnackbar } from '../hooks/useSnackbar';
import axios from 'axios';

// API configuration
const API_BASE_URL = 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * Notification types supported by the system
 */
export type NotificationType = 'sms' | 'voice' | 'email' | 'app' | 'whatsapp';

/**
 * Priority levels for notifications
 */
export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical';

/**
 * Notification message interface
 */
export interface NotificationMessage {
  to: string;
  message: string;
  subject?: string;
  priority?: NotificationPriority;
  type: NotificationType;
  attachments?: Array<{
    name: string;
    content: string;
    contentType: string;
  }>;
  metadata?: Record<string, any>;
}

/**
 * Recipient interface for notifications
 */
export interface NotificationRecipient {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  notificationTypes: NotificationType[];
  role?: string;
}

/**
 * Birth notification configuration
 */
export interface BirthNotificationConfig {
  enabled: boolean;
  notifyDaysBefore: number[];
  recipients: NotificationRecipient[];
  notificationTypes: NotificationType[];
  customMessage?: string;
  escalationEnabled?: boolean;
  escalationThresholds?: {
    critical: number; // Days before birth for critical notifications
    high: number; // Days before birth for high priority notifications
    medium: number; // Days before birth for medium priority notifications
  };
}

/**
 * Notification service for sending various types of notifications
 */
export const notificationService = {
  /**
   * Send an SMS notification
   * @param to Recipient phone number
   * @param message Message content
   * @param priority Priority level
   * @returns Promise with the result
   */
  sendSMS: async (
    to: string,
    message: string,
    priority: NotificationPriority = 'medium'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      console.log(`Sending SMS to ${to} with priority ${priority}:`, message);

      // Call the backend API to send the SMS
      const response = await api.post('/notifications/sms', {
        to,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending SMS:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending SMS'
      };
    }
  },

  /**
   * Send a voice call notification
   * @param to Recipient phone number
   * @param message Message to convert to speech
   * @param priority Priority level
   * @returns Promise with the result
   */
  sendVoiceCall: async (
    to: string,
    message: string,
    priority: NotificationPriority = 'high'
  ): Promise<{ success: boolean; callId?: string; error?: string }> => {
    try {
      console.log(`Initiating voice call to ${to} with priority ${priority}:`, message);

      // Call the backend API to make the voice call
      const response = await api.post('/notifications/voice', {
        to,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error making voice call:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error making voice call'
      };
    }
  },

  /**
   * Send an email notification
   * @param to Recipient email address
   * @param subject Email subject
   * @param message Email body
   * @param priority Priority level
   * @returns Promise with the result
   */
  sendEmail: async (
    to: string,
    subject: string,
    message: string,
    priority: NotificationPriority = 'low'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      console.log(`Sending email to ${to} with priority ${priority}:`, subject, message);

      // Call the backend API to send the email
      const response = await api.post('/notifications/email', {
        to,
        subject,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending email'
      };
    }
  },

  /**
   * Send a WhatsApp notification
   * @param to Recipient phone number
   * @param message Message content
   * @param priority Priority level
   * @returns Promise with the result
   */
  sendWhatsApp: async (
    to: string,
    message: string,
    priority: NotificationPriority = 'medium'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      console.log(`Sending WhatsApp message to ${to} with priority ${priority}:`, message);

      // Call the backend API to send the WhatsApp message
      const response = await api.post('/notifications/whatsapp', {
        to,
        message,
        priority
      });

      return response.data;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending WhatsApp message'
      };
    }
  },

  /**
   * Send an in-app notification
   * @param to Recipient user ID
   * @param message Notification content
   * @param priority Priority level
   * @returns Promise with the result
   */
  sendAppNotification: async (
    to: string,
    message: string,
    priority: NotificationPriority = 'low'
  ): Promise<{ success: boolean; notificationId?: string; error?: string }> => {
    try {
      console.log(`Sending in-app notification to ${to} with priority ${priority}:`, message);

      // In a real implementation, this would use a notification service
      // For now, we'll simulate a successful response
      const mockResponse = {
        success: true,
        notificationId: `app-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString()
      };

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return mockResponse;
    } catch (error) {
      console.error('Error sending in-app notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending in-app notification'
      };
    }
  },

  /**
   * Send a notification using the specified method
   * @param notification Notification message
   * @returns Promise with the result
   */
  sendNotification: async (
    notification: NotificationMessage
  ): Promise<{ success: boolean; notificationId?: string; error?: string }> => {
    try {
      switch (notification.type) {
        case 'sms':
          return await notificationService.sendSMS(
            notification.to,
            notification.message,
            notification.priority
          );
        case 'voice':
          return await notificationService.sendVoiceCall(
            notification.to,
            notification.message,
            notification.priority
          );
        case 'email':
          return await notificationService.sendEmail(
            notification.to,
            notification.subject || 'Notification from AMPD Livestock',
            notification.message,
            notification.priority
          );
        case 'whatsapp':
          return await notificationService.sendWhatsApp(
            notification.to,
            notification.message,
            notification.priority
          );
        case 'app':
          return await notificationService.sendAppNotification(
            notification.to,
            notification.message,
            notification.priority
          );
        default:
          throw new Error(`Unsupported notification type: ${notification.type}`);
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending notification'
      };
    }
  },

  /**
   * Send a birth prediction alert using multiple notification methods
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param daysRemaining Days remaining until expected birth
   * @param recipient Recipient information
   * @param config Notification configuration
   * @returns Promise with the results for each notification type
   */
  sendBirthPredictionAlert: async (
    animalId: string,
    animalName: string,
    daysRemaining: number,
    recipient: {
      name: string;
      phone?: string;
      email?: string;
      notificationTypes: NotificationType[];
    },
    config?: {
      customMessage?: string;
      priority?: NotificationPriority;
    }
  ): Promise<Array<{ type: NotificationType; result: { success: boolean; notificationId?: string; error?: string } }>> => {
    try {
      // Call the backend API to send the birth alert
      const response = await api.post('/notifications/birth-alert', {
        animalId,
        animalName,
        daysRemaining,
        recipient,
        notificationTypes: recipient.notificationTypes,
        customMessage: config?.customMessage,
        priority: config?.priority
      });

      return response.data.results;
    } catch (error) {
      console.error('Error sending birth alert:', error);

      // Fallback to local implementation if API call fails
      // Determine priority based on days remaining
      let priority: NotificationPriority = 'medium';
      if (daysRemaining <= 2) {
        priority = 'critical';
      } else if (daysRemaining <= 7) {
        priority = 'high';
      } else if (daysRemaining <= 14) {
        priority = 'medium';
      } else {
        priority = 'low';
      }

      // Override priority if specified in config
      if (config?.priority) {
        priority = config.priority;
      }

      // Create the base message
      const baseMessage = config?.customMessage ||
        `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days.`;

      // Add urgency indicator based on priority
      let message = baseMessage;
      if (priority === 'critical') {
        message = `URGENT: ${baseMessage} IMMEDIATE ATTENTION REQUIRED.`;
      } else if (priority === 'high') {
        message = `IMPORTANT: ${baseMessage} Please prepare accordingly.`;
      }

      // Create email subject
      const subject = `${priority === 'critical' ? 'URGENT: ' : ''}Birth Alert for ${animalName} - ${daysRemaining} days remaining`;

      // Send notifications using each specified method
      const results: Array<{ type: NotificationType; result: { success: boolean; notificationId?: string; error?: string } }> = [];

      for (const type of recipient.notificationTypes) {
        let result;

        switch (type) {
          case 'sms':
            if (recipient.phone) {
              result = await notificationService.sendSMS(recipient.phone, message, priority);
              results.push({ type, result });
            }
            break;
          case 'voice':
            if (recipient.phone) {
              result = await notificationService.sendVoiceCall(recipient.phone, message, priority);
              results.push({ type, result });
            }
            break;
          case 'email':
            if (recipient.email) {
              result = await notificationService.sendEmail(recipient.email, subject, message, priority);
              results.push({ type, result });
            }
            break;
          case 'whatsapp':
            if (recipient.phone) {
              result = await notificationService.sendWhatsApp(recipient.phone, message, priority);
              results.push({ type, result });
            }
            break;
          case 'app':
            // For app notifications, we use the recipient's name as the identifier
            result = await notificationService.sendAppNotification(recipient.name, message, priority);
            results.push({ type, result });
            break;
        }
      }

      return results;
    }
  }
};

/**
 * Hook for using the notification service with integrated snackbar notifications
 */
export const useNotificationService = () => {
  const { showSnackbar } = useSnackbar();

  /**
   * Send a notification with snackbar feedback
   * @param notification Notification message
   * @returns Promise with the result
   */
  const sendNotification = async (notification: NotificationMessage) => {
    try {
      const result = await notificationService.sendNotification(notification);

      if (result.success) {
        showSnackbar(`${notification.type.toUpperCase()} notification sent successfully`, 'success');
        return result;
      } else {
        showSnackbar(`Failed to send ${notification.type} notification: ${result.error}`, 'error');
        return result;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showSnackbar(`Error sending ${notification.type} notification: ${errorMessage}`, 'error');
      return { success: false, error: errorMessage };
    }
  };

  /**
   * Send a birth prediction alert with snackbar feedback
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param daysRemaining Days remaining until expected birth
   * @param recipient Recipient information
   * @param config Notification configuration
   * @returns Promise with the results
   */
  const sendBirthPredictionAlert = async (
    animalId: string,
    animalName: string,
    daysRemaining: number,
    recipient: {
      name: string;
      phone?: string;
      email?: string;
      notificationTypes: NotificationType[];
    },
    config?: {
      customMessage?: string;
      priority?: NotificationPriority;
    }
  ) => {
    try {
      const results = await notificationService.sendBirthPredictionAlert(
        animalId,
        animalName,
        daysRemaining,
        recipient,
        config
      );

      const successCount = results.filter(r => r.result.success).length;

      if (successCount === results.length) {
        showSnackbar(`Birth alert for ${animalName} sent successfully via ${successCount} channels`, 'success');
      } else if (successCount > 0) {
        showSnackbar(`Birth alert for ${animalName} sent via ${successCount}/${results.length} channels`, 'warning');
      } else {
        showSnackbar(`Failed to send birth alert for ${animalName}`, 'error');
      }

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showSnackbar(`Error sending birth alert: ${errorMessage}`, 'error');
      return [];
    }
  };

  return {
    sendNotification,
    sendBirthPredictionAlert,
    sendSMS: notificationService.sendSMS,
    sendVoiceCall: notificationService.sendVoiceCall,
    sendEmail: notificationService.sendEmail,
    sendWhatsApp: notificationService.sendWhatsApp,
    sendAppNotification: notificationService.sendAppNotification
  };
};

export default notificationService;
