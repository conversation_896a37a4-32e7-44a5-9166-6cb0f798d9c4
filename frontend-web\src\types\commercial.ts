export interface CommercialOrder {
  id: string;
  orderNumber: string;
  date: Date;
  customer: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'partial' | 'refunded';
  shippingAddress: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  animalId?: string;
}

export interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  products: string[];
  rating: number;
  status: 'active' | 'inactive';
  lastOrderDate?: Date;
  notes?: string;
}

export interface Auction {
  id: string;
  title: string;
  date: Date;
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';
  location: string;
  description: string;
  items: AuctionItem[];
  registeredBidders: number;
  minimumBid: number;
  endTime: Date;
  featured?: boolean;
}

export interface AuctionItem {
  id: string;
  type: 'livestock' | 'equipment' | 'feed' | 'other';
  description: string;
  startingPrice: number;
  currentBid?: number;
  numberOfBids: number;
  images: string[];
  status: 'available' | 'sold' | 'withdrawn';
  animalId?: string;
}

export interface PriceUpdate {
  id: string;
  productType: string;
  oldPrice: number;
  newPrice: number;
  effectiveDate: Date;
  reason: string;
  updatedBy: string;
}

export interface CommercialStats {
  totalOrders: number;
  pendingOrders: number;
  totalRevenue: number;
  monthlyRevenue: number;
  activeSuppliers: number;
  pendingAuctions: number;
  averageOrderValue: number;
  topSellingProducts: {
    productName: string;
    quantity: number;
    revenue: number;
  }[];
  revenueByMonth: {
    month: string;
    revenue: number;
  }[];
  ordersByStatus: {
    status: string;
    count: number;
  }[];
}
