/**
 * MongoDB User Controller
 *
 * Handles user management operations with MongoDB
 */

const { getCollection } = require('../config/db');
const logger = require('../utils/logger');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongodb');

// Try to require bcrypt, but don't fail if it's not available
let bcrypt;
try {
  bcrypt = require('bcrypt');
} catch (error) {
  logger.warn('bcrypt module not available, falling back to SHA-256 for password hashing');
  bcrypt = null;
}

// JWT Secret from environment variables
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Ensure JWT_SECRET is set
if (!JWT_SECRET) {
  logger.error('JWT_SECRET is not set in environment variables');
  throw new Error('JWT_SECRET is required for authentication');
}

/**
 * Hash password using SHA-256 (for compatibility with inject-users.js)
 * @param {string} password - Password to hash
 * @returns {string} - Hashed password
 */
const hashPassword = (password) => {
  const salt = 'maycaiphus-livestock-salt';
  return crypto.createHash('sha256').update(password + salt).digest('hex');
};

/**
 * Compare password with hashed password
 * @param {string} password - Password to compare
 * @param {string} hashedPassword - Hashed password from database
 * @returns {boolean} - True if passwords match
 */
const comparePassword = (password, hashedPassword) => {
  // First try SHA-256 hash (used in inject-users.js)
  const shaHash = hashPassword(password);
  if (shaHash === hashedPassword) {
    return true;
  }

  // Then try bcrypt if available (used in user.model.js)
  if (bcrypt) {
    try {
      return bcrypt.compareSync(password, hashedPassword);
    } catch (error) {
      // If bcrypt fails, it's not a bcrypt hash
      logger.warn('bcrypt comparison failed:', error.message);
      return false;
    }
  }

  // If bcrypt is not available, just return false for non-SHA-256 hashes
  return false;
};

const mongoUserController = {
  /**
   * Get all users
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllUsers: async (req, res) => {
    try {
      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Get all users
      const users = await usersCollection.find({}).toArray();

      // Transform users for response
      const transformedUsers = users.map(user => ({
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status || 'active',
        permissions: user.permissions || [],
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        department: user.department,
        position: user.position,
        phoneNumber: user.phoneNumber,
        profileImage: user.profileImage
      }));

      res.json(transformedUsers);
    } catch (error) {
      logger.error('Get all users error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get user by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getUserById: async (req, res) => {
    try {
      const userId = req.params.id;

      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by ID
      let user;
      try {
        user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        user = await usersCollection.findOne({ _id: userId });
      }

      if (!user) {
        logger.warn(`User not found: ${userId}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Transform user for response
      const transformedUser = {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status || 'active',
        permissions: user.permissions || [],
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        department: user.department,
        position: user.position,
        phoneNumber: user.phoneNumber,
        profileImage: user.profileImage
      };

      res.json(transformedUser);
    } catch (error) {
      logger.error('Get user by ID error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get user by username
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getUserByUsername: async (req, res) => {
    try {
      const username = req.params.username;

      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by username
      const user = await usersCollection.findOne({ username });

      if (!user) {
        logger.warn(`User not found: ${username}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Transform user for response
      const transformedUser = {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        status: user.status || 'active',
        permissions: user.permissions || [],
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        department: user.department,
        position: user.position,
        phoneNumber: user.phoneNumber,
        profileImage: user.profileImage
      };

      res.json(transformedUser);
    } catch (error) {
      logger.error('Get user by username error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Create user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createUser: async (req, res) => {
    try {
      const { username, email, password, firstName, lastName, role, status, permissions, department, position, phoneNumber } = req.body;

      // Validate required fields
      if (!username || !email || !password || !firstName || !lastName) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Check if username already exists
      const existingUser = await usersCollection.findOne({ username });
      if (existingUser) {
        return res.status(400).json({ error: 'Username already exists' });
      }

      // Check if email already exists
      const existingEmail = await usersCollection.findOne({ email });
      if (existingEmail) {
        return res.status(400).json({ error: 'Email already exists' });
      }

      // Hash password
      let hashedPassword;
      if (bcrypt) {
        // Use bcrypt if available
        const salt = await bcrypt.genSalt(10);
        hashedPassword = await bcrypt.hash(password, salt);
      } else {
        // Fall back to SHA-256
        hashedPassword = hashPassword(password);
      }

      // Create user
      const newUser = {
        username,
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role: role || 'viewer',
        status: status || 'active',
        permissions: permissions || [],
        department: department || '',
        position: position || '',
        phoneNumber: phoneNumber || '',
        lastLogin: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Insert user
      const result = await usersCollection.insertOne(newUser);

      // Return created user
      const createdUser = {
        id: result.insertedId.toString(),
        username,
        email,
        firstName,
        lastName,
        role: role || 'viewer',
        status: status || 'active',
        permissions: permissions || [],
        department: department || '',
        position: position || '',
        phoneNumber: phoneNumber || '',
        lastLogin: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      logger.info(`User created: ${username}`);
      res.status(201).json(createdUser);
    } catch (error) {
      logger.error('Create user error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Update user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateUser: async (req, res) => {
    try {
      const userId = req.params.id;
      const { email, firstName, lastName, role, status, permissions, department, position, phoneNumber, password } = req.body;

      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by ID
      let user;
      try {
        user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        user = await usersCollection.findOne({ _id: userId });
      }

      if (!user) {
        logger.warn(`User not found: ${userId}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Prepare update data
      const updateData = {
        updatedAt: new Date()
      };

      if (email) updateData.email = email;
      if (firstName) updateData.firstName = firstName;
      if (lastName) updateData.lastName = lastName;
      if (role) updateData.role = role;
      if (status) updateData.status = status;
      if (permissions) updateData.permissions = permissions;
      if (department) updateData.department = department;
      if (position) updateData.position = position;
      if (phoneNumber) updateData.phoneNumber = phoneNumber;

      // Hash password if provided
      if (password) {
        if (bcrypt) {
          // Use bcrypt if available
          const salt = await bcrypt.genSalt(10);
          updateData.password = await bcrypt.hash(password, salt);
        } else {
          // Fall back to SHA-256
          updateData.password = hashPassword(password);
        }
      }

      // Update user
      let filter;
      try {
        filter = { _id: new ObjectId(userId) };
      } catch (error) {
        // If ObjectId conversion fails, use string ID
        filter = { _id: userId };
      }

      await usersCollection.updateOne(filter, { $set: updateData });

      // Get updated user
      let updatedUser;
      try {
        updatedUser = await usersCollection.findOne({ _id: new ObjectId(userId) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        updatedUser = await usersCollection.findOne({ _id: userId });
      }

      // Transform user for response
      const transformedUser = {
        id: updatedUser._id.toString(),
        username: updatedUser.username,
        email: updatedUser.email,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        role: updatedUser.role,
        status: updatedUser.status || 'active',
        permissions: updatedUser.permissions || [],
        lastLogin: updatedUser.lastLogin,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
        department: updatedUser.department,
        position: updatedUser.position,
        phoneNumber: updatedUser.phoneNumber,
        profileImage: updatedUser.profileImage
      };

      logger.info(`User updated: ${updatedUser.username}`);
      res.json(transformedUser);
    } catch (error) {
      logger.error('Update user error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Delete user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteUser: async (req, res) => {
    try {
      const userId = req.params.id;

      // Get users collection
      const usersCollection = await getCollection('users');
      if (!usersCollection) {
        logger.error('Users collection not found');
        return res.status(500).json({ error: 'Database error' });
      }

      // Find user by ID
      let user;
      try {
        user = await usersCollection.findOne({ _id: new ObjectId(userId) });
      } catch (error) {
        // If ObjectId conversion fails, try string ID
        user = await usersCollection.findOne({ _id: userId });
      }

      if (!user) {
        logger.warn(`User not found: ${userId}`);
        return res.status(404).json({ error: 'User not found' });
      }

      // Delete user
      let filter;
      try {
        filter = { _id: new ObjectId(userId) };
      } catch (error) {
        // If ObjectId conversion fails, use string ID
        filter = { _id: userId };
      }

      const result = await usersCollection.deleteOne(filter);

      if (result.deletedCount === 0) {
        return res.status(500).json({ error: 'Failed to delete user' });
      }

      logger.info(`User deleted: ${user.username}`);
      res.json({ message: 'User deleted successfully' });
    } catch (error) {
      logger.error('Delete user error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = mongoUserController;
