import { useState, useEffect } from 'react';
import { mockInventory } from '../mocks';
import { useMockData } from '../utils/config';
import { fetchData } from '../services/apiService';
import { exportToCSV } from '../utils/export';
import { InventoryItem } from '../types/inventory';

export const useInventoryData = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInventory = async () => {
    try {
      setLoading(true);

      if (useMockData) {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Transform mockInventory to match the expected type
        const transformedInventory = mockInventory.map(item => ({
          ...item,
          lastRestockDate: item.lastRestocked || new Date().toISOString(),
          unitCost: item.cost || 0,
          totalValue: (item.cost || 0) * item.quantity
        }));

        setInventory(transformedInventory as any);
      } else {
        const data = await fetchData('/inventory');
        setInventory(data);
      }
    } catch (err) {
      setError('Failed to fetch inventory');
    } finally {
      setLoading(false);
    }
  };

  const exportInventory = () => {
    const csvData = inventory.map(item => ({
      ID: item.id,
      Category: item.category,
      Name: item.name,
      Quantity: item.quantity,
      Unit: item.unit,
      'Min Threshold': item.minThreshold,
      Location: item.location,
      'Last Restocked': item.lastRestocked,
      Supplier: item.supplier,
      Cost: item.cost
    }));

    exportToCSV(csvData, `inventory_export_${new Date().toISOString().split('T')[0]}`);
  };

  useEffect(() => {
    fetchInventory();
  }, []);

  return {
    inventory,
    loading,
    error,
    fetchInventory,
    exportInventory
  };
};

