// Financial Transaction Type
export interface FinancialTransaction {
  id: string;
  date: string;
  type: 'income' | 'expense';
  category: string;
  amount: number;
  description: string;
  paymentMethod: string;
  status: 'completed' | 'pending' | 'cancelled';
  relatedTo?: string;
  tags?: string[];
}

// Budget Type
export interface Budget {
  id: string;
  name: string;
  category: string;
  amount: number;
  spent: number;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'cancelled';
}

// Mock Financial Transactions
export const mockFinancialTransactions: FinancialTransaction[] = [
  {
    id: 'FT001',
    date: '2024-04-01',
    type: 'income',
    category: 'Sales',
    amount: 25000,
    description: 'Sale of 5 Nguni cattle',
    paymentMethod: 'Bank Transfer',
    status: 'completed',
    relatedTo: 'Livestock Sales',
    tags: ['cattle', 'sales']
  },
  {
    id: 'FT002',
    date: '2024-04-05',
    type: 'expense',
    category: 'Feed',
    amount: 12500,
    description: 'Purchase of cattle feed',
    paymentMethod: 'Credit Card',
    status: 'completed',
    relatedTo: 'Feed Management',
    tags: ['feed', 'cattle']
  },
  {
    id: 'FT003',
    date: '2024-04-10',
    type: 'expense',
    category: 'Veterinary',
    amount: 8500,
    description: 'Veterinary services for sheep',
    paymentMethod: 'Cash',
    status: 'completed',
    relatedTo: 'Health Management',
    tags: ['health', 'sheep']
  },
  {
    id: 'FT004',
    date: '2024-04-15',
    type: 'income',
    category: 'Sales',
    amount: 18000,
    description: 'Sale of wool',
    paymentMethod: 'Bank Transfer',
    status: 'completed',
    relatedTo: 'Wool Production',
    tags: ['wool', 'sales']
  },
  {
    id: 'FT005',
    date: '2024-04-20',
    type: 'expense',
    category: 'Equipment',
    amount: 35000,
    description: 'Purchase of new fencing equipment',
    paymentMethod: 'Credit Card',
    status: 'completed',
    relatedTo: 'Infrastructure',
    tags: ['equipment', 'infrastructure']
  },
  {
    id: 'FT006',
    date: '2024-04-25',
    type: 'expense',
    category: 'Labor',
    amount: 22000,
    description: 'Monthly wages for farm workers',
    paymentMethod: 'Bank Transfer',
    status: 'completed',
    relatedTo: 'Human Resources',
    tags: ['labor', 'wages']
  },
  {
    id: 'FT007',
    date: '2024-04-28',
    type: 'income',
    category: 'Sales',
    amount: 15000,
    description: 'Sale of goat milk products',
    paymentMethod: 'Cash',
    status: 'completed',
    relatedTo: 'Dairy Production',
    tags: ['goat', 'milk', 'sales']
  },
  {
    id: 'FT008',
    date: '2024-04-30',
    type: 'expense',
    category: 'Utilities',
    amount: 7500,
    description: 'Electricity and water bills',
    paymentMethod: 'Bank Transfer',
    status: 'completed',
    relatedTo: 'Operations',
    tags: ['utilities', 'operations']
  }
];

// Mock Budgets
export const mockBudgets: Budget[] = [
  {
    id: 'B001',
    name: 'Feed Budget',
    category: 'Feed',
    amount: 50000,
    spent: 32500,
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    status: 'active'
  },
  {
    id: 'B002',
    name: 'Veterinary Budget',
    category: 'Health',
    amount: 30000,
    spent: 18500,
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    status: 'active'
  },
  {
    id: 'B003',
    name: 'Equipment Budget',
    category: 'Infrastructure',
    amount: 100000,
    spent: 65000,
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    status: 'active'
  },
  {
    id: 'B004',
    name: 'Labor Budget',
    category: 'Human Resources',
    amount: 80000,
    spent: 52000,
    startDate: '2024-04-01',
    endDate: '2024-04-30',
    status: 'active'
  },
  {
    id: 'B005',
    name: 'Marketing Budget',
    category: 'Marketing',
    amount: 25000,
    spent: 12000,
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    status: 'active'
  }
];

// Financial Summary
export const mockFinancialSummary = {
  totalIncome: mockFinancialTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0),
  totalExpenses: mockFinancialTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0),
  netProfit: mockFinancialTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0) - 
    mockFinancialTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0),
  budgetUtilization: mockBudgets.reduce((sum, b) => sum + b.spent, 0) / 
    mockBudgets.reduce((sum, b) => sum + b.amount, 0) * 100,
  expensesByCategory: {
    Feed: mockFinancialTransactions
      .filter(t => t.type === 'expense' && t.category === 'Feed')
      .reduce((sum, t) => sum + t.amount, 0),
    Veterinary: mockFinancialTransactions
      .filter(t => t.type === 'expense' && t.category === 'Veterinary')
      .reduce((sum, t) => sum + t.amount, 0),
    Equipment: mockFinancialTransactions
      .filter(t => t.type === 'expense' && t.category === 'Equipment')
      .reduce((sum, t) => sum + t.amount, 0),
    Labor: mockFinancialTransactions
      .filter(t => t.type === 'expense' && t.category === 'Labor')
      .reduce((sum, t) => sum + t.amount, 0),
    Utilities: mockFinancialTransactions
      .filter(t => t.type === 'expense' && t.category === 'Utilities')
      .reduce((sum, t) => sum + t.amount, 0)
  },
  incomeByCategory: {
    'Livestock Sales': mockFinancialTransactions
      .filter(t => t.type === 'income' && t.relatedTo === 'Livestock Sales')
      .reduce((sum, t) => sum + t.amount, 0),
    'Wool Production': mockFinancialTransactions
      .filter(t => t.type === 'income' && t.relatedTo === 'Wool Production')
      .reduce((sum, t) => sum + t.amount, 0),
    'Dairy Production': mockFinancialTransactions
      .filter(t => t.type === 'income' && t.relatedTo === 'Dairy Production')
      .reduce((sum, t) => sum + t.amount, 0)
  }
};
