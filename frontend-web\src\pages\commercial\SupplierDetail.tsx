import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Typography, Card, CardContent, Grid, Divider, Chip, Rating, Avatar, List, ListItem, ListItemAvatar, ListItemText, IconButton, Tooltip, Tab, Tabs, Paper, useTheme, alpha, CircularProgress, Link } from '@mui/material';
import { motion } from 'framer-motion';
import {
  ArrowBack,
  LocationOn,
  Phone,
  Email,
  Language,
  Store,
  Inventory,
  Info,
  ContactPhone,
  Map,
  ShoppingCart,
  Star,
  Business,
  CalendarMonth,
  Category
} from '@mui/icons-material';
import { useSuppliers } from '../../contexts/SupplierContext';
import { Supplier, SupplierProduct, SupplierLocation, SupplierContact } from '../../types/commercial/suppliers';
import { getConsistentBackground } from '../../utils/backgroundThemes';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`supplier-tabpanel-${index}`}
      aria-labelledby={`supplier-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `supplier-tab-${index}`,
    'aria-controls': `supplier-tabpanel-${index}`,
  };
};

const SupplierDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { getSupplierById, getSupplierProducts, loadingProducts } = useSuppliers();
  
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [products, setProducts] = useState<SupplierProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    const fetchSupplierData = async () => {
      try {
        if (id) {
          const supplierData = await getSupplierById(id);
          setSupplier(supplierData);
          
          const productsData = await getSupplierProducts(id);
          setProducts(productsData);
        }
      } catch (error) {
        console.error('Error fetching supplier details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSupplierData();
  }, [id, getSupplierById, getSupplierProducts]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleBack = () => {
    navigate('/commercial/suppliers');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!supplier) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" color="error">Supplier not found</Typography>
        <CustomButton startIcon={<ArrowBack />} onClick={handleBack} sx={{ mt: 2 }}>
          Back to Suppliers
        </CustomButton>
      </Box>
    );
  }

  const getPrimaryContact = (type: 'phone' | 'email' | 'website' | 'address' | 'social'): string => {
    const contact = supplier.contacts.find(c => c.type === type);
    return contact ? contact.value : '';
  };

  const getPrimaryLocation = (): SupplierLocation | undefined => {
    return supplier.locations.length > 0 ? supplier.locations[0] : undefined;
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <CustomButton 
          startIcon={<ArrowBack />} 
          onClick={handleBack}
          sx={{ mb: 2 }}
        >
          Back to Suppliers
        </CustomButton>

        {/* Header */}
        <Box
          sx={{
            position: 'relative',
            height: 200,
            mb: 8,
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          }}
        >
          <Box
            component="img"
            src={supplier.coverImage || 'https://source.unsplash.com/random/?farm,agriculture'}
            alt={supplier.name}
            sx={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: `linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7))`,
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -40,
              left: 24,
              display: 'flex',
              alignItems: 'flex-end',
            }}
          >
            <Avatar
              src={supplier.logo}
              alt={supplier.name}
              sx={{
                width: 80,
                height: 80,
                border: `4px solid ${theme.palette.background.paper}`,
                boxShadow: '0 4px 10px rgba(0,0,0,0.2)',
                bgcolor: 'white'
              }}
            />
            <Box sx={{ ml: 2, mb: 1 }}>
              <Typography variant="h5" color="white" fontWeight="bold">
                {supplier.name}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Rating value={supplier.rating || 0} precision={0.5} size="small" readOnly />
                <Typography variant="body2" color="white" sx={{ ml: 1 }}>
                  ({supplier.rating || 0})
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              bottom: 16,
              right: 16,
              display: 'flex',
              gap: 1
            }}
          >
            {supplier.categories.map((category, index) => (
              <Chip
                key={index}
                label={category.replace('-', ' ')}
                size="small"
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.9),
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Tabs */}
        <Paper sx={{ mb: 3, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.05)' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                minWidth: 120,
                fontWeight: 'bold',
              }
            }}
          >
            <Tab icon={<Info />} label="Overview" {...a11yProps(0)} />
            <Tab icon={<Inventory />} label="Products" {...a11yProps(1)} />
            <Tab icon={<Map />} label="Locations" {...a11yProps(2)} />
            <Tab icon={<ContactPhone />} label="Contact" {...a11yProps(3)} />
          </Tabs>

          {/* Overview Tab */}
          <TabPanel value={tabValue} index={0}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Grid container spacing={3}>
                <Grid item xs={12} md={8}>
                  <Card sx={{ 
                    mb: 3, 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.primary.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.primary.main}`
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>About</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Typography variant="body1" paragraph>
                        {supplier.description}
                      </Typography>
                      
                      <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Business sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              Established: {supplier.established || 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <CalendarMonth sx={{ mr: 1, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              Last Order: {supplier.lastOrder || 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>

                  <Card sx={{ 
                    mb: 3, 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.info.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.info.main}`
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Services</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {supplier.services && supplier.services.length > 0 ? (
                          supplier.services.map((service, index) => (
                            <Chip 
                              key={index} 
                              label={service} 
                              size="small" 
                              sx={{ 
                                bgcolor: alpha(theme.palette.info.main, 0.1),
                                color: theme.palette.info.dark
                              }} 
                            />
                          ))
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No services listed
                          </Typography>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={4}>
                  <Card sx={{ 
                    mb: 3, 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.success.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.success.main}`
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Quick Contact</Typography>
                      <Divider sx={{ mb: 2 }} />
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <LocationOn sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2">
                          {getPrimaryLocation()?.address || 'Address not available'}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2">
                          {getPrimaryContact('phone') || 'Phone not available'}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <Email sx={{ mr: 1, color: 'text.secondary' }} />
                        <Typography variant="body2">
                          {getPrimaryContact('email') || 'Email not available'}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Language sx={{ mr: 1, color: 'text.secondary' }} />
                        <Link href={supplier.website} target="_blank" rel="noopener noreferrer">
                          {supplier.website}
                        </Link>
                      </Box>
                    </CardContent>
                  </Card>

                  <Card sx={{ 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.warning.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.warning.main}`
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Categories</Typography>
                      <Divider sx={{ mb: 2 }} />
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {supplier.categories.map((category, index) => (
                          <Chip 
                            key={index} 
                            icon={<Category fontSize="small" />}
                            label={category.replace('-', ' ')} 
                            size="small" 
                            sx={{ 
                              bgcolor: alpha(theme.palette.warning.main, 0.1),
                              color: theme.palette.warning.dark
                            }} 
                          />
                        ))}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          </TabPanel>

          {/* Products Tab */}
          <TabPanel value={tabValue} index={1}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h6" gutterBottom>Products</Typography>
              <Divider sx={{ mb: 3 }} />
              
              {loadingProducts ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : products.length > 0 ? (
                <Grid container spacing={3}>
                  {products.map((product) => (
                    <Grid item xs={12} sm={6} md={4} key={product.id}>
                      <Card sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        borderRadius: 2,
                        transition: 'transform 0.3s, box-shadow 0.3s',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                        }
                      }}>
                        {product.imageUrl && (
                          <Box sx={{ position: 'relative', pt: '56.25%' }}>
                            <Box
                              component="img"
                              src={product.imageUrl}
                              alt={product.name}
                              sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                width: '100%',
                                height: '100%',
                                objectFit: 'cover',
                              }}
                            />
                          </Box>
                        )}
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" gutterBottom>{product.name}</Typography>
                          <Chip 
                            label={product.category.replace('-', ' ')} 
                            size="small" 
                            sx={{ mb: 2 }} 
                          />
                          <Typography variant="body2" color="text.secondary" paragraph>
                            {product.description}
                          </Typography>
                          
                          {product.price && (
                            <Typography variant="h6" color="primary" sx={{ mt: 'auto' }}>
                              {product.currency || 'R'} {product.price.toFixed(2)}
                            </Typography>
                          )}
                          
                          {product.availability && (
                            <Chip 
                              label={product.availability} 
                              size="small"
                              color={
                                product.availability === 'in-stock' 
                                  ? 'success' 
                                  : product.availability === 'limited' 
                                    ? 'warning' 
                                    : 'error'
                              }
                              sx={{ mt: 1 }}
                            />
                          )}
                        </CardContent>
                        {product.url && (
                          <Box sx={{ p: 2, pt: 0 }}>
                            <CustomButton 
                              variant="outlined" 
                              size="small" 
                              startIcon={<ShoppingCart />}
                              href={product.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              fullWidth
                            >
                              View Product
                            </CustomButton>
                          </Box>
                        )}
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  No products available for this supplier.
                </Typography>
              )}
            </motion.div>
          </TabPanel>

          {/* Locations Tab */}
          <TabPanel value={tabValue} index={2}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h6" gutterBottom>Locations</Typography>
              <Divider sx={{ mb: 3 }} />
              
              {supplier.locations.length > 0 ? (
                <Grid container spacing={3}>
                  {supplier.locations.map((location) => (
                    <Grid item xs={12} md={6} key={location.id}>
                      <Card sx={{ 
                        borderRadius: 2,
                        boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                        transition: 'transform 0.3s, box-shadow 0.3s',
                        '&:hover': {
                          transform: 'translateY(-5px)',
                          boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                        }
                      }}>
                        <CardContent>
                          <Typography variant="h6" gutterBottom>{location.name}</Typography>
                          <Divider sx={{ mb: 2 }} />
                          
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                            <LocationOn sx={{ mr: 1, mt: 0.5, color: 'text.secondary' }} />
                            <Typography variant="body2">
                              {location.address}
                            </Typography>
                          </Box>
                          
                          {location.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Phone sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                {location.phone}
                              </Typography>
                            </Box>
                          )}
                          
                          {location.email && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                              <Email sx={{ mr: 1, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                {location.email}
                              </Typography>
                            </Box>
                          )}
                          
                          {location.hours && (
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                              <CalendarMonth sx={{ mr: 1, mt: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2">
                                {location.hours}
                              </Typography>
                            </Box>
                          )}
                          
                          {location.coordinates && (
                            <CustomButton 
                              variant="outlined" 
                              size="small" 
                              startIcon={<Map />}
                              href={`https://www.google.com/maps/search/?api=1&query=${location.coordinates.latitude},${location.coordinates.longitude}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{ mt: 1 }}
                            >
                              View on Map
                            </CustomButton>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  No locations available for this supplier.
                </Typography>
              )}
            </motion.div>
          </TabPanel>

          {/* Contact Tab */}
          <TabPanel value={tabValue} index={3}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Typography variant="h6" gutterBottom>Contact Information</Typography>
              <Divider sx={{ mb: 3 }} />
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card sx={{ 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.primary.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                    mb: { xs: 3, md: 0 }
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Contact Details</Typography>
                      <Divider sx={{ mb: 2 }} />
                      
                      <List>
                        {supplier.contacts.map((contact, index) => (
                          <ListItem key={index} sx={{ px: 0 }}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                                {contact.type === 'phone' && <Phone color="primary" />}
                                {contact.type === 'email' && <Email color="primary" />}
                                {contact.type === 'website' && <Language color="primary" />}
                                {contact.type === 'address' && <LocationOn color="primary" />}
                                {contact.type === 'social' && <Language color="primary" />}
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText 
                              primary={contact.label || contact.type.charAt(0).toUpperCase() + contact.type.slice(1)} 
                              secondary={
                                contact.type === 'website' || contact.type === 'social' ? (
                                  <Link href={contact.value} target="_blank" rel="noopener noreferrer">
                                    {contact.value}
                                  </Link>
                                ) : (
                                  contact.value
                                )
                              } 
                            />
                          </ListItem>
                        ))}
                      </List>
                      
                      {supplier.contactPerson && (
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Primary Contact Person
                          </Typography>
                          <Typography variant="body2">
                            {supplier.contactPerson}
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Card sx={{ 
                    borderRadius: 2,
                    boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                    bgcolor: alpha(theme.palette.success.main, 0.03),
                    borderLeft: `4px solid ${theme.palette.success.main}`
                  }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Website</Typography>
                      <Divider sx={{ mb: 2 }} />
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Language sx={{ mr: 1, color: 'text.secondary' }} />
                        <Link 
                          href={supplier.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          sx={{ fontWeight: 'bold' }}
                        >
                          {supplier.website}
                        </Link>
                      </Box>
                      
                      <CustomButton 
                        variant="contained" 
                        color="primary" 
                        startIcon={<Store />}
                        href={supplier.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        fullWidth
                      >
                        Visit Website
                      </CustomButton>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          </TabPanel>
        </Paper>
      </motion.div>
    </Box>
  );
};

export default SupplierDetail;
