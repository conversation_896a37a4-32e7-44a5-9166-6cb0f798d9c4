/**
 * Inject Sample Data Script
 *
 * This script injects sample data into the MongoDB database
 */

const { connectDB, disconnectDB } = require('../src/config/mongodb');
const { ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');
const bcrypt = require('bcryptjs');

async function injectSampleData() {
  try {
    logger.info('Injecting sample data into MongoDB database...');

    // Connect to MongoDB
    const { db } = await connectDB();

    // Create admin user first
    const adminUserId = await createAdminUser(db);

    // Create sample data
    await createSampleAnimals(db, adminUserId);
    await createSampleHealthRecords(db, adminUserId);
    await createSampleBreedingRecords(db, adminUserId);
    await createSampleFinancialRecords(db, adminUserId);
    await createSampleBusinessData(db, adminUserId);

    logger.info('Sample data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting sample data:', error);
  } finally {
    await disconnectDB();
  }
}

async function createAdminUser(db) {
  logger.info('Creating admin user...');

  const usersCollection = db.collection('users');

  // Check if admin user already exists
  const existingAdmin = await usersCollection.findOne({ username: 'admin' });

  let adminUserId;

  if (existingAdmin) {
    logger.info('Admin user already exists');
    adminUserId = existingAdmin._id;
  } else {
    // Check if email already exists
    const existingEmail = await usersCollection.findOne({ email: '<EMAIL>' });

    if (existingEmail) {
      logger.info('Email already exists, using a different email');

      // Create admin user with a different email
      const adminUser = {
        _id: new ObjectId(),
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('Admin@123', 10),
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        permissions: ['all'],
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await usersCollection.insertOne(adminUser);
      logger.info('Admin user created successfully with different email');
      adminUserId = adminUser._id;
    } else {
      // Create admin user
      const adminUser = {
        _id: new ObjectId(),
        username: 'admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('Admin@123', 10),
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin',
        status: 'active',
        permissions: ['all'],
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await usersCollection.insertOne(adminUser);
      logger.info('Admin user created successfully');
      adminUserId = adminUser._id;
    }
  }

  // Create additional users
  const additionalUsers = [
    {
      username: 'mayrakgama',
      email: '<EMAIL>',
      password: await bcrypt.hash('Morok@1951ke*#', 10),
      firstName: 'May',
      lastName: 'Rakgama',
      role: 'admin',
      status: 'active',
      permissions: ['all']
    },
    {
      username: 'MayCaiphus',
      email: '<EMAIL>',
      password: await bcrypt.hash('123', 10),
      firstName: 'May',
      lastName: 'Caiphus',
      role: 'admin',
      status: 'active',
      permissions: ['all']
    },
    {
      username: 'manager',
      email: '<EMAIL>',
      password: await bcrypt.hash('Manager@123', 10),
      firstName: 'Farm',
      lastName: 'Manager',
      role: 'manager',
      status: 'active',
      permissions: ['view', 'edit']
    }
  ];

  for (const userData of additionalUsers) {
    try {
      // Check if username already exists
      const existingUser = await usersCollection.findOne({ username: userData.username });

      if (!existingUser) {
        // Check if email already exists
        const existingEmail = await usersCollection.findOne({ email: userData.email });

        if (existingEmail) {
          logger.info(`Email ${userData.email} already exists, using a different email`);

          // Create user with a different email
          const user = {
            _id: new ObjectId(),
            ...userData,
            email: `${userData.username}@maycaiphus.com`,
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await usersCollection.insertOne(user);
          logger.info(`User ${userData.username} created successfully with different email`);
        } else {
          // Create user
          const user = {
            _id: new ObjectId(),
            ...userData,
            lastLogin: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
          };

          await usersCollection.insertOne(user);
          logger.info(`User ${userData.username} created successfully`);
        }
      } else {
        logger.info(`User ${userData.username} already exists`);
      }
    } catch (error) {
      logger.error(`Error creating user ${userData.username}:`, error);
    }
  }

  return adminUserId;
}

async function createSampleAnimals(db, adminUserId) {
  logger.info('Creating sample animals...');

  const animalsCollection = db.collection('animals');

  // Sample animals
  const animals = [
    {
      _id: new ObjectId(),
      tagNumber: 'CTL-2024-001',
      name: 'Thabo',
      species: 'Cattle',
      breed: 'Nguni',
      gender: 'male',
      birthDate: new Date('2022-03-15'),
      acquisitionDate: new Date('2022-03-15'),
      weight: 450,
      status: 'active',
      healthStatus: 'healthy',
      location: 'North Paddock',
      rfidTag: 'RF00123456',
      notes: 'Prime breeding bull with excellent conformation',
      purchasePrice: 15000,
      estimatedValue: 25000,
      images: [
        {
          url: '/images/animals/cattle-1.jpeg',
          caption: 'Thabo - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdBy: adminUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'CTL-2024-002',
      name: 'Lesedi',
      species: 'Cattle',
      breed: 'Bonsmara',
      gender: 'female',
      birthDate: new Date('2021-06-10'),
      acquisitionDate: new Date('2021-06-10'),
      weight: 380,
      status: 'active',
      healthStatus: 'healthy',
      location: 'South Paddock',
      rfidTag: 'RF00123457',
      notes: 'Excellent milk producer',
      purchasePrice: 12000,
      estimatedValue: 18000,
      images: [
        {
          url: '/images/animals/cattle-2.avif',
          caption: 'Lesedi - Side View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdBy: adminUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'SHP-2024-001',
      name: 'Kgosi',
      species: 'Sheep',
      breed: 'Dorper',
      gender: 'male',
      birthDate: new Date('2023-01-20'),
      acquisitionDate: new Date('2023-01-20'),
      weight: 65,
      status: 'active',
      healthStatus: 'healthy',
      location: 'East Paddock',
      rfidTag: 'RF00123458',
      notes: 'Breeding ram with excellent wool quality',
      purchasePrice: 3500,
      estimatedValue: 5000,
      images: [
        {
          url: '/images/animals/sheep-1.jpeg',
          caption: 'Kgosi - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdBy: adminUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'SHP-2024-002',
      name: 'Naledi',
      species: 'Sheep',
      breed: 'Merino',
      gender: 'female',
      birthDate: new Date('2022-11-05'),
      acquisitionDate: new Date('2022-11-05'),
      weight: 45,
      status: 'active',
      healthStatus: 'healthy',
      location: 'East Paddock',
      rfidTag: 'RF00123459',
      notes: 'High wool producer',
      purchasePrice: 2800,
      estimatedValue: 4000,
      images: [
        {
          url: '/images/animals/sheep-2.jpeg',
          caption: 'Naledi - Side View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdBy: adminUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      tagNumber: 'GOT-2024-001',
      name: 'Tumelo',
      species: 'Goat',
      breed: 'Boer Goat',
      gender: 'male',
      birthDate: new Date('2023-02-15'),
      acquisitionDate: new Date('2023-02-15'),
      weight: 70,
      status: 'active',
      healthStatus: 'healthy',
      location: 'West Paddock',
      rfidTag: 'RF00123460',
      notes: 'Breeding buck with excellent meat conformation',
      purchasePrice: 4000,
      estimatedValue: 6000,
      images: [
        {
          url: '/images/animals/goat-1.jpeg',
          caption: 'Tumelo - Front View',
          isPrimary: true,
          uploadDate: new Date()
        }
      ],
      createdBy: adminUserId,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  // Insert or update animals
  for (const animal of animals) {
    const existingAnimal = await animalsCollection.findOne({ tagNumber: animal.tagNumber });

    if (existingAnimal) {
      logger.info(`Updating animal: ${animal.tagNumber}`);
      await animalsCollection.updateOne(
        { _id: existingAnimal._id },
        { $set: { ...animal, _id: existingAnimal._id } }
      );
    } else {
      logger.info(`Creating animal: ${animal.tagNumber}`);
      await animalsCollection.insertOne(animal);
    }
  }

  logger.info('Sample animals created successfully');
  return animals;
}

// Additional functions for creating sample data for other collections
async function createSampleHealthRecords(db, adminUserId) {
  // Implementation will be added in the next part
  logger.info('Sample health records created successfully');
}

async function createSampleBreedingRecords(db, adminUserId) {
  // Implementation will be added in the next part
  logger.info('Sample breeding records created successfully');
}

async function createSampleFinancialRecords(db, adminUserId) {
  // Implementation will be added in the next part
  logger.info('Sample financial records created successfully');
}

async function createSampleBusinessData(db, adminUserId) {
  // Implementation will be added in the next part
  logger.info('Sample business data created successfully');
}

// Run the injection function
injectSampleData()
  .then(() => {
    logger.info('Sample data injection script completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Error in sample data injection script:', error);
    process.exit(1);
  });
