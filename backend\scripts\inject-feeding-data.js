/**
 * Inject Feeding Data Script
 * 
 * This script injects realistic feeding records into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');
const { animals } = require('./inject-animal-data');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Feed items
const feedItems = [
  {
    name: 'Alfalfa Hay',
    type: 'hay',
    nutritionalInfo: {
      protein: 18,
      fat: 2.5,
      fiber: 25,
      calcium: 1.2,
      phosphorus: 0.3
    }
  },
  {
    name: '<PERSON><PERSON>',
    type: 'hay',
    nutritionalInfo: {
      protein: 8,
      fat: 3.2,
      fiber: 21,
      calcium: 0.3,
      phosphorus: 0.2
    }
  },
  {
    name: 'Dairy Concentrate',
    type: 'grain',
    nutritionalInfo: {
      protein: 22,
      fat: 4.5,
      fiber: 8,
      calcium: 1.0,
      phosphorus: 0.8
    }
  },
  {
    name: 'She<PERSON> Pellets',
    type: 'grain',
    nutritionalInfo: {
      protein: 16,
      fat: 3.0,
      fiber: 12,
      calcium: 0.9,
      phosphorus: 0.6
    }
  },
  {
    name: 'Mineral Supplement',
    type: 'supplement',
    nutritionalInfo: {
      calcium: 12.0,
      phosphorus: 8.0,
      magnesium: 3.5,
      sodium: 10.0
    }
  }
];

// Feeding records to inject
const feedingRecords = [
  {
    _id: new ObjectId(),
    animal: animals[0]._id, // Thabo (bull)
    feedItems: [
      {
        ...feedItems[0], // Alfalfa Hay
        quantity: 8,
        unit: 'kg',
        cost: 120
      },
      {
        ...feedItems[2], // Dairy Concentrate
        quantity: 3,
        unit: 'kg',
        cost: 75
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.1,
        unit: 'kg',
        cost: 15
      }
    ],
    feedingTime: new Date('2024-05-01T08:00:00'),
    feeder: new ObjectId(), // Will be updated with actual user ID
    location: 'North Paddock',
    status: 'completed',
    consumption: 'all',
    notes: 'Normal feeding, consumed all feed',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[1]._id, // Lesedi (cow)
    feedItems: [
      {
        ...feedItems[0], // Alfalfa Hay
        quantity: 7,
        unit: 'kg',
        cost: 105
      },
      {
        ...feedItems[2], // Dairy Concentrate
        quantity: 4,
        unit: 'kg',
        cost: 100
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.1,
        unit: 'kg',
        cost: 15
      }
    ],
    feedingTime: new Date('2024-05-01T08:15:00'),
    feeder: new ObjectId(), // Will be updated with actual user ID
    location: 'South Paddock',
    status: 'completed',
    consumption: 'all',
    notes: 'Increased concentrate for milk production',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[2]._id, // Kgosi (ram)
    feedItems: [
      {
        ...feedItems[1], // Corn Silage
        quantity: 2,
        unit: 'kg',
        cost: 30
      },
      {
        ...feedItems[3], // Sheep Pellets
        quantity: 1,
        unit: 'kg',
        cost: 25
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.05,
        unit: 'kg',
        cost: 7.5
      }
    ],
    feedingTime: new Date('2024-05-01T09:00:00'),
    feeder: new ObjectId(), // Will be updated with actual user ID
    location: 'East Paddock',
    status: 'completed',
    consumption: 'all',
    notes: 'Normal feeding, consumed all feed',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[3]._id, // Naledi (ewe)
    feedItems: [
      {
        ...feedItems[1], // Corn Silage
        quantity: 1.5,
        unit: 'kg',
        cost: 22.5
      },
      {
        ...feedItems[3], // Sheep Pellets
        quantity: 1,
        unit: 'kg',
        cost: 25
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.05,
        unit: 'kg',
        cost: 7.5
      }
    ],
    feedingTime: new Date('2024-05-01T09:15:00'),
    feeder: new ObjectId(), // Will be updated with actual user ID
    location: 'East Paddock',
    status: 'completed',
    consumption: 'all',
    notes: 'Normal feeding, consumed all feed',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animal: animals[4]._id, // Tumelo (buck)
    feedItems: [
      {
        ...feedItems[0], // Alfalfa Hay
        quantity: 3,
        unit: 'kg',
        cost: 45
      },
      {
        ...feedItems[3], // Sheep Pellets (also used for goats)
        quantity: 1.5,
        unit: 'kg',
        cost: 37.5
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.05,
        unit: 'kg',
        cost: 7.5
      }
    ],
    feedingTime: new Date('2024-05-01T09:30:00'),
    feeder: new ObjectId(), // Will be updated with actual user ID
    location: 'West Paddock',
    status: 'completed',
    consumption: 'all',
    notes: 'Normal feeding, consumed all feed',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Feeding schedules to inject
const feedingSchedules = [
  {
    _id: new ObjectId(),
    name: 'Cattle Feeding Schedule',
    description: 'Standard feeding schedule for cattle',
    animals: [animals[0]._id, animals[1]._id], // Thabo and Lesedi
    animalGroups: ['Cattle'],
    feedItems: [
      {
        ...feedItems[0], // Alfalfa Hay
        quantity: 7,
        unit: 'kg',
        cost: 105
      },
      {
        ...feedItems[2], // Dairy Concentrate
        quantity: 3,
        unit: 'kg',
        cost: 75
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.1,
        unit: 'kg',
        cost: 15
      }
    ],
    schedule: {
      frequency: 'daily',
      times: ['08:00', '16:00'],
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // Every day
      startDate: new Date('2024-01-01'),
      endDate: null
    },
    isActive: true,
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Small Ruminant Feeding Schedule',
    description: 'Standard feeding schedule for sheep and goats',
    animals: [animals[2]._id, animals[3]._id, animals[4]._id], // Kgosi, Naledi, and Tumelo
    animalGroups: ['Sheep', 'Goat'],
    feedItems: [
      {
        ...feedItems[1], // Corn Silage
        quantity: 2,
        unit: 'kg',
        cost: 30
      },
      {
        ...feedItems[3], // Sheep Pellets
        quantity: 1,
        unit: 'kg',
        cost: 25
      },
      {
        ...feedItems[4], // Mineral Supplement
        quantity: 0.05,
        unit: 'kg',
        cost: 7.5
      }
    ],
    schedule: {
      frequency: 'daily',
      times: ['09:00', '17:00'],
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // Every day
      startDate: new Date('2024-01-01'),
      endDate: null
    },
    isActive: true,
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject feeding data
async function injectFeedingData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const feedingRecordsCollection = db.collection('feedingrecords');
    const feedingSchedulesCollection = db.collection('feedingschedules');
    const usersCollection = db.collection('users');

    // Get a user ID for the performer field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const performerId = adminUser ? adminUser._id : new ObjectId();

    // Insert or update feeding records
    for (const record of feedingRecords) {
      // Set the feeder to an actual user
      record.feeder = performerId;
      
      const existingRecord = await feedingRecordsCollection.findOne({ 
        animal: record.animal,
        feedingTime: record.feedingTime
      });

      if (existingRecord) {
        logger.info(`Updating feeding record: ${record._id}`);
        await feedingRecordsCollection.updateOne(
          { _id: existingRecord._id },
          { $set: { ...record, _id: existingRecord._id } }
        );
      } else {
        logger.info(`Inserting new feeding record: ${record._id}`);
        await feedingRecordsCollection.insertOne(record);
      }
    }

    // Insert or update feeding schedules
    for (const schedule of feedingSchedules) {
      // Set the creator to an actual user
      schedule.createdBy = performerId;
      
      const existingSchedule = await feedingSchedulesCollection.findOne({ 
        name: schedule.name
      });

      if (existingSchedule) {
        logger.info(`Updating feeding schedule: ${schedule.name}`);
        await feedingSchedulesCollection.updateOne(
          { _id: existingSchedule._id },
          { $set: { ...schedule, _id: existingSchedule._id } }
        );
      } else {
        logger.info(`Inserting new feeding schedule: ${schedule.name}`);
        await feedingSchedulesCollection.insertOne(schedule);
      }
    }

    logger.info('Feeding data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting feeding data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectFeedingData()
  .then(() => {
    logger.info('Feeding data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in feeding data injection script:', error);
  });

module.exports = { feedingRecords, feedingSchedules };
