const express = require('express');
const cors = require('cors');
const path = require('path');

// Simple logger for demo
const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`),
  warn: (msg) => console.warn(`[WARN] ${new Date().toISOString()} - ${msg}`)
};

const app = express();
const PORT = process.env.PORT || 3002;

// CORS configuration
const corsOptions = {
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};

app.use(cors(corsOptions));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Simple health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Simple server is running' });
});

// API status
app.get('/api/status', (req, res) => {
  res.json({ status: 'ok', message: 'API is running' });
});

// In-memory storage for demo purposes with default users
let users = [
  {
    id: 'demo-user-id',
    username: 'Demo',
    password: '123',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'beta',
    permissions: ['view_animals', 'create_animal', 'view_health_records', 'view_resources'],
    subscriptionTier: 'beta',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: 'pro-user-id',
    username: 'Pro',
    password: '123',
    email: '<EMAIL>',
    firstName: 'Professional',
    lastName: 'User',
    role: 'professional',
    permissions: ['all'],
    subscriptionTier: 'professional',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: 'admin-user-id',
    username: 'admin',
    password: 'Admin@123',
    email: '<EMAIL>',
    firstName: 'Administrator',
    lastName: 'User',
    role: 'admin',
    permissions: ['all'],
    subscriptionTier: 'enterprise',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  }
];

let animals = [];
let healthRecords = [];
let breedingRecords = [];

// Auth routes
app.post('/api/auth/register', (req, res) => {
  try {
    const userData = req.body;

    // Check if user already exists
    const existingUser = users.find(u => u.username === userData.username || u.email === userData.email);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Create new user
    const newUser = {
      id: Date.now().toString(),
      ...userData,
      createdAt: new Date().toISOString(),
      status: 'active'
    };

    users.push(newUser);

    res.json({
      success: true,
      message: 'Registration successful',
      data: { id: newUser.id, username: newUser.username }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Registration failed',
      error: error.message
    });
  }
});

app.post('/api/auth/login', (req, res) => {
  try {
    const { username, password } = req.body;

    logger.info(`Login attempt for username: ${username}`);

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required'
      });
    }

    const user = users.find(u => u.username === username && u.password === password);
    if (!user) {
      logger.warn(`Login failed for username: ${username}`);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    // Update last login
    user.lastLogin = new Date().toISOString();

    logger.info(`Login successful for username: ${username}`);

    res.json({
      success: true,
      user: { ...user, password: undefined },
      token: `demo-token-${user.id}-${Date.now()}`,
      message: 'Login successful'
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Subscription routes (minimal)
app.get('/api/subscription/plans', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 'beta',
        name: 'Beta',
        price: 0,
        features: ['50 animals', 'Basic features']
      },
      {
        id: 'professional',
        name: 'Professional',
        price: 299,
        features: ['Unlimited animals', 'Advanced features']
      }
    ]
  });
});

app.get('/api/subscription/current', (req, res) => {
  res.json({
    success: true,
    data: {
      plan: { name: 'beta' },
      usage: { animals: 25, limit: 50 },
      user: { role: 'user', username: 'demo' }
    }
  });
});

app.post('/api/subscription/check-access', (req, res) => {
  res.json({
    success: true,
    allowed: true,
    reason: 'Demo access granted'
  });
});

// Animals CRUD
app.get('/api/animals', (req, res) => {
  res.json({
    success: true,
    data: animals
  });
});

app.post('/api/animals', (req, res) => {
  const newAnimal = {
    id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString()
  };
  animals.push(newAnimal);
  res.json({
    success: true,
    data: newAnimal
  });
});

app.put('/api/animals/:id', (req, res) => {
  const index = animals.findIndex(a => a.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ success: false, message: 'Animal not found' });
  }
  animals[index] = { ...animals[index], ...req.body, updatedAt: new Date().toISOString() };
  res.json({
    success: true,
    data: animals[index]
  });
});

app.delete('/api/animals/:id', (req, res) => {
  const index = animals.findIndex(a => a.id === req.params.id);
  if (index === -1) {
    return res.status(404).json({ success: false, message: 'Animal not found' });
  }
  animals.splice(index, 1);
  res.json({
    success: true,
    message: 'Animal deleted successfully'
  });
});

// Health Records CRUD
app.get('/api/health/records', (req, res) => {
  res.json({
    success: true,
    data: healthRecords
  });
});

app.post('/api/health/records', (req, res) => {
  const newRecord = {
    id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString()
  };
  healthRecords.push(newRecord);
  res.json({
    success: true,
    data: newRecord
  });
});

// Breeding Records CRUD
app.get('/api/breeding/records', (req, res) => {
  res.json({
    success: true,
    data: breedingRecords
  });
});

app.post('/api/breeding/records', (req, res) => {
  const newRecord = {
    id: Date.now().toString(),
    ...req.body,
    createdAt: new Date().toISOString()
  };
  breedingRecords.push(newRecord);
  res.json({
    success: true,
    data: newRecord
  });
});

// Additional endpoints for comprehensive functionality
app.get('/api/feeding/records', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', animalId: '1', feedType: 'Hay', quantity: 5, unit: 'kg', date: new Date().toISOString() },
      { id: '2', animalId: '2', feedType: 'Grain', quantity: 2, unit: 'kg', date: new Date().toISOString() }
    ]
  });
});

app.get('/api/financial/records', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', type: 'income', amount: 5000, description: 'Cattle sale', date: new Date().toISOString() },
      { id: '2', type: 'expense', amount: 1200, description: 'Feed purchase', date: new Date().toISOString() }
    ]
  });
});

app.get('/api/inventory/items', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: '1', name: 'Feed Mixer', category: 'equipment', quantity: 1, value: 25000 },
      { id: '2', name: 'Cattle Feed', category: 'supplies', quantity: 500, value: 15000 }
    ]
  });
});

app.get('/api/reports/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      totalAnimals: animals.length,
      healthRecords: healthRecords.length,
      breedingRecords: breedingRecords.length,
      monthlyIncome: 25000,
      monthlyExpenses: 8000
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: err.message
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found'
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`Simple server running on port ${PORT}`);
  console.log(`Simple server running on port ${PORT}`);
});
