import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ComplianceDashboard from './compliance/ComplianceDashboard';
import Documents from './compliance/Documents';
import Inspections from './compliance/Inspections';
import Certifications from './compliance/Certifications';
import BrandmarksCertificates from '../components/modules/compliance/BrandmarksCertificates';

const Compliance: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ComplianceDashboard />} />
      <Route path="/dashboard" element={<ComplianceDashboard />} />
      <Route path="/certifications" element={<Certifications />} />
      <Route path="/inspections" element={<Inspections />} />
      <Route path="/documents" element={<Documents />} />
      <Route path="/brandmarks" element={<BrandmarksCertificates />} />
    </Routes>
  );
};

export default Compliance;