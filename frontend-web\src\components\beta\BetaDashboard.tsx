import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha,
  Divider
} from '@mui/material';
import {
  Star,
  TrendingUp,
  Lock,
  CheckCircle,
  Warning,
  Pets,
  LocalHospital,
  AccountBalance,
  Analytics,
  SmartToy,
  CloudDownload,
  Support,
  Timer,
  LocationOn,
  Restaurant,
  Settings
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useSubscription } from '../../contexts/SubscriptionContext';
import BetaFeatureGate from './BetaFeatureGate';
import '../../styles/beta-tier.css';

interface BetaDashboardProps {
  userProvince?: string;
}

const BetaDashboard: React.FC<BetaDashboardProps> = ({ userProvince = 'gauteng' }) => {
  const theme = useTheme();
  const { subscription, getUsagePercentage, isTrialExpiring, getDaysLeft } = useSubscription();
  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [provinceData, setProvinceData] = useState<any>(null);

  useEffect(() => {
    fetchProvinceData();
  }, [userProvince]);

  const fetchProvinceData = async () => {
    try {
      const response = await fetch(`/api/subscription/province/${userProvince}`);
      const data = await response.json();
      if (data.success) {
        setProvinceData(data.data);
      }
    } catch (error) {
      console.error('Error fetching province data:', error);
    }
  };

  const betaFeatures = [
    {
      name: 'Animal Inventory',
      icon: Pets,
      description: 'Track up to 50 animals with basic health records',
      status: 'included',
      usage: getUsagePercentage('animals'),
      limit: '50 animals max'
    },
    {
      name: 'Health Records',
      icon: LocalHospital,
      description: 'Basic health logging with vaccination reminders',
      status: 'included',
      usage: 0,
      limit: 'Basic features only'
    },
    {
      name: 'Government Data',
      icon: LocationOn,
      description: 'Real-time auction prices and vaccination calendar',
      status: 'included',
      usage: 0,
      limit: 'Full access'
    },
    {
      name: 'Financial Tracking',
      icon: AccountBalance,
      description: 'Expense input only - no analysis or projections',
      status: 'limited',
      usage: 0,
      limit: 'Input only'
    }
  ];

  const premiumFeatures = [
    {
      name: 'AI Agent Mode',
      icon: SmartToy,
      description: 'Voice-activated farm management with one-click automation',
      benefits: ['Voice commands', 'Auto-ordering', 'Smart scheduling'],
      savings: 'R8,000/month in time savings'
    },
    {
      name: 'Advanced Analytics',
      icon: Analytics,
      description: 'Predictive insights and performance optimization',
      benefits: ['Predictive health alerts', 'Market timing', 'ROI optimization'],
      savings: 'R12,000/month in improved decisions'
    },
    {
      name: 'Financial Intelligence',
      icon: TrendingUp,
      description: 'Complete profit/loss analysis and tax optimization',
      benefits: ['P&L analysis', 'Tax optimization', 'Banking integration'],
      savings: 'R15,000/year in tax savings'
    },
    {
      name: 'Priority Support',
      icon: Support,
      description: '24/7 expert support with dedicated account manager',
      benefits: ['24/7 support', 'Dedicated manager', 'Training sessions'],
      savings: 'Priceless peace of mind'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'included': return 'success';
      case 'limited': return 'warning';
      case 'locked': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'included': return 'Included';
      case 'limited': return 'Limited';
      case 'locked': return 'Premium Only';
      default: return 'Unknown';
    }
  };

  return (
    <Box className="beta-dashboard-container">
      {/* BETA V1 Dashboard Header */}
      <Box className="beta-dashboard-header">
        <Box className="beta-dashboard-title">
          <Typography variant="h4" sx={{ fontWeight: 800, color: 'white' }}>
            AgriIntel BETA V1 Dashboard
          </Typography>
          <Chip
            label="Starter Package"
            className="beta-tier-badge"
            icon={<Star />}
          />
        </Box>
        <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.9)', mt: 1 }}>
          Welcome to your free trial! Manage up to 50 animals with our core features.
          {isTrialExpiring() && ` Trial expires in ${getDaysLeft()} days.`}
        </Typography>
      </Box>

      <Box sx={{ p: 3 }}>
        {/* Beta Status Banner */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Alert
            severity={isTrialExpiring() ? "warning" : "info"}
            sx={{
              mb: 3,
              fontSize: '1.1rem',
              background: 'var(--beta-gradient-primary)',
              color: 'white',
              '& .MuiAlert-icon': {
                color: 'white'
              }
            }}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => setShowUpgradeDialog(true)}
                sx={{ fontWeight: 'bold', color: 'white' }}
              >
                Upgrade Now
              </Button>
            }
          >
            <Typography variant="body1" fontWeight="bold">
              {isTrialExpiring()
                ? `🚨 Trial expires in ${getDaysLeft()} days - Upgrade to keep your data!`
                : `🎯 BETA V1 Active - You have ${getDaysLeft()} days left in your trial.`
              }
            </Typography>
          </Alert>
        </motion.div>

      {/* Province-Specific Welcome */}
      {provinceData && (
        <Card sx={{ mb: 3, background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`, color: 'white' }}>
          <CardContent>
            <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
              Welcome, {provinceData.name} Farmer!
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  <strong>{provinceData.demographics.totalFarms.toLocaleString()}</strong> farms in your province
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Average <strong>{provinceData.demographics.avgAnimals}</strong> animals per farm
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Current cattle price: <strong>R{provinceData.marketPrices.cattle.toLocaleString()}</strong>
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}

      <Grid container spacing={3}>
        {/* Beta Features */}
        <Grid item xs={12} lg={8}>
          <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
            Your Beta Features
          </Typography>
          
          <Grid container spacing={2}>
            {betaFeatures.map((feature, index) => (
              <Grid item xs={12} sm={6} key={feature.name}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="beta-module-card" sx={{ height: '100%' }}>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <feature.icon className="beta-module-icon" sx={{ mr: 2, fontSize: '2rem' }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6" className="beta-module-title" fontWeight="bold">
                            {feature.name}
                          </Typography>
                          <Chip
                            label={getStatusText(feature.status)}
                            color={getStatusColor(feature.status) as any}
                            size="small"
                            sx={{
                              backgroundColor: feature.status === 'included' ? '#4CAF50' :
                                             feature.status === 'limited' ? '#FF9800' : '#F44336',
                              color: 'white',
                              fontWeight: 600
                            }}
                          />
                        </Box>
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        {feature.description}
                      </Typography>
                      
                      {feature.usage > 0 && (
                        <Box sx={{ mb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">Usage</Typography>
                            <Typography variant="body2">{feature.usage.toFixed(0)}%</Typography>
                          </Box>
                          <LinearProgress 
                            variant="determinate" 
                            value={feature.usage} 
                            color={feature.usage > 80 ? 'warning' : 'primary'}
                          />
                        </Box>
                      )}
                      
                      <Typography variant="caption" color="text.secondary">
                        {feature.limit}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Upgrade Incentive */}
        <Grid item xs={12} lg={4}>
          <Card 
            sx={{ 
              height: '100%',
              background: `linear-gradient(135deg, ${alpha(theme.palette.secondary.main, 0.1)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
              border: `2px solid ${alpha(theme.palette.secondary.main, 0.3)}`
            }}
          >
            <CardContent>
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Star sx={{ fontSize: 48, color: theme.palette.secondary.main, mb: 1 }} />
                <Typography variant="h5" fontWeight="bold" color="secondary">
                  Unlock Premium
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Save R15,000+ annually with advanced features
                </Typography>
              </Box>

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Unlimited animals"
                    secondary="No more 50-animal limit"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="AI automation"
                    secondary="Voice commands & auto-ordering"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Financial intelligence"
                    secondary="Complete P&L analysis"
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <CheckCircle sx={{ color: 'success.main', fontSize: 20 }} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Priority support"
                    secondary="24/7 expert assistance"
                  />
                </ListItem>
              </List>

              <Button
                variant="contained"
                color="secondary"
                fullWidth
                size="large"
                onClick={() => setShowUpgradeDialog(true)}
                sx={{ mt: 2 }}
              >
                Upgrade for R299/month
              </Button>

              <Typography variant="caption" display="block" textAlign="center" sx={{ mt: 1 }}>
                First month free for beta users!
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Premium Features Preview */}
      <Box sx={{ mt: 4 }}>
        <Typography variant="h5" fontWeight="bold" sx={{ mb: 3 }}>
          Premium Features Preview
        </Typography>
        
        <Grid container spacing={3}>
          {premiumFeatures.map((feature, index) => (
            <Grid item xs={12} md={6} key={feature.name}>
              <BetaFeatureGate
                feature={feature.name.toLowerCase().replace(/\s+/g, '_')}
                showPreview={true}
                upgradeMessage={`Unlock ${feature.name} to ${feature.savings}`}
              >
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <feature.icon sx={{ mr: 2, color: theme.palette.primary.main }} />
                      <Typography variant="h6" fontWeight="bold">
                        {feature.name}
                      </Typography>
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {feature.description}
                    </Typography>
                    
                    <List dense>
                      {feature.benefits.map((benefit, idx) => (
                        <ListItem key={idx} sx={{ px: 0 }}>
                          <ListItemIcon sx={{ minWidth: 32 }}>
                            <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                          </ListItemIcon>
                          <ListItemText primary={benefit} />
                        </ListItem>
                      ))}
                    </List>
                    
                    <Alert severity="success" sx={{ mt: 2 }}>
                      <Typography variant="body2" fontWeight="bold">
                        {feature.savings}
                      </Typography>
                    </Alert>
                  </CardContent>
                </Card>
              </BetaFeatureGate>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Upgrade Dialog */}
      <Dialog
        open={showUpgradeDialog}
        onClose={() => setShowUpgradeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            Upgrade to Premium
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Alert severity="success" sx={{ mb: 3 }}>
            <Typography variant="body1" fontWeight="bold">
              Special Beta Offer: First month FREE + 20% off first year!
            </Typography>
          </Alert>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{ border: `2px solid ${theme.palette.primary.main}` }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="primary">
                    Professional
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                    R299<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Perfect for growing farms (up to 200 animals)
                  </Typography>
                  <Button variant="contained" fullWidth>
                    Choose Professional
                  </Button>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Card sx={{ border: `2px solid ${theme.palette.secondary.main}` }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Typography variant="h6" fontWeight="bold" color="secondary">
                      Enterprise
                    </Typography>
                    <Chip label="Most Popular" size="small" color="secondary" />
                  </Box>
                  <Typography variant="h4" fontWeight="bold" sx={{ my: 1 }}>
                    R599<Typography component="span" variant="body2">/month</Typography>
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    For serious operations (unlimited animals)
                  </Typography>
                  <Button variant="contained" color="secondary" fullWidth>
                    Choose Enterprise
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowUpgradeDialog(false)}>
            Maybe Later
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaDashboard;
