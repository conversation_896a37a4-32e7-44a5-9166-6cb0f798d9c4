/**
 * Livestock Insights Panel
 * 
 * AI-powered insights and recommendations for livestock management
 * Features:
 * - Predictive analytics
 * - Health alerts
 * - Breeding recommendations
 * - Financial optimization
 * - Weather-based suggestions
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  IconButton,
  Collapse,
  useTheme,
  alpha,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Psychology,
  TrendingUp,
  Warning,
  Lightbulb,
  ExpandMore,
  ExpandLess,
  MonitorHeart,
  Thermostat,
  WaterDrop,
  Agriculture,
  AttachMoney,
  Pets
} from '../../utils/iconImports';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface Insight {
  id: string;
  type: 'health' | 'breeding' | 'financial' | 'weather' | 'feed' | 'prediction';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  confidence: number;
  impact: string;
  timeframe: string;
  icon: React.ReactNode;
  color: string;
}

interface LivestockInsightsPanelProps {
  module?: string;
  animalData?: any[];
  compact?: boolean;
}

const LivestockInsightsPanel: React.FC<LivestockInsightsPanelProps> = ({
  module = 'dashboard',
  animalData = [],
  compact = false
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [insights, setInsights] = useState<Insight[]>([]);
  const [expandedInsight, setExpandedInsight] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Generate AI-powered insights based on data
  useEffect(() => {
    const generateInsights = () => {
      const newInsights: Insight[] = [];

      // Health Insights
      if (animalData.length > 0) {
        const sickAnimals = animalData.filter(animal => 
          animal.healthStatus && animal.healthStatus.toLowerCase() !== 'healthy'
        ).length;
        
        if (sickAnimals > 0) {
          newInsights.push({
            id: 'health-alert',
            type: 'health',
            priority: sickAnimals > 5 ? 'critical' : 'high',
            title: `${sickAnimals} Animals Need Attention`,
            description: `${sickAnimals} animals are showing signs of illness or injury`,
            recommendation: 'Schedule veterinary checkups and isolate affected animals',
            confidence: 95,
            impact: 'High - Prevent disease spread',
            timeframe: 'Immediate action required',
            icon: <MonitorHeart />,
            color: theme.palette.error.main
          });
        }

        // Breeding Insights
        const breedingAge = animalData.filter(animal => {
          if (!animal.birthDate) return false;
          const age = new Date().getFullYear() - new Date(animal.birthDate).getFullYear();
          return age >= 2 && age <= 8 && animal.gender === 'female';
        }).length;

        if (breedingAge > 0) {
          newInsights.push({
            id: 'breeding-opportunity',
            type: 'breeding',
            priority: 'medium',
            title: `${breedingAge} Animals Ready for Breeding`,
            description: 'Optimal breeding candidates identified based on age and health',
            recommendation: 'Plan breeding schedule for maximum genetic diversity',
            confidence: 88,
            impact: 'Medium - Improve herd genetics',
            timeframe: 'Next 3 months',
            icon: <Pets />,
            color: theme.palette.success.main
          });
        }
      }

      // Financial Insights
      newInsights.push({
        id: 'cost-optimization',
        type: 'financial',
        priority: 'medium',
        title: 'Feed Cost Optimization Opportunity',
        description: 'Bulk purchasing could reduce feed costs by 15%',
        recommendation: 'Consider seasonal bulk feed purchases during harvest',
        confidence: 82,
        impact: 'Medium - Save R15,000 annually',
        timeframe: 'Next purchase cycle',
        icon: <AttachMoney />,
        color: theme.palette.warning.main
      });

      // Weather-based Insights
      newInsights.push({
        id: 'weather-alert',
        type: 'weather',
        priority: 'high',
        title: 'Drought Conditions Expected',
        description: 'Weather patterns suggest reduced rainfall in coming months',
        recommendation: 'Increase water storage and consider supplemental feeding',
        confidence: 76,
        impact: 'High - Ensure animal welfare',
        timeframe: 'Next 2 months',
        icon: <WaterDrop />,
        color: theme.palette.info.main
      });

      // Predictive Insights
      newInsights.push({
        id: 'market-prediction',
        type: 'prediction',
        priority: 'low',
        title: 'Market Price Increase Predicted',
        description: 'Cattle prices expected to rise 12% in Q2',
        recommendation: 'Consider delaying sales until price peak',
        confidence: 71,
        impact: 'Medium - Increase revenue by R50,000',
        timeframe: '3-6 months',
        icon: <TrendingUp />,
        color: theme.palette.primary.main
      });

      // Feed Optimization
      newInsights.push({
        id: 'feed-optimization',
        type: 'feed',
        priority: 'medium',
        title: 'Nutrition Profile Optimization',
        description: 'Current feed mix could be improved for better weight gain',
        recommendation: 'Adjust protein content to 16% for optimal growth',
        confidence: 84,
        impact: 'Medium - Improve growth rates by 8%',
        timeframe: 'Next feeding cycle',
        icon: <Agriculture />,
        color: theme.palette.success.main
      });

      setInsights(newInsights);
      setIsLoading(false);
    };

    // Simulate AI processing time
    setTimeout(generateInsights, 1500);
  }, [animalData, theme]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return theme.palette.error.main;
      case 'high': return theme.palette.warning.main;
      case 'medium': return theme.palette.info.main;
      case 'low': return theme.palette.success.main;
      default: return theme.palette.grey[500];
    }
  };

  const handleExpandInsight = (insightId: string) => {
    setExpandedInsight(expandedInsight === insightId ? null : insightId);
  };

  if (isLoading) {
    return (
      <Card sx={{ height: compact ? 300 : 400 }}>
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <Psychology sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="h6">AI Insights</Typography>
          </Box>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary" mb={1}>
              Analyzing livestock data...
            </Typography>
            <LinearProgress />
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card 
      sx={{ 
        height: compact ? 300 : 400,
        background: `linear-gradient(135deg, ${alpha(availableColors[currentColor].primary, 0.02)}, ${alpha(availableColors[currentColor].secondary, 0.01)})`,
        border: `1px solid ${alpha(availableColors[currentColor].primary, 0.1)}`
      }}
    >
      <CardContent sx={{ height: '100%', overflow: 'hidden' }}>
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center">
            <Psychology sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="h6">AI Insights</Typography>
          </Box>
          <Chip 
            label={`${insights.length} insights`} 
            size="small" 
            color="primary" 
            variant="outlined"
          />
        </Box>

        <Box sx={{ height: compact ? 220 : 320, overflow: 'auto' }}>
          <List dense>
            <AnimatePresence>
              {insights.map((insight, index) => (
                <motion.div
                  key={insight.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ListItem
                    sx={{
                      mb: 1,
                      borderRadius: 2,
                      bgcolor: alpha(insight.color, 0.05),
                      border: `1px solid ${alpha(insight.color, 0.2)}`,
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: alpha(insight.color, 0.1),
                      }
                    }}
                    onClick={() => handleExpandInsight(insight.id)}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: insight.color, width: 32, height: 32 }}>
                        {insight.icon}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" justifyContent="space-between">
                          <Typography variant="subtitle2" noWrap>
                            {insight.title}
                          </Typography>
                          <Box display="flex" alignItems="center">
                            <Chip
                              label={insight.priority}
                              size="small"
                              sx={{
                                bgcolor: getPriorityColor(insight.priority),
                                color: 'white',
                                fontSize: '0.7rem',
                                height: 20,
                                mr: 1
                              }}
                            />
                            <IconButton size="small">
                              {expandedInsight === insight.id ? <ExpandLess /> : <ExpandMore />}
                            </IconButton>
                          </Box>
                        </Box>
                      }
                      secondary={
                        <Typography variant="caption" color="text.secondary" noWrap>
                          {insight.description}
                        </Typography>
                      }
                    />
                  </ListItem>

                  <Collapse in={expandedInsight === insight.id}>
                    <Box sx={{ pl: 2, pr: 2, pb: 2 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}>
                        <strong>Recommendation:</strong> {insight.recommendation}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="text.secondary">
                          Confidence: {insight.confidence}%
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {insight.timeframe}
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={insight.confidence} 
                        sx={{ mt: 1, height: 4, borderRadius: 2 }}
                      />
                    </Box>
                  </Collapse>
                </motion.div>
              ))}
            </AnimatePresence>
          </List>
        </Box>
      </CardContent>
    </Card>
  );
};

export default LivestockInsightsPanel;
