export interface RFIDAlert {
  id: string;
  timestamp: string;
  type: 'movement' | 'health' | 'battery' | 'system' | 'boundary';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'new' | 'acknowledged' | 'resolved';
  tagId: string;
  animalId: string;
  animalName: string;
  location: string;
  description: string;
  metadata: AlertMetadata;
  assignedTo?: string;
  resolvedAt?: string;
  resolvedBy?: string;
  resolutionNotes?: string;
}

export interface AlertMetadata {
  batteryLevel?: number;
  temperature?: number;
  humidity?: number;
  movement?: {
    speed: number;
    direction: string;
    distanceMoved: number;
  };
  boundary?: {
    zoneId: string;
    zoneName: string;
    exitPoint: string;
  };
}

export interface AlertStats {
  total: number;
  new: number;
  acknowledged: number;
  resolved: number;
  bySeverity: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  byType: {
    movement: number;
    health: number;
    battery: number;
    system: number;
    boundary: number;
  };
}

export const mockRFIDAlerts: RFIDAlert[] = [
  {
    id: 'ALT001',
    timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    type: 'boundary',
    severity: 'high',
    status: 'new',
    tagId: 'TAG001',
    animalId: 'CTL-001',
    animalName: 'Bella',
    location: 'Fence Line - North Pasture',
    description: 'Animal has crossed designated boundary',
    metadata: {
      movement: {
        speed: 5.2,
        direction: 'North',
        distanceMoved: 150
      },
      boundary: {
        zoneId: 'ZONE001',
        zoneName: 'North Pasture',
        exitPoint: 'North Gate'
      }
    }
  },
  {
    id: 'ALT002',
    timestamp: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
    type: 'health',
    severity: 'critical',
    status: 'acknowledged',
    tagId: 'TAG002',
    animalId: 'SHP-001',
    animalName: 'Wooley',
    location: 'East Barn',
    description: 'Abnormal temperature detected',
    metadata: {
      temperature: 40.5,
      humidity: 65
    },
    assignedTo: 'Dr. Sarah Johnson'
  },
  {
    id: 'ALT003',
    timestamp: new Date(Date.now() - 86400000).toISOString(), // 24 hours ago
    type: 'battery',
    severity: 'medium',
    status: 'resolved',
    tagId: 'TAG003',
    animalId: 'CTL-002',
    animalName: 'Zeus',
    location: 'North Pasture',
    description: 'Low battery level detected',
    metadata: {
      batteryLevel: 15
    },
    resolvedAt: new Date(Date.now() - 82800000).toISOString(),
    resolvedBy: 'John Smith',
    resolutionNotes: 'Battery replaced'
  }
];

export const calculateAlertStats = (alerts: RFIDAlert[]): AlertStats => {
  const stats: AlertStats = {
    total: alerts.length,
    new: 0,
    acknowledged: 0,
    resolved: 0,
    bySeverity: {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    },
    byType: {
      movement: 0,
      health: 0,
      battery: 0,
      system: 0,
      boundary: 0
    }
  };

  alerts.forEach(alert => {
    // Count by status
    stats[alert.status]++;
    
    // Count by severity
    stats.bySeverity[alert.severity]++;
    
    // Count by type
    stats.byType[alert.type]++;
  });

  return stats;
};

export const mockAlertStats: AlertStats = calculateAlertStats(mockRFIDAlerts);

export const generateMockAlerts = (count: number): RFIDAlert[] => {
  const alerts: RFIDAlert[] = [];
  const types: Array<RFIDAlert['type']> = ['movement', 'health', 'battery', 'system', 'boundary'];
  const severities: Array<RFIDAlert['severity']> = ['low', 'medium', 'high', 'critical'];
  const statuses: Array<RFIDAlert['status']> = ['new', 'acknowledged', 'resolved'];

  for (let i = 0; i < count; i++) {
    const type = types[Math.floor(Math.random() * types.length)];
    const alert: RFIDAlert = {
      id: `ALT${(i + 4).toString().padStart(3, '0')}`,
      timestamp: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
      type,
      severity: severities[Math.floor(Math.random() * severities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      tagId: `TAG${Math.floor(Math.random() * 10 + 1).toString().padStart(3, '0')}`,
      animalId: `CTL${Math.floor(Math.random() * 10 + 1).toString().padStart(3, '0')}`,
      animalName: ['Bella', 'Zeus', 'Luna', 'Max', 'Ruby'][Math.floor(Math.random() * 5)],
      location: ['North Pasture', 'East Barn', 'West Paddock', 'South Field'][Math.floor(Math.random() * 4)],
      description: getAlertDescription(type),
      metadata: generateAlertMetadata(type)
    };
    alerts.push(alert);
  }

  return alerts;
};

const getAlertDescription = (type: RFIDAlert['type']): string => {
  const descriptions = {
    movement: 'Unusual movement pattern detected',
    health: 'Abnormal vital signs detected',
    battery: 'Low battery level warning',
    system: 'System communication error',
    boundary: 'Zone boundary violation detected'
  };
  return descriptions[type];
};

const generateAlertMetadata = (type: RFIDAlert['type']): AlertMetadata => {
  const metadata: AlertMetadata = {};
  
  switch (type) {
    case 'movement':
      metadata.movement = {
        speed: Math.random() * 10,
        direction: ['North', 'South', 'East', 'West'][Math.floor(Math.random() * 4)],
        distanceMoved: Math.random() * 200
      };
      break;
    case 'health':
      metadata.temperature = 35 + Math.random() * 7;
      metadata.humidity = 60 + Math.random() * 20;
      break;
    case 'battery':
      metadata.batteryLevel = Math.floor(Math.random() * 20);
      break;
    case 'boundary':
      metadata.boundary = {
        zoneId: `ZONE${Math.floor(Math.random() * 4 + 1).toString().padStart(3, '0')}`,
        zoneName: ['North Pasture', 'East Barn', 'West Paddock', 'South Field'][Math.floor(Math.random() * 4)],
        exitPoint: ['North Gate', 'South Gate', 'East Gate', 'West Gate'][Math.floor(Math.random() * 4)]
      };
      break;
  }
  
  return metadata;
};