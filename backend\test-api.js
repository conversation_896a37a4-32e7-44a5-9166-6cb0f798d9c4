/**
 * Simple script to test API endpoints
 */

const axios = require('axios');

// Base URL
const BASE_URL = 'http://localhost:3001';

// Test health endpoint
async function testHealth() {
  try {
    console.log('Testing health endpoint...');
    console.log(`GET ${BASE_URL}/health`);
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('Health endpoint response:', response.data);
    return true;
  } catch (error) {
    console.error('Health endpoint error:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Is the server running?');
    }
    return false;
  }
}

// Test login endpoint
async function testLogin(username, password) {
  try {
    console.log(`Testing login with ${username}...`);
    const response = await axios.post(`${BASE_URL}/api/auth/login`, {
      username,
      password
    });
    console.log('Login response:', response.data);
    return response.data.token;
  } catch (error) {
    console.error('Login error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Test get users endpoint
async function testGetUsers(token) {
  try {
    console.log('Testing get users endpoint...');
    const response = await axios.get(`${BASE_URL}/api/users`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log(`Found ${response.data.length} users`);
    return response.data;
  } catch (error) {
    console.error('Get users error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Run tests
async function runTests() {
  // Test health endpoint
  const healthOk = await testHealth();
  if (!healthOk) {
    console.error('Health check failed, server may not be running');
    return;
  }

  // Test login with different users
  const users = [
    { username: 'AMPD', password: '123' },
    { username: 'MayCaiphus', password: '123' },
    { username: 'mayrakgama', password: 'Morok@1951ke*#' },
    { username: 'peterbotha', password: 'Admin@2023' }
  ];

  for (const user of users) {
    const token = await testLogin(user.username, user.password);
    if (token) {
      console.log(`Login successful for ${user.username}`);

      // Test get users endpoint
      const users = await testGetUsers(token);
      if (users) {
        console.log('Get users successful');
      }

      break; // Stop after first successful login
    }
  }
}

// Run the tests
runTests()
  .then(() => {
    console.log('Tests completed');
  })
  .catch(error => {
    console.error('Tests failed:', error);
  });
