import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, CircularProgress, Dialog, DialogTitle, DialogContent, DialogActions, IconButton } from '@mui/material';
import { Close, Download, Print, ZoomIn, ZoomOut } from '@mui/icons-material';

interface HtmlReportViewerProps {
  htmlContent: string;
  title?: string;
  onDownload?: () => void;
  onPrint?: () => void;
  onClose?: () => void;
  open?: boolean;
}

const HtmlReportViewer: React.FC<HtmlReportViewerProps> = ({
  htmlContent,
  title = 'Report Preview',
  onDownload,
  onPrint,
  onClose,
  open = true
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [zoom, setZoom] = useState<number>(1);
  const [iframeHeight, setIframeHeight] = useState<number>(600);

  // Create a blob URL for the HTML content
  const [blobUrl, setBlobUrl] = useState<string>('');

  useEffect(() => {
    if (htmlContent) {
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      setBlobUrl(url);

      // Clean up the URL when the component unmounts
      return () => {
        URL.revokeObjectURL(url);
      };
    }
  }, [htmlContent]);

  const handleIframeLoad = () => {
    setLoading(false);
    
    // Try to adjust iframe height based on content
    try {
      const iframe = document.getElementById('report-iframe') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        const height = iframe.contentWindow.document.body.scrollHeight;
        setIframeHeight(height);
      }
    } catch (error) {
      console.error('Error adjusting iframe height:', error);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  const handlePrint = () => {
    if (onPrint) {
      onPrint();
    } else {
      // Default print behavior
      const iframe = document.getElementById('report-iframe') as HTMLIFrameElement;
      if (iframe && iframe.contentWindow) {
        iframe.contentWindow.print();
      }
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      PaperProps={{
        sx: {
          height: '90vh',
          maxHeight: '90vh',
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h6">{title}</Typography>
        <Box>
          <IconButton onClick={handleZoomOut} disabled={zoom <= 0.5}>
            <ZoomOut />
          </IconButton>
          <Typography variant="body2" component="span" sx={{ mx: 1 }}>
            {Math.round(zoom * 100)}%
          </Typography>
          <IconButton onClick={handleZoomIn} disabled={zoom >= 2}>
            <ZoomIn />
          </IconButton>
          <IconButton onClick={handlePrint}>
            <Print />
          </IconButton>
          <IconButton onClick={onDownload}>
            <Download />
          </IconButton>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers sx={{ p: 0, flex: 1, overflow: 'auto' }}>
        {loading && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%'
            }}
          >
            <CircularProgress />
          </Box>
        )}
        
        <Box
          sx={{
            width: '100%',
            height: '100%',
            overflow: 'auto',
            display: loading ? 'none' : 'block'
          }}
        >
          <Box
            sx={{
              transform: `scale(${zoom})`,
              transformOrigin: 'top center',
              width: `${100 / zoom}%`,
              height: iframeHeight,
              transition: 'transform 0.2s ease'
            }}
          >
            {blobUrl && (
              <iframe
                id="report-iframe"
                src={blobUrl}
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none'
                }}
                onLoad={handleIframeLoad}
                title="Report Preview"
              />
            )}
          </Box>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <CustomButton onClick={onClose}>Close</CustomButton>
      </DialogActions>
    </Dialog>
  );
};

export default HtmlReportViewer;
