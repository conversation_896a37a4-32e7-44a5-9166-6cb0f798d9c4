/**
 * Form Hook
 * 
 * This hook provides functions for handling form state and validation.
 */

import { useState, useCallback, ChangeEvent, FormEvent } from 'react';
import useApiError from './useApiError';

interface UseFormOptions<T> {
  initialValues: T;
  onSubmit: (values: T) => Promise<void> | void;
  validate?: (values: T) => Record<string, string>;
}

interface UseFormReturn<T> {
  values: T;
  errors: Record<string, string> | null;
  isSubmitting: boolean;
  apiError: string | null;
  touched: Record<string, boolean>;
  handleChange: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleBlur: (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: FormEvent<HTMLFormElement>) => void;
  setFieldValue: (field: string, value: any) => void;
  setFieldError: (field: string, error: string) => void;
  resetForm: () => void;
  clearErrors: () => void;
}

/**
 * Hook for handling form state and validation
 * @param options - Form options
 * @returns Form state and functions
 */
const useForm = <T extends Record<string, any>>(
  options: UseFormOptions<T>
): UseFormReturn<T> => {
  const { initialValues, onSubmit, validate } = options;
  
  const [values, setValues] = useState<T>(initialValues);
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const { error: apiError, errors: apiErrors, setFieldError, clearError } = useApiError();
  
  /**
   * Validate form values
   * @returns Validation errors
   */
  const validateForm = useCallback((): Record<string, string> => {
    if (!validate) return {};
    
    return validate(values);
  }, [validate, values]);
  
  /**
   * Handle form input change
   * @param e - Change event
   */
  const handleChange = useCallback((
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    
    // Handle different input types
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setValues(prevValues => ({
        ...prevValues,
        [name]: checked
      }));
    } else if (type === 'number') {
      setValues(prevValues => ({
        ...prevValues,
        [name]: value === '' ? '' : Number(value)
      }));
    } else {
      setValues(prevValues => ({
        ...prevValues,
        [name]: value
      }));
    }
    
    // Clear field error when value changes
    if (validationErrors[name]) {
      setValidationErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [validationErrors]);
  
  /**
   * Handle input blur
   * @param e - Blur event
   */
  const handleBlur = useCallback((
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name } = e.target;
    
    // Mark field as touched
    setTouched(prevTouched => ({
      ...prevTouched,
      [name]: true
    }));
    
    // Validate field on blur
    if (validate) {
      const errors = validate(values);
      
      if (errors[name]) {
        setValidationErrors(prevErrors => ({
          ...prevErrors,
          [name]: errors[name]
        }));
      }
    }
  }, [validate, values]);
  
  /**
   * Handle form submission
   * @param e - Form event
   */
  const handleSubmit = useCallback(async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateForm();
    setValidationErrors(errors);
    
    // Mark all fields as touched
    const allTouched: Record<string, boolean> = {};
    Object.keys(values).forEach(key => {
      allTouched[key] = true;
    });
    setTouched(allTouched);
    
    // If there are validation errors, don't submit
    if (Object.keys(errors).length > 0) {
      return;
    }
    
    // Submit form
    setIsSubmitting(true);
    clearError();
    
    try {
      await onSubmit(values);
    } catch (error) {
      // Error handling is done by the onSubmit function
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, values, onSubmit, clearError]);
  
  /**
   * Set field value
   * @param field - Field name
   * @param value - Field value
   */
  const setFieldValue = useCallback((field: string, value: any) => {
    setValues(prevValues => ({
      ...prevValues,
      [field]: value
    }));
    
    // Clear field error when value changes
    if (validationErrors[field]) {
      setValidationErrors(prevErrors => {
        const newErrors = { ...prevErrors };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);
  
  /**
   * Reset form to initial values
   */
  const resetForm = useCallback(() => {
    setValues(initialValues);
    setTouched({});
    setValidationErrors({});
    clearError();
  }, [initialValues, clearError]);
  
  /**
   * Clear all errors
   */
  const clearErrors = useCallback(() => {
    setValidationErrors({});
    clearError();
  }, [clearError]);
  
  // Combine validation errors and API errors
  const combinedErrors = {
    ...validationErrors,
    ...(apiErrors || {})
  };
  
  return {
    values,
    errors: Object.keys(combinedErrors).length > 0 ? combinedErrors : null,
    isSubmitting,
    apiError,
    touched,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    resetForm,
    clearErrors
  };
};

export default useForm;
