/**
 * Simplified MongoDB Configuration
 *
 * This module provides a simplified connection to MongoDB
 */

const { MongoClient } = require('mongodb');
const logger = require('../utils/logger');
require('dotenv').config();

// MongoDB connection string
const uri = process.env.MONGODB_URI || "mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// MongoDB client instance
let client = null;
let db = null;
let connected = false;

/**
 * Connect to MongoDB
 * @returns {Promise<Object>} MongoDB database instance
 */
const connect = async () => {
  if (connected && db) {
    return { client, db };
  }

  try {
    logger.info('Connecting to MongoDB...');
    logger.info(`Using connection string: ${uri.replace(/:[^:]*@/, ':****@')}`);
    logger.info(`Using database: ${dbName}`);

    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    db = client.db(dbName);
    connected = true;

    logger.info('Successfully connected to MongoDB!');
    return { client, db };
  } catch (error) {
    logger.error('Error connecting to MongoDB:', error);
    connected = false;
    throw error;
  }
};

/**
 * Get a MongoDB collection
 * @param {string} collectionName - Name of the collection
 * @returns {Promise<Collection>} MongoDB collection
 */
const getCollection = async (collectionName) => {
  try {
    if (!connected) {
      await connect();
    }
    return db.collection(collectionName);
  } catch (error) {
    logger.error(`Error getting collection ${collectionName}:`, error);
    throw error;
  }
};

/**
 * Close MongoDB connection
 */
const close = async () => {
  if (client) {
    try {
      await client.close();
      client = null;
      db = null;
      connected = false;
      logger.info('MongoDB connection closed');
    } catch (error) {
      logger.error('Error closing MongoDB connection:', error);
    }
  }
};

/**
 * Check MongoDB connection
 * @returns {Promise<boolean>} Connection status
 */
const checkConnection = async () => {
  try {
    if (!connected) {
      await connect();
    }

    // Ping the database
    await db.command({ ping: 1 });
    logger.info('MongoDB connection check successful');
    return true;
  } catch (error) {
    logger.error('MongoDB connection check failed:', error);
    connected = false;
    return false;
  }
};

/**
 * Create indexes for collections
 */
const createIndexes = async () => {
  try {
    const { db } = await connect();

    // Create indexes for animals collection
    const animalsCollection = db.collection('animals');
    await animalsCollection.createIndex({ tagNumber: 1 }, { unique: true });
    await animalsCollection.createIndex({ species: 1 });
    await animalsCollection.createIndex({ status: 1 });

    // Create indexes for users collection
    const usersCollection = db.collection('users');
    await usersCollection.createIndex({ username: 1 }, { unique: true });
    await usersCollection.createIndex({ email: 1 }, { unique: true });

    logger.info('MongoDB indexes created successfully');
  } catch (error) {
    logger.error('Error creating MongoDB indexes:', error);
  }
};

module.exports = {
  connect,
  getCollection,
  close,
  checkConnection,
  createIndexes
};
