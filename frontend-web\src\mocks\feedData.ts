export interface FeedInventory {
  id: string;
  name: string;
  type: 'concentrate' | 'forage' | 'supplement' | 'mineral';
  quantity: number;
  unit: 'kg' | 'tons' | 'bales';
  location: string;
  supplier: string;
  purchaseDate: Date;
  expiryDate: Date;
  batchNumber: string;
  costPerUnit: number;
  nutritionalInfo: NutritionalInfo;
  minimumStock: number;
  status: 'available' | 'low' | 'reorder' | 'expired';
}

export interface NutritionalInfo {
  protein: number;
  fiber: number;
  energy: number;
  minerals: Record<string, number>;
  moisture: number;
}

export interface FeedingSchedule {
  id: string;
  animalGroup: string;
  feedType: string;
  quantityPerAnimal: number;
  frequency: 'daily' | 'twice-daily' | 'weekly';
  timeSlots: string[];
  specialInstructions?: string;
}

export interface FeedingRecord {
  id: string;
  scheduleId: string;
  date: Date;
  feedId: string;
  quantityUsed: number;
  administrator: string;
  animalGroupId: string;
  consumption: 'normal' | 'low' | 'high';
  wastage: number;
  notes: string;
}

export const mockFeedInventory: FeedInventory[] = [
  {
    id: 'FD001',
    name: 'Premium Dairy Feed',
    type: 'concentrate',
    quantity: 2000,
    unit: 'kg',
    location: 'Storage Bin A',
    supplier: 'FeedCo Ltd',
    purchaseDate: new Date('2024-01-01'),
    expiryDate: new Date('2024-06-01'),
    batchNumber: 'BATCH2024001',
    costPerUnit: 0.75,
    nutritionalInfo: {
      protein: 18,
      fiber: 12,
      energy: 75,
      minerals: {
        calcium: 0.8,
        phosphorus: 0.6,
        magnesium: 0.2
      },
      moisture: 12
    },
    minimumStock: 500,
    status: 'available'
  }
];

export const mockFeedingSchedules: FeedingSchedule[] = [
  {
    id: 'FS001',
    animalGroup: 'Dairy Cows - Lactating',
    feedType: 'Premium Dairy Feed',
    quantityPerAnimal: 12,
    frequency: 'twice-daily',
    timeSlots: ['06:00', '16:00'],
    specialInstructions: 'Mix with mineral supplement'
  }
];