const logger = require('../utils/logger');
const { validateBatchOperation } = require('../utils/validation');
const reportingService = require('../services/reportingService');
const AnimalService = require('../services/animalService');
const FileProcessor = require('../utils/fileProcessor');
const DataExporter = require('../utils/dataExporter');

class BatchOperationsController {
  async batchUpdateAnimals(req, res) {
    try {
      const { operations } = req.body;
      const results = [];
      const errors = [];

      for (const op of operations) {
        try {
          await validateBatchOperation(op);
          const result = await this.processAnimalUpdate(op);
          results.push(result);
        } catch (error) {
          logger.error(`Operation failed for animal ${op.animalId}:`, error);
          errors.push({
            operation: op,
            error: error.message
          });
        }
      }

      res.json({
        success: results.length,
        failed: errors.length,
        results,
        errors
      });
    } catch (error) {
      logger.error('Batch update error:', error);
      res.status(500).json({ error: 'Batch operation failed' });
    }
  }

  async batchImport(req, res) {
    try {
      const { entityType } = req.params;
      const file = req.uploadedFiles[0];
      
      if (!file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const supportedTypes = ['animals', 'feed', 'health', 'breeding'];
      if (!supportedTypes.includes(entityType)) {
        return res.status(400).json({ error: 'Unsupported entity type' });
      }
      
      const importResults = await this.processImportFile(file, entityType);
      
      res.json({
        message: 'Import completed',
        processed: importResults.processed,
        success: importResults.success,
        failed: importResults.failed,
        errors: importResults.errors
      });
    } catch (error) {
      logger.error('Batch import error:', error);
      res.status(500).json({ error: 'Import failed' });
    }
  }

  async batchExport(req, res) {
    try {
      const { entityType, format = 'csv' } = req.query;
      const filters = req.body.filters || {};

      const supportedFormats = ['csv', 'xlsx', 'pdf'];
      if (!supportedFormats.includes(format)) {
        return res.status(400).json({ error: 'Unsupported export format' });
      }

      const data = await this.fetchExportData(entityType, filters);
      const reportPath = await reportingService.generateReport(entityType, data, format);

      res.download(reportPath, `${entityType}_export_${Date.now()}.${format}`, (err) => {
        if (err) {
          logger.error('Download error:', err);
        }
        // Cleanup temporary file
        FileProcessor.cleanupTempFile(reportPath);
      });
    } catch (error) {
      logger.error('Batch export error:', error);
      res.status(500).json({ error: 'Export failed' });
    }
  }

  async processAnimalUpdate(operation) {
    const { animalId, updateType, data } = operation;
    
    switch (updateType) {
      case 'health':
        return await AnimalService.updateHealth(animalId, data);
      case 'weight':
        return await AnimalService.updateWeight(animalId, data);
      case 'location':
        return await AnimalService.updateLocation(animalId, data);
      case 'status':
        return await AnimalService.updateStatus(animalId, data);
      default:
        throw new Error(`Unsupported update type: ${updateType}`);
    }
  }

  async processImportFile(file, entityType) {
    const results = {
      processed: 0,
      success: 0,
      failed: 0,
      errors: []
    };

    try {
      const records = await FileProcessor.parseFile(file);
      
      for (const record of records) {
        results.processed++;
        try {
          await this.validateAndSaveRecord(entityType, record);
          results.success++;
        } catch (error) {
          results.failed++;
          results.errors.push({
            record,
            error: error.message
          });
        }
      }
    } catch (error) {
      throw new Error(`File processing failed: ${error.message}`);
    }

    return results;
  }

  async fetchExportData(entityType, filters) {
    const exporter = new DataExporter(entityType);
    const data = await exporter.fetchData(filters);
    
    if (!data || data.length === 0) {
      throw new Error('No data found for export');
    }
    
    return data;
  }

  async validateAndSaveRecord(entityType, record) {
    // Validate required fields based on entity type
    const validator = await import(`../validators/${entityType}Validator`);
    await validator.validate(record);
    
    // Save to appropriate service
    const service = await import(`../services/${entityType}Service`);
    return await service.create(record);
  }
}

module.exports = new BatchOperationsController();
