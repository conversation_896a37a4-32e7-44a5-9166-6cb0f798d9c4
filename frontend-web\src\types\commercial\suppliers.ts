/**
 * Types for agricultural suppliers integration
 */

export interface SupplierProduct {
  id: string;
  name: string;
  category: SupplierProductCategory;
  description: string;
  imageUrl?: string;
  price?: number;
  currency?: string;
  availability?: 'in-stock' | 'limited' | 'out-of-stock';
  url?: string;
}

export type SupplierProductCategory =
  | 'livestock-feed'
  | 'equipment'
  | 'seeds'
  | 'fertilizer'
  | 'chemicals'
  | 'irrigation'
  | 'animal-health'
  | 'fuel'
  | 'retail'
  | 'financial'
  | 'insurance'
  | 'other';

export interface SupplierContact {
  type: 'phone' | 'email' | 'website' | 'address' | 'social';
  value: string;
  label?: string;
}

export interface SupplierLocation {
  id: string;
  name: string;
  address: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  phone?: string;
  email?: string;
  hours?: string;
}

export interface Supplier {
  id: string;
  name: string;
  logo: string;
  description: string;
  shortDescription: string;
  categories: SupplierProductCategory[];
  contacts: SupplierContact[];
  locations: SupplierLocation[];
  website: string;
  featuredProducts?: SupplierProduct[];
  services?: string[];
  established?: number;
  coverImage?: string;
  rating?: number;
  lastOrder?: string;
  contactPerson?: string;
}

export interface SupplierFilter {
  categories?: SupplierProductCategory[];
  search?: string;
  province?: string[];
}
