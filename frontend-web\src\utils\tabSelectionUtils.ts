/**
 * Utility functions for ensuring proper tab selection behavior
 */

import { SxProps, Theme } from '@mui/material';
import { getSelectableStyles } from './selectionUtils';

/**
 * Returns styles for properly selectable tabs
 */
export const getSelectableTabsStyles = (): SxProps<Theme> => ({
  '& .MuiTabs-root': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto !important',
  },
  '& .MuiTab-root': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto !important',
    cursor: 'pointer !important',
    '&:hover': {
      opacity: 0.8,
    }
  },
  '& .MuiTabs-indicator': {
    position: 'relative',
    zIndex: 9,
  },
  '& .MuiTabs-scroller': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto !important',
  },
  '& .MuiTabs-flexContainer': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto !important',
  }
});

/**
 * Returns styles for properly selectable tab panels
 */
export const getSelectableTabPanelStyles = (): SxProps<Theme> => ({
  position: 'relative',
  zIndex: 5,
  pointerEvents: 'auto !important',
  cursor: 'default',
  '& [role="tabpanel"]': {
    position: 'relative',
    zIndex: 5,
    pointerEvents: 'auto !important'
  },
  ...getSelectableStyles()
});

/**
 * Returns styles for properly selectable tab content
 */
export const getSelectableTabContentStyles = (): SxProps<Theme> => ({
  '& .MuiButton-root': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto',
    cursor: 'pointer',
  },
  '& .MuiIconButton-root': {
    position: 'relative',
    zIndex: 10,
    pointerEvents: 'auto',
    cursor: 'pointer',
  },
  '& .MuiTableRow-root': {
    position: 'relative',
    zIndex: 5,
    pointerEvents: 'auto',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)'
    }
  },
  '& .MuiTableCell-root': {
    position: 'relative',
    zIndex: 5,
    pointerEvents: 'auto',
  },
  '& .MuiCard-root': {
    position: 'relative',
    zIndex: 5,
    pointerEvents: 'auto',
  },
  '& .MuiCardContent-root': {
    position: 'relative',
    zIndex: 5,
    pointerEvents: 'auto',
  },
  ...getSelectableStyles()
});
