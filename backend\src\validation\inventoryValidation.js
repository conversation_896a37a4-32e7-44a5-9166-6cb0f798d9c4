/**
 * Inventory Validation Schemas
 * 
 * This module provides validation schemas for inventory-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// Inventory item categories
const categories = [
  'feed', 
  'medication', 
  'equipment', 
  'supplies', 
  'tools', 
  'vehicles', 
  'buildings', 
  'other'
];

// Inventory item statuses
const statuses = ['active', 'low_stock', 'out_of_stock', 'discontinued', 'on_order'];

// Inventory transaction types
const transactionTypes = ['purchase', 'use', 'adjustment', 'transfer', 'disposal', 'return'];

// Create inventory item validation schema
const createInventoryItemSchema = Joi.object({
  name: Joi.string().trim().required().messages({
    'string.empty': 'Name cannot be empty',
    'any.required': 'Name is required'
  }),
  description: Joi.string().trim().allow('').messages({
    'string.base': 'Description must be a string'
  }),
  category: Joi.string().valid(...categories).required().messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`,
    'any.required': 'Category is required'
  }),
  quantity: Joi.number().min(0).default(0).messages({
    'number.base': 'Quantity must be a number',
    'number.min': 'Quantity cannot be negative'
  }),
  unit: Joi.string().trim().required().messages({
    'string.empty': 'Unit cannot be empty',
    'any.required': 'Unit is required'
  }),
  unitCost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Unit cost must be a number',
    'number.min': 'Unit cost cannot be negative'
  }),
  reorderPoint: Joi.number().min(0).allow(null).messages({
    'number.base': 'Reorder point must be a number',
    'number.min': 'Reorder point cannot be negative'
  }),
  reorderQuantity: Joi.number().min(0).allow(null).messages({
    'number.base': 'Reorder quantity must be a number',
    'number.min': 'Reorder quantity cannot be negative'
  }),
  location: Joi.string().trim().allow('').messages({
    'string.base': 'Location must be a string'
  }),
  supplier: Joi.string().trim().allow('').messages({
    'string.base': 'Supplier must be a string'
  }),
  supplierContact: Joi.string().trim().allow('').messages({
    'string.base': 'Supplier contact must be a string'
  }),
  status: Joi.string().valid(...statuses).default('active').messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  expiryDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Expiry date must be a valid date',
    'date.format': 'Expiry date must be in ISO format (YYYY-MM-DD)'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  images: Joi.array().items(Joi.string()).default([]).messages({
    'array.base': 'Images must be an array'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

// Update inventory item validation schema
const updateInventoryItemSchema = Joi.object({
  name: Joi.string().trim().messages({
    'string.empty': 'Name cannot be empty'
  }),
  description: Joi.string().trim().allow('').messages({
    'string.base': 'Description must be a string'
  }),
  category: Joi.string().valid(...categories).messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`
  }),
  quantity: Joi.number().min(0).messages({
    'number.base': 'Quantity must be a number',
    'number.min': 'Quantity cannot be negative'
  }),
  unit: Joi.string().trim().messages({
    'string.empty': 'Unit cannot be empty'
  }),
  unitCost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Unit cost must be a number',
    'number.min': 'Unit cost cannot be negative'
  }),
  reorderPoint: Joi.number().min(0).allow(null).messages({
    'number.base': 'Reorder point must be a number',
    'number.min': 'Reorder point cannot be negative'
  }),
  reorderQuantity: Joi.number().min(0).allow(null).messages({
    'number.base': 'Reorder quantity must be a number',
    'number.min': 'Reorder quantity cannot be negative'
  }),
  location: Joi.string().trim().allow('').messages({
    'string.base': 'Location must be a string'
  }),
  supplier: Joi.string().trim().allow('').messages({
    'string.base': 'Supplier must be a string'
  }),
  supplierContact: Joi.string().trim().allow('').messages({
    'string.base': 'Supplier contact must be a string'
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  expiryDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Expiry date must be a valid date',
    'date.format': 'Expiry date must be in ISO format (YYYY-MM-DD)'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  images: Joi.array().items(Joi.string()).messages({
    'array.base': 'Images must be an array'
  }),
  metadata: Joi.object().messages({
    'object.base': 'Metadata must be an object'
  })
});

// Get inventory items query validation schema
const getInventoryItemsQuerySchema = Joi.object({
  category: Joi.string().valid(...categories).messages({
    'any.only': `Category must be one of: ${categories.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  }),
  minQuantity: Joi.number().min(0).messages({
    'number.base': 'Minimum quantity must be a number',
    'number.min': 'Minimum quantity cannot be negative'
  }),
  maxQuantity: Joi.number().min(0).messages({
    'number.base': 'Maximum quantity must be a number',
    'number.min': 'Maximum quantity cannot be negative'
  }),
  location: Joi.string().trim().messages({
    'string.base': 'Location must be a string'
  }),
  supplier: Joi.string().trim().messages({
    'string.base': 'Supplier must be a string'
  }),
  expiryBefore: Joi.date().iso().messages({
    'date.base': 'Expiry before must be a valid date',
    'date.format': 'Expiry before must be in ISO format (YYYY-MM-DD)'
  }),
  expiryAfter: Joi.date().iso().messages({
    'date.base': 'Expiry after must be a valid date',
    'date.format': 'Expiry after must be in ISO format (YYYY-MM-DD)'
  })
}).concat(schemas.pagination);

// Inventory item ID parameter validation schema
const inventoryItemIdParamSchema = Joi.object({
  id: schemas.id
});

// Create inventory transaction validation schema
const createInventoryTransactionSchema = Joi.object({
  itemId: schemas.id,
  date: Joi.date().iso().required().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),
  type: Joi.string().valid(...transactionTypes).required().messages({
    'any.only': `Type must be one of: ${transactionTypes.join(', ')}`,
    'any.required': 'Type is required'
  }),
  quantity: Joi.number().required().messages({
    'number.base': 'Quantity must be a number',
    'any.required': 'Quantity is required'
  }),
  unitCost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Unit cost must be a number',
    'number.min': 'Unit cost cannot be negative'
  }),
  reference: Joi.string().trim().allow('').messages({
    'string.base': 'Reference must be a string'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  performedBy: Joi.string().trim().allow('').messages({
    'string.base': 'Performed by must be a string'
  }),
  relatedEntityType: Joi.string().trim().valid('animal', 'breeding', 'health', 'financial', 'other').allow('').messages({
    'any.only': 'Related entity type must be one of: animal, breeding, health, financial, other'
  }),
  relatedEntityId: Joi.string().trim().allow('').messages({
    'string.base': 'Related entity ID must be a string'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

module.exports = {
  createInventoryItemSchema,
  updateInventoryItemSchema,
  getInventoryItemsQuerySchema,
  inventoryItemIdParamSchema,
  createInventoryTransactionSchema,
  categories,
  statuses,
  transactionTypes
};
