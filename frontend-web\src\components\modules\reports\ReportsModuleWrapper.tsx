import React, { useEffect } from 'react';
import { applyReportSelectionFixes } from '../../../utils/applyReportSelectionFixes';

interface ReportsModuleWrapperProps {
  children: React.ReactNode;
}

/**
 * A wrapper component for the Reports module
 * This component applies fixes to ensure proper selection and button functionality
 */
const ReportsModuleWrapper: React.FC<ReportsModuleWrapperProps> = ({ children }) => {
  useEffect(() => {
    // Apply fixes immediately
    applyReportSelectionFixes();
    
    // Apply fixes again after a delay to ensure all components are rendered
    const timer1 = setTimeout(() => {
      applyReportSelectionFixes();
    }, 500);
    
    // Apply fixes one more time after a longer delay
    const timer2 = setTimeout(() => {
      applyReportSelectionFixes();
    }, 2000);
    
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);
  
  return (
    <div className="reports-module">
      {children}
    </div>
  );
};

export default ReportsModuleWrapper;
