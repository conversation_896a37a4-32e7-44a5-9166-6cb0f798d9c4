/**
 * <PERSON>ript to populate MongoDB with sample report data
 */

const mongoose = require('mongoose');
const Report = require('../src/models/Report');
const { MongoClient } = require('mongodb');
const logger = require('../src/utils/logger');
require('dotenv').config();

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/livestock-management';

// Sample report data
const sampleReports = [
  {
    id: 'REP001',
    title: 'Monthly Livestock Status Report',
    type: 'livestock',
    module: 'animals',
    date: new Date('2024-02-15'),
    status: 'generated',
    format: 'pdf',
    size: '2.5 MB',
    author: 'System',
    description: 'Comprehensive overview of livestock status including health, breeding, and inventory',
    downloadUrl: '/reports/REP001.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg',
    tags: ['livestock', 'monthly', 'status'],
    viewCount: 42,
    downloadCount: 15,
    isShared: true,
    parameters: {
      dateRange: { start: '2024-01-01', end: '2024-01-31' },
      includeHealthMetrics: true,
      includeBreedingMetrics: true
    }
  },
  {
    id: 'REP002',
    title: 'Q1 Financial Summary',
    type: 'financial',
    module: 'financial',
    date: new Date('2024-01-31'),
    status: 'generated',
    format: 'excel',
    size: '1.8 MB',
    author: 'System',
    description: 'Financial performance summary for Q1 including revenue, expenses, and profit analysis',
    downloadUrl: '/reports/REP002.xlsx',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2020/01/money-farm.jpg',
    tags: ['financial', 'quarterly', 'summary'],
    viewCount: 35,
    downloadCount: 20,
    isShared: true,
    parameters: {
      dateRange: { start: '2024-01-01', end: '2024-03-31' },
      includeRevenueBreakdown: true,
      includeExpenseBreakdown: true
    }
  },
  {
    id: 'REP003',
    title: 'Breeding Performance Analysis',
    type: 'breeding',
    module: 'breeding',
    date: new Date('2024-02-10'),
    status: 'generated',
    format: 'pdf',
    size: '3.2 MB',
    author: 'System',
    description: 'Analysis of breeding program performance including success rates, genetic improvements, and recommendations',
    downloadUrl: '/reports/REP003.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2018/10/cows-calves.jpg',
    tags: ['breeding', 'performance', 'analysis'],
    viewCount: 28,
    downloadCount: 12,
    isShared: false,
    parameters: {
      dateRange: { start: '2023-01-01', end: '2024-01-31' },
      includeGeneticAnalysis: true,
      includeRecommendations: true
    }
  },
  {
    id: 'REP004',
    title: 'Feed Consumption Trends',
    type: 'feeding',
    module: 'feed-management',
    date: new Date('2024-02-05'),
    status: 'generated',
    format: 'pdf',
    size: '2.1 MB',
    author: 'System',
    description: 'Analysis of feed consumption patterns, costs, and efficiency metrics',
    downloadUrl: '/reports/REP004.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/05/cattle-feeding.jpg',
    tags: ['feeding', 'consumption', 'trends'],
    viewCount: 19,
    downloadCount: 8,
    isShared: false,
    parameters: {
      dateRange: { start: '2023-07-01', end: '2024-01-31' },
      includeCostAnalysis: true,
      includeEfficiencyMetrics: true
    }
  },
  {
    id: 'REP005',
    title: 'Health Inspection Summary',
    type: 'health',
    module: 'health',
    date: new Date('2024-02-20'),
    status: 'generated',
    format: 'pdf',
    size: '1.5 MB',
    author: 'System',
    description: 'Summary of health inspections, vaccinations, and treatment outcomes',
    downloadUrl: '/reports/REP005.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2018/05/vet-cattle.jpg',
    tags: ['health', 'inspection', 'summary'],
    viewCount: 31,
    downloadCount: 14,
    isShared: true,
    parameters: {
      dateRange: { start: '2024-01-01', end: '2024-02-15' },
      includeVaccinationRecords: true,
      includeTreatmentOutcomes: true
    }
  },
  {
    id: 'REP006',
    title: 'Market Price Analysis',
    type: 'market',
    module: 'commercial',
    date: new Date('2024-02-25'),
    status: 'generated',
    format: 'excel',
    size: '2.3 MB',
    author: 'System',
    description: 'Analysis of market prices for livestock and agricultural products',
    downloadUrl: '/reports/REP006.xlsx',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/08/auction.jpg',
    tags: ['market', 'price', 'analysis'],
    viewCount: 27,
    downloadCount: 18,
    isShared: true,
    parameters: {
      dateRange: { start: '2023-01-01', end: '2024-02-15' },
      includePriceTrends: true,
      includeMarketForecasts: true
    }
  },
  {
    id: 'REP007',
    title: 'Compliance Audit Report',
    type: 'compliance',
    module: 'compliance',
    date: new Date('2024-01-15'),
    status: 'generated',
    format: 'pdf',
    size: '4.1 MB',
    author: 'System',
    description: 'Comprehensive audit of compliance with agricultural regulations and standards',
    downloadUrl: '/reports/REP007.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2020/03/compliance.jpg',
    tags: ['compliance', 'audit', 'regulations'],
    viewCount: 15,
    downloadCount: 10,
    isShared: false,
    parameters: {
      auditDate: '2024-01-10',
      includeRegulatoryCompliance: true,
      includeRecommendations: true
    }
  },
  {
    id: 'REP008',
    title: 'Custom Productivity Report',
    type: 'custom',
    module: 'reports',
    date: new Date('2024-02-28'),
    status: 'generated',
    format: 'pdf',
    size: '2.8 MB',
    author: 'Admin',
    description: 'Custom report on farm productivity metrics and efficiency',
    downloadUrl: '/reports/REP008.pdf',
    thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/12/farm-productivity.jpg',
    tags: ['custom', 'productivity', 'efficiency'],
    viewCount: 8,
    downloadCount: 3,
    isShared: false,
    parameters: {
      dateRange: { start: '2023-01-01', end: '2024-02-15' },
      customMetrics: ['labor_efficiency', 'land_productivity', 'resource_utilization'],
      includeRecommendations: true
    }
  }
];

// Function to populate the database
async function populateReports() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    logger.info('Connected to MongoDB');
    
    // Check if reports collection already has data
    const existingReports = await Report.countDocuments();
    
    if (existingReports > 0) {
      logger.info(`Reports collection already has ${existingReports} documents. Skipping population.`);
    } else {
      // Insert sample reports
      await Report.insertMany(sampleReports);
      logger.info(`Successfully inserted ${sampleReports.length} sample reports`);
    }
    
    // Close the connection
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');
    
  } catch (error) {
    logger.error('Error populating reports data:', error);
  }
}

// Run the population function
populateReports();
