# Button Usage Analysis

- **Files using MUI Button:** 28
- **Files using CustomButton:** 135
- **Files with no button usage:** 171

## Files Using MUI Button

- `components\analytics\CustomAnalyticsButton.tsx` (Import: No, Usage: Yes)
- `components\breeding\NotificationConfig.tsx` (Import: No, Usage: Yes)
- `components\common\AnimatedBackgroundCard.tsx` (Import: No, Usage: Yes)
- `components\common\ButtonReplacer.tsx` (Import: No, Usage: Yes)
- `components\common\CustomButton.tsx` (Import: No, Usage: Yes)
- `components\common\DirectFixButton.tsx` (Import: No, Usage: Yes)
- `components\common\FinalButtonReplacer.tsx` (Import: No, Usage: Yes)
- `components\common\FinalFixButton.tsx` (Import: No, Usage: Yes)
- `components\common\FinalMuiButtonFix.tsx` (Import: No, Usage: Yes)
- `components\common\FixedThemeProvider.tsx` (Import: No, Usage: Yes)
- `components\common\GlobalSafeButton.tsx` (Import: No, Usage: Yes)
- `components\common\ModuleHeader.tsx` (Import: No, Usage: Yes)
- `components\common\ModuleHeaderCard.tsx` (Import: No, Usage: Yes)
- `components\common\MuiButtonFix.tsx` (Import: No, Usage: Yes)
- `components\common\OfflineIndicator.tsx` (Import: No, Usage: Yes)
- `components\common\SafeButton.tsx` (Import: No, Usage: Yes)
- `components\common\SubModuleChart.tsx` (Import: No, Usage: Yes)
- `components\common\SubModuleDataTable.tsx` (Import: No, Usage: Yes)
- `components\common\SubModuleReport.tsx` (Import: No, Usage: Yes)
- `components\login\EnhancedLoginForm.tsx` (Import: No, Usage: Yes)
- `components\modules\commercial\MarketplaceListingDetail.tsx` (Import: No, Usage: Yes)
- `components\modules\reports\ReportGenerator.tsx` (Import: No, Usage: Yes)
- `components\Navbar.tsx` (Import: No, Usage: Yes)
- `contexts\ThemeContext.tsx` (Import: No, Usage: Yes)
- `design\DesignSystem.tsx` (Import: No, Usage: Yes)
- `pages\BusinessAnalysis.tsx` (Import: No, Usage: Yes)
- `pages\health\HealthRecords.tsx` (Import: No, Usage: Yes)
- `utils\animationUtils.tsx` (Import: No, Usage: Yes)

## Files Using CustomButton

- `App.tsx`
- `components\admin\DataMigration.tsx`
- `components\animals\AnimalFilters.tsx`
- `components\animals\AnimalForm.tsx`
- `components\animals\AnimalList.tsx`
- `components\animals\AnimalQuickView.tsx`
- `components\animals\RealTimeTracking.tsx`
- `components\animals\RFIDManagement.tsx`
- `components\animals\RFIDTracking.tsx`
- `components\breeding\BreedingList.tsx`
- `components\breeding\BreedingRecordModal.tsx`
- `components\breeding\NotificationHistory.tsx`
- `components\breeding\TestNotification.tsx`
- `components\commercial\AuctionModal.tsx`
- `components\commercial\BkbAuctionCalendar.tsx`
- `components\commercial\BkbAuctionFilters.tsx`
- `components\commercial\OrderModal.tsx`
- `components\commercial\SupplierModal.tsx`
- `components\commercial\SuppliersDirectory.tsx`
- `components\common\AnimatedChart.tsx`
- `components\common\AnimatedDashboardCard.tsx`
- `components\common\BlendedImageCard.tsx`
- `components\common\CrudComponent.tsx`
- `components\common\DashboardCard.tsx`
- `components\common\EnhancedChart.tsx`
- `components\common\EnhancedTable.tsx`
- `components\common\ErrorBoundary.tsx`
- `components\common\FallbackUI.tsx`
- `components\common\FeaturedDealCard.tsx`
- `components\common\ImageTest.tsx`
- `components\common\LazyLoadFallback.tsx`
- `components\common\ModernCard.tsx`
- `components\common\ModernDataTable.tsx`
- `components\common\ModuleDashboard.tsx`
- `components\common\ModuleItemCard.tsx`
- `components\common\ModulePage.tsx`
- `components\common\ProductCard.tsx`
- `components\common\SubModuleActionPanel.tsx`
- `components\common\ThemeSafeButton.tsx`
- `components\feeding\FeedingRecordModal.tsx`
- `components\feeding\FeedInventoryModal.tsx`
- `components\health\HealthRecordModal.tsx`
- `components\LanguageTest.tsx`
- `components\modules\animals\AnimalMarketplaceCard.tsx`
- `components\modules\animals\AnimalMarketplaceGrid.tsx`
- `components\modules\animals\AnimalsDashboard.tsx`
- `components\modules\breeding\BreedingDashboard.tsx`
- `components\modules\commercial\MarketForecast.tsx`
- `components\modules\commercial\MarketplaceSuppliers.tsx`
- `components\modules\feed\FeedManagementDashboard.tsx`
- `components\modules\feeding\FeedingDashboard.tsx`
- `components\modules\feeding\FeedingRecordsList.tsx`
- `components\modules\financial\FinancialManagementDashboard.tsx`
- `components\modules\health\HealthRecords.tsx`
- `components\modules\reports\MetricsChart.tsx`
- `components\NotificationsPanel.tsx`
- `components\reports\HtmlReportViewer.tsx`
- `components\settings\EnhancedThemeSettings.tsx`
- `components\settings\ThemeSettings.tsx`
- `components\settings\UserManagement.tsx`
- `components\settings\UserPermissions.tsx`
- `pages\animals\AnimalDetail.tsx`
- `pages\animals\AnimalForm.tsx`
- `pages\animals\AnimalsDashboard.tsx`
- `pages\animals\AnimalsList.tsx`
- `pages\animals\AnimalTracking.tsx`
- `pages\animals\HealthPrediction.tsx`
- `pages\animals\RetirementTracking.tsx`
- `pages\animals\Tracking.tsx`
- `pages\breeding\BirthPredictions.tsx`
- `pages\breeding\BirthRecords.tsx`
- `pages\breeding\BreedingDashboard.tsx`
- `pages\breeding\BreedingRecords.tsx`
- `pages\breeding\BreedingSchedule.tsx`
- `pages\breeding\HeatCalendar.tsx`
- `pages\breeding\PregnancyTracking.tsx`
- `pages\breeding\Schedule.tsx`
- `pages\commercial\Auctions.tsx`
- `pages\commercial\CommercialDashboard.tsx`
- `pages\commercial\Marketplace.tsx`
- `pages\commercial\Orders.tsx`
- `pages\commercial\Pricing.tsx`
- `pages\commercial\SupplierDetail.tsx`
- `pages\commercial\Suppliers.tsx`
- `pages\compliance\Certifications.tsx`
- `pages\compliance\ComplianceDashboard.tsx`
- `pages\compliance\Inspections.tsx`
- `pages\feeding\FeedingDashboard.tsx`
- `pages\feeding\FeedingInventory.tsx`
- `pages\feeding\FeedingPlans.tsx`
- `pages\feeding\FeedingSchedules.tsx`
- `pages\feeding\Inventory.tsx`
- `pages\feeding\Suppliers.tsx`
- `pages\financial\FinancialBudgets.tsx`
- `pages\financial\FinancialDashboard.tsx`
- `pages\financial\FinancialOverview.tsx`
- `pages\financial\FinancialReports.tsx`
- `pages\financial\FinancialTransactions.tsx`
- `pages\financial\Forecast.tsx`
- `pages\financial\Invoices.tsx`
- `pages\financial\ROI.tsx`
- `pages\health\Appointments.tsx`
- `pages\health\Diseases.tsx`
- `pages\health\HealthDashboard.tsx`
- `pages\health\Records.tsx`
- `pages\health\Treatments.tsx`
- `pages\health\Vaccinations.tsx`
- `pages\Reports.tsx`
- `pages\reports\Analysis.tsx`
- `pages\reports\Custom.tsx`
- `pages\reports\FeedingReports.tsx`
- `pages\reports\Financial.tsx`
- `pages\reports\FinancialReport.tsx`
- `pages\reports\FinancialReports.tsx`
- `pages\reports\Health.tsx`
- `pages\reports\HealthReports.tsx`
- `pages\reports\Market.tsx`
- `pages\reports\Performance.tsx`
- `pages\reports\PredictiveAnalysis.tsx`
- `pages\reports\ReportPage.tsx`
- `pages\reports\ReportsDashboard.tsx`
- `pages\resources\Documentation.tsx`
- `pages\resources\Downloads.tsx`
- `pages\resources\GovernmentResources.tsx`
- `pages\resources\Guidelines.tsx`
- `pages\resources\ResourceMaintenance.tsx`
- `pages\resources\ResourcesDashboard.tsx`
- `pages\resources\ResourcesList.tsx`
- `pages\resources\Support.tsx`
- `pages\resources\Training.tsx`
- `pages\Settings.tsx`
- `pages\settings\BackupSettings.tsx`
- `pages\settings\DatabaseSettings.tsx`
- `pages\settings\UserManagement.tsx`
- `pages\TranslationTest.tsx`
