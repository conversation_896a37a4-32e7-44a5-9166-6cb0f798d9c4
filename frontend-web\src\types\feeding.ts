export interface FeedingRecord {
  id: string;
  date: Date;
  feedId: string;
  feedType: string;
  quantity: number;
  costPerUnit: number;
  totalCost: number;
  animalGroupId: string;
  animalGroup?: string;
  animals?: string[]; // Add this property for animal IDs
  notes: string;
  unit?: string;
  location?: string;
}

export interface FeedInventory {
  id: string;
  name: string;
  type: 'concentrate' | 'forage' | 'supplement' | 'mineral' | 'other';
  quantity: number;
  unit: 'kg' | 'tons' | 'bales';
  location: string;
  supplier: string;
  purchaseDate: Date;
  expiryDate: Date;
  batchNumber: string;
  costPerUnit: number;
  nutritionalInfo: {
    protein: number;
    fiber: number;
    energy: number;
    minerals: Record<string, number>;
    moisture: number;
  };
  minimumStock: number;
  status: 'available' | 'low' | 'reorder' | 'expired';
  reorderLevel: number;
  capacity: number;
  lastUpdated?: Date;
  notes?: string;
  totalCost?: number;
}

export interface FeedingPlan {
  id: string;
  name: string;
  animalGroupId: string;
  feedItems: FeedPlanItem[];
  startDate: Date;
  endDate?: Date;
  status: 'active' | 'completed' | 'draft';
  isActive: boolean;
}

export interface FeedPlanItem {
  feedId: string;
  quantity: number;
  frequency: 'daily' | 'weekly' | 'monthly';
  timeOfDay?: string;
}

export interface FeedingStats {
  totalFeedUsedToday: number;
  totalFeedCostToday: number;
  feedInventoryValue: number;
  lowStockItems: number;
  activeFeedingPlans: number;
  feedingRecordsThisWeek: number;
  feedUsageByType: {
    concentrate: number;
    forage: number;
    mineral: number;
  };
}

export interface FeedingMetrics {
  totalFeedCost: number;
  feedUsageByType: Record<string, number>;
  costByAnimalGroup: Record<string, number>;
  averageDailyConsumption: number;
  feedConversionRatio: number;
  feedEfficiency: number;
  costPerKgGain: number;
}

export interface FeedingReportParams {
  startDate: string;
  endDate: string;
  animalGroupIds?: string[];
  feedTypeIds?: string[];
  includeInventory: boolean;
  includeSchedules: boolean;
  includeMetrics: boolean;
  format: 'pdf' | 'excel' | 'csv' | 'html';
}
