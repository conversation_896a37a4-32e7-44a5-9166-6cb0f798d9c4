/**
 * Push Notification Service for AgriIntel PWA
 * Handles push notifications, service worker registration, and notification permissions
 */

interface PushNotificationPayload {
  title: string;
  body: string;
  icon?: string;
  badge?: string;
  tag?: string;
  data?: any;
  actions?: NotificationAction[];
  requireInteraction?: boolean;
  silent?: boolean;
}

interface NotificationAction {
  action: string;
  title: string;
  icon?: string;
}

class PushNotificationService {
  private swRegistration: ServiceWorkerRegistration | null = null;
  private vapidPublicKey = process.env.REACT_APP_VAPID_PUBLIC_KEY || 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HtLlueVJQzPJtqbJWHXml5AC_4j1QOHsjHqVOkrq-JxbmRSdU';

  /**
   * Initialize the push notification service
   */
  async initialize(): Promise<boolean> {
    try {
      // Check if service workers are supported
      if (!('serviceWorker' in navigator)) {
        console.warn('Service workers not supported');
        return false;
      }

      // Check if push messaging is supported
      if (!('PushManager' in window)) {
        console.warn('Push messaging not supported');
        return false;
      }

      // Register service worker
      this.swRegistration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', this.swRegistration);

      // Request notification permission
      const permission = await this.requestPermission();
      if (permission !== 'granted') {
        console.warn('Notification permission not granted');
        return false;
      }

      // Subscribe to push notifications
      await this.subscribeToPush();

      return true;
    } catch (error) {
      console.error('Failed to initialize push notification service:', error);
      return false;
    }
  }

  /**
   * Request notification permission from user
   */
  async requestPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return 'denied';
    }

    if (Notification.permission === 'granted') {
      return 'granted';
    }

    if (Notification.permission === 'denied') {
      return 'denied';
    }

    // Request permission
    const permission = await Notification.requestPermission();
    return permission;
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(): Promise<PushSubscription | null> {
    try {
      if (!this.swRegistration) {
        throw new Error('Service worker not registered');
      }

      // Check if already subscribed
      let subscription = await this.swRegistration.pushManager.getSubscription();
      
      if (!subscription) {
        // Create new subscription
        subscription = await this.swRegistration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(this.vapidPublicKey)
        });
      }

      // Send subscription to server
      await this.sendSubscriptionToServer(subscription);

      console.log('Push subscription successful:', subscription);
      return subscription;
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return null;
    }
  }

  /**
   * Show local notification
   */
  async showNotification(payload: PushNotificationPayload): Promise<void> {
    try {
      if (!this.swRegistration) {
        // Fallback to browser notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(payload.title, {
            body: payload.body,
            icon: payload.icon || '/logo192.png',
            badge: payload.badge || '/favicon.ico'
          });
        }
        return;
      }

      await this.swRegistration.showNotification(payload.title, {
        body: payload.body,
        icon: payload.icon || '/logo192.png',
        badge: payload.badge || '/favicon.ico',
        tag: payload.tag,
        data: payload.data,
        actions: payload.actions || [],
        requireInteraction: payload.requireInteraction || false,
        silent: payload.silent || false,
        vibrate: [100, 50, 100]
      });
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  /**
   * Send subscription to server
   */
  private async sendSubscriptionToServer(subscription: PushSubscription): Promise<void> {
    try {
      const response = await fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          subscription: subscription.toJSON(),
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send subscription to server');
      }
    } catch (error) {
      console.error('Error sending subscription to server:', error);
    }
  }

  /**
   * Convert VAPID key to Uint8Array
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  /**
   * Show animal health reminder notification
   */
  async showHealthReminder(animalName: string, reminderType: string): Promise<void> {
    await this.showNotification({
      title: 'Health Reminder - AgriIntel',
      body: `${animalName} is due for ${reminderType}`,
      icon: '/logo192.png',
      tag: 'health-reminder',
      data: {
        type: 'health-reminder',
        animal: animalName,
        reminder: reminderType
      },
      actions: [
        {
          action: 'view',
          title: 'View Details'
        },
        {
          action: 'dismiss',
          title: 'Dismiss'
        }
      ],
      requireInteraction: true
    });
  }

  /**
   * Show breeding notification
   */
  async showBreedingNotification(animalName: string, event: string): Promise<void> {
    await this.showNotification({
      title: 'Breeding Alert - AgriIntel',
      body: `${animalName}: ${event}`,
      icon: '/logo192.png',
      tag: 'breeding-alert',
      data: {
        type: 'breeding',
        animal: animalName,
        event: event
      },
      actions: [
        {
          action: 'view',
          title: 'View Record'
        }
      ]
    });
  }

  /**
   * Show weather alert
   */
  async showWeatherAlert(message: string, severity: 'low' | 'medium' | 'high'): Promise<void> {
    await this.showNotification({
      title: 'Weather Alert - AgriIntel',
      body: message,
      icon: '/logo192.png',
      tag: 'weather-alert',
      data: {
        type: 'weather',
        severity: severity
      },
      requireInteraction: severity === 'high',
      actions: [
        {
          action: 'view',
          title: 'View Weather'
        }
      ]
    });
  }

  /**
   * Get notification permission status
   */
  getPermissionStatus(): NotificationPermission {
    if (!('Notification' in window)) {
      return 'denied';
    }
    return Notification.permission;
  }

  /**
   * Check if push notifications are supported
   */
  isPushSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window;
  }
}

// Create singleton instance
const pushNotificationService = new PushNotificationService();

export default pushNotificationService;
export type { PushNotificationPayload, NotificationAction };
