// This file sets up polyfills for Node.js modules used by MongoDB
// Simplified version to avoid webpack compilation issues

// Import required polyfills
import { <PERSON><PERSON><PERSON> } from 'buffer';
import process from 'process/browser';

// Import our simplified polyfills
import mockImplementations, {
  fs,
  net,
  tls,
  dns,
  child_process,
  fsPromises,
  timersPromises
} from './polyfills';

// Make Buffer and process available globally
window.Buffer = Buffer;
window.process = process;

// Define global variables for Node.js modules
window.global = window;
window.global.process = process;
window.global.Buffer = Buffer;

// Use mock implementations for modules without browser versions
window.fs = fs;
window.net = net;
window.tls = tls;
window.dns = dns;
window.child_process = child_process;
window['fs/promises'] = fsPromises;
window['timers/promises'] = timersPromises;

console.log('Simplified polyfills have been set up successfully!');
