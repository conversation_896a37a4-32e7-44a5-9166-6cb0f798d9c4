/**
 * Business Analysis Schema
 * 
 * Defines the MongoDB schema for the business analysis collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Business Strategy Schema
const businessStrategySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['growth', 'efficiency', 'diversification', 'specialization', 'other'],
    default: 'growth'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['planning', 'active', 'completed', 'on hold', 'cancelled'],
    default: 'planning'
  },
  goals: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    targetValue: {
      type: Number
    },
    unit: {
      type: String
    },
    currentValue: {
      type: Number,
      default: 0
    },
    progress: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    dueDate: {
      type: Date
    },
    status: {
      type: String,
      enum: ['not started', 'in progress', 'completed', 'overdue'],
      default: 'not started'
    }
  }],
  actions: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    assignedTo: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    startDate: {
      type: Date
    },
    dueDate: {
      type: Date
    },
    completedDate: {
      type: Date
    },
    status: {
      type: String,
      enum: ['not started', 'in progress', 'completed', 'overdue'],
      default: 'not started'
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    notes: {
      type: String
    }
  }],
  budget: {
    planned: {
      type: Number,
      min: 0
    },
    actual: {
      type: Number,
      min: 0,
      default: 0
    },
    currency: {
      type: String,
      default: 'USD'
    }
  },
  risks: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    impact: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    probability: {
      type: String,
      enum: ['low', 'medium', 'high', 'very high'],
      default: 'medium'
    },
    mitigationPlan: {
      type: String
    },
    status: {
      type: String,
      enum: ['identified', 'monitored', 'mitigated', 'occurred', 'closed'],
      default: 'identified'
    }
  }],
  attachments: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Market Analysis Schema
const marketAnalysisSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  marketSegments: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    size: {
      type: Number,
      min: 0
    },
    growthRate: {
      type: Number
    },
    trends: [{
      type: String,
      trim: true
    }],
    opportunities: [{
      type: String,
      trim: true
    }],
    threats: [{
      type: String,
      trim: true
    }]
  }],
  competitors: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    strengths: [{
      type: String,
      trim: true
    }],
    weaknesses: [{
      type: String,
      trim: true
    }],
    marketShare: {
      type: Number,
      min: 0,
      max: 100
    },
    pricePoints: {
      type: String
    },
    notes: {
      type: String
    }
  }],
  pricingAnalysis: {
    currentPrices: [{
      product: {
        type: String,
        required: true,
        trim: true
      },
      price: {
        type: Number,
        required: true,
        min: 0
      },
      unit: {
        type: String,
        required: true
      },
      date: {
        type: Date,
        default: Date.now
      }
    }],
    marketAverages: [{
      product: {
        type: String,
        required: true,
        trim: true
      },
      price: {
        type: Number,
        required: true,
        min: 0
      },
      unit: {
        type: String,
        required: true
      },
      source: {
        type: String
      },
      date: {
        type: Date,
        default: Date.now
      }
    }],
    trends: {
      type: String
    },
    recommendations: {
      type: String
    }
  },
  swotAnalysis: {
    strengths: [{
      type: String,
      trim: true
    }],
    weaknesses: [{
      type: String,
      trim: true
    }],
    opportunities: [{
      type: String,
      trim: true
    }],
    threats: [{
      type: String,
      trim: true
    }]
  },
  conclusions: {
    type: String
  },
  recommendations: {
    type: String
  },
  attachments: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Predictive Analysis Schema
const predictiveAnalysisSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  type: {
    type: String,
    enum: ['production', 'financial', 'market', 'breeding', 'health', 'other'],
    default: 'production'
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  timeframe: {
    start: {
      type: Date,
      required: true
    },
    end: {
      type: Date,
      required: true
    }
  },
  methodology: {
    type: String,
    enum: ['statistical', 'machine learning', 'expert opinion', 'historical trend', 'hybrid', 'other'],
    default: 'statistical'
  },
  parameters: {
    type: Map,
    of: Schema.Types.Mixed
  },
  dataPoints: [{
    date: {
      type: Date,
      required: true
    },
    values: {
      type: Map,
      of: Schema.Types.Mixed,
      required: true
    }
  }],
  results: {
    summary: {
      type: String
    },
    keyFindings: [{
      type: String,
      trim: true
    }],
    data: {
      type: Schema.Types.Mixed
    }
  },
  confidence: {
    level: {
      type: Number,
      min: 0,
      max: 100
    },
    factors: [{
      type: String,
      trim: true
    }]
  },
  recommendations: {
    type: String
  },
  attachments: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
businessStrategySchema.index({ type: 1 });
businessStrategySchema.index({ status: 1 });
businessStrategySchema.index({ startDate: 1, endDate: 1 });

marketAnalysisSchema.index({ date: -1 });

predictiveAnalysisSchema.index({ type: 1 });
predictiveAnalysisSchema.index({ date: -1 });
predictiveAnalysisSchema.index({ 'timeframe.start': 1, 'timeframe.end': 1 });

// Pre-save hook to update timestamps
businessStrategySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

marketAnalysisSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

predictiveAnalysisSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const BusinessStrategy = mongoose.model('BusinessStrategy', businessStrategySchema);
const MarketAnalysis = mongoose.model('MarketAnalysis', marketAnalysisSchema);
const PredictiveAnalysis = mongoose.model('PredictiveAnalysis', predictiveAnalysisSchema);

module.exports = {
  BusinessStrategy,
  MarketAnalysis,
  PredictiveAnalysis
};
