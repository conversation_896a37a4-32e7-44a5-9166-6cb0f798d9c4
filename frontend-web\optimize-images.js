/**
 * <PERSON><PERSON>t to optimize images in the frontend application
 * This script converts images to WebP format and optimizes them for better performance
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const glob = require('glob');

// Configuration
const imageDirectories = [
  'public/images',
  'src/assets/images'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

/**
 * Check if required packages are installed
 */
function checkDependencies() {
  try {
    // Check if sharp is installed
    require.resolve('sharp');
    return true;
  } catch (error) {
    console.log(`${colors.fg.yellow}Required package 'sharp' is not installed.${colors.reset}`);
    console.log(`${colors.fg.yellow}Installing required packages...${colors.reset}`);
    
    try {
      execSync('npm install --save-dev sharp', { stdio: 'inherit' });
      console.log(`${colors.fg.green}✓ Dependencies installed successfully${colors.reset}`);
      return true;
    } catch (installError) {
      console.error(`${colors.fg.red}✗ Failed to install dependencies${colors.reset}`);
      console.error(installError.message);
      return false;
    }
  }
}

/**
 * Find all images in the specified directories
 * @returns {Array<string>} Array of image file paths
 */
function findImages() {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
  let images = [];
  
  for (const dir of imageDirectories) {
    for (const ext of imageExtensions) {
      const pattern = `${dir}/**/*.${ext}`;
      const files = glob.sync(pattern);
      images = [...images, ...files];
    }
  }
  
  return images;
}

/**
 * Convert an image to WebP format
 * @param {string} imagePath Path to the image file
 * @returns {boolean} Whether the conversion was successful
 */
async function convertToWebP(imagePath) {
  const sharp = require('sharp');
  const outputPath = imagePath.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
  
  try {
    await sharp(imagePath)
      .webp({ quality: 80 })
      .toFile(outputPath);
    
    console.log(`${colors.fg.green}✓ Converted: ${colors.reset}${imagePath} -> ${outputPath}`);
    return true;
  } catch (error) {
    console.error(`${colors.fg.red}✗ Failed to convert: ${colors.reset}${imagePath}`);
    console.error(`  ${error.message}`);
    return false;
  }
}

/**
 * Main function to optimize all images
 */
async function main() {
  console.log(`${colors.bright}${colors.fg.cyan}=== Optimizing Images ===${colors.reset}\n`);
  
  // Check dependencies
  if (!checkDependencies()) {
    process.exit(1);
  }
  
  // Find all images
  const images = findImages();
  console.log(`${colors.fg.cyan}Found ${images.length} images to optimize${colors.reset}\n`);
  
  if (images.length === 0) {
    console.log(`${colors.fg.yellow}No images found in the specified directories.${colors.reset}`);
    process.exit(0);
  }
  
  // Convert images to WebP
  let successCount = 0;
  let failCount = 0;
  
  for (const imagePath of images) {
    const success = await convertToWebP(imagePath);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  // Print summary
  console.log(`\n${colors.bright}${colors.fg.cyan}=== Optimization Summary ===${colors.reset}`);
  console.log(`${colors.fg.green}✓ Successfully optimized: ${successCount} images${colors.reset}`);
  
  if (failCount > 0) {
    console.log(`${colors.fg.red}✗ Failed to optimize: ${failCount} images${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`${colors.fg.green}${colors.bright}All images optimized successfully!${colors.reset}`);
    process.exit(0);
  }
}

// Run the main function
main().catch(error => {
  console.error(`${colors.fg.red}${colors.bright}An unexpected error occurred:${colors.reset}`);
  console.error(error);
  process.exit(1);
});
