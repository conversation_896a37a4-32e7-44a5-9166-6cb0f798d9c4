import React, { useState, useEffect } from 'react';
import { Box, useTheme, alpha } from '@mui/material';

interface ModuleBackgroundProps {
  module: string;
  children: React.ReactNode;
  opacity?: number;
  interval?: number;
}

/**
 * Component to display rotating background images for modules
 * @param module - The module name (animals, health, breeding, etc.)
 * @param children - The content to display on top of the background
 * @param opacity - The opacity of the background (0-1)
 * @param interval - The interval in milliseconds to rotate images
 */
const ModuleBackground: React.FC<ModuleBackgroundProps> = ({
  module,
  children,
  opacity = 0.15,
  interval = 10000
}) => {
  const theme = useTheme();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [images, setImages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Get images for the module
  useEffect(() => {
    const getImagesForModule = () => {
      // Default images if module-specific ones aren't available
      const defaultImages = ['/images/dashboard/main-dashboard.jpg'];
      
      // Module-specific image paths
      const moduleImageMap: Record<string, string[]> = {
        animals: [
          '/images/modules/animals/cattle-1.jpeg',
          '/images/modules/animals/cattle-2.avif',
          '/images/modules/animals/cattle-3.jpeg',
          '/images/modules/animals/cattle-4.jpeg',
          '/images/modules/animals/cattle-5.avif'
        ],
        health: [
          '/images/modules/health/health-main.png',
          '/images/modules/health/veterinary-1.jpg',
          '/images/modules/health/veterinary-2.jpg',
          '/images/modules/health/veterinary-3.jpg',
          '/images/modules/health/veterinary-main.jpg'
        ],
        breeding: [
          '/images/modules/breeding/breeding-main.png'
        ],
        feeding: [
          '/images/modules/feeding/feed-main.jpeg',
          '/images/modules/feeding/feed-2.jpeg',
          '/images/modules/feeding/feed-sheep.jpeg'
        ],
        commercial: [
          '/images/modules/commercial/commercial-main.webp',
          '/images/modules/commercial/commercial-1.jpeg',
          '/images/modules/commercial/commercial-2.jpeg',
          '/images/modules/commercial/commercial-3.jpeg'
        ],
        financial: [
          '/images/modules/commercial/commercial-main.webp',
          '/images/modules/commercial/commercial-1.jpeg'
        ],
        compliance: [
          '/images/modules/commercial/commercial-2.jpeg',
          '/images/modules/commercial/commercial-3.jpeg'
        ],
        rfid: [
          '/images/modules/rfid/rfid-1.webp',
          '/images/modules/rfid/rfid-2.jpg',
          '/images/modules/rfid/rfid-3.jpg',
          '/images/modules/rfid/rfid-4.jpg'
        ],
        reports: [
          '/images/modules/commercial/commercial-1.jpeg',
          '/images/modules/commercial/commercial-2.jpeg'
        ],
        settings: [
          '/images/dashboard/main-dashboard.jpg'
        ],
        login: [
          '/images/dashboard/main-dashboard.jpg'
        ]
      };
      
      // Return module-specific images or default
      return moduleImageMap[module.toLowerCase()] || defaultImages;
    };
    
    setImages(getImagesForModule());
    setIsLoading(false);
  }, [module]);

  // Rotate images
  useEffect(() => {
    if (images.length <= 1) return;
    
    const timer = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, interval);
    
    return () => clearInterval(timer);
  }, [images, interval]);

  if (isLoading) {
    return <>{children}</>;
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        borderRadius: 1,
      }}
    >
      {/* Background image */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${images[currentImageIndex]})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: opacity,
          transition: 'opacity 1s ease-in-out',
          zIndex: 0,
          '&::after': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha(theme.palette.background.paper, 0.85),
            zIndex: 1
          }
        }}
      />
      
      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 2,
          height: '100%'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ModuleBackground;
