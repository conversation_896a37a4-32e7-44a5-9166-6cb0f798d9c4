/**
 * Animal Validation Schemas
 * 
 * This module provides validation schemas for animal-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// Animal species
const species = ['cattle', 'sheep', 'goat', 'pig', 'chicken', 'duck', 'other'];

// Animal genders
const genders = ['male', 'female', 'unknown'];

// Animal statuses
const statuses = ['active', 'sold', 'deceased', 'transferred', 'quarantined'];

// Create animal validation schema
const createAnimalSchema = Joi.object({
  tagNumber: Joi.string().trim().required().messages({
    'string.empty': 'Tag number cannot be empty',
    'any.required': 'Tag number is required'
  }),
  name: Joi.string().trim().allow('').messages({
    'string.base': 'Name must be a string'
  }),
  species: Joi.string().valid(...species).required().messages({
    'any.only': `Species must be one of: ${species.join(', ')}`,
    'any.required': 'Species is required'
  }),
  breed: Joi.string().trim().allow('').messages({
    'string.base': 'Breed must be a string'
  }),
  gender: Joi.string().valid(...genders).required().messages({
    'any.only': `Gender must be one of: ${genders.join(', ')}`,
    'any.required': 'Gender is required'
  }),
  dateOfBirth: Joi.date().iso().allow(null).messages({
    'date.base': 'Date of birth must be a valid date',
    'date.format': 'Date of birth must be in ISO format (YYYY-MM-DD)'
  }),
  weight: Joi.number().positive().allow(null).messages({
    'number.base': 'Weight must be a number',
    'number.positive': 'Weight must be positive'
  }),
  status: Joi.string().valid(...statuses).default('active').messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  location: Joi.string().trim().allow('').messages({
    'string.base': 'Location must be a string'
  }),
  purchaseDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Purchase date must be a valid date',
    'date.format': 'Purchase date must be in ISO format (YYYY-MM-DD)'
  }),
  purchasePrice: Joi.number().positive().allow(null).messages({
    'number.base': 'Purchase price must be a number',
    'number.positive': 'Purchase price must be positive'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  parentMale: Joi.string().trim().allow('').messages({
    'string.base': 'Male parent ID must be a string'
  }),
  parentFemale: Joi.string().trim().allow('').messages({
    'string.base': 'Female parent ID must be a string'
  }),
  birthWeight: Joi.number().positive().allow(null).messages({
    'number.base': 'Birth weight must be a number',
    'number.positive': 'Birth weight must be positive'
  }),
  images: Joi.array().items(Joi.string()).default([]).messages({
    'array.base': 'Images must be an array'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

// Update animal validation schema
const updateAnimalSchema = Joi.object({
  tagNumber: Joi.string().trim().messages({
    'string.empty': 'Tag number cannot be empty'
  }),
  name: Joi.string().trim().allow('').messages({
    'string.base': 'Name must be a string'
  }),
  species: Joi.string().valid(...species).messages({
    'any.only': `Species must be one of: ${species.join(', ')}`
  }),
  breed: Joi.string().trim().allow('').messages({
    'string.base': 'Breed must be a string'
  }),
  gender: Joi.string().valid(...genders).messages({
    'any.only': `Gender must be one of: ${genders.join(', ')}`
  }),
  dateOfBirth: Joi.date().iso().allow(null).messages({
    'date.base': 'Date of birth must be a valid date',
    'date.format': 'Date of birth must be in ISO format (YYYY-MM-DD)'
  }),
  weight: Joi.number().positive().allow(null).messages({
    'number.base': 'Weight must be a number',
    'number.positive': 'Weight must be positive'
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  location: Joi.string().trim().allow('').messages({
    'string.base': 'Location must be a string'
  }),
  purchaseDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Purchase date must be a valid date',
    'date.format': 'Purchase date must be in ISO format (YYYY-MM-DD)'
  }),
  purchasePrice: Joi.number().positive().allow(null).messages({
    'number.base': 'Purchase price must be a number',
    'number.positive': 'Purchase price must be positive'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  parentMale: Joi.string().trim().allow('').messages({
    'string.base': 'Male parent ID must be a string'
  }),
  parentFemale: Joi.string().trim().allow('').messages({
    'string.base': 'Female parent ID must be a string'
  }),
  birthWeight: Joi.number().positive().allow(null).messages({
    'number.base': 'Birth weight must be a number',
    'number.positive': 'Birth weight must be positive'
  }),
  images: Joi.array().items(Joi.string()).messages({
    'array.base': 'Images must be an array'
  }),
  metadata: Joi.object().messages({
    'object.base': 'Metadata must be an object'
  })
});

// Get animals query validation schema
const getAnimalsQuerySchema = Joi.object({
  species: Joi.string().valid(...species).messages({
    'any.only': `Species must be one of: ${species.join(', ')}`
  }),
  gender: Joi.string().valid(...genders).messages({
    'any.only': `Gender must be one of: ${genders.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  location: Joi.string().trim().messages({
    'string.base': 'Location must be a string'
  }),
  minWeight: Joi.number().positive().messages({
    'number.base': 'Minimum weight must be a number',
    'number.positive': 'Minimum weight must be positive'
  }),
  maxWeight: Joi.number().positive().messages({
    'number.base': 'Maximum weight must be a number',
    'number.positive': 'Maximum weight must be positive'
  }),
  minAge: Joi.number().integer().min(0).messages({
    'number.base': 'Minimum age must be a number',
    'number.integer': 'Minimum age must be an integer',
    'number.min': 'Minimum age cannot be negative'
  }),
  maxAge: Joi.number().integer().min(0).messages({
    'number.base': 'Maximum age must be a number',
    'number.integer': 'Maximum age must be an integer',
    'number.min': 'Maximum age cannot be negative'
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  })
}).concat(schemas.pagination);

// Animal ID parameter validation schema
const animalIdParamSchema = Joi.object({
  id: schemas.id
});

module.exports = {
  createAnimalSchema,
  updateAnimalSchema,
  getAnimalsQuerySchema,
  animalIdParamSchema,
  species,
  genders,
  statuses
};
