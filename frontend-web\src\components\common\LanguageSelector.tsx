import React, { useState } from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  Box,
  Typography,
  Tooltip,
  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
  useTheme,
  alpha
} from '@mui/material';
import {
  Language as LanguageIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';

// South African Official Languages
const languages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇿🇦' },
  { code: 'af', name: 'Afrikaans', nativeName: 'Afrikaans', flag: '🇿🇦' },
  { code: 'zu', name: 'Zulu', nativeName: 'isiZulu', flag: '🇿🇦' },
  { code: 'xh', name: 'Xhosa', nativeName: 'isiXhosa', flag: '🇿🇦' },
  { code: 'st', name: 'Sotho', nativeName: 'Sesotho', flag: '🇿🇦' },
  { code: 'tn', name: 'Ts<PERSON>', nativeName: '<PERSON><PERSON><PERSON>', flag: '🇿🇦' },
  { code: 'ss', name: '<PERSON><PERSON>', nativeName: 'si<PERSON><PERSON>', flag: '🇿🇦' },
  { code: 've', name: 'Venda', nativeName: 'Tshivenḓa', flag: '🇿🇦' },
  { code: 'ts', name: 'Tsonga', nativeName: 'Xitsonga', flag: '🇿🇦' },
  { code: 'nr', name: 'Ndebele', nativeName: 'isiNdebele', flag: '🇿🇦' },
  { code: 'nso', name: 'Northern Sotho', nativeName: 'Sepedi', flag: '🇿🇦' }
];

interface LanguageSelectorProps {
  variant?: 'dropdown' | 'menu' | 'compact';
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'secondary' | 'inherit';
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  variant = 'dropdown',
  showLabel = true,
  size = 'medium',
  color = 'primary'
}) => {
  const { language, setLanguage, translate } = useLanguage();
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const currentLanguage = languages.find(lang => lang.code === language) || languages[0];

  const handleLanguageChange = (languageCode: string) => {
    setLanguage(languageCode);
    setAnchorEl(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  if (variant === 'menu') {
    return (
      <>
        <Tooltip title={translate('common.selectLanguage', { fallback: 'Select Language' })}>
          <IconButton
            onClick={handleMenuClick}
            size={size}
            sx={{
              color: color === 'inherit' ? 'inherit' : theme.palette[color].main,
              '&:hover': {
                backgroundColor: alpha(
                  color === 'inherit' ? theme.palette.text.primary : theme.palette[color].main,
                  0.1
                )
              }
            }}
          >
            <LanguageIcon />
          </IconButton>
        </Tooltip>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleMenuClose}
          PaperProps={{
            sx: {
              maxHeight: 400,
              width: 280,
              mt: 1,
              borderRadius: 2,
              boxShadow: theme.shadows[8]
            }
          }}
        >
          {languages.map((language) => (
            <MenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              selected={language.code === currentLanguage.code}
              sx={{
                py: 1.5,
                px: 2,
                '&.Mui-selected': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.2)
                  }
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <Typography variant="h6" component="span">
                  {language.flag}
                </Typography>
              </ListItemIcon>
              <ListItemText
                primary={language.name}
                secondary={language.nativeName}
                primaryTypographyProps={{
                  fontWeight: language.code === currentLanguage.code ? 600 : 400
                }}
              />
            </MenuItem>
          ))}
        </Menu>
      </>
    );
  }

  if (variant === 'compact') {
    return (
      <Box
        onClick={handleMenuClick}
        sx={{
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          px: 1,
          py: 0.5,
          borderRadius: 1,
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.1)
          }
        }}
      >
        <Typography variant="h6" component="span" sx={{ mr: 0.5 }}>
          {currentLanguage.flag}
        </Typography>
        <Typography variant="body2" sx={{ mr: 0.5, fontWeight: 500 }}>
          {currentLanguage.code.toUpperCase()}
        </Typography>
        <ExpandMoreIcon fontSize="small" />
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleMenuClose}
          PaperProps={{
            sx: {
              maxHeight: 400,
              width: 280,
              mt: 1,
              borderRadius: 2,
              boxShadow: theme.shadows[8]
            }
          }}
        >
          {languages.map((language) => (
            <MenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              selected={language.code === currentLanguage.code}
              sx={{
                py: 1.5,
                px: 2,
                '&.Mui-selected': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.2)
                  }
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                <Typography variant="h6" component="span">
                  {language.flag}
                </Typography>
              </ListItemIcon>
              <ListItemText
                primary={language.name}
                secondary={language.nativeName}
                primaryTypographyProps={{
                  fontWeight: language.code === currentLanguage.code ? 600 : 400
                }}
              />
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }

  // Default dropdown variant
  return (
    <FormControl size={size} sx={{ minWidth: 200 }}>
      {showLabel && (
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          {translate('common.language', { fallback: 'Language' })}
        </Typography>
      )}
      <Select
        value={currentLanguage.code}
        onChange={(e) => handleLanguageChange(e.target.value)}
        displayEmpty
        sx={{
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }
        }}
        renderValue={(value) => {
          const selected = languages.find(lang => lang.code === value);
          return (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="h6" component="span">
                {selected?.flag}
              </Typography>
              <Typography variant="body2">
                {selected?.name} ({selected?.nativeName})
              </Typography>
            </Box>
          );
        }}
      >
        {languages.map((language) => (
          <MenuItem key={language.code} value={language.code}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
              <Typography variant="h6" component="span">
                {language.flag}
              </Typography>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {language.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {language.nativeName}
                </Typography>
              </Box>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default LanguageSelector;
