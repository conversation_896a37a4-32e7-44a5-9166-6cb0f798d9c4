import React, { ReactNode, useState } from 'react';
import { Box, Tabs, Tab, Typography, useTheme, alpha, IconButton, Menu, MenuItem, Badge } from '@mui/material';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import { getSelectableTabStyles } from '../../utils/selectionUtils';

interface NavTab {
  id: string;
  label: string;
  icon?: ReactNode;
  content: ReactNode;
  badge?: number;
  disabled?: boolean;
}

interface ResponsiveNavTabsProps {
  tabs: NavTab[];
  initialTab?: string;
  onChange?: (tabId: string) => void;
  variant?: 'standard' | 'fullWidth' | 'scrollable';
  accentColor?: string;
  centered?: boolean;
  showContent?: boolean;
}

/**
 * ResponsiveNavTabs component
 * Provides a consistent, responsive navigation tabs component
 */
const ResponsiveNavTabs: React.FC<ResponsiveNavTabsProps> = ({
  tabs,
  initialTab,
  onChange,
  variant = 'standard',
  accentColor,
  centered = false,
  showContent = true
}) => {
  const theme = useTheme();
  const { isMobile } = useResponsive();
  const [activeTab, setActiveTab] = useState<string>(initialTab || (tabs.length > 0 ? tabs[0].id : ''));
  const [moreMenuAnchorEl, setMoreMenuAnchorEl] = useState<null | HTMLElement>(null);

  // Get primary color
  const primaryColor = accentColor || theme.palette.primary.main;

  // Determine which tabs to show in the overflow menu
  const visibleTabs = isMobile ? tabs.slice(0, 3) : tabs;
  const overflowTabs = isMobile ? tabs.slice(3) : [];
  const showMoreMenu = overflowTabs.length > 0;

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
    if (onChange) {
      onChange(newValue);
    }
  };

  // Handle more menu
  const handleMoreMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMoreMenuAnchorEl(event.currentTarget);
  };

  const handleMoreMenuClose = () => {
    setMoreMenuAnchorEl(null);
  };

  const handleMoreMenuItemClick = (tabId: string) => {
    setActiveTab(tabId);
    if (onChange) {
      onChange(tabId);
    }
    handleMoreMenuClose();
  };

  // Get active tab content
  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider', position: 'relative' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant={isMobile ? 'scrollable' : variant}
          scrollButtons={isMobile ? 'auto' : false}
          allowScrollButtonsMobile={isMobile}
          centered={!isMobile && centered}
          sx={{
            ...getSelectableTabStyles(),
            '& .MuiTabs-indicator': {
              backgroundColor: primaryColor,
              height: 3,
              borderTopLeftRadius: 3,
              borderTopRightRadius: 3
            },
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: { xs: '0.875rem', sm: '0.9rem' },
              minWidth: isMobile ? 'auto' : 120,
              '&.Mui-selected': {
                color: primaryColor,
                fontWeight: 600
              }
            }
          }}
        >
          {visibleTabs.map((tab) => (
            <Tab
              key={tab.id}
              value={tab.id}
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  {tab.badge ? (
                    <Badge
                      badgeContent={tab.badge}
                      color="error"
                      sx={{
                        '& .MuiBadge-badge': {
                          fontSize: '0.65rem',
                          height: 16,
                          minWidth: 16,
                          padding: '0 4px'
                        }
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {tab.icon && <Box sx={{ mr: 1 }}>{tab.icon}</Box>}
                        {tab.label}
                      </Box>
                    </Badge>
                  ) : (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {tab.icon && <Box sx={{ mr: 1 }}>{tab.icon}</Box>}
                      {tab.label}
                    </Box>
                  )}
                </Box>
              }
              disabled={tab.disabled}
            />
          ))}

          {/* More menu for overflow tabs */}
          {showMoreMenu && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                borderBottom: activeTab && overflowTabs.some(tab => tab.id === activeTab)
                  ? `3px solid ${primaryColor}`
                  : 'none',
                ml: 1
              }}
            >
              <IconButton
                size="small"
                onClick={handleMoreMenuOpen}
                sx={{
                  color: activeTab && overflowTabs.some(tab => tab.id === activeTab)
                    ? primaryColor
                    : 'text.secondary'
                }}
              >
                <MoreHorizIcon />
              </IconButton>
              <Menu
                anchorEl={moreMenuAnchorEl}
                open={Boolean(moreMenuAnchorEl)}
                onClose={handleMoreMenuClose}
              >
                {overflowTabs.map((tab) => (
                  <MenuItem
                    key={tab.id}
                    onClick={() => handleMoreMenuItemClick(tab.id)}
                    disabled={tab.disabled}
                    selected={activeTab === tab.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      minWidth: 150
                    }}
                  >
                    {tab.badge ? (
                      <Badge
                        badgeContent={tab.badge}
                        color="error"
                        sx={{
                          '& .MuiBadge-badge': {
                            fontSize: '0.65rem',
                            height: 16,
                            minWidth: 16,
                            padding: '0 4px'
                          }
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {tab.icon && <Box sx={{ mr: 1 }}>{tab.icon}</Box>}
                          {tab.label}
                        </Box>
                      </Badge>
                    ) : (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {tab.icon && <Box sx={{ mr: 1 }}>{tab.icon}</Box>}
                        {tab.label}
                      </Box>
                    )}
                  </MenuItem>
                ))}
              </Menu>
            </Box>
          )}
        </Tabs>
      </Box>

      {/* Tab Content */}
      {showContent && activeTabContent && (
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          style={{
            padding: '16px 0',
            position: 'relative',
            zIndex: 1,
            pointerEvents: 'auto'
          }}
        >
          {activeTabContent}
        </motion.div>
      )}
    </Box>
  );
};

export default ResponsiveNavTabs;
