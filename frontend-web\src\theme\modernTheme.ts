import { createTheme, ThemeOptions } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';

// Modern AgriIntel Color Palette
const colors = {
  primary: {
    50: '#E8F5F3',
    100: '#C6E7E1',
    200: '#A0D7CD',
    300: '#7AC7B9',
    400: '#5EBBAA',
    500: '#42AF9B', // Main brand color
    600: '#3CA893',
    700: '#339F89',
    800: '#2B967F',
    900: '#1D866D',
  },
  secondary: {
    50: '#FFF8E1',
    100: '#FFECB3',
    200: '#FFE082',
    300: '#FFD54F',
    400: '#FFCA28',
    500: '#FFC107', // Golden accent
    600: '#FFB300',
    700: '#FFA000',
    800: '#FF8F00',
    900: '#FF6F00',
  },
  success: {
    50: '#E8F5E8',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50',
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
  },
  warning: {
    50: '#FFF3E0',
    100: '#FFE0B2',
    200: '#FFCC80',
    300: '#FFB74D',
    400: '#FFA726',
    500: '#FF9800',
    600: '#FB8C00',
    700: '#F57C00',
    800: '#EF6C00',
    900: '#E65100',
  },
  error: {
    50: '#FFEBEE',
    100: '#FFCDD2',
    200: '#EF9A9A',
    300: '#E57373',
    400: '#EF5350',
    500: '#F44336',
    600: '#E53935',
    700: '#D32F2F',
    800: '#C62828',
    900: '#B71C1C',
  },
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
  farm: {
    earth: '#8D6E63',
    grass: '#689F38',
    sky: '#42A5F5',
    barn: '#D84315',
    wheat: '#FFA000',
  }
};

// Modern Typography
const typography = {
  fontFamily: [
    'Inter',
    'Roboto',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif',
  ].join(','),
  h1: {
    fontSize: '3.5rem',
    fontWeight: 800,
    lineHeight: 1.2,
    letterSpacing: '-0.02em',
  },
  h2: {
    fontSize: '2.75rem',
    fontWeight: 700,
    lineHeight: 1.3,
    letterSpacing: '-0.01em',
  },
  h3: {
    fontSize: '2.25rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h4: {
    fontSize: '1.875rem',
    fontWeight: 600,
    lineHeight: 1.4,
  },
  h5: {
    fontSize: '1.5rem',
    fontWeight: 500,
    lineHeight: 1.5,
  },
  h6: {
    fontSize: '1.25rem',
    fontWeight: 500,
    lineHeight: 1.5,
  },
  body1: {
    fontSize: '1rem',
    lineHeight: 1.6,
    fontWeight: 400,
  },
  body2: {
    fontSize: '0.875rem',
    lineHeight: 1.5,
    fontWeight: 400,
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 600,
    textTransform: 'none' as const,
    letterSpacing: '0.02em',
  },
};

// Modern Shadows
const shadows = [
  'none',
  '0px 2px 4px rgba(0, 0, 0, 0.05)',
  '0px 4px 8px rgba(0, 0, 0, 0.08)',
  '0px 8px 16px rgba(0, 0, 0, 0.1)',
  '0px 12px 24px rgba(0, 0, 0, 0.12)',
  '0px 16px 32px rgba(0, 0, 0, 0.15)',
  '0px 20px 40px rgba(0, 0, 0, 0.18)',
  '0px 24px 48px rgba(0, 0, 0, 0.2)',
  '0px 32px 64px rgba(0, 0, 0, 0.25)',
  // Continue with more shadows...
  ...Array(16).fill('0px 32px 64px rgba(0, 0, 0, 0.25)')
] as any;

// Create the modern theme
export const createModernTheme = (mode: 'light' | 'dark' = 'light') => {
  const isLight = mode === 'light';
  
  const themeOptions: ThemeOptions = {
    palette: {
      mode,
      primary: {
        main: colors.primary[500],
        light: colors.primary[300],
        dark: colors.primary[700],
        contrastText: '#ffffff',
      },
      secondary: {
        main: colors.secondary[500],
        light: colors.secondary[300],
        dark: colors.secondary[700],
        contrastText: '#000000',
      },
      success: {
        main: colors.success[500],
        light: colors.success[300],
        dark: colors.success[700],
      },
      warning: {
        main: colors.warning[500],
        light: colors.warning[300],
        dark: colors.warning[700],
      },
      error: {
        main: colors.error[500],
        light: colors.error[300],
        dark: colors.error[700],
      },
      background: {
        default: isLight ? '#FAFBFC' : '#0A0E13',
        paper: isLight ? '#FFFFFF' : '#1A1D23',
      },
      text: {
        primary: isLight ? colors.neutral[900] : colors.neutral[50],
        secondary: isLight ? colors.neutral[600] : colors.neutral[300],
      },
      divider: isLight ? colors.neutral[200] : colors.neutral[700],
    },
    typography,
    shadows,
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            padding: '12px 24px',
            fontSize: '0.875rem',
            fontWeight: 600,
            textTransform: 'none',
            boxShadow: 'none',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0px 8px 25px rgba(66, 175, 155, 0.25)',
            },
          },
          contained: {
            background: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
            '&:hover': {
              background: `linear-gradient(135deg, ${colors.primary[600]} 0%, ${colors.primary[700]} 100%)`,
            },
          },
          outlined: {
            borderWidth: 2,
            '&:hover': {
              borderWidth: 2,
              backgroundColor: alpha(colors.primary[500], 0.08),
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            border: `1px solid ${isLight ? colors.neutral[200] : colors.neutral[700]}`,
            boxShadow: isLight 
              ? '0px 4px 20px rgba(0, 0, 0, 0.08)' 
              : '0px 4px 20px rgba(0, 0, 0, 0.3)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: isLight 
                ? '0px 12px 40px rgba(0, 0, 0, 0.15)' 
                : '0px 12px 40px rgba(0, 0, 0, 0.5)',
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 12,
              transition: 'all 0.3s ease',
              '&:hover': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: colors.primary[400],
                },
              },
              '&.Mui-focused': {
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: colors.primary[500],
                  borderWidth: 2,
                },
              },
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
            transition: 'all 0.2s ease',
            '&:hover': {
              transform: 'scale(1.05)',
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            border: `1px solid ${isLight ? colors.neutral[200] : colors.neutral[700]}`,
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: isLight ? 'rgba(255, 255, 255, 0.9)' : 'rgba(26, 29, 35, 0.9)',
            backdropFilter: 'blur(20px)',
            borderBottom: `1px solid ${isLight ? colors.neutral[200] : colors.neutral[700]}`,
            boxShadow: 'none',
          },
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: isLight ? 'rgba(255, 255, 255, 0.95)' : 'rgba(26, 29, 35, 0.95)',
            backdropFilter: 'blur(20px)',
            borderRight: `1px solid ${isLight ? colors.neutral[200] : colors.neutral[700]}`,
          },
        },
      },
    },
  };

  return createTheme(themeOptions);
};

// Export color palette for use in components
export { colors };

// Enhanced Gradient Collection
export const gradients = {
  // Primary gradients
  primary: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[600]} 100%)`,
  primaryLight: `linear-gradient(135deg, ${colors.primary[300]} 0%, ${colors.primary[500]} 100%)`,
  primaryDark: `linear-gradient(135deg, ${colors.primary[600]} 0%, ${colors.primary[800]} 100%)`,

  // Secondary gradients
  secondary: `linear-gradient(135deg, ${colors.secondary[400]} 0%, ${colors.secondary[600]} 100%)`,
  secondaryLight: `linear-gradient(135deg, ${colors.secondary[200]} 0%, ${colors.secondary[400]} 100%)`,

  // Status gradients
  success: `linear-gradient(135deg, ${colors.success[400]} 0%, ${colors.success[600]} 100%)`,
  warning: `linear-gradient(135deg, ${colors.warning[400]} 0%, ${colors.warning[600]} 100%)`,
  error: `linear-gradient(135deg, ${colors.error[400]} 0%, ${colors.error[600]} 100%)`,

  // Farm-themed gradients
  farm: `linear-gradient(135deg, ${colors.farm.grass} 0%, ${colors.primary[500]} 50%, ${colors.farm.sky} 100%)`,
  farmEarth: `linear-gradient(135deg, ${colors.farm.earth} 0%, ${colors.farm.wheat} 50%, ${colors.secondary[500]} 100%)`,
  farmSky: `linear-gradient(135deg, ${colors.farm.sky} 0%, ${colors.primary[400]} 50%, ${colors.farm.grass} 100%)`,

  // Atmospheric gradients
  sunset: `linear-gradient(135deg, ${colors.secondary[400]} 0%, ${colors.warning[500]} 50%, ${colors.error[400]} 100%)`,
  sunrise: `linear-gradient(135deg, ${colors.warning[300]} 0%, ${colors.secondary[400]} 50%, ${colors.primary[400]} 100%)`,
  ocean: `linear-gradient(135deg, ${colors.primary[400]} 0%, ${colors.farm.sky} 50%, ${colors.primary[600]} 100%)`,
  forest: `linear-gradient(135deg, ${colors.farm.grass} 0%, ${colors.success[500]} 50%, ${colors.primary[500]} 100%)`,

  // Module-specific gradients
  dashboard: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.farm.sky} 50%, ${colors.secondary[400]} 100%)`,
  animals: `linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 50%, ${colors.warning[400]} 100%)`,
  health: `linear-gradient(135deg, #4ECDC4 0%, #44A08D 50%, ${colors.success[500]} 100%)`,
  breeding: `linear-gradient(135deg, #FF8A80 0%, #FF5722 50%, ${colors.error[400]} 100%)`,
  feeding: `linear-gradient(135deg, #FFB74D 0%, #FF9800 50%, ${colors.secondary[500]} 100%)`,
  financial: `linear-gradient(135deg, #81C784 0%, #4CAF50 50%, ${colors.success[600]} 100%)`,
  inventory: `linear-gradient(135deg, #9575CD 0%, #673AB7 50%, ${colors.primary[600]} 100%)`,
  commercial: `linear-gradient(135deg, #F06292 0%, #E91E63 50%, ${colors.error[500]} 100%)`,
  reports: `linear-gradient(135deg, #64B5F6 0%, #2196F3 50%, ${colors.farm.sky} 100%)`,
  resources: `linear-gradient(135deg, #A1887F 0%, #8D6E63 50%, ${colors.farm.earth} 100%)`,
  settings: `linear-gradient(135deg, #90A4AE 0%, #607D8B 50%, ${colors.neutral[600]} 100%)`,
  compliance: `linear-gradient(135deg, #FFAB91 0%, #FF7043 50%, ${colors.warning[500]} 100%)`,

  // Background gradients
  pageBackground: `linear-gradient(135deg,
    ${colors.primary[50]} 0%,
    ${colors.secondary[50]} 25%,
    ${colors.success[50]} 50%,
    ${colors.farm.sky}20 75%,
    ${colors.primary[100]} 100%
  )`,
  cardBackground: `linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.95) 100%
  )`,
  darkPageBackground: `linear-gradient(135deg,
    ${colors.neutral[900]} 0%,
    ${colors.neutral[800]} 25%,
    ${colors.primary[900]} 50%,
    ${colors.neutral[800]} 75%,
    ${colors.neutral[900]} 100%
  )`,
};

// Animation utilities
export const animations = {
  fadeIn: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, ease: [0.4, 0, 0.2, 1] },
  },
  slideIn: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5, ease: [0.4, 0, 0.2, 1] },
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.4, ease: [0.4, 0, 0.2, 1] },
  },
  float: {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  },
  pulse: {
    animate: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  },
};
