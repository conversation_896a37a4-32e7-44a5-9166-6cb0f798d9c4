/**
 * API Service
 *
 * This service provides functions for interacting with the backend API.
 */

import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// API base URL - Updated to match backend configuration
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3002/api';

// Log API URL for debugging
console.log('Using API URL:', API_URL);

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Log the error for debugging
    console.error('API Error:', error.response?.data || error.message);

    // Handle token expiration
    if (error.response?.status === 401) {
      // Check if the error is due to token expiration
      const errorMessage = error.response?.data?.message;
      if (errorMessage === 'Invalid or expired token' || errorMessage === 'Token expired' || errorMessage === 'Unauthorized') {
        console.log('Token expired or invalid, redirecting to login');

        // Clear local storage
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Only redirect to login if not already on login page
        if (!window.location.pathname.includes('/login')) {
          // Redirect to login page
          window.location.href = '/login';
        }
      }
    }

    // Handle server errors
    if (error.response?.status === 500) {
      console.error('Server error:', error.response?.data);
    }

    // Handle network errors
    if (error.message === 'Network Error') {
      console.error('Network error - API server may be down');

      // If on login page, don't redirect
      if (!window.location.pathname.includes('/login')) {
        // Show a notification or handle offline mode
        console.log('Application is in offline mode');
      }
    }

    return Promise.reject(error);
  }
);

/**
 * Generic API request function
 * @param method - HTTP method
 * @param url - API endpoint
 * @param data - Request data
 * @param config - Axios config
 * @returns Promise with response data
 */
const request = async <T>(
  method: string,
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    // Log request for debugging
    console.log(`API Request: ${method.toUpperCase()} ${url}`, data);

    const response: AxiosResponse = await api.request({
      method,
      url,
      data,
      ...config,
    });

    // Log successful response for debugging
    console.log(`API Response: ${method.toUpperCase()} ${url}`, response.data);

    return response.data;
  } catch (error: any) {
    // Format error message
    let errorMessage = 'An error occurred';
    let errorDetails = null;

    if (error.response) {
      // Server responded with an error
      errorMessage = error.response.data.message || 'Server error';
      errorDetails = error.response.data;
      console.error(`API Error (${error.response.status}): ${method.toUpperCase()} ${url}`, errorDetails);
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response from server';
      console.error(`API No Response: ${method.toUpperCase()} ${url}`);
    } else {
      // Something else happened
      errorMessage = error.message;
      console.error(`API Error: ${method.toUpperCase()} ${url}`, error.message);
    }

    // Create enhanced error object
    const enhancedError = new Error(errorMessage);
    (enhancedError as any).details = errorDetails;
    (enhancedError as any).status = error.response?.status;

    throw enhancedError;
  }
};

/**
 * Authentication API
 */
export const authAPI = {
  /**
   * Login user
   * @param username - Username
   * @param password - Password
   * @returns Promise with login result
   */
  login: async (username: string, password: string) => {
    try {
      console.log(`Attempting login for user: ${username}`);
      const response = await request<any>('post', '/auth/login', { username, password });

      if (response.success) {
        console.log('Backend authentication successful');
        return response;
      } else {
        throw new Error(response.message || 'Authentication failed');
      }
    } catch (error) {
      console.error('Backend authentication failed:', error);

      // Only provide fallback for development/demo purposes
      // In production, this should be removed or limited to specific demo accounts
      if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_DEMO_MODE === 'true') {
        console.warn('Using fallback authentication - not recommended for production');

        const demoCredentials = [
          {
            username: 'Demo',
            password: '123',
            user: {
              id: 'demo-user-id',
              username: 'Demo',
              email: '<EMAIL>',
              firstName: 'Demo',
              lastName: 'User',
              role: 'beta',
              status: 'active',
              permissions: ['view_animals', 'create_animal', 'view_health_records', 'view_resources'],
              subscriptionTier: 'beta',
              lastLogin: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          },
          {
            username: 'Pro',
            password: '123',
            user: {
              id: 'pro-user-id',
              username: 'Pro',
              email: '<EMAIL>',
              firstName: 'Professional',
              lastName: 'User',
              role: 'professional',
              status: 'active',
              permissions: ['all'],
              subscriptionTier: 'professional',
              lastLogin: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          },
          {
            username: 'admin',
            password: 'Admin@123',
            user: {
              id: 'admin-user-id',
              username: 'admin',
              email: '<EMAIL>',
              firstName: 'Administrator',
              lastName: 'User',
              role: 'admin',
              status: 'active',
              permissions: ['all'],
              subscriptionTier: 'enterprise',
              lastLogin: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          }
        ];

        const fallbackUser = demoCredentials.find(u => u.username === username && u.password === password);

        if (fallbackUser) {
          console.log(`Using demo fallback authentication for user: ${username}`);
          return {
            success: true,
            user: fallbackUser.user,
            token: `demo-token-${fallbackUser.user.id}-${Date.now()}`,
            message: 'Demo authentication successful'
          };
        }
      }

      // If no fallback user found or not in demo mode, throw the original error
      throw new Error('Authentication failed - please check your credentials or ensure the backend server is running');
    }
  },

  /**
   * Register user
   * @param userData - User data
   * @returns Promise with register result
   */
  register: (userData: any) =>
    request<any>('post', '/auth/register', userData),

  /**
   * Get current user
   * @returns Promise with user data
   */
  getCurrentUser: () =>
    request<any>('get', '/auth/me'),

  /**
   * Change password
   * @param currentPassword - Current password
   * @param newPassword - New password
   * @returns Promise with change password result
   */
  changePassword: (currentPassword: string, newPassword: string) =>
    request<any>('post', '/auth/change-password', { currentPassword, newPassword }),

  /**
   * Refresh token
   * @returns Promise with refresh token result
   */
  refreshToken: () => {
    const token = localStorage.getItem('token');
    return request<any>('post', '/auth/refresh-token', { token });
  },

  /**
   * Logout user
   * @returns Promise with logout result
   */
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    return request<any>('post', '/auth/logout');
  }
};

/**
 * Users API
 */
export const usersAPI = {
  /**
   * Get all users
   * @param params - Query parameters
   * @returns Promise with users data
   */
  getUsers: (params?: any) =>
    request<any>('get', '/users', undefined, { params }),

  /**
   * Get user by ID
   * @param id - User ID
   * @returns Promise with user data
   */
  getUserById: (id: string) =>
    request<any>('get', `/users/${id}`),

  /**
   * Create user
   * @param userData - User data
   * @returns Promise with create user result
   */
  createUser: (userData: any) =>
    request<any>('post', '/users', userData),

  /**
   * Update user
   * @param id - User ID
   * @param userData - User data
   * @returns Promise with update user result
   */
  updateUser: (id: string, userData: any) =>
    request<any>('put', `/users/${id}`, userData),

  /**
   * Delete user
   * @param id - User ID
   * @returns Promise with delete user result
   */
  deleteUser: (id: string) =>
    request<any>('delete', `/users/${id}`),

  /**
   * Get user roles
   * @returns Promise with user roles
   */
  getRoles: () =>
    request<any>('get', '/users/roles'),

  /**
   * Get user permissions
   * @returns Promise with user permissions
   */
  getPermissions: () =>
    request<any>('get', '/users/permissions')
};

/**
 * Animals API
 */
export const animalsAPI = {
  /**
   * Get all animals
   * @param params - Query parameters
   * @returns Promise with animals data
   */
  getAnimals: (params?: any) =>
    request<any>('get', '/animals', undefined, { params }),

  /**
   * Get animal by ID
   * @param id - Animal ID
   * @returns Promise with animal data
   */
  getAnimalById: (id: string) =>
    request<any>('get', `/animals/${id}`),

  /**
   * Create animal
   * @param animalData - Animal data
   * @returns Promise with create animal result
   */
  createAnimal: (animalData: any) =>
    request<any>('post', '/animals', animalData),

  /**
   * Update animal
   * @param id - Animal ID
   * @param animalData - Animal data
   * @returns Promise with update animal result
   */
  updateAnimal: (id: string, animalData: any) =>
    request<any>('put', `/animals/${id}`, animalData),

  /**
   * Delete animal
   * @param id - Animal ID
   * @returns Promise with delete animal result
   */
  deleteAnimal: (id: string) =>
    request<any>('delete', `/animals/${id}`),

  /**
   * Get animal statistics
   * @returns Promise with animal statistics
   */
  getAnimalStats: () =>
    request<any>('get', '/animals/stats')
};

/**
 * Health API
 */
export const healthAPI = {
  /**
   * Get all health records
   * @param params - Query parameters
   * @returns Promise with health records data
   */
  getHealthRecords: (params?: any) =>
    request<any>('get', '/health/records', undefined, { params }),

  /**
   * Get health record by ID
   * @param id - Health record ID
   * @returns Promise with health record data
   */
  getHealthRecordById: (id: string) =>
    request<any>('get', `/health/records/${id}`),

  /**
   * Create health record
   * @param recordData - Health record data
   * @returns Promise with create health record result
   */
  createHealthRecord: (recordData: any) =>
    request<any>('post', '/health/records', recordData),

  /**
   * Update health record
   * @param id - Health record ID
   * @param recordData - Health record data
   * @returns Promise with update health record result
   */
  updateHealthRecord: (id: string, recordData: any) =>
    request<any>('put', `/health/records/${id}`, recordData),

  /**
   * Delete health record
   * @param id - Health record ID
   * @returns Promise with delete health record result
   */
  deleteHealthRecord: (id: string) =>
    request<any>('delete', `/health/records/${id}`),

  /**
   * Get health statistics
   * @returns Promise with health statistics
   */
  getHealthStats: () =>
    request<any>('get', '/health/stats'),

  /**
   * Get vaccination schedule
   * @param params - Query parameters
   * @returns Promise with vaccination schedule data
   */
  getVaccinations: (params?: any) =>
    request<any>('get', '/health/vaccinations', undefined, { params })
};

/**
 * Breeding API
 */
export const breedingAPI = {
  /**
   * Get all breeding records
   * @param params - Query parameters
   * @returns Promise with breeding records data
   */
  getBreedingRecords: (params?: any) =>
    request<any>('get', '/breeding/records', undefined, { params }),

  /**
   * Get breeding record by ID
   * @param id - Breeding record ID
   * @returns Promise with breeding record data
   */
  getBreedingRecordById: (id: string) =>
    request<any>('get', `/breeding/records/${id}`),

  /**
   * Create breeding record
   * @param recordData - Breeding record data
   * @returns Promise with create breeding record result
   */
  createBreedingRecord: (recordData: any) =>
    request<any>('post', '/breeding/records', recordData),

  /**
   * Update breeding record
   * @param id - Breeding record ID
   * @param recordData - Breeding record data
   * @returns Promise with update breeding record result
   */
  updateBreedingRecord: (id: string, recordData: any) =>
    request<any>('put', `/breeding/records/${id}`, recordData),

  /**
   * Delete breeding record
   * @param id - Breeding record ID
   * @returns Promise with delete breeding record result
   */
  deleteBreedingRecord: (id: string) =>
    request<any>('delete', `/breeding/records/${id}`),

  /**
   * Get all birth records
   * @param params - Query parameters
   * @returns Promise with birth records data
   */
  getBirthRecords: (params?: any) =>
    request<any>('get', '/breeding/births', undefined, { params }),

  /**
   * Create birth record
   * @param recordData - Birth record data
   * @returns Promise with create birth record result
   */
  createBirthRecord: (recordData: any) =>
    request<any>('post', '/breeding/births', recordData),

  /**
   * Get breeding statistics
   * @returns Promise with breeding statistics
   */
  getBreedingStats: () =>
    request<any>('get', '/breeding/stats')
};

/**
 * Financial API
 */
export const financialAPI = {
  /**
   * Get all transactions
   * @param params - Query parameters
   * @returns Promise with transactions data
   */
  getTransactions: (params?: any) =>
    request<any>('get', '/financial/transactions', undefined, { params }),

  /**
   * Get transaction by ID
   * @param id - Transaction ID
   * @returns Promise with transaction data
   */
  getTransactionById: (id: string) =>
    request<any>('get', `/financial/transactions/${id}`),

  /**
   * Create transaction
   * @param transactionData - Transaction data
   * @returns Promise with create transaction result
   */
  createTransaction: (transactionData: any) =>
    request<any>('post', '/financial/transactions', transactionData),

  /**
   * Update transaction
   * @param id - Transaction ID
   * @param transactionData - Transaction data
   * @returns Promise with update transaction result
   */
  updateTransaction: (id: string, transactionData: any) =>
    request<any>('put', `/financial/transactions/${id}`, transactionData),

  /**
   * Delete transaction
   * @param id - Transaction ID
   * @returns Promise with delete transaction result
   */
  deleteTransaction: (id: string) =>
    request<any>('delete', `/financial/transactions/${id}`),

  /**
   * Get financial summary
   * @param params - Query parameters
   * @returns Promise with financial summary data
   */
  getFinancialSummary: (params?: any) =>
    request<any>('get', '/financial/summary', undefined, { params })
};

/**
 * Inventory API
 */
export const inventoryAPI = {
  /**
   * Get all inventory items
   * @param params - Query parameters
   * @returns Promise with inventory items data
   */
  getInventoryItems: (params?: any) =>
    request<any>('get', '/inventory/items', undefined, { params }),

  /**
   * Get inventory item by ID
   * @param id - Inventory item ID
   * @returns Promise with inventory item data
   */
  getInventoryItemById: (id: string) =>
    request<any>('get', `/inventory/items/${id}`),

  /**
   * Create inventory item
   * @param itemData - Inventory item data
   * @returns Promise with create inventory item result
   */
  createInventoryItem: (itemData: any) =>
    request<any>('post', '/inventory/items', itemData),

  /**
   * Update inventory item
   * @param id - Inventory item ID
   * @param itemData - Inventory item data
   * @returns Promise with update inventory item result
   */
  updateInventoryItem: (id: string, itemData: any) =>
    request<any>('put', `/inventory/items/${id}`, itemData),

  /**
   * Delete inventory item
   * @param id - Inventory item ID
   * @returns Promise with delete inventory item result
   */
  deleteInventoryItem: (id: string) =>
    request<any>('delete', `/inventory/items/${id}`),

  /**
   * Get all inventory transactions
   * @param params - Query parameters
   * @returns Promise with inventory transactions data
   */
  getInventoryTransactions: (params?: any) =>
    request<any>('get', '/inventory/transactions', undefined, { params }),

  /**
   * Create inventory transaction
   * @param transactionData - Inventory transaction data
   * @returns Promise with create inventory transaction result
   */
  createInventoryTransaction: (transactionData: any) =>
    request<any>('post', '/inventory/transactions', transactionData),

  /**
   * Get inventory statistics
   * @returns Promise with inventory statistics
   */
  getInventoryStats: () =>
    request<any>('get', '/inventory/stats')
};

/**
 * Business API
 */
export const businessAPI = {
  /**
   * Get all business strategies
   * @param params - Query parameters
   * @returns Promise with business strategies data
   */
  getBusinessStrategies: (params?: any) =>
    request<any>('get', '/business/strategies', undefined, { params }),

  /**
   * Get business strategy by ID
   * @param id - Business strategy ID
   * @returns Promise with business strategy data
   */
  getBusinessStrategyById: (id: string) =>
    request<any>('get', `/business/strategies/${id}`),

  /**
   * Create business strategy
   * @param strategyData - Business strategy data
   * @returns Promise with create business strategy result
   */
  createBusinessStrategy: (strategyData: any) =>
    request<any>('post', '/business/strategies', strategyData),

  /**
   * Update business strategy
   * @param id - Business strategy ID
   * @param strategyData - Business strategy data
   * @returns Promise with update business strategy result
   */
  updateBusinessStrategy: (id: string, strategyData: any) =>
    request<any>('put', `/business/strategies/${id}`, strategyData),

  /**
   * Delete business strategy
   * @param id - Business strategy ID
   * @returns Promise with delete business strategy result
   */
  deleteBusinessStrategy: (id: string) =>
    request<any>('delete', `/business/strategies/${id}`),

  /**
   * Get business KPIs
   * @param params - Query parameters
   * @returns Promise with business KPIs data
   */
  getBusinessKPIs: (params?: any) =>
    request<any>('get', '/business/kpis', undefined, { params })
};

/**
 * Reports API
 */
export const reportsAPI = {
  /**
   * Get all reports
   * @param params - Query parameters
   * @returns Promise with reports data
   */
  getReports: (params?: any) =>
    request<any>('get', '/reports', undefined, { params }),

  /**
   * Get report by ID
   * @param id - Report ID
   * @returns Promise with report data
   */
  getReportById: (id: string) =>
    request<any>('get', `/reports/${id}`),

  /**
   * Create report
   * @param reportData - Report data
   * @returns Promise with create report result
   */
  createReport: (reportData: any) =>
    request<any>('post', '/reports', reportData),

  /**
   * Delete report
   * @param id - Report ID
   * @returns Promise with delete report result
   */
  deleteReport: (id: string) =>
    request<any>('delete', `/reports/${id}`),

  /**
   * Generate animal report
   * @param params - Report parameters
   * @returns Promise with animal report data
   */
  generateAnimalReport: (params: any) =>
    request<any>('post', '/reports/generate/animal', params),

  /**
   * Generate financial report
   * @param params - Report parameters
   * @returns Promise with financial report data
   */
  generateFinancialReport: (params: any) =>
    request<any>('post', '/reports/generate/financial', params),

  /**
   * Generate breeding report
   * @param params - Report parameters
   * @returns Promise with breeding report data
   */
  generateBreedingReport: (params: any) =>
    request<any>('post', '/reports/generate/breeding', params)
};

// Export all APIs
export default {
  auth: authAPI,
  users: usersAPI,
  animals: animalsAPI,
  health: healthAPI,
  breeding: breedingAPI,
  financial: financialAPI,
  inventory: inventoryAPI,
  business: businessAPI,
  reports: reportsAPI
};
