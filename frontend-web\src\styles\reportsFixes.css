/**
 * CSS fixes for the Reports module
 * These styles ensure that report items are properly clickable
 */

/* Make report cards clickable */
.MuiCard-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Make table rows clickable */
.MuiTableRow-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make table cells clickable */
.MuiTableCell-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Make buttons in reports clickable */
.MuiCard-root button,
.MuiTableRow-root button,
.MuiTableCell-root button {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make icons in reports clickable */
.MuiCard-root .MuiSvgIcon-root,
.MuiTableRow-root .MuiSvgIcon-root,
.MuiTableCell-root .MuiSvgIcon-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Make chips in reports clickable */
.MuiCard-root .MuiChip-root,
.MuiTableRow-root .MuiChip-root,
.MuiTableCell-root .MuiChip-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Make links in reports clickable */
.MuiCard-root a,
.MuiTableRow-root a,
.MuiTableCell-root a {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make report cards in grid clickable */
.MuiGrid-root .MuiCard-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make report card content clickable */
.MuiCardContent-root {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Make report card actions clickable */
.MuiCardActions-root {
  position: relative !important;
  z-index: 10 !important;
  pointer-events: auto !important;
}

/* Make report card buttons clickable */
.MuiCardActions-root .MuiButton-root,
.MuiCardContent-root .MuiButton-root {
  position: relative !important;
  z-index: 15 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make report card icons clickable */
.MuiCardActions-root .MuiIconButton-root,
.MuiCardContent-root .MuiIconButton-root {
  position: relative !important;
  z-index: 15 !important;
  pointer-events: auto !important;
  cursor: pointer !important;
}

/* Make motion divs clickable */
.motion-div {
  position: relative !important;
  z-index: 5 !important;
  pointer-events: auto !important;
}

/* Make motion div children clickable */
.motion-div * {
  position: relative !important;
  z-index: inherit !important;
  pointer-events: auto !important;
}
