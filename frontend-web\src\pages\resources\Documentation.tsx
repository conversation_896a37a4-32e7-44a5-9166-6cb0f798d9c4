import React, { useState } from 'react';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab, InputAdornment, Link, Paper, Breadcrumbs } from '@mui/material';
import {
  Add,
  Description,
  Search,
  FilterList,
  Visibility,
  Edit,
  Delete,
  Download,
  CalendarToday,
  LocalOffer,
  ExpandMore,
  ChevronRight,
  Folder,
  InsertDriveFile,
  Book,
  MenuBook,
  LibraryBooks,
  Article
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { useResourcesDataContext } from '../../contexts/DataContext';

// Custom TreeView implementation
interface TreeViewProps {
  children: React.ReactNode;
  defaultExpanded?: string[];
  defaultCollapseIcon?: React.ReactNode;
  defaultExpandIcon?: React.ReactNode;
  sx?: any;
}

const TreeView: React.FC<TreeViewProps> = ({ children, defaultExpanded = [], defaultCollapseIcon, defaultExpandIcon, sx }) => {
  return (
    <div className="custom-tree-view" style={sx}>
      {children}
    </div>
  );
};

interface TreeItemProps {
  nodeId: string;
  label: React.ReactNode;
  children?: React.ReactNode;
  onClick?: (e: React.MouseEvent) => void;
}

const TreeItem: React.FC<TreeItemProps> = ({ nodeId, label, children, onClick }) => {
  const [expanded, setExpanded] = useState(false);

  const handleClick = (e: React.MouseEvent) => {
    setExpanded(!expanded);
    if (onClick) onClick(e);
  };

  return (
    <div className="custom-tree-item">
      <div
        className="custom-tree-item-label"
        onClick={handleClick}
        style={{
          padding: '8px 16px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          backgroundColor: expanded ? 'rgba(0,0,0,0.04)' : 'transparent',
          borderRadius: '4px'
        }}
      >
        {expanded ? <ExpandMore fontSize="small" style={{ marginRight: 8 }} /> : <ChevronRight fontSize="small" style={{ marginRight: 8 }} />}
        {label}
      </div>
      {expanded && children && (
        <div style={{ paddingLeft: 24 }}>
          {children}
        </div>
      )}
    </div>
  );
};

// Define DocumentationResource type
interface DocumentationResource {
  id: string;
  title: string;
  category: string;
  description: string;
  version?: string;
  fileUrl?: string;
  fileSize?: string;
  fileType?: string;
  tags: string[];
  lastUpdated: string;
  downloadCount: number;
  viewCount: number;
  thumbnailUrl?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Documentation: React.FC = () => {
  const theme = useTheme();
  const { documentationResources, loading, error } = useResourcesDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedResource, setSelectedResource] = useState<DocumentationResource | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table' | 'tree'>('grid');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    category: '',
    description: '',
    version: '',
    fileUrl: '',
    fileSize: '',
    fileType: '',
    tags: ['']
  });

  // Get unique categories from documentation resources
  const categories = documentationResources ? Array.from(new Set(documentationResources.map((resource: DocumentationResource) => resource.category))) : [];

  // Filter resources based on category and search term
  const filteredResources = documentationResources?.filter((resource: DocumentationResource) => {
    const matchesCategory = filterCategory === 'all' || resource.category === filterCategory;
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesCategory && matchesSearch;
  }) || [];

  // Calculate statistics
  const docStats = {
    total: documentationResources?.length || 0,
    categories: categories.length,
    mostDownloaded: documentationResources?.length
      ? documentationResources.reduce((prev: DocumentationResource, current: DocumentationResource) => (prev.downloadCount > current.downloadCount) ? prev : current).title
      : 'N/A',
    mostViewed: documentationResources?.length
      ? documentationResources.reduce((prev: DocumentationResource, current: DocumentationResource) => (prev.viewCount > current.viewCount) ? prev : current).title
      : 'N/A',
    latestUpdate: documentationResources?.length
      ? documentationResources.reduce((prev: DocumentationResource, current: DocumentationResource) =>
          new Date(prev.lastUpdated) > new Date(current.lastUpdated) ? prev : current
        ).lastUpdated
      : 'N/A'
  };

  // Handle dialog open/close
  const handleOpenDialog = (resource?: DocumentationResource) => {
    if (resource) {
      setSelectedResource(resource);
      setFormData({
        id: resource.id,
        title: resource.title,
        category: resource.category,
        description: resource.description,
        version: resource.version || '',
        fileUrl: resource.fileUrl || '',
        fileSize: resource.fileSize || '',
        fileType: resource.fileType || '',
        tags: resource.tags
      });
    } else {
      setSelectedResource(null);
      setFormData({
        id: `DOC-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        title: '',
        category: '',
        description: '',
        version: '',
        fileUrl: '',
        fileSize: '',
        fileType: '',
        tags: ['']
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedResource(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (e: any) => {
    setFormData(prev => ({ ...prev, category: e.target.value }));
  };

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags];
    newTags[index] = value;
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  const handleAddTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const handleRemoveTag = (index: number) => {
    const newTags = [...formData.tags];
    newTags.splice(index, 1);
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  // Handle save resource
  const handleSaveResource = () => {
    // In a real app, this would call an API to save the resource
    console.log('Saving documentation resource:', formData);

    // For now, just close the dialog
    handleCloseDialog();
  };

  // Handle delete resource
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the resource
    console.log('Deleting documentation resource:', id);
  };

  // Handle view resource
  const handleViewResource = (resource: DocumentationResource) => {
    // In a real app, this would open the resource or redirect to a detailed view
    console.log('Viewing documentation resource:', resource);

    // For now, just open the dialog in view mode
    handleOpenDialog(resource);
  };

  // Handle download resource
  const handleDownloadResource = (resource: DocumentationResource) => {
    // In a real app, this would download the resource file
    console.log('Downloading documentation resource:', resource);
  };

  // Get file type icon
  const getFileTypeIcon = (fileType: string) => {
    switch (fileType?.toLowerCase()) {
      case 'pdf':
        return <Description />;
      case 'doc':
      case 'docx':
        return <Article />;
      case 'xls':
      case 'xlsx':
        return <InsertDriveFile />;
      case 'ppt':
      case 'pptx':
        return <InsertDriveFile />;
      default:
        return <InsertDriveFile />;
    }
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    { id: 'category', label: 'Category', minWidth: 120 },
    { id: 'version', label: 'Version', minWidth: 80 },
    { id: 'fileType', label: 'Type', minWidth: 80 },
    { id: 'fileSize', label: 'Size', minWidth: 80 },
    { id: 'lastUpdated', label: 'Last Updated', minWidth: 120 },
    {
      id: 'tags',
      label: 'Tags',
      minWidth: 200,
      format: (value: string[]) => (
        <Box>
          {value.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              sx={{ mr: 0.5, mb: 0.5 }}
            />
          ))}
        </Box>
      )
    },
    {
      id: 'downloadCount',
      label: 'Downloads',
      minWidth: 80,
      align: 'right'
    }
  ];

  // Organize resources by category for tree view
  const resourcesByCategory: Record<string, DocumentationResource[]> = {};
  filteredResources.forEach((resource: DocumentationResource) => {
    if (!resourcesByCategory[resource.category]) {
      resourcesByCategory[resource.category] = [];
    }
    resourcesByCategory[resource.category].push(resource);
  });

  return (
    <Box>
      <ModuleHeader
        title="Documentation"
        subtitle="Access user manuals, guides, and technical documentation for the livestock management system"
        module="resources"
        submodule="documentation"
        actionLabel="Add Document"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Documents"
                  subtitle={docStats.total.toString()}
                  module="resources"
                  uniqueId="total-docs"
                  icon={<LibraryBooks />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Categories"
                  subtitle={docStats.categories.toString()}
                  module="resources"
                  uniqueId="doc-categories"
                  icon={<Folder />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Most Downloaded"
                  subtitle={docStats.mostDownloaded}
                  module="resources"
                  uniqueId="most-downloaded-doc"
                  icon={<Download />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Latest Update"
                  subtitle={docStats.latestUpdate}
                  module="resources"
                  uniqueId="latest-doc-update"
                  icon={<CalendarToday />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200, mr: 2 }}>
              <InputLabel id="category-filter-label">Category</InputLabel>
              <Select
                labelId="category-filter-label"
                id="category-filter"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                label="Category"
              >
                <MenuItem value="all">All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem component="li" key={category as React.Key} value={category as string}>{String(category)}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              placeholder="Search documents..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Tabs
            value={viewMode === 'grid' ? 0 : viewMode === 'table' ? 1 : 2}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : newValue === 1 ? 'table' : 'tree')}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Grid View" />
            <Tab label="Table View" />
            <Tab label="Tree View" />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              {filteredResources.map((resource: DocumentationResource) => (
                <Grid item xs={12} sm={6} md={4} key={resource.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '4px',
                        background: theme.palette.primary.main
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              mr: 1
                            }}
                          >
                            {getFileTypeIcon(resource.fileType || 'unknown')}
                          </Avatar>
                          <Typography variant="h6" component="h2" fontWeight="bold">
                            {resource.title}
                          </Typography>
                        </Box>
                        <Chip
                          label={resource.category}
                          size="small"
                          color="primary"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          Last Updated: {resource.lastUpdated}
                        </Box>
                        {resource.version && (
                          <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Description fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            Version: {resource.version}
                          </Box>
                        )}
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        {resource.description}
                      </Typography>

                      <Box sx={{ mb: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Tags:
                          </Typography>
                        </Box>
                        <Box>
                          {resource.tags.map((tag: string, index: number) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Downloads:</strong> {resource.downloadCount}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Size:</strong> {resource.fileSize}
                        </Typography>
                      </Box>
                    </CardContent>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                      <IconButton size="small" onClick={() => handleViewResource(resource)}>
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleDownloadResource(resource)}>
                        <Download fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleOpenDialog(resource)}>
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleDelete(resource.id)}>
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  </Card>
                </Grid>
              ))}

              {filteredResources.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No documentation found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new document
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                    >
                      Add Document
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EnhancedDataTable
              title="Documentation"
              subtitle="Access user manuals, guides, and technical documentation"
              columns={columns as any}
              data={filteredResources}
              keyField="id"
              loading={loading}
              onView={(row: any) => handleViewResource(row)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['title', 'description', 'tags']}
              emptyMessage="No documentation found"
              accentColor={theme.palette.primary.main}
              actions={[
                {
                  icon: <Download />,
                  title: 'Download',
                  onClick: (row: any) => handleDownloadResource(row)
                }
              ]}
            />
          </motion.div>
        )}

        {/* Tree View */}
        {viewMode === 'tree' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Paper
              elevation={3}
              sx={{
                p: 3,
                borderRadius: 2,
                minHeight: 400
              }}
            >
              <Typography variant="h6" gutterBottom>
                Documentation Library
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <TreeView
                defaultCollapseIcon={<ExpandMore />}
                defaultExpandIcon={<ChevronRight />}
                sx={{
                  flexGrow: 1,
                  overflowY: 'auto',
                  '& .MuiTreeItem-root': {
                    py: 0.5
                  }
                }}
              >
                {Object.entries(resourcesByCategory).map(([category, resources]) => (
                  <TreeItem
                    key={category}
                    nodeId={category}
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center', py: 0.5 }}>
                        <Folder color="primary" sx={{ mr: 1 }} />
                        <Typography variant="body1" fontWeight="medium">
                          {category} ({resources.length})
                        </Typography>
                      </Box>
                    }
                  >
                    {resources.map(resource => (
                      <TreeItem
                        key={resource.id}
                        nodeId={resource.id}
                        label={
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', py: 0.5 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              {getFileTypeIcon(resource.fileType || 'unknown')}
                              <Typography variant="body2" sx={{ ml: 1 }}>
                                {resource.title} {resource.version && `(v${resource.version})`}
                              </Typography>
                            </Box>
                            <Box>
                              <IconButton size="small" onClick={() => handleViewResource(resource)}>
                                <Visibility fontSize="small" />
                              </IconButton>
                              <IconButton size="small" onClick={() => handleDownloadResource(resource)}>
                                <Download fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>
                        }
                        onClick={(e: React.MouseEvent) => e.stopPropagation()}
                      />
                    ))}
                  </TreeItem>
                ))}
              </TreeView>

              {Object.keys(resourcesByCategory).length === 0 && (
                <Box sx={{ textAlign: 'center', py: 5 }}>
                  <Typography variant="h6" color="text.secondary">
                    No documentation found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Try changing your filter or add a new document
                  </Typography>
                  <CustomButton
                    variant="contained"
                    startIcon={<Add />}
                    sx={{ mt: 2 }}
                    onClick={() => handleOpenDialog()}
                  >
                    Add Document
                  </CustomButton>
                </Box>
              )}
            </Paper>
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Document Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedResource ? 'Edit Document' : 'Add New Document'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Document ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  labelId="category-label"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  {categories.map((category) => (
                    <MenuItem component="li" key={category as React.Key} value={category as string}>{String(category)}</MenuItem>
                  ))}
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter document title"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Version"
                name="version"
                value={formData.version}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., 1.0, 2.3.1"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="File Type"
                name="fileType"
                value={formData.fileType}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., PDF, DOCX"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="File Size"
                name="fileSize"
                value={formData.fileSize}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., 2.5 MB"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                placeholder="Enter a detailed description of the document"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="File URL"
                name="fileUrl"
                value={formData.fileUrl}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter URL to the document file"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              {formData.tags.map((tag, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={tag}
                    onChange={(e) => handleTagChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Tag ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveTag(index)}
                    disabled={formData.tags.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddTag}
                size="small"
                sx={{ mt: 1 }}
              >
                Add Tag
              </CustomButton>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSaveResource}
            variant="contained"
            color="primary"
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Documentation;
