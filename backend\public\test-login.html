<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      padding: 10px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    button:hover {
      background-color: #45a049;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
      white-space: pre-wrap;
    }
    .error {
      color: red;
    }
    .success {
      color: green;
    }
    .user-list {
      margin-top: 20px;
    }
    .user-item {
      padding: 10px;
      margin-bottom: 5px;
      border: 1px solid #ddd;
      cursor: pointer;
    }
    .user-item:hover {
      background-color: #f0f0f0;
    }
  </style>
</head>
<body>
  <h1>Login Test</h1>
  
  <div class="form-group">
    <label for="api-url">API URL:</label>
    <input type="text" id="api-url" value="http://localhost:3001/api">
  </div>
  
  <div class="form-group">
    <label for="username">Username:</label>
    <input type="text" id="username">
  </div>
  
  <div class="form-group">
    <label for="password">Password:</label>
    <input type="password" id="password">
  </div>
  
  <button id="login-btn">Login</button>
  <button id="test-connection-btn">Test Connection</button>
  
  <div class="user-list">
    <h3>Test Users</h3>
    <div class="user-item" data-username="mayrakgama" data-password="Morok@1951ke*#">May Rakgama (Admin)</div>
    <div class="user-item" data-username="peterbotha" data-password="Admin@2023">Peter Botha (Admin)</div>
    <div class="user-item" data-username="johnsmith" data-password="Manager@2023">John Smith (Manager)</div>
    <div class="user-item" data-username="davidmokoena" data-password="Manager@2024">David Mokoena (Manager)</div>
    <div class="user-item" data-username="sarahndlovu" data-password="Super@2023">Sarah Ndlovu (Super User)</div>
    <div class="user-item" data-username="thembaventer" data-password="Super@2024">Themba Venter (Super User)</div>
    <div class="user-item" data-username="lindiwemolefe" data-password="Super@2025">Lindiwe Molefe (Super User)</div>
    <div class="user-item" data-username="AMPD" data-password="123">AMPD (Admin)</div>
    <div class="user-item" data-username="MayCaiphus" data-password="123">MayCaiphus (Admin)</div>
  </div>
  
  <div class="result" id="result"></div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const apiUrlInput = document.getElementById('api-url');
      const usernameInput = document.getElementById('username');
      const passwordInput = document.getElementById('password');
      const loginBtn = document.getElementById('login-btn');
      const testConnectionBtn = document.getElementById('test-connection-btn');
      const resultDiv = document.getElementById('result');
      const userItems = document.querySelectorAll('.user-item');
      
      // Set up user item click handlers
      userItems.forEach(item => {
        item.addEventListener('click', function() {
          usernameInput.value = this.getAttribute('data-username');
          passwordInput.value = this.getAttribute('data-password');
        });
      });
      
      // Login button click handler
      loginBtn.addEventListener('click', async function() {
        const apiUrl = apiUrlInput.value.trim();
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!apiUrl || !username || !password) {
          resultDiv.innerHTML = '<span class="error">Please fill in all fields</span>';
          return;
        }
        
        resultDiv.innerHTML = 'Logging in...';
        
        try {
          const response = await fetch(`${apiUrl}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
          });
          
          const data = await response.json();
          
          if (response.ok) {
            resultDiv.innerHTML = `<span class="success">Login successful!</span>\n\nToken: ${data.token.substring(0, 20)}...\n\nUser: ${JSON.stringify(data.user, null, 2)}`;
          } else {
            resultDiv.innerHTML = `<span class="error">Login failed: ${data.error || 'Unknown error'}</span>\n\n${JSON.stringify(data, null, 2)}`;
          }
        } catch (error) {
          resultDiv.innerHTML = `<span class="error">Error: ${error.message}</span>`;
        }
      });
      
      // Test connection button click handler
      testConnectionBtn.addEventListener('click', async function() {
        const apiUrl = apiUrlInput.value.trim();
        
        if (!apiUrl) {
          resultDiv.innerHTML = '<span class="error">Please enter API URL</span>';
          return;
        }
        
        resultDiv.innerHTML = 'Testing connection...';
        
        try {
          const response = await fetch(`${apiUrl}/db-status`);
          const data = await response.json();
          
          resultDiv.innerHTML = `<span class="success">Connection successful!</span>\n\n${JSON.stringify(data, null, 2)}`;
        } catch (error) {
          resultDiv.innerHTML = `<span class="error">Connection failed: ${error.message}</span>`;
        }
      });
    });
  </script>
</body>
</html>
