/**
 * Rural SMS Service for AMPD Livestock
 * 
 * This script provides a simple SMS service for rural areas with poor connectivity
 * It uses a local file to store SMS messages that can be sent when connectivity is available
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Phone numbers to send SMS to
  phoneNumbers: [
    '0761246783',
    '0828808204',
    '0766535887'
  ],
  
  // File to store SMS messages
  smsFile: path.join(__dirname, 'pending_sms.json'),
  
  // Log file
  logFile: path.join(__dirname, 'sms_log.txt')
};

/**
 * Log a message to the console and log file
 * @param {string} message - Message to log
 */
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}`;
  
  console.log(logMessage);
  
  // Append to log file
  fs.appendFileSync(config.logFile, logMessage + '\n');
}

/**
 * Queue an SMS message to be sent when connectivity is available
 * @param {string} message - Message to send
 * @param {string[]} recipients - Phone numbers to send to
 * @param {string} priority - Priority level (low, medium, high, critical)
 */
function queueSMS(message, recipients = config.phoneNumbers, priority = 'medium') {
  // Create the SMS object
  const sms = {
    message,
    recipients,
    priority,
    timestamp: new Date().toISOString(),
    status: 'pending'
  };
  
  // Load existing SMS messages
  let pendingSMS = [];
  
  try {
    if (fs.existsSync(config.smsFile)) {
      const fileContent = fs.readFileSync(config.smsFile, 'utf8');
      pendingSMS = JSON.parse(fileContent);
    }
  } catch (error) {
    log(`Error loading pending SMS: ${error.message}`);
  }
  
  // Add the new SMS
  pendingSMS.push(sms);
  
  // Save the updated list
  try {
    fs.writeFileSync(config.smsFile, JSON.stringify(pendingSMS, null, 2));
    log(`SMS queued: ${message} (to ${recipients.join(', ')})`);
  } catch (error) {
    log(`Error saving pending SMS: ${error.message}`);
  }
}

/**
 * Send a birth alert SMS
 * @param {string} animalId - Animal ID
 * @param {string} animalName - Animal name
 * @param {number} daysRemaining - Days remaining until birth
 * @param {string[]} recipients - Phone numbers to send to
 */
function sendBirthAlert(animalId, animalName, daysRemaining, recipients = config.phoneNumbers) {
  // Determine priority based on days remaining
  let priority;
  if (daysRemaining <= 1) {
    priority = 'critical';
  } else if (daysRemaining <= 3) {
    priority = 'high';
  } else if (daysRemaining <= 7) {
    priority = 'medium';
  } else {
    priority = 'low';
  }
  
  // Create the message
  let message;
  if (priority === 'critical') {
    message = `URGENT: BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days. IMMEDIATE ATTENTION REQUIRED.`;
  } else if (priority === 'high') {
    message = `IMPORTANT: BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days. Please prepare accordingly.`;
  } else {
    message = `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days.`;
  }
  
  // Queue the SMS
  queueSMS(message, recipients, priority);
}

/**
 * Process command line arguments
 */
function processArgs() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    console.log('Usage:');
    console.log('  node rural_sms.js send "Message to send"');
    console.log('  node rural_sms.js birth CTL001 Bella 1');
    console.log('  node rural_sms.js list');
    return;
  }
  
  const command = args[0];
  
  switch (command) {
    case 'send':
      if (args.length < 2) {
        console.log('Error: No message specified');
        return;
      }
      
      const message = args[1];
      const recipients = args.length > 2 ? [args[2]] : config.phoneNumbers;
      const priority = args.length > 3 ? args[3] : 'medium';
      
      queueSMS(message, recipients, priority);
      break;
    
    case 'birth':
      if (args.length < 4) {
        console.log('Error: Missing parameters');
        console.log('Usage: node rural_sms.js birth CTL001 Bella 1');
        return;
      }
      
      const animalId = args[1];
      const animalName = args[2];
      const daysRemaining = parseInt(args[3], 10);
      const birthRecipients = args.length > 4 ? [args[4]] : config.phoneNumbers;
      
      sendBirthAlert(animalId, animalName, daysRemaining, birthRecipients);
      break;
    
    case 'list':
      try {
        if (fs.existsSync(config.smsFile)) {
          const fileContent = fs.readFileSync(config.smsFile, 'utf8');
          const pendingSMS = JSON.parse(fileContent);
          
          console.log('=== Pending SMS Messages ===');
          console.log(`Total: ${pendingSMS.length}`);
          console.log('');
          
          pendingSMS.forEach((sms, index) => {
            console.log(`[${index + 1}] ${sms.timestamp}`);
            console.log(`Priority: ${sms.priority}`);
            console.log(`Recipients: ${sms.recipients.join(', ')}`);
            console.log(`Message: ${sms.message}`);
            console.log(`Status: ${sms.status}`);
            console.log('');
          });
        } else {
          console.log('No pending SMS messages');
        }
      } catch (error) {
        console.log(`Error listing pending SMS: ${error.message}`);
      }
      break;
    
    default:
      console.log(`Unknown command: ${command}`);
      console.log('Use --help for usage information');
  }
}

// Process command line arguments
processArgs();
