/**
 * Default values for theme.custom properties
 * Use these as fallbacks when theme.custom might be undefined
 */

export const defaultElevation = {
  0: 'none',
  1: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  2: '0px 4px 8px rgba(0, 0, 0, 0.08)',
  3: '0px 8px 16px rgba(0, 0, 0, 0.1)',
  4: '0px 12px 24px rgba(0, 0, 0, 0.12)',
  5: '0px 16px 32px rgba(0, 0, 0, 0.14)'
};

export const defaultAnimation = {
  short: '0.2s ease-in-out',
  medium: '0.3s ease-in-out',
  long: '0.5s ease-in-out',
  bounce: '0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)'
};

/**
 * Get a safe elevation value, with fallback if theme.custom.elevation is undefined
 * @param theme The Material-UI theme
 * @param level The elevation level (0-5)
 * @returns The elevation shadow string
 */
export const getSafeElevation = (theme: any, level: number): string => {
  return theme?.custom?.elevation?.[level] || defaultElevation[level as keyof typeof defaultElevation] || defaultElevation[2];
};

/**
 * Get a safe animation value, with fallback if theme.custom.animation is undefined
 * @param theme The Material-UI theme
 * @param type The animation type ('short', 'medium', 'long', 'bounce')
 * @returns The animation transition string
 */
export const getSafeAnimation = (theme: any, type: 'short' | 'medium' | 'long' | 'bounce'): string => {
  return theme?.custom?.animation?.[type] || defaultAnimation[type];
};

/**
 * Get a safe color value, with fallback if the color is undefined
 * @param color The primary color
 * @param fallback The fallback color
 * @returns A safe color string
 */
export const getSafeColor = (color: string | undefined, fallback: string): string => {
  return color || fallback;
};

export default {
  elevation: defaultElevation,
  animation: defaultAnimation,
  getSafeElevation,
  getSafeAnimation,
  getSafeColor
};
