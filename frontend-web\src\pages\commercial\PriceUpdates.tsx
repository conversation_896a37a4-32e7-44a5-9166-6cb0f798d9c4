import React, { useState } from 'react';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, Card, CardContent, Grid, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, useTheme, alpha } from '@mui/material';
import {
  Search,
  TrendingUp,
  TrendingDown,
  ArrowUpward,
  ArrowDownward,
  FilterList,
  Add
} from '@mui/icons-material';
import { useCommercialData } from '../../hooks/useCommercialData';
import { formatCurrency, formatDate } from '../../utils/commercialUtils';
import ModuleHeader from '../../components/common/ModuleHeader';
import AnimatedBackgroundCard from '../../components/common/AnimatedBackgroundCard';
import { motion } from 'framer-motion';

const PriceUpdates: React.FC = () => {
  const theme = useTheme();
  const { priceUpdates, loading, error } = useCommercialData();

  // State for filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortField, setSortField] = useState<string>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle pagination change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle category filter change
  const handleCategoryFilterChange = (event: SelectChangeEvent) => {
    setCategoryFilter(event.target.value);
  };

  // Handle sort change
  const handleSortChange = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Filter and sort price updates
  const filteredPriceUpdates = priceUpdates
    .filter(update => {
      // Filter by category
      if (categoryFilter !== 'all' && update.category !== categoryFilter) {
        return false;
      }

      // Filter by search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        return (
          update.productName.toLowerCase().includes(term) ||
          update.category.toLowerCase().includes(term) ||
          update.supplier.toLowerCase().includes(term)
        );
      }

      return true;
    })
    .sort((a, b) => {
      // Sort by selected field
      if (sortField === 'date') {
        return sortDirection === 'asc'
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime();
      } else if (sortField === 'productName') {
        return sortDirection === 'asc'
          ? a.productName.localeCompare(b.productName)
          : b.productName.localeCompare(a.productName);
      } else if (sortField === 'newPrice') {
        return sortDirection === 'asc'
          ? a.newPrice - b.newPrice
          : b.newPrice - a.newPrice;
      } else if (sortField === 'percentChange') {
        return sortDirection === 'asc'
          ? a.percentChange - b.percentChange
          : b.percentChange - a.percentChange;
      }
      return 0;
    });

  // Calculate price update statistics
  const priceStats = {
    totalUpdates: priceUpdates.length,
    increases: priceUpdates.filter(update => update.percentChange > 0).length,
    decreases: priceUpdates.filter(update => update.percentChange < 0).length,
    unchanged: priceUpdates.filter(update => update.percentChange === 0).length,
    averageChange: priceUpdates.length > 0
      ? priceUpdates.reduce((sum, update) => sum + update.percentChange, 0) / priceUpdates.length
      : 0,
    largestIncrease: priceUpdates.length > 0
      ? Math.max(...priceUpdates.map(update => update.percentChange))
      : 0,
    largestDecrease: priceUpdates.length > 0
      ? Math.min(...priceUpdates.map(update => update.percentChange))
      : 0
  };

  // Get trend chip color
  const getTrendChipProps = (percentChange: number) => {
    if (percentChange > 0) {
      return { color: 'error', icon: <TrendingUp fontSize="small" /> };
    } else if (percentChange < 0) {
      return { color: 'success', icon: <TrendingDown fontSize="small" /> };
    } else {
      return { color: 'default', icon: null };
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Price Updates"
        subtitle="Track livestock and product price changes"
        module="commercial"
        submodule="price-updates"
      />

      {/* Statistics Cards */}
      <Box sx={{ px: 3, pb: 5 }}>
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Price Increases"
              subtitle="Products with price increases"
              module="commercial"
              uniqueId="stats-increases"
              icon={<TrendingUp />}
              accentColor={theme.palette.error.main}
              delay={0.1}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {priceStats.increases}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Price Decreases"
              subtitle="Products with price decreases"
              module="commercial"
              uniqueId="stats-decreases"
              icon={<TrendingDown />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.2}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {priceStats.decreases}
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Average Change"
              subtitle="Average price change percentage"
              module="commercial"
              uniqueId="stats-average"
              icon={<FilterList />}
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              delay={0.3}
              height={140}
            >
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {priceStats.averageChange.toFixed(2)}%
              </Typography>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Search and Filter */}
        <Box sx={{ mb: 3, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
          <TextField
            placeholder="Search price updates..."
            variant="outlined"
            fullWidth
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            }}
            sx={{ flex: 1 }}
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="category-filter-label">Category</InputLabel>
            <Select
              labelId="category-filter-label"
              value={categoryFilter}
              label="Category"
              onChange={handleCategoryFilterChange}
            >
              <MenuItem value="all">All Categories</MenuItem>
              <MenuItem value="livestock">Livestock</MenuItem>
              <MenuItem value="feed">Feed</MenuItem>
              <MenuItem value="equipment">Equipment</MenuItem>
              <MenuItem value="medicine">Medicine</MenuItem>
              <MenuItem value="other">Other</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Price Updates Table */}
        <Card sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
        }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight="bold">
                Price Updates
              </Typography>
            </Box>

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <Typography>Loading price updates...</Typography>
              </Box>
            ) : error ? (
              <Box sx={{ py: 4, textAlign: 'center' }}>
                <Typography color="error">Error loading price updates. Please try again.</Typography>
              </Box>
            ) : (
              <>
                <TableContainer component={Paper} sx={{ boxShadow: 'none', mb: 2 }}>
                  <Table
                    sx={{
                      '& .MuiTableRow-root:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        transition: 'background-color 0.3s ease'
                      }
                    }}
                  >
                    <TableHead>
                      <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                        <TableCell
                          onClick={() => handleSortChange('date')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Date
                            {sortField === 'date' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell
                          onClick={() => handleSortChange('productName')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Product
                            {sortField === 'productName' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell>Category</TableCell>
                        <TableCell>Supplier</TableCell>
                        <TableCell>Old Price</TableCell>
                        <TableCell
                          onClick={() => handleSortChange('newPrice')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            New Price
                            {sortField === 'newPrice' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                        <TableCell
                          onClick={() => handleSortChange('percentChange')}
                          sx={{ cursor: 'pointer' }}
                        >
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            Change
                            {sortField === 'percentChange' && (
                              sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
                            )}
                          </Box>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {filteredPriceUpdates
                        .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                        .map((update, index) => {
                          const trendProps = getTrendChipProps(update.percentChange);

                          return (
                            <TableRow
                              key={update.id}
                              hover
                              component={motion.tr}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ duration: 0.3, delay: index * 0.05 }}
                            >
                              <TableCell>{formatDate(update.date)}</TableCell>
                              <TableCell>
                                <Typography variant="body2" fontWeight="medium">
                                  {update.productName}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={update.category}
                                  size="small"
                                  sx={{ textTransform: 'capitalize' }}
                                />
                              </TableCell>
                              <TableCell>{update.supplier}</TableCell>
                              <TableCell>{formatCurrency(update.oldPrice)}</TableCell>
                              <TableCell>{formatCurrency(update.newPrice)}</TableCell>
                              <TableCell>
                                <Chip
                                  label={`${update.percentChange > 0 ? '+' : ''}${update.percentChange.toFixed(2)}%`}
                                  size="small"
                                  color={trendProps.color as any}
                                  icon={trendProps.icon}
                                />
                              </TableCell>
                            </TableRow>
                          );
                        })}
                    </TableBody>
                  </Table>
                </TableContainer>

                <TablePagination
                  rowsPerPageOptions={[5, 10, 25]}
                  component="div"
                  count={filteredPriceUpdates.length}
                  rowsPerPage={rowsPerPage}
                  page={page}
                  onPageChange={handleChangePage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                />
              </>
            )}
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default PriceUpdates;
