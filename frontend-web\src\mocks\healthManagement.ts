export interface HealthRecord {
  id: string;
  animalId: string;
  recordType: 'checkup' | 'treatment' | 'vaccination';
  date: Date;
  diagnosis?: string;
  treatment?: string;
  medication?: string;
  dosage?: string;
  veterinarianId: string;
  cost: number;
  followUpDate?: Date;
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

export interface VaccinationSchedule {
  id: string;
  animalId: string;
  vaccineName: string;
  dueDate: Date;
  administeredDate?: Date;
  administeredBy?: string;
  batchNumber?: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'overdue';
}

export const mockHealthRecords: HealthRecord[] = [
  {
    id: 'HR001',
    animalId: 'ANM001',
    recordType: 'checkup',
    date: new Date('2024-03-01'),
    diagnosis: 'Healthy, no issues found',
    veterinarianId: 'VET001',
    cost: 50.00,
    status: 'completed'
  },
  // Add more health records...
];

export const mockVaccinationSchedule: VaccinationSchedule[] = [
  {
    id: 'VS001',
    animalId: 'ANM001',
    vaccineName: 'Annual Cattle Vaccine',
    dueDate: new Date('2024-04-15'),
    status: 'scheduled'
  },
  // Add more vaccination schedules...
];