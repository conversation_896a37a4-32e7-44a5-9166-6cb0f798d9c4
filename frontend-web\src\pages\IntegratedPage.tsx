import React from 'react';
import { Box } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import IntegratedApp from '../components/IntegratedApp';
import GlobalThemeWrapper from '../components/common/GlobalThemeWrapper';

const IntegratedPage: React.FC = () => {
  const navigate = useNavigate();

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <GlobalThemeWrapper>
      <Box sx={{ minHeight: '100vh', width: '100%' }}>
        <IntegratedApp onNavigate={handleNavigate} />
      </Box>
    </GlobalThemeWrapper>
  );
};

export default IntegratedPage;
