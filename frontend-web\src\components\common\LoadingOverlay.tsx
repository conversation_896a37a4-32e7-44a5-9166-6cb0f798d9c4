import React from 'react';
import { Box, CircularProgress, Typography, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

interface LoadingOverlayProps {
  loading: boolean;
  message?: string;
  fullScreen?: boolean;
  transparent?: boolean;
  size?: 'small' | 'medium' | 'large';
  children?: React.ReactNode;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  loading,
  message,
  fullScreen = false,
  transparent = false,
  size = 'medium',
  children
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  const getSize = () => {
    switch (size) {
      case 'small':
        return 30;
      case 'large':
        return 60;
      case 'medium':
      default:
        return 40;
    }
  };

  // Always render children, but conditionally show the overlay
  // This prevents the blinking issue when loading state changes rapidly

  const overlayContent = (
    <Box
      sx={{
        position: fullScreen ? 'fixed' : 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: transparent
          ? alpha(theme.palette.background.paper, 0.3)
          : alpha(theme.palette.background.paper, 0.8),
        zIndex: theme.zIndex.drawer + 1,
        backdropFilter: transparent ? 'blur(1px)' : 'none'
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <CircularProgress
          size={getSize()}
          thickness={4}
          sx={{
            color: theme.palette.primary.main,
            mb: 2
          }}
        />
      </motion.div>

      {message && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Typography
            variant="body1"
            sx={{
              fontWeight: 500,
              color: theme.palette.text.primary
            }}
          >
            {message}
          </Typography>
        </motion.div>
      )}
    </Box>
  );

  return (
    <Box sx={{ position: 'relative', minHeight: '100%', overflow: 'visible' }}>
      {children}
      {loading && overlayContent}
    </Box>
  );
};

export default LoadingOverlay;
