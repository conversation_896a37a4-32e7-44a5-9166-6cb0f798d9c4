import { alpha, Theme } from '@mui/material';

/**
 * Utility functions for consistent card styling across all modules
 */

// List of modules that should keep their original styling
const EXCLUDED_MODULES = ['dashboard', 'login', 'commercial'];

// Metallic blue color scheme for all other modules
const METALLIC_BLUE = {
  primary: '#4A6FA5',
  secondary: '#3A5A8C',
};

/**
 * Get the appropriate color scheme for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns The primary and secondary colors for the module
 */
export const getModuleColors = (module: string, theme: Theme) => {
  const isExcludedModule = EXCLUDED_MODULES.includes(module.toLowerCase());
  
  return {
    primary: isExcludedModule ? theme.palette.primary.main : METALLIC_BLUE.primary,
    secondary: isExcludedModule ? theme.palette.primary.dark : METALLIC_BLUE.secondary,
  };
};

/**
 * Get the gradient background style for a card
 * @param module The module name
 * @param theme The Material UI theme
 * @param opacity The opacity of the gradient (0-1)
 * @returns CSS properties for the gradient background
 */
export const getGradientBackground = (module: string, theme: Theme, opacity: number = 0.85) => {
  const colors = getModuleColors(module, theme);
  
  return {
    background: `linear-gradient(135deg, ${alpha(colors.primary, opacity)}, ${alpha(colors.secondary, opacity)})`,
    color: 'white',
  };
};

/**
 * Get the card style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the card
 */
export const getCardStyle = (module: string, theme: Theme) => {
  return {
    borderRadius: '12px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    overflow: 'hidden',
    ...getGradientBackground(module, theme),
  };
};

/**
 * Get the table header style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the table header
 */
export const getTableHeaderStyle = (module: string, theme: Theme) => {
  return {
    bgcolor: 'rgba(255,255,255,0.1)',
    '& .MuiTableCell-root': {
      color: 'white',
      fontWeight: 'bold',
    },
  };
};

/**
 * Get the table row style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the table row
 */
export const getTableRowStyle = (module: string, theme: Theme) => {
  return {
    '&:hover': { 
      bgcolor: 'rgba(255,255,255,0.1)' 
    },
    '& .MuiTableCell-root': {
      color: 'white',
    },
  };
};

/**
 * Get the chip style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @param color Optional color variant
 * @returns CSS properties for the chip
 */
export const getChipStyle = (module: string, theme: Theme, color?: string) => {
  let bgColor = 'rgba(255,255,255,0.2)';
  
  if (color === 'warning') {
    bgColor = 'rgba(255,193,7,0.2)';
  } else if (color === 'success') {
    bgColor = 'rgba(76,175,80,0.2)';
  } else if (color === 'error') {
    bgColor = 'rgba(244,67,54,0.2)';
  }
  
  return {
    bgcolor: bgColor,
    color: 'white',
  };
};

/**
 * Get the chart style for a module
 * @param module The module name
 * @returns CSS properties for charts
 */
export const getChartStyle = (module: string) => {
  return {
    '& .recharts-polar-grid-line': {
      stroke: 'rgba(255,255,255,0.3)',
    },
    '& .recharts-polar-angle-axis-tick-value': {
      fill: 'white',
    },
    '& .recharts-polar-radius-axis-tick-value': {
      fill: 'white',
    },
    '& .recharts-legend-item-text': {
      color: 'white',
    },
  };
};

export default {
  getModuleColors,
  getGradientBackground,
  getCardStyle,
  getTableHeaderStyle,
  getTableRowStyle,
  getChipStyle,
  getChartStyle,
};
