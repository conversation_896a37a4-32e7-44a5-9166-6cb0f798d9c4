/* CustomButton.css */

.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border: none;
  outline: none;
}

.custom-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.custom-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-button-text {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Button variants */
.custom-button-contained {
  color: #fff;
  background-color: var(--primary-color, #1976d2);
}

.custom-button-contained:hover:not(:disabled) {
  background-color: var(--primary-dark, #115293);
}

.custom-button-outlined {
  color: var(--primary-color, #1976d2);
  background-color: transparent;
  border: 1px solid var(--primary-color, #1976d2);
}

.custom-button-outlined:hover:not(:disabled) {
  background-color: rgba(25, 118, 210, 0.04);
}

.custom-button-text-only {
  color: var(--primary-color, #1976d2);
  background-color: transparent;
}

.custom-button-text-only:hover:not(:disabled) {
  background-color: rgba(25, 118, 210, 0.04);
}

/* Button sizes */
.custom-button-small {
  font-size: 0.8125rem;
  padding: 4px 10px;
}

.custom-button-medium {
  font-size: 0.875rem;
  padding: 6px 16px;
}

.custom-button-large {
  font-size: 0.9375rem;
  padding: 8px 22px;
}

/* Button colors */
.custom-button-primary {
  --primary-color: #1976d2;
  --primary-dark: #115293;
}

.custom-button-secondary {
  --primary-color: #9c27b0;
  --primary-dark: #7b1fa2;
}

.custom-button-success {
  --primary-color: #2e7d32;
  --primary-dark: #1b5e20;
}

.custom-button-error {
  --primary-color: #d32f2f;
  --primary-dark: #c62828;
}

.custom-button-warning {
  --primary-color: #ed6c02;
  --primary-dark: #e65100;
}

.custom-button-info {
  --primary-color: #0288d1;
  --primary-dark: #01579b;
}

/* Full width button */
.custom-button-fullWidth {
  width: 100%;
}

/* Button icons */
.custom-button-start-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.custom-button-end-icon {
  margin-left: 8px;
  display: flex;
  align-items: center;
}

/* Loading spinner */
.custom-button-spinner {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-top-color: #ffffff;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

.custom-button-outlined .custom-button-spinner,
.custom-button-text-only .custom-button-spinner {
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-top-color: var(--primary-color, #1976d2);
}

@keyframes spin {
  0% {
    transform: rotate(0deg) translateZ(0);
    will-change: transform;
  }
  100% {
    transform: rotate(360deg) translateZ(0);
    will-change: transform;
  }
}
