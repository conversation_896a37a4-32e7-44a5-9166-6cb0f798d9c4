/**
 * Local image paths for the AgriIntel Livestock Management System
 * This file provides access to locally stored images in the public folder
 */

// Base path for images
const BASE_PATH = '/images';

// Login and Dashboard images
export const loginImages = {
  background: `${BASE_PATH}/login/login-bg.jpeg`, // log in page.jpeg
  secondary: [
    `${BASE_PATH}/modules/animals/cattle-1.jpeg`, // Cows 1.jpeg
    `${BASE_PATH}/modules/animals/cattle-2.avif`, // cows 2.avif
    `${BASE_PATH}/modules/animals/cattle-3.jpeg`, // cows 3.jpeg
    `${BASE_PATH}/modules/animals/cattle-4.jpeg`  // cows 4.jpeg
  ]
};

export const dashboardImages = {
  main: `${BASE_PATH}/modules/animals/cattle-1.jpeg`, // Use cattle image as fallback
};

// Define module background image interface
interface ModuleBackground {
  main: string;
  secondary?: string[];
}

// Module background images
export const moduleBackgrounds: Record<string, ModuleBackground> = {
  // Dashboard
  dashboard: {
    main: `${BASE_PATH}/modules/animals/cattle-1.jpeg`,
  },

  // Animals module
  animals: {
    main: `${BASE_PATH}/modules/animals/cattle-1.jpeg`, // Cows 1.jpeg
    secondary: [
      `${BASE_PATH}/modules/animals/cattle-2.avif`, // cows 2.avif
      `${BASE_PATH}/modules/animals/cattle-3.jpeg`, // cows 3.jpeg
      `${BASE_PATH}/modules/animals/cattle-4.jpeg`, // cows 4.jpeg
      `${BASE_PATH}/modules/animals/cattle-5.avif`, // cows 5.avif
    ]
  },

  // Commercial module
  commercial: {
    main: `${BASE_PATH}/modules/commercial/commercial-main.webp`,
    secondary: [
      `${BASE_PATH}/modules/commercial/commercial-1.jpeg`,
      `${BASE_PATH}/modules/commercial/commercial-2.jpeg`,
      `${BASE_PATH}/modules/commercial/commercial-3.jpeg`,
    ]
  },

  // Breeding module
  breeding: {
    main: `${BASE_PATH}/modules/breeding/breeding-main.png`,
  },

  // Health module
  health: {
    main: `${BASE_PATH}/modules/health/health-main.png`, // health Managemnt.png
    secondary: [
      `${BASE_PATH}/modules/health/veterinary-1.jpg`, // veteian health 1.jpg
      `${BASE_PATH}/modules/health/veterinary-2.jpg`, // veteian health 2.jpg
      `${BASE_PATH}/modules/health/veterinary-3.jpg`, // veteian health 3.jpg
      `${BASE_PATH}/modules/health/veterinary-main.jpg`, // Veteran health.jpg
    ]
  },

  // Feed module
  feed: {
    main: `${BASE_PATH}/modules/feeding/feed-main.jpeg`, // Feed managemnt.jpeg
    secondary: [
      `${BASE_PATH}/modules/feeding/feed-2.jpeg`, // feed management 2.jpeg
      `${BASE_PATH}/modules/feeding/feed-sheep.jpeg`, // feed managemnt sheep.jpeg
    ]
  },

  // Financial module
  financial: {
    main: `${BASE_PATH}/modules/commercial/commercial-1.jpeg`,
  },

  // Inventory module
  inventory: {
    main: `${BASE_PATH}/modules/commercial/commercial-2.jpeg`,
  },

  // Compliance module
  compliance: {
    main: `${BASE_PATH}/modules/commercial/commercial-3.jpeg`,
  },

  // Resources module
  resources: {
    main: `${BASE_PATH}/modules/health/veterinary-1.jpg`,
  },

  // RFID module
  rfid: {
    main: `${BASE_PATH}/modules/rfid/rfid-1.webp`, // RFID 1.webp
    secondary: [
      `${BASE_PATH}/modules/rfid/rfid-2.jpg`, // RFID 2.jpg
      `${BASE_PATH}/modules/rfid/rfid-3.jpg`, // FRID 3.jpg
      `${BASE_PATH}/modules/rfid/rfid-4.jpg`, // FRID 4.jpg
    ]
  },

  // Reports module
  reports: {
    main: `${BASE_PATH}/modules/commercial/commercial-2.jpeg`,
  }
};

/**
 * Get a random image from a module's secondary images
 * @param module The module name
 * @returns A random image path from the module's secondary images
 */
export const getRandomModuleImage = (module: keyof typeof moduleBackgrounds): string => {
  const moduleData = moduleBackgrounds[module];

  if (!moduleData.secondary || moduleData.secondary.length === 0) {
    return moduleData.main;
  }

  const randomIndex = Math.floor(Math.random() * moduleData.secondary.length);
  return moduleData.secondary[randomIndex];
};

/**
 * Create a blended background style with an image and gradient overlay
 * @param backgroundImage The background image URL
 * @param primaryColor The primary color for the gradient
 * @param secondaryColor The secondary color for the gradient
 * @param opacity The opacity of the gradient overlay (0-1)
 * @returns A CSS style object for the blended background
 */
export const createBlendedBackground = (
  backgroundImage: string,
  primaryColor: string,
  secondaryColor: string,
  opacity: number = 0.8
): React.CSSProperties => {
  return {
    backgroundImage: `linear-gradient(135deg, ${primaryColor}${Math.round(opacity * 99)}, ${secondaryColor}${Math.round(opacity * 85)}), url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    transition: 'background-image 0.5s ease-in-out',
  };
};

/**
 * Get all images for a specific module
 * @param module The module name
 * @returns An array of all image paths for the module
 */
export const getAllModuleImages = (module: keyof typeof moduleBackgrounds): string[] => {
  const moduleData = moduleBackgrounds[module];

  if (!moduleData.secondary) {
    return [moduleData.main];
  }

  return [moduleData.main, ...moduleData.secondary];
};
