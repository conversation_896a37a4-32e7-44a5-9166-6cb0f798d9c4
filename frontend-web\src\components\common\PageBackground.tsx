import React from 'react';
import { Box, useTheme } from '@mui/material';

interface PageBackgroundProps {
  imagePath?: string;
  opacity?: number;
  children: React.ReactNode;
  moduleName?: string;
}

/**
 * PageBackground Component
 * 
 * This component provides a consistent background image for all pages.
 * It automatically selects an appropriate background image based on the module name,
 * or uses the provided imagePath.
 * 
 * @param {string} imagePath - Optional custom image path
 * @param {number} opacity - Opacity of the background image (0-1)
 * @param {React.ReactNode} children - Content to render on top of the background
 * @param {string} moduleName - Name of the module (animals, health, etc.)
 */
const PageBackground: React.FC<PageBackgroundProps> = ({
  imagePath,
  opacity = 0.08,
  children,
  moduleName
}) => {
  const theme = useTheme();

  // Select background image based on module name
  const getBackgroundImage = (): string => {
    if (imagePath) return imagePath;
    
    switch (moduleName?.toLowerCase()) {
      case 'animals':
        return '/images/modules/animals/cattle-1.jpeg';
      case 'health':
        return '/images/modules/health/health-main.png';
      case 'breeding':
        return '/images/modules/breeding/breeding-main.png';
      case 'feeding':
        return '/images/modules/feeding/feed-main.jpeg';
      case 'financial':
        return '/images/modules/financial/financial-main.jpg';
      case 'commercial':
        return '/images/modules/commercial/commercial-main.webp';
      case 'rfid':
        return '/images/modules/rfid/rfid-1.webp';
      case 'login':
        return '/images/login/login-bg.jpeg';
      default:
        return '/images/dashboard/main-dashboard.jpg';
    }
  };

  return (
    <Box sx={{ position: 'relative', minHeight: '100%' }}>
      {/* Background Image */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${getBackgroundImage()})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: opacity,
          zIndex: 0,
          pointerEvents: 'none',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `linear-gradient(135deg, ${theme.palette.primary.dark}08, ${theme.palette.primary.main}05)`,
            zIndex: 1
          }
        }}
      />
      
      {/* Content */}
      <Box sx={{ position: 'relative', zIndex: 1 }}>
        {children}
      </Box>
    </Box>
  );
};

export default PageBackground;
