import api from './apiService';
import { useMockData } from '../utils/config';
import {
  mockBusinessKPIs,
  mockBusinessPredictions,
  mockBusinessInsights,
  mockBusinessRisks,
  mockBusinessOpportunities,
  mockStrategicGoals,
  mockStrategicInitiatives,
  mockSWOTAnalysis,
  mockStrategicScenarios,
  mockAnimalDistributionData
} from '../mocks/businessAnalysisData';

// Business Analysis Service
export const businessAnalysisService = {
  // Get dashboard data
  getDashboardData: async () => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        kpis: mockBusinessKPIs,
        predictions: mockBusinessPredictions,
        insights: mockBusinessInsights.slice(0, 5),
        risks: mockBusinessRisks.slice(0, 3),
        opportunities: mockBusinessOpportunities.slice(0, 3),
        animalDistributionData: mockAnimalDistributionData
      };
    }
    return api.get('/business-analysis/dashboard').then(response => response.data);
  },

  // Get business strategy data
  getBusinessStrategy: async () => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        vision: 'To be the leading sustainable livestock farm in South Africa, recognized for excellence in animal welfare, environmental stewardship, and community impact.',
        mission: 'We are committed to producing high-quality livestock through ethical farming practices, innovative technology, and responsible resource management while supporting local communities and preserving the environment for future generations.',
        goals: mockStrategicGoals,
        initiatives: mockStrategicInitiatives,
        swot: mockSWOTAnalysis,
        opportunities: mockBusinessOpportunities,
        scenarios: mockStrategicScenarios
      };
    }
    return api.get('/business-analysis/strategy').then(response => response.data);
  },

  // Get KPIs
  getKPIs: async (category?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredKPIs = [...mockBusinessKPIs];
      if (category) filteredKPIs = filteredKPIs.filter(kpi => kpi.category === category);
      return filteredKPIs;
    }
    return api.get('/business-analysis/kpis', { params: { category } }).then(response => response.data);
  },

  // Get predictions
  getPredictions: async (category?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredPredictions = [...mockBusinessPredictions];
      if (category) filteredPredictions = filteredPredictions.filter(pred => pred.category === category);
      return filteredPredictions;
    }
    return api.get('/business-analysis/predictions', { params: { category } }).then(response => response.data);
  },

  // Get insights
  getInsights: async (category?: string, impact?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredInsights = [...mockBusinessInsights];
      if (category) filteredInsights = filteredInsights.filter(insight => insight.category === category);
      if (impact) filteredInsights = filteredInsights.filter(insight => insight.impact === impact);
      return filteredInsights;
    }
    return api.get('/business-analysis/insights', { params: { category, impact } }).then(response => response.data);
  },

  // Get risks
  getRisks: async (category?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredRisks = [...mockBusinessRisks];
      if (category) filteredRisks = filteredRisks.filter(risk => risk.category === category);
      return filteredRisks;
    }
    return api.get('/business-analysis/risks', { params: { category } }).then(response => response.data);
  },

  // Get opportunities
  getOpportunities: async (category?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredOpportunities = [...mockBusinessOpportunities];
      if (category) filteredOpportunities = filteredOpportunities.filter(opp => opp.category === category);
      return filteredOpportunities;
    }
    return api.get('/business-analysis/opportunities', { params: { category } }).then(response => response.data);
  },

  // Get strategic goals
  getStrategicGoals: async (category?: string, status?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredGoals = [...mockStrategicGoals];
      if (category) filteredGoals = filteredGoals.filter(goal => goal.category === category);
      if (status) filteredGoals = filteredGoals.filter(goal => goal.status === status);
      return filteredGoals;
    }
    return api.get('/business-analysis/goals', { params: { category, status } }).then(response => response.data);
  },

  // Get strategic initiatives
  getStrategicInitiatives: async (category?: string, status?: string) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      let filteredInitiatives = [...mockStrategicInitiatives];
      if (category) filteredInitiatives = filteredInitiatives.filter(init => init.category === category);
      if (status) filteredInitiatives = filteredInitiatives.filter(init => init.status === status);
      return filteredInitiatives;
    }
    return api.get('/business-analysis/initiatives', { params: { category, status } }).then(response => response.data);
  },

  // Get SWOT analysis
  getSWOTAnalysis: async () => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      return mockSWOTAnalysis;
    }
    return api.get('/business-analysis/swot').then(response => response.data);
  },

  // Get strategic scenarios
  getStrategicScenarios: async () => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      return mockStrategicScenarios;
    }
    return api.get('/business-analysis/scenarios').then(response => response.data);
  },

  // Create a new KPI
  createKPI: async (kpiData: any) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newId = `kpi-${mockBusinessKPIs.length + 1}`;
      const newKPI = { id: newId, ...kpiData };
      mockBusinessKPIs.push(newKPI);
      return newKPI;
    }
    return api.post('/business-analysis/kpis', kpiData).then(response => response.data);
  },

  // Update a KPI
  updateKPI: async (id: string, kpiData: any) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      const index = mockBusinessKPIs.findIndex(kpi => kpi.id === id);
      if (index === -1) {
        throw new Error('KPI not found');
      }
      mockBusinessKPIs[index] = { ...mockBusinessKPIs[index], ...kpiData };
      return mockBusinessKPIs[index];
    }
    return api.put(`/business-analysis/kpis/${id}`, kpiData).then(response => response.data);
  },

  // Create a new insight
  createInsight: async (insightData: any) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newId = `insight-${mockBusinessInsights.length + 1}`;
      const newInsight = { id: newId, ...insightData };
      mockBusinessInsights.push(newInsight);
      return newInsight;
    }
    return api.post('/business-analysis/insights', insightData).then(response => response.data);
  },

  // Update SWOT analysis
  updateSWOTAnalysis: async (swotData: any) => {
    if (useMockData) {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      Object.assign(mockSWOTAnalysis, swotData);
      return mockSWOTAnalysis;
    }
    return api.put('/business-analysis/swot', swotData).then(response => response.data);
  }
};
