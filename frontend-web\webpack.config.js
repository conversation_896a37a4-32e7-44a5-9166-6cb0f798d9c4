
const path = require('path');
const webpack = require('webpack');

module.exports = {
  resolve: {
    alias: {
      'mongodb': path.resolve(__dirname, 'src/mocks/mockMongoDb.ts'),
      'fs': path.resolve(__dirname, 'src/mocks/fs.js')
    },
    mainFields: ['browser', 'module', 'main'],
    fallback: {
      // Use actual polyfill packages for Node.js modules
      "crypto": require.resolve('crypto-browserify'),
      "stream": require.resolve('stream-browserify'),
      "timers": require.resolve('timers-browserify'),
      "os": require.resolve('os-browserify/browser'),
      "zlib": require.resolve('browserify-zlib'),
      "http": require.resolve('stream-http'),
      "https": require.resolve('https-browserify'),
      "querystring": require.resolve('querystring-es3'),
      "buffer": require.resolve('buffer/'),
      "process": require.resolve('process/browser'),

      // Use our mock implementation for fs
      "fs": path.resolve(__dirname, 'src/mocks/fs.js'),
      "net": false,
      "tls": false,
      "dns": false,
      "child_process": false,
      "fs/promises": false,
      "timers/promises": path.resolve(__dirname, 'src/polyfills/timers-promises-polyfill.js'),
      "path": require.resolve('path-browserify')
    }
  },
  plugins: [
    // Provide process and Buffer polyfills
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    })
  ],
  // Fix for the assets error in Node.js v18+
  module: {
    rules: [
      {
        test: /.m?js$/,
        resolve: {
          fullySpecified: false
        }
      }
    ]
  },
  // Optimization for better chunk loading
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true
        }
      }
    }
  }
};
