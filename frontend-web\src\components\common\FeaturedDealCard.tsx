import React from 'react';
import { CustomButton } from '../common';
import { Typography, Box, useTheme } from '@mui/material';
import BlendedBackgroundCard from './BlendedBackgroundCard';

interface FeaturedDealCardProps {
  id: string;
  title: string;
  subtitle: string;
  discount: string;
  backgroundImage: string;
  icon?: React.ReactNode;
  onViewOffers?: () => void;
  primaryColor?: string;
  secondaryColor?: string;
}

/**
 * Featured Deal card component for marketplace
 * Displays featured deals with a blended background image
 */
const FeaturedDealCard: React.FC<FeaturedDealCardProps> = ({
  id,
  title,
  subtitle,
  discount,
  backgroundImage,
  icon,
  onViewOffers,
  primaryColor,
  secondaryColor,
}) => {
  const theme = useTheme();

  return (
    <BlendedBackgroundCard
      backgroundImage={backgroundImage}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      height={180}
      title={
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            {title}
          </Typography>
          
          {icon && (
            <Box
              sx={{
                p: 1,
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {icon}
            </Box>
          )}
        </Box>
      }
      content={
        <Box>
          <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
            {subtitle}
          </Typography>
          
          <Box
            sx={{
              display: 'inline-block',
              px: 2,
              py: 1,
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 2,
              fontWeight: 'bold'
            }}
          >
            {discount}
          </Box>
        </Box>
      }
      action={
        <CustomButton 
          variant="outlined" 
          size="small" 
          fullWidth
          onClick={onViewOffers}
          sx={{ 
            borderColor: 'rgba(255, 255, 255, 0.5)',
            color: 'white',
            '&:hover': {
              borderColor: 'white',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
            }
          }}
        >
          View Offers
        </CustomButton>
      }
    />
  );
};

export default FeaturedDealCard;
