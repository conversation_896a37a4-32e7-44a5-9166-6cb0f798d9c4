/**
 * Breeding Management Controller
 * Handles all breeding-related CRUD operations
 */

const mongodb = require('../../config/mongodb');
const logger = require('../../utils/logger');
const { ObjectId } = require('mongodb');

class BreedingController {
  // Get all breeding records
  async getBreedingRecords(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('breeding_records');
      
      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const femaleId = req.query.femaleId;
      const status = req.query.status;
      
      // Build filter
      const filter = {};
      if (femaleId) filter.femaleId = femaleId;
      if (status) filter.status = status;
      
      // Get records with pagination
      const records = await collection
        .find(filter)
        .sort({ breedingDate: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();
      
      const total = await collection.countDocuments(filter);
      
      res.json({
        success: true,
        data: records,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching breeding records:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch breeding records',
        error: error.message
      });
    }
  }

  // Get breeding record by ID
  async getBreedingRecordById(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('breeding_records');
      
      const record = await collection.findOne({ _id: new ObjectId(req.params.id) });
      
      if (!record) {
        return res.status(404).json({
          success: false,
          message: 'Breeding record not found'
        });
      }
      
      res.json({
        success: true,
        data: record
      });
    } catch (error) {
      logger.error('Error fetching breeding record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch breeding record',
        error: error.message
      });
    }
  }

  // Create new breeding record
  async createBreedingRecord(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('breeding_records');
      
      const {
        femaleId,
        maleId,
        breedingDate,
        expectedCalvingDate,
        method,
        notes
      } = req.body;
      
      // Validate required fields
      if (!femaleId || !maleId || !breedingDate) {
        return res.status(400).json({
          success: false,
          message: 'Female ID, Male ID, and breeding date are required'
        });
      }
      
      // Verify animals exist
      const animalsCollection = db.collection('animals');
      const female = await animalsCollection.findOne({ tagNumber: femaleId });
      const male = await animalsCollection.findOne({ tagNumber: maleId });
      
      if (!female || !male) {
        return res.status(404).json({
          success: false,
          message: 'One or both animals not found'
        });
      }
      
      if (female.gender !== 'female' || male.gender !== 'male') {
        return res.status(400).json({
          success: false,
          message: 'Invalid gender combination for breeding'
        });
      }
      
      const breedingRecord = {
        femaleId,
        maleId,
        breedingDate: new Date(breedingDate),
        expectedCalvingDate: expectedCalvingDate ? new Date(expectedCalvingDate) : null,
        status: 'bred',
        method: method || 'natural',
        notes: notes || '',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await collection.insertOne(breedingRecord);
      
      res.status(201).json({
        success: true,
        message: 'Breeding record created successfully',
        data: { ...breedingRecord, _id: result.insertedId }
      });
    } catch (error) {
      logger.error('Error creating breeding record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create breeding record',
        error: error.message
      });
    }
  }

  // Update breeding record
  async updateBreedingRecord(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('breeding_records');
      
      const updateData = { ...req.body };
      delete updateData._id;
      
      // Convert date strings to Date objects
      if (updateData.breedingDate) updateData.breedingDate = new Date(updateData.breedingDate);
      if (updateData.expectedCalvingDate) updateData.expectedCalvingDate = new Date(updateData.expectedCalvingDate);
      if (updateData.actualCalvingDate) updateData.actualCalvingDate = new Date(updateData.actualCalvingDate);
      
      updateData.updatedAt = new Date();
      
      const result = await collection.updateOne(
        { _id: new ObjectId(req.params.id) },
        { $set: updateData }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Breeding record not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Breeding record updated successfully'
      });
    } catch (error) {
      logger.error('Error updating breeding record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update breeding record',
        error: error.message
      });
    }
  }

  // Delete breeding record
  async deleteBreedingRecord(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('breeding_records');
      
      const result = await collection.deleteOne({ _id: new ObjectId(req.params.id) });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({
          success: false,
          message: 'Breeding record not found'
        });
      }
      
      res.json({
        success: true,
        message: 'Breeding record deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting breeding record:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete breeding record',
        error: error.message
      });
    }
  }

  // Get breeding statistics
  async getBreedingStatistics(req, res) {
    try {
      const db = mongodb.getDB();
      const breedingCollection = db.collection('breeding_records');
      const animalsCollection = db.collection('animals');
      
      // Get basic counts
      const totalBreedings = await breedingCollection.countDocuments();
      const pregnantAnimals = await breedingCollection.countDocuments({ status: 'pregnant' });
      const totalFemales = await animalsCollection.countDocuments({ gender: 'female' });
      const totalMales = await animalsCollection.countDocuments({ gender: 'male' });
      
      // Get breeding success rate
      const successfulBreedings = await breedingCollection.countDocuments({ status: 'pregnant' });
      const successRate = totalBreedings > 0 ? Math.round((successfulBreedings / totalBreedings) * 100) : 0;
      
      // Get upcoming calvings
      const upcomingCalvings = await breedingCollection
        .find({
          expectedCalvingDate: {
            $gte: new Date(),
            $lte: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000) // Next 60 days
          }
        })
        .sort({ expectedCalvingDate: 1 })
        .limit(10)
        .toArray();
      
      // Get breeding methods distribution
      const methodsDistribution = await breedingCollection.aggregate([
        { $group: { _id: '$method', count: { $sum: 1 } } }
      ]).toArray();
      
      res.json({
        success: true,
        data: {
          totalBreedings,
          pregnantAnimals,
          totalFemales,
          totalMales,
          successRate,
          upcomingCalvings,
          methodsDistribution
        }
      });
    } catch (error) {
      logger.error('Error fetching breeding statistics:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch breeding statistics',
        error: error.message
      });
    }
  }

  // Get breeding overview
  async getBreeding(req, res) {
    try {
      res.json({
        success: true,
        message: 'Breeding module is operational',
        data: {
          module: 'breeding',
          status: 'active',
          features: [
            'Breeding Records Management',
            'Pregnancy Tracking',
            'Calving Predictions',
            'Breeding Performance Analytics',
            'Genetic Tracking'
          ]
        }
      });
    } catch (error) {
      logger.error('Error in breeding overview:', error);
      res.status(500).json({
        success: false,
        message: 'Breeding module error',
        error: error.message
      });
    }
  }
}

module.exports = new BreedingController();
