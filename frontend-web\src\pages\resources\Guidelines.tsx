import React, { useState, useRef } from 'react';
import { useAccessibility } from '../../hooks/useAccessibility';
import AccessibleWrapper from '../../components/common/AccessibleWrapper';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab, InputAdornment } from '@mui/material';
import {
  Add,
  Book,
  Search,
  FilterList,
  Download,
  Visibility,
  Edit,
  Delete,
  Description,
  Category,
  Person,
  CalendarToday,
  LocalOffer
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { useResourcesDataContext } from '../../contexts/DataContext';
// Define ResourceGuide type
interface ResourceGuide {
  id: string;
  title: string;
  category: string;
  description: string;
  author: string;
  tags: string[];
  dateAdded: string;
  viewCount: number;
  downloadCount: number;
  fileUrl?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Guidelines: React.FC = () => {
  const theme = useTheme();
  const { guides, loading, error } = useResourcesDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedGuide, setSelectedGuide] = useState<ResourceGuide | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const { handleKeyboardNavigation, getAriaAttributes } = useAccessibility();
  const dialogRef = useRef<HTMLDivElement>(null);

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    category: '',
    description: '',
    author: '',
    tags: [''],
    fileUrl: ''
  });

  // Get unique categories from guides
  const categories = guides ? Array.from(new Set(guides.map((guide: ResourceGuide) => guide.category))) : [];

  // Filter guides based on category and search term
  const filteredGuides = guides?.filter((guide: ResourceGuide) => {
    const matchesCategory = filterCategory === 'all' || guide.category === filterCategory;
    const matchesSearch =
      guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      guide.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesCategory && matchesSearch;
  }) || [];

  // Handle dialog open/close
  const handleOpenDialog = (guide?: ResourceGuide) => {
    if (guide) {
      setSelectedGuide(guide);
      setFormData({
        id: guide.id,
        title: guide.title,
        category: guide.category,
        description: guide.description,
        author: guide.author,
        tags: guide.tags,
        fileUrl: guide.fileUrl || ''
      });
    } else {
      setSelectedGuide(null);
      setFormData({
        id: `GUIDE-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        title: '',
        category: '',
        description: '',
        author: '',
        tags: [''],
        fileUrl: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedGuide(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCategoryChange = (e: any) => {
    setFormData(prev => ({ ...prev, category: e.target.value }));
  };

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags];
    newTags[index] = value;
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  const handleAddTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const handleRemoveTag = (index: number) => {
    const newTags = [...formData.tags];
    newTags.splice(index, 1);
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  // Handle save guide
  const handleSaveGuide = () => {
    // In a real app, this would call an API to save the guide
    console.log('Saving guide:', formData);

    // For now, just close the dialog
    handleCloseDialog();
  };

  // Handle delete guide
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the guide
    console.log('Deleting guide:', id);
  };

  // Handle view guide
  const handleViewGuide = (guide: ResourceGuide) => {
    // In a real app, this would open the guide file or redirect to a detailed view
    console.log('Viewing guide:', guide);

    // For now, just open the dialog in view mode
    handleOpenDialog(guide);
  };

  // Handle download guide
  const handleDownloadGuide = (guide: ResourceGuide) => {
    // In a real app, this would download the guide file
    console.log('Downloading guide:', guide);
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    { id: 'category', label: 'Category', minWidth: 120 },
    { id: 'author', label: 'Author', minWidth: 150 },
    { id: 'dateAdded', label: 'Date Added', minWidth: 120 },
    {
      id: 'tags',
      label: 'Tags',
      minWidth: 200,
      format: (value: string[]) => (
        <Box>
          {value.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              sx={{ mr: 0.5, mb: 0.5 }}
            />
          ))}
        </Box>
      )
    },
    {
      id: 'viewCount',
      label: 'Views',
      minWidth: 80,
      align: 'right'
    }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Guidelines"
        subtitle="Access and manage farming and livestock management guidelines"
        module="resources"
        submodule="guidelines"
        actionLabel="Add Guideline"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Guidelines"
                  subtitle={guides?.length.toString() || '0'}
                  module="resources"
                  uniqueId="total-guidelines"
                  icon={<Book />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Most Viewed"
                  subtitle={guides?.length ? guides.reduce((prev: ResourceGuide, current: ResourceGuide) => (prev.viewCount > current.viewCount) ? prev : current).title : 'N/A'}
                  module="resources"
                  uniqueId="most-viewed"
                  icon={<Visibility />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Most Downloaded"
                  subtitle={guides?.length ? guides.reduce((prev: ResourceGuide, current: ResourceGuide) => (prev.downloadCount > current.downloadCount) ? prev : current).title : 'N/A'}
                  module="resources"
                  uniqueId="most-downloaded"
                  icon={<Download />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Categories"
                  subtitle={categories.length.toString()}
                  module="resources"
                  uniqueId="categories"
                  icon={<Category />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200, mr: 2 }}>
              <InputLabel id="category-filter-label">Category Filter</InputLabel>
              <Select
                labelId="category-filter-label"
                id="category-filter"
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                label="Category Filter"
              >
                <MenuItem value="all" role="option" aria-selected={filterCategory === 'all'}>All Categories</MenuItem>
                {categories.map((category) => (
                  <MenuItem
                    component="li"
                    key={category as React.Key}
                    value={category as string}
                    role="option"
                    aria-selected={filterCategory === category}
                  >
                    {String(category)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              placeholder="Search guidelines..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Tabs
            value={viewMode === 'grid' ? 0 : 1}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : 'table')}
            indicatorColor="primary"
            textColor="primary"
            aria-label="View mode selection"
            role="tablist"
          >
            <Tab
              label="Grid View"
              id="guidelines-tab-0"
              aria-controls="guidelines-tabpanel-0"
              role="tab"
            />
            <Tab
              label="Table View"
              id="guidelines-tab-1"
              aria-controls="guidelines-tabpanel-1"
              role="tab"
            />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            role="tabpanel"
            id="guidelines-tabpanel-0"
            aria-labelledby="guidelines-tab-0"
          >
            <Grid container spacing={3}>
              {filteredGuides.map((guide: ResourceGuide) => (
                <Grid item xs={12} sm={6} md={4} key={guide.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '4px',
                        background: theme.palette.primary.main
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" component="h2" fontWeight="bold">
                          {guide.title}
                        </Typography>
                        <Chip
                          label={guide.category}
                          size="small"
                          color="primary"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Person fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          {guide.author}
                        </Box>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          {guide.dateAdded}
                        </Box>
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        {guide.description}
                      </Typography>

                      <Box sx={{ mb: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Tags:
                          </Typography>
                        </Box>
                        <Box>
                          {guide.tags.map((tag: string, index: number) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Views:</strong> {guide.viewCount}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Downloads:</strong> {guide.downloadCount}
                        </Typography>
                      </Box>
                    </CardContent>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => handleViewGuide(guide)}
                        aria-label={`View ${guide.title}`}
                      >
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDownloadGuide(guide)}
                        aria-label={`Download ${guide.title}`}
                      >
                        <Download fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(guide)}
                        aria-label={`Edit ${guide.title}`}
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(guide.id)}
                        aria-label={`Delete ${guide.title}`}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  </Card>
                </Grid>
              ))}

              {filteredGuides.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No guidelines found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new guideline
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                      aria-label="Add new guideline"
                    >
                      Add Guideline
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            role="tabpanel"
            id="guidelines-tabpanel-1"
            aria-labelledby="guidelines-tab-1"
          >
            <EnhancedDataTable
              title="Guidelines"
              subtitle="Access and manage farming and livestock management guidelines"
              columns={columns as any}
              data={filteredGuides}
              keyField="id"
              loading={loading}
              onView={(row: any) => handleViewGuide(row)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['title', 'description', 'author', 'tags']}
              emptyMessage="No guidelines found"
              accentColor={theme.palette.primary.main}
              actions={[
                {
                  icon: <Download />,
                  title: 'Download',
                  onClick: (row: any) => handleDownloadGuide(row)
                }
              ]}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Guide Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        ref={dialogRef}
        aria-labelledby="guidelines-dialog-title"
        aria-describedby="guidelines-dialog-description"
      >
        <DialogTitle id="guidelines-dialog-title">
          {selectedGuide ? 'Edit Guideline' : 'Add New Guideline'}
        </DialogTitle>
        <DialogContent dividers id="guidelines-dialog-description">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Guideline ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="category-label">Category</InputLabel>
                <Select
                  labelId="category-label"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleCategoryChange}
                  label="Category"
                >
                  {categories.map((category) => (
                    <MenuItem component="li" key={category as React.Key} value={category as string}>{String(category)}</MenuItem>
                  ))}
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter guideline title"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Author"
                name="author"
                value={formData.author}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter author name or organization"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                placeholder="Enter a detailed description of the guideline"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="File URL"
                name="fileUrl"
                value={formData.fileUrl}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter URL to the guideline document"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              {formData.tags.map((tag, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={tag}
                    onChange={(e) => handleTagChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Tag ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveTag(index)}
                    disabled={formData.tags.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddTag}
                size="small"
                sx={{ mt: 1 }}
                aria-label="Add new tag"
              >
                Add Tag
              </CustomButton>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton
            onClick={handleCloseDialog}
            aria-label="Cancel and close dialog"
          >
            Cancel
          </CustomButton>
          <CustomButton
            onClick={handleSaveGuide}
            variant="contained"
            color="primary"
            aria-label={selectedGuide ? 'Save changes to guideline' : 'Add new guideline'}
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Guidelines;