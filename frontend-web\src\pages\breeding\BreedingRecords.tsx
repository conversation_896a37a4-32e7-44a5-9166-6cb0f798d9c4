import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { motion } from 'framer-motion';
import { Card, IconButton, TextField, MenuItem, Grid, Typography, Chip, Alert, Snackbar,  } from '@mui/material';
import {
  Add,
  CalendarToday,
  Search,
  Timeline,
  Edit,
  Delete,
 } from '../../utils/iconImports';
import { DataGrid } from '@mui/x-data-grid';
import BreedingRecordModal from '../../components/breeding/BreedingRecordModal';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';
import { BreedingRecord } from '../../mocks/breedingData';
import { useSnackbar } from '../../contexts/SnackbarContext';

const BreedingRecords: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<BreedingRecord | null>(null);
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState<BreedingRecord[]>([]);
  const { showSnackbar } = useSnackbar();

  const { breedingRecords, loading: recordsLoading, addBreedingRecord, updateBreedingRecord, deleteBreedingRecord } = useBreedingRecords();

  // Initialize records from the hook
  React.useEffect(() => {
    setRecords(breedingRecords);
  }, [breedingRecords]);

  // Handle saving a new breeding record
  const handleSaveRecord = async (record: Partial<BreedingRecord>) => {
    try {
      setLoading(true);
      const newRecord = await addBreedingRecord(record as BreedingRecord);
      setRecords(prev => [...prev, newRecord]);
      return newRecord;
    } catch (error) {
      console.error('Error saving breeding record:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Handle updating an existing breeding record
  const handleUpdateRecord = async (id: string, record: Partial<BreedingRecord>) => {
    try {
      setLoading(true);
      const updatedRecord = await updateBreedingRecord(id, record);
      setRecords(prev => prev.map(r => r.id === id ? { ...r, ...updatedRecord } : r));
      return updatedRecord;
    } catch (error) {
      console.error('Error updating breeding record:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Handle deleting a breeding record
  const handleDeleteRecord = async (id: string) => {
    try {
      setLoading(true);
      await deleteBreedingRecord(id);
      setRecords(prev => prev.filter(r => r.id !== id));
    } catch (error) {
      console.error('Error deleting breeding record:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      field: 'femaleTag',
      headerName: 'Female Tag',
      width: 150,
    },
    {
      field: 'maleTag',
      headerName: 'Male Tag',
      width: 150,
    },
    {
      field: 'breedingDate',
      headerName: 'Breeding Date',
      width: 180,
      renderCell: (params: any) => (
        <div className="flex items-center space-x-2">
          <CalendarToday fontSize="small" />
          <span>{new Date(params.value).toLocaleDateString()}</span>
        </div>
      ),
    },
    {
      field: 'expectedDueDate',
      headerName: 'Expected Due Date',
      width: 180,
      renderCell: (params: any) => (
        <div className="flex items-center space-x-2">
          <CalendarToday fontSize="small" />
          <span>{new Date(params.value).toLocaleDateString()}</span>
        </div>
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 150,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={
            params.value === 'Confirmed'
              ? 'success'
              : params.value === 'Pending'
              ? 'warning'
              : 'error'
          }
          size="small"
        />
      ),
    },
    {
      field: 'method',
      headerName: 'Breeding Method',
      width: 180,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: any) => (
        <div className="flex space-x-2">
          <IconButton
            size="small"
            onClick={() => {
              setSelectedRecord(params.row);
              setIsModalOpen(true);
            }}
          >
            <Timeline fontSize="small" />
          </IconButton>
        </div>
      ),
    },
  ];

  const breedingMetrics = [
    {
      title: 'Pending Confirmations',
      value: records.filter((r: BreedingRecord) => r.status === 'pending').length,
      color: 'bg-green-100 text-green-800',
    },
    {
      title: 'Confirmed Pregnancies',
      value: records.filter((r: BreedingRecord) => r.status === 'confirmed').length,
      color: 'bg-blue-100 text-blue-800',
    },
    {
      title: 'Failed Attempts',
      value: records.filter((r: BreedingRecord) => r.status === 'unsuccessful').length,
      color: 'bg-red-100 text-red-800',
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Breeding Records</h1>
          <p className="text-gray-600">
            Track breeding cycles and manage pregnancies
          </p>
        </div>
        <CustomButton
          variant="contained"
          startIcon={<Add />}
          onClick={() => {
            setSelectedRecord(null);
            setIsModalOpen(true);
          }}
        >
          New Breeding Record
        </CustomButton>
      </div>

      {/* Metrics Cards */}
      <Grid container spacing={3}>
        {breedingMetrics.map((metric) => (
          <Grid item xs={12} sm={6} md={4} key={metric.title}>
            <Card className="p-4">
              <Typography variant="subtitle2" className="text-gray-600">
                {metric.title}
              </Typography>
              <Typography
                variant="h4"
                className={`mt-2 font-bold ${metric.color} p-2 rounded-lg`}
              >
                {metric.value}
              </Typography>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Filters and Search */}
      <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow">
        <div className="flex space-x-4">
          <TextField
            select
            label="Status"
            size="small"
            className="w-48"
          >
            <MenuItem value="all">All Status</MenuItem>
            <MenuItem value="confirmed">Confirmed</MenuItem>
            <MenuItem value="pending">Pending</MenuItem>
            <MenuItem value="failed">Failed</MenuItem>
          </TextField>
          <TextField
            select
            label="Breeding Method"
            size="small"
            className="w-48"
          >
            <MenuItem value="all">All Methods</MenuItem>
            <MenuItem value="natural">Natural</MenuItem>
            <MenuItem value="artificial">Artificial Insemination</MenuItem>
          </TextField>
        </div>
        <TextField
          placeholder="Search records..."
          size="small"
          className="w-64"
          InputProps={{
            startAdornment: <Search className="text-gray-400 mr-2" />,
          }}
        />
      </div>

      {/* Data Grid */}
      <Card>
        <DataGrid
          rows={records}
          columns={columns}
          getRowId={(row) => row._id || row.id}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 10, page: 0 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          checkboxSelection
          disableRowSelectionOnClick
          autoHeight
          loading={loading || recordsLoading}
          className="p-4"
        />
      </Card>

      {/* Breeding Record Modal */}
      <BreedingRecordModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        record={selectedRecord}
        onSave={handleSaveRecord}
        onUpdate={handleUpdateRecord}
        onDelete={handleDeleteRecord}
      />
    </motion.div>
  );
};

export default BreedingRecords;
