import React from 'react';

interface FinalFixButtonProps {
  variant?: 'contained' | 'outlined' | 'text';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  children: React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  disabled?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: React.CSSProperties;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
}

/**
 * A final fix for the MUI Button component
 * This component doesn't rely on the theme at all
 */
const FinalFixButton: React.FC<FinalFixButtonProps> = ({
  variant = 'contained',
  color = 'primary',
  onClick,
  children,
  startIcon,
  endIcon,
  disabled = false,
  fullWidth = false,
  size = 'medium',
  style,
  className,
  type = 'button'
}) => {
  // Define color mappings
  const colorMap: Record<string, { main: string; dark: string; light: string; contrastText: string }> = {
    primary: {
      main: '#3AA99F',
      dark: '#2A8A82',
      light: '#4FBEB4',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#38B2AC',
      dark: '#2C8A84',
      light: '#4FD1CB',
      contrastText: '#ffffff'
    },
    error: {
      main: '#ef4444',
      dark: '#dc2626',
      light: '#f87171',
      contrastText: '#ffffff'
    },
    warning: {
      main: '#f59e0b',
      dark: '#d97706',
      light: '#fbbf24',
      contrastText: '#ffffff'
    },
    info: {
      main: '#3b82f6',
      dark: '#2563eb',
      light: '#60a5fa',
      contrastText: '#ffffff'
    },
    success: {
      main: '#10b981',
      dark: '#059669',
      light: '#34d399',
      contrastText: '#ffffff'
    }
  };

  // Get the color values
  const colorValues = colorMap[color] || colorMap.primary;

  // Define the styles based on the variant and color
  const getStyles = () => {
    const styles: React.CSSProperties = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      boxSizing: 'border-box',
      outline: 0,
      border: 0,
      margin: 0,
      cursor: disabled ? 'not-allowed' : 'pointer',
      userSelect: 'none',
      verticalAlign: 'middle',
      textDecoration: 'none',
      fontWeight: 500,
      fontSize: size === 'small' ? '0.8125rem' : size === 'large' ? '0.9375rem' : '0.875rem',
      lineHeight: 1.75,
      letterSpacing: '0.02857em',
      textTransform: 'uppercase',
      minWidth: 64,
      padding: size === 'small' ? '4px 10px' : size === 'large' ? '8px 22px' : '6px 16px',
      borderRadius: 4,
      transition: 'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.5 : 1
    };

    // Apply variant-specific styles
    if (variant === 'contained') {
      styles.backgroundColor = colorValues.main;
      styles.color = colorValues.contrastText;
      styles.boxShadow = '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)';
    } else if (variant === 'outlined') {
      styles.backgroundColor = 'transparent';
      styles.color = colorValues.main;
      styles.border = `1px solid ${colorValues.main}`;
      styles.padding = size === 'small' ? '3px 9px' : size === 'large' ? '7px 21px' : '5px 15px';
    } else if (variant === 'text') {
      styles.backgroundColor = 'transparent';
      styles.color = colorValues.main;
      styles.padding = size === 'small' ? '3px 9px' : size === 'large' ? '7px 21px' : '6px 8px';
    }

    return styles;
  };

  // Get the hover styles
  const [isHovered, setIsHovered] = React.useState(false);

  // Get the styles
  const styles = getStyles();

  // Apply hover styles
  if (isHovered && !disabled) {
    if (variant === 'contained') {
      styles.backgroundColor = colorValues.dark;
      styles.boxShadow = '0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12)';
    } else if (variant === 'outlined') {
      styles.backgroundColor = `${colorValues.main}20`; // 20% opacity
    } else if (variant === 'text') {
      styles.backgroundColor = `${colorValues.main}10`; // 10% opacity
    }
  }

  return (
    <button
      className={`mui-button-fixed ${className || ''}`}
      style={{ ...styles, ...style }}
      onClick={onClick}
      disabled={disabled}
      type={type}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {startIcon && <span style={{ marginRight: 8, marginLeft: -4, display: 'inherit' }}>{startIcon}</span>}
      {children}
      {endIcon && <span style={{ marginRight: -4, marginLeft: 8, display: 'inherit' }}>{endIcon}</span>}
    </button>
  );
};

export default FinalFixButton;
