import React, { useState } from 'react';
import { CustomButton } from '../components/common';
import { Routes, Route, Link } from 'react-router-dom';
import { Grid, Card, CardContent, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, IconButton, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, MenuItem, Select, FormControl, InputLabel, Box, Tabs, Tab, useTheme } from '@mui/material';
import {
  Add,
  Edit,
  BarChart,
  PieChart,
  Timeline,
  Download,
  Print,
  Share,
  FilterList,
  Assessment,
  TrendingUp,
  MonetizationOn,
  LocalHospital,
  ShowChart
 } from '../utils/iconImports';
import { StandardDashboard, ModernChart } from '../components/common';
import { motion } from 'framer-motion';
import AnalysisReport from './reports/AnalysisReport';
import PerformanceReport from './reports/PerformanceReport';
import HealthReport from './reports/HealthReport';
import MarketReport from './reports/MarketReport';
import FinancialReport from './reports/FinancialReport';
import FeedingReports from './reports/FeedingReports';
import PredictiveAnalysis from './reports/PredictiveAnalysis';

// Mock data
const mockReports = [
  { id: 'REP001', name: 'Monthly Livestock Performance', category: 'Performance', lastGenerated: '2024-03-01', frequency: 'Monthly', format: 'PDF' },
  { id: 'REP002', name: 'Quarterly Financial Summary', category: 'Financial', lastGenerated: '2024-01-15', frequency: 'Quarterly', format: 'Excel' },
  { id: 'REP003', name: 'Annual Health Statistics', category: 'Health', lastGenerated: '2023-12-31', frequency: 'Annual', format: 'PDF' },
  { id: 'REP004', name: 'Feed Consumption Analysis', category: 'Feeding', lastGenerated: '2024-02-28', frequency: 'Monthly', format: 'PDF' },
  { id: 'REP005', name: 'Breeding Success Rate', category: 'Breeding', lastGenerated: '2024-02-15', frequency: 'Monthly', format: 'Excel' }
];

// Limit to 10 records per module
const mockMetrics = [
  { id: 'MET001', name: 'Average Daily Weight Gain', value: '0.8 kg', trend: 'up', change: '+5%', period: 'vs. last month' },
  { id: 'MET002', name: 'Feed Conversion Ratio', value: '4.2:1', trend: 'down', change: '-3%', period: 'vs. last month' },
  { id: 'MET003', name: 'Mortality Rate', value: '1.2%', trend: 'down', change: '-0.3%', period: 'vs. last month' },
  { id: 'MET004', name: 'Breeding Success Rate', value: '87%', trend: 'up', change: '+2%', period: 'vs. last month' },
  { id: 'MET005', name: 'Average Market Price', value: 'R42/kg', trend: 'up', change: '+8%', period: 'vs. last month' },
  { id: 'MET006', name: 'Health Treatment Cost', value: 'R15,200', trend: 'down', change: '-12%', period: 'vs. last month' }
];



const ReportsDashboard: React.FC = () => {
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);

  // Dialog handlers
  const handleOpenDialog = (report?: any) => {
    setSelectedReport(report || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedReport(null);
  };

  const handleDownload = (reportType: string) => {
    // Simulate download with a notification
    setLoading(true);
    alert(`Downloading ${reportType} report...`);

    // In a real application, this would trigger an API call to generate and download the report
    setTimeout(() => {
      console.log(`${reportType} report downloaded successfully`);
      setLoading(false);
    }, 1500);
  };

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: 'Saved Reports',
      value: mockReports.length,
      icon: <Assessment />,
      color: theme.palette.primary.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Performance Metrics',
      value: '12',
      icon: <TrendingUp />,
      color: theme.palette.success.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Financial Reports',
      value: '8',
      icon: <MonetizationOn />,
      color: theme.palette.info.main,
      trend: {
        value: 2,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Health Statistics',
      value: '15',
      icon: <LocalHospital />,
      color: theme.palette.warning.main,
      trend: {
        value: 4,
        isPositive: true,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: 'New Report',
      icon: <Add />,
      onClick: () => handleOpenDialog(),
      color: 'primary'
    },
    {
      label: 'Download All',
      icon: <Download />,
      onClick: () => handleDownload('All'),
      color: 'secondary'
    }
  ];

  const handleSave = () => {
    // Save logic would go here
    handleCloseDialog();
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <StandardDashboard
      title="Reports & Analytics"
      subtitle="Generate insights, track performance metrics, and make data-driven decisions for your livestock operation"
      icon={<Assessment />}
      stats={dashboardStats}
      actions={dashboardActions}
      isLoading={loading}
      loadingMessage="Loading reports data..."
      onRefresh={() => console.log('Refreshing reports data')}
      module="reports"
      tabs={[
        { label: 'Overview', icon: <Assessment />, content: <div>Overview Content</div> },
        { label: 'Analysis', icon: <ShowChart />, content: <AnalysisReport /> },
        { label: 'Performance', icon: <TrendingUp />, content: <PerformanceReport /> },
        { label: 'Financial', icon: <MonetizationOn />, content: <FinancialReport /> },
        { label: 'Health', icon: <LocalHospital />, content: <HealthReport /> },
        { label: 'Market', icon: <BarChart />, content: <MarketReport /> },
        { label: 'Feeding', icon: <Timeline />, content: <FeedingReports /> },
        { label: 'Custom', icon: <PieChart />, content: <PredictiveAnalysis /> }
      ]}
      activeTab={tabValue}
      onTabChange={(newValue) => setTabValue(newValue)}
    >

      {/* Tabs for different report types */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          aria-label="report tabs"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Overview" />
          <Tab label="Analysis" />
          <Tab label="Performance" />
          <Tab label="Financial" />
          <Tab label="Health" />
          <Tab label="Market" />
          <Tab label="Feeding" />
          <Tab label="Custom" />
        </Tabs>
      </Box>

      {/* Reports Dashboard - Overview Tab */}
      {tabValue === 0 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Comprehensive Dashboard
            </Typography>
            <div className="flex space-x-2">
              <CustomButton
                variant="outlined"
                startIcon={<FilterList />}
                className="border-primary-600 text-primary-600"
              >
                Filter Data
              </CustomButton>
              <CustomButton
                variant="contained"
                startIcon={<Download />}
                className="bg-primary-600 mr-2"
                onClick={() => handleDownload('All')}
              >
                Download All Reports
              </CustomButton>
              <CustomButton
                variant="contained"
                startIcon={<Add />}
                onClick={() => handleOpenDialog()}
                className="bg-primary-600"
              >
                New Report
              </CustomButton>
            </div>
          </div>

          {/* Key Metrics Overview */}
          <Typography variant="h6" className="font-bold mb-3 mt-4">
            Key Performance Indicators
          </Typography>
          <Grid container spacing={3} className="mb-6">
            {mockMetrics.map((metric) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={metric.id}>
                <motion.div variants={itemVariants}>
                  <Card className="h-full shadow-md hover:shadow-lg transition-shadow duration-300">
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        {metric.name}
                      </Typography>
                      <div className="flex items-end justify-between">
                        <Typography variant="h4" className="font-bold text-primary-800">
                          {metric.value}
                        </Typography>
                        <div className={`flex items-center ${metric.trend === 'up' ? 'text-success' : 'text-danger'}`}>
                          {metric.trend === 'up' ? '↑' : '↓'}
                          <Typography variant="body2" className="ml-1">
                            {metric.change}
                          </Typography>
                        </div>
                      </div>
                      <Typography variant="caption" color="textSecondary">
                        {metric.period}
                      </Typography>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Module Reports Overview */}
          <Typography variant="h6" className="font-bold mb-3">
            Module Reports
          </Typography>
          <Grid container spacing={3} className="mb-6">
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <ModernChart
                  title="Animal Health Status"
                  subtitle="Distribution of animal health conditions"
                  data={[
                    { name: 'Healthy', value: 75 },
                    { name: 'Under Treatment', value: 15 },
                    { name: 'Quarantined', value: 5 },
                    { name: 'Recovering', value: 5 }
                  ]}
                  type="pie"
                  dataKeys={['value']}
                  height={350}
                  accentColor={theme.palette.primary.main}
                  allowChartTypeChange={true}
                  module="reports"
                  tooltip="Shows the distribution of animal health statuses"
                  formatValue={(value) => `${value}%`}
                  onRefresh={() => console.log('Refreshing health status data')}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <ModernChart
                  title="Financial Performance"
                  subtitle="Revenue vs expenses by month"
                  data={[
                    { month: 'Jan', revenue: 45000, expenses: 32000, profit: 13000 },
                    { month: 'Feb', revenue: 52000, expenses: 34000, profit: 18000 },
                    { month: 'Mar', revenue: 48000, expenses: 36000, profit: 12000 },
                    { month: 'Apr', revenue: 61000, expenses: 41000, profit: 20000 },
                    { month: 'May', revenue: 55000, expenses: 39000, profit: 16000 },
                    { month: 'Jun', revenue: 67000, expenses: 43000, profit: 24000 }
                  ]}
                  type="bar"
                  dataKeys={['revenue', 'expenses', 'profit']}
                  xAxisDataKey="month"
                  height={350}
                  accentColor={theme.palette.success.main}
                  allowChartTypeChange={true}
                  allowTimeRangeChange={true}
                  module="reports"
                  tooltip="Shows revenue vs expenses by month"
                  formatValue={(value) => `R${value.toLocaleString()}`}
                  onRefresh={() => console.log('Refreshing financial data')}
                />
              </motion.div>
            </Grid>
          </Grid>

          {/* Recent Reports */}
          <Typography variant="h6" className="font-bold mb-3">
            Recent Reports
          </Typography>
          <motion.div variants={itemVariants}>
            <TableContainer component={Paper} className="shadow-md">
              <Table>
                <TableHead className="bg-gray-100">
                  <TableRow>
                    <TableCell><strong>Report Name</strong></TableCell>
                    <TableCell><strong>Category</strong></TableCell>
                    <TableCell><strong>Last Generated</strong></TableCell>
                    <TableCell><strong>Format</strong></TableCell>
                    <TableCell><strong>Actions</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockReports.slice(0, 5).map((report) => (
                    <TableRow key={report.id} hover>
                      <TableCell>{report.name}</TableCell>
                      <TableCell>{report.category}</TableCell>
                      <TableCell>{report.lastGenerated}</TableCell>
                      <TableCell>{report.format}</TableCell>
                      <TableCell>
                        <IconButton size="small" onClick={() => handleDownload(report.name)}>
                          <Download fontSize="small" />
                        </IconButton>
                        <IconButton size="small">
                          <Print fontSize="small" />
                        </IconButton>
                        <IconButton size="small">
                          <Share fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>
        </motion.div>
      )}

      {/* Analysis Tab */}
      {tabValue === 1 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Key Performance Metrics
            </Typography>
            <div className="flex space-x-2">
              <FormControl variant="outlined" size="small" style={{ minWidth: 150 }}>
                <InputLabel>Time Period</InputLabel>
                <Select
                  label="Time Period"
                  defaultValue="month"
                >
                  <MenuItem value="week">Last Week</MenuItem>
                  <MenuItem value="month">Last Month</MenuItem>
                  <MenuItem value="quarter">Last Quarter</MenuItem>
                  <MenuItem value="year">Last Year</MenuItem>
                </Select>
              </FormControl>
              <CustomButton
                variant="outlined"
                startIcon={<Download />}
                className="border-primary-600 text-primary-600"
              >
                Export
              </CustomButton>
            </div>
          </div>

          <Grid container spacing={3}>
            {mockMetrics.map((metric) => (
              <Grid item xs={12} sm={6} md={4} key={metric.id}>
                <motion.div variants={itemVariants}>
                  <Card className="h-full shadow-md hover:shadow-lg transition-shadow duration-300">
                    <CardContent>
                      <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                        {metric.name}
                      </Typography>
                      <div className="flex items-end justify-between">
                        <Typography variant="h4" className="font-bold text-primary-800">
                          {metric.value}
                        </Typography>
                        <div className={`flex items-center ${
                          metric.trend === 'up' ? 'text-success' : 'text-danger'
                        }`}>
                          {metric.trend === 'up' ? '↑' : '↓'}
                          <Typography variant="body2" className="ml-1">
                            {metric.change}
                          </Typography>
                        </div>
                      </div>
                      <Typography variant="caption" color="textSecondary">
                        {metric.period}
                      </Typography>

                      {/* Placeholder for chart */}
                      <div className="h-16 mt-2 bg-gray-100 rounded-md flex items-center justify-center">
                        {metric.trend === 'up' ? <Timeline fontSize="small" /> : <Timeline fontSize="small" />}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Placeholder for larger charts */}
          <Grid container spacing={3} className="mt-4">
            <Grid item xs={12} md={8}>
              <motion.div variants={itemVariants}>
                <ModernChart
                  title="Performance Trends"
                  subtitle="Key performance indicators over time"
                  data={[
                    { month: 'Jan', growth: 2.5, yield: 3.2, efficiency: 78 },
                    { month: 'Feb', growth: 2.7, yield: 3.5, efficiency: 80 },
                    { month: 'Mar', growth: 3.1, yield: 3.8, efficiency: 82 },
                    { month: 'Apr', growth: 2.9, yield: 3.6, efficiency: 81 },
                    { month: 'May', growth: 3.3, yield: 4.0, efficiency: 85 },
                    { month: 'Jun', growth: 3.5, yield: 4.2, efficiency: 87 }
                  ]}
                  type="line"
                  dataKeys={['growth', 'yield', 'efficiency']}
                  xAxisDataKey="month"
                  height={350}
                  accentColor={theme.palette.info.main}
                  allowChartTypeChange={true}
                  allowTimeRangeChange={true}
                  module="reports"
                  tooltip="Shows performance trends over time"
                  formatValue={(value) => typeof value === 'number' ? (value > 10 ? `${value}%` : value.toFixed(1)) : String(value)}
                  onRefresh={() => console.log('Refreshing performance data')}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <ModernChart
                  title="Category Distribution"
                  subtitle="Distribution by livestock category"
                  data={[
                    { name: 'Cattle', value: 45 },
                    { name: 'Sheep', value: 25 },
                    { name: 'Goats', value: 20 },
                    { name: 'Other', value: 10 }
                  ]}
                  type="pie"
                  dataKeys={['value']}
                  height={350}
                  accentColor={theme.palette.warning.main}
                  allowChartTypeChange={true}
                  module="reports"
                  tooltip="Shows distribution by livestock category"
                  formatValue={(value) => `${value}%`}
                  onRefresh={() => console.log('Refreshing category data')}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Health Tab */}
      {tabValue === 2 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Health Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Health')}
            >
              Download Health Reports
            </CustomButton>
          </div>

          <motion.div variants={itemVariants}>
            <Card className="shadow-md mb-6">
              <CardContent>
                <Typography variant="h6" className="font-bold mb-4">
                  Report Builder
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Report Type</InputLabel>
                      <Select
                        label="Report Type"
                        defaultValue="performance"
                      >
                        <MenuItem value="performance">Performance Report</MenuItem>
                        <MenuItem value="financial">Financial Report</MenuItem>
                        <MenuItem value="health">Health Report</MenuItem>
                        <MenuItem value="breeding">Breeding Report</MenuItem>
                        <MenuItem value="inventory">Inventory Report</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Time Period</InputLabel>
                      <Select
                        label="Time Period"
                        defaultValue="month"
                      >
                        <MenuItem value="week">Last Week</MenuItem>
                        <MenuItem value="month">Last Month</MenuItem>
                        <MenuItem value="quarter">Last Quarter</MenuItem>
                        <MenuItem value="year">Last Year</MenuItem>
                        <MenuItem value="custom">Custom Range</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Format</InputLabel>
                      <Select
                        label="Format"
                        defaultValue="pdf"
                      >
                        <MenuItem value="pdf">PDF</MenuItem>
                        <MenuItem value="excel">Excel</MenuItem>
                        <MenuItem value="csv">CSV</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <div className="flex justify-end mt-4">
                  <CustomButton
                    variant="contained"
                    className="bg-primary-600"
                    onClick={() => handleDownload('Custom')}
                  >
                    Generate Report
                  </CustomButton>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Typography variant="h6" className="font-bold mb-4">
              Saved Custom Reports
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={4}>
                <Card className="h-full shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-2">
                      Monthly Profit Analysis
                    </Typography>
                    <Typography variant="body2" color="textSecondary" className="mb-4">
                      Financial performance breakdown by livestock category
                    </Typography>
                    <div className="flex justify-between">
                      <Typography variant="caption" color="textSecondary">
                        Last generated: 2024-03-01
                      </Typography>
                      <div>
                        <IconButton size="small">
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDownload('Monthly Profit Analysis')}>
                          <Download fontSize="small" />
                        </IconButton>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card className="h-full shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-2">
                      Breeding Efficiency Report
                    </Typography>
                    <Typography variant="body2" color="textSecondary" className="mb-4">
                      Success rates and cost analysis for breeding program
                    </Typography>
                    <div className="flex justify-between">
                      <Typography variant="caption" color="textSecondary">
                        Last generated: 2024-02-15
                      </Typography>
                      <div>
                        <IconButton size="small">
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDownload('Breeding Efficiency Report')}>
                          <Download fontSize="small" />
                        </IconButton>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={4}>
                <Card className="h-full shadow-md hover:shadow-lg transition-shadow duration-300">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-2">
                      Health Intervention Impact
                    </Typography>
                    <Typography variant="body2" color="textSecondary" className="mb-4">
                      Analysis of health treatments and their effect on productivity
                    </Typography>
                    <div className="flex justify-between">
                      <Typography variant="caption" color="textSecondary">
                        Last generated: 2024-02-28
                      </Typography>
                      <div>
                        <IconButton size="small">
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDownload('Health Intervention Impact')}>
                          <Download fontSize="small" />
                        </IconButton>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </motion.div>
        </motion.div>
      )}

      {/* Breeding Tab */}
      {tabValue === 3 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Breeding Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Breeding')}
            >
              Download Breeding Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Breeding Success Rate
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Breeding success rate visualization
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Pregnancy Distribution
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <PieChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Pregnancy distribution by breed
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Feeding Tab */}
      {tabValue === 4 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Feeding Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Feeding')}
            >
              Download Feeding Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Feed Consumption Trends
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <Timeline fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Feed consumption over time
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Feed Cost Analysis
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Feed cost breakdown by type
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Financial Tab */}
      {tabValue === 5 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Financial Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Financial')}
            >
              Download Financial Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Revenue vs Expenses
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Monthly revenue vs expenses
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Profit Margin Analysis
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <Timeline fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Profit margin trends
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Feeding Tab */}
      {tabValue === 6 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Feeding Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Feeding')}
            >
              Download Feeding Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Feed Consumption Trends
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <Timeline fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Feed consumption over time
                      </Typography>
                    </div>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Link to="/reports/feeding" style={{ textDecoration: 'none' }}>
                        <CustomButton
                          variant="text"
                          color="primary"
                        >
                          View Detailed Reports
                        </CustomButton>
                      </Link>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Feed Cost Analysis
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Feed cost breakdown by type
                      </Typography>
                    </div>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
                      <Link to="/reports/feeding" style={{ textDecoration: 'none' }}>
                        <CustomButton
                          variant="text"
                          color="primary"
                        >
                          View Detailed Reports
                        </CustomButton>
                      </Link>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Compliance Tab */}
      {tabValue === 7 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Compliance Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Compliance')}
            >
              Download Compliance Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Certification Status
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <PieChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Certification status breakdown
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Inspection Results
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Inspection results by category
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Commercial Tab */}
      {tabValue === 8 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Commercial Reports & Analysis
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Download />}
              className="bg-primary-600"
              onClick={() => handleDownload('Commercial')}
            >
              Download Commercial Reports
            </CustomButton>
          </div>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Sales Performance
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <BarChart fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Sales performance by product
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md h-full">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Market Price Trends
                    </Typography>
                    <div className="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                      <Timeline fontSize="large" className="text-gray-400" />
                      <Typography variant="body2" color="textSecondary" className="ml-2">
                        Market price trends over time
                      </Typography>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      )}

      {/* Custom Reports Tab */}
      {tabValue === 9 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="flex justify-between items-center mb-4">
            <Typography variant="h5" className="text-gray-800 font-bold">
              Custom Reports
            </Typography>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              className="bg-primary-600"
            >
              Create Custom Report
            </CustomButton>
          </div>

          <motion.div variants={itemVariants}>
            <Card className="shadow-md mb-6">
              <CardContent>
                <Typography variant="h6" className="font-bold mb-4">
                  Report Builder
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Report Type</InputLabel>
                      <Select
                        label="Report Type"
                        defaultValue="performance"
                      >
                        <MenuItem value="performance">Performance Report</MenuItem>
                        <MenuItem value="financial">Financial Report</MenuItem>
                        <MenuItem value="health">Health Report</MenuItem>
                        <MenuItem value="breeding">Breeding Report</MenuItem>
                        <MenuItem value="inventory">Inventory Report</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Time Period</InputLabel>
                      <Select
                        label="Time Period"
                        defaultValue="month"
                      >
                        <MenuItem value="week">Last Week</MenuItem>
                        <MenuItem value="month">Last Month</MenuItem>
                        <MenuItem value="quarter">Last Quarter</MenuItem>
                        <MenuItem value="year">Last Year</MenuItem>
                        <MenuItem value="custom">Custom Range</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" className="mb-4">
                      <InputLabel>Format</InputLabel>
                      <Select
                        label="Format"
                        defaultValue="pdf"
                      >
                        <MenuItem value="pdf">PDF</MenuItem>
                        <MenuItem value="excel">Excel</MenuItem>
                        <MenuItem value="csv">CSV</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <div className="flex justify-end mt-4">
                  <CustomButton
                    variant="contained"
                    className="bg-primary-600"
                    onClick={() => handleDownload('Custom')}
                  >
                    Generate Report
                  </CustomButton>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}

      {/* Dialog for creating/editing reports */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedReport ? 'Edit Report' : 'Create New Report'}
        </DialogTitle>
        <DialogContent>
          <div className="space-y-4 py-4">
            <TextField
              label="Report Name"
              fullWidth
              defaultValue={selectedReport?.name || ''}
            />
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                label="Category"
                defaultValue={selectedReport?.category || 'Performance'}
              >
                <MenuItem value="Performance">Performance</MenuItem>
                <MenuItem value="Financial">Financial</MenuItem>
                <MenuItem value="Health">Health</MenuItem>
                <MenuItem value="Breeding">Breeding</MenuItem>
                <MenuItem value="Feeding">Feeding</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Frequency</InputLabel>
              <Select
                label="Frequency"
                defaultValue={selectedReport?.frequency || 'Monthly'}
              >
                <MenuItem value="Daily">Daily</MenuItem>
                <MenuItem value="Weekly">Weekly</MenuItem>
                <MenuItem value="Monthly">Monthly</MenuItem>
                <MenuItem value="Quarterly">Quarterly</MenuItem>
                <MenuItem value="Annual">Annual</MenuItem>
                <MenuItem value="On Demand">On Demand</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Format</InputLabel>
              <Select
                label="Format"
                defaultValue={selectedReport?.format || 'PDF'}
              >
                <MenuItem value="PDF">PDF</MenuItem>
                <MenuItem value="Excel">Excel</MenuItem>
                <MenuItem value="CSV">CSV</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Description"
              fullWidth
              multiline
              rows={3}
              defaultValue={selectedReport?.description || ''}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog} color="primary">
            Cancel
          </CustomButton>
          <CustomButton onClick={handleSave} color="primary" variant="contained">
            {selectedReport ? 'Update' : 'Create'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </StandardDashboard>
  );
};

export default ReportsDashboard;

const Reports: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ReportsDashboard />} />
      <Route path="/analysis" element={<AnalysisReport />} />
      <Route path="/performance" element={<PerformanceReport />} />
      <Route path="/financial" element={<FinancialReport />} />
      <Route path="/health" element={<HealthReport />} />
      <Route path="/market" element={<MarketReport />} />
      <Route path="/feeding" element={<FeedingReports />} />
      <Route path="/predictive" element={<PredictiveAnalysis />} />
    </Routes>
  );
};

export { Reports };
