const User = require('../models/user');
const UserPermission = require('../models/userPermission');
const jwt = require('jsonwebtoken');
const logger = require('../utils/logger');

/**
 * User controller for handling user-related requests
 */
const userController = {
  /**
   * Register a new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  register: async (req, res) => {
    try {
      const { username, email, password, firstName, lastName, role } = req.body;

      // Check if user already exists
      const existingUser = await User.findByUsername(username);
      if (existingUser) {
        return res.status(400).json({
          error: {
            message: 'Username already exists',
            status: 400
          }
        });
      }

      // Check if email already exists
      const existingEmail = await User.findByEmail(email);
      if (existingEmail) {
        return res.status(400).json({
          error: {
            message: 'Email already exists',
            status: 400
          }
        });
      }

      // Create user
      const user = await User.create({
        username,
        email,
        password,
        firstName,
        lastName,
        role: role || 'viewer'
      });

      // Create default permissions for the user
      let permissions = null;
      try {
        const permissionsDoc = await UserPermission.createOrUpdate({
          userId: user.id,
          username: user.username,
          role: user.role
        });
        permissions = permissionsDoc.permissions;
      } catch (permError) {
        logger.warn('Error creating user permissions:', permError);
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN }
      );

      // Return user without password
      const userResponse = { ...user };
      delete userResponse.password;

      res.status(201).json({
        message: 'User registered successfully',
        user: {
          ...userResponse,
          permissions
        },
        token
      });
    } catch (error) {
      logger.error('Error registering user:', error);
      res.status(500).json({
        error: {
          message: 'Error registering user',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Login user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  login: async (req, res) => {
    try {
      const { username, password } = req.body;

      // Authenticate user
      const user = await User.authenticate(username, password);
      if (!user) {
        return res.status(401).json({
          error: {
            message: 'Invalid credentials',
            status: 401
          }
        });
      }

      // Get user permissions
      let permissions = null;
      try {
        const permissionsDoc = await UserPermission.findByUserId(user.id);
        if (permissionsDoc) {
          permissions = permissionsDoc.permissions;
        }
      } catch (permError) {
        logger.warn('Error fetching user permissions:', permError);
      }

      // Generate JWT token
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN }
      );

      // Update last login time
      try {
        await User.update(user.id, { lastLogin: new Date() });
      } catch (updateError) {
        logger.warn('Error updating last login time:', updateError);
      }

      res.status(200).json({
        message: 'Login successful',
        user: {
          ...user,
          permissions
        },
        token
      });
    } catch (error) {
      logger.error('Error logging in:', error);
      res.status(500).json({
        error: {
          message: 'Error logging in',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get current user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getProfile: async (req, res) => {
    try {
      const user = await User.findById(req.user.id);
      if (!user) {
        return res.status(404).json({
          error: {
            message: 'User not found',
            status: 404
          }
        });
      }

      res.status(200).json({
        user
      });
    } catch (error) {
      logger.error('Error getting user profile:', error);
      res.status(500).json({
        error: {
          message: 'Error getting user profile',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Update user profile
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateProfile: async (req, res) => {
    try {
      const { username, email, firstName, lastName, password } = req.body;

      // Check if username is taken by another user
      if (username) {
        const existingUser = await User.findByUsername(username);
        if (existingUser && existingUser.id !== req.user.id) {
          return res.status(400).json({
            error: {
              message: 'Username already exists',
              status: 400
            }
          });
        }
      }

      // Check if email is taken by another user
      if (email) {
        const existingEmail = await User.findByEmail(email);
        if (existingEmail && existingEmail.id !== req.user.id) {
          return res.status(400).json({
            error: {
              message: 'Email already exists',
              status: 400
            }
          });
        }
      }

      // Update user
      const updatedUser = await User.update(req.user.id, {
        username,
        email,
        firstName,
        lastName,
        password
      });

      res.status(200).json({
        message: 'Profile updated successfully',
        user: updatedUser
      });
    } catch (error) {
      logger.error('Error updating user profile:', error);
      res.status(500).json({
        error: {
          message: 'Error updating user profile',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get all users (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllUsers: async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const result = await User.findAll(page, limit);

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting all users:', error);
      res.status(500).json({
        error: {
          message: 'Error getting all users',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get user by ID (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getUserById: async (req, res) => {
    try {
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({
          error: {
            message: 'User not found',
            status: 404
          }
        });
      }

      // Get user permissions
      let permissions = null;
      try {
        const permissionsDoc = await UserPermission.findByUserId(user.id);
        if (permissionsDoc) {
          permissions = permissionsDoc.permissions;
        }
      } catch (permError) {
        logger.warn('Error fetching user permissions:', permError);
      }

      // Return user without password
      const userResponse = { ...user };
      delete userResponse.password;

      res.status(200).json({
        user: {
          ...userResponse,
          permissions
        }
      });
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      res.status(500).json({
        error: {
          message: 'Error getting user',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Update user by ID (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateUser: async (req, res) => {
    try {
      const { username, email, firstName, lastName, password, role } = req.body;

      // Check if user exists
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({
          error: {
            message: 'User not found',
            status: 404
          }
        });
      }

      // Check if username is taken by another user
      if (username) {
        const existingUser = await User.findByUsername(username);
        if (existingUser && existingUser.id !== parseInt(req.params.id)) {
          return res.status(400).json({
            error: {
              message: 'Username already exists',
              status: 400
            }
          });
        }
      }

      // Check if email is taken by another user
      if (email) {
        const existingEmail = await User.findByEmail(email);
        if (existingEmail && existingEmail.id !== parseInt(req.params.id)) {
          return res.status(400).json({
            error: {
              message: 'Email already exists',
              status: 400
            }
          });
        }
      }

      // Get original user role
      const originalRole = user.role;

      // Update user
      const updatedUser = await User.update(req.params.id, {
        username,
        email,
        firstName,
        lastName,
        password,
        role
      });

      // Update permissions if role changed
      let permissions = null;
      if (role && role !== originalRole) {
        try {
          const permissionsDoc = await UserPermission.createOrUpdate({
            userId: updatedUser.id,
            username: updatedUser.username,
            role: updatedUser.role
          });
          permissions = permissionsDoc.permissions;
        } catch (permError) {
          logger.warn('Error updating user permissions:', permError);
        }
      } else {
        try {
          const permissionsDoc = await UserPermission.findByUserId(updatedUser.id);
          if (permissionsDoc) {
            permissions = permissionsDoc.permissions;
          }
        } catch (permError) {
          logger.warn('Error fetching user permissions:', permError);
        }
      }

      res.status(200).json({
        message: 'User updated successfully',
        user: {
          ...updatedUser,
          permissions
        }
      });
    } catch (error) {
      logger.error('Error updating user:', error);
      res.status(500).json({
        error: {
          message: 'Error updating user',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Delete user by ID (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteUser: async (req, res) => {
    try {
      // Check if user exists
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({
          error: {
            message: 'User not found',
            status: 404
          }
        });
      }

      // Delete user
      const deleted = await User.delete(req.params.id);
      if (!deleted) {
        return res.status(500).json({
          error: {
            message: 'Failed to delete user',
            status: 500
          }
        });
      }

      // Delete user permissions
      try {
        await UserPermission.findOneAndDelete({ userId: req.params.id });
      } catch (permError) {
        logger.warn('Error deleting user permissions:', permError);
      }

      res.status(200).json({
        message: 'User deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting user:', error);
      res.status(500).json({
        error: {
          message: 'Error deleting user',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Update user permissions (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateUserPermissions: async (req, res) => {
    try {
      const { permissions } = req.body;

      // Check if user exists
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({
          error: {
            message: 'User not found',
            status: 404
          }
        });
      }

      // Update permissions
      const updatedPermissions = await UserPermission.createOrUpdate({
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions
      });

      res.status(200).json({
        message: 'User permissions updated successfully',
        permissions: updatedPermissions.permissions
      });
    } catch (error) {
      logger.error('Error updating user permissions:', error);
      res.status(500).json({
        error: {
          message: 'Error updating user permissions',
          details: error.message,
          status: 500
        }
      });
    }
  }
};

module.exports = userController;