import { smsService, useSMSService } from './smsService';
import { renderHook, act } from '@testing-library/react-hooks';
import React from 'react';

// Mock the SnackbarProvider component
const SnackbarProvider = ({ children }: { children: React.ReactNode }) => {
  return React.createElement('div', {}, children);
};

// Mock the useSnackbar hook
jest.mock('../hooks/useSnackbar', () => ({
  __esModule: true,
  useSnackbar: () => ({
    showSnackbar: jest.fn()
  }),
  default: () => ({
    showSnackbar: jest.fn()
  })
}));

describe('SMS Service', () => {
  describe('smsService', () => {
    it('should send an SMS message', async () => {
      // Spy on console.log
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const message = {
        to: '+27123456789',
        message: 'Test message',
        priority: 'high' as const
      };

      const result = await smsService.sendSMS(message);

      // Check if console.log was called with the message
      expect(consoleSpy).toHaveBeenCalledWith('Sending SMS:', message);

      // Check if the result is successful
      expect(result.success).toBe(true);
      expect(result.messageId).toBeDefined();

      // Restore console.log
      consoleSpy.mockRestore();
    });

    it('should handle errors when sending SMS', async () => {
      // Mock console.error
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      // Mock implementation to throw an error
      const originalSendSMS = smsService.sendSMS;
      smsService.sendSMS = jest.fn().mockImplementation(() => {
        throw new Error('Test error');
      });

      try {
        await smsService.sendSMS({
          to: '+27123456789',
          message: 'Test message'
        });
      } catch (error) {
        expect(consoleErrorSpy).toHaveBeenCalled();
      }

      // Restore original implementation
      smsService.sendSMS = originalSendSMS;
      consoleErrorSpy.mockRestore();
    });

    it('should send a birth prediction alert', async () => {
      // Spy on sendSMS method
      const sendSMSSpy = jest.spyOn(smsService, 'sendSMS').mockResolvedValue({
        success: true,
        messageId: 'test-id'
      });

      const result = await smsService.sendBirthPredictionAlert(
        'CTL001',
        'Bella',
        5,
        '+27123456789'
      );

      // Check if sendSMS was called with the correct parameters
      expect(sendSMSSpy).toHaveBeenCalledWith({
        to: '+27123456789',
        message: expect.stringContaining('BIRTH ALERT'),
        priority: 'high'
      });

      // Check if the result is successful
      expect(result.success).toBe(true);

      // Restore original implementation
      sendSMSSpy.mockRestore();
    });

    it('should send a health alert', async () => {
      // Spy on sendSMS method
      const sendSMSSpy = jest.spyOn(smsService, 'sendSMS').mockResolvedValue({
        success: true,
        messageId: 'test-id'
      });

      const result = await smsService.sendHealthAlert(
        'CTL001',
        'Bella',
        'Respiratory infection',
        '+27123456789'
      );

      // Check if sendSMS was called with the correct parameters
      expect(sendSMSSpy).toHaveBeenCalledWith({
        to: '+27123456789',
        message: expect.stringContaining('HEALTH ALERT'),
        priority: 'critical'
      });

      // Check if the result is successful
      expect(result.success).toBe(true);

      // Restore original implementation
      sendSMSSpy.mockRestore();
    });
  });

  describe('useSMSService hook', () => {
    it('should send an SMS and show a success snackbar', async () => {
      // Render the hook with the necessary providers
      const wrapper = ({ children }: { children: React.ReactNode }) => {
        return React.createElement(SnackbarProvider, {}, children);
      };

      const { result } = renderHook(() => useSMSService(), { wrapper });

      // Mock the smsService.sendSMS method
      const sendSMSSpy = jest.spyOn(smsService, 'sendSMS').mockResolvedValue({
        success: true,
        messageId: 'test-id'
      });

      // Call the hook's sendSMS method
      await act(async () => {
        await result.current.sendSMS({
          to: '+27123456789',
          message: 'Test message'
        });
      });

      // Check if smsService.sendSMS was called
      expect(sendSMSSpy).toHaveBeenCalled();

      // Restore original implementation
      sendSMSSpy.mockRestore();
    });

    it('should handle errors when sending SMS', async () => {
      // Render the hook with the necessary providers
      const wrapper = ({ children }: { children: React.ReactNode }) => {
        return React.createElement(SnackbarProvider, {}, children);
      };

      const { result } = renderHook(() => useSMSService(), { wrapper });

      // Mock the smsService.sendSMS method to throw an error
      const sendSMSSpy = jest.spyOn(smsService, 'sendSMS').mockRejectedValue(
        new Error('Test error')
      );

      // Call the hook's sendSMS method
      await act(async () => {
        await result.current.sendSMS({
          to: '+27123456789',
          message: 'Test message'
        });
      });

      // Check if smsService.sendSMS was called
      expect(sendSMSSpy).toHaveBeenCalled();

      // Restore original implementation
      sendSMSSpy.mockRestore();
    });
  });
});
