{"login.welcome": "Bem-v<PERSON><PERSON> Volta", "login.subtitle": "Entre na sua conta AMPD Livestock", "login.username": "Nome de usuário", "login.password": "<PERSON><PERSON>", "login.signin": "ENTRAR", "login.sign_in": "ENTRAR", "login.title": "Bem-v<PERSON><PERSON> Volta", "login.footer_text": "Sistema de Gestão de Gado AMPD", "login.enter_username": "Digite seu nome de usuário", "login.enter_password": "Digite sua senha", "login.error.credentials": "Nome de usuário ou senha inválid<PERSON>", "login.error.empty_fields": "Por favor, digite o nome de usuário e a senha", "login.error.invalid_credentials": "Nome de usuário ou senha inválid<PERSON>", "login.language_selection": "Selecionar Idioma", "login.forgot_password": "<PERSON><PERSON><PERSON> a senha?", "nav.dashboard": "<PERSON><PERSON>", "nav.animals": "<PERSON><PERSON><PERSON>", "nav.health": "<PERSON><PERSON><PERSON>", "nav.breeding": "Reprodução", "nav.feeding": "Alimentação", "nav.financial": "Financeiro", "nav.inventory": "Inventário", "nav.commercial": "Comercial", "nav.reports": "Relatórios", "nav.analytics": "<PERSON><PERSON><PERSON><PERSON>", "nav.resources": "Recursos", "nav.compliance": "Conformidade", "nav.settings": "Configurações", "dashboard.total_animals": "Total de Animais", "dashboard.healthy_animals": "<PERSON><PERSON><PERSON>", "dashboard.animals_attention": "{{count}} animais precisam de atenção", "dashboard.pending_checkups": "{{count}} exames pendentes", "dashboard.due_dates": "{{count}} datas de vencimento", "dashboard.low_stock": "{{count}} estoque baixo", "dashboard.revenue_month": "<PERSON><PERSON><PERSON> deste mês", "dashboard.new_insights": "{{count}} novos insights", "dashboard.health_trends": "Tendências de Saúde", "dashboard.weekly_health": "<PERSON><PERSON><PERSON>", "dashboard.financial_performance": "Desempenho Financeiro", "dashboard.monthly_revenue": "<PERSON><PERSON><PERSON>", "dashboard.sponsors": "Patrocinadores", "animals.title": "Gestão de Animais", "animals.total": "Total de Animais", "animals.healthy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animals.sick": "<PERSON><PERSON><PERSON>", "animals.add_animal": "<PERSON><PERSON><PERSON><PERSON>", "animals.search": "Buscar Animais", "animals.filter": "Filtrar", "animals.name": "Nome", "animals.species": "Espécie", "animals.breed": "Raça", "animals.age": "<PERSON><PERSON>", "animals.weight": "Peso", "animals.status": "Status", "animals.location": "Localização", "health.title": "Gestão de Saúde", "health.records": "Registros de Saúde", "health.treatments": "Trata<PERSON>s", "health.vaccinations": "Vacinações", "health.appointments": "Consultas", "health.add_record": "<PERSON><PERSON><PERSON><PERSON>", "health.schedule_appointment": "Agendar <PERSON>", "breeding.title": "Gestão de Reprodução", "breeding.records": "Registros de Reprodução", "breeding.schedule": "Cronograma de Reprodução", "breeding.pregnancy": "Acompanhamento de Gravidez", "breeding.births": "Registros de Nascimento", "breeding.predictions": "Previsões de Nascimento", "feeding.title": "Gestão de Alimentação", "feeding.records": "Registros de Alimentação", "feeding.schedules": "Cronogramas de Alimentação", "feeding.plans": "Planos de Alimentação", "feeding.nutrition": "Nutrição", "feeding.inventory": "Inventário de Ração", "financial.title": "Gestão Financeira", "financial.overview": "Visão Geral", "financial.transactions": "Transações", "financial.reports": "Relatórios", "financial.budgets": "Orçamentos", "financial.invoices": "<PERSON><PERSON><PERSON>", "reports.title": "Relatórios e Análises", "reports.analysis": "<PERSON><PERSON><PERSON><PERSON>", "reports.performance": "<PERSON><PERSON><PERSON><PERSON>", "reports.health": "Relatórios de Saúde", "reports.market": "Relatórios de Mercado", "reports.financial": "Relatórios Financeiros", "settings.title": "Configurações", "settings.theme": "Configurações de Tema", "settings.users": "Gestão de Usuários", "settings.database": "Configurações de Banco de Dados", "settings.backup": "Configurações de Backup", "common.save": "<PERSON><PERSON>", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "Excluir", "common.edit": "<PERSON><PERSON>", "common.add": "<PERSON><PERSON><PERSON><PERSON>", "common.search": "Buscar", "common.filter": "Filtrar", "common.export": "Exportar", "common.import": "Importar", "common.print": "Imprimir", "common.help": "<PERSON><PERSON><PERSON>", "common.loading": "Carregando...", "common.error": "Erro", "common.success": "Sucesso", "common.warning": "Aviso", "common.info": "Informação"}