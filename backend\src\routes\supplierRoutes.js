/**
 * Supplier Routes
 * 
 * API routes for supplier-related operations.
 */

const express = require('express');
const router = express.Router();
const supplierController = require('../controllers/supplierController');
const { protect, authorize } = require('../middleware/auth');

// Public routes
router.get('/', supplierController.getAllSuppliers);
router.get('/:id', supplierController.getSupplierById);
router.get('/:id/products', supplierController.getSupplierProducts);

// Admin routes
router.post('/update', protect, authorize('admin'), supplierController.updateSupplierData);

module.exports = router;
