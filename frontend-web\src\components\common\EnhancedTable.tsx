import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TableSortLabel, Paper, Typography, IconButton, Tooltip, Chip, Menu, MenuItem, Checkbox, useTheme, alpha, Card, CardContent, CardHeader, Divider, TextField, InputAdornment } from '@mui/material';
import {
  FilterList,
  Search,
  MoreVert,
  Download,
  Print,
  Share,
  Refresh,
  ViewColumn,
  Delete,
  Edit,
  Visibility,
  Add
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';

export interface Column {
  id: string;
  label: string;
  align?: 'left' | 'right' | 'center';
  format?: (value: any) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
  hidden?: boolean;
}

export interface Action {
  icon: React.ReactNode;
  label: string;
  onClick: (row: any) => void;
  color?: string;
  disabled?: (row: any) => boolean;
  hidden?: (row: any) => boolean;
}

export interface EnhancedTableProps {
  title: string;
  subtitle?: string;
  columns: Column[];
  data: any[];
  actions?: Action[];
  onAdd?: () => void;
  onRefresh?: () => void;
  onDownload?: () => void;
  onPrint?: () => void;
  onShare?: () => void;
  onDelete?: (selected: any[]) => void;
  onEdit?: (row: any) => void;
  onView?: (row: any) => void;
  selectable?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  module?: string;
  rowsPerPageOptions?: number[];
  defaultRowsPerPage?: number;
  defaultSortBy?: string;
  defaultSortDirection?: 'asc' | 'desc';
  searchable?: boolean;
  searchPlaceholder?: string;
  elevation?: number;
  height?: string | number;
  maxHeight?: string | number;
  stickyHeader?: boolean;
  dense?: boolean;
  showPagination?: boolean;
  onRowClick?: (row: any) => void;
  getRowId?: (row: any) => string | number;
}

const EnhancedTable: React.FC<EnhancedTableProps> = ({
  title,
  subtitle,
  columns,
  data,
  actions,
  onAdd,
  onRefresh,
  onDownload,
  onPrint,
  onShare,
  onDelete,
  onEdit,
  onView,
  selectable = false,
  loading = false,
  emptyMessage = 'No data available',
  module,
  rowsPerPageOptions = [5, 10, 25, 50],
  defaultRowsPerPage = 10,
  defaultSortBy,
  defaultSortDirection = 'asc',
  searchable = true,
  searchPlaceholder = 'Search...',
  elevation = 2,
  height,
  maxHeight = '70vh',
  stickyHeader = true,
  dense = false,
  showPagination = true,
  onRowClick,
  getRowId = (row) => row.id
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // State
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);
  const [order, setOrder] = useState<'asc' | 'desc'>(defaultSortDirection);
  const [orderBy, setOrderBy] = useState<string | undefined>(defaultSortBy);
  const [selected, setSelected] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [columnMenuAnchorEl, setColumnMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    columns.filter(column => !column.hidden).map(column => column.id)
  );

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      default:
        return theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  // Menu handlers
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleColumnMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setColumnMenuAnchorEl(event.currentTarget);
  };

  const handleColumnMenuClose = () => {
    setColumnMenuAnchorEl(null);
  };

  // Sorting
  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Selection
  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(filteredData);
      return;
    }
    setSelected([]);
  };

  const handleSelectClick = (event: React.MouseEvent, row: any) => {
    event.stopPropagation();
    
    const selectedIndex = selected.findIndex(item => getRowId(item) === getRowId(row));
    let newSelected: any[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, row];
    } else {
      newSelected = selected.filter(item => getRowId(item) !== getRowId(row));
    }

    setSelected(newSelected);
  };

  const isSelected = (row: any) => selected.findIndex(item => getRowId(item) === getRowId(row)) !== -1;

  // Pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Search
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Column visibility
  const handleToggleColumnVisibility = (columnId: string) => {
    if (visibleColumns.includes(columnId)) {
      setVisibleColumns(visibleColumns.filter(id => id !== columnId));
    } else {
      setVisibleColumns([...visibleColumns, columnId]);
    }
  };

  // Filter data based on search term
  const filteredData = data.filter(row => {
    if (!searchTerm) return true;
    
    const searchTermLower = searchTerm.toLowerCase();
    return columns.some(column => {
      const value = row[column.id];
      if (value === null || value === undefined) return false;
      return String(value).toLowerCase().includes(searchTermLower);
    });
  });

  // Sort data
  const sortedData = orderBy
    ? [...filteredData].sort((a, b) => {
        const aValue = a[orderBy];
        const bValue = b[orderBy];
        
        if (aValue === bValue) return 0;
        if (aValue === null || aValue === undefined) return order === 'asc' ? -1 : 1;
        if (bValue === null || bValue === undefined) return order === 'asc' ? 1 : -1;
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return order === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        return order === 'asc'
          ? aValue < bValue ? -1 : 1
          : aValue < bValue ? 1 : -1;
      })
    : filteredData;

  // Paginate data
  const paginatedData = showPagination
    ? sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : sortedData;

  // Visible columns
  const visibleColumnsList = columns.filter(column => visibleColumns.includes(column.id));

  return (
    <Card
      elevation={elevation}
      className="color-transition"
      sx={{
        height: height || 'auto',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '12px',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
        backdropFilter: 'blur(10px)',
        background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
        '&:hover': {
          boxShadow: `0 8px 25px ${alpha(moduleColor, 0.15)}`,
        }
      }}
    >
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              {title}
            </Typography>
            {selected.length > 0 && (
              <Chip
                label={`${selected.length} ${translate('table.selected')}`}
                size="small"
                color="primary"
                sx={{ ml: 2 }}
              />
            )}
          </Box>
        }
        subheader={subtitle}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {onAdd && (
              <Tooltip title={translate('common.add')}>
                <IconButton onClick={onAdd} size="small" color="primary">
                  <Add />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title={translate('table.columns')}>
              <IconButton onClick={handleColumnMenuOpen} size="small">
                <ViewColumn />
              </IconButton>
            </Tooltip>
            <IconButton onClick={handleMenuOpen} size="small">
              <MoreVert />
            </IconButton>
          </Box>
        }
        sx={{
          pb: 0,
          '& .MuiCardHeader-title': {
            fontSize: '1rem',
            fontWeight: 'bold'
          },
          '& .MuiCardHeader-subheader': {
            fontSize: '0.8rem'
          }
        }}
      />
      <CardContent sx={{ p: 0, pt: 2, flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Search and filters */}
        {searchable && (
          <Box sx={{ px: 2, mb: 2 }}>
            <TextField
              fullWidth
              size="small"
              placeholder={searchPlaceholder}
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setSearchTerm('')}
                      edge="end"
                    >
                      <Delete fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: '8px',
                  backgroundColor: alpha(theme.palette.background.paper, 0.5),
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.background.paper, 0.7),
                  }
                }
              }}
            />
          </Box>
        )}

        {/* Table */}
        <TableContainer
          sx={{
            maxHeight,
            flex: 1,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
              height: '8px'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: alpha(theme.palette.primary.main, 0.2),
              borderRadius: '4px'
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: alpha(theme.palette.primary.main, 0.05)
            }
          }}
        >
          <Table
            stickyHeader={stickyHeader}
            size={dense ? 'small' : 'medium'}
            aria-label={title}
          >
            <TableHead>
              <TableRow>
                {selectable && (
                  <TableCell padding="checkbox">
                    <Checkbox
                      indeterminate={selected.length > 0 && selected.length < filteredData.length}
                      checked={filteredData.length > 0 && selected.length === filteredData.length}
                      onChange={handleSelectAllClick}
                      inputProps={{ 'aria-label': 'select all' }}
                      sx={{
                        color: moduleColor,
                        '&.Mui-checked': {
                          color: moduleColor,
                        }
                      }}
                    />
                  </TableCell>
                )}
                {visibleColumnsList.map(column => (
                  <TableCell
                    key={column.id}
                    align={column.align || 'left'}
                    sortDirection={orderBy === column.id ? order : false}
                    sx={{
                      fontWeight: 'bold',
                      backgroundColor: alpha(moduleColor, 0.05),
                      width: column.width,
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {column.sortable !== false ? (
                      <TableSortLabel
                        active={orderBy === column.id}
                        direction={orderBy === column.id ? order : 'asc'}
                        onClick={() => handleRequestSort(column.id)}
                        sx={{
                          '&.MuiTableSortLabel-active': {
                            color: moduleColor,
                          },
                          '& .MuiTableSortLabel-icon': {
                            color: `${moduleColor} !important`,
                          }
                        }}
                      >
                        {column.label}
                      </TableSortLabel>
                    ) : (
                      column.label
                    )}
                  </TableCell>
                ))}
                {(actions || onEdit || onView || onDelete) && (
                  <TableCell
                    align="right"
                    sx={{
                      fontWeight: 'bold',
                      backgroundColor: alpha(moduleColor, 0.05),
                      width: '120px',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {translate('table.actions')}
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell
                    colSpan={
                      (selectable ? 1 : 0) +
                      visibleColumnsList.length +
                      ((actions || onEdit || onView || onDelete) ? 1 : 0)
                    }
                    align="center"
                    sx={{ py: 4 }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {translate('common.loading')}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={
                      (selectable ? 1 : 0) +
                      visibleColumnsList.length +
                      ((actions || onEdit || onView || onDelete) ? 1 : 0)
                    }
                    align="center"
                    sx={{ py: 4 }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {emptyMessage}
                    </Typography>
                    {onRefresh && (
                      <CustomButton
                        startIcon={<Refresh />}
                        onClick={onRefresh}
                        sx={{ mt: 2 }}
                        variant="outlined"
                        size="small"
                      >
                        {translate('common.refresh')}
                      </CustomButton>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((row, index) => {
                  const isItemSelected = isSelected(row);
                  const labelId = `enhanced-table-checkbox-${index}`;

                  return (
                    <TableRow
                      hover
                      onClick={onRowClick ? () => onRowClick(row) : undefined}
                      role="checkbox"
                      aria-checked={isItemSelected}
                      tabIndex={-1}
                      key={getRowId(row)}
                      selected={isItemSelected}
                      sx={{
                        cursor: onRowClick ? 'pointer' : 'default',
                        '&.Mui-selected': {
                          backgroundColor: alpha(moduleColor, 0.1),
                        },
                        '&.Mui-selected:hover': {
                          backgroundColor: alpha(moduleColor, 0.15),
                        }
                      }}
                    >
                      {selectable && (
                        <TableCell padding="checkbox">
                          <Checkbox
                            checked={isItemSelected}
                            onClick={(event) => handleSelectClick(event, row)}
                            inputProps={{ 'aria-labelledby': labelId }}
                            sx={{
                              color: moduleColor,
                              '&.Mui-checked': {
                                color: moduleColor,
                              }
                            }}
                          />
                        </TableCell>
                      )}
                      {visibleColumnsList.map(column => (
                        <TableCell
                          key={column.id}
                          align={column.align || 'left'}
                          sx={{ whiteSpace: 'nowrap' }}
                        >
                          {column.format ? column.format(row[column.id]) : row[column.id]}
                        </TableCell>
                      ))}
                      {(actions || onEdit || onView || onDelete) && (
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                            {onView && (
                              <Tooltip title={translate('common.view')}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onView(row);
                                  }}
                                  sx={{ color: theme.palette.info.main }}
                                >
                                  <Visibility fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {onEdit && (
                              <Tooltip title={translate('common.edit')}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEdit(row);
                                  }}
                                  sx={{ color: theme.palette.warning.main }}
                                >
                                  <Edit fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {onDelete && (
                              <Tooltip title={translate('common.delete')}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onDelete([row]);
                                  }}
                                  sx={{ color: theme.palette.error.main }}
                                >
                                  <Delete fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {actions && actions.map((action, actionIndex) => {
                              if (action.hidden && action.hidden(row)) return null;
                              
                              return (
                                <Tooltip key={actionIndex} title={action.label}>
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      action.onClick(row);
                                    }}
                                    disabled={action.disabled ? action.disabled(row) : false}
                                    sx={{
                                      color: action.color
                                        ? theme.palette[action.color as 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success'].main
                                        : undefined
                                    }}
                                  >
                                    {action.icon}
                                  </IconButton>
                                </Tooltip>
                              );
                            })}
                          </Box>
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {showPagination && (
          <TablePagination
            rowsPerPageOptions={rowsPerPageOptions}
            component="div"
            count={filteredData.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderTop: `1px solid ${theme.palette.divider}`,
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                fontSize: '0.875rem',
              }
            }}
          />
        )}
      </CardContent>

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            mt: 1
          }
        }}
      >
        {onRefresh && (
          <MenuItem onClick={() => { onRefresh(); handleMenuClose(); }}>
            <Refresh fontSize="small" sx={{ mr: 1 }} />
            {translate('common.refresh')}
          </MenuItem>
        )}
        {onDownload && (
          <MenuItem onClick={() => { onDownload(); handleMenuClose(); }}>
            <Download fontSize="small" sx={{ mr: 1 }} />
            {translate('common.download')}
          </MenuItem>
        )}
        {onPrint && (
          <MenuItem onClick={() => { onPrint(); handleMenuClose(); }}>
            <Print fontSize="small" sx={{ mr: 1 }} />
            {translate('common.print')}
          </MenuItem>
        )}
        {onShare && (
          <MenuItem onClick={() => { onShare(); handleMenuClose(); }}>
            <Share fontSize="small" sx={{ mr: 1 }} />
            {translate('common.share')}
          </MenuItem>
        )}
        {onDelete && selected.length > 0 && (
          <MenuItem
            onClick={() => {
              onDelete(selected);
              setSelected([]);
              handleMenuClose();
            }}
            sx={{ color: theme.palette.error.main }}
          >
            <Delete fontSize="small" sx={{ mr: 1 }} />
            {translate('common.delete')} ({selected.length})
          </MenuItem>
        )}
      </Menu>

      {/* Column Visibility Menu */}
      <Menu
        anchorEl={columnMenuAnchorEl}
        open={Boolean(columnMenuAnchorEl)}
        onClose={handleColumnMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 180,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            mt: 1
          }
        }}
      >
        {columns.map(column => (
          <MenuItem key={column.id} onClick={() => handleToggleColumnVisibility(column.id)}>
            <Checkbox
              checked={visibleColumns.includes(column.id)}
              sx={{
                color: moduleColor,
                '&.Mui-checked': {
                  color: moduleColor,
                }
              }}
            />
            {column.label}
          </MenuItem>
        ))}
      </Menu>
    </Card>
  );
};

export default EnhancedTable;
