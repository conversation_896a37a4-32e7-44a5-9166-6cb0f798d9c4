/**
 * Reports API Routes
 */

const express = require('express');
const router = express.Router();
const mongoose = require('mongoose');
const Report = require('../models/Report');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

// Get all reports
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { type, module, status, author, format, startDate, endDate, search } = req.query;
    
    // Build filter object
    const filter = {};
    
    if (type) filter.type = type;
    if (module) filter.module = module;
    if (status) filter.status = status;
    if (author) filter.author = author;
    if (format) filter.format = format;
    
    // Date range filter
    if (startDate || endDate) {
      filter.date = {};
      if (startDate) filter.date.$gte = new Date(startDate);
      if (endDate) filter.date.$lte = new Date(endDate);
    }
    
    // Search filter
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    const reports = await Report.find(filter).sort({ date: -1 });
    res.json(reports);
  } catch (error) {
    logger.error('Error fetching reports:', error);
    res.status(500).json({ message: 'Error fetching reports', error: error.message });
  }
});

// Get report by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findOne({ id: req.params.id });
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Increment view count
    report.viewCount += 1;
    await report.save();
    
    res.json(report);
  } catch (error) {
    logger.error(`Error fetching report ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error fetching report', error: error.message });
  }
});

// Create a new report
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { title, type, module, format, description, tags } = req.body;
    
    // Validate required fields
    if (!title || !type || !module) {
      return res.status(400).json({ message: 'Title, type, and module are required' });
    }
    
    // Generate a unique report ID
    const reportCount = await Report.countDocuments();
    const reportId = `REP${(reportCount + 1).toString().padStart(3, '0')}`;
    
    const newReport = new Report({
      id: reportId,
      title,
      type,
      module,
      format: format || 'pdf',
      description,
      tags: tags || [],
      author: req.user.username || 'System',
      status: 'generated',
      date: new Date(),
      size: '1.0 MB', // Default size
      downloadUrl: `/reports/${reportId}.${format || 'pdf'}`,
      viewCount: 0,
      downloadCount: 0
    });
    
    const savedReport = await newReport.save();
    res.status(201).json(savedReport);
  } catch (error) {
    logger.error('Error creating report:', error);
    res.status(500).json({ message: 'Error creating report', error: error.message });
  }
});

// Update a report
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findOne({ id: req.params.id });
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Update fields
    const updatableFields = [
      'title', 'description', 'tags', 'status', 'isShared', 'permissions'
    ];
    
    updatableFields.forEach(field => {
      if (req.body[field] !== undefined) {
        report[field] = req.body[field];
      }
    });
    
    report.updatedAt = new Date();
    
    const updatedReport = await report.save();
    res.json(updatedReport);
  } catch (error) {
    logger.error(`Error updating report ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error updating report', error: error.message });
  }
});

// Delete a report
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findOne({ id: req.params.id });
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    await Report.deleteOne({ id: req.params.id });
    res.json({ message: 'Report deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting report ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error deleting report', error: error.message });
  }
});

// Download a report (increment download count)
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const report = await Report.findOne({ id: req.params.id });
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    // Increment download count
    report.downloadCount += 1;
    await report.save();
    
    // In a real application, this would serve the actual file
    // For this demo, we'll just return the report data
    res.json({
      ...report.toObject(),
      message: 'Download initiated'
    });
  } catch (error) {
    logger.error(`Error downloading report ${req.params.id}:`, error);
    res.status(500).json({ message: 'Error downloading report', error: error.message });
  }
});

// Get report statistics
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    const totalReports = await Report.countDocuments();
    
    // Reports generated this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const reportsThisMonth = await Report.countDocuments({
      date: { $gte: startOfMonth }
    });
    
    // Most viewed report
    const mostViewedReport = await Report.findOne().sort({ viewCount: -1 }).limit(1);
    
    // Most downloaded report
    const mostDownloadedReport = await Report.findOne().sort({ downloadCount: -1 }).limit(1);
    
    // Reports by type
    const reportsByType = await Report.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Reports by module
    const reportsByModule = await Report.aggregate([
      { $group: { _id: '$module', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    res.json({
      totalReports,
      reportsThisMonth,
      mostViewedReport: mostViewedReport ? {
        id: mostViewedReport.id,
        title: mostViewedReport.title,
        viewCount: mostViewedReport.viewCount
      } : null,
      mostDownloadedReport: mostDownloadedReport ? {
        id: mostDownloadedReport.id,
        title: mostDownloadedReport.title,
        downloadCount: mostDownloadedReport.downloadCount
      } : null,
      reportsByType: reportsByType.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      reportsByModule: reportsByModule.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {})
    });
  } catch (error) {
    logger.error('Error fetching report statistics:', error);
    res.status(500).json({ message: 'Error fetching report statistics', error: error.message });
  }
});

module.exports = router;
