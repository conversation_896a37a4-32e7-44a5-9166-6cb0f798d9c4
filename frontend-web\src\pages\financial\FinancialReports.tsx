import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Paper, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import {  Download, PictureAsPdf, InsertDriveFile  } from '../../utils/iconImports';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

// Mock monthly data
const monthlyData = [
  { month: 'Jan', income: 18500, expenses: 15200, profit: 3300 },
  { month: 'Feb', income: 19200, expenses: 14800, profit: 4400 },
  { month: 'Mar', income: 21000, expenses: 16500, profit: 4500 },
  { month: 'Apr', income: 20500, expenses: 15800, profit: 4700 },
  { month: 'May', income: 22500, expenses: 17200, profit: 5300 },
  { month: 'Jun', income: 23500, expenses: 18000, profit: 5500 },
];

// Mock expense categories
const expenseCategories = [
  { name: 'Feed', value: 35000 },
  { name: 'Labor', value: 25000 },
  { name: 'Veterinary', value: 15000 },
  { name: 'Equipment', value: 7500 },
  { name: 'Utilities', value: 5000 },
];

// Mock income sources
const incomeSources = [
  { name: 'Cattle Sales', value: 45000 },
  { name: 'Milk Sales', value: 35000 },
  { name: 'Breeding Services', value: 12000 },
  { name: 'Subsidies', value: 8000 },
];

// Mock profit and loss statement
const profitLossStatement = {
  income: {
    cattleSales: 45000,
    milkSales: 35000,
    breedingServices: 12000,
    subsidies: 8000,
    otherIncome: 5000,
    totalIncome: 105000
  },
  expenses: {
    feed: 35000,
    labor: 25000,
    veterinary: 15000,
    equipment: 7500,
    utilities: 5000,
    maintenance: 4500,
    insurance: 3000,
    otherExpenses: 2000,
    totalExpenses: 97000
  },
  grossProfit: 8000,
  taxes: 1600,
  netProfit: 6400
};

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const FinancialReports: React.FC = () => {
  const [reportType, setReportType] = useState('profitLoss');
  const [timeFrame, setTimeFrame] = useState('monthly');

  const handleReportTypeChange = (event: SelectChangeEvent) => {
    setReportType(event.target.value);
  };

  const handleTimeFrameChange = (event: SelectChangeEvent) => {
    setTimeFrame(event.target.value);
  };

  return (
    <Box p={4}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">Financial Reports</Typography>
        <Box>
          <CustomButton variant="outlined" startIcon={<PictureAsPdf />} sx={{ mr: 1 }}>
            Export PDF
          </CustomButton>
          <CustomButton variant="outlined" startIcon={<InsertDriveFile />}>
            Export CSV
          </CustomButton>
        </Box>
      </Box>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Report Type</InputLabel>
            <Select
              value={reportType}
              label="Report Type"
              onChange={handleReportTypeChange}
            >
              <MenuItem value="profitLoss">Profit & Loss Statement</MenuItem>
              <MenuItem value="incomeStatement">Income Statement</MenuItem>
              <MenuItem value="expenseReport">Expense Report</MenuItem>
              <MenuItem value="cashFlow">Cash Flow</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Time Frame</InputLabel>
            <Select
              value={timeFrame}
              label="Time Frame"
              onChange={handleTimeFrameChange}
            >
              <MenuItem value="monthly">Monthly</MenuItem>
              <MenuItem value="quarterly">Quarterly</MenuItem>
              <MenuItem value="yearly">Yearly</MenuItem>
              <MenuItem value="custom">Custom Range</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {reportType === 'profitLoss' && (
        <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', p: 3, mb: 3 }}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" fontWeight="bold">Profit & Loss Statement</Typography>
            <Typography variant="subtitle1">January - June 2023</Typography>
          </Box>

          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell colSpan={2}>
                    <Typography variant="h6">Income</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>Cattle Sales</TableCell>
                  <TableCell align="right">${profitLossStatement.income.cattleSales.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Milk Sales</TableCell>
                  <TableCell align="right">${profitLossStatement.income.milkSales.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Breeding Services</TableCell>
                  <TableCell align="right">${profitLossStatement.income.breedingServices.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Subsidies</TableCell>
                  <TableCell align="right">${profitLossStatement.income.subsidies.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Other Income</TableCell>
                  <TableCell align="right">${profitLossStatement.income.otherIncome.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow sx={{ bgcolor: 'primary.light' }}>
                  <TableCell><Typography fontWeight="bold">Total Income</Typography></TableCell>
                  <TableCell align="right"><Typography fontWeight="bold">${profitLossStatement.income.totalIncome.toLocaleString()}</Typography></TableCell>
                </TableRow>
              </TableBody>

              <TableHead>
                <TableRow>
                  <TableCell colSpan={2} sx={{ pt: 3 }}>
                    <Typography variant="h6">Expenses</Typography>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                <TableRow>
                  <TableCell>Feed</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.feed.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Labor</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.labor.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Veterinary</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.veterinary.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Equipment</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.equipment.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Utilities</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.utilities.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Maintenance</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.maintenance.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Insurance</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.insurance.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Other Expenses</TableCell>
                  <TableCell align="right">${profitLossStatement.expenses.otherExpenses.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow sx={{ bgcolor: 'error.light' }}>
                  <TableCell><Typography fontWeight="bold">Total Expenses</Typography></TableCell>
                  <TableCell align="right"><Typography fontWeight="bold">${profitLossStatement.expenses.totalExpenses.toLocaleString()}</Typography></TableCell>
                </TableRow>
              </TableBody>

              <TableBody>
                <TableRow sx={{ bgcolor: 'success.light' }}>
                  <TableCell><Typography fontWeight="bold">Gross Profit</Typography></TableCell>
                  <TableCell align="right"><Typography fontWeight="bold">${profitLossStatement.grossProfit.toLocaleString()}</Typography></TableCell>
                </TableRow>
                <TableRow>
                  <TableCell>Taxes</TableCell>
                  <TableCell align="right">${profitLossStatement.taxes.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow sx={{ bgcolor: 'success.main' }}>
                  <TableCell><Typography fontWeight="bold" color="white">Net Profit</Typography></TableCell>
                  <TableCell align="right"><Typography fontWeight="bold" color="white">${profitLossStatement.netProfit.toLocaleString()}</Typography></TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Monthly Financial Performance</Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                    <Legend />
                    <Bar dataKey="income" name="Income" fill="#8884d8" />
                    <Bar dataKey="expenses" name="Expenses" fill="#82ca9d" />
                    <Bar dataKey="profit" name="Profit" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Expense Distribution</Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={expenseCategories}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {expenseCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FinancialReports;
