import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  IconButton, 
  Badge, 
  Popover, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  Divider, 
  useTheme, 
  alpha, 
  Tooltip,
  <PERSON><PERSON>,
  Chip,
  Tabs,
  Tab
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  Notifications as NotificationsIcon,
  NotificationsActive as NotificationsActiveIcon,
  NotificationsOff as NotificationsOffIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Delete as DeleteIcon,
  DoneAll as DoneAllIcon,
  AccessTime as AccessTimeIcon
} from '@mui/icons-material';
import { CustomButton } from '../common';

// Notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
  link?: string;
  category: string;
}

// Props interface
interface NotificationCenterProps {
  initialNotifications?: Notification[];
  onNotificationUpdate?: (notifications: Notification[]) => void;
}

/**
 * NotificationCenter Component
 * 
 * A comprehensive notification system for the dashboard
 */
const NotificationCenter: React.FC<NotificationCenterProps> = ({ 
  initialNotifications = [],
  onNotificationUpdate
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  
  // State for notifications
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications);
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  
  // Calculate unread count
  const unreadCount = notifications.filter(notification => !notification.read).length;
  
  // Handle opening the notification popover
  const handleOpenNotifications = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  // Handle closing the notification popover
  const handleCloseNotifications = () => {
    setAnchorEl(null);
  };
  
  // Handle marking a notification as read
  const handleMarkAsRead = (id: string) => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
  };
  
  // Handle marking all notifications as read
  const handleMarkAllAsRead = () => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => ({ ...notification, read: true }))
    );
  };
  
  // Handle deleting a notification
  const handleDeleteNotification = (id: string) => {
    setNotifications(prevNotifications => 
      prevNotifications.filter(notification => notification.id !== id)
    );
  };
  
  // Handle clearing all notifications
  const handleClearAll = () => {
    setNotifications([]);
  };
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Notify parent component when notifications change
  useEffect(() => {
    if (onNotificationUpdate) {
      onNotificationUpdate(notifications);
    }
  }, [notifications, onNotificationUpdate]);
  
  // Get icon for notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'error': return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case 'warning': return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'info': default: return <InfoIcon sx={{ color: theme.palette.info.main }} />;
    }
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(timestamp).getTime();
    
    // Less than a minute
    if (diff < 60 * 1000) {
      return translate ? translate('notifications.just_now', { fallback: 'Just now' }) : 'Just now';
    }
    
    // Less than an hour
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000));
      return `${minutes} ${translate ? translate('notifications.minutes_ago', { fallback: 'minutes ago' }) : 'minutes ago'}`;
    }
    
    // Less than a day
    if (diff < 24 * 60 * 60 * 1000) {
      const hours = Math.floor(diff / (60 * 60 * 1000));
      return `${hours} ${translate ? translate('notifications.hours_ago', { fallback: 'hours ago' }) : 'hours ago'}`;
    }
    
    // Less than a week
    if (diff < 7 * 24 * 60 * 60 * 1000) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000));
      return `${days} ${translate ? translate('notifications.days_ago', { fallback: 'days ago' }) : 'days ago'}`;
    }
    
    // Otherwise, return the date
    return new Date(timestamp).toLocaleDateString();
  };
  
  // Filter notifications based on active tab
  const getFilteredNotifications = () => {
    if (activeTab === 0) {
      return notifications;
    } else if (activeTab === 1) {
      return notifications.filter(notification => !notification.read);
    } else {
      return notifications.filter(notification => notification.read);
    }
  };
  
  // Check if popover is open
  const open = Boolean(anchorEl);
  const id = open ? 'notification-popover' : undefined;
  
  return (
    <Box>
      {/* Notification button with badge */}
      <Tooltip title={translate ? translate('notifications.title', { fallback: 'Notifications' }) : 'Notifications'}>
        <IconButton
          onClick={handleOpenNotifications}
          size="large"
          sx={{
            position: 'relative',
            color: open ? themeColor.primary : 'inherit',
            transition: 'all 0.3s ease'
          }}
        >
          <Badge badgeContent={unreadCount} color="error" max={99}>
            {unreadCount > 0 ? <NotificationsActiveIcon /> : <NotificationsIcon />}
          </Badge>
        </IconButton>
      </Tooltip>
      
      {/* Notification popover */}
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleCloseNotifications}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 360,
            maxHeight: 500,
            overflow: 'hidden',
            borderRadius: 2,
            boxShadow: theme.shadows[8]
          }
        }}
      >
        {/* Notification header */}
        <Box sx={{ p: 2, bgcolor: alpha(themeColor.primary, 0.05) }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              {translate ? translate('notifications.title', { fallback: 'Notifications' }) : 'Notifications'}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              {unreadCount > 0 && (
                <Tooltip title={translate ? translate('notifications.mark_all_read', { fallback: 'Mark all as read' }) : 'Mark all as read'}>
                  <IconButton size="small" onClick={handleMarkAllAsRead}>
                    <DoneAllIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {notifications.length > 0 && (
                <Tooltip title={translate ? translate('notifications.clear_all', { fallback: 'Clear all' }) : 'Clear all'}>
                  <IconButton size="small" onClick={handleClearAll}>
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          </Box>
          
          {/* Notification tabs */}
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ mt: 1 }}
          >
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2">
                    {translate ? translate('notifications.all', { fallback: 'All' }) : 'All'}
                  </Typography>
                  <Chip 
                    label={notifications.length} 
                    size="small" 
                    sx={{ ml: 1, height: 20, minWidth: 20 }}
                  />
                </Box>
              } 
            />
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography variant="body2">
                    {translate ? translate('notifications.unread', { fallback: 'Unread' }) : 'Unread'}
                  </Typography>
                  {unreadCount > 0 && (
                    <Chip 
                      label={unreadCount} 
                      size="small" 
                      color="error" 
                      sx={{ ml: 1, height: 20, minWidth: 20 }}
                    />
                  )}
                </Box>
              } 
            />
            <Tab 
              label={
                <Typography variant="body2">
                  {translate ? translate('notifications.read', { fallback: 'Read' }) : 'Read'}
                </Typography>
              } 
            />
          </Tabs>
        </Box>
        
        <Divider />
        
        {/* Notification list */}
        <List sx={{ p: 0, maxHeight: 350, overflow: 'auto' }}>
          {getFilteredNotifications().length === 0 ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <NotificationsOffIcon sx={{ fontSize: 40, color: 'text.disabled', mb: 1 }} />
              <Typography color="text.secondary">
                {activeTab === 1
                  ? translate ? translate('notifications.no_unread', { fallback: 'No unread notifications' }) : 'No unread notifications'
                  : activeTab === 2
                    ? translate ? translate('notifications.no_read', { fallback: 'No read notifications' }) : 'No read notifications'
                    : translate ? translate('notifications.no_notifications', { fallback: 'No notifications' }) : 'No notifications'
                }
              </Typography>
            </Box>
          ) : (
            <AnimatePresence>
              {getFilteredNotifications().map((notification) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ListItem
                    sx={{
                      px: 2,
                      py: 1.5,
                      bgcolor: notification.read ? 'transparent' : alpha(themeColor.primary, 0.05),
                      '&:hover': {
                        bgcolor: alpha(themeColor.primary, 0.1)
                      },
                      transition: 'background-color 0.3s ease',
                      position: 'relative'
                    }}
                    secondaryAction={
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        {!notification.read && (
                          <Tooltip title={translate ? translate('notifications.mark_read', { fallback: 'Mark as read' }) : 'Mark as read'}>
                            <IconButton 
                              edge="end" 
                              size="small"
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <CheckCircleIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                        <Tooltip title={translate ? translate('notifications.delete', { fallback: 'Delete' }) : 'Delete'}>
                          <IconButton 
                            edge="end" 
                            size="small"
                            onClick={() => handleDeleteNotification(notification.id)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    }
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      {getNotificationIcon(notification.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography 
                          variant="body2" 
                          fontWeight={notification.read ? 'normal' : 'medium'}
                        >
                          {notification.title}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {notification.message}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <AccessTimeIcon sx={{ fontSize: 12, mr: 0.5, color: 'text.disabled' }} />
                            <Typography variant="caption" color="text.disabled">
                              {formatTimestamp(notification.timestamp)}
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                  <Divider />
                </motion.div>
              ))}
            </AnimatePresence>
          )}
        </List>
        
        {/* Notification footer */}
        {notifications.length > 0 && (
          <Box sx={{ p: 1.5, textAlign: 'center', borderTop: `1px solid ${theme.palette.divider}` }}>
            <Button 
              size="small" 
              fullWidth
              onClick={handleCloseNotifications}
            >
              {translate ? translate('notifications.view_all', { fallback: 'View All Notifications' }) : 'View All Notifications'}
            </Button>
          </Box>
        )}
      </Popover>
    </Box>
  );
};

export default NotificationCenter;
