import { useState, useEffect, useCallback } from 'react';
import { useAnimalData } from './useAnimalData';
import { useFinancialData } from './useFinancialData';
import { useHealthRecords } from './useHealthRecords';
import { useBreedingRecords } from './useBreedingRecords';
import { useFeedingData } from './useFeedingData';
import { businessAnalysisService } from '../services/businessAnalysisService';

// Types
export interface BusinessPrediction {
  id: string;
  name: string;
  description: string;
  timeframe: string;
  currentValue: string | number;
  predictedValue: string | number;
  confidenceLevel: number;
  category: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  suggestedAction?: string;
}

export interface BusinessInsight {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  source: string;
  date: string;
  actionable: boolean;
  suggestedAction?: string;
  relatedMetrics?: string[];
}

export interface BusinessPredictionsData {
  predictions: BusinessPrediction[];
  insights: BusinessInsight[];
}

/**
 * Custom hook for business predictions data
 * This hook provides business predictions and insights
 */
export const useBusinessPredictions = () => {
  const [predictionsData, setPredictionsData] = useState<BusinessPredictionsData>({
    predictions: [],
    insights: []
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Get data from other hooks
  const { animals, stats: animalStats, loading: animalsLoading } = useAnimalData();
  const { transactions, loading: financialLoading } = useFinancialData();
  const { healthRecords, loading: healthLoading } = useHealthRecords();
  const { breedingRecords, loading: breedingLoading } = useBreedingRecords();
  const { feedingRecords, loading: feedingLoading } = useFeedingData();

  // Generate mock data for business predictions
  const generateMockData = useCallback(() => {
    // Mock predictions
    const predictions: BusinessPrediction[] = [
      {
        id: 'prediction-1',
        name: 'Market Price Forecast',
        description: 'Based on historical trends and current market conditions, livestock prices are expected to increase by 15% in the next quarter.',
        timeframe: 'Next Quarter',
        currentValue: 'R 8,500 per head',
        predictedValue: 'R 9,775 per head',
        confidenceLevel: 0.85,
        category: 'Market',
        impact: 'high',
        actionable: true,
        suggestedAction: 'Consider delaying sales of market-ready animals to capitalize on price increases.'
      },
      {
        id: 'prediction-2',
        name: 'Feed Cost Projection',
        description: 'Feed costs are projected to rise due to global grain shortages and supply chain disruptions.',
        timeframe: '6 Months',
        currentValue: 'R 3,200 per ton',
        predictedValue: 'R 3,840 per ton',
        confidenceLevel: 0.75,
        category: 'Costs',
        impact: 'medium',
        actionable: true,
        suggestedAction: 'Secure long-term feed contracts now and explore alternative feed sources.'
      },
      {
        id: 'prediction-3',
        name: 'Disease Outbreak Risk',
        description: 'Increased risk of respiratory diseases in the coming winter season based on weather forecasts and historical patterns.',
        timeframe: '3-4 Months',
        currentValue: 'Low Risk',
        predictedValue: 'Moderate Risk',
        confidenceLevel: 0.7,
        category: 'Health',
        impact: 'medium',
        actionable: true,
        suggestedAction: 'Implement preventative health measures and ensure adequate shelter for vulnerable animals.'
      },
      {
        id: 'prediction-4',
        name: 'Breeding Success Rate',
        description: 'Current breeding program is projected to achieve higher success rates based on genetic improvements and management changes.',
        timeframe: 'Next Breeding Cycle',
        currentValue: '78% Success Rate',
        predictedValue: '85% Success Rate',
        confidenceLevel: 0.8,
        category: 'Breeding',
        impact: 'high',
        actionable: false
      },
      {
        id: 'prediction-5',
        name: 'Cash Flow Forecast',
        description: 'Seasonal variations and planned expenses will create a temporary cash flow constraint in the coming months.',
        timeframe: '2-3 Months',
        currentValue: 'Positive Cash Flow',
        predictedValue: 'Negative Cash Flow',
        confidenceLevel: 0.9,
        category: 'Financial',
        impact: 'high',
        actionable: true,
        suggestedAction: 'Arrange short-term financing options or adjust expense timing to manage the cash flow gap.'
      }
    ];

    // Mock insights
    const insights: BusinessInsight[] = [
      {
        id: 'insight-1',
        title: 'Feed Efficiency Opportunity',
        description: 'Analysis shows that your feed conversion ratio is 15% below industry benchmarks, representing a significant opportunity for improvement.',
        impact: 'high',
        category: 'Feed Management',
        source: 'Feed Data Analysis',
        date: '2023-05-15',
        actionable: true,
        suggestedAction: 'Review feed formulations and feeding practices. Consider consulting with a nutritionist to optimize rations.',
        relatedMetrics: ['Feed Conversion Ratio', 'Feed Cost per Kg Gain', 'Daily Feed Intake']
      },
      {
        id: 'insight-2',
        title: 'Breeding Cycle Optimization',
        description: 'Your breeding intervals are longer than optimal, reducing the number of offspring per year and limiting revenue potential.',
        impact: 'medium',
        category: 'Breeding',
        source: 'Breeding Records Analysis',
        date: '2023-05-10',
        actionable: true,
        suggestedAction: 'Implement estrus synchronization and improve heat detection methods to reduce days open.',
        relatedMetrics: ['Calving Interval', 'Days Open', 'Conception Rate']
      },
      {
        id: 'insight-3',
        title: 'Market Timing Advantage',
        description: 'Historical sales data shows that selling livestock in March-April yields 12% higher prices than your current selling pattern.',
        impact: 'high',
        category: 'Sales',
        source: 'Market Data Analysis',
        date: '2023-05-08',
        actionable: true,
        suggestedAction: 'Adjust production cycle to have more market-ready animals available during peak price periods.',
        relatedMetrics: ['Average Sale Price', 'Seasonal Price Variation', 'Profit Margin']
      },
      {
        id: 'insight-4',
        title: 'Preventative Health ROI',
        description: 'Farms with similar profiles that invest 5% more in preventative health measures see 20% lower treatment costs and 8% better growth rates.',
        impact: 'medium',
        category: 'Health',
        source: 'Industry Benchmark Comparison',
        date: '2023-05-05',
        actionable: true,
        suggestedAction: 'Increase preventative health budget and implement a more comprehensive vaccination program.',
        relatedMetrics: ['Health Cost per Animal', 'Treatment Frequency', 'Mortality Rate']
      },
      {
        id: 'insight-5',
        title: 'Labor Efficiency Gap',
        description: 'Your operation uses 25% more labor hours per animal than top-performing farms of similar size and type.',
        impact: 'medium',
        category: 'Operations',
        source: 'Operational Efficiency Analysis',
        date: '2023-05-01',
        actionable: true,
        suggestedAction: 'Audit daily workflows and implement labor-saving technologies in high-effort areas.',
        relatedMetrics: ['Labor Hours per Animal', 'Labor Cost Percentage', 'Tasks per Worker']
      }
    ];

    return {
      predictions,
      insights
    };
  }, []);

  // Fetch data
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch predictions from service
      const predictions = await businessAnalysisService.getPredictions();

      // Fetch insights from service
      const insights = await businessAnalysisService.getInsights();

      // Set predictions data
      setPredictionsData({
        predictions: predictions.map(p => ({
          id: p.id,
          name: p.name,
          description: `${p.name} is projected to change from ${p.currentValue} to ${p.predictedValue} with ${p.confidence * 100}% confidence.`,
          timeframe: p.timeframe,
          currentValue: p.currentValue,
          predictedValue: p.predictedValue,
          confidenceLevel: p.confidence,
          category: p.category,
          impact: p.confidence > 0.8 ? 'high' : p.confidence > 0.6 ? 'medium' : 'low',
          actionable: true
        })),
        insights: insights.map(i => ({
          id: i.id,
          title: i.title,
          description: i.description,
          impact: i.impact,
          category: i.category,
          source: 'Data Analysis',
          date: new Date().toISOString().split('T')[0],
          actionable: i.actionable,
          suggestedAction: i.suggestedAction
        }))
      });

      setError(null);
    } catch (err) {
      console.error('Error fetching business predictions data:', err);
      setError(err as Error);

      // Fallback to mock data
      const mockData = generateMockData();
      setPredictionsData(mockData);
    } finally {
      setLoading(false);
    }
  }, [generateMockData]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData, animalsLoading, financialLoading, healthLoading, breedingLoading, feedingLoading]);

  return {
    ...predictionsData,
    loading,
    error,
    refetch: fetchData
  };
};

export default useBusinessPredictions;
