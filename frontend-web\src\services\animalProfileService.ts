import { dataIntegrationService } from './dataIntegrationService';
import { Animal } from '../types/animal';

/**
 * Service for animal profile management
 * Handles animal profiles with proper image handling
 */
class AnimalProfileService {
  /**
   * Get all animal profiles
   * @param limit Maximum number of profiles to return (default: 5)
   * @returns Animal profiles
   */
  async getAllProfiles(limit: number = 5): Promise<Animal[]> {
    try {
      const animals = await dataIntegrationService.getDocuments('animals', {}, limit);
      return animals.map(this.mapToAnimal);
    } catch (error) {
      console.error('Error getting animal profiles:', error);
      throw error;
    }
  }

  /**
   * Get animal profile by ID
   * @param id Animal ID
   * @returns Animal profile
   */
  async getProfileById(id: string): Promise<Animal | null> {
    try {
      const animal = await dataIntegrationService.getDocument('animals', id);
      return animal ? this.mapToAnimal(animal) : null;
    } catch (error) {
      console.error('Error getting animal profile:', error);
      throw error;
    }
  }

  /**
   * Create animal profile
   * @param animal Animal data
   * @param imageFile Image file (optional)
   * @returns Created animal profile
   */
  async createProfile(animal: Omit<Animal, 'id'>, imageFile?: File): Promise<Animal> {
    try {
      // Handle image upload if provided
      let imageUrl = '';
      if (imageFile) {
        imageUrl = await this.uploadImage(imageFile);
      }

      // Create animal with image URL
      const animalData = {
        ...animal,
        imageUrl
      };

      const createdAnimal = await dataIntegrationService.createDocument('animals', animalData);
      return this.mapToAnimal(createdAnimal);
    } catch (error) {
      console.error('Error creating animal profile:', error);
      throw error;
    }
  }

  /**
   * Update animal profile
   * @param id Animal ID
   * @param animal Animal data
   * @param imageFile Image file (optional)
   * @returns Updated animal profile
   */
  async updateProfile(id: string, animal: Partial<Animal>, imageFile?: File): Promise<Animal> {
    try {
      // Handle image upload if provided
      let imageUrl = animal.imageUrl;
      if (imageFile) {
        imageUrl = await this.uploadImage(imageFile);
      }

      // Update animal with image URL
      const animalData = {
        ...animal,
        imageUrl
      };

      const updatedAnimal = await dataIntegrationService.updateDocument('animals', id, animalData);
      return this.mapToAnimal(updatedAnimal);
    } catch (error) {
      console.error('Error updating animal profile:', error);
      throw error;
    }
  }

  /**
   * Delete animal profile
   * @param id Animal ID
   * @returns Deletion result
   */
  async deleteProfile(id: string): Promise<boolean> {
    try {
      return await dataIntegrationService.deleteDocument('animals', id);
    } catch (error) {
      console.error('Error deleting animal profile:', error);
      throw error;
    }
  }

  /**
   * Upload animal image
   * @param file Image file
   * @returns Image URL
   */
  private async uploadImage(file: File): Promise<string> {
    try {
      // In a real implementation, this would upload the image to a storage service
      // For now, we'll create a data URL
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          resolve(reader.result as string);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  }

  /**
   * Map MongoDB animal document to Animal type
   * @param doc MongoDB animal document
   * @returns Animal object
   */
  private mapToAnimal(doc: any): Animal {
    return {
      id: doc._id.toString(),
      tagNumber: doc.tagNumber || '',
      name: doc.name || '',
      type: doc.type || '',
      breed: doc.breed || '',
      gender: doc.gender || '',
      birthDate: doc.birthDate || '',
      weight: doc.weight || 0,
      status: doc.status || 'Active',
      healthStatus: doc.healthStatus || 'healthy',
      location: doc.location || '',
      purchaseDate: doc.purchaseDate || '',
      purchasePrice: doc.purchasePrice || 0,
      notes: doc.notes || '',
      parentId: doc.parentId || '',
      species: doc.species || doc.type || '',
      imageUrl: doc.imageUrl || ''
    };
  }
}

// Export singleton instance
export const animalProfileService = new AnimalProfileService();
