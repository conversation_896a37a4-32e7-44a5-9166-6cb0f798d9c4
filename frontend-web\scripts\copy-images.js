const fs = require('fs');
const path = require('path');

// Source directory (UXUI LiveStock folder)
const sourceDir = 'C:\\Users\\<USER>\\OneDrive\\Desktop\\UXUI LiveStock';

// Destination directories
const destDirs = {
  animals: path.join(__dirname, '..', 'public', 'images', 'modules', 'animals'),
  health: path.join(__dirname, '..', 'public', 'images', 'modules', 'health'),
  feeding: path.join(__dirname, '..', 'public', 'images', 'modules', 'feeding'),
  commercial: path.join(__dirname, '..', 'public', 'images', 'modules', 'commercial'),
  rfid: path.join(__dirname, '..', 'public', 'images', 'modules', 'rfid'),
  login: path.join(__dirname, '..', 'public', 'images', 'login'),
  dashboard: path.join(__dirname, '..', 'public', 'images', 'dashboard'),
};

// Mapping of source files to destination files
const fileMapping = [
  // Animals
  { src: 'Cows 1.jpeg', dest: path.join(destDirs.animals, 'cattle-1.jpeg') },
  { src: 'cows 2.avif', dest: path.join(destDirs.animals, 'cattle-2.avif') },
  { src: 'cows 3.jpeg', dest: path.join(destDirs.animals, 'cattle-3.jpeg') },
  { src: 'cows 4.jpeg', dest: path.join(destDirs.animals, 'cattle-4.jpeg') },
  { src: 'cows 5.avif', dest: path.join(destDirs.animals, 'cattle-5.avif') },
  
  // Health
  { src: 'health Managemnt.png', dest: path.join(destDirs.health, 'health-main.png') },
  { src: 'veteian health 1.jpg', dest: path.join(destDirs.health, 'veterinary-1.jpg') },
  { src: 'veteian health 2.jpg', dest: path.join(destDirs.health, 'veterinary-2.jpg') },
  { src: 'veteian health 3.jpg', dest: path.join(destDirs.health, 'veterinary-3.jpg') },
  { src: 'Veteran health.jpg', dest: path.join(destDirs.health, 'veterinary-main.jpg') },
  
  // Feeding
  { src: 'Feed managemnt.jpeg', dest: path.join(destDirs.feeding, 'feed-main.jpeg') },
  { src: 'feed management 2.jpeg', dest: path.join(destDirs.feeding, 'feed-2.jpeg') },
  { src: 'feed managemnt sheep.jpeg', dest: path.join(destDirs.feeding, 'feed-sheep.jpeg') },
  
  // Commercial
  { src: 'commercial products.webp', dest: path.join(destDirs.commercial, 'commercial-main.webp') },
  { src: 'commercial products 1.web.jpeg', dest: path.join(destDirs.commercial, 'commercial-1.jpeg') },
  { src: 'commercial products 2.web.jpeg', dest: path.join(destDirs.commercial, 'commercial-2.jpeg') },
  { src: 'commercial products 3.web.jpeg', dest: path.join(destDirs.commercial, 'commercial-3.jpeg') },
  
  // RFID
  { src: 'RFID 1.webp', dest: path.join(destDirs.rfid, 'rfid-1.webp') },
  { src: 'RFID 2.jpg', dest: path.join(destDirs.rfid, 'rfid-2.jpg') },
  { src: 'FRID 3.jpg', dest: path.join(destDirs.rfid, 'rfid-3.jpg') },
  { src: 'FRID 4.jpg', dest: path.join(destDirs.rfid, 'rfid-4.jpg') },
  
  // Login
  { src: 'log in page.jpeg', dest: path.join(destDirs.login, 'login-bg.jpeg') },
  
  // Dashboard
  { src: 'Maindash.jpg', dest: path.join(destDirs.dashboard, 'main-dashboard.jpg') },
  
  // Breeding
  { src: 'breending and pregnancy.png', dest: path.join(destDirs.animals, 'breeding-main.png') },
];

// Ensure all destination directories exist
Object.values(destDirs).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Copy files
let successCount = 0;
let errorCount = 0;

fileMapping.forEach(({ src, dest }) => {
  const sourcePath = path.join(sourceDir, src);
  
  try {
    if (fs.existsSync(sourcePath)) {
      fs.copyFileSync(sourcePath, dest);
      console.log(`Copied: ${src} -> ${dest}`);
      successCount++;
    } else {
      console.error(`Source file not found: ${sourcePath}`);
      errorCount++;
    }
  } catch (error) {
    console.error(`Error copying ${src}: ${error.message}`);
    errorCount++;
  }
});

console.log(`\nCopy complete: ${successCount} files copied, ${errorCount} errors`);
