import React, { useState, useEffect, useRef } from 'react';
import { Box, CircularProgress } from '@mui/material';

// Map container style
const containerStyle = {
  width: '100%',
  height: '100%',
  borderRadius: '8px'
};

// Default center (South Africa)
const defaultCenter = {
  lat: -28.4792625,
  lng: 24.6727135
};

interface MapLocation {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  title: string;
  info?: string;
  icon?: string;
}

interface GoogleMapComponentProps {
  locations: MapLocation[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string | number;
}

const GoogleMapComponent: React.FC<GoogleMapComponentProps> = ({
  locations = [],
  center = defaultCenter,
  zoom = 7,
  height = '400px'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const mapRef = useRef<any>(null);
  const mapContainerRef = useRef<HTMLDivElement>(null);

  // Load Google Maps script
  useEffect(() => {
    const loadGoogleMapsScript = () => {
      if (window.google) {
        setIsLoaded(true);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyA9JZ_PcJQ4H7mmXWnUXVsCbDEEBgGEPWs&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setIsLoaded(true);
      document.head.appendChild(script);
    };

    loadGoogleMapsScript();

    return () => {
      // Cleanup if needed
    };
  }, []);

  // Initialize map when script is loaded
  useEffect(() => {
    if (!isLoaded || !mapContainerRef.current) return;

    const map = new window.google.maps.Map(mapContainerRef.current, {
      center,
      zoom,
      fullscreenControl: true,
      mapTypeControl: true,
      streetViewControl: false,
      mapTypeId: 'hybrid',
      mapTypeControlOptions: {
        position: window.google.maps.ControlPosition.TOP_RIGHT,
      }
    });

    mapRef.current = map;

    // Create markers
    const markers = locations.map(location => {
      const marker = new window.google.maps.Marker({
        position: location.position,
        map,
        title: location.title,
        icon: location.icon
      });

      marker.addListener('click', () => {
        setSelectedLocation(location);
      });

      return marker;
    });

    // Fit bounds if there are locations
    if (locations.length > 0) {
      const bounds = new window.google.maps.LatLngBounds();
      locations.forEach(location => {
        bounds.extend(location.position);
      });
      map.fitBounds(bounds);

      const currentZoom = map.getZoom();
      if (currentZoom !== undefined && currentZoom > 15) {
        map.setZoom(15);
      }
    }

    // Create info window for selected location
    let infoWindow: any = null;

    // Watch for selected location changes
    if (selectedLocation) {
      if (infoWindow) {
        infoWindow.close();
      }

      infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 8px; max-width: 200px;">
            <h3 style="margin: 0 0 8px 0; font-size: 16px;">${selectedLocation.title}</h3>
            ${selectedLocation.info ? `<p style="margin: 0; font-size: 14px;">${selectedLocation.info}</p>` : ''}
          </div>
        `,
        position: selectedLocation.position
      });

      infoWindow.open(map);
      infoWindow.addListener('closeclick', () => {
        setSelectedLocation(null);
      });
    }

    return () => {
      // Clean up markers
      markers.forEach(marker => marker.setMap(null));
      if (infoWindow) infoWindow.close();
    };
  }, [isLoaded, locations, center, zoom, selectedLocation]);

  if (!isLoaded) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          bgcolor: 'background.paper',
          borderRadius: '8px'
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ height, width: '100%', position: 'relative' }}>
      <div
        ref={mapContainerRef}
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '8px'
        }}
      />
    </Box>
  );
};

export default GoogleMapComponent;
