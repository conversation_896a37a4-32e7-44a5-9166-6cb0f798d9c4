/**
 * Simple MongoDB Check Script
 */

const { MongoClient } = require('mongodb');

// MongoDB connection string
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

async function checkMongoDB() {
  const client = new MongoClient(uri);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections`);

    for (const collection of collections) {
      console.log(`- ${collection.name}`);
    }
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

checkMongoDB();
