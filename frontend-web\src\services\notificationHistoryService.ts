import { NotificationType, NotificationPriority } from './notificationService';

/**
 * Interface for a notification history entry
 */
export interface NotificationHistoryEntry {
  id: string;
  timestamp: Date;
  animalId: string;
  animalName: string;
  notificationType: NotificationType;
  recipient: {
    name: string;
    contact: string; // Phone number or email
  };
  message: string;
  priority: NotificationPriority;
  status: 'success' | 'failed';
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Service for managing notification history
 */
export const notificationHistoryService = {
  history: [] as NotificationHistoryEntry[],
  
  /**
   * Add a notification to the history
   * @param entry Notification history entry
   * @returns The added entry
   */
  addEntry: (entry: Omit<NotificationHistoryEntry, 'id'>): NotificationHistoryEntry => {
    const newEntry: NotificationHistoryEntry = {
      ...entry,
      id: `history-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    };
    
    notificationHistoryService.history.unshift(newEntry); // Add to the beginning of the array
    
    // Limit history size (optional)
    if (notificationHistoryService.history.length > 1000) {
      notificationHistoryService.history = notificationHistoryService.history.slice(0, 1000);
    }
    
    return newEntry;
  },
  
  /**
   * Get all notification history entries
   * @param limit Maximum number of entries to return
   * @param offset Offset for pagination
   * @returns Array of notification history entries
   */
  getHistory: (limit?: number, offset = 0): NotificationHistoryEntry[] => {
    if (limit) {
      return notificationHistoryService.history.slice(offset, offset + limit);
    }
    return [...notificationHistoryService.history];
  },
  
  /**
   * Get notification history for a specific animal
   * @param animalId Animal ID
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the animal
   */
  getHistoryForAnimal: (animalId: string, limit?: number): NotificationHistoryEntry[] => {
    const animalHistory = notificationHistoryService.history.filter(entry => entry.animalId === animalId);
    if (limit) {
      return animalHistory.slice(0, limit);
    }
    return animalHistory;
  },
  
  /**
   * Get notification history for a specific recipient
   * @param recipientName Recipient name
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the recipient
   */
  getHistoryForRecipient: (recipientName: string, limit?: number): NotificationHistoryEntry[] => {
    const recipientHistory = notificationHistoryService.history.filter(
      entry => entry.recipient.name === recipientName
    );
    if (limit) {
      return recipientHistory.slice(0, limit);
    }
    return recipientHistory;
  },
  
  /**
   * Get notification history for a specific notification type
   * @param type Notification type
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the notification type
   */
  getHistoryByType: (type: NotificationType, limit?: number): NotificationHistoryEntry[] => {
    const typeHistory = notificationHistoryService.history.filter(
      entry => entry.notificationType === type
    );
    if (limit) {
      return typeHistory.slice(0, limit);
    }
    return typeHistory;
  },
  
  /**
   * Get notification history for a specific priority
   * @param priority Notification priority
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the priority
   */
  getHistoryByPriority: (priority: NotificationPriority, limit?: number): NotificationHistoryEntry[] => {
    const priorityHistory = notificationHistoryService.history.filter(
      entry => entry.priority === priority
    );
    if (limit) {
      return priorityHistory.slice(0, limit);
    }
    return priorityHistory;
  },
  
  /**
   * Get notification history for a specific status
   * @param status Notification status
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the status
   */
  getHistoryByStatus: (status: 'success' | 'failed', limit?: number): NotificationHistoryEntry[] => {
    const statusHistory = notificationHistoryService.history.filter(
      entry => entry.status === status
    );
    if (limit) {
      return statusHistory.slice(0, limit);
    }
    return statusHistory;
  },
  
  /**
   * Get notification history for a date range
   * @param startDate Start date
   * @param endDate End date
   * @param limit Maximum number of entries to return
   * @returns Array of notification history entries for the date range
   */
  getHistoryByDateRange: (startDate: Date, endDate: Date, limit?: number): NotificationHistoryEntry[] => {
    const rangeHistory = notificationHistoryService.history.filter(
      entry => entry.timestamp >= startDate && entry.timestamp <= endDate
    );
    if (limit) {
      return rangeHistory.slice(0, limit);
    }
    return rangeHistory;
  },
  
  /**
   * Clear all notification history
   */
  clearHistory: (): void => {
    notificationHistoryService.history = [];
  }
};

export default notificationHistoryService;
