/**
 * Inject Financial Data Script
 * 
 * This script injects realistic financial records into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');
const { animals } = require('./inject-animal-data');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Transactions to inject
const transactions = [
  {
    _id: new ObjectId(),
    transactionDate: new Date('2024-01-15'),
    type: 'expense',
    category: 'feed',
    subcategory: 'hay',
    amount: 5000,
    currency: 'ZAR',
    description: 'Purchase of alfalfa hay',
    paymentMethod: 'bank transfer',
    reference: 'INV-2024-001',
    vendor: 'Farm Feed Suppliers',
    status: 'completed',
    relatedTo: {
      type: 'feeding',
      id: null
    },
    attachments: [],
    notes: 'Monthly hay purchase',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    transactionDate: new Date('2024-02-10'),
    type: 'expense',
    category: 'veterinary',
    subcategory: 'medication',
    amount: 2500,
    currency: 'ZAR',
    description: 'Veterinary supplies and medications',
    paymentMethod: 'credit card',
    reference: 'INV-2024-023',
    vendor: 'Rural Veterinary Services',
    status: 'completed',
    relatedTo: {
      type: 'health',
      id: null
    },
    attachments: [],
    notes: 'Quarterly veterinary supplies',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    transactionDate: new Date('2024-03-05'),
    type: 'expense',
    category: 'equipment',
    subcategory: 'maintenance',
    amount: 3500,
    currency: 'ZAR',
    description: 'Repair of feeding equipment',
    paymentMethod: 'cash',
    reference: 'REC-2024-015',
    vendor: 'Farm Equipment Services',
    status: 'completed',
    relatedTo: {
      type: 'equipment',
      id: null
    },
    attachments: [],
    notes: 'Repair of automatic feeders',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    transactionDate: new Date('2024-04-20'),
    type: 'income',
    category: 'sales',
    subcategory: 'livestock',
    amount: 15000,
    currency: 'ZAR',
    description: 'Sale of cattle',
    paymentMethod: 'bank transfer',
    reference: 'SALE-2024-008',
    vendor: 'Local Livestock Auction',
    status: 'completed',
    relatedTo: {
      type: 'animal',
      id: null
    },
    attachments: [],
    notes: 'Sale of 2 cattle at auction',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    transactionDate: new Date('2024-05-01'),
    type: 'income',
    category: 'sales',
    subcategory: 'products',
    amount: 8500,
    currency: 'ZAR',
    description: 'Sale of wool',
    paymentMethod: 'bank transfer',
    reference: 'SALE-2024-012',
    vendor: 'Wool Buyers Co-op',
    status: 'completed',
    relatedTo: {
      type: 'other',
      id: null
    },
    attachments: [],
    notes: 'Sale of wool from sheep shearing',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Budgets to inject
const budgets = [
  {
    _id: new ObjectId(),
    name: 'Feed Budget 2024',
    description: 'Annual budget for animal feed',
    year: 2024,
    category: 'feed',
    plannedAmount: 60000,
    currency: 'ZAR',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    status: 'active',
    items: [
      {
        name: 'Hay',
        plannedAmount: 30000,
        actualAmount: 15000,
        notes: 'Monthly purchases of hay'
      },
      {
        name: 'Concentrates',
        plannedAmount: 20000,
        actualAmount: 8000,
        notes: 'Dairy and sheep concentrates'
      },
      {
        name: 'Supplements',
        plannedAmount: 10000,
        actualAmount: 4000,
        notes: 'Mineral and vitamin supplements'
      }
    ],
    notes: 'Annual feed budget with monthly tracking',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Veterinary Budget 2024',
    description: 'Annual budget for veterinary expenses',
    year: 2024,
    category: 'veterinary',
    plannedAmount: 30000,
    currency: 'ZAR',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    status: 'active',
    items: [
      {
        name: 'Routine Care',
        plannedAmount: 15000,
        actualAmount: 6000,
        notes: 'Regular check-ups and vaccinations'
      },
      {
        name: 'Medications',
        plannedAmount: 10000,
        actualAmount: 4000,
        notes: 'Antibiotics, dewormers, and other medications'
      },
      {
        name: 'Emergency Care',
        plannedAmount: 5000,
        actualAmount: 0,
        notes: 'Reserve for emergency veterinary services'
      }
    ],
    notes: 'Annual veterinary budget with quarterly reviews',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Assets to inject
const assets = [
  {
    _id: new ObjectId(),
    name: 'Tractor - John Deere 5075E',
    category: 'equipment',
    subcategory: 'machinery',
    description: 'Utility tractor for farm operations',
    acquisitionDate: new Date('2022-06-15'),
    acquisitionCost: 450000,
    currentValue: 400000,
    currency: 'ZAR',
    location: 'Equipment Shed',
    status: 'active',
    serialNumber: 'JD5075E-123456',
    manufacturer: 'John Deere',
    model: '5075E',
    purchaseDocument: 'INV-2022-045',
    warrantyExpiration: new Date('2025-06-15'),
    maintenanceSchedule: {
      lastMaintenance: new Date('2023-12-10'),
      nextMaintenance: new Date('2024-06-10'),
      interval: 6, // months
      notes: 'Regular service every 6 months'
    },
    depreciationMethod: 'straight-line',
    depreciationRate: 10, // percent per year
    usefulLife: 10, // years
    salvageValue: 50000,
    notes: 'Main farm tractor, used for multiple operations',
    attachments: [],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Feed Mixer - Vertical TMR',
    category: 'equipment',
    subcategory: 'feeding',
    description: 'Vertical total mixed ration feed mixer',
    acquisitionDate: new Date('2023-03-20'),
    acquisitionCost: 180000,
    currentValue: 170000,
    currency: 'ZAR',
    location: 'Equipment Shed',
    status: 'active',
    serialNumber: 'TMR-987654',
    manufacturer: 'Farm Equipment Co.',
    model: 'Vertical TMR 2000',
    purchaseDocument: 'INV-2023-018',
    warrantyExpiration: new Date('2025-03-20'),
    maintenanceSchedule: {
      lastMaintenance: new Date('2024-01-15'),
      nextMaintenance: new Date('2024-07-15'),
      interval: 6, // months
      notes: 'Regular service every 6 months'
    },
    depreciationMethod: 'straight-line',
    depreciationRate: 15, // percent per year
    usefulLife: 8, // years
    salvageValue: 20000,
    notes: 'Used for preparing mixed feed rations',
    attachments: [],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'North Paddock Land',
    category: 'property',
    subcategory: 'land',
    description: 'Grazing land in north section of farm',
    acquisitionDate: new Date('2015-05-10'),
    acquisitionCost: 2000000,
    currentValue: 2500000,
    currency: 'ZAR',
    location: 'North Farm',
    status: 'active',
    size: {
      value: 20,
      unit: 'hectares'
    },
    propertyDetails: {
      titleDeed: 'TD-2015-123',
      propertyTax: 15000,
      zoning: 'Agricultural'
    },
    notes: 'Prime grazing land with water access',
    attachments: [],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject financial data
async function injectFinancialData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const transactionsCollection = db.collection('transactions');
    const budgetsCollection = db.collection('budgets');
    const assetsCollection = db.collection('assets');
    const usersCollection = db.collection('users');

    // Get a user ID for the creator field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const creatorId = adminUser ? adminUser._id : new ObjectId();

    // Insert or update transactions
    for (const transaction of transactions) {
      // Set the creator to an actual user
      transaction.createdBy = creatorId;
      
      const existingTransaction = await transactionsCollection.findOne({ 
        transactionDate: transaction.transactionDate,
        amount: transaction.amount,
        description: transaction.description
      });

      if (existingTransaction) {
        logger.info(`Updating transaction: ${transaction.description}`);
        await transactionsCollection.updateOne(
          { _id: existingTransaction._id },
          { $set: { ...transaction, _id: existingTransaction._id } }
        );
      } else {
        logger.info(`Inserting new transaction: ${transaction.description}`);
        await transactionsCollection.insertOne(transaction);
      }
    }

    // Insert or update budgets
    for (const budget of budgets) {
      // Set the creator to an actual user
      budget.createdBy = creatorId;
      
      const existingBudget = await budgetsCollection.findOne({ 
        name: budget.name,
        year: budget.year
      });

      if (existingBudget) {
        logger.info(`Updating budget: ${budget.name}`);
        await budgetsCollection.updateOne(
          { _id: existingBudget._id },
          { $set: { ...budget, _id: existingBudget._id } }
        );
      } else {
        logger.info(`Inserting new budget: ${budget.name}`);
        await budgetsCollection.insertOne(budget);
      }
    }

    // Insert or update assets
    for (const asset of assets) {
      // Set the creator to an actual user
      asset.createdBy = creatorId;
      
      const existingAsset = await assetsCollection.findOne({ 
        name: asset.name
      });

      if (existingAsset) {
        logger.info(`Updating asset: ${asset.name}`);
        await assetsCollection.updateOne(
          { _id: existingAsset._id },
          { $set: { ...asset, _id: existingAsset._id } }
        );
      } else {
        logger.info(`Inserting new asset: ${asset.name}`);
        await assetsCollection.insertOne(asset);
      }
    }

    logger.info('Financial data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting financial data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectFinancialData()
  .then(() => {
    logger.info('Financial data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in financial data injection script:', error);
  });

module.exports = { transactions, budgets, assets };
