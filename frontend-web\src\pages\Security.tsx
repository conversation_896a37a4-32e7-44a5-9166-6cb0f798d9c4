import React from 'react';
import { motion } from 'framer-motion';

const Security: React.FC = () => {
  // Add console.log to verify component mounting
  console.log('Security component rendering');

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">Security Management</h1>
      {/* Remove motion wrapper temporarily for testing */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">Active Incidents</h2>
          </div>
          <div className="p-4">
            Test Content
          </div>
        </div>
      </div>
    </div>
  );
};

export default Security;
