import React, { useState, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  useTheme,
  alpha,
  Container,
  Tabs,
  Tab,
  Divider,
  Paper
} from '@mui/material';
import { motion } from 'framer-motion';
import {
  TrendingUp,
  Assessment,
  Lightbulb,
  ShowChart,
  Refresh,
  Timeline,
  InsertChart,
  Insights
} from '@mui/icons-material';
import { useLanguage } from '../../contexts/LanguageContext';
import { useThemeContext } from '../../contexts/ThemeContext';
import { useBusinessPredictions } from '../../hooks/useBusinessPredictions';

// Import custom components
import BusinessAnalyticsButton from '../../components/analytics/BusinessAnalyticsButton';
import BusinessAnalyticsCard from '../../components/analytics/BusinessAnalyticsCard';
import BusinessPredictionCard from '../../components/analytics/BusinessPredictionCard';
import BusinessInsightCard from '../../components/analytics/BusinessInsightCard';

const BusinessPredictions: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { themeColor } = useThemeContext();
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);

  // Business predictions hooks
  const { predictions, insights, loading: predictionsLoading } = useBusinessPredictions();

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: string) => {
    setActiveTab(newValue);
  };

  // Handle refresh data
  const handleRefreshData = useCallback(() => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Filter predictions by category
  const filterPredictionsByCategory = (category: string) => {
    if (category === 'all') {
      return predictions;
    }
    return predictions.filter(prediction => prediction.category.toLowerCase() === category.toLowerCase());
  };

  // Filter insights by category
  const filterInsightsByCategory = (category: string) => {
    if (category === 'all') {
      return insights;
    }
    return insights.filter(insight => insight.category.toLowerCase().includes(category.toLowerCase()));
  };

  // Render overview section
  const renderOverview = () => {
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.predictive_insights')}
        </Typography>
        
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <BusinessPredictionCard
              prediction={predictions[0]}
              icon={<TrendingUp />}
              delay={0.1}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <BusinessPredictionCard
              prediction={predictions[1]}
              icon={<TrendingUp />}
              delay={0.2}
            />
          </Grid>
        </Grid>
        
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.business_insights')}
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <BusinessInsightCard
              insight={insights[0]}
              delay={0.3}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <BusinessInsightCard
              insight={insights[1]}
              delay={0.4}
            />
          </Grid>
        </Grid>
      </Box>
    );
  };

  // Render predictions section
  const renderPredictions = () => {
    const filteredPredictions = filterPredictionsByCategory(activeTab === 'predictions' ? 'all' : activeTab);
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.predictive_insights')}
        </Typography>
        
        <Grid container spacing={3}>
          {filteredPredictions.map((prediction, index) => (
            <Grid item xs={12} md={6} key={prediction.id}>
              <BusinessPredictionCard
                prediction={prediction}
                icon={<TrendingUp />}
                delay={0.1 + (index * 0.05)}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // Render insights section
  const renderInsights = () => {
    const filteredInsights = filterInsightsByCategory(activeTab === 'insights' ? 'all' : activeTab);
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          {translate('dashboard.business_insights')}
        </Typography>
        
        <Grid container spacing={3}>
          {filteredInsights.map((insight, index) => (
            <Grid item xs={12} md={6} key={insight.id}>
              <BusinessInsightCard
                insight={insight}
                delay={0.1 + (index * 0.05)}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'predictions':
      case 'market':
      case 'financial':
      case 'health':
      case 'breeding':
        return renderPredictions();
      case 'insights':
      case 'feed':
      case 'operations':
      case 'sales':
        return renderInsights();
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <Box sx={{ mb: 4 }}>
          <motion.div variants={itemVariants}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {translate('dashboard.predictive_analytics')}
            </Typography>
            <Typography variant="body1" color="text.secondary">
              {translate('dashboard.predictive_analytics_description')}
            </Typography>
          </motion.div>
        </Box>

        <Box sx={{ mb: 4 }}>
          <motion.div variants={itemVariants}>
            <Paper 
              elevation={0} 
              sx={{ 
                borderRadius: 2,
                overflow: 'hidden',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                background: alpha(theme.palette.background.paper, 0.8),
                backdropFilter: 'blur(10px)'
              }}
            >
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  px: 2,
                  pt: 2,
                  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  '& .MuiTab-root': {
                    minWidth: 'auto',
                    px: 3,
                    py: 1.5,
                    borderRadius: 2,
                    fontWeight: 'medium',
                    textTransform: 'none',
                    fontSize: '0.9rem',
                    transition: 'all 0.2s',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.05)
                    }
                  },
                  '& .Mui-selected': {
                    color: `${theme.palette.primary.main} !important`,
                    fontWeight: 'bold',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1)
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none'
                  }
                }}
              >
                <Tab value="overview" label={translate('dashboard.overview')} icon={<Assessment />} iconPosition="start" />
                <Tab value="predictions" label={translate('dashboard.predictions')} icon={<TrendingUp />} iconPosition="start" />
                <Tab value="insights" label={translate('dashboard.insights')} icon={<Lightbulb />} iconPosition="start" />
                <Tab value="market" label={translate('dashboard.market')} icon={<ShowChart />} iconPosition="start" />
                <Tab value="financial" label={translate('dashboard.financial')} icon={<InsertChart />} iconPosition="start" />
                <Tab value="health" label={translate('dashboard.health')} icon={<Timeline />} iconPosition="start" />
                <Tab value="breeding" label={translate('dashboard.breeding')} icon={<Insights />} iconPosition="start" />
              </Tabs>
              <Box sx={{ p: 3 }}>
                {isLoading || predictionsLoading ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        border: `3px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                        borderTop: `3px solid ${theme.palette.primary.main}`,
                        animation: 'spin 1s linear infinite',
                        '@keyframes spin': {
                          '0%': { transform: 'rotate(0deg)' },
                          '100%': { transform: 'rotate(360deg)' }
                        }
                      }}
                    />
                  </Box>
                ) : (
                  renderContent()
                )}
              </Box>
            </Paper>
          </motion.div>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <motion.div variants={itemVariants}>
            <BusinessAnalyticsButton
              variant="contained"
              color="primary"
              onClick={handleRefreshData}
              startIcon={<Refresh />}
              disabled={isLoading}
            >
              {isLoading ? translate('common.refreshing') : translate('common.refresh')}
            </BusinessAnalyticsButton>
          </motion.div>
        </Box>
      </motion.div>
    </Container>
  );
};

export default BusinessPredictions;
