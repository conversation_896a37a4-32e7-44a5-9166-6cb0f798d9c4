import React, { useState } from 'react';
import { Box, Container, Typography, Paper, Grid, Divider, Alert, Alert<PERSON>itle, CircularProgress, Snackbar } from '@mui/material';
import { Storage, CloudSync, Sync, Storage as Database, Refresh } from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, LoadingOverlay , CustomButton } from '../../components/common';
import DataMigration from '../../components/admin/DataMigration';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { tryCatch, getErrorMessage } from '../../utils/errorHandling';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const DatabaseSettings: React.FC = () => {
  const { isConnected, isLoading, error, syncMockData, refreshStats, mongoStats } = useMongoDb();
  const { translate } = useLanguage();
  const [syncLoading, setSyncLoading] = useState(false);
  const [alert, setAlert] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  const handleSyncMockData = async () => {
    setSyncLoading(true);

    await tryCatch(
      async () => {
        const success = await syncMockData();
        if (success) {
          await refreshStats();
          setAlert({
            open: true,
            message: translate('common.success') + ': ' + 'Mock data successfully synced to MongoDB',
            severity: 'success'
          });
        } else {
          setAlert({
            open: true,
            message: 'Failed to sync mock data to MongoDB',
            severity: 'error'
          });
        }
      },
      (error) => {
        setAlert({
          open: true,
          message: getErrorMessage(error),
          severity: 'error'
        });
      }
    );

    setSyncLoading(false);
  };

  const handleRefreshStats = async () => {
    try {
      await refreshStats();
      setAlert({
        open: true,
        message: 'Database statistics refreshed successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: getErrorMessage(error as Error),
        severity: 'error'
      });
    }
  };

  return (
    <LoadingOverlay
      loading={isLoading || syncLoading}
      message={syncLoading ? 'Syncing data to MongoDB...' : translate('common.loading')}
      transparent
    >
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <ModuleHeader
          title="Database Settings"
          subtitle="Manage your database configuration and data"
          icon={<Storage fontSize="large" />}
        />

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <Paper
                  elevation={3}
                  sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    mb: 3
                  }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CloudSync fontSize="large" color="primary" sx={{ mr: 2 }} />
                  <Typography variant="h5" component="h2">
                    MongoDB Connection Status
                  </Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                {isLoading ? (
                  <Alert severity="info">
                    <AlertTitle>Connecting</AlertTitle>
                    Establishing connection to MongoDB...
                  </Alert>
                ) : error ? (
                  <Alert severity="error">
                    <AlertTitle>Connection Error</AlertTitle>
                    {error.message}
                  </Alert>
                ) : isConnected ? (
                  <Alert severity="success">
                    <AlertTitle>Connected</AlertTitle>
                    Successfully connected to MongoDB database
                  </Alert>
                ) : (
                  <Alert severity="warning">
                    <AlertTitle>Disconnected</AlertTitle>
                    Not connected to MongoDB database
                  </Alert>
                )}

                <Typography variant="body1" sx={{ mt: 2 }}>
                  Database: {process.env.REACT_APP_MONGODB_DB_NAME || 'ampd_livestock'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Connection URI: {process.env.REACT_APP_MONGODB_URI ? '******' : 'Not configured'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  API URL: {process.env.REACT_APP_API_URL || 'http://localhost:3001/api'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Mock Data: {process.env.REACT_APP_USE_MOCK_DATA === 'true' ? 'Enabled' : 'Disabled'}
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="subtitle2" color="primary">
                      Database Statistics:
                    </Typography>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={handleRefreshStats}
                      disabled={!isConnected || isLoading}
                      startIcon={isLoading ? <CircularProgress size={16} /> : <Refresh />}
                    >
                      Refresh
                    </CustomButton>
                  </Box>
                  {mongoStats ? (
                    <>
                      <Typography variant="body2" color="text.secondary">
                        Collections: {mongoStats.collections?.length || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Documents: {mongoStats.totalDocuments || 0}
                      </Typography>

                      {/* Animal Statistics */}
                      {mongoStats.totalAnimals > 0 && (
                        <Box sx={{ mt: 1 }}>
                          <Typography variant="body2" color="text.secondary" fontWeight="bold">
                            Animal Statistics:
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Total Animals: {mongoStats.totalAnimals || 0}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Active: {mongoStats.activeAnimals || 0} | Healthy: {mongoStats.healthyAnimals || 0}
                          </Typography>
                        </Box>
                      )}

                      {/* Record Statistics */}
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" color="text.secondary" fontWeight="bold">
                          Record Statistics:
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Health Records: {mongoStats.totalHealthRecords || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Breeding Records: {mongoStats.totalBreedingRecords || 0}
                        </Typography>
                      </Box>
                    </>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      Statistics not available. {isConnected ? 'Refresh to load statistics.' : 'Connect to MongoDB to view statistics.'}
                    </Typography>
                  )}
                </Box>
              </Paper>
            </motion.div>
          </Grid>

            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <Paper
                  elevation={3}
                  sx={{
                    p: 3,
                    display: 'flex',
                    flexDirection: 'column',
                    mb: 3
                  }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Sync fontSize="large" color="primary" sx={{ mr: 2 }} />
                  <Typography variant="h5" component="h2">
                    Sync Mock Data to MongoDB
                  </Typography>
                </Box>
                <Divider sx={{ mb: 2 }} />

                <Typography variant="body1" paragraph>
                  This will sync all mock data from the application to your MongoDB database.
                  Use this feature to populate your database with sample data for testing and development.
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 2 }}>
                  <CustomButton
                    variant="contained"
                    color="primary"
                    startIcon={syncLoading ? <CircularProgress size={20} color="inherit" /> : <Database />}
                    onClick={handleSyncMockData}
                    disabled={syncLoading || !isConnected}
                    sx={{ mr: 2 }}
                  >
                    {syncLoading ? 'Syncing...' : 'Sync Mock Data to MongoDB'}
                  </CustomButton>
                </Box>
              </Paper>
            </motion.div>
          </Grid>

            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <DataMigration />
              </motion.div>
          </Grid>
          </Grid>
        </motion.div>

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert({ ...alert, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setAlert({ ...alert, open: false })}
            severity={alert.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
      </Container>
    </LoadingOverlay>
  );
};

export default DatabaseSettings;
