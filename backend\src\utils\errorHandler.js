const ono = require('@jsdevtools/ono');
const { BaseError } = require('./errors');

// Create custom error types with ono
const validationError = (message, originalError) => 
  ono({ statusCode: 400 }, originalError, message);

const notFoundError = (message, originalError) => 
  ono({ statusCode: 404 }, originalError, message);

const serverError = (message, originalError) => 
  ono({ statusCode: 500 }, originalError, message);

module.exports = {
  validationError,
  notFoundError,
  serverError
};