import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Typography, Breadcrumbs, Link, Tabs, Tab, Paper, useTheme, alpha } from '@mui/material';
import {
  <PERSON>B<PERSON>,
  <PERSON><PERSON>hart,
  Timeline,
  LocalHospital,
  TrendingUp,
  AttachMoney
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import ReportGenerator from '../../components/modules/reports/ReportGenerator';
import { ReportType } from '../../services/reportService';
import {  ModuleContainer, withSubModuleTranslation , CustomButton } from '../../components/common';

interface ReportPageProps {
  defaultType?: ReportType;
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const ReportPage: React.FC<ReportPageProps> = ({
  defaultType = 'analysis',
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const { type = defaultType } = useParams<{ type: ReportType }>();
  const [activeTab, setActiveTab] = React.useState<number>(0);
  const navigate = useNavigate();
  const theme = useTheme();

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getReportTitle = () => {
    switch (type) {
      case 'analysis': return translateModuleField ? translateModuleField('analysis_reports', 'Analysis Reports') : 'Analysis Reports';
      case 'performance': return translateModuleField ? translateModuleField('performance_reports', 'Performance Reports') : 'Performance Reports';
      case 'health': return translateModuleField ? translateModuleField('health_reports', 'Health Reports') : 'Health Reports';
      case 'market': return translateModuleField ? translateModuleField('market_reports', 'Market Reports') : 'Market Reports';
      case 'financial': return translateModuleField ? translateModuleField('financial_reports', 'Financial Reports') : 'Financial Reports';
      case 'custom': return translateModuleField ? translateModuleField('custom_reports', 'Custom Reports') : 'Custom Reports';
      default: return translateModuleField ? translateModuleField('reports', 'Reports') : 'Reports';
    }
  };

  const getReportIcon = () => {
    switch (type) {
      case 'analysis': return <BarChart />;
      case 'performance': return <Timeline />;
      case 'health': return <LocalHospital />;
      case 'market': return <TrendingUp />;
      case 'financial': return <AttachMoney />;
      case 'custom': return <BarChart />;
      default: return <BarChart />;
    }
  };

  const getReportDescription = () => {
    switch (type) {
      case 'analysis':
        return translateModuleField ? translateModuleField('analysis_description', 'Comprehensive analysis of your livestock data with key insights and trends.') : 'Comprehensive analysis of your livestock data with key insights and trends.';
      case 'performance':
        return translateModuleField ? translateModuleField('performance_description', 'Track and measure the performance metrics of your livestock over time.') : 'Track and measure the performance metrics of your livestock over time.';
      case 'health':
        return translateModuleField ? translateModuleField('health_description', 'Monitor health statistics, vaccination records, and medical treatments.') : 'Monitor health statistics, vaccination records, and medical treatments.';
      case 'market':
        return translateModuleField ? translateModuleField('market_description', 'Analyze market trends, pricing data, and sales opportunities.') : 'Analyze market trends, pricing data, and sales opportunities.';
      case 'financial':
        return translateModuleField ? translateModuleField('financial_description', 'Review financial statements, revenue streams, and expense breakdowns.') : 'Review financial statements, revenue streams, and expense breakdowns.';
      case 'custom':
        return translateModuleField ? translateModuleField('custom_description', 'Create customized reports tailored to your specific needs.') : 'Create customized reports tailored to your specific needs.';
      default:
        return translateModuleField ? translateModuleField('reports_description', 'Generate and download reports from your livestock management system.') : 'Generate and download reports from your livestock management system.';
    }
  };

  return (
    <ModuleContainer module="reports">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <CustomButton
              startIcon={<ArrowBack />}
              onClick={() => navigate('/reports')}
              sx={{ mr: 2 }}
            >
              {translate ? translate('common.back', { fallback: "Back" }) : "Back"}
            </CustomButton>
            <Breadcrumbs aria-label="breadcrumb">
              <Link
                color="inherit"
                href="/reports"
                underline="hover"
              >
                {translateModuleField ? translateModuleField('reports', 'Reports') : 'Reports'}
              </Link>
              <Typography color="text.primary">{getReportTitle()}</Typography>
            </Breadcrumbs>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Box
              sx={{
                mr: 2,
                p: 1,
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main
              }}
            >
              {getReportIcon()}
            </Box>
            <Typography variant="h4" component="h1">
              {getReportTitle()}
            </Typography>
          </Box>

          <Typography variant="body1" color="text.secondary" paragraph>
            {getReportDescription()}
          </Typography>
        </Box>

        {/* Tabs */}
        <Paper
          sx={{
            mb: 4,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2
              }
            }}
          >
            <Tab label={translateModuleField ? translateModuleField('generate_report', 'Generate Report') : 'Generate Report'} />
            <Tab label={translateModuleField ? translateModuleField('saved_reports', 'Saved Reports') : 'Saved Reports'} />
            <Tab label={translateModuleField ? translateModuleField('scheduled_reports', 'Scheduled Reports') : 'Scheduled Reports'} />
          </Tabs>

          {/* Tab Content */}
          <Box sx={{ p: 0 }}>
            {/* Generate Report Tab */}
            {activeTab === 0 && (
              <Box sx={{ p: 3 }}>
                <ReportGenerator defaultType={type as ReportType} />
              </Box>
            )}

            {/* Saved Reports Tab */}
            {activeTab === 1 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {translateModuleField ? translateModuleField('saved_reports', 'Saved Reports') : 'Saved Reports'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {translateModuleField ? translateModuleField('saved_reports_description', 'View and download your previously generated reports.') : 'View and download your previously generated reports.'}
                </Typography>

                <Box
                  sx={{
                    mt: 3,
                    p: 4,
                    border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.primary.main, 0.02),
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="body1">
                    {translateModuleField ? translateModuleField('saved_reports_empty', 'Your saved reports will appear here after you generate them.') : 'Your saved reports will appear here after you generate them.'}
                  </Typography>
                </Box>
              </Box>
            )}

            {/* Scheduled Reports Tab */}
            {activeTab === 2 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  {translateModuleField ? translateModuleField('scheduled_reports', 'Scheduled Reports') : 'Scheduled Reports'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {translateModuleField ? translateModuleField('scheduled_reports_description', 'Set up automatic report generation on a schedule.') : 'Set up automatic report generation on a schedule.'}
                </Typography>

                <Box
                  sx={{
                    mt: 3,
                    p: 4,
                    border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.primary.main, 0.02),
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="body1">
                    {translateModuleField ? translateModuleField('scheduled_reports_info', 'You can schedule reports to be generated automatically on a daily, weekly, or monthly basis.') : 'You can schedule reports to be generated automatically on a daily, weekly, or monthly basis.'}
                  </Typography>
                  <CustomButton
                    variant="outlined"
                    sx={{ mt: 2 }}
                    disabled
                  >
                    {translateModuleField ? translateModuleField('schedule_new_report', 'Schedule New Report') : 'Schedule New Report'}
                  </CustomButton>
                </Box>
              </Box>
            )}
          </Box>
        </Paper>
      </motion.div>
    </ModuleContainer>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(ReportPage, 'reports', 'page');
