const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const notificationController = require('../controllers/notificationController');

// Get all notifications with pagination and filtering
router.get('/', authenticateToken, notificationController.getAllNotifications);

// Get notification by ID
router.get('/:id', authenticateToken, notificationController.getNotificationById);

// Send a notification
router.post('/', authenticateToken, notificationController.sendNotification);

// Mark notification as read
router.put('/:id/read', authenticateToken, notificationController.markAsRead);

module.exports = router;