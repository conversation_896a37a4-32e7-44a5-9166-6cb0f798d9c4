/**
 * Financial Dashboard
 *
 * This component displays the financial dashboard with key metrics,
 * charts, and recent transactions using standardized components.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Grid, Typography, Card, CardContent, IconButton, Paper, Table, TableContainer, TableHead, TableRow, TableCell, TableBody, Chip, CircularProgress, useTheme, alpha } from '@mui/material';
import {
  Add,
  MonetizationOn,
  TrendingUp,
  TrendingDown,
  Receipt,
  AttachMoney,
  AccountBalance,
  MoneyOff,
  Search,
  FilterList,
  MoreVert,
  ArrowForward
} from '../../utils/iconImports';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Legend,
  AreaChart,
  Area,
  BarChart,
  Bar,
  Tooltip
} from 'recharts';
import { motion } from 'framer-motion';
import { formatCurrency } from '../../utils/formatters';
import {
  StandardDashboard,
  AnimatedBackgroundCard,
  withSubModuleTranslation
, CustomButton } from '../../components/common';
import { financialService } from '../../services/moduleService';
import useModuleData from '../../hooks/useModuleData';
import { getCardStyle } from '../../utils/cardStyles';

// Mock financial data (in South African Rand)
const mockFinancialStats = {
  totalRevenue: 1250000, // R1.25 million
  totalExpenses: 875000, // R875,000
  netProfit: 375000, // R375,000
  profitMargin: 30,
  pendingInvoices: 125000, // R125,000
  upcomingExpenses: 85000, // R85,000
  cashOnHand: 450000, // R450,000
  revenueGrowth: 12.5,
  expensesByCategory: {
    feed: 350000, // R350,000
    labor: 250000, // R250,000
    veterinary: 150000, // R150,000
    equipment: 75000, // R75,000
    utilities: 50000 // R50,000
  },
  revenueBySource: {
    sales: 850000, // R850,000
    services: 250000, // R250,000
    subsidies: 150000 // R150,000
  }
};

// Mock monthly financial data
const monthlyData = [
  { name: 'Jan', income: 180000, expenses: 150000, profit: 30000 },
  { name: 'Feb', income: 195000, expenses: 145000, profit: 50000 },
  { name: 'Mar', income: 210000, expenses: 160000, profit: 50000 },
  { name: 'Apr', income: 205000, expenses: 155000, profit: 50000 },
  { name: 'May', income: 225000, expenses: 165000, profit: 60000 },
  { name: 'Jun', income: 235000, expenses: 170000, profit: 65000 },
  { name: 'Jul', income: 250000, expenses: 180000, profit: 70000 },
  { name: 'Aug', income: 265000, expenses: 190000, profit: 75000 },
  { name: 'Sep', income: 280000, expenses: 200000, profit: 80000 },
  { name: 'Oct', income: 295000, expenses: 210000, profit: 85000 },
  { name: 'Nov', income: 310000, expenses: 220000, profit: 90000 },
  { name: 'Dec', income: 325000, expenses: 230000, profit: 95000 },
];

// Prepare data for expense categories chart
const categoryData = [
  { name: 'Feed', value: 350000 },
  { name: 'Labor', value: 250000 },
  { name: 'Veterinary', value: 150000 },
  { name: 'Equipment', value: 75000 },
  { name: 'Utilities', value: 50000 },
];

// Mock recent transactions
const mockTransactions = [
  { id: 'TR001', date: '2023-06-15', description: 'Bonsmara Cattle Sale', amount: 125000, type: 'income', category: 'Sales', status: 'completed' },
  { id: 'TR002', date: '2023-06-12', description: 'Lucerne Purchase', amount: 35000, type: 'expense', category: 'Feed', status: 'completed' },
  { id: 'TR003', date: '2023-06-10', description: 'Veterinary Services', amount: 12000, type: 'expense', category: 'Veterinary', status: 'completed' },
  { id: 'TR004', date: '2023-06-08', description: 'Dorper Sheep Sale', amount: 45000, type: 'income', category: 'Sales', status: 'completed' },
  { id: 'TR005', date: '2023-06-05', description: 'Dipping Equipment', amount: 8500, type: 'expense', category: 'Equipment', status: 'completed' },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

// Animation variants for staggered animations
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

interface FinancialDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const FinancialDashboard: React.FC<FinancialDashboardProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean; content: string }>({
    show: false,
    content: ''
  });

  // Prepare data for charts
  const expenseCategoryData = [
    { name: 'Feed', value: mockFinancialStats.expensesByCategory.feed },
    { name: 'Labor', value: mockFinancialStats.expensesByCategory.labor },
    { name: 'Veterinary', value: mockFinancialStats.expensesByCategory.veterinary },
    { name: 'Equipment', value: mockFinancialStats.expensesByCategory.equipment },
    { name: 'Utilities', value: mockFinancialStats.expensesByCategory.utilities }
  ];

  const revenueSourceData = [
    { name: 'Sales', value: mockFinancialStats.revenueBySource.sales },
    { name: 'Services', value: mockFinancialStats.revenueBySource.services },
    { name: 'Subsidies', value: mockFinancialStats.revenueBySource.subsidies }
  ];

  const monthlyFinancialData = monthlyData.map(item => ({
    month: item.name,
    revenue: item.income,
    expenses: item.expenses,
    profit: item.profit
  }));

  // Dashboard stats for the StandardDashboard component
  const dashboardStats = [
    {
      label: translateModuleField ? translateModuleField('total_revenue', 'Total Revenue') : 'Total Revenue',
      value: formatCurrency(mockFinancialStats.totalRevenue),
      icon: <MonetizationOn />,
      color: theme.palette.primary.main,
      trend: {
        value: mockFinancialStats.revenueGrowth,
        isPositive: true,
        label: 'since last month'
      }
    },
    {
      label: translateModuleField ? translateModuleField('total_expenses', 'Total Expenses') : 'Total Expenses',
      value: formatCurrency(mockFinancialStats.totalExpenses),
      icon: <TrendingDown />,
      color: theme.palette.error.main,
      trend: {
        value: 3.2,
        isPositive: false,
        label: 'since last month'
      }
    },
    {
      label: translateModuleField ? translateModuleField('net_profit', 'Net Profit') : 'Net Profit',
      value: formatCurrency(mockFinancialStats.netProfit),
      icon: <TrendingUp />,
      color: theme.palette.success.main,
      trend: {
        value: 8.5,
        isPositive: true,
        label: 'since last month'
      }
    },
    {
      label: translateModuleField ? translateModuleField('profit_margin', 'Profit Margin') : 'Profit Margin',
      value: `${mockFinancialStats.profitMargin}%`,
      icon: <AttachMoney />,
      color: theme.palette.info.main,
      trend: {
        value: 2.1,
        isPositive: true,
        label: 'since last month'
      }
    }
  ];

  // Dashboard actions for the StandardDashboard component
  const dashboardActions = [
    {
      label: translateModuleField ? translateModuleField('add_transaction', 'Add Transaction') : 'Add Transaction',
      icon: <Add />,
      onClick: () => navigate('/financial/transactions/new'),
      color: theme.palette.primary.main
    },
    {
      label: translateModuleField ? translateModuleField('view_reports', 'View Reports') : 'View Reports',
      icon: <Receipt />,
      onClick: () => navigate('/financial/reports'),
      color: theme.palette.secondary.main
    },
    {
      label: translateModuleField ? translateModuleField('manage_budgets', 'Manage Budgets') : 'Manage Budgets',
      icon: <AttachMoney />,
      onClick: () => navigate('/financial/budget'),
      color: theme.palette.info.main
    }
  ];

  return (
    <StandardDashboard
      title={translateSubModule ? translateSubModule('title', "Financial Dashboard") : "Financial Dashboard"}
      subtitle={translateSubModule ? translateSubModule('subtitle', "Monitor financial performance, revenue, expenses, and profitability metrics") : "Monitor financial performance, revenue, expenses, and profitability metrics"}
      icon={<MonetizationOn />}
      stats={dashboardStats}
      actions={dashboardActions}
      isLoading={isLoading}
      loadingMessage={translateModuleField ? translateModuleField('loading', "Loading financial data...") : "Loading financial data..."}
      onRefresh={() => console.log('Refreshing financial data')}
      module="financial"
    >

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Financial Overview Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={3}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title={translateModuleField ? translateModuleField('total_revenue', "Total Revenue") : "Total Revenue"}
                subtitle={formatCurrency(mockFinancialStats.totalRevenue)}
                module="financial"
                uniqueId="financial-revenue"
                icon={<MonetizationOn />}
                height={160}
                accentColor={theme.palette.primary.main}
                backgroundImage="/images/modules/financial/revenue.jpg"
                overlay="gradient"
              >
                <Box mt={1}>
                  <Typography variant="body2" fontWeight="medium" color="success.main">
                    +{mockFinancialStats.revenueGrowth}% since last month
                  </Typography>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={3}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title={translateModuleField ? translateModuleField('total_expenses', "Total Expenses") : "Total Expenses"}
                subtitle={formatCurrency(mockFinancialStats.totalExpenses)}
                module="financial"
                uniqueId="financial-expenses"
                icon={<TrendingDown />}
                height={160}
                accentColor={theme.palette.error.main}
                backgroundImage="/images/modules/financial/expenses.jpg"
                overlay="gradient"
              >
                <Box mt={1}>
                  <Typography variant="body2" fontWeight="medium" color="error.main">
                    -3.2% since last month
                  </Typography>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={3}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title={translateModuleField ? translateModuleField('net_profit', "Net Profit") : "Net Profit"}
                subtitle={formatCurrency(mockFinancialStats.netProfit)}
                module="financial"
                uniqueId="financial-profit"
                icon={<TrendingUp />}
                height={160}
                accentColor={theme.palette.success.main}
                backgroundImage="/images/modules/financial/profit.jpg"
                overlay="gradient"
              >
                <Box mt={1}>
                  <Typography variant="body2" fontWeight="medium" color="success.main">
                    +8.5% since last month
                  </Typography>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={3}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title={translateModuleField ? translateModuleField('cash_on_hand', "Cash on Hand") : "Cash on Hand"}
                subtitle={formatCurrency(mockFinancialStats.cashOnHand)}
                module="financial"
                uniqueId="financial-cash"
                icon={<AccountBalance />}
                height={160}
                accentColor={theme.palette.info.main}
                backgroundImage="/images/modules/financial/cash.jpg"
                overlay="gradient"
              >
                <Box mt={1}>
                  <Typography variant="body2" fontWeight="medium" color="info.main">
                    +5.3% since last month
                  </Typography>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>
        </Grid>

      {/* Charts and Lists Section */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('expenses_by_category', "Expenses by Category") : "Expenses by Category"}
              subtitle={translateModuleField ? translateModuleField('expenses_by_category_desc', "Distribution of expenses by category") : "Distribution of expenses by category"}
              module="financial"
              uniqueId="financial-expenses-chart"
              icon={<TrendingDown />}
              height={400}
              accentColor={theme.palette.primary.main}
              backgroundImage="/images/modules/financial/expenses-chart.jpg"
              overlay="gradient"
            >
              <Box height={300} mt={2}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={expenseCategoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                      outerRadius={100}
                      fill={theme.palette.primary.main}
                      dataKey="value"
                      onClick={(data) => {
                        setSelectedCategory(data.name);
                        setSelectedChart('expenses');
                        setChartTooltip({
                          show: true,
                          content: `${data.name}: ${formatCurrency(data.value)} (${(data.percent ? (data.percent * 100).toFixed(1) : 0)}%)`
                        });
                      }}
                    >
                      {expenseCategoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={6}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('revenue_by_source', "Revenue by Source") : "Revenue by Source"}
              subtitle={translateModuleField ? translateModuleField('revenue_by_source_desc', "Distribution of revenue by source") : "Distribution of revenue by source"}
              module="financial"
              uniqueId="financial-revenue-chart"
              icon={<TrendingUp />}
              height={400}
              accentColor={theme.palette.success.main}
              backgroundImage="/images/modules/financial/revenue-chart.jpg"
              overlay="gradient"
            >
              <Box height={300} mt={2}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={revenueSourceData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                      outerRadius={100}
                      fill={theme.palette.success.main}
                      dataKey="value"
                      onClick={(data) => {
                        setSelectedCategory(data.name);
                        setSelectedChart('revenue');
                        setChartTooltip({
                          show: true,
                          content: `${data.name}: ${formatCurrency(data.value)} (${(data.percent ? (data.percent * 100).toFixed(1) : 0)}%)`
                        });
                      }}
                    >
                      {revenueSourceData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>
      </Grid>

      {/* Monthly Financial Performance Chart */}
      <motion.div variants={itemVariants}>
        <AnimatedBackgroundCard
          title={translateModuleField ? translateModuleField('monthly_financial_performance', "Monthly Financial Performance") : "Monthly Financial Performance"}
          subtitle={translateModuleField ? translateModuleField('monthly_financial_performance_desc', "Track revenue, expenses, and profit over time") : "Track revenue, expenses, and profit over time"}
          module="financial"
          uniqueId="financial-monthly"
          icon={<MonetizationOn />}
          height={450}
          accentColor={theme.palette.primary.main}
          backgroundImage="/images/modules/financial/monthly-performance.jpg"
          overlay="gradient"
        >
          <Box height={350} mt={2}>
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={monthlyFinancialData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                onClick={(data) => {
                  if (data && data.activePayload && data.activePayload.length > 0) {
                    const payload = data.activePayload[0].payload;
                    setSelectedChart('monthly');
                    setChartTooltip({
                      show: true,
                      content: `${payload.month}: Revenue: ${formatCurrency(payload.revenue)}, Expenses: ${formatCurrency(payload.expenses)}, Profit: ${formatCurrency(payload.profit)}`
                    });
                  }
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis tickFormatter={(value) => `R${(value / 1000)}k`} />
                <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                <Legend />
                <Area type="monotone" dataKey="revenue" stackId="1" stroke={theme.palette.primary.main} fill={alpha(theme.palette.primary.main, 0.6)} />
                <Area type="monotone" dataKey="expenses" stackId="2" stroke={theme.palette.error.main} fill={alpha(theme.palette.error.main, 0.6)} />
                <Area type="monotone" dataKey="profit" stackId="3" stroke={theme.palette.success.main} fill={alpha(theme.palette.success.main, 0.6)} />
              </AreaChart>
            </ResponsiveContainer>
          </Box>
        </AnimatedBackgroundCard>
      </motion.div>

      {/* Chart Interaction Feedback */}
      {chartTooltip.show && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{
            ...getCardStyle('financial', theme),
            mb: 4,
            overflow: 'hidden'
          }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" fontWeight="bold" color="white">
                  {selectedChart === 'expenses' ? 'Expense Category Details' :
                   selectedChart === 'revenue' ? 'Revenue Source Details' :
                   'Financial Performance Details'}
                </Typography>
                <IconButton size="small" onClick={() => setChartTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>
                  <MoreVert />
                </IconButton>
              </Box>
              <Typography variant="body1" mt={1} color="white">
                {chartTooltip.content}
              </Typography>
              {selectedCategory && (
                <Box mt={2}>
                  <CustomButton
                    variant="outlined"
                    size="small"
                    sx={{ color: 'white', borderColor: 'rgba(255,255,255,0.5)', '&:hover': { borderColor: 'white' } }}
                    onClick={() => navigate(`/financial/${selectedChart === 'expenses' ? 'expenses' : 'revenue'}?category=${selectedCategory}`)}
                  >
                    View {selectedCategory} Details
                  </CustomButton>
                  <Typography variant="body2" color="rgba(255,255,255,0.8)" mt={1}>
                    {selectedChart === 'expenses' && 'Analyze expense patterns to identify cost-saving opportunities.'}
                    {selectedChart === 'revenue' && 'Understand revenue streams to focus on high-performing areas.'}
                    {selectedChart === 'monthly' && 'Monthly trends help forecast future financial performance.'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Budget vs. Actual Comparison */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('budget_vs_actual', "Budget vs. Actual") : "Budget vs. Actual"}
              subtitle={translateModuleField ? translateModuleField('budget_vs_actual_desc', "Compare budgeted amounts with actual spending") : "Compare budgeted amounts with actual spending"}
              module="financial"
              uniqueId="financial-budget"
              icon={<AttachMoney />}
              height={450}
              accentColor={theme.palette.info.main}
              backgroundImage="/images/modules/financial/budget.jpg"
              overlay="gradient"
            >
              <Box height={350} mt={2}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={[
                      { category: 'Feed', budget: 45000, actual: 42500 },
                      { category: 'Veterinary', budget: 25000, actual: 28700 },
                      { category: 'Labor', budget: 35000, actual: 34200 },
                      { category: 'Equipment', budget: 15000, actual: 18900 },
                      { category: 'Utilities', budget: 8000, actual: 7600 },
                      { category: 'Marketing', budget: 5000, actual: 4200 },
                      { category: 'Maintenance', budget: 12000, actual: 13500 }
                    ]}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis tickFormatter={(value) => `R${(value / 1000)}k`} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                    <Bar dataKey="budget" name="Budget" fill={alpha(theme.palette.info.main, 0.8)} />
                    <Bar dataKey="actual" name="Actual" fill={alpha(theme.palette.warning.main, 0.8)} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
              <Box display="flex" justifyContent="center" mt={2}>
                <CustomButton
                  variant="outlined"
                  color="inherit"
                  onClick={() => navigate('/financial/budget')}
                  sx={{ color: 'white', borderColor: 'white' }}
                >
                  {translateModuleField ? translateModuleField('view_budget_details', "View Budget Details") : "View Budget Details"}
                </CustomButton>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>
      </Grid>

      {/* Financial Forecast */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('financial_forecast', "Financial Forecast") : "Financial Forecast"}
              subtitle={translateModuleField ? translateModuleField('financial_forecast_desc', "Projected revenue and expenses for the next 6 months") : "Projected revenue and expenses for the next 6 months"}
              module="financial"
              uniqueId="financial-forecast"
              icon={<TrendingUp />}
              height={400}
              accentColor={theme.palette.secondary.main}
              backgroundImage="/images/modules/financial/forecast.jpg"
              overlay="gradient"
            >
              <Box height={300} mt={2}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={[
                      { month: 'Jul', revenue: 125000, expenses: 98000, profit: 27000 },
                      { month: 'Aug', revenue: 132000, expenses: 101000, profit: 31000 },
                      { month: 'Sep', revenue: 141000, expenses: 105000, profit: 36000 },
                      { month: 'Oct', revenue: 138000, expenses: 108000, profit: 30000 },
                      { month: 'Nov', revenue: 152000, expenses: 112000, profit: 40000 },
                      { month: 'Dec', revenue: 168000, expenses: 118000, profit: 50000 }
                    ]}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `R${(value / 1000)}k`} />
                    <Tooltip formatter={(value) => formatCurrency(Number(value))} />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" name="Revenue" stroke={theme.palette.primary.main} strokeWidth={2} dot={{ r: 4 }} />
                    <Line type="monotone" dataKey="expenses" name="Expenses" stroke={theme.palette.error.main} strokeWidth={2} dot={{ r: 4 }} />
                    <Line type="monotone" dataKey="profit" name="Profit" stroke={theme.palette.success.main} strokeWidth={2} dot={{ r: 4 }} />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
              <Box display="flex" justifyContent="center" mt={2}>
                <CustomButton
                  variant="outlined"
                  color="inherit"
                  onClick={() => navigate('/financial/forecast')}
                  sx={{ color: 'white', borderColor: 'white' }}
                >
                  {translateModuleField ? translateModuleField('view_detailed_forecast', "View Detailed Forecast") : "View Detailed Forecast"}
                </CustomButton>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={6}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('financial_health', "Financial Health") : "Financial Health"}
              subtitle={translateModuleField ? translateModuleField('financial_health_desc', "Key financial ratios and indicators") : "Key financial ratios and indicators"}
              module="financial"
              uniqueId="financial-health"
              icon={<MonetizationOn />}
              height={400}
              accentColor={theme.palette.info.main}
              backgroundImage="/images/modules/financial/financial-health.jpg"
              overlay="gradient"
            >
              <Grid container spacing={2} mt={1}>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.1), backdropFilter: 'blur(10px)' }}>
                    <Typography variant="subtitle2" color="white" fontWeight="bold">Profit Margin</Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress
                          variant="determinate"
                          value={18.5}
                          size={60}
                          thickness={5}
                          sx={{ color: theme.palette.success.main }}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" color="white" fontWeight="bold">18.5%</Typography>
                        </Box>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="white">Good</Typography>
                        <Typography variant="caption" color="white">Industry avg: 15.2%</Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.1), backdropFilter: 'blur(10px)' }}>
                    <Typography variant="subtitle2" color="white" fontWeight="bold">Debt-to-Equity</Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress
                          variant="determinate"
                          value={42}
                          size={60}
                          thickness={5}
                          sx={{ color: theme.palette.warning.main }}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" color="white" fontWeight="bold">0.42</Typography>
                        </Box>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="white">Moderate</Typography>
                        <Typography variant="caption" color="white">Industry avg: 0.38</Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.1), backdropFilter: 'blur(10px)' }}>
                    <Typography variant="subtitle2" color="white" fontWeight="bold">Current Ratio</Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress
                          variant="determinate"
                          value={78}
                          size={60}
                          thickness={5}
                          sx={{ color: theme.palette.success.main }}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" color="white" fontWeight="bold">1.95</Typography>
                        </Box>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="white">Excellent</Typography>
                        <Typography variant="caption" color="white">Industry avg: 1.5</Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper sx={{ p: 2, bgcolor: alpha(theme.palette.background.paper, 0.1), backdropFilter: 'blur(10px)' }}>
                    <Typography variant="subtitle2" color="white" fontWeight="bold">ROI</Typography>
                    <Box display="flex" alignItems="center" mt={1}>
                      <Box sx={{ position: 'relative', display: 'inline-flex', mr: 2 }}>
                        <CircularProgress
                          variant="determinate"
                          value={65}
                          size={60}
                          thickness={5}
                          sx={{ color: theme.palette.info.main }}
                        />
                        <Box
                          sx={{
                            top: 0,
                            left: 0,
                            bottom: 0,
                            right: 0,
                            position: 'absolute',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Typography variant="caption" color="white" fontWeight="bold">12.8%</Typography>
                        </Box>
                      </Box>
                      <Box>
                        <Typography variant="body2" color="white">Good</Typography>
                        <Typography variant="caption" color="white">Industry avg: 10.5%</Typography>
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
              <Box display="flex" justifyContent="center" mt={2}>
                <CustomButton
                  variant="outlined"
                  color="inherit"
                  onClick={() => navigate('/financial/health')}
                  sx={{ color: 'white', borderColor: 'white' }}
                >
                  {translateModuleField ? translateModuleField('view_financial_health', "View Financial Health") : "View Financial Health"}
                </CustomButton>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>
      </Grid>

      {/* Recent Transactions */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12}>
          <motion.div variants={itemVariants}>
            <AnimatedBackgroundCard
              title={translateModuleField ? translateModuleField('recent_transactions', "Recent Transactions") : "Recent Transactions"}
              subtitle={translateModuleField ? translateModuleField('recent_transactions_desc', "View and manage your latest financial transactions") : "View and manage your latest financial transactions"}
              module="financial"
              uniqueId="financial-transactions"
              icon={<Receipt />}
              height={400}
              accentColor={theme.palette.primary.main}
              backgroundImage="/images/modules/financial/transactions.jpg"
              overlay="gradient"
              action={(
                <Box>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <Search />
                  </IconButton>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <FilterList />
                  </IconButton>
                  <CustomButton
                    variant="contained"
                    size="small"
                    startIcon={<Add />}
                    onClick={() => navigate('/financial/transactions/new')}
                    sx={{ ml: 1, bgcolor: alpha(theme.palette.common.white, 0.2), '&:hover': { bgcolor: alpha(theme.palette.common.white, 0.3) } }}
                  >
                    {translateModuleField ? translateModuleField('add_transaction', "Add Transaction") : "Add Transaction"}
                  </CustomButton>
                </Box>
              )}
            >
              <TableContainer component={Paper} sx={{ mt: 2, bgcolor: 'transparent', boxShadow: 'none' }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>ID</TableCell>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>Date</TableCell>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>Description</TableCell>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>Amount</TableCell>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>Type</TableCell>
                      <TableCell sx={{ color: 'white', fontWeight: 'bold', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.2)}` }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockTransactions.map((transaction) => (
                      <TableRow key={transaction.id} hover sx={{ '&:hover': { bgcolor: alpha(theme.palette.common.white, 0.1) } }}>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>{transaction.id}</TableCell>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>{new Date(transaction.date).toLocaleDateString('en-ZA')}</TableCell>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>{transaction.description}</TableCell>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>
                          <Typography
                            variant="body2"
                            sx={{
                              color: transaction.type === 'income' ? theme.palette.success.light : theme.palette.error.light,
                              fontWeight: 'bold'
                            }}
                          >
                            {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>
                          <Chip
                            label={transaction.type.charAt(0).toUpperCase() + transaction.type.slice(1)}
                            size="small"
                            sx={{
                              bgcolor: transaction.type === 'income' ? alpha(theme.palette.success.main, 0.2) : alpha(theme.palette.error.main, 0.2),
                              color: transaction.type === 'income' ? theme.palette.success.light : theme.palette.error.light,
                              fontWeight: 'bold'
                            }}
                          />
                        </TableCell>
                        <TableCell sx={{ color: 'white', borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}` }}>
                          <IconButton size="small" onClick={() => navigate(`/financial/transactions/${transaction.id}`)} sx={{ color: 'white' }}>
                            <ArrowForward fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box display="flex" justifyContent="center" mt={2}>
                <CustomButton
                  variant="outlined"
                  color="inherit"
                  onClick={() => navigate('/financial/transactions')}
                  sx={{ color: 'white', borderColor: 'white' }}
                >
                  {translateModuleField ? translateModuleField('view_all_transactions', "View All Transactions") : "View All Transactions"}
                </CustomButton>
              </Box>
            </AnimatedBackgroundCard>
          </motion.div>
        </Grid>
      </Grid>
        </Box>
    </StandardDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(FinancialDashboard, 'financial', 'dashboard');
