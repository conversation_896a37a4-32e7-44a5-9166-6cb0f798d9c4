/**
 * MongoDB Animal Model
 * 
 * This model provides methods to interact with the animals collection in MongoDB
 */

const { getCollection } = require('../../config/mongodb');
const { ObjectId } = require('mongodb');
const logger = require('../../utils/logger');

/**
 * Animal model for MongoDB
 */
class AnimalModel {
  /**
   * Get the animals collection
   * @returns {Promise<Collection>} MongoDB collection
   */
  static async getCollection() {
    return await getCollection('animals');
  }

  /**
   * Find all animals with pagination and filtering
   * @param {Object} options - Query options
   * @returns {Promise<Object>} - Paginated animals
   */
  static async findAll(options = {}) {
    try {
      const collection = await this.getCollection();
      
      const page = options.page || 1;
      const limit = options.limit || 10;
      const skip = (page - 1) * limit;
      
      // Build filter based on options
      const filter = {};
      
      if (options.species) {
        filter.species = options.species;
      }
      
      if (options.status) {
        filter.status = options.status;
      }
      
      if (options.location) {
        filter.location = options.location;
      }
      
      if (options.gender) {
        filter.gender = options.gender;
      }
      
      if (options.minWeight) {
        filter.weight = filter.weight || {};
        filter.weight.$gte = parseFloat(options.minWeight);
      }
      
      if (options.maxWeight) {
        filter.weight = filter.weight || {};
        filter.weight.$lte = parseFloat(options.maxWeight);
      }
      
      if (options.search) {
        filter.$or = [
          { tagNumber: { $regex: options.search, $options: 'i' } },
          { name: { $regex: options.search, $options: 'i' } },
          { breed: { $regex: options.search, $options: 'i' } },
          { notes: { $regex: options.search, $options: 'i' } }
        ];
      }
      
      // Build sort options
      const sort = {};
      if (options.sortBy) {
        sort[options.sortBy] = options.sortOrder === 'desc' ? -1 : 1;
      } else {
        sort.createdAt = -1; // Default sort by creation date, newest first
      }
      
      // Get total count
      const total = await collection.countDocuments(filter);
      
      // Get animals
      const animals = await collection.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .toArray();
      
      return {
        animals,
        pagination: {
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Error finding all animals in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Find animal by ID
   * @param {string} id - Animal ID
   * @returns {Promise<Object>} - Animal object
   */
  static async findById(id) {
    try {
      const collection = await this.getCollection();
      
      let objectId;
      try {
        objectId = new ObjectId(id);
      } catch (error) {
        return null; // Invalid ObjectId
      }
      
      return await collection.findOne({ _id: objectId });
    } catch (error) {
      logger.error('Error finding animal by ID in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Find animal by tag number
   * @param {string} tagNumber - Animal tag number
   * @returns {Promise<Object>} - Animal object
   */
  static async findByTagNumber(tagNumber) {
    try {
      const collection = await this.getCollection();
      return await collection.findOne({ tagNumber });
    } catch (error) {
      logger.error('Error finding animal by tag number in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Create a new animal
   * @param {Object} animalData - Animal data
   * @returns {Promise<Object>} - Created animal
   */
  static async create(animalData) {
    try {
      const collection = await this.getCollection();
      
      const now = new Date();
      const animal = {
        ...animalData,
        createdAt: now,
        updatedAt: now
      };
      
      const result = await collection.insertOne(animal);
      
      if (result.acknowledged) {
        return { _id: result.insertedId, ...animal };
      }
      
      throw new Error('Failed to create animal');
    } catch (error) {
      logger.error('Error creating animal in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Update animal
   * @param {string} id - Animal ID
   * @param {Object} animalData - Animal data to update
   * @returns {Promise<Object>} - Updated animal
   */
  static async update(id, animalData) {
    try {
      const collection = await this.getCollection();
      
      let objectId;
      try {
        objectId = new ObjectId(id);
      } catch (error) {
        throw new Error('Invalid animal ID');
      }
      
      const updateData = {
        ...animalData,
        updatedAt: new Date()
      };
      
      // Remove _id if present to avoid MongoDB error
      delete updateData._id;
      
      const result = await collection.findOneAndUpdate(
        { _id: objectId },
        { $set: updateData },
        { returnDocument: 'after' }
      );
      
      if (result) {
        return result;
      }
      
      throw new Error('Failed to update animal');
    } catch (error) {
      logger.error('Error updating animal in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Delete animal
   * @param {string} id - Animal ID
   * @returns {Promise<boolean>} - Success status
   */
  static async delete(id) {
    try {
      const collection = await this.getCollection();
      
      let objectId;
      try {
        objectId = new ObjectId(id);
      } catch (error) {
        return false; // Invalid ObjectId
      }
      
      const result = await collection.deleteOne({ _id: objectId });
      
      return result.deletedCount === 1;
    } catch (error) {
      logger.error('Error deleting animal in MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get animal statistics
   * @returns {Promise<Object>} - Animal statistics
   */
  static async getStatistics() {
    try {
      const collection = await this.getCollection();
      
      // Get total count
      const total = await collection.countDocuments();
      
      // Get counts by status
      const statusCounts = await collection.aggregate([
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get counts by gender
      const genderCounts = await collection.aggregate([
        { $group: { _id: '$gender', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get counts by species
      const speciesCounts = await collection.aggregate([
        { $group: { _id: '$species', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get counts by location
      const locationCounts = await collection.aggregate([
        { $group: { _id: '$location', count: { $sum: 1 } } }
      ]).toArray();
      
      // Get average weight
      const weightResult = await collection.aggregate([
        { $group: { _id: null, averageWeight: { $avg: '$weight' } } }
      ]).toArray();
      
      const averageWeight = weightResult.length > 0 ? weightResult[0].averageWeight : 0;
      
      // Format the results
      const statusMap = {};
      statusCounts.forEach(item => {
        statusMap[item._id] = item.count;
      });
      
      const genderMap = {};
      genderCounts.forEach(item => {
        genderMap[item._id] = item.count;
      });
      
      return {
        summary: {
          total_animals: total,
          active_animals: statusMap.active || 0,
          sick_animals: statusMap.sick || 0,
          pregnant_animals: statusMap.pregnant || 0,
          sold_animals: statusMap.sold || 0,
          deceased_animals: statusMap.deceased || 0,
          male_animals: genderMap.male || 0,
          female_animals: genderMap.female || 0,
          average_weight: averageWeight
        },
        byType: speciesCounts.map(item => ({
          animal_type: item._id,
          count: item.count
        })),
        byLocation: locationCounts.map(item => ({
          location: item._id,
          count: item.count
        }))
      };
    } catch (error) {
      logger.error('Error getting animal statistics from MongoDB:', error);
      throw error;
    }
  }
}

module.exports = AnimalModel;
