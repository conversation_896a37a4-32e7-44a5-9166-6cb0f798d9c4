import { useLanguage } from '../contexts/LanguageContext';

/**
 * Error types for better error handling
 */
export enum ErrorType {
  NETWORK = 'network',
  DATABASE = 'database',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  NOT_FOUND = 'not_found',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

/**
 * Custom error class with additional properties
 */
export class AppError extends Error {
  type: ErrorType;
  statusCode?: number;
  details?: any;
  
  constructor(message: string, type: ErrorType = ErrorType.UNKNOWN, statusCode?: number, details?: any) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * Parse error from various sources into a standardized AppError
 * @param error The error to parse
 * @returns A standardized AppError
 */
export function parseError(error: any): AppError {
  // If it's already an AppError, return it
  if (error instanceof AppError) {
    return error;
  }
  
  // Handle network errors
  if (error instanceof TypeError && error.message.includes('network')) {
    return new AppError(
      'Network error. Please check your internet connection.',
      ErrorType.NETWORK
    );
  }
  
  // Handle timeout errors
  if (error.name === 'TimeoutError' || (error.message && error.message.includes('timeout'))) {
    return new AppError(
      'Request timed out. Please try again later.',
      ErrorType.TIMEOUT
    );
  }
  
  // Handle MongoDB errors
  if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    return new AppError(
      'Database error. Please try again later.',
      ErrorType.DATABASE,
      500,
      error
    );
  }
  
  // Handle authentication errors
  if (error.statusCode === 401 || (error.message && error.message.includes('unauthorized'))) {
    return new AppError(
      'Authentication failed. Please log in again.',
      ErrorType.AUTHENTICATION,
      401
    );
  }
  
  // Handle authorization errors
  if (error.statusCode === 403 || (error.message && error.message.includes('forbidden'))) {
    return new AppError(
      'You do not have permission to perform this action.',
      ErrorType.AUTHORIZATION,
      403
    );
  }
  
  // Handle validation errors
  if (error.statusCode === 400 || (error.message && error.message.includes('validation'))) {
    return new AppError(
      'Validation error. Please check your input.',
      ErrorType.VALIDATION,
      400,
      error.details || error
    );
  }
  
  // Handle not found errors
  if (error.statusCode === 404 || (error.message && error.message.includes('not found'))) {
    return new AppError(
      'Resource not found.',
      ErrorType.NOT_FOUND,
      404
    );
  }
  
  // Default to unknown error
  return new AppError(
    error.message || 'An unknown error occurred.',
    ErrorType.UNKNOWN,
    error.statusCode || 500,
    error
  );
}

/**
 * Get a user-friendly error message based on the error type
 * @param error The error to get a message for
 * @returns A user-friendly error message
 */
export function getErrorMessage(error: any): string {
  const appError = parseError(error);
  
  // Return the error message
  return appError.message;
}

/**
 * Hook to get translated error messages
 */
export function useErrorMessages() {
  const { translate } = useLanguage();
  
  /**
   * Get a translated error message based on the error type
   * @param error The error to get a message for
   * @returns A translated user-friendly error message
   */
  const getTranslatedErrorMessage = (error: any): string => {
    const appError = parseError(error);
    
    // Try to get a translation key based on the error type
    const translationKey = `error.${appError.type}`;
    const translatedMessage = translate(translationKey);
    
    // If the translation key doesn't exist, return the error message
    if (translatedMessage === translationKey) {
      return appError.message;
    }
    
    return translatedMessage;
  };
  
  return {
    getTranslatedErrorMessage
  };
}

/**
 * Log an error to the console with additional context
 * @param error The error to log
 * @param context Additional context for the error
 */
export function logError(error: any, context?: any): void {
  const appError = parseError(error);
  
  console.error('Error:', {
    message: appError.message,
    type: appError.type,
    statusCode: appError.statusCode,
    details: appError.details,
    context
  });
}

/**
 * Safely execute a function and handle any errors
 * @param fn The function to execute
 * @param errorHandler Function to handle errors
 * @returns The result of the function or undefined if an error occurred
 */
export async function tryCatch<T>(
  fn: () => Promise<T>,
  errorHandler?: (error: AppError) => void
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    const appError = parseError(error);
    
    // Log the error
    logError(appError);
    
    // Call the error handler if provided
    if (errorHandler) {
      errorHandler(appError);
    }
    
    return undefined;
  }
}
