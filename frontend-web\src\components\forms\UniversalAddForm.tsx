import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Box,
  Typography,
  useTheme,
  alpha
} from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
}

interface UniversalAddFormProps {
  open: boolean;
  onClose: () => void;
  title: string;
  fields: FormField[];
  onSubmit: (data: any) => Promise<void>;
  submitLabel?: string;
}

const UniversalAddForm: React.FC<UniversalAddFormProps> = ({
  open,
  onClose,
  title,
  fields,
  onSubmit,
  submitLabel = 'Add Record'
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const themeColor = availableColors[currentColor];

  const handleInputChange = (fieldName: string) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
    setError(null);
  };

  const validateForm = (): boolean => {
    for (const field of fields) {
      if (field.required && (!formData[field.name] || formData[field.name].toString().trim() === '')) {
        setError(`${field.label} is required`);
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setError(null);

    try {
      await onSubmit(formData);
      setSuccess('Record added successfully!');
      setFormData({});
      setTimeout(() => {
        setSuccess(null);
        onClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add record');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({});
      setError(null);
      setSuccess(null);
      onClose();
    }
  };

  const renderField = (field: FormField) => {
    const commonProps = {
      fullWidth: true,
      label: field.label,
      value: formData[field.name] || '',
      onChange: handleInputChange(field.name),
      required: field.required,
      disabled: isLoading,
      placeholder: field.placeholder
    };

    switch (field.type) {
      case 'select':
        return (
          <FormControl fullWidth key={field.name}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={formData[field.name] || ''}
              onChange={handleInputChange(field.name)}
              disabled={isLoading}
              required={field.required}
            >
              {field.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'textarea':
        return (
          <TextField
            key={field.name}
            {...commonProps}
            multiline
            rows={field.rows || 4}
          />
        );

      case 'date':
        return (
          <TextField
            key={field.name}
            {...commonProps}
            type="date"
            InputLabelProps={{ shrink: true }}
          />
        );

      case 'number':
        return (
          <TextField
            key={field.name}
            {...commonProps}
            type="number"
          />
        );

      default:
        return (
          <TextField
            key={field.name}
            {...commonProps}
            type={field.type}
          />
        );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          backdropFilter: 'blur(10px)',
          backgroundColor: alpha(theme.palette.background.paper, 0.95),
          boxShadow: `0 8px 32px ${alpha(themeColor.primary, 0.3)}`
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography
            variant="h5"
            fontWeight="bold"
            sx={{
              background: `linear-gradient(135deg, ${themeColor.primary}, ${themeColor.secondary})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            {title}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {fields.map((field, index) => (
              <Grid item xs={12} sm={field.type === 'textarea' ? 12 : 6} key={field.name}>
                {renderField(field)}
              </Grid>
            ))}
          </Grid>
        </form>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={handleClose}
          disabled={isLoading}
          variant="outlined"
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isLoading}
          variant="contained"
          sx={{
            background: `linear-gradient(135deg, ${themeColor.primary}, ${themeColor.secondary})`,
            minWidth: 120
          }}
        >
          {isLoading ? (
            <>
              <CircularProgress size={20} sx={{ mr: 1 }} />
              Adding...
            </>
          ) : (
            submitLabel
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UniversalAddForm;
