import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, useTheme, alpha, Grid, Divider, FormControl, InputLabel, Select, MenuItem, TextField, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Tooltip, Chip, CircularProgress } from '@mui/material';
import { 
  Print, 
  GetApp, 
  Share, 
  PictureAsPdf, 
  Description, 
  InsertDriveFile, 
  Close,
  CalendarToday,
  FilterList,
  Settings
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { getSafeElevation } from '../../utils/themeDefaults';
import { useLanguage } from '../../contexts/LanguageContext';

export interface ReportFilter {
  id: string;
  label: string;
  type: 'select' | 'date' | 'dateRange' | 'text' | 'multiSelect';
  options?: { value: string; label: string }[];
  value?: any;
}

export interface ReportSection {
  title: string;
  type: 'table' | 'chart' | 'summary' | 'details';
  data: any;
  columns?: { id: string; label: string; format?: (value: any) => React.ReactNode }[];
}

export interface SubModuleReportProps {
  title: string;
  subtitle?: string;
  moduleColor?: string;
  filters?: ReportFilter[];
  sections: ReportSection[];
  isLoading?: boolean;
  onGenerateReport: (filters: ReportFilter[]) => void;
  onExport?: (format: 'pdf' | 'excel' | 'csv') => void;
  onPrint?: () => void;
  onShare?: () => void;
}

/**
 * A standardized report component for submodules
 */
const SubModuleReport: React.FC<SubModuleReportProps> = ({
  title,
  subtitle,
  moduleColor,
  filters = [],
  sections,
  isLoading = false,
  onGenerateReport,
  onExport,
  onPrint,
  onShare
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Use provided module color or default to primary
  const reportColor = moduleColor || theme.palette.primary.main;
  
  // State for filters
  const [reportFilters, setReportFilters] = useState<ReportFilter[]>(filters);
  const [showFilters, setShowFilters] = useState(false);
  
  // State for export dialog
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  
  // Handle filter change
  const handleFilterChange = (id: string, value: any) => {
    setReportFilters(prevFilters => 
      prevFilters.map(filter => 
        filter.id === id ? { ...filter, value } : filter
      )
    );
  };
  
  // Handle generate report
  const handleGenerateReport = () => {
    onGenerateReport(reportFilters);
  };
  
  // Handle export
  const handleExport = (format: 'pdf' | 'excel' | 'csv') => {
    if (onExport) {
      onExport(format);
    }
    setExportDialogOpen(false);
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        elevation={getSafeElevation(theme, 1)}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(reportColor, 0.1)}`,
          mb: 4
        }}
      >
        {/* Report Header */}
        <Box
          sx={{
            p: 3,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: `1px solid ${alpha(reportColor, 0.1)}`,
            backgroundColor: alpha(reportColor, 0.05)
          }}
        >
          <Box>
            <Typography variant="h5" fontWeight="bold" color={reportColor}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="text.secondary" mt={0.5}>
                {subtitle}
              </Typography>
            )}
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Filter Button */}
            <Tooltip title={translate('common.filter', { fallback: "Filter" })}>
              <CustomButton
                variant="outlined"
                size="small"
                startIcon={<FilterList />}
                onClick={() => setShowFilters(!showFilters)}
                sx={{
                  borderColor: alpha(reportColor, 0.3),
                  color: reportColor,
                  '&:hover': {
                    borderColor: reportColor,
                    backgroundColor: alpha(reportColor, 0.05)
                  }
                }}
              >
                {translate('common.filter', { fallback: "Filter" })}
              </CustomButton>
            </Tooltip>
            
            {/* Export Button */}
            {onExport && (
              <Tooltip title={translate('common.export', { fallback: "Export" })}>
                <CustomButton
                  variant="outlined"
                  size="small"
                  startIcon={<GetApp />}
                  onClick={() => setExportDialogOpen(true)}
                  sx={{
                    borderColor: alpha(reportColor, 0.3),
                    color: reportColor,
                    '&:hover': {
                      borderColor: reportColor,
                      backgroundColor: alpha(reportColor, 0.05)
                    }
                  }}
                >
                  {translate('common.export', { fallback: "Export" })}
                </CustomButton>
              </Tooltip>
            )}
            
            {/* Print Button */}
            {onPrint && (
              <Tooltip title={translate('common.print', { fallback: "Print" })}>
                <IconButton
                  size="small"
                  onClick={onPrint}
                  sx={{
                    color: reportColor,
                    border: `1px solid ${alpha(reportColor, 0.3)}`,
                    '&:hover': {
                      backgroundColor: alpha(reportColor, 0.05)
                    }
                  }}
                >
                  <Print fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            
            {/* Share Button */}
            {onShare && (
              <Tooltip title={translate('common.share', { fallback: "Share" })}>
                <IconButton
                  size="small"
                  onClick={onShare}
                  sx={{
                    color: reportColor,
                    border: `1px solid ${alpha(reportColor, 0.3)}`,
                    '&:hover': {
                      backgroundColor: alpha(reportColor, 0.05)
                    }
                  }}
                >
                  <Share fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
        
        {/* Filters Section */}
        {showFilters && (
          <Box sx={{ p: 3, borderBottom: `1px solid ${alpha(reportColor, 0.1)}` }}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              {translate('common.report_filters', { fallback: "Report Filters" })}
            </Typography>
            
            <Grid container spacing={2} sx={{ mt: 1 }}>
              {reportFilters.map((filter) => (
                <Grid item xs={12} sm={6} md={4} key={filter.id}>
                  {filter.type === 'select' && (
                    <FormControl fullWidth size="small">
                      <InputLabel id={`filter-${filter.id}-label`}>{filter.label}</InputLabel>
                      <Select
                        labelId={`filter-${filter.id}-label`}
                        id={`filter-${filter.id}`}
                        value={filter.value || ''}
                        label={filter.label}
                        onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                      >
                        {filter.options?.map((option) => (
                          <MenuItem key={option.value} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                  
                  {filter.type === 'date' && (
                    <TextField
                      fullWidth
                      size="small"
                      label={filter.label}
                      type="date"
                      value={filter.value || ''}
                      onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                      InputLabelProps={{ shrink: true }}
                    />
                  )}
                  
                  {filter.type === 'dateRange' && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <TextField
                        fullWidth
                        size="small"
                        label={`${filter.label} From`}
                        type="date"
                        value={filter.value?.from || ''}
                        onChange={(e) => handleFilterChange(filter.id, { ...filter.value, from: e.target.value })}
                        InputLabelProps={{ shrink: true }}
                      />
                      <TextField
                        fullWidth
                        size="small"
                        label={`${filter.label} To`}
                        type="date"
                        value={filter.value?.to || ''}
                        onChange={(e) => handleFilterChange(filter.id, { ...filter.value, to: e.target.value })}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Box>
                  )}
                  
                  {filter.type === 'text' && (
                    <TextField
                      fullWidth
                      size="small"
                      label={filter.label}
                      value={filter.value || ''}
                      onChange={(e) => handleFilterChange(filter.id, e.target.value)}
                    />
                  )}
                </Grid>
              ))}
              
              <Grid item xs={12} sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
                <CustomButton
                  variant="contained"
                  onClick={handleGenerateReport}
                  disabled={isLoading}
                  startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : undefined}
                  sx={{
                    backgroundColor: reportColor,
                    '&:hover': {
                      backgroundColor: alpha(reportColor, 0.8)
                    }
                  }}
                >
                  {isLoading
                    ? translate('common.generating', { fallback: "Generating..." })
                    : translate('common.generate_report', { fallback: "Generate Report" })}
                </CustomButton>
              </Grid>
            </Grid>
          </Box>
        )}
        
        {/* Report Content */}
        <Box sx={{ p: 3 }}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 8 }}>
              <CircularProgress sx={{ color: reportColor }} />
            </Box>
          ) : (
            <>
              {/* Report Sections */}
              {sections.map((section, index) => (
                <Box key={index} sx={{ mb: 4 }}>
                  <Typography variant="h6" fontWeight="bold" gutterBottom>
                    {section.title}
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  {/* Table Section */}
                  {section.type === 'table' && section.columns && (
                    <Box sx={{ overflowX: 'auto' }}>
                      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                        <thead>
                          <tr>
                            {section.columns.map((column) => (
                              <th
                                key={column.id}
                                style={{
                                  padding: '12px 16px',
                                  textAlign: 'left',
                                  backgroundColor: alpha(reportColor, 0.1),
                                  color: theme.palette.text.primary,
                                  fontWeight: 'bold',
                                  borderBottom: `1px solid ${alpha(reportColor, 0.2)}`
                                }}
                              >
                                {column.label}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {section.data.map((row: any, rowIndex: number) => (
                            <tr
                              key={rowIndex}
                              style={{
                                backgroundColor: rowIndex % 2 === 0
                                  ? 'transparent'
                                  : alpha(theme.palette.background.default, 0.5)
                              }}
                            >
                              {section.columns?.map((column) => (
                                <td
                                  key={column.id}
                                  style={{
                                    padding: '12px 16px',
                                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                                  }}
                                >
                                  {column.format
                                    ? column.format(row[column.id])
                                    : row[column.id]}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </Box>
                  )}
                  
                  {/* Summary Section */}
                  {section.type === 'summary' && (
                    <Grid container spacing={3}>
                      {section.data.map((item: any, itemIndex: number) => (
                        <Grid item xs={12} sm={6} md={3} key={itemIndex}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 2,
                              backgroundColor: alpha(reportColor, 0.05),
                              border: `1px solid ${alpha(reportColor, 0.1)}`
                            }}
                          >
                            <Typography variant="body2" color="text.secondary" gutterBottom>
                              {item.label}
                            </Typography>
                            <Typography variant="h5" fontWeight="bold" color="text.primary">
                              {item.value}
                            </Typography>
                            {item.change !== undefined && (
                              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                                <Chip
                                  size="small"
                                  label={`${item.change > 0 ? '+' : ''}${item.change}%`}
                                  color={item.change > 0 ? 'success' : 'error'}
                                  sx={{ height: 20, fontSize: '0.75rem' }}
                                />
                                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                                  {item.changePeriod || 'vs. previous period'}
                                </Typography>
                              </Box>
                            )}
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                  
                  {/* Details Section */}
                  {section.type === 'details' && (
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        backgroundColor: alpha(theme.palette.background.default, 0.5),
                        border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                      }}
                    >
                      <Grid container spacing={2}>
                        {Object.entries(section.data).map(([key, value]) => (
                          <Grid item xs={12} sm={6} md={4} key={key}>
                            <Box sx={{ mb: 1 }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                {key}
                              </Typography>
                              <Typography variant="body1" fontWeight="medium">
                                {value as React.ReactNode}
                              </Typography>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                  
                  {/* Chart Section */}
                  {section.type === 'chart' && (
                    <Box
                      sx={{
                        p: 2,
                        height: 300,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        border: `1px dashed ${alpha(reportColor, 0.3)}`,
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="body1" color={reportColor} fontWeight="medium">
                        {section.data.type?.toUpperCase()} Chart: {section.title}
                      </Typography>
                    </Box>
                  )}
                </Box>
              ))}
            </>
          )}
        </Box>
      </Paper>
      
      {/* Export Dialog */}
      <Dialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {translate('common.export_report', { fallback: "Export Report" })}
            <IconButton
              size="small"
              onClick={() => setExportDialogOpen(false)}
              sx={{ color: theme.palette.text.secondary }}
            >
              <Close fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Typography variant="body2" color="text.secondary" paragraph>
            {translate('common.select_export_format', { fallback: "Select the format to export your report:" })}
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <CustomButton
              variant="outlined"
              startIcon={<PictureAsPdf />}
              onClick={() => handleExport('pdf')}
              sx={{ justifyContent: 'flex-start', py: 1.5 }}
            >
              <Box sx={{ textAlign: 'left' }}>
                <Typography variant="body1" fontWeight="medium">PDF Document</Typography>
                <Typography variant="caption" color="text.secondary">
                  {translate('common.export_pdf_desc', { fallback: "Export as a PDF document for printing or sharing" })}
                </Typography>
              </Box>
            </CustomButton>
            
            <CustomButton
              variant="outlined"
              startIcon={<Description />}
              onClick={() => handleExport('excel')}
              sx={{ justifyContent: 'flex-start', py: 1.5 }}
            >
              <Box sx={{ textAlign: 'left' }}>
                <Typography variant="body1" fontWeight="medium">Excel Spreadsheet</Typography>
                <Typography variant="caption" color="text.secondary">
                  {translate('common.export_excel_desc', { fallback: "Export as an Excel file for data analysis" })}
                </Typography>
              </Box>
            </CustomButton>
            
            <CustomButton
              variant="outlined"
              startIcon={<InsertDriveFile />}
              onClick={() => handleExport('csv')}
              sx={{ justifyContent: 'flex-start', py: 1.5 }}
            >
              <Box sx={{ textAlign: 'left' }}>
                <Typography variant="body1" fontWeight="medium">CSV File</Typography>
                <Typography variant="caption" color="text.secondary">
                  {translate('common.export_csv_desc', { fallback: "Export as a CSV file for data import" })}
                </Typography>
              </Box>
            </CustomButton>
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setExportDialogOpen(false)}>
            {translate('common.cancel', { fallback: "Cancel" })}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </motion.div>
  );
};

export default SubModuleReport;
