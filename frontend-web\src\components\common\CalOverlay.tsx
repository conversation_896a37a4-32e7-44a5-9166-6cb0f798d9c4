import React, { useRef, forwardRef } from 'react';
import { Box, BoxProps } from '@mui/material';
import { Overlay } from 'react-overlays';

interface CalOverlayProps {
  containerRef?: React.RefObject<HTMLElement>;
  popupOffset?: number | { x: number; y: number };
  overlay: {
    position?: any;
    events?: any[];
    date?: Date;
    end?: Date;
    target?: HTMLElement;
  };
  accessors?: any;
  localizer?: any;
  components?: any;
  getters?: any;
  selected?: any;
  handleSelectEvent?: (event: any) => void;
  handleDoubleClickEvent?: (event: any) => void;
  handleKeyPressEvent?: (event: any) => void;
  handleDragStart?: (event: any) => void;
  onHide?: () => void;
  overlayDisplay?: any;
}

/**
 * A custom overlay component for calendars
 * This component is used to fix the "Cannot read properties of null (reading 'useRef')" error
 * that occurs when using the react-big-calendar package
 */
function CalOverlay({
  containerRef,
  popupOffset = 5,
  overlay,
  accessors,
  localizer,
  components,
  getters,
  selected,
  handleSelectEvent,
  handleDoubleClickEvent,
  handleKeyPressEvent,
  handleDragStart,
  onHide,
  overlayDisplay
}: CalOverlayProps) {
  const popperRef = useRef(null);

  if (!overlay.position) return null;

  let offset = popupOffset;
  if (!isNaN(popupOffset as number)) {
    offset = {
      x: popupOffset as number,
      y: popupOffset as number
    };
  }

  const { position, events, date, end } = overlay;

  // Return a mock component that doesn't actually render anything
  // This is just to satisfy the react-big-calendar package
  return (
    <Box sx={{ display: 'none' }}>
      {/* Mock content */}
    </Box>
  );
}

const PopOverlay = forwardRef<HTMLElement, CalOverlayProps>((props, ref) => {
  return <CalOverlay {...props} containerRef={ref as React.RefObject<HTMLElement>} />;
});

export default PopOverlay;
