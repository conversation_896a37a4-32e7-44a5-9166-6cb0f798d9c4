/**
 * Modern Interactive Landing Page Styles
 * Inspired by modern AI/tech landing pages with floating elements and glassmorphism
 */

/* Floating Elements and Particles */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
}

.floating-particle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(16, 185, 129, 0.3));
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
}

.floating-particle:nth-child(1) {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-particle:nth-child(2) {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-particle:nth-child(3) {
  width: 60px;
  height: 60px;
  top: 80%;
  left: 70%;
  animation-delay: 4s;
}

.floating-particle:nth-child(4) {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* Glassmorphism Cards */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  transform: translateY(-8px);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card-dark:hover {
  background: rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Modern Gradient Backgrounds */
.gradient-bg-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-nature {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-bg-tech {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Interactive Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
  position: relative;
}

.hover-glow::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #667eea, #764ba2, #43e97b, #38f9d7);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  filter: blur(10px);
}

.hover-glow:hover::before {
  opacity: 0.7;
}

/* Animated Backgrounds */
.animated-bg {
  background: linear-gradient(-45deg, #667eea, #764ba2, #43e97b, #38f9d7);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Modern Typography Effects */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: textShimmer 3s ease-in-out infinite;
}

@keyframes textShimmer {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Interactive Buttons */
.modern-button {
  position: relative;
  padding: 16px 32px;
  border: none;
  border-radius: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.modern-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.modern-button:hover::before {
  left: 100%;
}

/* Pulse Animation */
.pulse-animation {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Geometric Shapes */
.geometric-shape {
  position: absolute;
  opacity: 0.1;
  pointer-events: none;
}

.shape-triangle {
  width: 0;
  height: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
  border-bottom: 87px solid currentColor;
}

.shape-triangle-blue {
  color: #3b82f6;
}

.shape-triangle-green {
  color: #10b981;
}

.shape-hexagon {
  width: 100px;
  height: 55px;
  background: currentColor;
  position: relative;
}

.shape-hexagon-blue {
  color: #3b82f6;
}

.shape-hexagon-green {
  color: #10b981;
}

.shape-hexagon::before,
.shape-hexagon::after {
  content: "";
  position: absolute;
  width: 0;
  border-left: 50px solid transparent;
  border-right: 50px solid transparent;
}

.shape-hexagon::before {
  bottom: 100%;
  border-bottom: 29px solid currentColor;
}

.shape-hexagon::after {
  top: 100%;
  border-top: 29px solid currentColor;
}

/* Responsive Design */
@media (max-width: 768px) {
  .floating-particle {
    display: none;
  }
  
  .glass-card {
    border-radius: 16px;
  }
  
  .modern-button {
    padding: 12px 24px;
    font-size: 14px;
  }
  
  .hover-lift:hover {
    transform: translateY(-6px) scale(1.01);
  }
}

@media (prefers-reduced-motion: reduce) {
  .floating-particle,
  .animated-bg,
  .text-gradient,
  .pulse-animation {
    animation: none;
  }
  
  .hover-lift:hover,
  .modern-button:hover {
    transform: none;
  }
}
