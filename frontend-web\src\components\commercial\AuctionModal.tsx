import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, IconButton, Typography, FormControl, InputLabel, Select, CircularProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Divider, Box, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete, Add, Remove  } from '../../utils/iconImports';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import { Auction, AuctionItem } from '../../types/commercial';
import dayjs from 'dayjs';
import {  withSubModuleTranslation , CustomButton } from '../common';

interface AuctionModalProps {
  open: boolean;
  onClose: () => void;
  auction?: Auction | null;
  onSave: (auction: Omit<Auction, 'id'>) => void;
  onUpdate?: (id: string, auction: Partial<Auction>) => void;
  onDelete?: (id: string) => void;
  loading?: boolean;
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const AuctionModal: React.FC<AuctionModalProps> = ({
  open,
  onClose,
  auction,
  onSave,
  onUpdate,
  onDelete,
  loading = false,
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const isEditMode = !!auction;

  // Form state
  const [formData, setFormData] = useState<Partial<Auction>>({
    title: '',
    date: new Date(),
    status: 'upcoming',
    location: '',
    description: '',
    items: [],
    registeredBidders: 0,
    minimumBid: 0,
    endTime: new Date(new Date().setHours(new Date().getHours() + 3)), // Default to 3 hours after current time
  });

  // Items state for the auction
  const [items, setItems] = useState<AuctionItem[]>([]);

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when auction changes
  useEffect(() => {
    if (auction) {
      setFormData({
        title: auction.title,
        date: auction.date,
        status: auction.status,
        location: auction.location,
        description: auction.description,
        registeredBidders: auction.registeredBidders,
        minimumBid: auction.minimumBid,
        endTime: auction.endTime,
      });
      setItems([...auction.items]);
    } else {
      // Reset form for new auction
      const now = new Date();
      setFormData({
        title: '',
        date: now,
        status: 'upcoming',
        location: '',
        description: '',
        items: [],
        registeredBidders: 0,
        minimumBid: 100,
        endTime: new Date(now.setHours(now.getHours() + 3)),
      });
      setItems([]);
    }
    setErrors({});
  }, [auction, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle number field changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: Number(value) }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle date changes
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date: date.toDate() }));

      // Clear error when field is edited
      if (errors.date) {
        setErrors(prev => ({ ...prev, date: '' }));
      }
    }
  };

  // Handle end time changes
  const handleEndTimeChange = (time: dayjs.Dayjs | null) => {
    if (time) {
      setFormData(prev => ({ ...prev, endTime: time.toDate() }));

      // Clear error when field is edited
      if (errors.endTime) {
        setErrors(prev => ({ ...prev, endTime: '' }));
      }
    }
  };

  // Add a new item to the auction
  const handleAddItem = () => {
    const newItem: AuctionItem = {
      id: `ITEM${Date.now()}`,
      type: 'livestock',
      description: '',
      startingPrice: 0,
      numberOfBids: 0,
      images: [],
      status: 'available',
    };
    setItems(prev => [...prev, newItem]);
  };

  // Update an item in the auction
  const handleItemChange = (index: number, field: keyof AuctionItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    setItems(updatedItems);
    setFormData(prev => ({ ...prev, items: updatedItems }));
  };

  // Remove an item from the auction
  const handleRemoveItem = (index: number) => {
    const updatedItems = items.filter((_, i) => i !== index);
    setItems(updatedItems);
    setFormData(prev => ({ ...prev, items: updatedItems }));
  };

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title) newErrors.title = translate ? translate('commercial.auction.error.title_required', { fallback: 'Title is required' }) : 'Title is required';
    if (!formData.date) newErrors.date = translate ? translate('commercial.auction.error.date_required', { fallback: 'Date is required' }) : 'Date is required';
    if (!formData.status) newErrors.status = translate ? translate('commercial.auction.error.status_required', { fallback: 'Status is required' }) : 'Status is required';
    if (!formData.location) newErrors.location = translate ? translate('commercial.auction.error.location_required', { fallback: 'Location is required' }) : 'Location is required';
    if (!formData.description) newErrors.description = translate ? translate('commercial.auction.error.description_required', { fallback: 'Description is required' }) : 'Description is required';

    if (formData.registeredBidders === undefined || formData.registeredBidders < 0) {
      newErrors.registeredBidders = translate ? translate('commercial.auction.error.bidders_positive', { fallback: 'Registered bidders must be a positive number' }) : 'Registered bidders must be a positive number';
    }

    if (formData.minimumBid === undefined || formData.minimumBid <= 0) {
      newErrors.minimumBid = translate ? translate('commercial.auction.error.minimum_bid_positive', { fallback: 'Minimum bid must be greater than zero' }) : 'Minimum bid must be greater than zero';
    }

    if (!formData.endTime) {
      newErrors.endTime = translate ? translate('commercial.auction.error.end_time_required', { fallback: 'End time is required' }) : 'End time is required';
    } else if (formData.date && formData.endTime <= formData.date) {
      newErrors.endTime = translate ? translate('commercial.auction.error.end_time_after_start', { fallback: 'End time must be after start time' }) : 'End time must be after start time';
    }

    if (items.length === 0) {
      newErrors.items = translate ? translate('commercial.auction.error.items_required', { fallback: 'At least one item is required' }) : 'At least one item is required';
    } else {
      // Validate each item
      const invalidItems = items.some(item =>
        !item.description ||
        !item.type ||
        item.startingPrice <= 0
      );

      if (invalidItems) {
        newErrors.items = translate ? translate('commercial.auction.error.invalid_items', { fallback: 'All items must have a description, type, and valid starting price' }) : 'All items must have a description, type, and valid starting price';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;

    const auctionData = {
      ...formData,
      items,
    } as Omit<Auction, 'id'>;

    if (isEditMode && auction && onUpdate) {
      onUpdate(auction.id, auctionData);
    } else {
      onSave(auctionData);
    }
  };

  // Handle auction deletion
  const handleDelete = () => {
    if (isEditMode && auction && onDelete) {
      onDelete(auction.id);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {isEditMode
            ? (translateSubModule ? translateSubModule('edit_title', 'Edit Auction') : 'Edit Auction')
            : (translateSubModule ? translateSubModule('new_title', 'New Auction') : 'New Auction')}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Auction Details */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {translateModuleField ? translateModuleField('auction_details', 'Auction Details') : 'Auction Details'}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('auction_title', 'Auction Title') : 'Auction Title'}
              name="title"
              value={formData.title || ''}
              onChange={handleChange}
              error={!!errors.title}
              helperText={errors.title}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label="Auction Date"
              value={formData.date ? dayjs(formData.date) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.date,
                  helperText: errors.date
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TimePicker
              label="End Time"
              value={formData.endTime ? dayjs(formData.endTime) : null}
              onChange={handleEndTimeChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                  error: !!errors.endTime,
                  helperText: errors.endTime
                }
              }}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.status}>
              <InputLabel>{translateModuleField ? translateModuleField('status', 'Status') : 'Status'}</InputLabel>
              <Select
                name="status"
                value={formData.status || ''}
                onChange={handleSelectChange}
                label="Status"
              >
                <MenuItem value="upcoming">{translate ? translate('commercial.auction.status.upcoming', { fallback: 'Upcoming' }) : 'Upcoming'}</MenuItem>
                <MenuItem value="active">{translate ? translate('commercial.auction.status.active', { fallback: 'Active' }) : 'Active'}</MenuItem>
                <MenuItem value="completed">{translate ? translate('commercial.auction.status.completed', { fallback: 'Completed' }) : 'Completed'}</MenuItem>
                <MenuItem value="cancelled">{translate ? translate('commercial.auction.status.cancelled', { fallback: 'Cancelled' }) : 'Cancelled'}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('location', 'Location') : 'Location'}
              name="location"
              value={formData.location || ''}
              onChange={handleChange}
              error={!!errors.location}
              helperText={errors.location}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Registered Bidders"
              name="registeredBidders"
              type="number"
              inputProps={{ min: 0 }}
              value={formData.registeredBidders || 0}
              onChange={handleNumberChange}
              error={!!errors.registeredBidders}
              helperText={errors.registeredBidders}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Minimum Bid"
              name="minimumBid"
              type="number"
              inputProps={{ min: 0, step: 0.01 }}
              value={formData.minimumBid || 0}
              onChange={handleNumberChange}
              error={!!errors.minimumBid}
              helperText={errors.minimumBid}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('description', 'Description') : 'Description'}
              name="description"
              value={formData.description || ''}
              onChange={handleChange}
              multiline
              rows={2}
              error={!!errors.description}
              helperText={errors.description}
              required
            />
          </Grid>

          {/* Auction Items */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="subtitle1" fontWeight="bold">
                {translateModuleField ? translateModuleField('auction_items', 'Auction Items') : 'Auction Items'}
              </Typography>
              <CustomButton
                startIcon={<Add />}
                variant="outlined"
                onClick={handleAddItem}
                size="small"
              >
                {translate ? translate('commercial.auction.add_item', { fallback: 'Add Item' }) : 'Add Item'}
              </CustomButton>
            </Box>

            {errors.items && (
              <Typography color="error" variant="body2" sx={{ mb: 2 }}>
                {errors.items}
              </Typography>
            )}

            <TableContainer component={Paper} variant="outlined" sx={{ mb: 3 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>{translateModuleField ? translateModuleField('type', 'Type') : 'Type'}</TableCell>
                    <TableCell>{translateModuleField ? translateModuleField('description', 'Description') : 'Description'}</TableCell>
                    <TableCell align="right">{translateModuleField ? translateModuleField('starting_price', 'Starting Price') : 'Starting Price'}</TableCell>
                    <TableCell align="center">{translateModuleField ? translateModuleField('status', 'Status') : 'Status'}</TableCell>
                    <TableCell align="center">{translate ? translate('common.actions', { fallback: 'Actions' }) : 'Actions'}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {items.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} align="center">
                        <Typography variant="body2" color="text.secondary" sx={{ py: 2 }}>
                          {translate ? translate('commercial.auction.no_items', { fallback: 'No items added yet. Click "Add Item" to add items to this auction.' }) : 'No items added yet. Click "Add Item" to add items to this auction.'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ) : (
                    items.map((item, index) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Select
                              value={item.type}
                              onChange={(e) => handleItemChange(index, 'type', e.target.value)}
                              variant="standard"
                            >
                              <MenuItem value="livestock">Livestock</MenuItem>
                              <MenuItem value="equipment">Equipment</MenuItem>
                              <MenuItem value="feed">Feed</MenuItem>
                              <MenuItem value="other">Other</MenuItem>
                            </Select>
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          <TextField
                            fullWidth
                            size="small"
                            placeholder="Item description"
                            value={item.description}
                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                            variant="standard"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <TextField
                            size="small"
                            type="number"
                            inputProps={{ min: 0, step: 0.01, style: { textAlign: 'right' } }}
                            value={item.startingPrice}
                            onChange={(e) => handleItemChange(index, 'startingPrice', Number(e.target.value))}
                            variant="standard"
                            sx={{ width: '100px' }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          <FormControl size="small">
                            <Select
                              value={item.status}
                              onChange={(e) => handleItemChange(index, 'status', e.target.value)}
                              variant="standard"
                            >
                              <MenuItem value="available">Available</MenuItem>
                              <MenuItem value="sold">Sold</MenuItem>
                              <MenuItem value="withdrawn">Withdrawn</MenuItem>
                            </Select>
                          </FormControl>
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleRemoveItem(index)}
                          >
                            <Remove fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {isEditMode && onDelete && (
          <CustomButton
            onClick={handleDelete}
            color="error"
            startIcon={<Delete />}
            disabled={loading}
          >
            {translate ? translate('common.delete', { fallback: 'Delete' }) : 'Delete'}
          </CustomButton>
        )}
        <Box flexGrow={1} />
        <CustomButton onClick={onClose} disabled={loading}>
          {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
        </CustomButton>
        <CustomButton
          onClick={handleSubmit}
          variant="contained"
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            isEditMode
              ? (translate ? translate('common.update', { fallback: 'Update' }) : 'Update')
              : (translate ? translate('common.save', { fallback: 'Save' }) : 'Save')
          )}
        </CustomButton>
      </DialogActions>
    </Dialog>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(AuctionModal, 'commercial', 'auction');
