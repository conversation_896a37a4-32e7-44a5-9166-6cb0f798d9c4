import React, { useState } from 'react';
import { Button, useTheme, alpha } from '@mui/material';
import { Add } from '@mui/icons-material';
import { useThemeContext } from '../../contexts/ThemeContext';
import UniversalAddForm from '../forms/UniversalAddForm';

interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
}

interface AddRecordButtonProps {
  module: string;
  title: string;
  fields: FormField[];
  onSuccess?: () => void;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const AddRecordButton: React.FC<AddRecordButtonProps> = ({
  module,
  title,
  fields,
  onSuccess,
  variant = 'contained',
  size = 'medium',
  fullWidth = false
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [isFormOpen, setIsFormOpen] = useState(false);

  const themeColor = availableColors[currentColor];

  const handleSubmit = async (data: any) => {
    try {
      const response = await fetch(`/api/${module}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to add record');
      }

      const result = await response.json();
      
      if (result.success) {
        if (onSuccess) {
          onSuccess();
        }
        setIsFormOpen(false);
      } else {
        throw new Error(result.message || 'Failed to add record');
      }
    } catch (error) {
      throw error;
    }
  };

  return (
    <>
      <Button
        variant={variant}
        size={size}
        fullWidth={fullWidth}
        startIcon={<Add />}
        onClick={() => setIsFormOpen(true)}
        sx={{
          background: variant === 'contained' 
            ? `linear-gradient(135deg, ${themeColor.primary}, ${themeColor.secondary})`
            : 'transparent',
          border: variant === 'outlined' 
            ? `2px solid ${themeColor.primary}`
            : 'none',
          color: variant === 'contained' ? 'white' : themeColor.primary,
          fontWeight: 'bold',
          textTransform: 'none',
          borderRadius: 2,
          px: 3,
          py: 1.5,
          '&:hover': {
            background: variant === 'contained'
              ? `linear-gradient(135deg, ${themeColor.secondary}, ${themeColor.primary})`
              : alpha(themeColor.primary, 0.1),
            transform: 'translateY(-2px)',
            boxShadow: `0 8px 25px ${alpha(themeColor.primary, 0.3)}`
          },
          transition: 'all 0.3s ease'
        }}
      >
        Add New {title}
      </Button>

      <UniversalAddForm
        open={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        title={`Add New ${title}`}
        fields={fields}
        onSubmit={handleSubmit}
        submitLabel={`Add ${title}`}
      />
    </>
  );
};

export default AddRecordButton;
