import React, { useState, useEffect } from 'react';
import { Calendar, dateFnsLocalizer, Views } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay, addDays, parseISO } from 'date-fns';
import enUS from 'date-fns/locale/en-US';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { motion } from 'framer-motion';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';
import { mockAnimals } from '../../mocks/animalData';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, SelectChangeEvent, Chip, IconButton, Grid, Tooltip, Alert, Snackbar, useTheme, alpha, Paper, Divider } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Info,
  FilterList,
  Search,
  Female,
  CalendarToday,
  Thermostat,
  Notifications,
  Close,
  Event,
  ChevronLeft,
  ChevronRight,
  Today
} from '../../utils/iconImports';
import { 
  SubModuleDashboard,
  SubModuleMetricsCard,
  SubModuleActionPanel,
  SubModuleChart
, CustomButton } from '../../components/common';
import { CustomCalendarPopup } from '../../components/calendar';

interface HeatEvent {
  id: string;
  animalId: string;
  animalName: string;
  status: 'upcoming' | 'active' | 'completed';
  intensity: 'high' | 'medium' | 'low';
  notes: string;
  start: Date;
  end: Date;
  title: string;
}

interface EventFormData {
  animalId: string;
  date: string;
  intensity: 'high' | 'medium' | 'low';
  notes: string;
}

const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const HeatCalendar: React.FC = () => {
  const { heatRecords, addHeatRecord } = useBreedingRecords();
  const [events, setEvents] = useState<HeatEvent[]>([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<HeatEvent | null>(null);
  const [formData, setFormData] = useState<EventFormData>({
    animalId: '',
    date: format(new Date(), 'yyyy-MM-dd'),
    intensity: 'medium',
    notes: ''
  });
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  useEffect(() => {
    // Convert heat records to calendar events
    if (heatRecords && heatRecords.length > 0) {
      const mappedEvents = heatRecords.map(record => {
        const animal = mockAnimals.find(a => a.id === record.animalId) || { name: `Animal ${record.animalId}` };
        const eventDate = parseISO(record.date);

        return {
          id: record.id,
          animalId: record.animalId,
          animalName: animal.name,
          status: determineStatus(eventDate),
          intensity: record.intensity,
          notes: record.notes,
          start: eventDate,
          end: eventDate,
          title: `${animal.name} - Heat (${record.intensity})`
        };
      });

      setEvents(mappedEvents);
    }
  }, [heatRecords]);

  const determineStatus = (date: Date): 'upcoming' | 'active' | 'completed' => {
    const today = new Date();
    if (date > today) return 'upcoming';
    if (date < today) return 'completed';
    return 'active';
  };

  const generateSampleEvents = (): HeatEvent[] => {
    const today = new Date();
    const femaleAnimals = mockAnimals.filter(animal => animal.gender.toLowerCase() === 'female').slice(0, 5);

    return femaleAnimals.map((animal, index) => {
      const startDate = addDays(today, -15 + index * 7); // Spread events around today

      return {
        id: `HE${index + 1}`,
        animalId: animal.id,
        animalName: animal.name,
        status: determineStatus(startDate),
        intensity: ['high', 'medium', 'low'][index % 3] as 'high' | 'medium' | 'low',
        notes: `Heat cycle for ${animal.name}`,
        start: startDate,
        end: startDate,
        title: `${animal.name} - Heat Cycle`
      };
    });
  };

  const handleEventSelect = (event: HeatEvent) => {
    setSelectedEvent(event);
    setFormData({
      animalId: event.animalId,
      date: format(event.start, 'yyyy-MM-dd'),
      intensity: event.intensity,
      notes: event.notes
    });
    setOpenDialog(true);
  };

  const handleAddNew = () => {
    setSelectedEvent(null);
    setFormData({
      animalId: '',
      date: format(new Date(), 'yyyy-MM-dd'),
      intensity: 'medium',
      notes: ''
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string | 'high' | 'medium' | 'low'>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name as string]: value
    });
  };

  const handleSave = () => {
    try {
      if (!formData.animalId) {
        setSnackbar({ open: true, message: 'Please select an animal', severity: 'error' });
        return;
      }

      if (selectedEvent) {
        // Update existing event logic would go here
        // For now, we'll just update the local state
        const updatedEvents = events.map(event =>
          event.id === selectedEvent.id
            ? {
                ...event,
                animalId: formData.animalId,
                animalName: mockAnimals.find(a => a.id === formData.animalId)?.name || `Animal ${formData.animalId}`,
                start: parseISO(formData.date),
                end: parseISO(formData.date),
                intensity: formData.intensity,
                notes: formData.notes,
                title: `${mockAnimals.find(a => a.id === formData.animalId)?.name || `Animal ${formData.animalId}`} - Heat (${formData.intensity})`
              }
            : event
        );
        setEvents(updatedEvents);
        setSnackbar({ open: true, message: 'Heat record updated successfully', severity: 'success' });
      } else {
        // Add new heat record
        const newRecord = {
          animalId: formData.animalId,
          date: formData.date,
          intensity: formData.intensity,
          notes: formData.notes
        };

        const addedRecord = addHeatRecord(newRecord);
        const animal = mockAnimals.find(a => a.id === formData.animalId) || { name: `Animal ${formData.animalId}` };

        const newEvent: HeatEvent = {
          id: addedRecord.id,
          animalId: addedRecord.animalId,
          animalName: animal.name,
          status: determineStatus(parseISO(addedRecord.date)),
          intensity: addedRecord.intensity,
          notes: addedRecord.notes,
          start: parseISO(addedRecord.date),
          end: parseISO(addedRecord.date),
          title: `${animal.name} - Heat (${addedRecord.intensity})`
        };

        setEvents([...events, newEvent]);
        setSnackbar({ open: true, message: 'Heat record added successfully', severity: 'success' });
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Error saving heat record:', error);
      setSnackbar({ open: true, message: 'Error saving heat record', severity: 'error' });
    }
  };

  const eventStyleGetter = (event: HeatEvent) => {
    let backgroundColor = '#3174ad';
    let textColor = '#ffffff';

    // Color based on intensity
    if (event.intensity === 'high') {
      backgroundColor = '#d32f2f'; // Red for high intensity
    } else if (event.intensity === 'medium') {
      backgroundColor = '#f57c00'; // Orange for medium intensity
    } else if (event.intensity === 'low') {
      backgroundColor = '#388e3c'; // Green for low intensity
    }

    // Adjust style based on status
    if (event.status === 'completed') {
      backgroundColor = `${backgroundColor}99`; // Add transparency
    } else if (event.status === 'upcoming') {
      backgroundColor = `${backgroundColor}cc`; // Less transparency
    }

    return {
      style: {
        backgroundColor,
        color: textColor,
        borderRadius: '4px',
        border: 'none',
        display: 'block'
      }
    };
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  const theme = useTheme();
  const navigate = useNavigate();

  // Define metrics for the dashboard
  const metrics = [
    {
      label: "Active Heat Cycles",
      value: events.filter(e => e.status === 'active').length,
      icon: <Thermostat />,
      color: theme.palette.error.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "from last month"
      }
    },
    {
      label: "Upcoming Cycles",
      value: events.filter(e => e.status === 'upcoming').length,
      icon: <Event />,
      color: theme.palette.primary.main
    },
    {
      label: "Total Records",
      value: events.length,
      icon: <Female />,
      color: theme.palette.secondary.main
    }
  ];

  // Define actions for the dashboard
  const actions = [
    {
      label: "Add Heat Record",
      icon: <Add />,
      onClick: handleAddNew,
      color: theme.palette.success.main
    },
    {
      label: "Generate Report",
      icon: <CalendarToday />,
      onClick: () => console.log("Generate report"),
      color: theme.palette.info.main
    },
    {
      label: "Set Notifications",
      icon: <Notifications />,
      onClick: () => console.log("Set notifications"),
      color: theme.palette.warning.main
    }
  ];

  // Define chart data for the dashboard
  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Heat Cycles',
        data: [12, 19, 15, 17, 14, 13],
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.main
      }
    ]
  };

  return (
    <SubModuleDashboard
      title="Heat Detection Calendar"
      subtitle="Track and manage breeding cycles for optimal reproduction management"
      parentModule="breeding"
      subModule="heat-calendar"
      icon={<CalendarToday />}
      metrics={metrics}
      actions={actions}
      isLoading={false}
      onRefresh={() => console.log("Refreshing data")}
      onBack={() => navigate('/breeding')}
    >
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            height: '100%',
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
          }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Heat Records Legend
              </Typography>
              <Divider sx={{ my: 1.5 }} />
              <Box display="flex" flexDirection="column" gap={1.5} mt={2}>
                <Paper elevation={0} sx={{ p: 1.5, bgcolor: alpha('#d32f2f', 0.1), borderLeft: `4px solid #d32f2f` }}>
                  <Typography variant="subtitle2" fontWeight="medium">High Intensity</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Strong signs, optimal for breeding
                  </Typography>
                </Paper>

                <Paper elevation={0} sx={{ p: 1.5, bgcolor: alpha('#f57c00', 0.1), borderLeft: `4px solid #f57c00` }}>
                  <Typography variant="subtitle2" fontWeight="medium">Medium Intensity</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Moderate signs, good for breeding
                  </Typography>
                </Paper>

                <Paper elevation={0} sx={{ p: 1.5, bgcolor: alpha('#388e3c', 0.1), borderLeft: `4px solid #388e3c` }}>
                  <Typography variant="subtitle2" fontWeight="medium">Low Intensity</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Mild signs, monitor closely
                  </Typography>
                </Paper>
              </Box>

              <Box mt={3}>
                <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                  Status Indicators
                </Typography>
                <Box display="flex" flexDirection="column" gap={1}>
                  <Box display="flex" alignItems="center">
                    <Chip size="small" label="Active" color="error" sx={{ mr: 1 }} />
                    <Typography variant="body2">Current heat cycle</Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    <Chip size="small" label="Upcoming" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="body2">Expected heat cycle</Typography>
                  </Box>
                  <Box display="flex" alignItems="center">
                    <Chip size="small" label="Completed" color="default" sx={{ mr: 1 }} />
                    <Typography variant="body2">Past heat cycle</Typography>
                  </Box>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={9}>
          <Card sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
          }}>
            <CardContent sx={{ p: 2 }}>
              <Box sx={{ height: 'calc(100vh - 350px)', p: 1 }}>
                <Calendar
                  localizer={localizer}
                  events={events}
                  startAccessor="start"
                  endAccessor="end"
                  style={{ height: '100%' }}
                  views={[Views.MONTH, Views.WEEK, Views.DAY]}
                  defaultView={Views.MONTH}
                  onSelectEvent={handleEventSelect}
                  eventPropGetter={eventStyleGetter}
                  popup={false} // Disable the built-in popup to avoid the useRef error
                  components={{
                    toolbar: CustomToolbar,
                    // Use a custom popup component
                    event: ({ event }) => (
                      <div onClick={() => handleEventSelect(event)}>
                        <span>{event.title}</span>
                      </div>
                    )
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Heat Cycle Analysis Chart */}
      <SubModuleChart
        title="Heat Cycle Analysis"
        description="Monthly distribution of heat cycles by intensity level"
        type="bar"
        data={chartData}
        height={300}
        moduleColor={theme.palette.secondary.main}
        onExport={() => console.log("Exporting chart")}
        onPrint={() => console.log("Printing chart")}
      />

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedEvent ? 'Edit Heat Record' : 'Add New Heat Record'}
        </DialogTitle>
        <DialogContent dividers>
          <Box component="form" sx={{ mt: 1 }}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="animal-select-label">Animal</InputLabel>
              <Select
                labelId="animal-select-label"
                id="animal-select"
                name="animalId"
                value={formData.animalId}
                label="Animal"
                onChange={handleInputChange}
              >
                {mockAnimals
                  .filter(animal => animal.gender.toLowerCase() === 'female')
                  .map(animal => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} (ID: {animal.id})
                    </MenuItem>
                  ))
                }
              </Select>
            </FormControl>

            <TextField
              margin="normal"
              fullWidth
              id="date"
              label="Date"
              type="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              InputLabelProps={{ shrink: true }}
            />

            <FormControl fullWidth margin="normal">
              <InputLabel id="intensity-select-label">Intensity</InputLabel>
              <Select
                labelId="intensity-select-label"
                id="intensity-select"
                name="intensity"
                value={formData.intensity}
                label="Intensity"
                onChange={handleInputChange}
              >
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="low">Low</MenuItem>
              </Select>
            </FormControl>

            <TextField
              margin="normal"
              fullWidth
              id="notes"
              label="Notes"
              name="notes"
              multiline
              rows={4}
              value={formData.notes}
              onChange={handleInputChange}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton onClick={handleSave} variant="contained" color="primary">
            {selectedEvent ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </SubModuleDashboard>
  );
};

// Custom toolbar component for the calendar
const CustomToolbar = (toolbar: any) => {
  const theme = useTheme();

  const goToBack = () => {
    toolbar.date.setMonth(toolbar.date.getMonth() - 1);
    toolbar.onNavigate('prev');
  };

  const goToNext = () => {
    toolbar.date.setMonth(toolbar.date.getMonth() + 1);
    toolbar.onNavigate('next');
  };

  const goToCurrent = () => {
    const now = new Date();
    toolbar.date.setMonth(now.getMonth());
    toolbar.date.setYear(now.getFullYear());
    toolbar.onNavigate('current');
  };

  const label = () => {
    const date = new Date(toolbar.date);
    return (
      <Typography variant="h6" fontWeight="medium">{format(date, 'MMMM yyyy')}</Typography>
    );
  };

  return (
    <Paper
      elevation={0}
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 1.5,
        mb: 2,
        borderRadius: 2,
        bgcolor: alpha(theme.palette.primary.main, 0.05)
      }}
    >
      <Box>
        <IconButton onClick={goToBack} size="small" color="primary">
          <ChevronLeft />
        </IconButton>
        <CustomButton
          onClick={goToCurrent}
          size="small"
          sx={{ mx: 1 }}
          startIcon={<Today />}
          variant="outlined"
        >
          Today
        </CustomButton>
        <IconButton onClick={goToNext} size="small" color="primary">
          <ChevronRight />
        </IconButton>
      </Box>
      <Box>
        {label()}
      </Box>
      <Box>
        <CustomButton
          size="small"
          onClick={() => toolbar.onView(Views.MONTH)}
          variant={toolbar.view === Views.MONTH ? "contained" : "text"}
          sx={{ minWidth: 80, mr: 1 }}
        >
          Month
        </CustomButton>
        <CustomButton
          size="small"
          onClick={() => toolbar.onView(Views.WEEK)}
          variant={toolbar.view === Views.WEEK ? "contained" : "text"}
          sx={{ minWidth: 80, mr: 1 }}
        >
          Week
        </CustomButton>
        <CustomButton
          size="small"
          onClick={() => toolbar.onView(Views.DAY)}
          variant={toolbar.view === Views.DAY ? "contained" : "text"}
          sx={{ minWidth: 80 }}
        >
          Day
        </CustomButton>
      </Box>
    </Paper>
  );
};

export default HeatCalendar;
