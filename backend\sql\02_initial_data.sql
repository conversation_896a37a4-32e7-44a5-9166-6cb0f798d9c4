-- AMPD Livestock Management System Initial Data
-- SQL Server (SSMS) Initial Data

-- Insert User Roles
INSERT INTO dbo.UserRoles (name, description, permissions)
VALUES 
('admin', 'Administrator with full access', 'all'),
('manager', 'Farm manager with most access', 'read,write,update'),
('staff', 'Regular staff with limited access', 'read,write'),
('veterinarian', 'Veterinarian with health record access', 'read,write,update:health'),
('viewer', 'Read-only access', 'read');

-- Insert Users (password is 'password123' hashed)
INSERT INTO dbo.Users (username, email, password, first_name, last_name, role_id)
VALUES 
('admin', '<EMAIL>', '$2a$10$JsRJUhSQXrCkHxQTCIwqguwE.CRM9EQVgB7gVQtF0e/0WqvVJ.IXm', 'Admin', 'User', 1),
('manager', '<EMAIL>', '$2a$10$JsRJUhSQXrCkHxQTCIwqguwE.CRM9EQVgB7gVQtF0e/0WqvVJ.IXm', 'Farm', 'Manager', 2),
('staff1', '<EMAIL>', '$2a$10$JsRJUhSQXrCkHxQTCIwqguwE.CRM9EQVgB7gVQtF0e/0WqvVJ.IXm', 'Staff', 'One', 3),
('vet', '<EMAIL>', '$2a$10$JsRJUhSQXrCkHxQTCIwqguwE.CRM9EQVgB7gVQtF0e/0WqvVJ.IXm', 'Veterinary', 'Doctor', 4),
('viewer', '<EMAIL>', '$2a$10$JsRJUhSQXrCkHxQTCIwqguwE.CRM9EQVgB7gVQtF0e/0WqvVJ.IXm', 'View', 'Only', 5);

-- Insert Locations
INSERT INTO dbo.Locations (name, type, capacity, current_count, description, created_by)
VALUES 
('North Paddock', 'paddock', 50, 20, 'Main cattle grazing area', 1),
('South Paddock', 'paddock', 40, 15, 'Secondary grazing area', 1),
('Barn A', 'barn', 30, 10, 'Main livestock barn', 1),
('Pen 1', 'pen', 10, 5, 'Holding pen for new arrivals', 1),
('Quarantine Area', 'quarantine', 5, 0, 'Isolation area for sick animals', 1);

-- Insert Animals (Cattle)
INSERT INTO dbo.Animals (tag_number, name, animal_type, breed, gender, birth_date, weight, status, health_status, location_id, purchase_date, purchase_price, notes, created_by)
VALUES 
('CTL-2023-001', 'Bella', 'cattle', 'Angus', 'female', '2021-05-15', 650.5, 'active', 'healthy', 1, '2022-01-10', 1200.00, 'Prime breeding cow', 1),
('CTL-2023-002', 'Thor', 'cattle', 'Hereford', 'male', '2020-03-20', 950.0, 'active', 'healthy', 1, '2021-06-15', 1800.00, 'Breeding bull', 1),
('CTL-2023-003', 'Daisy', 'cattle', 'Holstein', 'female', '2022-01-10', 580.0, 'active', 'healthy', 2, NULL, NULL, 'Born on farm', 1),
('CTL-2023-004', 'Max', 'cattle', 'Angus', 'male', '2022-06-05', 450.0, 'active', 'healthy', 2, NULL, NULL, 'Born on farm', 1),
('CTL-2023-005', 'Luna', 'cattle', 'Jersey', 'female', '2021-11-12', 520.0, 'active', 'healthy', 3, '2022-03-20', 1100.00, 'Dairy cow', 1);

-- Insert Animals (Sheep)
INSERT INTO dbo.Animals (tag_number, name, animal_type, breed, gender, birth_date, weight, status, health_status, location_id, purchase_date, purchase_price, notes, created_by)
VALUES 
('SHP-2023-001', 'Woolly', 'sheep', 'Merino', 'female', '2022-02-10', 65.5, 'active', 'healthy', 3, '2022-08-15', 250.00, 'Wool producer', 1),
('SHP-2023-002', 'Fluff', 'sheep', 'Dorper', 'female', '2022-04-18', 70.0, 'active', 'healthy', 3, '2022-08-15', 280.00, 'Meat producer', 1),
('SHP-2023-003', 'Rambo', 'sheep', 'Merino', 'male', '2021-09-22', 85.0, 'active', 'healthy', 3, '2022-01-10', 350.00, 'Breeding ram', 1);

-- Insert Animals (Goats)
INSERT INTO dbo.Animals (tag_number, name, animal_type, breed, gender, birth_date, weight, status, health_status, location_id, purchase_date, purchase_price, notes, created_by)
VALUES 
('GT-2023-001', 'Nibbles', 'goat', 'Boer', 'female', '2022-03-15', 45.0, 'active', 'healthy', 4, '2022-09-10', 200.00, 'Meat goat', 1),
('GT-2023-002', 'Billy', 'goat', 'Boer', 'male', '2021-08-20', 65.0, 'active', 'healthy', 4, '2022-02-15', 280.00, 'Breeding buck', 1);

-- Insert Weight Records
INSERT INTO dbo.WeightRecords (animal_id, weight, height, date, notes, created_by)
VALUES 
(1, 620.5, 145.0, '2023-01-15', 'Regular weighing', 1),
(1, 650.5, 145.0, '2023-04-15', 'Good weight gain', 1),
(2, 920.0, 155.0, '2023-01-15', 'Regular weighing', 1),
(2, 950.0, 155.0, '2023-04-15', 'Stable weight', 1),
(3, 540.0, 140.0, '2023-01-15', 'Regular weighing', 1),
(3, 580.0, 142.0, '2023-04-15', 'Good growth rate', 1);

-- Insert Feed Inventory
INSERT INTO dbo.FeedInventory (name, type, quantity, unit, cost_per_unit, supplier, purchase_date, expiration_date, storage_location, created_by)
VALUES 
('Premium Hay', 'hay', 500.0, 'bale', 15.00, 'Farm Supplies Inc.', '2023-03-01', '2023-12-31', 'Hay Shed', 1),
('Cattle Feed Mix', 'grain', 2000.0, 'kg', 0.50, 'Nutrition Plus', '2023-03-15', '2023-09-15', 'Feed Silo 1', 1),
('Sheep Feed', 'grain', 1000.0, 'kg', 0.45, 'Nutrition Plus', '2023-03-15', '2023-09-15', 'Feed Silo 2', 1),
('Mineral Supplement', 'supplement', 100.0, 'kg', 2.50, 'Animal Health Co.', '2023-03-10', '2024-03-10', 'Storage Room', 1);

-- Insert Feeding Records
INSERT INTO dbo.FeedingRecords (animal_id, feed_id, quantity, unit, feeding_date, notes, created_by)
VALUES 
(1, 2, 5.0, 'kg', '2023-04-15 08:00:00', 'Morning feeding', 3),
(2, 2, 7.0, 'kg', '2023-04-15 08:00:00', 'Morning feeding', 3),
(3, 2, 5.0, 'kg', '2023-04-15 08:00:00', 'Morning feeding', 3),
(6, 3, 1.5, 'kg', '2023-04-15 08:30:00', 'Morning feeding', 3),
(7, 3, 1.5, 'kg', '2023-04-15 08:30:00', 'Morning feeding', 3);

-- Insert Health Records
INSERT INTO dbo.HealthRecords (animal_id, record_type, date, diagnosis, treatment, medication, dosage, veterinarian, cost, status, notes, created_by)
VALUES 
(1, 'vaccination', '2023-02-10 10:00:00', NULL, 'Annual vaccination', 'Cattle 7-way vaccine', '10ml', 'Dr. Smith', 45.00, 'completed', 'Routine vaccination', 4),
(2, 'vaccination', '2023-02-10 10:30:00', NULL, 'Annual vaccination', 'Cattle 7-way vaccine', '10ml', 'Dr. Smith', 45.00, 'completed', 'Routine vaccination', 4),
(3, 'check-up', '2023-03-15 14:00:00', 'Healthy', 'None required', NULL, NULL, 'Dr. Smith', 30.00, 'completed', 'Routine check-up', 4),
(5, 'treatment', '2023-04-01 09:00:00', 'Minor wound', 'Wound cleaning and dressing', 'Antibiotic ointment', 'As needed', 'Dr. Johnson', 65.00, 'completed', 'Cut on right front leg', 4),
(8, 'vaccination', '2023-02-15 11:00:00', NULL, 'Annual vaccination', 'Sheep vaccine', '5ml', 'Dr. Smith', 25.00, 'completed', 'Routine vaccination', 4);

-- Insert Heat Cycles
INSERT INTO dbo.HeatCycles (animal_id, start_date, end_date, intensity, symptoms, action, notes, created_by)
VALUES 
(1, '2023-03-10', '2023-03-12', 'high', 'Mounting behavior, restlessness', 'breed', 'Good timing for breeding', 2),
(3, '2023-03-15', '2023-03-17', 'medium', 'Slight restlessness', 'monitor', 'Too young for breeding', 2),
(5, '2023-03-20', '2023-03-22', 'high', 'Clear mucus discharge, mounting behavior', 'breed', 'Ready for breeding', 2);

-- Insert Breeding Records
INSERT INTO dbo.BreedingRecords (female_id, male_id, breeding_date, breeding_method, expected_due_date, pregnancy_check_date, pregnancy_result, status, notes, created_by)
VALUES 
(1, 2, '2023-03-12', 'natural', '2023-12-21', '2023-04-12', 'positive', 'in-progress', 'Successful breeding', 2),
(5, 2, '2023-03-22', 'natural', '2023-12-31', '2023-04-22', 'positive', 'in-progress', 'Successful breeding', 2);

-- Insert Artificial Insemination Record
INSERT INTO dbo.BreedingRecords (female_id, breeding_date, breeding_method, ai_batch_number, ai_technician, ai_cost, expected_due_date, pregnancy_check_date, pregnancy_result, status, notes, created_by)
VALUES 
(3, '2023-04-10', 'artificial', 'AI-2023-042', 'Mike Johnson', 150.00, '2024-01-19', NULL, NULL, 'planned', 'First AI attempt', 2);