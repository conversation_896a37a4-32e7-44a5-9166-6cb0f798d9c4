/**
 * Utility functions for consistent color blending across the application
 */

import { alpha } from '@mui/material';

/**
 * Returns a linear gradient background style for consistent page backgrounds
 * @param primaryColor - The primary theme color
 * @param secondaryColor - The secondary theme color
 * @param primaryOpacity - Opacity for the primary color (default: 0.05)
 * @param secondaryOpacity - Opacity for the secondary color (default: 0.1)
 * @returns CSS background style
 */
export const getPageGradientBackground = (
  primaryColor: string,
  secondaryColor: string,
  primaryOpacity = 0.05,
  secondaryOpacity = 0.1
) => {
  return {
    background: `linear-gradient(135deg, ${alpha(primaryColor, primaryOpacity)}, ${alpha(secondaryColor, secondaryOpacity)})`,
    backgroundSize: '100% 100%',
    backgroundAttachment: 'fixed'
  };
};

/**
 * Returns a linear gradient background style for card headers
 * @param primaryColor - The primary theme color
 * @param secondaryColor - The secondary theme color
 * @param primaryOpacity - Opacity for the primary color (default: 0.8)
 * @param secondaryOpacity - Opacity for the secondary color (default: 0.7)
 * @returns CSS background style
 */
export const getCardHeaderGradient = (
  primaryColor: string,
  secondaryColor: string,
  primaryOpacity = 0.8,
  secondaryOpacity = 0.7
) => {
  return {
    background: `linear-gradient(135deg, ${alpha(primaryColor, primaryOpacity)}, ${alpha(secondaryColor, secondaryOpacity)})`,
    color: 'white'
  };
};

/**
 * Returns a linear gradient background style for card content
 * @param primaryColor - The primary theme color
 * @param secondaryColor - The secondary theme color
 * @param primaryOpacity - Opacity for the primary color (default: 0.05)
 * @param secondaryOpacity - Opacity for the secondary color (default: 0.03)
 * @returns CSS background style
 */
export const getCardContentGradient = (
  primaryColor: string,
  secondaryColor: string,
  primaryOpacity = 0.05,
  secondaryOpacity = 0.03
) => {
  return {
    background: `linear-gradient(135deg, ${alpha(primaryColor, primaryOpacity)}, ${alpha(secondaryColor, secondaryOpacity)})`,
  };
};

/**
 * Returns a linear gradient background style for section dividers
 * @param primaryColor - The primary theme color
 * @param secondaryColor - The secondary theme color
 * @returns CSS background style
 */
export const getSectionDividerGradient = (
  primaryColor: string,
  secondaryColor: string
) => {
  return {
    background: `linear-gradient(to right, ${primaryColor}, ${secondaryColor})`,
    height: '3px',
    border: 'none',
    margin: '16px 0'
  };
};

/**
 * Returns a linear gradient background style for buttons
 * @param primaryColor - The primary theme color
 * @param secondaryColor - The secondary theme color
 * @returns CSS background style
 */
export const getButtonGradient = (
  primaryColor: string,
  secondaryColor: string
) => {
  return {
    background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColor})`,
    color: 'white',
    '&:hover': {
      background: `linear-gradient(135deg, ${secondaryColor}, ${primaryColor})`,
    }
  };
};
