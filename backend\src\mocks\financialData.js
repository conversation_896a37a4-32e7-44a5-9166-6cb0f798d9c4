exports.mockExpenses = [
  {
    id: 1,
    date: '2023-03-01',
    category: 'Feed',
    description: 'Monthly lucerne purchase',
    amount: 25000.00,
    vendor: 'Afgri Animal Feeds',
    paymentMethod: 'EFT',
    receiptNumber: 'AF-2023-0301',
    notes: 'Premium quality lucerne'
  },
  {
    id: 2,
    date: '2023-03-10',
    category: 'Veterinary',
    description: 'Quarterly herd health check',
    amount: 8500.00,
    vendor: 'Onderstepoort Veterinary Services',
    paymentMethod: 'Credit Card',
    receiptNumber: 'OVS-10032023',
    notes: '<PERSON><PERSON> <PERSON>'
  },
  {
    id: 3,
    date: '2023-03-12',
    category: 'Veterinary',
    description: 'Emergency call - Nguni cow',
    amount: 3500.00,
    vendor: 'Onderstepoort Veterinary Services',
    paymentMethod: 'Credit Card',
    receiptNumber: 'OVS-12032023',
    notes: 'Dr. Botha'
  },
  {
    id: 4,
    date: '2023-03-15',
    category: 'Equipment',
    description: 'Replacement parts for feed mixer',
    amount: 320.50,
    vendor: 'Farm Equipment Ltd',
    paymentMethod: 'Credit Card',
    receiptNumber: 'FEL-2023-0315',
    notes: 'Scheduled maintenance'
  },
  {
    id: 5,
    date: '2023-03-20',
    category: 'Supplies',
    description: 'Ear tags and applicator',
    amount: 145.75,
    vendor: 'Agricultural Supply Co',
    paymentMethod: 'Check',
    receiptNumber: 'ASC-20032023',
    notes: '200 tags for new calves'
  }
];

exports.mockRevenue = [
  {
    id: 1,
    date: '2023-03-05',
    category: 'Milk',
    description: 'Weekly milk collection',
    amount: 18500.00,
    customer: 'Clover Dairy',
    paymentMethod: 'EFT',
    invoiceNumber: 'INV-2023-0305',
    notes: '2300 liters @ R8.04/liter'
  },
  {
    id: 2,
    date: '2023-03-12',
    category: 'Milk',
    description: 'Weekly milk collection',
    amount: 19200.00,
    customer: 'Clover Dairy',
    paymentMethod: 'EFT',
    invoiceNumber: 'INV-2023-0312',
    notes: '2400 liters @ R8.00/liter'
  },
  {
    id: 3,
    date: '2023-03-15',
    category: 'Livestock',
    description: 'Sale of 5 Bonsmara cattle',
    amount: 85000.00,
    customer: 'Beefmaster Kimberley',
    paymentMethod: 'EFT',
    invoiceNumber: 'INV-2023-0315',
    notes: '5 Bonsmara cattle @ R17,000 each'
  },
  {
    id: 4,
    date: '2023-03-19',
    category: 'Milk',
    description: 'Weekly milk collection',
    amount: 1880.00,
    customer: 'Dairy Processing Co',
    paymentMethod: 'Direct Deposit',
    invoiceNumber: 'INV-2023-0319',
    notes: '2350 liters @ $0.80/liter'
  },
  {
    id: 5,
    date: '2023-03-25',
    category: 'Wool',
    description: 'Spring shearing',
    amount: 750.00,
    customer: 'Textile Mills Inc',
    paymentMethod: 'Direct Deposit',
    invoiceNumber: 'INV-2023-0325',
    notes: '150kg of raw wool'
  }
];

exports.mockBudgets = [
  {
    id: 1,
    year: 2023,
    month: 3,
    category: 'Feed',
    budgetedAmount: 5000.00,
    actualAmount: 4850.00,
    variance: 150.00,
    notes: 'Under budget due to bulk purchase discount'
  },
  {
    id: 2,
    year: 2023,
    month: 3,
    category: 'Veterinary',
    budgetedAmount: 1000.00,
    actualAmount: 1250.00,
    variance: -250.00,
    notes: 'Over budget due to emergency call for cow #13'
  },
  {
    id: 3,
    year: 2023,
    month: 3,
    category: 'Equipment',
    budgetedAmount: 1500.00,
    actualAmount: 1320.50,
    variance: 179.50,
    notes: 'Maintenance costs lower than expected'
  },
  {
    id: 4,
    year: 2023,
    month: 3,
    category: 'Labor',
    budgetedAmount: 8000.00,
    actualAmount: 8000.00,
    variance: 0.00,
    notes: 'On budget'
  },
  {
    id: 5,
    year: 2023,
    month: 3,
    category: 'Supplies',
    budgetedAmount: 500.00,
    actualAmount: 645.75,
    variance: -145.75,
    notes: 'Additional ear tags purchased for upcoming calving season'
  }
];