exports.mockHealthRecords = [
  {
    id: 'HR001',
    animalId: 1,
    date: '2023-01-15',
    type: 'checkup',
    description: 'Regular health checkup',
    performedBy: 'Dr<PERSON> <PERSON>',
    cost: 75.00,
    notes: 'Animal appears healthy with no signs of illness',
    status: 'completed'
  },
  {
    id: 'HR002',
    animalId: 2,
    date: '2023-02-10',
    type: 'treatment',
    description: 'Treatment for minor infection',
    performedBy: 'Dr. <PERSON>',
    cost: 120.50,
    notes: 'Administered antibiotics, follow up in 7 days',
    followUpDate: '2023-02-17',
    status: 'scheduled'
  },
  {
    id: 'HR003',
    animalId: 3,
    date: '2023-03-05',
    type: 'vaccination',
    description: 'Annual vaccination',
    performedBy: 'Dr<PERSON> <PERSON>',
    cost: 95.00,
    notes: 'Administered BVD and Blackleg vaccines',
    status: 'completed'
  },
  {
    id: 'HR004',
    animalId: 4,
    date: '2023-03-20',
    type: 'surgery',
    description: 'Minor surgical procedure',
    performedBy: 'Dr. <PERSON>',
    cost: 350.00,
    notes: 'Procedure went well, animal recovering',
    followUpDate: '2023-03-27',
    status: 'cancelled'
  }
];

exports.mockVaccinations = [
  {
    id: 'VAC001',
    animalId: 1,
    vaccineType: 'BVD',
    date: '2022-11-10',
    expiryDate: '2023-11-10',
    batchNumber: 'VAC2022-112',
    administeredBy: 'Dr. Smith',
    notes: 'Annual vaccination'
  },
  {
    id: 'VAC002',
    animalId: 2,
    vaccineType: 'Blackleg',
    date: '2022-10-05',
    expiryDate: '2023-10-05',
    batchNumber: 'VAC2022-098',
    administeredBy: 'Dr. Johnson',
    notes: 'Initial vaccination'
  },
  {
    id: 'VAC003',
    animalId: 3,
    vaccineType: 'Leptospirosis',
    date: '2023-01-15',
    expiryDate: '2024-01-15',
    batchNumber: 'VAC2023-015',
    administeredBy: 'Dr. Williams',
    notes: 'Booster shot'
  }
];

exports.mockHealthStats = {
  totalRecords: 4,
  byType: {
    vaccination: 1,
    treatment: 1,
    checkup: 1,
    surgery: 1,
    quarantine: 0
  },
  byStatus: {
    completed: 2,
    scheduled: 1,
    requiresFollowup: 1
  },
  pendingCount: 2
};
