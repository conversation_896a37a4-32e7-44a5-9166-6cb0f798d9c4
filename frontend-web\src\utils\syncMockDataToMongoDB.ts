import { mockAnimals } from '../mocks/animalData';
import { mockHealthRecords } from '../mocks/healthData';
import { mockBreedingRecords } from '../mocks/breedingData';
import { mockFeedingRecords } from '../mocks/feedingData';
import { mockTransactions, mockSalesRecords } from '../mocks/financialData';
import { mockInventory } from '../mocks/inventoryData';

/**
 * Syncs mock data to MongoDB
 * @param mongoClient - MongoDB client instance
 * @returns Promise<boolean> - true if sync was successful
 */
export const syncMockDataToMongoDB = async (mongoClient: any): Promise<boolean> => {
  try {
    console.log('Starting mock data sync to MongoDB...');

    // Check if MongoDB is connected
    if (!mongoClient || !mongoClient.db) {
      console.error('MongoDB client is not connected');
      return false;
    }

    const db = mongoClient.db();

    // Define collections to sync
    const collectionsToSync = [
      { name: 'animals', data: mockAnimals },
      { name: 'health_records', data: mockHealthRecords },
      { name: 'breeding_records', data: mockBreedingRecords },
      { name: 'feeding_records', data: mockFeedingRecords },
      { name: 'transactions', data: mockTransactions },
      { name: 'sales_records', data: mockSalesRecords },
      { name: 'inventory', data: mockInventory }
    ];

    // Sync each collection
    for (const { name, data } of collectionsToSync) {
      const collection = db.collection(name);

      // Check if collection is empty
      const count = await collection.countDocuments();

      if (count === 0) {
        // Collection is empty, insert mock data
        console.log(`Syncing mock data to ${name} collection...`);

        if (Array.isArray(data) && data.length > 0) {
          // Add timestamps to each document
          const dataWithTimestamps = data.map(item => ({
            ...item,
            createdAt: new Date(),
            updatedAt: new Date()
          }));

          // Insert data
          await collection.insertMany(dataWithTimestamps);
          console.log(`Successfully synced ${dataWithTimestamps.length} documents to ${name} collection`);
        } else {
          console.warn(`No mock data available for ${name} collection`);
        }
      } else {
        console.log(`Collection ${name} already has ${count} documents, skipping sync`);
      }
    }

    console.log('Mock data sync completed successfully');
    return true;
  } catch (error) {
    console.error('Error syncing mock data to MongoDB:', error);
    return false;
  }
};

/**
 * Checks if a collection has data
 * @param mongoClient - MongoDB client instance
 * @param collectionName - Name of the collection to check
 * @returns Promise<boolean> - true if collection has data
 */
export const hasCollectionData = async (mongoClient: any, collectionName: string): Promise<boolean> => {
  try {
    if (!mongoClient || !mongoClient.db) {
      return false;
    }

    const db = mongoClient.db();
    const collection = db.collection(collectionName);
    const count = await collection.countDocuments();

    return count > 0;
  } catch (error) {
    console.error(`Error checking if collection ${collectionName} has data:`, error);
    return false;
  }
};

/**
 * Gets real-time statistics from MongoDB
 * @param mongoClient - MongoDB client instance
 * @returns Promise<any> - Statistics object
 */
export const getRealTimeStats = async (mongoClient: any): Promise<any> => {
  try {
    if (!mongoClient || !mongoClient.db) {
      throw new Error('MongoDB client is not connected');
    }

    const db = mongoClient.db();

    // Get animal statistics
    const animals = db.collection('animals');
    const totalAnimals = await animals.countDocuments();
    const activeAnimals = await animals.countDocuments({ status: 'Active' });
    const inactiveAnimals = await animals.countDocuments({ status: { $ne: 'Active' } });
    const healthyAnimals = await animals.countDocuments({ healthStatus: 'Healthy' });
    const sickAnimals = await animals.countDocuments({ healthStatus: 'Sick' });
    const pregnantAnimals = await animals.countDocuments({ pregnancyStatus: 'Pregnant' });

    // Get species breakdown
    const speciesPipeline = [
      { $group: { _id: '$species', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ];
    const speciesResult = await animals.aggregate(speciesPipeline).toArray();
    const bySpecies: Record<string, number> = speciesResult.reduce((acc: Record<string, number>, curr: any) => {
      acc[curr._id] = curr.count;
      return acc;
    }, {});

    // Get health records statistics
    const healthRecords = db.collection('health_records');
    const totalHealthRecords = await healthRecords.countDocuments();

    // Get breeding records statistics
    const breedingRecords = db.collection('breeding_records');
    const totalBreedingRecords = await breedingRecords.countDocuments();

    // Get financial records statistics
    const transactions = db.collection('transactions');
    const totalTransactions = await transactions.countDocuments();

    const salesRecords = db.collection('sales_records');
    const totalSalesRecords = await salesRecords.countDocuments();

    // Get all collections
    const collections = await db.listCollections().toArray();

    // Calculate total documents across all collections
    let totalDocuments = 0;
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      totalDocuments += count;
    }

    // Return compiled statistics
    return {
      collections,
      totalDocuments,
      totalAnimals,
      activeAnimals,
      inactiveAnimals,
      healthyAnimals,
      sickAnimals,
      pregnantAnimals,
      bySpecies,
      totalHealthRecords,
      totalBreedingRecords,
      totalTransactions,
      totalSalesRecords,
      healthPercentage: totalAnimals > 0 ? Math.round((healthyAnimals / totalAnimals) * 100) : 0,
      byHealth: {
        healthy: healthyAnimals,
        sick: sickAnimals,
        pregnant: pregnantAnimals
      }
    };
  } catch (error) {
    console.error('Error getting real-time statistics:', error);
    throw error;
  }
};
