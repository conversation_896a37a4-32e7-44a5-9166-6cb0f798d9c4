import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';
import { useThemeContext } from '../../../contexts/ThemeContext';
import { alpha } from '@mui/material';

const ReportsDashboard: React.FC = () => {
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const reportCategories = [
    {
      name: 'Analysis Reports',
      icon: '📊',
      path: `${ROUTES.REPORTS}/analysis`,
      description: 'Comprehensive analysis of your livestock data',
      updates: 3
    },
    {
      name: 'Performance Metrics',
      icon: '📈',
      path: `${ROUTES.REPORTS}/performance`,
      description: 'Track livestock performance and growth metrics',
      updates: 2
    },
    {
      name: 'Financial Reports',
      icon: '💰',
      path: `${ROUTES.REPORTS}/financial`,
      description: 'Review financial statements and projections',
      updates: 1
    },
    {
      name: 'Health Statistics',
      icon: '🏥',
      path: `${ROUTES.REPORTS}/health`,
      description: 'Monitor health trends and vaccination status',
      updates: 2
    },
    {
      name: 'Market Analysis',
      icon: '📈',
      path: `${ROUTES.REPORTS}/market`,
      description: 'Analyze market trends and pricing data',
      updates: 0
    }
  ];

  const recentReports = [
    { name: 'Q2 Financial Summary', date: '2024-03-15', type: 'Financial' },
    { name: 'Health Inspection Report', date: '2024-03-14', type: 'Health' },
    { name: 'Market Trend Analysis', date: '2024-03-13', type: 'Market' }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Reports & Analytics</h1>
          <p className="text-gray-600">Generate insights and analyze your farm's performance</p>
        </div>
        <Link to={`${ROUTES.REPORTS}/analysis`}>
          <button
            className="text-white px-4 py-2 rounded-lg transition-colors hover:opacity-90"
            style={{
              backgroundColor: themeColor.primary
            }}>
            Generate New Report
          </button>
        </Link>
      </div>

      {/* Report Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reportCategories.map((category) => (
          <Link
            key={category.name}
            to={category.path}
            className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-start justify-between">
              <span className="text-2xl">{category.icon}</span>
              {category.updates > 0 && (
                <span
                  className="px-2 py-1 rounded-full text-sm"
                  style={{
                    backgroundColor: alpha(themeColor.primary, 0.1),
                    color: themeColor.secondary
                  }}>
                  {category.updates} new
                </span>
              )}
            </div>
            <h3 className="text-lg font-semibold mt-4">{category.name}</h3>
            <p className="text-gray-600 text-sm mt-2">{category.description}</p>
          </Link>
        ))}
      </div>

      {/* Recent Reports */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Recent Reports</h2>
          <Link
            to={`${ROUTES.REPORTS}/all`}
            className="text-sm hover:opacity-80"
            style={{
              color: themeColor.primary
            }}
          >
            View All Reports
          </Link>
        </div>
        <div className="space-y-4">
          {recentReports.map((report, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-4 hover:bg-gray-50 rounded-lg transition-colors"
            >
              <div>
                <h3 className="font-medium">{report.name}</h3>
                <p className="text-sm text-gray-500">Generated on {report.date}</p>
              </div>
              <div className="flex items-center space-x-3">
                <span className="px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800">
                  {report.type}
                </span>
                <button className="text-gray-400 hover:text-gray-600">
                  <span>⬇️</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Analytics Preview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Performance Trends</h2>
          <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
            {/* Placeholder for charts/graphs */}
            <p className="text-gray-500">Performance Chart Visualization</p>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Key Insights</h2>
          <div className="space-y-4">
            <div className="p-4 rounded-lg" style={{ backgroundColor: alpha(themeColor.primary, 0.1) }}>
              <h3 className="font-medium" style={{ color: themeColor.secondary }}>Positive Growth</h3>
              <p className="text-sm mt-1" style={{ color: themeColor.primary }}>Livestock health metrics improved by 12% this quarter</p>
            </div>
            <div className="p-4 rounded-lg" style={{ backgroundColor: alpha(themeColor.primary, 0.05) }}>
              <h3 className="font-medium" style={{ color: themeColor.secondary }}>Action Required</h3>
              <p className="text-sm mt-1" style={{ color: themeColor.primary }}>Feed inventory approaching minimum threshold</p>
            </div>
            <div className="p-4 rounded-lg" style={{ backgroundColor: alpha(themeColor.primary, 0.15) }}>
              <h3 className="font-medium" style={{ color: themeColor.secondary }}>Market Insight</h3>
              <p className="text-sm mt-1" style={{ color: themeColor.primary }}>Favorable market conditions predicted for next month</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportsDashboard;