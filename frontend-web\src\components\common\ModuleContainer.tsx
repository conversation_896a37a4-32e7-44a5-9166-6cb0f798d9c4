import React, { ReactNode } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { getModuleStyles, getBackgroundPatternStyle } from '../../utils/moduleStyles';
import { ModuleBackgroundRotator } from './';

interface ModuleContainerProps {
  children: ReactNode;
  module: string;
  backgroundImage?: string;
}

/**
 * ModuleContainer component
 * Provides consistent styling for all modules
 */
const ModuleContainer: React.FC<ModuleContainerProps> = ({
  children,
  module,
  backgroundImage
}) => {
  const theme = useTheme();
  const styles = getModuleStyles(module, theme);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  // Get the appropriate color scheme based on the module
  const isExcludedModule = ['dashboard', 'login', 'commercial'].includes(module.toLowerCase());

  // Use teal for excluded modules, blue for all others
  const primaryColor = isExcludedModule ? '#3AA99F' : '#4A6FA5';
  const secondaryColor = isExcludedModule ? '#2A8A82' : '#3A5A8C';

  return (
    <Box sx={{ position: 'relative', minHeight: '100%' }}>
      {/* Background Header with Rotating Images */}
      <Box sx={{ ...styles.headerStyle, overflow: 'hidden' }}>
        <ModuleBackgroundRotator
          module={module}
          interval={10000}
          opacity={0.85}
        />
      </Box>

      {/* Background Pattern */}
      <Box sx={getBackgroundPatternStyle(0.05)} />

      {/* Content Container */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{
          ...styles.contentStyle,
          // Add subtle colored border to enhance the design
          borderLeft: `1px solid ${alpha(primaryColor, 0.1)}`,
          borderRight: `1px solid ${alpha(primaryColor, 0.1)}`,
        }}
      >
        {children}
      </motion.div>
    </Box>
  );
};

export default ModuleContainer;
