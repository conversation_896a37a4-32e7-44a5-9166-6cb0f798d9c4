import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ModuleHeader from './ModuleHeader';
import { ThemeProvider } from '@mui/material/styles';
import theme from '../../theme/theme';

// Mock the framer-motion animations to avoid test issues
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    },
    AnimatePresence: ({ children }: any) => <>{children}</>,
  };
});

// Mock the background themes
jest.mock('../../utils/backgroundThemes', () => ({
  getModuleTheme: jest.fn().mockReturnValue({
    backgroundImage: 'test-image.jpg',
    color: '#3AA99F',
  }),
}));

describe('ModuleHeader Component', () => {
  const renderWithTheme = (ui: React.ReactElement) => {
    return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
  };

  test('renders title and subtitle correctly', () => {
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        subtitle="Test Subtitle"
        module="animals"
      />
    );

    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
  });

  test('renders action button and triggers callback when clicked', () => {
    const mockAction = jest.fn();
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        actionLabel="Test Action"
        onAction={mockAction}
      />
    );

    const actionButton = screen.getByText('Test Action');
    expect(actionButton).toBeInTheDocument();

    fireEvent.click(actionButton);
    expect(mockAction).toHaveBeenCalledTimes(1);
  });

  test('renders secondary action button and triggers callback when clicked', () => {
    const mockSecondaryAction = jest.fn();
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        secondaryAction={{
          label: 'Secondary Action',
          onClick: mockSecondaryAction,
        }}
      />
    );

    const secondaryButton = screen.getByText('Secondary Action');
    expect(secondaryButton).toBeInTheDocument();

    fireEvent.click(secondaryButton);
    expect(mockSecondaryAction).toHaveBeenCalledTimes(1);
  });

  test('renders breadcrumbs correctly', () => {
    const breadcrumbs = [
      { label: 'Home', path: '/' },
      { label: 'Animals', path: '/animals' },
      { label: 'Details' },
    ];

    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        breadcrumbs={breadcrumbs}
      />
    );

    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Animals')).toBeInTheDocument();
    expect(screen.getByText('Details')).toBeInTheDocument();
  });

  test('renders status chip when status and statusText are provided', () => {
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        status="success"
        statusText="Active"
      />
    );

    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  test('toggles expanded state when expandable is true', () => {
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        subtitle="Test Subtitle"
        expandable={true}
      />
    );

    // Initially expanded
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();

    // Find and click the expand/collapse button
    const expandButton = screen.getByRole('button');
    fireEvent.click(expandButton);

    // After clicking, the subtitle should not be visible
    expect(screen.queryByText('Test Subtitle')).not.toBeInTheDocument();
  });

  test('renders tags correctly', () => {
    const tags = [
      { label: 'Tag 1', color: 'primary' as const },
      { label: 'Tag 2', color: 'secondary' as const },
    ];

    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        tags={tags}
      />
    );

    expect(screen.getByText('Tag 1')).toBeInTheDocument();
    expect(screen.getByText('Tag 2')).toBeInTheDocument();
  });

  test('renders info tooltip when provided', () => {
    renderWithTheme(
      <ModuleHeader
        title="Test Title"
        infoTooltip="Additional information"
      />
    );

    // Info icon should be present
    const infoIcon = screen.getByRole('button');
    expect(infoIcon).toBeInTheDocument();
  });
});
