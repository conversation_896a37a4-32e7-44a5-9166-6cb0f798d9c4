import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, TextField, Grid, FormControl, InputLabel, Select, MenuItem, Chip, IconButton, Divider, Alert, CircularProgress, Tooltip } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Add,
  Delete,
  Edit,
  Save,
  Cancel,
  Refresh,
  Link,
  LinkOff,
  QrCode,
  Print,
  Download
} from '../../utils/iconImports';
import { useTheme, alpha } from '@mui/material/styles';

interface RFIDTag {
  id: string;
  tagNumber: string;
  status: 'active' | 'inactive' | 'lost' | 'damaged';
  batteryLevel: number;
  lastSeen?: string;
  animalId?: string;
  animalName?: string;
  notes?: string;
}

interface RFIDManagementProps {
  onRefresh?: () => void;
}

const RFIDManagement: React.FC<RFIDManagementProps> = ({ onRefresh }) => {
  const [tags, setTags] = useState<RFIDTag[]>([
    {
      id: '1',
      tagNumber: 'RFID-001',
      status: 'active',
      batteryLevel: 85,
      lastSeen: '2024-03-15T08:45:22',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      notes: 'Attached to left ear'
    },
    {
      id: '2',
      tagNumber: 'RFID-002',
      status: 'active',
      batteryLevel: 72,
      lastSeen: '2024-03-15T09:12:05',
      animalId: 'ANM002',
      animalName: 'Kgosi',
      notes: 'Attached to collar'
    },
    {
      id: '3',
      tagNumber: 'RFID-003',
      status: 'inactive',
      batteryLevel: 15,
      lastSeen: '2024-03-14T07:30:18',
      animalId: 'ANM003',
      animalName: 'Lesedi',
      notes: 'Battery needs replacement'
    },
    {
      id: '4',
      tagNumber: 'RFID-004',
      status: 'lost',
      batteryLevel: 0,
      lastSeen: '2024-03-10T10:05:33',
      animalId: 'ANM004',
      animalName: 'Pule',
      notes: 'Last seen in Boma 1'
    },
    {
      id: '5',
      tagNumber: 'RFID-005',
      status: 'damaged',
      batteryLevel: 45,
      lastSeen: '2024-03-12T08:22:47',
      animalId: 'ANM005',
      animalName: 'Lerato',
      notes: 'Physical damage to casing'
    }
  ]);

  const [editingTag, setEditingTag] = useState<RFIDTag | null>(null);
  const [newTag, setNewTag] = useState<Partial<RFIDTag>>({
    tagNumber: '',
    status: 'active',
    batteryLevel: 100
  });
  const [showNewTagForm, setShowNewTagForm] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const theme = useTheme();

  // Filter tags based on status
  const filteredTags = filterStatus === 'all'
    ? tags
    : tags.filter(tag => tag.status === filterStatus);

  const handleEditTag = (tag: RFIDTag) => {
    setEditingTag({ ...tag });
  };

  const handleSaveEdit = () => {
    if (!editingTag) return;

    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setTags(prevTags =>
        prevTags.map(tag =>
          tag.id === editingTag.id ? editingTag : tag
        )
      );
      setEditingTag(null);
      setSuccess('Tag updated successfully');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);
  };

  const handleCancelEdit = () => {
    setEditingTag(null);
  };

  const handleDeleteTag = (id: string) => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setTags(prevTags => prevTags.filter(tag => tag.id !== id));
      setSuccess('Tag deleted successfully');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);
  };

  const handleAddNewTag = () => {
    if (!newTag.tagNumber) {
      setError('Tag number is required');
      return;
    }

    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      const newTagWithId: RFIDTag = {
        id: `${tags.length + 1}`,
        tagNumber: newTag.tagNumber || '',
        status: (newTag.status as 'active' | 'inactive' | 'lost' | 'damaged') || 'active',
        batteryLevel: newTag.batteryLevel || 100,
        animalId: newTag.animalId,
        animalName: newTag.animalName,
        notes: newTag.notes
      };

      setTags(prevTags => [...prevTags, newTagWithId]);
      setNewTag({
        tagNumber: '',
        status: 'active',
        batteryLevel: 100
      });
      setShowNewTagForm(false);
      setSuccess('New tag added successfully');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);
  };

  const handleUnlinkAnimal = (tagId: string) => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setTags(prevTags =>
        prevTags.map(tag =>
          tag.id === tagId
            ? { ...tag, animalId: undefined, animalName: undefined }
            : tag
        )
      );
      setSuccess('Animal unlinked from tag');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);
  };

  const handleGenerateQRCode = (tag: RFIDTag) => {
    // In a real implementation, this would generate a QR code
    setSuccess(`QR code generated for tag ${tag.tagNumber}`);

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const handlePrintTag = (tag: RFIDTag) => {
    // In a real implementation, this would send the tag to a printer
    setSuccess(`Print job sent for tag ${tag.tagNumber}`);

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const handleExportTags = () => {
    // In a real implementation, this would export the tags to a CSV file
    setSuccess('Tags exported successfully');

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return theme.palette.success.main;
      case 'inactive':
        return theme.palette.warning.main;
      case 'lost':
        return theme.palette.error.main;
      case 'damaged':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const getBatteryColor = (level: number) => {
    if (level > 70) return theme.palette.success.main;
    if (level > 30) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Box>
      {/* Header with actions */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 3,
        flexWrap: { xs: 'wrap', sm: 'nowrap' },
        gap: 2
      }}>
        <Typography variant="h6" fontWeight="bold">
          RFID Tag Management
        </Typography>

        <Box sx={{
          display: 'flex',
          gap: 1,
          flexWrap: { xs: 'wrap', sm: 'nowrap' }
        }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              value={filterStatus}
              label="Status"
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
              <MenuItem value="lost">Lost</MenuItem>
              <MenuItem value="damaged">Damaged</MenuItem>
            </Select>
          </FormControl>

          <CustomButton
            variant="outlined"
            startIcon={<Download />}
            size="small"
            onClick={handleExportTags}
          >
            Export
          </CustomButton>

          <CustomButton
            variant="contained"
            startIcon={<Add />}
            size="small"
            onClick={() => setShowNewTagForm(true)}
          >
            Add Tag
          </CustomButton>
        </Box>
      </Box>

      {/* Alerts */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {success && (
        <Alert
          severity="success"
          sx={{ mb: 2 }}
          onClose={() => setSuccess(null)}
        >
          {success}
        </Alert>
      )}

      {/* New Tag Form */}
      {showNewTagForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Paper sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            backgroundColor: alpha(theme.palette.primary.main, 0.03),
            borderLeft: `4px solid ${theme.palette.primary.main}`
          }}>
            <Typography variant="subtitle1" fontWeight="bold" mb={2}>
              Add New RFID Tag
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Tag Number"
                  value={newTag.tagNumber}
                  onChange={(e) => setNewTag({ ...newTag, tagNumber: e.target.value })}
                  fullWidth
                  required
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={newTag.status}
                    label="Status"
                    onChange={(e) => setNewTag({ ...newTag, status: e.target.value as any })}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                    <MenuItem value="lost">Lost</MenuItem>
                    <MenuItem value="damaged">Damaged</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Battery Level (%)"
                  type="number"
                  value={newTag.batteryLevel}
                  onChange={(e) => setNewTag({ ...newTag, batteryLevel: Number(e.target.value) })}
                  fullWidth
                  size="small"
                  InputProps={{ inputProps: { min: 0, max: 100 } }}
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Animal ID (optional)"
                  value={newTag.animalId || ''}
                  onChange={(e) => setNewTag({ ...newTag, animalId: e.target.value })}
                  fullWidth
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <TextField
                  label="Animal Name (optional)"
                  value={newTag.animalName || ''}
                  onChange={(e) => setNewTag({ ...newTag, animalName: e.target.value })}
                  fullWidth
                  size="small"
                />
              </Grid>

              <Grid item xs={12} sm={6} md={9}>
                <TextField
                  label="Notes (optional)"
                  value={newTag.notes || ''}
                  onChange={(e) => setNewTag({ ...newTag, notes: e.target.value })}
                  fullWidth
                  size="small"
                />
              </Grid>
            </Grid>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2, gap: 1 }}>
              <CustomButton
                variant="outlined"
                onClick={() => setShowNewTagForm(false)}
                startIcon={<Cancel />}
              >
                Cancel
              </CustomButton>
              <CustomButton
                variant="contained"
                onClick={handleAddNewTag}
                startIcon={<Save />}
                disabled={loading}
              >
                {loading ? <CircularProgress size={24} /> : 'Save'}
              </CustomButton>
            </Box>
          </Paper>
        </motion.div>
      )}

      {/* Tags List */}
      <Grid container spacing={2}>
        {filteredTags.map((tag, index) => (
          <Grid item xs={12} key={tag.id}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Paper
                sx={{
                  p: 3,
                  borderRadius: 2,
                  borderLeft: `4px solid ${getStatusColor(tag.status)}`,
                  transition: 'transform 0.2s ease',
                  backgroundColor: theme.palette.mode === 'dark'
                    ? alpha(getStatusColor(tag.status), 0.05)
                    : alpha(getStatusColor(tag.status), 0.03),
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                    backgroundColor: theme.palette.mode === 'dark'
                      ? alpha(getStatusColor(tag.status), 0.1)
                      : alpha(getStatusColor(tag.status), 0.05)
                  }
                }}
              >
                {editingTag && editingTag.id === tag.id ? (
                  // Edit Mode
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6} md={3}>
                      <TextField
                        label="Tag Number"
                        value={editingTag.tagNumber}
                        onChange={(e) => setEditingTag({ ...editingTag, tagNumber: e.target.value })}
                        fullWidth
                        required
                        size="small"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <FormControl fullWidth size="small">
                        <InputLabel>Status</InputLabel>
                        <Select
                          value={editingTag.status}
                          label="Status"
                          onChange={(e) => setEditingTag({ ...editingTag, status: e.target.value as any })}
                        >
                          <MenuItem value="active">Active</MenuItem>
                          <MenuItem value="inactive">Inactive</MenuItem>
                          <MenuItem value="lost">Lost</MenuItem>
                          <MenuItem value="damaged">Damaged</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <TextField
                        label="Battery Level (%)"
                        type="number"
                        value={editingTag.batteryLevel}
                        onChange={(e) => setEditingTag({ ...editingTag, batteryLevel: Number(e.target.value) })}
                        fullWidth
                        size="small"
                        InputProps={{ inputProps: { min: 0, max: 100 } }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <TextField
                        label="Animal ID"
                        value={editingTag.animalId || ''}
                        onChange={(e) => setEditingTag({ ...editingTag, animalId: e.target.value })}
                        fullWidth
                        size="small"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <TextField
                        label="Animal Name"
                        value={editingTag.animalName || ''}
                        onChange={(e) => setEditingTag({ ...editingTag, animalName: e.target.value })}
                        fullWidth
                        size="small"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6} md={9}>
                      <TextField
                        label="Notes"
                        value={editingTag.notes || ''}
                        onChange={(e) => setEditingTag({ ...editingTag, notes: e.target.value })}
                        fullWidth
                        size="small"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <CustomButton
                          variant="outlined"
                          onClick={handleCancelEdit}
                          startIcon={<Cancel />}
                        >
                          Cancel
                        </CustomButton>
                        <CustomButton
                          variant="contained"
                          onClick={handleSaveEdit}
                          startIcon={<Save />}
                          disabled={loading}
                        >
                          {loading ? <CircularProgress size={24} /> : 'Save'}
                        </CustomButton>
                      </Box>
                    </Grid>
                  </Grid>
                ) : (
                  // View Mode
                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2, flexWrap: { xs: 'wrap', sm: 'nowrap' }, gap: 1 }}>
                      <Box>
                        <Typography variant="h6" fontWeight="bold">
                          {tag.tagNumber}
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                          <Chip
                            label={tag.status.toUpperCase()}
                            size="small"
                            sx={{
                              bgcolor: getStatusColor(tag.status),
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                          />
                          <Chip
                            label={`Battery: ${tag.batteryLevel}%`}
                            size="small"
                            sx={{
                              bgcolor: getBatteryColor(tag.batteryLevel),
                              color: 'white'
                            }}
                          />
                          {tag.lastSeen && (
                            <Chip
                              label={`Last seen: ${new Date(tag.lastSeen).toLocaleString()}`}
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Tooltip title="Generate QR Code">
                          <IconButton
                            size="small"
                            onClick={() => handleGenerateQRCode(tag)}
                            color="primary"
                          >
                            <QrCode />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Print Tag">
                          <IconButton
                            size="small"
                            onClick={() => handlePrintTag(tag)}
                            color="primary"
                          >
                            <Print />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Edit Tag">
                          <IconButton
                            size="small"
                            onClick={() => handleEditTag(tag)}
                            color="primary"
                          >
                            <Edit />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Delete Tag">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteTag(tag.id)}
                            color="error"
                          >
                            <Delete />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">
                            Animal Information
                          </Typography>

                          {tag.animalId ? (
                            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Box>
                                <Typography variant="body1">
                                  <strong>ID:</strong> {tag.animalId}
                                </Typography>
                                {tag.animalName && (
                                  <Typography variant="body1">
                                    <strong>Name:</strong> {tag.animalName}
                                  </Typography>
                                )}
                              </Box>

                              <Tooltip title="Unlink Animal">
                                <IconButton
                                  size="small"
                                  onClick={() => handleUnlinkAnimal(tag.id)}
                                  color="warning"
                                >
                                  <LinkOff />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          ) : (
                            <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                No animal linked
                              </Typography>
                              <CustomButton
                                startIcon={<Link />}
                                size="small"
                                variant="outlined"
                                onClick={() => handleEditTag(tag)}
                              >
                                Link Animal
                              </CustomButton>
                            </Box>
                          )}
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Typography variant="subtitle2" color="text.secondary">
                          Notes
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          {tag.notes || 'No notes available'}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </Paper>
            </motion.div>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default RFIDManagement;
