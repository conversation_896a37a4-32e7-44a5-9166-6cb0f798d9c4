/**
 * Breadcrumb Navigation Component
 * Provides consistent navigation breadcrumbs across all modules
 */

import React from 'react';
import {
  Breadcrumbs,
  Link,
  Typography,
  Box,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Home,
  NavigateNext,
  Dashboard,
  Pets,
  LocalHospital,
  TrendingUp,
  AccountBalance,
  Inventory,
  Assessment,
  Security,
  Settings,
  Business,
  Description,
  Info
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from '../../hooks/useTranslation';

interface BreadcrumbItem {
  label: string;
  path: string;
  icon?: React.ReactElement;
  isActive?: boolean;
}

interface BreadcrumbNavigationProps {
  customItems?: BreadcrumbItem[];
  showIcons?: boolean;
  maxItems?: number;
}

const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  customItems,
  showIcons = true,
  maxItems = 8
}) => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { translate } = useTranslation();

  // Icon mapping for different modules
  const moduleIcons: Record<string, React.ReactElement> = {
    dashboard: <Dashboard fontSize="small" />,
    animals: <Pets fontSize="small" />,
    health: <LocalHospital fontSize="small" />,
    breeding: <TrendingUp fontSize="small" />,
    financial: <AccountBalance fontSize="small" />,
    inventory: <Inventory fontSize="small" />,
    analytics: <Assessment fontSize="small" />,
    compliance: <Security fontSize="small" />,
    settings: <Settings fontSize="small" />,
    commercial: <Business fontSize="small" />,
    reports: <Description fontSize="small" />,
    resources: <Info fontSize="small" />
  };

  // Generate breadcrumb items from current path
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) {
      return customItems;
    }

    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with home
    breadcrumbs.push({
      label: translate('nav.home', { fallback: 'Home' }),
      path: '/',
      icon: <Home fontSize="small" />
    });

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      // Generate label and icon based on segment
      let label = segment.charAt(0).toUpperCase() + segment.slice(1);
      let icon = moduleIcons[segment];

      // Special handling for common segments
      switch (segment) {
        case 'dashboard':
          label = translate('nav.dashboard', { fallback: 'Dashboard' });
          break;
        case 'animals':
          label = translate('nav.animals', { fallback: 'Animals' });
          break;
        case 'health':
          label = translate('nav.health', { fallback: 'Health' });
          break;
        case 'breeding':
          label = translate('nav.breeding', { fallback: 'Breeding' });
          break;
        case 'financial':
          label = translate('nav.financial', { fallback: 'Financial' });
          break;
        case 'inventory':
          label = translate('nav.inventory', { fallback: 'Inventory' });
          break;
        case 'analytics':
          label = translate('nav.analytics', { fallback: 'Analytics' });
          break;
        case 'compliance':
          label = translate('nav.compliance', { fallback: 'Compliance' });
          break;
        case 'settings':
          label = translate('nav.settings', { fallback: 'Settings' });
          break;
        case 'commercial':
          label = translate('nav.commercial', { fallback: 'Commercial' });
          break;
        case 'reports':
          label = translate('nav.reports', { fallback: 'Reports' });
          break;
        case 'resources':
          label = translate('nav.resources', { fallback: 'Resources' });
          break;
        case 'new':
          label = translate('common.new', { fallback: 'New' });
          icon = undefined;
          break;
        case 'edit':
          label = translate('common.edit', { fallback: 'Edit' });
          icon = undefined;
          break;
        case 'view':
          label = translate('common.view', { fallback: 'View' });
          icon = undefined;
          break;
      }

      breadcrumbs.push({
        label,
        path: currentPath,
        icon,
        isActive: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = generateBreadcrumbs();

  // Limit breadcrumbs if too many
  const displayItems = breadcrumbItems.length > maxItems
    ? [
        breadcrumbItems[0], // Home
        { label: '...', path: '', icon: undefined },
        ...breadcrumbItems.slice(-maxItems + 2)
      ]
    : breadcrumbItems;

  const handleBreadcrumbClick = (path: string) => {
    if (path && path !== '...') {
      navigate(path);
    }
  };

  return (
    <Box
      sx={{
        py: 1,
        px: 2,
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(10px)',
        borderRadius: 1,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        mb: 2
      }}
    >
      <Breadcrumbs
        separator={<NavigateNext fontSize="small" />}
        maxItems={maxItems}
        aria-label="breadcrumb navigation"
        sx={{
          '& .MuiBreadcrumbs-separator': {
            color: theme.palette.text.secondary,
            mx: 1
          }
        }}
      >
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isEllipsis = item.label === '...';

          if (isEllipsis) {
            return (
              <Typography
                key={index}
                color="text.secondary"
                sx={{ display: 'flex', alignItems: 'center' }}
              >
                ...
              </Typography>
            );
          }

          if (isLast || item.isActive) {
            return (
              <Chip
                key={item.path}
                icon={showIcons && item.icon ? item.icon : undefined}
                label={item.label}
                variant="filled"
                size="small"
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 600,
                  '& .MuiChip-icon': {
                    color: 'inherit'
                  }
                }}
              />
            );
          }

          return (
            <Link
              key={item.path}
              component="button"
              variant="body2"
              onClick={() => handleBreadcrumbClick(item.path)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                color: theme.palette.text.secondary,
                textDecoration: 'none',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: 1,
                transition: 'all 0.2s ease',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  textDecoration: 'none'
                }
              }}
            >
              {showIcons && item.icon && (
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 0.5 }}>
                  {item.icon}
                </Box>
              )}
              {item.label}
            </Link>
          );
        })}
      </Breadcrumbs>
    </Box>
  );
};

export default BreadcrumbNavigation;
