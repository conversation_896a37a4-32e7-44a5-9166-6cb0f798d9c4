import React from 'react';
import { Box, Typography, Paper, alpha, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import CustomAnalyticsButton from './CustomAnalyticsButton';

interface AnalyticsCardProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  height?: string | number;
  accentColor?: string;
  secondaryColor?: string;
  actionLabel?: string;
  onAction?: () => void;
  loading?: boolean;
  delay?: number;
}

/**
 * Custom card component for analytics module
 * This component doesn't rely on MUI components that might cause the dark property error
 */
const AnalyticsCard: React.FC<AnalyticsCardProps> = ({
  title,
  subtitle,
  icon,
  children,
  height = 'auto',
  accentColor,
  secondaryColor,
  actionLabel,
  onAction,
  loading = false,
  delay = 0
}) => {
  const theme = useTheme();
  
  // Use provided colors or fallback to theme colors
  const primaryColor = accentColor || theme.palette.primary.main;
  const secondaryColorValue = secondaryColor || (theme.palette.primary.main || '#2A8A82');

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
      style={{ height: '100%' }}
    >
      <Paper
        elevation={0}
        sx={{
          height,
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          transition: 'all 0.3s ease',
          backdropFilter: 'blur(10px)',
          backgroundColor: 'transparent',
          background: `linear-gradient(135deg,
            ${alpha(theme.palette.background.paper, 0.6)},
            ${alpha(theme.palette.background.paper, 0.4)})`,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: `linear-gradient(to right, ${primaryColor}, ${secondaryColorValue})`,
            zIndex: 3,
            opacity: 0.9
          },
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 20px rgba(0,0,0,0.12)'
          }
        }}
      >
        {/* Card Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && (
              <Box
                sx={{
                  mr: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  background: `linear-gradient(135deg, ${primaryColor}, ${secondaryColorValue})`,
                  color: '#fff'
                }}
              >
                {icon}
              </Box>
            )}
            <Box>
              <Typography variant="h6" fontWeight="600">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          
          {actionLabel && onAction && (
            <CustomAnalyticsButton
              label={actionLabel}
              onClick={onAction}
              variant="text"
              color="primary"
              size="small"
            />
          )}
        </Box>

        {/* Card Content */}
        <Box
          sx={{
            p: 2,
            position: 'relative',
            height: 'calc(100% - 72px)',
            overflow: 'auto'
          }}
        >
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                width: '100%'
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  border: `3px solid ${alpha(primaryColor, 0.2)}`,
                  borderTop: `3px solid ${primaryColor}`,
                  animation: 'spin 1s linear infinite',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }}
              />
            </Box>
          ) : (
            children
          )}
        </Box>
      </Paper>
    </motion.div>
  );
};

export default AnalyticsCard;
