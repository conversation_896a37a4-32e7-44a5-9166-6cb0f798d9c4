import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip } from '@mui/material';
import {  ModuleContainer, ModuleHeader, withSubModuleTranslation , CustomButton } from '../../components/common';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info
 } from '../../utils/iconImports';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';
import { mockAnimals } from '../../mocks/animalData';

interface BreedingScheduleItem {
  id: string;
  damId: string;
  damName: string;
  sireId: string;
  sireName: string;
  breedingDate: string;
  expectedDueDate: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'failed';
  method: 'natural' | 'artificial insemination' | 'embryo transfer';
  notes: string;
}

interface BreedingScheduleProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const BreedingSchedule: React.FC<BreedingScheduleProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const { breedingRecords } = useBreedingRecords();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState<BreedingScheduleItem | null>(null);
  const [scheduleItems, setScheduleItems] = useState<BreedingScheduleItem[]>([
    {
      id: 'BS001',
      damId: 'ANM001',
      damName: 'Umfazi',
      sireId: 'ANM007',
      sireName: 'Ubaba',
      breedingDate: '2024-04-15',
      expectedDueDate: '2025-01-25',
      status: 'scheduled',
      method: 'artificial insemination',
      notes: 'Nguni breeding program for improved heat tolerance'
    },
    {
      id: 'BS002',
      damId: 'ANM003',
      damName: 'Impilo',
      sireId: 'ANM009',
      sireName: 'Isibindi',
      breedingDate: '2024-04-10',
      expectedDueDate: '2025-01-20',
      status: 'scheduled',
      method: 'natural',
      notes: 'Afrikaner-Bonsmara crossbreeding for improved meat quality'
    },
    {
      id: 'BS003',
      damId: 'ANM004',
      damName: 'Imvula',
      sireId: 'ANM011',
      sireName: 'Amandla',
      breedingDate: '2024-03-20',
      expectedDueDate: '2024-08-20',
      status: 'in-progress',
      method: 'artificial insemination',
      notes: 'Dorper breeding for improved lamb production'
    },
    {
      id: 'BS004',
      damId: 'ANM008',
      damName: 'Umama',
      sireId: 'ANM007',
      sireName: 'Ubaba',
      breedingDate: '2024-02-15',
      expectedDueDate: '2024-11-25',
      status: 'in-progress',
      method: 'natural',
      notes: 'Pure Nguni breeding for conservation program'
    },
    {
      id: 'BS005',
      damId: 'ANM010',
      damName: 'Ithemba',
      sireId: 'ANM009',
      sireName: 'Isibindi',
      breedingDate: '2024-01-10',
      expectedDueDate: '2024-10-20',
      status: 'in-progress',
      method: 'artificial insemination',
      notes: 'Bonsmara breeding for improved feed conversion'
    }
  ]);

  const handleAddNew = () => {
    setSelectedSchedule(null);
    setOpenDialog(true);
  };

  const handleEdit = (item: BreedingScheduleItem) => {
    setSelectedSchedule(item);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setScheduleItems(scheduleItems.filter(item => item.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Chip label={translateModuleField ? translateModuleField('status_scheduled', "Scheduled") : "Scheduled"} color="primary" size="small" />;
      case 'in-progress':
        return <Chip label={translateModuleField ? translateModuleField('status_in_progress', "In Progress") : "In Progress"} color="info" size="small" />;
      case 'completed':
        return <Chip label={translateModuleField ? translateModuleField('status_completed', "Completed") : "Completed"} color="success" size="small" />;
      case 'failed':
        return <Chip label={translateModuleField ? translateModuleField('status_failed', "Failed") : "Failed"} color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getMethodChip = (method: string) => {
    switch (method) {
      case 'natural':
        return <Chip label={translateModuleField ? translateModuleField('method_natural', "Natural") : "Natural"} color="default" size="small" />;
      case 'artificial insemination':
        return <Chip label={translateModuleField ? translateModuleField('method_ai', "AI") : "AI"} color="secondary" size="small" />;
      case 'embryo transfer':
        return <Chip label={translateModuleField ? translateModuleField('method_embryo', "Embryo Transfer") : "Embryo Transfer"} color="warning" size="small" />;
      default:
        return <Chip label={method} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (dateString: string) => {
    const today = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <ModuleContainer module="breeding">
      <ModuleHeader
        title={translateSubModule ? translateSubModule('title', "Breeding Schedule") : "Breeding Schedule"}
        subtitle={translateSubModule ? translateSubModule('subtitle', "Manage breeding schedules and track animal reproduction cycles") : "Manage breeding schedules and track animal reproduction cycles"}
        module="breeding"
        submodule="schedule"
        actionLabel={translateSubModule ? translateSubModule('action', "Add Breeding Schedule") : "Add Breeding Schedule"}
        actionIcon={<Add />}
        onAction={handleAddNew}
      />

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                {translateModuleField ? translateModuleField('upcoming_breedings', "Upcoming Breedings") : "Upcoming Breedings"}
              </Typography>
              <Box mt={2}>
                {scheduleItems
                  .filter(item => item.status === 'scheduled')
                  .sort((a, b) => new Date(a.breedingDate).getTime() - new Date(b.breedingDate).getTime())
                  .slice(0, 3)
                  .map(item => (
                    <Box key={item.id} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {item.damName} × {item.sireName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          <CalendarToday fontSize="small" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                          {formatDate(item.breedingDate)}
                        </Typography>
                        {getMethodChip(item.method)}
                      </Box>
                    </Box>
                  ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                {translateModuleField ? translateModuleField('expected_births', "Expected Births") : "Expected Births"}
              </Typography>
              <Box mt={2}>
                {scheduleItems
                  .filter(item => item.status === 'in-progress')
                  .sort((a, b) => new Date(a.expectedDueDate).getTime() - new Date(b.expectedDueDate).getTime())
                  .slice(0, 3)
                  .map(item => (
                    <Box key={item.id} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {item.damName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          <CalendarToday fontSize="small" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                          Due: {formatDate(item.expectedDueDate)}
                        </Typography>
                        <Chip
                          label={`${calculateDaysRemaining(item.expectedDueDate)} days`}
                          size="small"
                          color={calculateDaysRemaining(item.expectedDueDate) < 30 ? "warning" : "default"}
                        />
                      </Box>
                    </Box>
                  ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Breeding Statistics
              </Typography>
              <Grid container spacing={2} mt={1}>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="primary">
                      {scheduleItems.filter(item => item.status === 'scheduled').length}
                    </Typography>
                    <Typography variant="body2">Scheduled</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center" p={1.5} bgcolor="rgba(2, 136, 209, 0.1)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="info.main">
                      {scheduleItems.filter(item => item.status === 'in-progress').length}
                    </Typography>
                    <Typography variant="body2">In Progress</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="success.main">
                      {scheduleItems.filter(item => item.status === 'completed').length}
                    </Typography>
                    <Typography variant="body2">Completed</Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} md={3}>
                  <Box textAlign="center" p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="error.main">
                      {scheduleItems.filter(item => item.status === 'failed').length}
                    </Typography>
                    <Typography variant="body2">Failed</Typography>
                  </Box>
                </Grid>
              </Grid>

              <Box mt={3}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Breeding Methods
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="h5" fontWeight="bold">
                        {scheduleItems.filter(item => item.method === 'natural').length}
                      </Typography>
                      <Typography variant="body2">Natural</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="h5" fontWeight="bold">
                        {scheduleItems.filter(item => item.method === 'artificial insemination').length}
                      </Typography>
                      <Typography variant="body2">AI</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={4}>
                    <Box textAlign="center" p={1} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="h5" fontWeight="bold">
                        {scheduleItems.filter(item => item.method === 'embryo transfer').length}
                      </Typography>
                      <Typography variant="body2">Embryo Transfer</Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', overflow: 'hidden' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
            <Table>
              <TableHead sx={{ bgcolor: 'primary.main' }}>
                <TableRow>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('dam', "Dam") : "Dam"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('sire', "Sire") : "Sire"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('breeding_date', "Breeding Date") : "Breeding Date"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('expected_due_date', "Expected Due Date") : "Expected Due Date"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('method', "Method") : "Method"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('status', "Status") : "Status"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translateModuleField ? translateModuleField('notes', "Notes") : "Notes"}</TableCell>
                  <TableCell sx={{ color: 'white' }}>{translate ? translate('common.actions', { fallback: "Actions" }) : "Actions"}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {scheduleItems.map((item) => (
                  <TableRow key={item.id} hover>
                    <TableCell>{item.damName}</TableCell>
                    <TableCell>{item.sireName}</TableCell>
                    <TableCell>{formatDate(item.breedingDate)}</TableCell>
                    <TableCell>{formatDate(item.expectedDueDate)}</TableCell>
                    <TableCell>{getMethodChip(item.method)}</TableCell>
                    <TableCell>{getStatusChip(item.status)}</TableCell>
                    <TableCell>
                      <Tooltip title={item.notes}>
                        <Typography
                          variant="body2"
                          sx={{
                            maxWidth: 150,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {item.notes}
                        </Typography>
                      </Tooltip>
                    </TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(item)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(item.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedSchedule ? 'Edit Breeding Schedule' : 'Add New Breeding Schedule'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="dam-select-label">Dam</InputLabel>
                <Select
                  labelId="dam-select-label"
                  id="dam-select"
                  label="Dam"
                  defaultValue={selectedSchedule?.damId || ''}
                >
                  {mockAnimals
                    .filter(animal => animal.gender.toLowerCase() === 'female')
                    .map(animal => (
                      <MenuItem key={animal.id} value={animal.id}>
                        {animal.name} ({animal.breed})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="sire-select-label">Sire</InputLabel>
                <Select
                  labelId="sire-select-label"
                  id="sire-select"
                  label="Sire"
                  defaultValue={selectedSchedule?.sireId || ''}
                >
                  {mockAnimals
                    .filter(animal => animal.gender.toLowerCase() === 'male')
                    .map(animal => (
                      <MenuItem key={animal.id} value={animal.id}>
                        {animal.name} ({animal.breed})
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="breedingDate"
                label="Breeding Date"
                type="date"
                defaultValue={selectedSchedule?.breedingDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="expectedDueDate"
                label="Expected Due Date"
                type="date"
                defaultValue={selectedSchedule?.expectedDueDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="method-select-label">Method</InputLabel>
                <Select
                  labelId="method-select-label"
                  id="method-select"
                  label="Method"
                  defaultValue={selectedSchedule?.method || 'natural'}
                >
                  <MenuItem value="natural">Natural</MenuItem>
                  <MenuItem value="artificial insemination">Artificial Insemination</MenuItem>
                  <MenuItem value="embryo transfer">Embryo Transfer</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedSchedule?.status || 'scheduled'}
                >
                  <MenuItem value="scheduled">Scheduled</MenuItem>
                  <MenuItem value="in-progress">In Progress</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedSchedule?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedSchedule ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </ModuleContainer>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(BreedingSchedule, 'breeding', 'schedule');
