const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const DB_NAME = 'ampd_livestock';

async function addDemoUsers() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const usersCollection = db.collection('users');
    
    // Demo users to add
    const demoUsers = [
      {
        username: 'Demo',
        email: '<EMAIL>',
        password: '123',
        firstName: 'Demo',
        lastName: 'User',
        role: 'demo',
        status: 'active',
        permissions: ['read', 'basic_features'],
        subscriptionTier: 'Beta Access',
        profile: {
          farmName: 'Demo Farm',
          farmSize: 'Small (1-50 animals)',
          province: 'Gauteng',
          animalTypes: ['Cattle', 'Sheep'],
          paymentMethod: ''
        },
        trialStartDate: new Date(),
        trialEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin@123',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        status: 'active',
        permissions: ['all'],
        subscriptionTier: 'Enterprise',
        profile: {
          farmName: 'AgriIntel System',
          farmSize: 'Enterprise (500+ animals)',
          province: 'Gauteng',
          animalTypes: ['Cattle', 'Sheep', 'Goats', 'Pigs'],
          paymentMethod: 'enterprise'
        },
        lastLogin: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    for (const user of demoUsers) {
      // Check if user already exists
      const existingUser = await usersCollection.findOne({ 
        $or: [{ username: user.username }, { email: user.email }] 
      });
      
      if (existingUser) {
        console.log(`User ${user.username} already exists, updating password...`);

        // Hash the password
        const hashedPassword = await bcrypt.hash(user.password, 10);

        // Create update object without the plain password
        const updateData = { ...user };
        delete updateData.password;

        // Update the existing user
        await usersCollection.updateOne(
          { username: user.username },
          {
            $set: {
              password: hashedPassword,
              updatedAt: new Date(),
              ...updateData
            }
          }
        );
        console.log(`✅ Updated user: ${user.username}`);
      } else {
        // Hash the password
        const hashedPassword = await bcrypt.hash(user.password, 10);
        user.password = hashedPassword;
        
        // Insert new user
        await usersCollection.insertOne(user);
        console.log(`✅ Created user: ${user.username}`);
      }
    }
    
    console.log('\n🎉 Demo users setup complete!');
    console.log('You can now login with:');
    console.log('- Username: Demo, Password: 123');
    console.log('- Username: admin, Password: Admin@123');
    
  } catch (error) {
    console.error('Error adding demo users:', error);
  } finally {
    await client.close();
  }
}

// Run the script
addDemoUsers().catch(console.error);
