/**
 * Inject All Data Script
 * 
 * This script runs all data injection scripts to populate the MongoDB database
 * with realistic data for all modules
 */

require('dotenv').config();
const { exec } = require('child_process');
const path = require('path');
const logger = require('../src/utils/logger');

// List of scripts to run in order
const scripts = [
  'inject-animal-data.js',
  'inject-health-data.js',
  'inject-breeding-data.js',
  'inject-feeding-data.js',
  'inject-financial-data.js',
  'inject-inventory-data.js',
  'inject-business-data.js'
];

// Function to run a script
function runScript(scriptPath) {
  return new Promise((resolve, reject) => {
    logger.info(`Running script: ${scriptPath}`);
    
    const child = exec(`node ${scriptPath}`, { cwd: __dirname }, (error, stdout, stderr) => {
      if (error) {
        logger.error(`Error executing ${scriptPath}: ${error}`);
        reject(error);
        return;
      }
      
      if (stderr) {
        logger.warn(`Script ${scriptPath} stderr: ${stderr}`);
      }
      
      logger.info(`Script ${scriptPath} completed successfully`);
      logger.info(`Output: ${stdout}`);
      resolve();
    });
    
    // Forward script output to console
    child.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    child.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
  });
}

// Run all scripts in sequence
async function runAllScripts() {
  logger.info('Starting data injection process...');
  
  for (const script of scripts) {
    const scriptPath = path.join(__dirname, script);
    try {
      await runScript(scriptPath);
      logger.info(`Successfully completed script: ${script}`);
    } catch (error) {
      logger.error(`Failed to run script ${script}: ${error}`);
      // Continue with next script even if one fails
    }
  }
  
  logger.info('All data injection scripts completed');
}

// Run the main function
runAllScripts()
  .then(() => {
    logger.info('Data injection process completed successfully');
  })
  .catch((error) => {
    logger.error('Error in data injection process:', error);
    process.exit(1);
  });
