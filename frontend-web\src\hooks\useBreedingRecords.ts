import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  BreedingRecord,
  BirthRecord,
  HeatRecord
} from '../types/breeding';

// API URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Initial stats
const initialBreedingStats = {
  totalBreedings: 0,
  successRate: 0,
  averageGestation: 283,
  monthlyBreedings: [0, 0, 0, 0, 0, 0]
};

export const useBreedingRecords = () => {
  const [breedingRecords, setBreedingRecords] = useState<BreedingRecord[]>([]);
  const [birthRecords, setBirthRecords] = useState<BirthRecord[]>([]);
  const [heatRecords, setHeatRecords] = useState<HeatRecord[]>([]);
  const [stats, setStats] = useState(initialBreedingStats);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchBreedingData = async () => {
      try {
        setLoading(true);

        // Fetch real data from API
        const [breedingResponse, birthResponse, heatResponse] = await Promise.all([
          axios.get(`${API_URL}/breeding/records`),
          axios.get(`${API_URL}/breeding/births`),
          axios.get(`${API_URL}/breeding/heat`)
        ]);

        setBreedingRecords(breedingResponse.data);
        setBirthRecords(birthResponse.data);
        setHeatRecords(heatResponse.data);

        // Update stats with real data
        updateStats();
        setLoading(false);
      } catch (err) {
        console.error('Error fetching breeding data:', err);
        setError(err as Error);
        setLoading(false);
      }
    };

    fetchBreedingData();
  }, []);

  // CRUD operations for breeding records
  const addBreedingRecord = (record: Omit<BreedingRecord, 'id'>) => {
    const newRecord = {
      ...record,
      id: `BR${String(breedingRecords.length + 1).padStart(3, '0')}`
    } as BreedingRecord;

    setBreedingRecords([...breedingRecords, newRecord]);
    updateStats();
    return newRecord;
  };

  const updateBreedingRecord = (id: string, updates: Partial<BreedingRecord>) => {
    const updatedRecords = breedingRecords.map(record =>
      record.id === id ? { ...record, ...updates } : record
    );
    setBreedingRecords(updatedRecords);
    updateStats();
    return updatedRecords.find(record => record.id === id);
  };

  const deleteBreedingRecord = (id: string) => {
    setBreedingRecords(breedingRecords.filter(record => record.id !== id));
    updateStats();
    return true;
  };

  // CRUD operations for birth records
  const addBirthRecord = (record: Omit<BirthRecord, 'id'>) => {
    const newRecord = {
      ...record,
      id: `BIR${String(birthRecords.length + 1).padStart(3, '0')}`
    } as BirthRecord;

    setBirthRecords([...birthRecords, newRecord]);
    updateStats();
    return newRecord;
  };

  const updateBirthRecord = (id: string, updates: Partial<BirthRecord>) => {
    const updatedRecords = birthRecords.map(record =>
      record.id === id ? { ...record, ...updates } : record
    );
    setBirthRecords(updatedRecords);
    updateStats();
    return updatedRecords.find(record => record.id === id);
  };

  const deleteBirthRecord = (id: string) => {
    setBirthRecords(birthRecords.filter(record => record.id !== id));
    updateStats();
    return true;
  };

  // CRUD operations for heat records
  const addHeatRecord = (record: Omit<HeatRecord, 'id'>) => {
    const newRecord = {
      ...record,
      id: `HR${String(heatRecords.length + 1).padStart(3, '0')}`
    } as HeatRecord;

    setHeatRecords([...heatRecords, newRecord]);
    updateStats();
    return newRecord;
  };

  const updateHeatRecord = (id: string, updates: Partial<HeatRecord>) => {
    const updatedRecords = heatRecords.map(record =>
      record.id === id ? { ...record, ...updates } : record
    );
    setHeatRecords(updatedRecords);
    updateStats();
    return updatedRecords.find(record => record.id === id);
  };

  const deleteHeatRecord = (id: string) => {
    setHeatRecords(heatRecords.filter(record => record.id !== id));
    updateStats();
    return true;
  };

  // Update statistics
  const updateStats = () => {
    setStats({
      totalBreedings: breedingRecords.length,
      successRate: breedingRecords.filter(r => r.status === 'confirmed').length / breedingRecords.length * 100 || 0,
      averageGestation: 283, // Default average gestation period for cattle
      monthlyBreedings: [4, 6, 3, 5, 2, 4] // Sample data
    });
  };

  // Generate breeding report
  const generateBreedingReport = (startDate: Date, endDate: Date) => {
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    const filteredBreedingRecords = breedingRecords.filter(r =>
      r.date >= startDateStr && r.date <= endDateStr
    );

    const filteredBirthRecords = birthRecords.filter(r =>
      r.date >= startDateStr && r.date <= endDateStr
    );

    return {
      period: {
        start: startDate,
        end: endDate
      },
      summary: {
        totalBreedings: filteredBreedingRecords.length,
        successfulBreedings: filteredBreedingRecords.filter(r => r.status === 'confirmed').length,
        unsuccessfulBreedings: filteredBreedingRecords.filter(r => r.status === 'unsuccessful').length,
        pendingBreedings: filteredBreedingRecords.filter(r => r.status === 'pending').length,
        totalBirths: filteredBirthRecords.length,
        totalOffspring: filteredBirthRecords.length // One birth = one offspring for simplicity
      },
      breedingRecords: filteredBreedingRecords,
      birthRecords: filteredBirthRecords,
      generatedAt: new Date()
    };
  };

  return {
    breedingRecords,
    birthRecords,
    heatRecords,
    stats,
    loading,
    error,
    addBreedingRecord,
    updateBreedingRecord,
    deleteBreedingRecord,
    addBirthRecord,
    updateBirthRecord,
    deleteBirthRecord,
    addHeatRecord,
    updateHeatRecord,
    deleteHeatRecord,
    generateBreedingReport
  };
};




