import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, Switch, FormControlLabel, Grid, Divider, useTheme, alpha, Tooltip, Slider } from '@mui/material';
import { useThemeContext, ThemeColorKey, themeColors } from '../../contexts/ThemeContext';
import {
  Palette,
  DarkMode,
  Animation,
  Image,
  Notifications,
  AttachMoney,
  FormatSize,
  Contrast,
  Check,
  Refresh
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const ThemeSettings: React.FC = () => {
  const {
    mode,
    toggleMode,
    currentColor,
    setThemeColor,
    showThemeAnimations,
    toggleThemeAnimations,
    showBackgroundImages,
    toggleBackgroundImages,
    showRecentActivity,
    toggleRecentActivity,
    showAgentPrices,
    toggleAgentPrices
  } = useThemeContext();
  const theme = useTheme();
  const [fontSize, setFontSize] = useState<number>(100);
  const [contrast, setContrast] = useState<number>(100);

  // Handle font size change
  const handleFontSizeChange = (event: Event, newValue: number | number[]) => {
    setFontSize(newValue as number);
    document.documentElement.style.fontSize = `${newValue}%`;
  };

  // Handle contrast change
  const handleContrastChange = (event: Event, newValue: number | number[]) => {
    setContrast(newValue as number);
    document.documentElement.style.setProperty('--contrast-factor', `${newValue}%`);
  };

  // Apply settings
  const handleApplySettings = () => {
    // Apply font size
    document.documentElement.style.fontSize = `${fontSize}%`;

    // Apply animations
    document.documentElement.style.setProperty('--enable-animations', showThemeAnimations ? '1' : '0');

    // Apply contrast
    document.documentElement.style.setProperty('--contrast-factor', `${contrast}%`);
  };

  // Reset settings
  const handleResetSettings = () => {
    setFontSize(100);
    setContrast(100);
    document.documentElement.style.fontSize = '100%';
    document.documentElement.style.setProperty('--contrast-factor', '100%');
  };

  return (
    <Card sx={{ borderRadius: '12px', overflow: 'hidden', mb: 4 }}>
      <Box sx={{
        p: 2,
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
        color: 'white'
      }}>
        <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
          <Palette sx={{ mr: 1 }} /> Theme Settings
        </Typography>
      </Box>
      <CardContent>
        <Grid container spacing={3}>
          {/* Theme Mode */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DarkMode sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle1" fontWeight="medium">Dark Mode</Typography>
              </Box>
              <Switch
                checked={mode === 'dark'}
                onChange={toggleMode}
                color="primary"
              />
            </Box>
            <Divider sx={{ my: 2 }} />
          </Grid>

          {/* Theme Colors */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
              Theme Color
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {Object.entries(themeColors).map(([key, color]) => (
                <Tooltip key={key} title={color.name}>
                  <CustomButton
                    sx={{
                      minWidth: 'auto',
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      backgroundColor: color.primary,
                      border: currentColor === key ? `3px solid ${theme.palette.background.paper}` : 'none',
                      boxShadow: currentColor === key ? `0 0 0 2px ${color.primary}` : 'none',
                      zIndex: 5,
                      position: 'relative',
                      '&:hover': {
                        backgroundColor: color.primary,
                        opacity: 0.9,
                        transform: 'scale(1.1)'
                      },
                      '&:active': {
                        transform: 'scale(0.95)'
                      },
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => setThemeColor(key as ThemeColorKey)}
                  />
                </Tooltip>
              ))}
            </Box>
            <Divider sx={{ my: 2 }} />
          </Grid>

          {/* UI Settings */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
              UI Settings
            </Typography>

            <FormControlLabel
              control={
                <Switch
                  checked={showThemeAnimations}
                  onChange={toggleThemeAnimations}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Animation sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography>Enable Animations</Typography>
                </Box>
              }
              sx={{ width: '100%', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={showBackgroundImages}
                  onChange={toggleBackgroundImages}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Image sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography>Show Background Images</Typography>
                </Box>
              }
              sx={{ width: '100%', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={showRecentActivity}
                  onChange={toggleRecentActivity}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Notifications sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography>Show Recent Activity</Typography>
                </Box>
              }
              sx={{ width: '100%', mb: 1 }}
            />

            <FormControlLabel
              control={
                <Switch
                  checked={showAgentPrices}
                  onChange={toggleAgentPrices}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AttachMoney sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography>Show Agent Prices</Typography>
                </Box>
              }
              sx={{ width: '100%' }}
            />
          </Grid>

          {/* Accessibility Settings */}
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
              Accessibility Settings
            </Typography>

            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <FormatSize sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" fontWeight="medium">
                  Font Size: {fontSize}%
                </Typography>
              </Box>
              <Slider
                value={fontSize}
                onChange={handleFontSizeChange}
                min={80}
                max={150}
                step={5}
                marks={[
                  { value: 80, label: '80%' },
                  { value: 100, label: '100%' },
                  { value: 120, label: '120%' },
                  { value: 150, label: '150%' }
                ]}
                sx={{
                  '& .MuiSlider-thumb': {
                    width: 16,
                    height: 16
                  }
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Contrast sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="subtitle2" fontWeight="medium">
                  Contrast: {contrast}%
                </Typography>
              </Box>
              <Slider
                value={contrast}
                onChange={handleContrastChange}
                min={80}
                max={120}
                step={5}
                marks={[
                  { value: 80, label: '80%' },
                  { value: 100, label: '100%' },
                  { value: 120, label: '120%' }
                ]}
                sx={{
                  '& .MuiSlider-thumb': {
                    width: 16,
                    height: 16
                  }
                }}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Preview */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
            Preview
          </Typography>
          <Box
            sx={{
              opacity: 1,
              transform: 'translateY(0)',
              transition: 'opacity 0.4s, transform 0.4s'
            }}
          >
            <Card sx={{
              borderRadius: '16px',
              overflow: 'hidden',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              boxShadow: `0 6px 24px ${alpha(theme.palette.primary.main, 0.15)}`
            }}>
              <Box sx={{
                height: 60,
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                display: 'flex',
                alignItems: 'center',
                px: 2,
                color: 'white'
              }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  Sample Card Header
                </Typography>
              </Box>
              <CardContent>
                <Typography variant="body2" color="text.secondary">
                  This is a preview of how cards will appear with your selected theme.
                </Typography>
                <Box sx={{
                  mt: 2,
                  p: 1,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  display: 'inline-block'
                }}>
                  <Typography variant="body2" fontWeight="medium">
                    Primary Color Sample
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Box>
        </Box>

        {/* Action Buttons */}
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <CustomButton
            variant="outlined"
            onClick={handleResetSettings}
            startIcon={<Refresh />}
          >
            Reset to Default
          </CustomButton>
          <CustomButton
            variant="contained"
            onClick={handleApplySettings}
            startIcon={<Check />}
          >
            Apply Settings
          </CustomButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ThemeSettings;
