import React from 'react';
import { render, screen } from '../../utils/testUtils';
import ResponsiveLayout from './ResponsiveLayout';
import { useMediaQuery } from '@mui/material';

// Mock useMediaQuery hook
jest.mock('@mui/material', () => ({
  ...jest.requireActual('@mui/material'),
  useMediaQuery: jest.fn(),
  useTheme: () => ({
    breakpoints: {
      down: () => 'sm'
    }
  })
}));

describe('ResponsiveLayout Component', () => {
  beforeEach(() => {
    // Reset mock
    (useMediaQuery as jest.Mock).mockReset();
  });
  
  it('renders children correctly', () => {
    // Mock desktop view
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    render(
      <ResponsiveLayout>
        <div data-testid="test-child">Test Content</div>
      </ResponsiveLayout>
    );
    
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });
  
  it('applies desktop layout on larger screens', () => {
    // Mock desktop view
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    const { container } = render(
      <ResponsiveLayout
        desktopDirection="row"
        desktopSpacing={3}
        desktopPadding={4}
        data-testid="layout"
      >
        <div>Item 1</div>
        <div>Item 2</div>
      </ResponsiveLayout>
    );
    
    // In a real test, we would check if the correct styles are applied
    // This is a simplified version
    expect(container.firstChild).toHaveStyle('display: flex');
  });
  
  it('applies mobile layout on smaller screens', () => {
    // Mock mobile view
    (useMediaQuery as jest.Mock).mockReturnValue(true);
    
    const { container } = render(
      <ResponsiveLayout
        mobileDirection="column"
        mobileSpacing={1}
        mobilePadding={2}
        mobileLayout="stack"
        data-testid="layout"
      >
        <div>Item 1</div>
        <div>Item 2</div>
      </ResponsiveLayout>
    );
    
    // In a real test, we would check if the correct styles are applied
    // This is a simplified version
    expect(container.firstChild).toHaveStyle('display: flex');
  });
  
  it('centers content when centerContent is true', () => {
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    const { container } = render(
      <ResponsiveLayout
        centerContent
        data-testid="layout"
      >
        <div>Centered Content</div>
      </ResponsiveLayout>
    );
    
    // In a real test, we would check if the content is centered
    expect(container.firstChild).toHaveStyle('display: flex');
  });
  
  it('applies full width and height when specified', () => {
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    const { container } = render(
      <ResponsiveLayout
        fullWidth
        fullHeight
        data-testid="layout"
      >
        <div>Full Size Content</div>
      </ResponsiveLayout>
    );
    
    // In a real test, we would check if the layout has full width and height
    expect(container.firstChild).toHaveStyle('display: flex');
  });
  
  it('applies scroll layout on mobile when specified', () => {
    // Mock mobile view
    (useMediaQuery as jest.Mock).mockReturnValue(true);
    
    const { container } = render(
      <ResponsiveLayout
        mobileLayout="scroll"
        data-testid="layout"
      >
        <div>Scrollable Content</div>
      </ResponsiveLayout>
    );
    
    // In a real test, we would check if the overflow is set to auto
    expect(container.firstChild).toHaveStyle('display: flex');
  });
});
