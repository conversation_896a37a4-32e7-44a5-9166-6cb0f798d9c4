/**
 * Business Analytics Hook
 *
 * Custom hook for accessing business analytics data and functionality.
 */

import { useState, useEffect, useCallback } from 'react';
import { useAnimalData } from './useAnimalData';
import { useFinancialData } from './useFinancialData';
import { useHealthRecords } from './useHealthRecords';
import { useBreedingRecords } from './useBreedingRecords';
import { useFeedingData } from './useFeedingData';
import businessAnalyticsService from '../services/businessAnalyticsService';

// Define types for business analytics data
export interface BusinessMetric {
  id: string;
  name: string;
  value: number | string;
  unit?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  description?: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessKPI {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  status: 'success' | 'warning' | 'danger';
  trend: number;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessPrediction {
  id: string;
  name: string;
  currentValue: number;
  predictedValue: number;
  confidence: number;
  timeframe: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessInsight {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  suggestedAction?: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessRisk {
  id: string;
  name: string;
  probability: number;
  impact: number;
  riskScore: number;
  mitigationStrategy?: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessOpportunity {
  id: string;
  name: string;
  potentialValue: number;
  feasibility: number;
  opportunityScore: number;
  implementationStrategy?: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
}

export interface BusinessAnalyticsData {
  metrics: BusinessMetric[];
  kpis: BusinessKPI[];
  predictions: BusinessPrediction[];
  insights: BusinessInsight[];
  risks: BusinessRisk[];
  opportunities: BusinessOpportunity[];
  revenueData: any[];
  expenseData: any[];
  animalDistributionData: any[];
  healthData: any[];
  breedingData: any[];
  feedData: any[];
  growthData: any[];
}

/**
 * Custom hook for business analytics data
 * This hook aggregates data from various sources to provide business analytics
 */
export const useBusinessAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState<BusinessAnalyticsData>({
    metrics: [],
    kpis: [],
    predictions: [],
    insights: [],
    risks: [],
    opportunities: [],
    revenueData: [],
    expenseData: [],
    animalDistributionData: [],
    healthData: [],
    breedingData: [],
    feedData: [],
    growthData: []
  });

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Get data from other hooks
  const { animals, stats: animalStats, loading: animalsLoading } = useAnimalData();
  const { stats: financialStats, loading: financialLoading } = useFinancialData();
  const { healthRecords, loading: healthLoading } = useHealthRecords();
  const { breedingRecords, loading: breedingLoading } = useBreedingRecords();
  const { feedingRecords, stats: feedingStats, loading: feedingLoading } = useFeedingData();

  // Generate mock data for business analytics
  const generateMockData = useCallback(() => {
    // Generate metrics
    const metrics: BusinessMetric[] = [
      {
        id: 'revenue',
        name: 'Total Revenue',
        value: 'R 203,000',
        trend: { value: 15, isPositive: true },
        category: 'financial'
      },
      {
        id: 'profit-margin',
        name: 'Profit Margin',
        value: '35%',
        trend: { value: 5, isPositive: true },
        category: 'financial'
      },
      {
        id: 'animal-count',
        name: 'Animal Count',
        value: animalStats?.totalAnimals || 440,
        trend: { value: 10, isPositive: true },
        category: 'animals'
      },
      {
        id: 'health-incidents',
        name: 'Health Incidents',
        value: 12,
        trend: { value: 25, isPositive: false },
        category: 'health'
      },
      {
        id: 'feed-efficiency',
        name: 'Feed Efficiency',
        value: '1.8',
        unit: 'kg feed/kg gain',
        trend: { value: 8, isPositive: true },
        category: 'feeding'
      },
      {
        id: 'breeding-success',
        name: 'Breeding Success Rate',
        value: '78%',
        trend: { value: 3, isPositive: true },
        category: 'breeding'
      }
    ];

    // Generate KPIs
    const kpis: BusinessKPI[] = [
      {
        id: 'roi',
        name: 'Return on Investment',
        value: 18,
        target: 15,
        unit: '%',
        status: 'success',
        trend: 5,
        category: 'financial'
      },
      {
        id: 'operational-costs',
        name: 'Operational Costs',
        value: 45000,
        target: 40000,
        unit: 'R',
        status: 'warning',
        trend: 8,
        category: 'operational'
      },
      {
        id: 'animal-mortality',
        name: 'Animal Mortality Rate',
        value: 2.5,
        target: 3,
        unit: '%',
        status: 'success',
        trend: -15,
        category: 'animals'
      },
      {
        id: 'vaccination-rate',
        name: 'Vaccination Rate',
        value: 95,
        target: 100,
        unit: '%',
        status: 'warning',
        trend: 5,
        category: 'health'
      }
    ];

    // Generate predictions
    const predictions: BusinessPrediction[] = [
      {
        id: 'revenue-prediction',
        name: 'Revenue (Next Quarter)',
        currentValue: 203000,
        predictedValue: 235000,
        confidence: 85,
        timeframe: 'Q3 2023',
        category: 'financial'
      },
      {
        id: 'animal-growth-prediction',
        name: 'Animal Growth Rate',
        currentValue: 0.8,
        predictedValue: 0.85,
        confidence: 78,
        timeframe: 'Next 3 months',
        category: 'animals'
      },
      {
        id: 'feed-cost-prediction',
        name: 'Feed Costs',
        currentValue: 35000,
        predictedValue: 38000,
        confidence: 82,
        timeframe: 'Next 3 months',
        category: 'feeding'
      },
      {
        id: 'health-incidents-prediction',
        name: 'Health Incidents',
        currentValue: 12,
        predictedValue: 8,
        confidence: 70,
        timeframe: 'Next 3 months',
        category: 'health'
      }
    ];

    // Generate insights
    const insights: BusinessInsight[] = [
      {
        id: 'feed-efficiency-insight',
        title: 'Feed Efficiency Improvement',
        description: 'Switching to the new feed mix has improved feed efficiency by 12% over the last quarter.',
        impact: 'high',
        actionable: true,
        suggestedAction: 'Expand the new feed mix to all animal groups',
        category: 'feeding'
      },
      {
        id: 'breeding-timing-insight',
        title: 'Optimal Breeding Timing',
        description: 'Animals bred during the early morning hours show a 15% higher success rate.',
        impact: 'medium',
        actionable: true,
        suggestedAction: 'Schedule breeding activities during early morning hours',
        category: 'breeding'
      },
      {
        id: 'preventive-health-insight',
        title: 'Preventive Health Measures',
        description: 'Farms implementing weekly health checks have 30% fewer disease outbreaks.',
        impact: 'high',
        actionable: true,
        suggestedAction: 'Implement weekly health check protocol',
        category: 'health'
      }
    ];

    // Generate risks
    const risks: BusinessRisk[] = [
      {
        id: 'disease-outbreak-risk',
        name: 'Disease Outbreak',
        probability: 0.3,
        impact: 0.8,
        riskScore: 0.24,
        mitigationStrategy: 'Enhance biosecurity measures and vaccination protocols',
        category: 'health'
      },
      {
        id: 'feed-price-increase-risk',
        name: 'Feed Price Increase',
        probability: 0.6,
        impact: 0.5,
        riskScore: 0.3,
        mitigationStrategy: 'Secure long-term contracts with suppliers and explore alternative feed sources',
        category: 'feeding'
      },
      {
        id: 'market-price-volatility-risk',
        name: 'Market Price Volatility',
        probability: 0.7,
        impact: 0.6,
        riskScore: 0.42,
        mitigationStrategy: 'Diversify sales channels and explore forward contracts',
        category: 'financial'
      }
    ];

    // Generate opportunities
    const opportunities: BusinessOpportunity[] = [
      {
        id: 'direct-marketing-opportunity',
        name: 'Direct-to-Consumer Marketing',
        potentialValue: 50000,
        feasibility: 0.7,
        opportunityScore: 35000,
        implementationStrategy: 'Develop online platform and local delivery network',
        category: 'financial'
      },
      {
        id: 'premium-breed-opportunity',
        name: 'Premium Breed Introduction',
        potentialValue: 80000,
        feasibility: 0.5,
        opportunityScore: 40000,
        implementationStrategy: 'Start with small pilot group and scale based on performance',
        category: 'animals'
      },
      {
        id: 'renewable-energy-opportunity',
        name: 'Renewable Energy Implementation',
        potentialValue: 35000,
        feasibility: 0.8,
        opportunityScore: 28000,
        implementationStrategy: 'Install solar panels and biogas digesters',
        category: 'operational'
      }
    ];

    // Generate chart data
    const revenueData = [
      { month: 'Jan', revenue: 42000, expenses: 30000, profit: 12000 },
      { month: 'Feb', revenue: 38000, expenses: 28000, profit: 10000 },
      { month: 'Mar', revenue: 45000, expenses: 32000, profit: 13000 },
      { month: 'Apr', revenue: 50000, expenses: 35000, profit: 15000 },
      { month: 'May', revenue: 55000, expenses: 38000, profit: 17000 },
      { month: 'Jun', revenue: 60000, expenses: 40000, profit: 20000 }
    ];

    const expenseData = [
      { category: 'Feed', value: 40 },
      { category: 'Labor', value: 25 },
      { category: 'Healthcare', value: 15 },
      { category: 'Utilities', value: 10 },
      { category: 'Maintenance', value: 5 },
      { category: 'Other', value: 5 }
    ];

    const animalDistributionData = [
      { name: 'Cattle', value: 45 },
      { name: 'Sheep', value: 25 },
      { name: 'Goats', value: 20 },
      { name: 'Other', value: 10 }
    ];

    const healthData = [
      { month: 'Jan', vaccinations: 45, treatments: 12, checkups: 30 },
      { month: 'Feb', vaccinations: 50, treatments: 15, checkups: 35 },
      { month: 'Mar', vaccinations: 55, treatments: 10, checkups: 40 },
      { month: 'Apr', vaccinations: 60, treatments: 8, checkups: 45 },
      { month: 'May', vaccinations: 65, treatments: 12, checkups: 50 },
      { month: 'Jun', vaccinations: 70, treatments: 15, checkups: 55 }
    ];

    const breedingData = [
      { month: 'Jan', success: 75, attempts: 100 },
      { month: 'Feb', success: 78, attempts: 105 },
      { month: 'Mar', success: 80, attempts: 110 },
      { month: 'Apr', success: 82, attempts: 115 },
      { month: 'May', success: 85, attempts: 120 },
      { month: 'Jun', success: 88, attempts: 125 }
    ];

    const feedData = [
      { month: 'Jan', hay: 2000, grain: 1500, supplements: 500 },
      { month: 'Feb', hay: 2100, grain: 1600, supplements: 550 },
      { month: 'Mar', hay: 2200, grain: 1700, supplements: 600 },
      { month: 'Apr', hay: 2300, grain: 1800, supplements: 650 },
      { month: 'May', hay: 2400, grain: 1900, supplements: 700 },
      { month: 'Jun', hay: 2500, grain: 2000, supplements: 750 }
    ];

    const growthData = [
      { month: 'Jan', cattle: 0.8, sheep: 0.5, goats: 0.6 },
      { month: 'Feb', cattle: 0.85, sheep: 0.55, goats: 0.65 },
      { month: 'Mar', cattle: 0.9, sheep: 0.6, goats: 0.7 },
      { month: 'Apr', cattle: 0.95, sheep: 0.65, goats: 0.75 },
      { month: 'May', cattle: 1.0, sheep: 0.7, goats: 0.8 },
      { month: 'Jun', cattle: 1.05, sheep: 0.75, goats: 0.85 }
    ];

    return {
      metrics,
      kpis,
      predictions,
      insights,
      risks,
      opportunities,
      revenueData,
      expenseData,
      animalDistributionData,
      healthData,
      breedingData,
      feedData,
      growthData
    };
  }, [animalStats]);

  // Fetch data
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch data from service
      const dashboardData = await businessAnalyticsService.getDashboardData();

      // Fetch strategy data
      const strategyData = await businessAnalyticsService.getBusinessStrategy();

      // Generate additional data for backward compatibility
      const mockData = generateMockData();

      // Set analytics data
      setAnalyticsData({
        ...mockData,
        kpis: dashboardData.kpis || mockData.kpis,
        predictions: dashboardData.predictions || mockData.predictions,
        insights: dashboardData.insights || mockData.insights,
        risks: dashboardData.risks || mockData.risks,
        opportunities: dashboardData.opportunities || mockData.opportunities,
        animalDistributionData: dashboardData.animalDistributionData || mockData.animalDistributionData
      });

      setError(null);
    } catch (err) {
      console.error('Error fetching business analytics data:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch business analytics data'));

      // Fallback to mock data
      const mockData = generateMockData();
      setAnalyticsData(mockData);
    } finally {
      setLoading(false);
    }
  }, [generateMockData]);

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData, animalsLoading, financialLoading, healthLoading, breedingLoading, feedingLoading]);

  // Generate predictive report
  const generatePredictiveReport = useCallback(async (data: any, format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
    try {
      setLoading(true);

      const reportUrl = await businessAnalyticsService.generatePredictiveReport(data, format);
      return reportUrl;
    } catch (err) {
      console.error('Error generating predictive report:', err);
      setError(err instanceof Error ? err : new Error('Failed to generate predictive report'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch predictive insights
  const fetchPredictiveInsights = useCallback(async (category?: string) => {
    try {
      setLoading(true);

      const data = await businessAnalyticsService.getPredictiveInsights(category);
      return data;
    } catch (err) {
      console.error('Error fetching predictive insights:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch predictive insights'));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch business insights
  const fetchBusinessInsights = useCallback(async (category?: string, impact?: string) => {
    try {
      setLoading(true);

      const data = await businessAnalyticsService.getBusinessInsights(category, impact);
      return data;
    } catch (err) {
      console.error('Error fetching business insights:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch business insights'));
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    analyticsData,
    loading,
    error,
    refetch: fetchData,
    generatePredictiveReport,
    fetchPredictiveInsights,
    fetchBusinessInsights
  };
};

export default useBusinessAnalytics;
