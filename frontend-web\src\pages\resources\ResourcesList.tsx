import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, TextField, InputAdornment, Grid, Card, CardContent, Tabs, Tab } from '@mui/material';
import {  Add, Search, FilterList, Edit, Delete, Visibility  } from '../../utils/iconImports';

// Mock resources data
const mockResources = [
  {
    id: 'RES001',
    name: 'Tractor #1',
    type: 'Equipment',
    category: 'Vehicle',
    location: 'Equipment Shed',
    status: 'available',
    lastMaintenance: '2023-05-15',
    nextMaintenance: '2023-08-15',
    purchaseDate: '2020-03-10',
    purchasePrice: 45000,
    currentValue: 38000
  },
  {
    id: 'RES002',
    name: 'Tractor #2',
    type: 'Equipment',
    category: 'Vehicle',
    location: 'Field 3',
    status: 'in-use',
    lastMaintenance: '2023-04-20',
    nextMaintenance: '2023-07-20',
    purchaseDate: '2019-06-15',
    purchasePrice: 52000,
    currentValue: 40000
  },
  {
    id: 'RES003',
    name: 'Water Pump',
    type: 'Equipment',
    category: 'Utility',
    location: 'Well House',
    status: 'available',
    lastMaintenance: '2023-06-01',
    nextMaintenance: '2023-12-01',
    purchaseDate: '2021-02-28',
    purchasePrice: 8500,
    currentValue: 7200
  },
  {
    id: 'RES004',
    name: 'Main Barn',
    type: 'Facility',
    category: 'Building',
    location: 'Central Farm',
    status: 'available',
    lastMaintenance: '2023-01-15',
    nextMaintenance: '2024-01-15',
    purchaseDate: '2015-05-20',
    purchasePrice: 120000,
    currentValue: 150000
  },
  {
    id: 'RES005',
    name: 'Feed Mixer',
    type: 'Equipment',
    category: 'Processing',
    location: 'Feed Room',
    status: 'maintenance',
    lastMaintenance: '2023-06-15',
    nextMaintenance: '2023-09-15',
    purchaseDate: '2022-01-10',
    purchasePrice: 12000,
    currentValue: 10800
  },
  {
    id: 'RES006',
    name: 'North Pasture',
    type: 'Land',
    category: 'Grazing',
    location: 'North Farm',
    status: 'available',
    lastMaintenance: '2023-04-10',
    nextMaintenance: '2023-10-10',
    purchaseDate: '2010-08-15',
    purchasePrice: 250000,
    currentValue: 320000
  },
  {
    id: 'RES007',
    name: 'Milking Machine',
    type: 'Equipment',
    category: 'Processing',
    location: 'Dairy Barn',
    status: 'in-use',
    lastMaintenance: '2023-05-28',
    nextMaintenance: '2023-08-28',
    purchaseDate: '2021-11-05',
    purchasePrice: 35000,
    currentValue: 31000
  },
  {
    id: 'RES008',
    name: 'Storage Barn',
    type: 'Facility',
    category: 'Building',
    location: 'East Farm',
    status: 'maintenance',
    lastMaintenance: '2022-12-10',
    nextMaintenance: '2023-06-10',
    purchaseDate: '2018-03-20',
    purchasePrice: 85000,
    currentValue: 92000
  },
];

const ResourcesList: React.FC = () => {
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Filter resources based on tab and search term
  const filteredResources = mockResources.filter(resource => {
    const matchesTab =
      (tabValue === 0) || // All
      (tabValue === 1 && resource.type === 'Equipment') ||
      (tabValue === 2 && resource.type === 'Facility') ||
      (tabValue === 3 && resource.type === 'Land');

    const matchesSearch =
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.id.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesTab && matchesSearch;
  });

  return (
    <Box p={4}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">Resources List</Typography>
        <CustomButton variant="contained" color="primary" startIcon={<Add />}>
          Add Resource
        </CustomButton>
      </Box>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'primary.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Total Resources</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockResources.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'success.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Available</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockResources.filter(r => r.status === 'available').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'warning.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">In Use</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockResources.filter(r => r.status === 'in-use').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={3}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'error.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Maintenance</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                {mockResources.filter(r => r.status === 'maintenance').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
        <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="All Resources" />
              <Tab label="Equipment" />
              <Tab label="Facilities" />
              <Tab label="Land" />
            </Tabs>
            <Box display="flex" alignItems="center">
              <TextField
                placeholder="Search resources..."
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ mr: 1 }}
              />
              <IconButton>
                <FilterList />
              </IconButton>
            </Box>
          </Box>
        </Box>

        <TableContainer>
          <Table>
            <TableHead sx={{ bgcolor: 'primary.light' }}>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Next Maintenance</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredResources.map((resource) => {
                let statusColor = 'info';
                if (resource.status === 'available') statusColor = 'success';
                if (resource.status === 'in-use') statusColor = 'warning';
                if (resource.status === 'maintenance') statusColor = 'error';

                return (
                  <TableRow key={resource.id} hover>
                    <TableCell>{resource.id}</TableCell>
                    <TableCell>{resource.name}</TableCell>
                    <TableCell>
                      <Chip
                        label={resource.type}
                        size="small"
                        color="primary"
                      />
                    </TableCell>
                    <TableCell>{resource.category}</TableCell>
                    <TableCell>{resource.location}</TableCell>
                    <TableCell>
                      <Chip
                        label={resource.status.replace('-', ' ').charAt(0).toUpperCase() + resource.status.replace('-', ' ').slice(1)}
                        color={statusColor as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{new Date(resource.nextMaintenance).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary" onClick={() => navigate(`/resources/detail/${resource.id}`)}>
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="primary" onClick={() => navigate(`/resources/edit/${resource.id}`)}>
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default ResourcesList;
