import React from 'react';
import { Routes, Route } from 'react-router-dom';
import HeatCalendar from './HeatCalendar';
import BreedingSchedule from './BreedingSchedule';
import PregnancyTracking from './PregnancyTracking';
import BirthPredictions from './BirthPredictions';
import BreedingRecords from './BreedingRecords';

const Breeding: React.FC = () => {
  return (
    <Routes>
      <Route index element={<BreedingSchedule />} />
      <Route path="heat-calendar" element={<HeatCalendar />} />
      <Route path="schedule" element={<BreedingSchedule />} />
      <Route path="pregnancy" element={<PregnancyTracking />} />
      <Route path="predictions" element={<BirthPredictions />} />
      <Route path="records" element={<BreedingRecords />} />
    </Routes>
  );
};

export default Breeding;
