/**
 * Simple MongoDB Connection Test
 */

const { MongoClient } = require('mongodb');

// MongoDB connection string - use double quotes to avoid escaping issues
const uri = "mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const dbName = "AMPD_Live_Stock";

async function testConnection() {
  const client = new MongoClient(uri);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB server successfully!');

    const db = client.db(dbName);
    console.log(`Connected to database: ${db.databaseName}`);

    // Ping the database
    await db.command({ ping: 1 });
    console.log('Ping command successful!');

    // List collections
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections in database`);

    if (collections.length > 0) {
      console.log('Collections:');
      for (const collection of collections) {
        const count = await db.collection(collection.name).countDocuments();
        console.log(`- ${collection.name}: ${count} documents`);
      }
    }
  } catch (error) {
    console.error('MongoDB connection test failed!');
    console.error('Error:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the test
testConnection()
  .then(() => console.log('Test completed'))
  .catch(error => console.error('Test failed:', error));
