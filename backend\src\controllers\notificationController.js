const NotificationService = require('../utils/notificationService');
const logger = require('../utils/logger');

/**
 * Notification controller for handling notification-related requests
 */
const notificationController = {
  /**
   * Send a notification
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  sendNotification: async (req, res) => {
    try {
      const {
        title,
        message,
        type,
        recipients,
        metadata,
        priority
      } = req.body;

      // Validate required fields
      if (!title || !message || !recipients || !Array.isArray(recipients) || recipients.length === 0) {
        return res.status(400).json({
          error: {
            message: 'Missing required fields',
            details: 'Title, message, and recipients are required',
            status: 400
          }
        });
      }

      // Send notification
      const result = await NotificationService.sendNotification({
        title,
        message,
        type: type || 'info',
        recipients,
        metadata: metadata || {},
        priority: priority || 'medium',
        createdBy: req.user.id
      });

      res.status(200).json({
        message: 'Notification sent successfully',
        result
      });
    } catch (error) {
      logger.error('Error sending notification:', error);
      res.status(500).json({
        error: {
          message: 'Error sending notification',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get all notifications with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllNotifications: async (req, res) => {
    try {
      // In a real implementation, this would fetch notifications from the database
      // For now, we'll return mock data
      const notifications = [
        {
          id: 'NOTIF-001',
          title: 'Animal Health Alert',
          message: 'Cow #TAG001 requires immediate medical attention',
          type: 'critical',
          priority: 'high',
          createdAt: new Date(Date.now() - 3600000),
          status: 'sent',
          channels: ['app', 'email', 'sms']
        },
        {
          id: 'NOTIF-002',
          title: 'Feeding Schedule',
          message: 'Feeding schedule for Paddock B is due today',
          type: 'info',
          priority: 'medium',
          createdAt: new Date(Date.now() - 86400000),
          status: 'sent',
          channels: ['app', 'email']
        },
        {
          id: 'NOTIF-003',
          title: 'Birth Prediction',
          message: 'Cow #TAG005 is expected to give birth in the next 24 hours',
          type: 'warning',
          priority: 'high',
          createdAt: new Date(Date.now() - 43200000),
          status: 'sent',
          channels: ['app', 'email', 'sms']
        }
      ];

      res.status(200).json({
        notifications,
        pagination: {
          total: notifications.length,
          page: 1,
          limit: 10,
          pages: 1
        }
      });
    } catch (error) {
      logger.error('Error getting notifications:', error);
      res.status(500).json({
        error: {
          message: 'Error getting notifications',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get notification by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getNotificationById: async (req, res) => {
    try {
      // In a real implementation, this would fetch a notification from the database
      // For now, we'll return mock data
      const notification = {
        id: req.params.id,
        title: 'Animal Health Alert',
        message: 'Cow #TAG001 requires immediate medical attention',
        type: 'critical',
        priority: 'high',
        createdAt: new Date(Date.now() - 3600000),
        status: 'sent',
        channels: ['app', 'email', 'sms'],
        results: {
          email: { success: true, sentTo: 3 },
          sms: { success: true, sentTo: 2 },
          app: { success: true, sentTo: 5 }
        }
      };

      res.status(200).json({
        notification
      });
    } catch (error) {
      logger.error('Error getting notification by ID:', error);
      res.status(500).json({
        error: {
          message: 'Error getting notification',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Mark notification as read
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  markAsRead: async (req, res) => {
    try {
      // In a real implementation, this would update the notification in the database
      // For now, we'll just return success
      res.status(200).json({
        message: 'Notification marked as read',
        notificationId: req.params.id
      });
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      res.status(500).json({
        error: {
          message: 'Error marking notification as read',
          details: error.message,
          status: 500
        }
      });
    }
  }
};

module.exports = notificationController;