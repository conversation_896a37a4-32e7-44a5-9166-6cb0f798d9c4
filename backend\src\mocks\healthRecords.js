exports.mockHealthRecords = [
  {
    id: 1,
    animalId: 1,
    recordType: 'Vaccination',
    date: '2023-01-15',
    description: 'Annual vaccination against Bovine Respiratory Disease',
    medication: 'Bovi-Shield Gold FP5',
    dosage: '5ml',
    administeredBy: 'Dr. <PERSON>',
    cost: 45,
    followUpDate: null,
    notes: 'No adverse reactions observed'
  },
  {
    id: 2,
    animalId: 13,
    recordType: 'Treatment',
    date: '2023-03-10',
    description: 'Respiratory infection treatment',
    medication: 'Draxxin',
    dosage: '10ml',
    administeredBy: 'Dr. <PERSON>',
    cost: 85,
    followUpDate: '2023-03-17',
    notes: 'Showing signs of improvement, monitor closely'
  },
  {
    id: 3,
    animalId: 6,
    recordType: 'Parasite Control',
    date: '2023-02-20',
    description: 'Routine deworming',
    medication: 'Ivermectin',
    dosage: '3ml',
    administeredBy: '<PERSON>',
    cost: 15,
    followUpDate: '2023-05-20',
    notes: 'Part of quarterly parasite control program'
  },
  {
    id: 4,
    animalId: 2,
    recordType: 'Examination',
    date: '2023-01-30',
    description: 'Breeding soundness examination',
    medication: null,
    dosage: null,
    administeredBy: 'Dr. <PERSON>',
    cost: 120,
    followUpDate: null,
    notes: 'Passed all fertility tests, cleared for breeding'
  },
  {
    id: 5,
    animalId: 10,
    recordType: 'Vaccination',
    date: '2023-02-05',
    description: 'Swine influenza vaccination',
    medication: 'FluSure XP',
    dosage: '2ml',
    administeredBy: 'Dr. Michael Brown',
    cost: 30,
    followUpDate: null,
    notes: 'Booster required in 3 weeks'
  }
];

exports.mockVeterinaryVisits = [
  {
    id: 1,
    date: '2023-03-10',
    veterinarian: 'Dr. Sarah Johnson',
    purpose: 'Quarterly herd health check',
    animalsExamined: [1, 2, 3, 4, 5],
    findings: 'All animals in good health. Recommended updated vaccination schedule.',
    recommendations: 'Increase mineral supplementation for lactating cows',
    cost: 350,
    nextVisitDate: '2023-06-10'
  },
  {
    id: 2,
    date: '2023-03-12',
    veterinarian: 'Dr. Michael Brown',
    purpose: 'Emergency call - respiratory distress',
    animalsExamined: [13],
    findings: 'Bovine respiratory disease diagnosed. Temperature 104.2°F.',
    recommendations: 'Isolate animal, administer antibiotics, monitor closely',
    cost: 175,
    nextVisitDate: '2023-03-17'
  }
];

exports.mockQuarantineRecords = [
  {
    id: 1,
    animalId: 13,
    startDate: '2023-03-10',
    expectedEndDate: '2023-03-24',
    actualEndDate: null,
    reason: 'Respiratory infection',
    location: 'Isolation Pen',
    treatmentPlan: 'Draxxin 10ml, monitor temperature twice daily',
    status: 'Active',
    notes: 'Showing improvement as of 03/15'
  }
];