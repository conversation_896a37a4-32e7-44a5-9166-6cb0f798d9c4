import { alpha, Theme } from '@mui/material';

/**
 * Enhanced design utilities for consistent and modern UI elements
 */

/**
 * Creates a gradient background with subtle texture
 * @param theme MUI theme
 * @param color Base color for the gradient
 * @param opacity Opacity level (0-1)
 * @returns CSS string for background
 */
export const createGradientBackground = (theme: Theme, color: string, opacity: number = 0.05) => {
  return `
    linear-gradient(135deg, 
      ${alpha(color, opacity + 0.02)}, 
      ${alpha(color, opacity)}
    ),
    linear-gradient(to right, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.98)})
  `;
};

/**
 * Creates a glass morphism effect for cards and containers
 * @param theme MUI theme
 * @param color Accent color
 * @param elevation Elevation level (0-5)
 * @returns Object with CSS properties
 */
export const createGlassMorphism = (theme: Theme, color: string, elevation: number = 1) => {
  return {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(theme.palette.background.paper, 0.8)})`,
    backdropFilter: 'blur(10px)',
    borderRadius: '12px',
    border: `1px solid ${alpha(color, 0.1)}`,
    boxShadow: elevation > 0 ? `0 ${elevation * 4}px ${elevation * 8}px ${alpha(color, 0.15)}` : 'none',
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    '&:hover': elevation > 0 ? {
      boxShadow: `0 ${elevation * 5}px ${elevation * 10}px ${alpha(color, 0.2)}`,
      transform: 'translateY(-2px)',
    } : {}
  };
};

/**
 * Creates a neuomorphic effect for buttons and interactive elements
 * @param theme MUI theme
 * @param color Base color
 * @param isPressed Whether the element is in pressed state
 * @returns Object with CSS properties
 */
export const createNeumorphism = (theme: Theme, color: string, isPressed: boolean = false) => {
  const isDark = theme.palette.mode === 'dark';
  const baseColor = isDark ? theme.palette.background.paper : theme.palette.background.default;
  
  return {
    backgroundColor: baseColor,
    borderRadius: '12px',
    boxShadow: isPressed
      ? `inset 3px 3px 6px ${alpha('#000', isDark ? 0.3 : 0.1)}, 
         inset -3px -3px 6px ${alpha('#fff', isDark ? 0.05 : 0.1)}`
      : `5px 5px 10px ${alpha('#000', isDark ? 0.3 : 0.1)}, 
         -5px -5px 10px ${alpha('#fff', isDark ? 0.05 : 0.1)}`,
    border: `1px solid ${alpha(color, 0.05)}`,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      boxShadow: isPressed
        ? `inset 3px 3px 6px ${alpha('#000', isDark ? 0.3 : 0.1)}, 
           inset -3px -3px 6px ${alpha('#fff', isDark ? 0.05 : 0.1)}`
        : `7px 7px 14px ${alpha('#000', isDark ? 0.3 : 0.1)}, 
           -7px -7px 14px ${alpha('#fff', isDark ? 0.05 : 0.1)}`,
    }
  };
};

/**
 * Creates a floating effect for cards and containers
 * @param theme MUI theme
 * @param color Accent color
 * @returns Object with CSS properties
 */
export const createFloatingEffect = (theme: Theme, color: string) => {
  return {
    borderRadius: '12px',
    boxShadow: `0 10px 30px ${alpha(color, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    '&:hover': {
      boxShadow: `0 15px 40px ${alpha(color, 0.2)}`,
      transform: 'translateY(-5px)',
    }
  };
};

/**
 * Creates a gradient text effect
 * @param startColor Start color of gradient
 * @param endColor End color of gradient
 * @returns Object with CSS properties
 */
export const createGradientText = (startColor: string, endColor: string) => {
  return {
    background: `linear-gradient(135deg, ${startColor}, ${endColor})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
    backgroundClip: 'text',
    textFillColor: 'transparent',
    display: 'inline-block'
  };
};

/**
 * Creates a subtle pattern background
 * @param theme MUI theme
 * @param color Base color
 * @param opacity Opacity level (0-1)
 * @returns CSS string for background
 */
export const createPatternBackground = (theme: Theme, color: string, opacity: number = 0.05) => {
  const patternColor = alpha(color, opacity);
  
  return `
    radial-gradient(${patternColor} 2px, transparent 2px) 0 0 / 20px 20px,
    radial-gradient(${patternColor} 1px, transparent 1px) 10px 10px / 20px 20px,
    linear-gradient(to right, ${alpha(theme.palette.background.paper, 0.97)}, ${alpha(theme.palette.background.paper, 0.98)})
  `;
};

/**
 * Creates a glow effect for highlighting important elements
 * @param color Glow color
 * @param intensity Intensity of the glow (0-1)
 * @returns Object with CSS properties
 */
export const createGlowEffect = (color: string, intensity: number = 0.5) => {
  return {
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: -1,
      borderRadius: 'inherit',
      boxShadow: `0 0 20px ${alpha(color, intensity)}`,
      opacity: 0,
      transition: 'opacity 0.3s ease'
    },
    '&:hover::after': {
      opacity: 1
    }
  };
};

/**
 * Creates a color transition effect for elements
 * @param theme MUI theme
 * @param startColor Start color
 * @param endColor End color
 * @returns Object with CSS properties
 */
export const createColorTransition = (theme: Theme, startColor: string, endColor: string) => {
  return {
    background: startColor,
    transition: 'background 0.5s ease',
    '&:hover': {
      background: endColor
    }
  };
};

/**
 * Creates a ripple effect for interactive elements
 * @param color Ripple color
 * @returns Object with CSS properties
 */
export const createRippleEffect = (color: string) => {
  return {
    position: 'relative',
    overflow: 'hidden',
    '&::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      left: '50%',
      width: 0,
      height: 0,
      backgroundColor: alpha(color, 0.3),
      borderRadius: '50%',
      transform: 'translate(-50%, -50%)',
      opacity: 0,
      transition: 'width 0.6s ease-out, height 0.6s ease-out, opacity 0.6s ease-out'
    },
    '&:active::after': {
      width: '300px',
      height: '300px',
      opacity: 1,
      transition: 'width 0.6s ease-out, height 0.6s ease-out, opacity 0s'
    }
  };
};

/**
 * Creates a 3D button effect
 * @param theme MUI theme
 * @param baseColor Base color
 * @param depth Depth of 3D effect (1-5)
 * @returns Object with CSS properties
 */
export const create3DButtonEffect = (theme: Theme, baseColor: string, depth: number = 3) => {
  const isDark = theme.palette.mode === 'dark';
  const shadowColor = isDark ? '#000' : '#888';
  const highlightColor = isDark ? '#444' : '#fff';
  
  return {
    backgroundColor: baseColor,
    borderRadius: '8px',
    border: 'none',
    boxShadow: `0 ${depth}px 0 ${shadowColor}`,
    transform: 'translateY(0)',
    transition: 'all 0.1s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 ${depth + 2}px 0 ${shadowColor}`,
    },
    '&:active': {
      transform: `translateY(${depth - 1}px)`,
      boxShadow: `0 1px 0 ${shadowColor}`,
    }
  };
};
