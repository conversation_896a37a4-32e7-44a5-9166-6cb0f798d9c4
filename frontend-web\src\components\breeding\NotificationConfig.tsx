import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, Switch, FormControlLabel, TextField, Divider, Chip, IconButton, Grid, FormControl, InputLabel, Select, MenuItem, Checkbox, ListItemText, OutlinedInput, SelectChangeEvent, Slider, Tooltip, useTheme, alpha } from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Notifications as NotificationsIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  NotificationsActive as NotificationsActiveIcon,
  Settings as SettingsIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { BirthNotificationConfig, NotificationType, NotificationRecipient } from '../../services/notificationService';
import { useSnackbar } from '../../hooks/useSnackbar';

type RecipientInput = Omit<NotificationRecipient, 'id'>;
type Recipient = NotificationRecipient;

interface NotificationConfigProps {
  animalId?: string;
  initialConfig?: BirthNotificationConfig;
  onSave?: (config: BirthNotificationConfig) => void;
  isGlobal?: boolean;
}

const NotificationConfig: React.FC<NotificationConfigProps> = ({
  animalId,
  initialConfig,
  onSave,
  isGlobal = false
}) => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();

  // Default configuration
  const defaultConfig: BirthNotificationConfig = {
    enabled: true,
    notifyDaysBefore: [1, 3, 7, 14, 30],
    recipients: [],
    notificationTypes: ['sms', 'email', 'app'],
    escalationEnabled: true,
    escalationThresholds: {
      critical: 2,
      high: 7,
      medium: 14
    }
  };

  // State for the configuration
  const [config, setConfig] = useState<BirthNotificationConfig>(initialConfig || defaultConfig);

  // State for the new recipient form
  const [newRecipient, setNewRecipient] = useState<RecipientInput>({
    name: '',
    phone: '',
    email: '',
    notificationTypes: ['sms'],
    role: ''
  });

  // State for the new notification day
  const [newNotificationDay, setNewNotificationDay] = useState<number>(0);

  // State for the custom message
  const [customMessage, setCustomMessage] = useState<string>(config.customMessage || '');

  // Update config when initialConfig changes
  useEffect(() => {
    if (initialConfig) {
      setConfig(initialConfig);
      setCustomMessage(initialConfig.customMessage || '');
    }
  }, [initialConfig]);

  // Handle toggling the enabled state
  const handleToggleEnabled = () => {
    setConfig(prev => ({
      ...prev,
      enabled: !prev.enabled
    }));
  };

  // Handle toggling the escalation enabled state
  const handleToggleEscalation = () => {
    setConfig(prev => ({
      ...prev,
      escalationEnabled: !prev.escalationEnabled
    }));
  };

  // Handle changing the escalation thresholds
  const handleEscalationThresholdChange = (
    threshold: 'critical' | 'high' | 'medium',
    value: number
  ) => {
    setConfig(prev => ({
      ...prev,
      escalationThresholds: {
        ...prev.escalationThresholds!,
        [threshold]: value
      }
    }));
  };

  // Handle changing the notification types
  const handleNotificationTypesChange = (event: SelectChangeEvent<NotificationType[]>) => {
    const value = event.target.value as NotificationType[];
    setConfig(prev => ({
      ...prev,
      notificationTypes: value
    }));
  };

  // Handle adding a new notification day
  const handleAddNotificationDay = () => {
    if (newNotificationDay <= 0) {
      showSnackbar('Please enter a valid number of days', 'error');
      return;
    }

    if (config.notifyDaysBefore.includes(newNotificationDay)) {
      showSnackbar('This notification day already exists', 'error');
      return;
    }

    setConfig(prev => ({
      ...prev,
      notifyDaysBefore: [...prev.notifyDaysBefore, newNotificationDay].sort((a, b) => a - b)
    }));

    setNewNotificationDay(0);
  };

  // Handle removing a notification day
  const handleRemoveNotificationDay = (day: number) => {
    setConfig(prev => ({
      ...prev,
      notifyDaysBefore: prev.notifyDaysBefore.filter(d => d !== day)
    }));
  };

  // Handle changing the new recipient form
  const handleNewRecipientChange = (field: keyof RecipientInput, value: any) => {
    setNewRecipient(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle changing the new recipient notification types
  const handleNewRecipientNotificationTypesChange = (event: SelectChangeEvent<NotificationType[]>) => {
    const value = event.target.value as NotificationType[];
    setNewRecipient(prev => ({
      ...prev,
      notificationTypes: value
    }));
  };

  // Handle adding a new recipient
  const handleAddRecipient = () => {
    if (!newRecipient.name) {
      showSnackbar('Please enter a recipient name', 'error');
      return;
    }

    if (newRecipient.notificationTypes.includes('sms') && !newRecipient.phone) {
      showSnackbar('Please enter a phone number for SMS notifications', 'error');
      return;
    }

    if (newRecipient.notificationTypes.includes('email') && !newRecipient.email) {
      showSnackbar('Please enter an email address for email notifications', 'error');
      return;
    }

    const newRecipientWithId: Recipient = {
      ...newRecipient,
      id: `recipient-${Date.now()}-${Math.floor(Math.random() * 10000)}`
    };

    setConfig(prev => ({
      ...prev,
      recipients: [...prev.recipients, newRecipientWithId]
    }));

    // Reset the form
    setNewRecipient({
      name: '',
      phone: '',
      email: '',
      notificationTypes: ['sms'],
      role: ''
    });
  };

  // Handle removing a recipient
  const handleRemoveRecipient = (id: string) => {
    setConfig(prev => ({
      ...prev,
      recipients: prev.recipients.filter(r => r.id !== id)
    }));
  };

  // Handle saving the custom message
  const handleSaveCustomMessage = () => {
    setConfig(prev => ({
      ...prev,
      customMessage
    }));

    showSnackbar('Custom message saved', 'success');
  };

  // Handle saving the configuration
  const handleSaveConfig = () => {
    if (onSave) {
      onSave(config);
    }

    showSnackbar('Notification configuration saved', 'success');
  };

  // Get notification type icon
  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return <PhoneIcon fontSize="small" />;
      case 'voice':
        return <PhoneIcon fontSize="small" />;
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'whatsapp':
        return <WhatsAppIcon fontSize="small" />;
      case 'app':
        return <NotificationsIcon fontSize="small" />;
      default:
        return <NotificationsIcon fontSize="small" />;
    }
  };

  // Get notification type color
  const getNotificationTypeColor = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return theme.palette.primary.main;
      case 'voice':
        return theme.palette.error.main;
      case 'email':
        return theme.palette.info.main;
      case 'whatsapp':
        return theme.palette.success.main;
      case 'app':
        return theme.palette.warning.main;
      default:
        return theme.palette.primary.main;
    }
  };

  return (
    <Card
      sx={{
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        overflow: 'hidden',
        mb: 4,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
      }}
    >
      <CardContent>
        <Box display="flex" alignItems="center" mb={2}>
          <NotificationsActiveIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="h6" fontWeight="bold">
            {isGlobal ? 'Global Birth Notification Settings' : 'Birth Notification Settings'}
          </Typography>
          <Box flexGrow={1} />
          <FormControlLabel
            control={
              <Switch
                checked={config.enabled}
                onChange={handleToggleEnabled}
                color="primary"
              />
            }
            label={config.enabled ? 'Enabled' : 'Disabled'}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        {/* Notification Days Section */}
        <Box mb={4}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Notification Schedule
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Configure when notifications should be sent before the expected birth date.
          </Typography>

          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
            {config.notifyDaysBefore.map(day => (
              <Chip
                key={day}
                label={`${day} ${day === 1 ? 'day' : 'days'} before`}
                onDelete={() => handleRemoveNotificationDay(day)}
                color={
                  day <= (config.escalationThresholds?.critical || 2) ? 'error' :
                  day <= (config.escalationThresholds?.high || 7) ? 'warning' :
                  day <= (config.escalationThresholds?.medium || 14) ? 'info' :
                  'default'
                }
              />
            ))}
          </Box>

          <Box display="flex" alignItems="center" gap={2}>
            <TextField
              label="Days before birth"
              type="number"
              value={newNotificationDay || ''}
              onChange={(e) => setNewNotificationDay(parseInt(e.target.value) || 0)}
              size="small"
              InputProps={{ inputProps: { min: 1 } }}
              sx={{ width: 150 }}
            />
            <CustomButton
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={handleAddNotificationDay}
              disabled={!newNotificationDay}
            >
              Add
            </CustomButton>
          </Box>
        </Box>

        {/* Escalation Settings */}
        <Box mb={4}>
          <Box display="flex" alignItems="center" mb={2}>
            <Typography variant="subtitle1" fontWeight="bold">
              Priority Escalation
            </Typography>
            <Tooltip title="Automatically increase notification priority as the birth date approaches">
              <IconButton size="small" sx={{ ml: 1 }}>
                <InfoIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Box flexGrow={1} />
            <FormControlLabel
              control={
                <Switch
                  checked={config.escalationEnabled}
                  onChange={handleToggleEscalation}
                  color="primary"
                />
              }
              label={config.escalationEnabled ? 'Enabled' : 'Disabled'}
            />
          </Box>

          {config.escalationEnabled && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                  Critical Priority (days before birth)
                </Typography>
                <Slider
                  value={config.escalationThresholds?.critical || 2}
                  onChange={(_, value) => handleEscalationThresholdChange('critical', value as number)}
                  min={1}
                  max={10}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  sx={{
                    color: theme.palette.error.main,
                    '& .MuiSlider-valueLabel': {
                      backgroundColor: theme.palette.error.main
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                  High Priority (days before birth)
                </Typography>
                <Slider
                  value={config.escalationThresholds?.high || 7}
                  onChange={(_, value) => handleEscalationThresholdChange('high', value as number)}
                  min={config.escalationThresholds?.critical || 2}
                  max={20}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  sx={{
                    color: theme.palette.warning.main,
                    '& .MuiSlider-valueLabel': {
                      backgroundColor: theme.palette.warning.main
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Typography variant="body2" gutterBottom>
                  Medium Priority (days before birth)
                </Typography>
                <Slider
                  value={config.escalationThresholds?.medium || 14}
                  onChange={(_, value) => handleEscalationThresholdChange('medium', value as number)}
                  min={config.escalationThresholds?.high || 7}
                  max={30}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                  sx={{
                    color: theme.palette.info.main,
                    '& .MuiSlider-valueLabel': {
                      backgroundColor: theme.palette.info.main
                    }
                  }}
                />
              </Grid>
            </Grid>
          )}
        </Box>

        {/* Notification Types */}
        <Box mb={4}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Notification Methods
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Select the notification methods to use for birth alerts.
          </Typography>

          <FormControl sx={{ width: '100%', maxWidth: 500 }}>
            <InputLabel id="notification-types-label">Notification Methods</InputLabel>
            <Select
              labelId="notification-types-label"
              multiple
              value={config.notificationTypes}
              onChange={handleNotificationTypesChange}
              input={<OutlinedInput label="Notification Methods" />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip
                      key={value}
                      label={value.toUpperCase()}
                      icon={getNotificationTypeIcon(value)}
                      size="small"
                      sx={{
                        bgcolor: alpha(getNotificationTypeColor(value), 0.1),
                        color: getNotificationTypeColor(value),
                        '& .MuiChip-icon': {
                          color: getNotificationTypeColor(value)
                        }
                      }}
                    />
                  ))}
                </Box>
              )}
            >
              <MenuItem value="sms">
                <Checkbox checked={config.notificationTypes.includes('sms')} />
                <ListItemText
                  primary="SMS"
                  secondary="Text message alerts to mobile phones"
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
              <MenuItem value="voice">
                <Checkbox checked={config.notificationTypes.includes('voice')} />
                <ListItemText
                  primary="Voice Call"
                  secondary="Automated voice calls for critical alerts"
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
              <MenuItem value="email">
                <Checkbox checked={config.notificationTypes.includes('email')} />
                <ListItemText
                  primary="Email"
                  secondary="Email notifications with detailed information"
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
              <MenuItem value="whatsapp">
                <Checkbox checked={config.notificationTypes.includes('whatsapp')} />
                <ListItemText
                  primary="WhatsApp"
                  secondary="WhatsApp messages for instant delivery"
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
              <MenuItem value="app">
                <Checkbox checked={config.notificationTypes.includes('app')} />
                <ListItemText
                  primary="In-App"
                  secondary="Notifications within the application"
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Custom Message */}
        <Box mb={4}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Custom Message Template
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Customize the notification message. Use placeholders like {'{animalName}'}, {'{animalId}'}, and {'{daysRemaining}'}.
          </Typography>

          <TextField
            fullWidth
            multiline
            rows={3}
            label="Custom Message"
            value={customMessage}
            onChange={(e) => setCustomMessage(e.target.value)}
            placeholder="BIRTH ALERT: {animalName} (ID: {animalId}) is expected to give birth in {daysRemaining} days. Please prepare accordingly."
            sx={{ mb: 2 }}
          />

          <CustomButton
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={handleSaveCustomMessage}
            disabled={!customMessage}
          >
            Save Message
          </CustomButton>
        </Box>

        {/* Recipients */}
        <Box mb={4}>
          <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
            Recipients
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Add people who should receive birth notifications.
          </Typography>

          {config.recipients.length > 0 ? (
            <Grid container spacing={2} mb={3}>
              {config.recipients.map(recipient => (
                <Grid item xs={12} sm={6} md={4} key={recipient.id}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={1}>
                        <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="subtitle2" fontWeight="bold">
                          {recipient.name}
                        </Typography>
                        <Box flexGrow={1} />
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleRemoveRecipient(recipient.id)}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      {recipient.role && (
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Role: {recipient.role}
                        </Typography>
                      )}

                      {recipient.phone && (
                        <Box display="flex" alignItems="center" mb={0.5}>
                          <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2">{recipient.phone}</Typography>
                        </Box>
                      )}

                      {recipient.email && (
                        <Box display="flex" alignItems="center" mb={0.5}>
                          <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2">{recipient.email}</Typography>
                        </Box>
                      )}

                      <Box mt={1}>
                        <Typography variant="caption" color="text.secondary">
                          Notification Methods:
                        </Typography>
                        <Box display="flex" flexWrap="wrap" gap={0.5} mt={0.5}>
                          {recipient.notificationTypes.map(type => (
                            <Chip
                              key={type}
                              label={type.toUpperCase()}
                              icon={getNotificationTypeIcon(type)}
                              size="small"
                              sx={{
                                height: 24,
                                '& .MuiChip-label': { px: 1, fontSize: '0.7rem' },
                                bgcolor: alpha(getNotificationTypeColor(type), 0.1),
                                color: getNotificationTypeColor(type),
                                '& .MuiChip-icon': {
                                  color: getNotificationTypeColor(type),
                                  fontSize: '1rem'
                                }
                              }}
                            />
                          ))}
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : (
            <Box
              sx={{
                p: 3,
                textAlign: 'center',
                bgcolor: 'background.paper',
                borderRadius: 1,
                mb: 3
              }}
            >
              <Typography color="text.secondary">
                No recipients added yet. Add recipients below.
              </Typography>
            </Box>
          )}

          <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent>
              <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                Add New Recipient
              </Typography>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Name"
                    value={newRecipient.name}
                    onChange={(e) => handleNewRecipientChange('name', e.target.value)}
                    size="small"
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Role (optional)"
                    value={newRecipient.role || ''}
                    onChange={(e) => handleNewRecipientChange('role', e.target.value)}
                    size="small"
                    placeholder="e.g., Veterinarian, Farm Manager"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    value={newRecipient.phone || ''}
                    onChange={(e) => handleNewRecipientChange('phone', e.target.value)}
                    size="small"
                    placeholder="+27 ************"
                    required={newRecipient.notificationTypes.some(t => t === 'sms' || t === 'voice' || t === 'whatsapp')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email Address"
                    value={newRecipient.email || ''}
                    onChange={(e) => handleNewRecipientChange('email', e.target.value)}
                    size="small"
                    placeholder="<EMAIL>"
                    required={newRecipient.notificationTypes.includes('email')}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="new-recipient-notification-types-label">Notification Methods</InputLabel>
                    <Select
                      labelId="new-recipient-notification-types-label"
                      multiple
                      value={newRecipient.notificationTypes}
                      onChange={handleNewRecipientNotificationTypesChange}
                      input={<OutlinedInput label="Notification Methods" />}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip
                              key={value}
                              label={value.toUpperCase()}
                              size="small"
                            />
                          ))}
                        </Box>
                      )}
                    >
                      <MenuItem value="sms">
                        <Checkbox checked={newRecipient.notificationTypes.includes('sms')} />
                        <ListItemText primary="SMS" />
                      </MenuItem>
                      <MenuItem value="voice">
                        <Checkbox checked={newRecipient.notificationTypes.includes('voice')} />
                        <ListItemText primary="Voice Call" />
                      </MenuItem>
                      <MenuItem value="email">
                        <Checkbox checked={newRecipient.notificationTypes.includes('email')} />
                        <ListItemText primary="Email" />
                      </MenuItem>
                      <MenuItem value="whatsapp">
                        <Checkbox checked={newRecipient.notificationTypes.includes('whatsapp')} />
                        <ListItemText primary="WhatsApp" />
                      </MenuItem>
                      <MenuItem value="app">
                        <Checkbox checked={newRecipient.notificationTypes.includes('app')} />
                        <ListItemText primary="In-App" />
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Box display="flex" justifyContent="flex-end" mt={2}>
                <CustomButton
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRecipient}
                  disabled={!newRecipient.name || (
                    (newRecipient.notificationTypes.some(t => t === 'sms' || t === 'voice' || t === 'whatsapp') && !newRecipient.phone) ||
                    (newRecipient.notificationTypes.includes('email') && !newRecipient.email)
                  )}
                >
                  Add Recipient
                </CustomButton>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* Save Button */}
        <Box display="flex" justifyContent="flex-end">
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={handleSaveConfig}
            size="large"
          >
            Save Configuration
          </CustomButton>
        </Box>
      </CardContent>
    </Card>
  );
};

export default NotificationConfig;
