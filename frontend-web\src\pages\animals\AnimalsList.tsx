import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, CardActions, Typography, Chip, TextField, Menu, MenuItem, Box, InputAdornment, useTheme, alpha, CircularProgress } from '@mui/material';
import {
  FilterList,
  Add,
  FileDownload,
  Pets,
  Search,
  Edit,
  Visibility,
  LocationOn,
  Scale,
  CalendarToday
} from '../../utils/iconImports';
import AnimalStatusBadge from '../../components/animals/AnimalStatusBadge';
import AnimalQuickView from '../../components/animals/AnimalQuickView';
import { useAnimalData } from '../../hooks/useAnimalData';
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import {  <PERSON><PERSON><PERSON><PERSON>ontainer, <PERSON><PERSON>leHeader, ModuleItemCard, withSubModuleTranslation , CustomButton } from '../../components/common';
import { getConsistentBackground } from '../../utils/backgroundThemes';
import { useLanguage } from '../../contexts/LanguageContext';

interface AnimalsListProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const AnimalsList: React.FC<AnimalsListProps> = ({
  translate: propTranslate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate: contextTranslate } = useLanguage();

  // Use the translate function from props if available, otherwise use the one from context
  const translate = propTranslate || contextTranslate;
  const [filterAnchor, setFilterAnchor] = useState<null | HTMLElement>(null);
  const [selectedAnimal, setSelectedAnimal] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [activeSpecies, setActiveSpecies] = useState<string | null>(null);

  // Use MongoDB if connected, otherwise use mock data
  const { isConnected } = useMongoDb();
  const { animals: mockAnimals } = useAnimalData();
  const { animals: mongoAnimals, loading: mongoLoading } = useMongoAnimalData();

  // Use MongoDB data if connected, otherwise use mock data
  const animals = isConnected && mongoAnimals ? mongoAnimals : mockAnimals;
  const loading = isConnected ? mongoLoading : false;

  const exportAnimals = () => {
    // Implement export functionality
    console.log('Exporting animals...');
  };

  const handleFilterSelect = (value: string) => {
    setActiveFilter(value);
    setFilterAnchor(null);
  };

  const handleSpeciesFilter = (species: string) => {
    setActiveSpecies(activeSpecies === species ? null : species);
  };

  const handleViewAnimal = (animal: any) => {
    navigate(`/animals/${animal.id}`);
  };

  const handleEditAnimal = (animal: any) => {
    navigate(`/animals/edit/${animal.id}`);
  };

  const filterOptions = [
    { label: translate('animals.filter.all'), value: 'all' },
    { label: translate('animals.filter.healthy'), value: 'healthy' },
    { label: translate('animals.filter.attention'), value: 'attention' },
    { label: translate('animals.filter.critical'), value: 'critical' },
    { label: translate('animals.filter.pregnant'), value: 'pregnant' }
  ];

  // Filter animals based on search term, active filter, and species
  const filteredAnimals = animals.filter(animal => {
    // Search filter
    const matchesSearch = searchTerm === '' ||
      animal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.tagNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.breed.toLowerCase().includes(searchTerm.toLowerCase());

    // Status filter
    const matchesStatus = activeFilter === 'all' ||
      (activeFilter === 'healthy' && animal.healthStatus === 'healthy') ||
      (activeFilter === 'pregnant' && animal.healthStatus === 'pregnant') ||
      (activeFilter === 'attention' && (animal.healthStatus === 'sick' || animal.healthStatus === 'injured'));

    // Species filter
    const matchesSpecies = activeSpecies === null || animal.species === activeSpecies;

    return matchesSearch && matchesStatus && matchesSpecies;
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Calculate age from birthDate
  const calculateAge = (birthDate?: Date | string) => {
    if (!birthDate) return 'Unknown';

    const birth = new Date(birthDate);
    const ageInMonths = Math.floor(
      (new Date().getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 30.44)
    );
    return ageInMonths < 12 ? `${ageInMonths} months` : `${Math.floor(ageInMonths / 12)} years`;
  };

  return (
    <ModuleContainer module="animals">
      <Box sx={{ width: '100%' }}>
        <ModuleHeader
          title={translateSubModule ? translateSubModule('title', "Animal List") : translate('animals.inventory.title')}
          subtitle={translateSubModule ? translateSubModule('subtitle', "Manage and view all your livestock") : translate('animals.inventory.subtitle')}
          module="animals"
          submodule="list"
          actionLabel={translateSubModule ? translateSubModule('action', "Add Animal") : translate('animals.add')}
          actionIcon={<Add />}
          onAction={() => navigate('/dashboard/animals/new')}
          icon={<Pets />}
        />

        <Box sx={{ px: 3, pb: 5 }}>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {/* Filters and Search */}
            <motion.div variants={itemVariants}>
              <Box sx={{ mb: 4, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
                <TextField
                  placeholder={translate('animals.search_placeholder')}
                  variant="outlined"
                  fullWidth
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ flex: 1 }}
                />
                <CustomButton
                  variant="outlined"
                  startIcon={<FilterList />}
                  onClick={(e) => setFilterAnchor(e.currentTarget)}
                  sx={{ minWidth: 120 }}
                >
                  {translate('common.filter')}
                </CustomButton>
                <Menu
                  anchorEl={filterAnchor}
                  open={Boolean(filterAnchor)}
                  onClose={() => setFilterAnchor(null)}
                >
                  {filterOptions.map((option) => (
                    <MenuItem
                      key={option.value}
                      onClick={() => handleFilterSelect(option.value)}
                      selected={activeFilter === option.value}
                    >
                      {option.label}
                    </MenuItem>
                  ))}
                </Menu>
                <CustomButton
                  variant="outlined"
                  startIcon={<FileDownload />}
                  onClick={exportAnimals}
                  sx={{ minWidth: 120 }}
                >
                  {translate('common.export')}
                </CustomButton>
                <CustomButton
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => navigate('/animals/new')}
                  sx={{ minWidth: 180 }}
                >
                  {translate('animals.add')}
                </CustomButton>
              </Box>
            </motion.div>

            {/* Species Tabs */}
            <motion.div variants={itemVariants}>
              <Box sx={{ mb: 4, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {[
                  { key: 'Cattle', translationKey: 'animals.species.cattle' },
                  { key: 'Sheep', translationKey: 'animals.species.sheep' },
                  { key: 'Goat', translationKey: 'animals.species.goat' },
                  { key: 'Horse', translationKey: 'animals.species.horse' },
                  { key: 'Game', translationKey: 'animals.species.game' }
                ].map((speciesObj) => (
                  <Chip
                    key={speciesObj.key}
                    label={translate(speciesObj.translationKey)}
                    onClick={() => handleSpeciesFilter(speciesObj.key)}
                    color={activeSpecies === speciesObj.key ? 'primary' : 'default'}
                    variant={activeSpecies === speciesObj.key ? 'filled' : 'outlined'}
                    sx={{
                      cursor: 'pointer',
                      fontWeight: activeSpecies === speciesObj.key ? 'bold' : 'normal',
                      px: 1
                    }}
                  />
                ))}
              </Box>
            </motion.div>

            {/* Animals Grid */}
            <motion.div variants={itemVariants}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
                  <CircularProgress />
                </Box>
              ) : (
                <Grid container spacing={3}>
                  {filteredAnimals.length > 0 ? (
                    filteredAnimals.map((animal, index) => (
                      <Grid item xs={12} sm={6} md={4} key={animal.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                        >
                          <ModuleItemCard
                            id={animal.id}
                            title={animal.name}
                            subtitle={`${animal.breed} • ${animal.gender} • Tag: ${animal.tagNumber}`}
                            module="animals"
                            backgroundImage={animal.imageUrl || undefined}
                            icon={<Pets />}
                            tags={[
                              { label: animal.species || 'Unknown', color: 'primary' },
                              { label: animal.status, color: animal.status === 'Active' ? 'success' : 'error' }
                            ]}
                            status={{
                              label: animal.healthStatus || 'healthy',
                              color: animal.healthStatus === 'healthy' ? 'success' :
                                    animal.healthStatus === 'pregnant' ? 'info' : 'warning'
                            }}
                            content={
                              <Box sx={{ mt: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <LocationOn sx={{ fontSize: 16, color: 'white', mr: 0.5, opacity: 0.9 }} />
                                  <Typography variant="body2" sx={{ color: 'white', opacity: 0.9 }}>
                                    {translate('animals.location')}: {animal.location}
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <Scale sx={{ fontSize: 16, color: 'white', mr: 0.5, opacity: 0.9 }} />
                                  <Typography variant="body2" sx={{ color: 'white', opacity: 0.9 }}>
                                    {translate('animals.weight')}: {animal.weight} kg
                                  </Typography>
                                </Box>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <CalendarToday sx={{ fontSize: 16, color: 'white', mr: 0.5, opacity: 0.9 }} />
                                  <Typography variant="body2" sx={{ color: 'white', opacity: 0.9 }}>
                                    {translate('animals.age')}: {calculateAge(animal.birthDate)}
                                  </Typography>
                                </Box>
                              </Box>
                            }
                            primaryAction={{
                              label: translate('common.view'),
                              onClick: () => handleViewAnimal(animal),
                              icon: <Visibility />
                            }}
                            secondaryAction={{
                              label: translate('common.edit'),
                              onClick: () => handleEditAnimal(animal),
                              icon: <Edit />
                            }}
                            height={320}
                          />
                        </motion.div>
                      </Grid>
                    ))
                  ) : (
                    <Box sx={{ width: '100%', py: 5, textAlign: 'center' }}>
                      <Typography variant="h6" color="text.secondary">
                        {translate('animals.no_animals_found')}
                      </Typography>
                      <CustomButton
                        variant="outlined"
                        sx={{ mt: 2 }}
                        onClick={() => {
                          setSearchTerm('');
                          setActiveFilter('all');
                          setActiveSpecies(null);
                        }}
                      >
                        {translate('common.clear_all')}
                      </CustomButton>
                    </Box>
                  )}
                </Grid>
              )}
            </motion.div>
          </motion.div>
        </Box>
      </Box>

      {/* Quick View Modal */}
      {selectedAnimal && (
        <AnimalQuickView
          animal={selectedAnimal}
          onClose={() => setSelectedAnimal(null)}
        />
      )}
    </ModuleContainer>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(AnimalsList, 'animals', 'list');
