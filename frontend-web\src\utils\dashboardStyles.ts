import { Theme, alpha } from '@mui/material';

/**
 * Utility functions for consistent dashboard styling across all modules
 */

/**
 * Get consistent card styling for all dashboard cards
 */
export const getDashboardCardStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    borderRadius: 2,
    overflow: 'hidden',
    background: alpha(theme.palette.background.paper, 0.8),
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(color, 0.1)}`,
    boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    '&:hover': {
      transform: 'translateY(-5px)',
      boxShadow: `0 8px 25px ${alpha(color, 0.15)}`,
    }
  };
};

/**
 * Get consistent chart container styling
 */
export const getChartContainerStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    p: 3,
    height: '100%',
    ...getDashboardCardStyle(theme, color)
  };
};

/**
 * Get consistent table container styling
 */
export const getTableContainerStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    p: 0,
    borderRadius: 2,
    overflow: 'hidden',
    background: alpha(theme.palette.background.paper, 0.8),
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(color, 0.1)}`,
    boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    '& .MuiTableHead-root': {
      background: `linear-gradient(135deg, ${alpha(color, 0.1)}, ${alpha(color, 0.05)})`,
    },
    '& .MuiTableRow-root:hover': {
      background: alpha(color, 0.05),
    }
  };
};

/**
 * Get consistent dashboard header styling
 */
export const getDashboardHeaderStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    mb: 4,
    p: 3,
    borderRadius: 2,
    background: `linear-gradient(135deg, ${alpha(color, 0.15)}, ${alpha(color, 0.05)})`,
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(color, 0.1)}`,
    boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
  };
};

/**
 * Get consistent dashboard action button styling
 */
export const getDashboardActionButtonStyle = (theme: Theme, color?: string) => {
  const buttonColor = color || theme.palette.primary.main;
  
  return {
    borderRadius: 8,
    textTransform: 'none',
    fontWeight: 'medium',
    boxShadow: `0 4px 10px ${alpha(buttonColor, 0.2)}`,
    background: `linear-gradient(135deg, ${buttonColor}, ${alpha(buttonColor, 0.8)})`,
    '&:hover': {
      boxShadow: `0 6px 15px ${alpha(buttonColor, 0.3)}`,
      background: `linear-gradient(135deg, ${buttonColor}, ${alpha(buttonColor, 0.9)})`,
      transform: 'translateY(-2px)'
    },
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
  };
};

/**
 * Get consistent dashboard tab styling
 */
export const getDashboardTabStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    borderRadius: '8px 8px 0 0',
    textTransform: 'none',
    fontWeight: 'medium',
    minHeight: 48,
    '&.Mui-selected': {
      color: color,
      fontWeight: 'bold',
    },
    '&:not(.Mui-selected)': {
      color: theme.palette.text.secondary,
    }
  };
};

/**
 * Get consistent dashboard tabs container styling
 */
export const getDashboardTabsContainerStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    '& .MuiTabs-indicator': {
      backgroundColor: color,
      height: 3,
      borderRadius: '3px 3px 0 0',
    }
  };
};

/**
 * Get consistent dashboard metric card styling
 */
export const getDashboardMetricCardStyle = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  
  return {
    p: 3,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    borderRadius: 2,
    background: alpha(theme.palette.background.paper, 0.8),
    backdropFilter: 'blur(10px)',
    border: `1px solid ${alpha(color, 0.1)}`,
    boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    '&:hover': {
      transform: 'translateY(-5px)',
      boxShadow: `0 8px 25px ${alpha(color, 0.15)}`,
    }
  };
};

/**
 * Get consistent dashboard background gradient
 */
export const getDashboardBackgroundGradient = (theme: Theme, moduleColor?: string) => {
  const color = moduleColor || theme.palette.primary.main;
  const secondaryColor = moduleColor ? alpha(moduleColor, 0.7) : theme.palette.secondary.main;
  
  return {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
    opacity: 0.05,
    background: `radial-gradient(circle at 10% 10%, ${color}, transparent 40%), 
                radial-gradient(circle at 90% 90%, ${secondaryColor}, transparent 40%)`
  };
};

/**
 * Get consistent dashboard content container styling
 */
export const getDashboardContentContainerStyle = (theme: Theme) => {
  return {
    flexGrow: 1,
    overflow: 'auto',
    p: 3,
    pb: 6,
    position: 'relative',
    '&::-webkit-scrollbar': {
      width: 8,
      height: 8,
    },
    '&::-webkit-scrollbar-track': {
      background: alpha(theme.palette.background.paper, 0.1),
      borderRadius: 4,
    },
    '&::-webkit-scrollbar-thumb': {
      background: alpha(theme.palette.primary.main, 0.2),
      borderRadius: 4,
      '&:hover': {
        background: alpha(theme.palette.primary.main, 0.3),
      }
    }
  };
};
