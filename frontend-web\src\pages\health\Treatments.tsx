import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, Avatar, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  LocalHospital,
  Healing,
  Notifications,
  Event
 } from '../../utils/iconImports';
import { mockAnimals } from '../../mocks/animalData';
import { formatCurrency } from '../../utils/formatters';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

interface Medication {
  name: string;
  dosage: string;
  frequency: string;
  duration: string;
}

interface TreatmentRecord {
  id: string;
  animalId: string;
  animalName: string;
  breed: string;
  condition: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'completed' | 'discontinued';
  medications: Medication[];
  veterinarian: string;
  cost: number;
  notes: string;
  followUpDate?: string;
}

const Treatments: React.FC = () => {
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<TreatmentRecord | null>(null);
  const [treatmentRecords, setTreatmentRecords] = useState<TreatmentRecord[]>([
    {
      id: 'TRT001',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      breed: 'Nguni',
      condition: 'Heartwater',
      startDate: '2024-03-01',
      status: 'active',
      medications: [
        {
          name: 'Oxytetracycline',
          dosage: '10mg/kg',
          frequency: 'Once daily',
          duration: '5 days'
        }
      ],
      veterinarian: 'Dr. van Niekerk',
      cost: 1500,
      notes: 'Responding well to treatment, continue monitoring',
      followUpDate: '2024-03-10'
    },
    {
      id: 'TRT002',
      animalId: 'ANM002',
      animalName: 'Tumelo',
      breed: 'Bonsmara',
      condition: 'Lumpy skin disease',
      startDate: '2024-02-15',
      status: 'active',
      medications: [
        {
          name: 'Penicillin',
          dosage: '20,000 IU/kg',
          frequency: 'Twice daily',
          duration: '7 days'
        },
        {
          name: 'Flunixin',
          dosage: '2.2mg/kg',
          frequency: 'Once daily',
          duration: '3 days'
        }
      ],
      veterinarian: 'Dr. Botha',
      cost: 2200,
      notes: 'Secondary bacterial infection being treated, skin lesions beginning to heal',
      followUpDate: '2024-03-01'
    },
    {
      id: 'TRT003',
      animalId: 'ANM004',
      animalName: 'Katlego',
      breed: 'Dorper',
      condition: 'Blue tongue',
      startDate: '2024-02-25',
      status: 'active',
      medications: [
        {
          name: 'Flunixin',
          dosage: '1.1mg/kg',
          frequency: 'Once daily',
          duration: '5 days'
        }
      ],
      veterinarian: 'Dr. Mkhize',
      cost: 1800,
      notes: 'Supportive care, monitor for respiratory distress',
      followUpDate: '2024-03-05'
    },
    {
      id: 'TRT004',
      animalId: 'ANM008',
      animalName: 'Lerato',
      breed: 'Nguni',
      condition: 'Redwater (Babesiosis)',
      startDate: '2024-01-20',
      endDate: '2024-02-05',
      status: 'completed',
      medications: [
        {
          name: 'Diminazene aceturate',
          dosage: '3.5mg/kg',
          frequency: 'Single dose',
          duration: '1 day'
        }
      ],
      veterinarian: 'Dr. van Niekerk',
      cost: 2500,
      notes: 'Blood transfusion was required, fully recovered'
    },
    {
      id: 'TRT005',
      animalId: 'ANM010',
      animalName: 'Naledi',
      breed: 'Bonsmara',
      condition: 'Foot rot',
      startDate: '2024-03-05',
      status: 'active',
      medications: [
        {
          name: 'Oxytetracycline',
          dosage: '10mg/kg',
          frequency: 'Once daily',
          duration: '5 days'
        }
      ],
      veterinarian: 'Dr. Viljoen',
      cost: 950,
      notes: 'Hoof trimming performed, daily foot bath recommended',
      followUpDate: '2024-03-12'
    }
  ]);

  const handleAddNew = () => {
    setSelectedRecord(null);
    setOpenDialog(true);
  };

  const handleEdit = (record: TreatmentRecord) => {
    setSelectedRecord(record);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setTreatmentRecords(treatmentRecords.filter(record => record.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="primary" size="small" />;
      case 'completed':
        return <Chip label="Completed" color="success" size="small" />;
      case 'discontinued':
        return <Chip label="Discontinued" color="error" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (dateString?: string) => {
    if (!dateString) return null;
    const today = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getTotalCost = () => {
    return treatmentRecords.reduce((total, record) => total + record.cost, 0);
  };

  const getActiveTreatmentsCount = () => {
    return treatmentRecords.filter(record => record.status === 'active').length;
  };

  const getCompletedTreatmentsCount = () => {
    return treatmentRecords.filter(record => record.status === 'completed').length;
  };

  return (
    <Box>
      <ModuleHeader
        title="Treatment Management"
        subtitle="Track and manage livestock treatments, medications, and health interventions"
        module="health"
        submodule="treatments"
        actionLabel="Add Treatment"
        actionIcon={<Add />}
        onAction={handleAddNew}
      />

      <Box sx={{ px: 3, pb: 5 }}>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Treatment Status"
              module="health"
              uniqueId="treatment-status"
              icon={<Healing />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {getActiveTreatmentsCount()}
                      </Typography>
                      <Typography variant="body2">Active</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {getCompletedTreatmentsCount()}
                      </Typography>
                      <Typography variant="body2">Completed</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Total Treatment Cost
                  </Typography>
                  <Box textAlign="center" p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                    <Typography variant="h4" fontWeight="bold" color="secondary.main">
                      {formatCurrency(getTotalCost())}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Year to date
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Upcoming Follow-ups"
              module="health"
              uniqueId="treatment-followups"
              icon={<CalendarToday />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2}>
                {treatmentRecords
                  .filter(record => record.followUpDate && record.status === 'active')
                  .sort((a, b) => new Date(a.followUpDate!).getTime() - new Date(b.followUpDate!).getTime())
                  .slice(0, 3)
                  .map(record => (
                    <Box key={record.id} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {record.animalName} - {record.condition}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          <Event fontSize="small" sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                          Follow-up: {formatDate(record.followUpDate)}
                        </Typography>
                        {record.followUpDate && (
                          <Chip
                            label={`${calculateDaysRemaining(record.followUpDate)} days`}
                            size="small"
                            color={calculateDaysRemaining(record.followUpDate)! < 3 ? "warning" : "default"}
                          />
                        )}
                      </Box>
                    </Box>
                  ))}
                {treatmentRecords.filter(record => record.followUpDate && record.status === 'active').length === 0 && (
                  <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                    <Typography variant="body2" color="text.secondary">No upcoming follow-ups</Typography>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Active Treatments"
              module="health"
              uniqueId="treatment-active"
              icon={<LocalHospital />}
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2} sx={{ maxHeight: 300, overflow: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Animal</TableCell>
                      <TableCell>Condition</TableCell>
                      <TableCell>Medications</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {treatmentRecords
                      .filter(record => record.status === 'active')
                      .map(record => (
                        <TableRow key={record.id} hover>
                          <TableCell>{record.animalName}</TableCell>
                          <TableCell>{record.condition}</TableCell>
                          <TableCell>
                            {record.medications.map((med, index) => (
                              <Tooltip
                                key={index}
                                title={`${med.dosage}, ${med.frequency}, ${med.duration}`}
                              >
                                <Chip
                                  label={med.name}
                                  size="small"
                                  color="primary"
                                  sx={{ mr: 0.5, mb: 0.5 }}
                                />
                              </Tooltip>
                            ))}
                          </TableCell>
                        </TableRow>
                      ))}
                    {treatmentRecords.filter(record => record.status === 'active').length === 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="center">No active treatments</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent sx={{ p: 0 }}>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                  <TableCell sx={{ color: 'white' }}>Animal</TableCell>
                  <TableCell sx={{ color: 'white' }}>Breed</TableCell>
                  <TableCell sx={{ color: 'white' }}>Condition</TableCell>
                  <TableCell sx={{ color: 'white' }}>Start Date</TableCell>
                  <TableCell sx={{ color: 'white' }}>End Date</TableCell>
                  <TableCell sx={{ color: 'white' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white' }}>Medications</TableCell>
                  <TableCell sx={{ color: 'white' }}>Cost</TableCell>
                  <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {treatmentRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>{record.animalName}</TableCell>
                    <TableCell>{record.breed}</TableCell>
                    <TableCell>{record.condition}</TableCell>
                    <TableCell>{formatDate(record.startDate)}</TableCell>
                    <TableCell>{formatDate(record.endDate)}</TableCell>
                    <TableCell>{getStatusChip(record.status)}</TableCell>
                    <TableCell>
                      {record.medications.map((med, index) => (
                        <Tooltip
                          key={index}
                          title={`${med.dosage}, ${med.frequency}, ${med.duration}`}
                        >
                          <Chip
                            label={med.name}
                            size="small"
                            color="primary"
                            sx={{ mr: 0.5, mb: 0.5 }}
                          />
                        </Tooltip>
                      ))}
                    </TableCell>
                    <TableCell>{formatCurrency(record.cost)}</TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(record)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(record.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          </CardContent>
        </Card>
      </Box>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedRecord ? 'Edit Treatment Record' : 'Add New Treatment Record'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="animal-select-label">Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  label="Animal"
                  defaultValue={selectedRecord?.animalId || ''}
                >
                  {mockAnimals.map(animal => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} ({animal.breed})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="condition"
                label="Condition/Disease"
                defaultValue={selectedRecord?.condition || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="startDate"
                label="Start Date"
                type="date"
                defaultValue={selectedRecord?.startDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="endDate"
                label="End Date"
                type="date"
                defaultValue={selectedRecord?.endDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedRecord?.status || 'active'}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="discontinued">Discontinued</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="veterinarian"
                label="Veterinarian"
                defaultValue={selectedRecord?.veterinarian || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="cost"
                label="Cost (ZAR)"
                type="number"
                defaultValue={selectedRecord?.cost || 0}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="followUpDate"
                label="Follow-up Date"
                type="date"
                defaultValue={selectedRecord?.followUpDate || ''}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom mt={2}>
                Medications
              </Typography>
              {/* Medication details would go here - simplified for this example */}
              <Box p={2} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Medication Name"
                      defaultValue={selectedRecord?.medications[0]?.name || ''}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Dosage"
                      defaultValue={selectedRecord?.medications[0]?.dosage || ''}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Frequency"
                      defaultValue={selectedRecord?.medications[0]?.frequency || ''}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Duration"
                      defaultValue={selectedRecord?.medications[0]?.duration || ''}
                    />
                  </Grid>
                </Grid>
                <CustomButton
                  variant="outlined"
                  startIcon={<Add />}
                  sx={{ mt: 2 }}
                >
                  Add Another Medication
                </CustomButton>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedRecord?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedRecord ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Treatments;
