/**
 * Start Server Script
 * 
 * This script sets the environment variables directly and then starts the server.
 */

const dotenv = require('dotenv');
const path = require('path');

dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Commenting out hardcoded environment variable overrides
// process.env.MONGODB_URI = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
// process.env.MONGODB_DB_NAME = 'ampd_livestock';
// process.env.PORT = '3002';

// Log the environment variables
console.log('Environment variables set:');
console.log('MONGODB_URI:', process.env.MONGODB_URI.replace(/:[^:]*@/, ':****@'));
console.log('MONGODB_DB_NAME:', process.env.MONGODB_DB_NAME);
console.log('PORT:', process.env.PORT);

// Start the server
require('./server');
