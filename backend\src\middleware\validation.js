const Joi = require('joi');
const logger = require('../utils/logger');

const schemas = {
  animal: Joi.object({
    tagId: Joi.string().required(),
    species: Joi.string().required(),
    breed: Joi.string().required(),
    birthDate: Joi.date().iso().required(),
    gender: Joi.string().valid('male', 'female').required(),
    weight: Joi.number().positive(),
    status: Joi.string().valid('active', 'sold', 'deceased').required(),
    parentIds: Joi.object({
      sire: Joi.string(),
      dam: Joi.string()
    }),
    purchaseInfo: Joi.object({
      date: Joi.date().iso(),
      price: Joi.number().positive(),
      supplier: Joi.string()
    })
  }),

  healthRecord: Joi.object({
    animalId: Joi.string().required(),
    type: Joi.string().valid('checkup', 'treatment', 'vaccination').required(),
    date: Joi.date().iso().required(),
    diagnosis: Joi.string().when('type', {
      is: 'treatment',
      then: Joi.required()
    }),
    treatment: Joi.string().when('type', {
      is: 'treatment',
      then: Joi.required()
    }),
    vaccineInfo: Joi.object({
      name: Joi.string(),
      batch: Joi.string(),
      expiryDate: Joi.date().iso()
    }).when('type', {
      is: 'vaccination',
      then: Joi.required()
    }),
    notes: Joi.string(),
    veterinarian: Joi.string().required()
  }),

  feedingLog: Joi.object({
    animalId: Joi.string().required(),
    feedType: Joi.string().required(),
    quantity: Joi.number().positive().required(),
    unit: Joi.string().required(),
    timestamp: Joi.date().iso().required(),
    feeder: Joi.string().required(),
    notes: Joi.string()
  })
};

const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      logger.warn('Validation error:', error.details);
      return res.status(400).json({
        error: 'Validation error',
        details: error.details.map(detail => ({
          field: detail.context.key,
          message: detail.message
        }))
      });
    }
    next();
  };
};

module.exports = {
  schemas,
  validateRequest
};