/**
 * Business Analytics Types
 * 
 * Type definitions for the Business Analysis module.
 */

/**
 * Business KPI
 */
export interface BusinessKPI {
  id: string;
  name: string;
  value: number;
  target: number;
  unit: string;
  trend: number;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  status: 'good' | 'warning' | 'critical';
  description?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Business Prediction
 */
export interface BusinessPrediction {
  id: string;
  name: string;
  currentValue: number;
  predictedValue: number;
  confidence: number;
  timeframe: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
  description?: string;
  methodology?: string;
  factors?: string[];
  recommendations?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Business Insight
 */
export interface BusinessInsight {
  id: string;
  name: string;
  description: string;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding';
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendations?: string[];
  source: 'ai' | 'analytics' | 'manual';
  createdAt: string;
  updatedAt: string;
}

/**
 * Business Risk
 */
export interface BusinessRisk {
  id: string;
  name: string;
  description: string;
  likelihood: number;
  impact: number;
  severity: number;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding' | 'market' | 'regulatory';
  mitigationStrategy?: string;
  status: 'identified' | 'assessed' | 'mitigated' | 'monitored';
  createdAt: string;
  updatedAt: string;
}

/**
 * Business Opportunity
 */
export interface BusinessOpportunity {
  id: string;
  name: string;
  description: string;
  potentialValue: number;
  feasibility: number;
  opportunityScore: number;
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding' | 'market' | 'innovation';
  implementationPlan?: string;
  status: 'identified' | 'evaluated' | 'planned' | 'implemented';
  createdAt: string;
  updatedAt: string;
}

/**
 * Strategic Goal
 */
export interface StrategicGoal {
  id: string;
  name: string;
  description: string;
  targetDate: string;
  progress: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed';
  kpis?: string[];
  category: 'financial' | 'operational' | 'animals' | 'health' | 'breeding' | 'feeding' | 'market' | 'innovation';
  createdAt: string;
  updatedAt: string;
}

/**
 * Strategic Initiative
 */
export interface StrategicInitiative {
  id: string;
  name: string;
  description: string;
  goalId: string;
  startDate: string;
  endDate: string;
  progress: number;
  status: 'not_started' | 'in_progress' | 'completed' | 'delayed';
  owner: string;
  budget?: number;
  actualCost?: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * SWOT Analysis
 */
export interface SWOTAnalysis {
  id: string;
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * Strategic Scenario
 */
export interface StrategicScenario {
  id: string;
  name: string;
  description: string;
  probability: number;
  impact: number;
  financialImpact: number;
  responseStrategy: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Business Analytics Dashboard Data
 */
export interface BusinessAnalyticsDashboardData {
  kpis: BusinessKPI[];
  predictions: BusinessPrediction[];
  insights: BusinessInsight[];
  risks: BusinessRisk[];
  opportunities: BusinessOpportunity[];
  animalDistributionData: { name: string; value: number }[];
}

/**
 * Business Strategy Data
 */
export interface BusinessStrategyData {
  vision: string;
  mission: string;
  goals: StrategicGoal[];
  initiatives: StrategicInitiative[];
  swot: SWOTAnalysis;
  opportunities: BusinessOpportunity[];
  scenarios: StrategicScenario[];
}
