import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, useTheme, alpha, IconButton, Divider, MenuItem, Select, FormControl, InputLabel, LinearProgress, Tabs, Tab } from '@mui/material';
import {
  Healing,
  LocalHospital,
  Vaccines,
  MedicalServices,
  Download,
  Print,
  Share,
  FilterList,
  BarChart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon
} from '../../utils/iconImports';
import { 
  ModuleHeader,
  AnimatedBackgroundCard,
  SubModuleDashboard,
  SubModuleMetricsCard,
  SubModuleActionPanel,
  SubModuleDataTable,
  SubModuleChart,
  SubModuleReport
, CustomButton } from '../../components/common';
import { useAlert } from '../../hooks/useAlert';
import { generateReport, ReportParams, getSavedReports } from '../../services/reportService';

// Mock data for health reports
const healthMetrics = [
  {
    metric: 'Vaccination Rate',
    value: '94%',
    status: 'good',
    change: '+2%',
    period: 'vs last quarter'
  },
  {
    metric: 'Active Health Issues',
    value: '12',
    status: 'warning',
    change: '+3',
    period: 'vs last month'
  },
  {
    metric: 'Treatment Success',
    value: '87%',
    status: 'good',
    change: '+5%',
    period: 'vs last quarter'
  },
  {
    metric: 'Mortality Rate',
    value: '1.2%',
    status: 'good',
    change: '-0.3%',
    period: 'vs last quarter'
  }
];

const recentHealthEvents = [
  {
    id: 'HE001',
    date: '2024-04-15',
    animalId: 'CTL042',
    animalName: 'Bella',
    eventType: 'Vaccination',
    description: 'Routine vaccination - Clostridial disease',
    performedBy: 'Dr. Smith',
    status: 'Completed'
  },
  {
    id: 'HE002',
    date: '2024-04-14',
    animalId: 'CTL038',
    animalName: 'Max',
    eventType: 'Treatment',
    description: 'Antibiotic treatment for respiratory infection',
    performedBy: 'Dr. Johnson',
    status: 'In Progress'
  },
  {
    id: 'HE003',
    date: '2024-04-12',
    animalId: 'CTL056',
    animalName: 'Daisy',
    eventType: 'Examination',
    description: 'Routine health check and hoof trimming',
    performedBy: 'Dr. Williams',
    status: 'Completed'
  },
  {
    id: 'HE004',
    date: '2024-04-10',
    animalId: 'CTL029',
    animalName: 'Rocky',
    eventType: 'Treatment',
    description: 'Wound treatment and dressing',
    performedBy: 'Dr. Smith',
    status: 'Completed'
  },
  {
    id: 'HE005',
    date: '2024-04-08',
    animalId: 'CTL045',
    animalName: 'Luna',
    eventType: 'Vaccination',
    description: 'Brucellosis vaccination',
    performedBy: 'Dr. Johnson',
    status: 'Completed'
  }
];

const diseasePrevalence = [
  { disease: 'Respiratory Infections', count: 15, percentage: 32 },
  { disease: 'Mastitis', count: 8, percentage: 17 },
  { disease: 'Foot Rot', count: 6, percentage: 13 },
  { disease: 'Parasitic Infections', count: 12, percentage: 26 },
  { disease: 'Digestive Issues', count: 5, percentage: 11 }
];

const reportTypes = [
  {
    name: 'Vaccination Status Report',
    description: 'Complete overview of vaccination status for all animals',
    lastGenerated: '2024-04-02',
    icon: <Vaccines />
  },
  {
    name: 'Disease Prevalence Analysis',
    description: 'Statistical breakdown of health issues by type and frequency',
    lastGenerated: '2024-04-01',
    icon: <PieChartIcon />
  },
  {
    name: 'Treatment Effectiveness',
    description: 'Analysis of treatment outcomes and success rates',
    lastGenerated: '2024-03-28',
    icon: <BarChartIcon />
  },
  {
    name: 'Veterinary Visit Log',
    description: 'Comprehensive record of all veterinary visits and procedures',
    lastGenerated: '2024-03-25',
    icon: <MedicalServices />
  }
];

const HealthReports: React.FC = () => {
  const theme = useTheme();
  const { showAlert } = useAlert();
  const [reportPeriod, setReportPeriod] = useState('quarter');
  const [reportFormat, setReportFormat] = useState('pdf');
  const [activeTab, setActiveTab] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [savedReports, setSavedReports] = useState<any[]>([]);

  useEffect(() => {
    // Load saved reports when component mounts
    const loadSavedReports = async () => {
      try {
        const reports = await getSavedReports('health');
        setSavedReports(reports);
      } catch (error) {
        console.error('Error loading saved reports:', error);
      }
    };

    loadSavedReports();
  }, []);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const handleGenerateReport = async (reportName: string) => {
    try {
      setIsGenerating(true);

      showAlert({
        message: `Generating ${reportName} in ${reportFormat.toUpperCase()} format...`,
        severity: 'info'
      });

      // Mock health records for demonstration
      const mockHealthRecords = await import('../../mocks/healthData').then(module => module.mockHealthRecords);
      const mockAnimals = await import('../../mocks/animalData').then(module => module.mockAnimals);

      const params: ReportParams = {
        type: 'health',
        format: reportFormat as any,
        timePeriod: reportPeriod as any,
        filters: {
          includeHealthRecords: true,
          includeAnimals: true
        }
      };

      await generateReport(params);

      // Refresh the saved reports list
      const reports = await getSavedReports('health');
      setSavedReports(reports);

      showAlert({
        message: `${reportName} has been generated successfully!`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Error generating report:', error);
      showAlert({
        message: 'Failed to generate report. Please try again.',
        severity: 'error'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return theme.palette.success.main;
      case 'in progress':
        return theme.palette.warning.main;
      case 'scheduled':
        return theme.palette.info.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getMetricColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'good':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'critical':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Livestock Health Analytics"
        subtitle="Comprehensive health monitoring, disease tracking, and treatment analysis"
        module="reports"
        submodule="health"
        actionLabel={isGenerating ? "Generating..." : "Export Health Report"}
        actionIcon={<Download />}
        onAction={() => handleGenerateReport('Comprehensive Health Report')}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Health Intelligence Dashboard */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Paper
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  mr: 2
                }}
              >
                <LocalHospital />
              </Box>
              <Typography variant="h5" fontWeight="bold">
                Health Intelligence Dashboard
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="error.main">
                    Health Alert
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>27% increase in respiratory issues</strong> detected in the northern paddock. Recommend veterinary inspection within the next 7 days.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="error" sx={{ mt: 1 }}>
                    Schedule Inspection
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="success.main">
                    Vaccination Due
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>15 animals due for vaccination</strong> in the next 14 days. Schedule batch vaccination to optimize veterinary costs.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="success" sx={{ mt: 1 }}>
                    View Vaccination Schedule
                  </CustomButton>
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="primary.main">
                    Treatment Effectiveness
                  </Typography>
                  <Typography variant="body2" paragraph>
                    <strong>92% success rate</strong> for antibiotic treatment protocol implemented last month. Continue current treatment regimen.
                  </Typography>
                  <CustomButton variant="outlined" size="small" color="primary" sx={{ mt: 1 }}>
                    View Treatment Analysis
                  </CustomButton>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Key metrics */}
          <Grid container spacing={3} mb={4}>
            {healthMetrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={metric.metric}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={metric.metric}
                    subtitle={metric.value}
                    icon={<Healing />}
                    accentColor={getMetricColor(metric.status)}
                    secondaryColor={theme.palette.mode === 'dark'
                      ? alpha(getMetricColor(metric.status), 0.7)
                      : alpha(getMetricColor(metric.status), 0.8)}
                    delay={index * 0.1}
                    height={160}
                    overlay="gradient"
                    content={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" fontWeight="medium" color={metric.change.startsWith('+') && metric.metric !== 'Active Health Issues' ? 'success.main' :
                                                                              metric.change.startsWith('-') && metric.metric === 'Mortality Rate' ? 'success.main' : 'error.main'}>
                          {metric.change} {metric.period}
                        </Typography>
                      </Box>
                    }
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Report Tabs */}
          <Card
            sx={{
              mb: 4,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={activeTab}
                onChange={handleTabChange}
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab label="Health Events" icon={<LocalHospital />} iconPosition="start" />
                <Tab label="Disease Prevalence" icon={<PieChartIcon />} iconPosition="start" />
                <Tab label="Report Generation" icon={<MedicalServices />} iconPosition="start" />
              </Tabs>
            </Box>

            {/* Health Events Tab */}
            {activeTab === 0 && (
              <TableContainer component={Paper} elevation={0}>
                <Table>
                  <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                    <TableRow>
                      <TableCell>Event ID</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Animal</TableCell>
                      <TableCell>Event Type</TableCell>
                      <TableCell>Description</TableCell>
                      <TableCell>Performed By</TableCell>
                      <TableCell>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {recentHealthEvents.map((event) => (
                      <TableRow key={event.id} hover>
                        <TableCell>{event.id}</TableCell>
                        <TableCell>{event.date}</TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2">{event.animalName}</Typography>
                            <Typography variant="caption" color="text.secondary">{event.animalId}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={event.eventType}
                            size="small"
                            sx={{
                              bgcolor: event.eventType === 'Vaccination'
                                ? alpha(theme.palette.success.main, 0.1)
                                : event.eventType === 'Treatment'
                                ? alpha(theme.palette.warning.main, 0.1)
                                : alpha(theme.palette.info.main, 0.1),
                              color: event.eventType === 'Vaccination'
                                ? theme.palette.success.main
                                : event.eventType === 'Treatment'
                                ? theme.palette.warning.main
                                : theme.palette.info.main
                            }}
                          />
                        </TableCell>
                        <TableCell>{event.description}</TableCell>
                        <TableCell>{event.performedBy}</TableCell>
                        <TableCell>
                          <Chip
                            label={event.status}
                            size="small"
                            sx={{
                              bgcolor: alpha(getStatusColor(event.status), 0.1),
                              color: getStatusColor(event.status)
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Disease Prevalence Tab */}
            {activeTab === 1 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Disease Prevalence Analysis
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Breakdown of health issues by type for the current quarter
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TableContainer component={Paper} elevation={0} sx={{ border: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                      <Table>
                        <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                          <TableRow>
                            <TableCell>Disease/Condition</TableCell>
                            <TableCell align="right">Count</TableCell>
                            <TableCell align="right">Percentage</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {diseasePrevalence.map((item) => (
                            <TableRow key={item.disease} hover>
                              <TableCell>{item.disease}</TableCell>
                              <TableCell align="right">{item.count}</TableCell>
                              <TableCell align="right">{item.percentage}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card sx={{ height: '100%', boxShadow: 'none', border: `1px solid ${alpha(theme.palette.divider, 0.1)}` }}>
                      <CardContent>
                        <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                          Disease Distribution
                        </Typography>

                        {diseasePrevalence.map((item) => (
                          <Box key={item.disease} sx={{ mb: 2 }}>
                            <Box display="flex" justifyContent="space-between" alignItems="center">
                              <Typography variant="body2">{item.disease}</Typography>
                              <Typography variant="body2" fontWeight="medium">{item.percentage}%</Typography>
                            </Box>
                            <LinearProgress
                              variant="determinate"
                              value={item.percentage}
                              sx={{
                                height: 8,
                                borderRadius: 4,
                                mt: 0.5,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                '& .MuiLinearProgress-bar': {
                                  bgcolor: item.percentage > 25
                                    ? theme.palette.warning.main
                                    : theme.palette.info.main
                                }
                              }}
                            />
                          </Box>
                        ))}

                        <Box mt={3}>
                          <CustomButton
                            variant="outlined"
                            fullWidth
                            startIcon={<Download />}
                            onClick={() => handleGenerateReport('Disease Prevalence Report')}
                          >
                            Download Full Analysis
                          </CustomButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}

            {/* Report Generation Tab */}
            {activeTab === 2 && (
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Health Report Generation
                </Typography>
                <Typography variant="body2" color="text.secondary" paragraph>
                  Generate comprehensive health reports for your livestock
                </Typography>

                <Grid container spacing={3} mb={3}>
                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel id="period-label">Report Period</InputLabel>
                      <Select
                        labelId="period-label"
                        value={reportPeriod}
                        label="Report Period"
                        onChange={(e) => setReportPeriod(e.target.value)}
                      >
                        <MenuItem value="month">Last Month</MenuItem>
                        <MenuItem value="quarter">Last Quarter</MenuItem>
                        <MenuItem value="year">Last Year</MenuItem>
                        <MenuItem value="all">All Time</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth size="small">
                      <InputLabel id="format-label">Report Format</InputLabel>
                      <Select
                        labelId="format-label"
                        value={reportFormat}
                        label="Report Format"
                        onChange={(e) => setReportFormat(e.target.value)}
                      >
                        <MenuItem value="pdf">PDF Document</MenuItem>
                        <MenuItem value="xlsx">Excel Spreadsheet</MenuItem>
                        <MenuItem value="csv">CSV File</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <CustomButton
                      variant="contained"
                      fullWidth
                      startIcon={<FilterList />}
                    >
                      Additional Filters
                    </CustomButton>
                  </Grid>
                </Grid>

                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  {reportTypes.map((report) => (
                    <Grid item xs={12} sm={6} key={report.name}>
                      <Card sx={{ boxShadow: '0 2px 8px rgba(0,0,0,0.05)', height: '100%' }}>
                        <CardContent>
                          <Box display="flex" alignItems="center" mb={2}>
                            <Box
                              sx={{
                                p: 1.5,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                color: theme.palette.primary.main,
                                mr: 2
                              }}
                            >
                              {report.icon}
                            </Box>
                            <Box>
                              <Typography variant="h6" fontWeight="bold">
                                {report.name}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Last generated: {report.lastGenerated}
                              </Typography>
                            </Box>
                          </Box>

                          <Typography variant="body2" paragraph>
                            {report.description}
                          </Typography>

                          <Box display="flex" justifyContent="flex-end" gap={1}>
                            <IconButton size="small" color="primary">
                              <Print />
                            </IconButton>
                            <IconButton size="small" color="primary">
                              <Download />
                            </IconButton>
                            <IconButton size="small" color="primary">
                              <Share />
                            </IconButton>
                            <CustomButton
                              variant="outlined"
                              size="small"
                              onClick={() => handleGenerateReport(report.name)}
                            >
                              Generate
                            </CustomButton>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            )}
          </Card>

          {/* Standardized Health Report */}
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 3 }}>
            Standardized Health Reports
          </Typography>

          <SubModuleReport
            title="Comprehensive Health Report"
            subtitle="Generate detailed health reports with customizable filters and export options"
            moduleColor={theme.palette.primary.main}
            filters={[
              {
                id: 'dateRange',
                label: 'Date Range',
                type: 'dateRange',
                value: {
                  from: new Date(new Date().setMonth(new Date().getMonth() - 3)).toISOString().split('T')[0],
                  to: new Date().toISOString().split('T')[0]
                }
              },
              {
                id: 'eventType',
                label: 'Event Type',
                type: 'select',
                options: [
                  { value: 'all', label: 'All Events' },
                  { value: 'vaccination', label: 'Vaccinations' },
                  { value: 'treatment', label: 'Treatments' },
                  { value: 'examination', label: 'Examinations' },
                  { value: 'surgery', label: 'Surgeries' }
                ],
                value: 'all'
              },
              {
                id: 'animalGroup',
                label: 'Animal Group',
                type: 'select',
                options: [
                  { value: 'all', label: 'All Animals' },
                  { value: 'cattle', label: 'Cattle' },
                  { value: 'sheep', label: 'Sheep' },
                  { value: 'goats', label: 'Goats' }
                ],
                value: 'all'
              }
            ]}
            sections={[
              {
                title: 'Health Summary',
                type: 'summary',
                data: [
                  { label: 'Total Health Events', value: '156', change: 12, changePeriod: 'vs. previous quarter' },
                  { label: 'Vaccination Rate', value: '94%', change: 2, changePeriod: 'vs. previous quarter' },
                  { label: 'Treatment Success', value: '87%', change: 5, changePeriod: 'vs. previous quarter' },
                  { label: 'Mortality Rate', value: '1.2%', change: -0.3, changePeriod: 'vs. previous quarter' }
                ]
              },
              {
                title: 'Recent Health Events',
                type: 'table',
                columns: [
                  { id: 'id', label: 'Event ID' },
                  { id: 'date', label: 'Date' },
                  { id: 'animalName', label: 'Animal' },
                  {
                    id: 'eventType',
                    label: 'Event Type',
                    format: (value) => (
                      <Chip
                        label={value}
                        size="small"
                        sx={{
                          bgcolor: value === 'Vaccination'
                            ? alpha(theme.palette.success.main, 0.1)
                            : value === 'Treatment'
                            ? alpha(theme.palette.warning.main, 0.1)
                            : alpha(theme.palette.info.main, 0.1),
                          color: value === 'Vaccination'
                            ? theme.palette.success.main
                            : value === 'Treatment'
                            ? theme.palette.warning.main
                            : theme.palette.info.main
                        }}
                      />
                    )
                  },
                  { id: 'description', label: 'Description' },
                  { id: 'performedBy', label: 'Performed By' },
                  {
                    id: 'status',
                    label: 'Status',
                    format: (value) => (
                      <Chip
                        label={value}
                        size="small"
                        sx={{
                          bgcolor: alpha(getStatusColor(value), 0.1),
                          color: getStatusColor(value)
                        }}
                      />
                    )
                  }
                ],
                data: recentHealthEvents
              },
              {
                title: 'Disease Prevalence',
                type: 'table',
                columns: [
                  { id: 'disease', label: 'Disease/Condition' },
                  { id: 'count', label: 'Count' },
                  { id: 'percentage', label: 'Percentage (%)' }
                ],
                data: diseasePrevalence
              },
              {
                title: 'Health Trends',
                type: 'chart',
                data: {
                  type: 'line',
                  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                  datasets: [
                    {
                      label: 'Vaccinations',
                      data: [12, 15, 10, 8, 14, 16]
                    },
                    {
                      label: 'Treatments',
                      data: [8, 10, 12, 15, 9, 7]
                    }
                  ]
                }
              },
              {
                title: 'Report Details',
                type: 'details',
                data: {
                  'Report Generated': new Date().toLocaleString(),
                  'Generated By': 'System Administrator',
                  'Report Period': 'Last Quarter',
                  'Total Animals': '156',
                  'Healthy Animals': '142 (91%)',
                  'Animals Under Treatment': '14 (9%)',
                  'Notes': 'This report includes all health events recorded in the system for the selected period.'
                }
              }
            ]}
            isLoading={isGenerating}
            onGenerateReport={(filters) => {
              console.log('Generating report with filters:', filters);
              handleGenerateReport('Comprehensive Health Report');
            }}
            onExport={(format) => {
              console.log(`Exporting report as ${format}`);
              setReportFormat(format === 'excel' ? 'xlsx' : format);
              handleGenerateReport('Comprehensive Health Report');
            }}
            onPrint={() => {
              console.log('Printing report');
              window.print();
            }}
            onShare={() => {
              console.log('Sharing report');
              showAlert({
                message: 'Report sharing functionality will be available soon.',
                severity: 'info'
              });
            }}
          />
        </motion.div>
      </Box>
    </Box>
  );
};

export default HealthReports;
