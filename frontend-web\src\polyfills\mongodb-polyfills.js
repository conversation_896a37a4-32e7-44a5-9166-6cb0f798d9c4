/**
 * Comprehensive polyfills for MongoDB Node.js core modules
 * This file provides browser-compatible implementations of Node.js modules
 * that MongoDB depends on.
 */

// Mock implementation of timers/promises
const timersPromises = {
  setTimeout: (delay, value) => {
    return new Promise((resolve) => {
      window.setTimeout(() => resolve(value), delay);
    });
  },
  setImmediate: (value) => {
    return new Promise((resolve) => {
      window.setTimeout(() => resolve(value), 0);
    });
  }
};

// Mock implementation of crypto (minimal)
const crypto = {
  randomBytes: (size) => {
    const array = new Uint8Array(size);
    if (window.crypto && window.crypto.getRandomValues) {
      window.crypto.getRandomValues(array);
    } else {
      // Fallback for older browsers
      for (let i = 0; i < size; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return {
      toString: (encoding) => {
        if (encoding === 'hex') {
          return Array.from(array)
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
        }
        return Array.from(array).join(',');
      }
    };
  },
  createHash: () => {
    return {
      update: () => {
        return {
          digest: () => 'mock-hash-value'
        };
      }
    };
  }
};

// Mock implementation of fs (minimal)
const fs = {
  readFileSync: () => {
    throw new Error('File system operations are not supported in the browser');
  },
  existsSync: () => false,
  promises: {
    readFile: () => Promise.reject(new Error('File system operations are not supported in the browser'))
  }
};

// Export all polyfills
module.exports = {
  timersPromises,
  crypto,
  fs
};
