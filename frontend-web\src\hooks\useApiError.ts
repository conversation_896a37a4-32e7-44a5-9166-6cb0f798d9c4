/**
 * API Error Hook
 * 
 * This hook provides functions for handling API errors.
 */

import { useState, useCallback } from 'react';
import { AxiosError } from 'axios';

interface ApiErrorState {
  error: string | null;
  errors: Record<string, string> | null;
  isError: boolean;
}

interface UseApiErrorReturn extends ApiErrorState {
  handleError: (error: unknown) => void;
  clearError: () => void;
  setError: (message: string) => void;
  setFieldError: (field: string, message: string) => void;
}

/**
 * Hook for handling API errors
 * @returns API error state and functions
 */
const useApiError = (): UseApiErrorReturn => {
  const [state, setState] = useState<ApiErrorState>({
    error: null,
    errors: null,
    isError: false
  });
  
  /**
   * Handle API error
   * @param error - Error object
   */
  const handleError = useCallback((error: unknown) => {
    if (error instanceof Error) {
      // Handle Axios error
      if ((error as AxiosError).isAxiosError) {
        const axiosError = error as AxiosError;
        
        // Handle validation errors
        if (axiosError.response?.status === 400 && axiosError.response?.data?.errors) {
          setState({
            error: axiosError.response.data.message || 'Validation error',
            errors: axiosError.response.data.errors,
            isError: true
          });
          return;
        }
        
        // Handle other API errors
        setState({
          error: axiosError.response?.data?.message || axiosError.message || 'An error occurred',
          errors: null,
          isError: true
        });
        return;
      }
      
      // Handle regular Error
      setState({
        error: error.message || 'An error occurred',
        errors: null,
        isError: true
      });
      return;
    }
    
    // Handle unknown error
    setState({
      error: 'An unknown error occurred',
      errors: null,
      isError: true
    });
  }, []);
  
  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState({
      error: null,
      errors: null,
      isError: false
    });
  }, []);
  
  /**
   * Set error message
   * @param message - Error message
   */
  const setError = useCallback((message: string) => {
    setState({
      error: message,
      errors: null,
      isError: true
    });
  }, []);
  
  /**
   * Set field error
   * @param field - Field name
   * @param message - Error message
   */
  const setFieldError = useCallback((field: string, message: string) => {
    setState(prevState => ({
      error: prevState.error,
      errors: {
        ...(prevState.errors || {}),
        [field]: message
      },
      isError: true
    }));
  }, []);
  
  return {
    ...state,
    handleError,
    clearError,
    setError,
    setFieldError
  };
};

export default useApiError;
