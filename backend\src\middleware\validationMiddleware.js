/**
 * Validation Middleware
 * 
 * This middleware provides request validation using Joi.
 */

const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Validate request body against a schema
 * @param {Object} schema - Joi schema
 * @returns {Function} - Express middleware
 */
function validateBody(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      // Format validation errors
      const errors = error.details.reduce((acc, detail) => {
        const key = detail.path.join('.');
        acc[key] = detail.message;
        return acc;
      }, {});
      
      // Log validation errors
      logger.warn(`Validation error for ${req.method} ${req.originalUrl}:`, { 
        errors,
        body: req.body,
        user: req.user ? `${req.user.username} (${req.user.role})` : 'unauthenticated'
      });
      
      // Return validation error response
      return res.status(400).json({
        success: false,
        message: 'Validation Error',
        errors
      });
    }
    
    // Replace request body with validated value
    req.body = value;
    next();
  };
}

/**
 * Validate request query parameters against a schema
 * @param {Object} schema - Joi schema
 * @returns {Function} - Express middleware
 */
function validateQuery(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false });
    
    if (error) {
      // Format validation errors
      const errors = error.details.reduce((acc, detail) => {
        const key = detail.path.join('.');
        acc[key] = detail.message;
        return acc;
      }, {});
      
      // Log validation errors
      logger.warn(`Query validation error for ${req.method} ${req.originalUrl}:`, { 
        errors,
        query: req.query,
        user: req.user ? `${req.user.username} (${req.user.role})` : 'unauthenticated'
      });
      
      // Return validation error response
      return res.status(400).json({
        success: false,
        message: 'Query Validation Error',
        errors
      });
    }
    
    // Replace request query with validated value
    req.query = value;
    next();
  };
}

/**
 * Validate request parameters against a schema
 * @param {Object} schema - Joi schema
 * @returns {Function} - Express middleware
 */
function validateParams(schema) {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.params, { abortEarly: false });
    
    if (error) {
      // Format validation errors
      const errors = error.details.reduce((acc, detail) => {
        const key = detail.path.join('.');
        acc[key] = detail.message;
        return acc;
      }, {});
      
      // Log validation errors
      logger.warn(`Parameter validation error for ${req.method} ${req.originalUrl}:`, { 
        errors,
        params: req.params,
        user: req.user ? `${req.user.username} (${req.user.role})` : 'unauthenticated'
      });
      
      // Return validation error response
      return res.status(400).json({
        success: false,
        message: 'Parameter Validation Error',
        errors
      });
    }
    
    // Replace request parameters with validated value
    req.params = value;
    next();
  };
}

// Common validation schemas
const schemas = {
  // ID validation
  id: Joi.string().trim().required().messages({
    'string.empty': 'ID cannot be empty',
    'any.required': 'ID is required'
  }),
  
  // Pagination validation
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).default(20).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
    sort: Joi.string().trim().default('createdAt').messages({
      'string.base': 'Sort must be a string'
    }),
    order: Joi.string().valid('asc', 'desc').default('desc').messages({
      'string.base': 'Order must be a string',
      'any.only': 'Order must be either "asc" or "desc"'
    })
  })
};

module.exports = {
  validateBody,
  validateQuery,
  validateParams,
  schemas
};
