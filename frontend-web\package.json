{"name": "ampd-livestock-frontend", "version": "0.1.0", "private": true, "dependencies": {"@craco/craco": "^7.1.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.15.0", "@mui/x-data-grid": "^6.18.5", "@mui/x-date-pickers": "^6.20.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.0", "@types/node": "^18.15.0", "@types/papaparse": "^5.3.15", "@types/react": "^18.2.0", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18.2.0", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/recharts": "^1.8.29", "autoprefixer": "^10.4.14", "axios": "^1.9.0", "chart.js": "^4.4.8", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "framer-motion": "^10.12.0", "http-proxy-middleware": "^3.0.5", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "notistack": "^3.0.2", "papaparse": "^5.5.2", "postcss": "^8.4.31", "react": "^18.2.0", "react-app-rewired": "^2.2.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.2", "react-overlays": "^5.2.1", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "tailwindcss": "^3.3.3", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "overrides": {"postcss": "^8.4.31", "nth-check": "^2.1.1", "semver": "^7.5.3", "webpack-dev-middleware": "^5.3.4", "@svgr/webpack": "^8.0.1", "json5": "^2.2.3", "@testing-library/react-hooks": "^7.0.2"}, "scripts": {"start": "react-scripts start", "start:original": "react-scripts start", "build": "react-app-rewired build", "build:original": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "test:components": "node run-tests.js --components", "test:services": "node run-tests.js --services", "test:hooks": "node run-tests.js --hooks", "test:utils": "node run-tests.js --utils", "test:all": "node run-tests.js --all", "test:watch": "node run-tests.js --watch", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:component": "cypress open --component", "e2e": "start-server-and-test start http://localhost:3000 cypress:open", "e2e:run": "start-server-and-test start http://localhost:3000 cypress:run", "optimize-images": "node optimize-images.js", "build:optimized": "npm run optimize-images && npm run build", "prestart-disabled": "npm run setup-images", "prebuild": "npm run setup-images", "build:safe": "cross-env SKIP_PREFLIGHT_CHECK=true react-app-rewired build", "setup-images": "node scripts/setup-images.js"}, "browser": {"fs": false, "path": false, "os": false, "net": false, "tls": false, "child_process": false, "crypto": false, "stream": false, "http": false, "https": false, "zlib": false, "dns": false, "timers": false, "timers/promises": false, "fs/promises": false, "querystring": false, "mongodb": false}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@testing-library/react-hooks": "^7.0.2", "@types/file-saver": "^2.0.5", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.36", "ajv": "^8.17.1", "babel-loader": "^10.0.0", "babel-plugin-import": "^1.13.8", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "customize-cra": "^1.0.0", "glob": "^11.0.1", "https-browserify": "^1.0.0", "jest-environment-jsdom": "^29.7.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "querystring-es3": "^0.2.1", "react-app-rewired": "^2.2.1", "sharp": "^0.33.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "timers-browserify": "^2.0.12", "webpack": "^5.76.0"}}