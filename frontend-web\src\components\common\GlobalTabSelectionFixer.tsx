import React, { useEffect } from 'react';
import { applyTabSelectionFixes } from '../../utils/applyTabSelectionFixes';
import ThemeSafeWrapper from './ThemeSafeWrapper';

/**
 * Component that applies tab selection fixes globally
 * This component should be added to the app's root component
 */
const GlobalTabSelectionFixerInner: React.FC = () => {
  useEffect(() => {
    // Apply tab selection fixes when the component mounts
    setTimeout(() => {
      applyTabSelectionFixes();
    }, 100);

    // Apply fixes again after a delay to ensure all components are rendered
    const timer = setTimeout(() => {
      applyTabSelectionFixes();
    }, 500);

    // Set up a MutationObserver to apply fixes when the DOM changes
    const observer = new MutationObserver((mutations) => {
      // Debounce the application of fixes to avoid excessive calls
      setTimeout(() => {
        applyTabSelectionFixes();
      }, 100);
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    // Clean up the observer and timer when the component unmounts
    return () => {
      observer.disconnect();
      clearTimeout(timer);
    };
  }, []);

  // This component doesn't render anything
  return null;
};

/**
 * Wrapper component that ensures the GlobalTabSelectionFixer has a theme context
 */
const GlobalTabSelectionFixer: React.FC = () => {
  return (
    <ThemeSafeWrapper>
      <GlobalTabSelectionFixerInner />
    </ThemeSafeWrapper>
  );
};

export default GlobalTabSelectionFixer;
