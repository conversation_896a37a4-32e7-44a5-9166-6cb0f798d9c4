// Note: Using mock data for now - database integration to be added later
const logger = require('../utils/logger');

const inventoryController = {
  // Get inventory overview (for /api/inventory route)
  getInventory: async (req, res) => {
    try {
      // Mock inventory overview data
      const inventoryOverview = {
        summary: {
          totalItems: 156,
          totalValue: 125000,
          lowStockItems: 8,
          outOfStockItems: 3,
          categories: {
            feed: 45,
            medicine: 23,
            equipment: 67,
            supplies: 21
          }
        },
        recentTransactions: [
          {
            id: '1',
            type: 'purchase',
            item: 'Cattle Feed Premium',
            quantity: 500,
            unit: 'kg',
            cost: 2500,
            date: '2024-12-15',
            supplier: 'AgriSupply Pro'
          },
          {
            id: '2',
            type: 'usage',
            item: 'Vaccination Syringes',
            quantity: 20,
            unit: 'pieces',
            date: '2024-12-14',
            usedFor: 'Vaccination program'
          }
        ],
        lowStockAlerts: [
          {
            id: '3',
            item: 'Mineral Supplements',
            currentStock: 5,
            minimumStock: 20,
            unit: 'kg',
            urgency: 'high'
          },
          {
            id: '4',
            item: 'Disinfectant',
            currentStock: 2,
            minimumStock: 10,
            unit: 'liters',
            urgency: 'medium'
          }
        ]
      };

      res.json({
        success: true,
        data: inventoryOverview
      });
    } catch (error) {
      logger.error('Error fetching inventory overview:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch inventory overview'
      });
    }
  },

  // Get all inventory items
  getItems: async (req, res) => {
    try {
      const { category, condition } = req.query;

      // Mock inventory items data
      let items = [
        {
          id: '1',
          name: 'Cattle Feed Premium',
          category: 'feed',
          description: 'High-quality cattle feed with balanced nutrition',
          quantity: 500,
          unit: 'kg',
          minimumStock: 100,
          location: 'Feed Storage A',
          purchaseDate: '2024-12-01',
          purchasePrice: 2500,
          supplier: 'AgriSupply Pro',
          condition: 'good',
          expiryDate: '2025-06-01',
          batchNumber: 'CF2024120001'
        },
        {
          id: '2',
          name: 'Vaccination Syringes',
          category: 'medicine',
          description: 'Disposable syringes for livestock vaccination',
          quantity: 150,
          unit: 'pieces',
          minimumStock: 50,
          location: 'Medical Storage',
          purchaseDate: '2024-11-15',
          purchasePrice: 750,
          supplier: 'VetMed Supplies',
          condition: 'new',
          expiryDate: '2026-11-15',
          batchNumber: 'VS2024111501'
        },
        {
          id: '3',
          name: 'Digital Weighing Scale',
          category: 'equipment',
          description: 'Portable digital scale for livestock weighing',
          quantity: 2,
          unit: 'pieces',
          minimumStock: 1,
          location: 'Equipment Shed',
          purchaseDate: '2024-10-20',
          purchasePrice: 15000,
          supplier: 'Farm Equipment Solutions',
          condition: 'excellent',
          lastMaintenanceDate: '2024-12-01',
          nextMaintenanceDate: '2025-06-01',
          serialNumber: 'DWS2024102001'
        },
        {
          id: '4',
          name: 'Mineral Supplements',
          category: 'feed',
          description: 'Essential mineral supplements for cattle health',
          quantity: 5,
          unit: 'kg',
          minimumStock: 20,
          location: 'Feed Storage B',
          purchaseDate: '2024-09-15',
          purchasePrice: 800,
          supplier: 'AgriSupply Pro',
          condition: 'good',
          expiryDate: '2025-09-15',
          batchNumber: 'MS2024091501'
        },
        {
          id: '5',
          name: 'Disinfectant Solution',
          category: 'supplies',
          description: 'Broad-spectrum disinfectant for farm hygiene',
          quantity: 2,
          unit: 'liters',
          minimumStock: 10,
          location: 'Chemical Storage',
          purchaseDate: '2024-11-30',
          purchasePrice: 300,
          supplier: 'Farm Hygiene Co.',
          condition: 'good',
          expiryDate: '2025-11-30',
          batchNumber: 'DS2024113001'
        }
      ];

      // Filter by category if provided
      if (category) {
        items = items.filter(item => item.category === category);
      }

      // Filter by condition if provided
      if (condition) {
        items = items.filter(item => item.condition === condition);
      }

      res.json({
        success: true,
        data: items,
        total: items.length
      });
    } catch (error) {
      logger.error('Error fetching inventory items:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch inventory items'
      });
    }
  },

  getInventoryById: async (req, res) => {
    try {
      const { id } = req.params;

      // Mock inventory item by ID
      const mockItem = {
        id: id,
        name: 'Cattle Feed Premium',
        category: 'feed',
        description: 'High-quality cattle feed with balanced nutrition',
        quantity: 500,
        unit: 'kg',
        minimumStock: 100,
        location: 'Feed Storage A',
        purchaseDate: '2024-12-01',
        purchasePrice: 2500,
        supplier: 'AgriSupply Pro',
        condition: 'good',
        expiryDate: '2025-06-01',
        batchNumber: 'CF2024120001'
      };

      res.json({
        success: true,
        data: mockItem
      });
    } catch (error) {
      logger.error('Error fetching inventory item:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch inventory item'
      });
    }
  },

  createInventoryItem: async (req, res) => {
    try {
      const { name, category, quantity, condition, location, purchaseDate, cost, notes } = req.body;

      // Mock creation - generate new item
      const newItem = {
        id: Date.now().toString(),
        name,
        category,
        quantity,
        condition: condition || 'good',
        location,
        purchaseDate,
        cost,
        notes,
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        data: newItem,
        message: 'Inventory item created successfully'
      });
    } catch (error) {
      logger.error('Error creating inventory item:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to create inventory item'
      });
    }
  },

  updateInventoryItem: async (req, res) => {
    try {
      const { id } = req.params;
      const { name, category, quantity, condition, location, purchaseDate, cost, notes } = req.body;

      // Mock update - return updated item
      const updatedItem = {
        id,
        name,
        category,
        quantity,
        condition,
        location,
        purchaseDate,
        cost,
        notes,
        updatedAt: new Date().toISOString()
      };

      res.json({
        success: true,
        data: updatedItem,
        message: 'Inventory item updated successfully'
      });
    } catch (error) {
      logger.error('Error updating inventory item:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to update inventory item'
      });
    }
  },

  deleteInventoryItem: async (req, res) => {
    try {
      const { id } = req.params;

      // Mock deletion - return success
      res.json({
        success: true,
        message: `Inventory item ${id} deleted successfully`
      });
    } catch (error) {
      logger.error('Error deleting inventory item:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to delete inventory item'
      });
    }
  }
};

module.exports = inventoryController;
