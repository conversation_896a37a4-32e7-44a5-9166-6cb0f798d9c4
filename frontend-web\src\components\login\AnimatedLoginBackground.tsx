import React, { useState, useEffect, useRef } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface AnimatedLoginBackgroundProps {
  images: string[];
  interval?: number;
  children?: React.ReactNode;
}

/**
 * AnimatedLoginBackground - A dynamic background component for the login page
 * Features smooth transitions between images with colorful animated gradients
 * inspired by the design: https://cdn.dribbble.com/users/2057697/screenshots/4231148/t_b_gif_2.gif
 */
const AnimatedLoginBackground: React.FC<AnimatedLoginBackgroundProps> = ({
  images,
  interval = 8000,
  children
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);
  
  // Get theme colors
  const primaryColor = availableColors[currentColor].primary;
  const secondaryColor = availableColors[currentColor].secondary;
  const accentColor = availableColors[currentColor].light;
  
  // Rotate through images
  useEffect(() => {
    const timer = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setIsTransitioning(false);
      }, 1000); // Transition duration
    }, interval);
    
    return () => clearInterval(timer);
  }, [images.length, interval]);
  
  // Canvas animation for colorful gradient effect
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    const resizeCanvas = () => {
      const { width, height } = canvas.getBoundingClientRect();
      canvas.width = width * window.devicePixelRatio;
      canvas.height = height * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Animation variables
    let time = 0;
    const circles: Array<{
      x: number;
      y: number;
      radius: number;
      color: string;
      speed: number;
      direction: number;
      opacity: number;
    }> = [];
    
    // Create initial circles
    const createCircles = () => {
      const colors = [
        primaryColor,
        secondaryColor,
        accentColor,
        theme.palette.primary.main,
        theme.palette.secondary.main
      ];
      
      circles.length = 0;
      
      for (let i = 0; i < 8; i++) {
        circles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          radius: 50 + Math.random() * 200,
          color: colors[Math.floor(Math.random() * colors.length)],
          speed: 0.2 + Math.random() * 0.5,
          direction: Math.random() * Math.PI * 2,
          opacity: 0.05 + Math.random() * 0.15
        });
      }
    };
    
    createCircles();
    
    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      // Update and draw circles
      circles.forEach(circle => {
        // Update position with slight movement
        circle.x += Math.cos(circle.direction + time * 0.1) * circle.speed;
        circle.y += Math.sin(circle.direction + time * 0.1) * circle.speed;
        
        // Bounce off edges
        if (circle.x < -circle.radius) circle.x = canvas.width + circle.radius;
        if (circle.x > canvas.width + circle.radius) circle.x = -circle.radius;
        if (circle.y < -circle.radius) circle.y = canvas.height + circle.radius;
        if (circle.y > canvas.height + circle.radius) circle.y = -circle.radius;
        
        // Draw gradient circle
        const gradient = ctx.createRadialGradient(
          circle.x, circle.y, 0,
          circle.x, circle.y, circle.radius
        );
        
        gradient.addColorStop(0, `${circle.color}${Math.round(circle.opacity * 255).toString(16).padStart(2, '0')}`);
        gradient.addColorStop(1, `${circle.color}00`);
        
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(circle.x, circle.y, circle.radius, 0, Math.PI * 2);
        ctx.fill();
      });
      
      time += 0.01;
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationRef.current);
    };
  }, [primaryColor, secondaryColor, accentColor, theme]);
  
  return (
    <Box sx={{
      position: 'relative',
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      borderRadius: { xs: 0, md: 2 }
    }}>
      {/* Animated Gradient Canvas */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 0
      }}>
        <canvas
          ref={canvasRef}
          style={{
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0
          }}
        />
      </Box>
      
      {/* Background Images with Transitions */}
      <AnimatePresence>
        <motion.div
          key={currentImageIndex}
          initial={{ opacity: 0 }}
          animate={{ opacity: isTransitioning ? 0 : 0.7 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1, ease: 'easeInOut' }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${images[currentImageIndex]})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            zIndex: 1,
            mixBlendMode: 'overlay'
          }}
        />
      </AnimatePresence>
      
      {/* Overlay with Blend Mode */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, 
            ${alpha(primaryColor, 0.3)}, 
            ${alpha(secondaryColor, 0.3)})`,
          zIndex: 2,
          mixBlendMode: 'color-dodge'
        }}
      />
      
      {/* Additional Animated Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 3,
          overflow: 'hidden'
        }}
      >
        {/* Animated Floating Shapes */}
        <motion.div
          initial={{ scale: 0.8, x: '10%', y: '10%' }}
          animate={{
            scale: [0.8, 1.2, 0.8],
            x: ['10%', '15%', '10%'],
            y: ['10%', '5%', '10%']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
          style={{
            position: 'absolute',
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.common.white, 0.2)} 0%, transparent 70%)`,
            filter: 'blur(40px)'
          }}
        />
        
        <motion.div
          initial={{ scale: 0.8, x: '80%', y: '80%' }}
          animate={{
            scale: [0.8, 1.5, 0.8],
            x: ['80%', '75%', '80%'],
            y: ['80%', '85%', '80%']
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: 'reverse',
            delay: 1
          }}
          style={{
            position: 'absolute',
            width: '250px',
            height: '250px',
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.common.white, 0.2)} 0%, transparent 70%)`,
            filter: 'blur(40px)'
          }}
        />
        
        <motion.div
          initial={{ scale: 0.8, x: '30%', y: '70%' }}
          animate={{
            scale: [0.8, 1.3, 0.8],
            x: ['30%', '35%', '30%'],
            y: ['70%', '65%', '70%']
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            repeatType: 'reverse',
            delay: 0.5
          }}
          style={{
            position: 'absolute',
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(accentColor, 0.2)} 0%, transparent 70%)`,
            filter: 'blur(30px)'
          }}
        />
      </Box>
      
      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 4,
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default AnimatedLoginBackground;
