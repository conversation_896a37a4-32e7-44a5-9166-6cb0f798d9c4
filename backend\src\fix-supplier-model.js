/**
 * Fix Supplier Model Script
 *
 * This script fixes the "Cannot overwrite `Supplier` model once compiled" error
 * by ensuring that the Supplier model is only defined once.
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');
const logger = require('./utils/logger');

// Load environment variables
dotenv.config();

// MongoDB connection URI
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    logger.info(`Connecting to MongoDB: ${uri.substring(0, uri.indexOf('?') > 0 ? uri.indexOf('?') : 20)}...`);
    logger.info(`Using database: ${dbName}`);

    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      dbName: dbName
    });

    logger.info('Successfully connected to MongoDB!');
    return true;
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    return false;
  }
}

// Define the Supplier schema
const createSupplierSchema = () => {
  // Define the contact schema
  const SupplierContactSchema = new mongoose.Schema({
    type: {
      type: String,
      enum: ['phone', 'email', 'website', 'address', 'social'],
      required: true
    },
    value: {
      type: String,
      required: true
    },
    label: {
      type: String
    }
  });

  // Define the location schema
  const SupplierLocationSchema = new mongoose.Schema({
    id: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    address: {
      type: String,
      required: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    phone: String,
    email: String,
    hours: String
  });

  // Define the main supplier schema
  const SupplierSchema = new mongoose.Schema({
    id: {
      type: String,
      required: true,
      unique: true
    },
    name: {
      type: String,
      required: true
    },
    logo: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    shortDescription: {
      type: String
    },
    categories: [{
      type: String,
      enum: [
        'livestock-feed',
        'equipment',
        'seeds',
        'fertilizer',
        'chemicals',
        'irrigation',
        'animal-health',
        'fuel',
        'retail',
        'financial',
        'insurance',
        'other'
      ]
    }],
    contacts: [SupplierContactSchema],
    locations: [SupplierLocationSchema],
    website: {
      type: String,
      required: true
    },
    services: [String],
    established: Number,
    coverImage: String,
    rating: {
      type: Number,
      min: 0,
      max: 5
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    },
    featuredProducts: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'SupplierProduct'
    }]
  }, {
    timestamps: true
  });

  // Add text index for search functionality
  SupplierSchema.index({
    name: 'text',
    description: 'text',
    shortDescription: 'text',
    'services': 'text'
  });

  return SupplierSchema;
};

// Fix the Supplier model
async function fixSupplierModel() {
  try {
    // Connect to MongoDB
    const connected = await connectToMongoDB();
    if (!connected) {
      logger.error('Failed to connect to MongoDB. Exiting...');
      process.exit(1);
    }

    // Check if the Supplier model already exists
    if (mongoose.models.Supplier) {
      logger.info('Supplier model already exists. Deleting it...');
      delete mongoose.models.Supplier;
    }

    // Create the Supplier schema
    const SupplierSchema = createSupplierSchema();

    // Create the Supplier model
    const Supplier = mongoose.model('Supplier', SupplierSchema);
    logger.info('Supplier model created successfully!');

    // Update the Supplier.js file
    const supplierFilePath = path.join(__dirname, 'models', 'Supplier.js');
    if (fs.existsSync(supplierFilePath)) {
      let content = fs.readFileSync(supplierFilePath, 'utf8');
      content = content.replace(
        'module.exports = mongoose.model(\'Supplier\', SupplierSchema);',
        '// Check if the model already exists to prevent "Cannot overwrite model once compiled" error\nmodule.exports = mongoose.models.Supplier || mongoose.model(\'Supplier\', SupplierSchema);'
      );
      fs.writeFileSync(supplierFilePath, content);
      logger.info('Updated Supplier.js file');
    }

    // Update the inventory.schema.js file
    const inventorySchemaFilePath = path.join(__dirname, 'models', 'schemas', 'inventory.schema.js');
    if (fs.existsSync(inventorySchemaFilePath)) {
      let content = fs.readFileSync(inventorySchemaFilePath, 'utf8');
      content = content.replace(
        'const Supplier = mongoose.model(\'Supplier\', supplierSchema);',
        '// Check if the model already exists to prevent "Cannot overwrite model once compiled" error\nconst Supplier = mongoose.models.Supplier || mongoose.model(\'Supplier\', supplierSchema);'
      );
      fs.writeFileSync(inventorySchemaFilePath, content);
      logger.info('Updated inventory.schema.js file');
    }

    // Update the mongoose-models.js file
    const mongooseModelsFilePath = path.join(__dirname, 'models', 'mongoose-models.js');
    if (fs.existsSync(mongooseModelsFilePath)) {
      let content = fs.readFileSync(mongooseModelsFilePath, 'utf8');
      content = content.replace(
        'Supplier: mongoose.model(\'Supplier\', new Schema({',
        '// Check if the model already exists to prevent "Cannot overwrite model once compiled" error\n  Supplier: mongoose.models.Supplier || mongoose.model(\'Supplier\', new Schema({'
      );
      fs.writeFileSync(mongooseModelsFilePath, content);
      logger.info('Updated mongoose-models.js file');
    }

    logger.info('All files updated successfully!');
    logger.info('The "Cannot overwrite `Supplier` model once compiled" error should be fixed now.');

    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');

    return true;
  } catch (error) {
    logger.error('Error fixing Supplier model:', error);
    return false;
  }
}

// Run the fix
fixSupplierModel()
  .then((success) => {
    if (success) {
      logger.info('Supplier model fix completed successfully!');
    } else {
      logger.error('Failed to fix Supplier model.');
    }
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Unhandled error:', error);
    process.exit(1);
  });
