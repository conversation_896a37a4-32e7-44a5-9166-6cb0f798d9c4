/**
 * API Service Tests
 * 
 * This file contains tests for the API service.
 */

import axios from 'axios';
import { authAPI, animalsAPI } from '../api';

// Mock axios
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    request: jest.fn(),
    interceptors: {
      request: {
        use: jest.fn()
      },
      response: {
        use: jest.fn()
      }
    }
  }))
}));

describe('API Service', () => {
  let mockAxios: any;
  
  beforeEach(() => {
    // Get the mocked axios instance
    mockAxios = (axios.create as jest.Mock).mock.results[0].value;
    
    // Clear localStorage
    localStorage.clear();
    
    // Reset mocks
    jest.clearAllMocks();
  });
  
  describe('authAPI', () => {
    it('should call login endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          user: { id: '1', username: 'testuser' },
          token: 'test-token'
        }
      });
      
      // Call login
      const result = await authAPI.login('testuser', 'password');
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'post',
        url: '/auth/login',
        data: { username: 'testuser', password: 'password' }
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        user: { id: '1', username: 'testuser' },
        token: 'test-token'
      });
    });
    
    it('should call getCurrentUser endpoint', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: { id: '1', username: 'testuser' }
        }
      });
      
      // Call getCurrentUser
      const result = await authAPI.getCurrentUser();
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'get',
        url: '/auth/me'
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: { id: '1', username: 'testuser' }
      });
    });
    
    it('should call logout endpoint', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true
        }
      });
      
      // Set token in localStorage
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('user', JSON.stringify({ id: '1', username: 'testuser' }));
      
      // Call logout
      await authAPI.logout();
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'post',
        url: '/auth/logout'
      });
      
      // Check that localStorage is cleared
      expect(localStorage.getItem('token')).toBeNull();
      expect(localStorage.getItem('user')).toBeNull();
    });
  });
  
  describe('animalsAPI', () => {
    it('should call getAnimals endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: [{ id: '1', name: 'Animal 1' }]
        }
      });
      
      // Call getAnimals
      const result = await animalsAPI.getAnimals({ species: 'Cattle' });
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'get',
        url: '/animals',
        params: { species: 'Cattle' }
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: [{ id: '1', name: 'Animal 1' }]
      });
    });
    
    it('should call getAnimalById endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: { id: '1', name: 'Animal 1' }
        }
      });
      
      // Call getAnimalById
      const result = await animalsAPI.getAnimalById('1');
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'get',
        url: '/animals/1'
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: { id: '1', name: 'Animal 1' }
      });
    });
    
    it('should call createAnimal endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: { id: '1', name: 'Animal 1' }
        }
      });
      
      // Call createAnimal
      const animalData = { name: 'Animal 1', species: 'Cattle' };
      const result = await animalsAPI.createAnimal(animalData);
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'post',
        url: '/animals',
        data: animalData
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: { id: '1', name: 'Animal 1' }
      });
    });
    
    it('should call updateAnimal endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: { id: '1', name: 'Updated Animal' }
        }
      });
      
      // Call updateAnimal
      const animalData = { name: 'Updated Animal' };
      const result = await animalsAPI.updateAnimal('1', animalData);
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'put',
        url: '/animals/1',
        data: animalData
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: { id: '1', name: 'Updated Animal' }
      });
    });
    
    it('should call deleteAnimal endpoint with correct parameters', async () => {
      // Mock successful response
      mockAxios.request.mockResolvedValueOnce({
        data: {
          success: true,
          data: { id: '1', name: 'Animal 1' }
        }
      });
      
      // Call deleteAnimal
      const result = await animalsAPI.deleteAnimal('1');
      
      // Check that axios.request was called with correct parameters
      expect(mockAxios.request).toHaveBeenCalledWith({
        method: 'delete',
        url: '/animals/1'
      });
      
      // Check that the result is correct
      expect(result).toEqual({
        success: true,
        data: { id: '1', name: 'Animal 1' }
      });
    });
  });
  
  it('should handle API errors', async () => {
    // Mock error response
    const errorResponse = {
      response: {
        data: {
          success: false,
          message: 'Invalid credentials'
        },
        status: 401
      }
    };
    
    mockAxios.request.mockRejectedValueOnce(errorResponse);
    
    // Call login and expect it to throw
    await expect(authAPI.login('testuser', 'wrong-password')).rejects.toThrow('Invalid credentials');
  });
  
  it('should handle network errors', async () => {
    // Mock network error
    mockAxios.request.mockRejectedValueOnce(new Error('Network Error'));
    
    // Call login and expect it to throw
    await expect(authAPI.login('testuser', 'password')).rejects.toThrow('Network Error');
  });
});
