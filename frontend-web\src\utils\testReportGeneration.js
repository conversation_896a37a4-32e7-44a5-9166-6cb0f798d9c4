/**
 * Test script for report generation
 * 
 * This script tests the report generation functionality to ensure it works correctly
 * with both real MongoDB and mock data.
 */

const { generateHtmlReport } = require('../services/reportService');

// Test parameters
const testParams = {
  type: 'financial',
  format: 'html',
  timePeriod: 'month',
  startDate: '2023-01-01',
  endDate: '2023-01-31',
  filters: {
    includeCharts: true,
    includeSummary: true,
    currency: 'ZAR'
  }
};

// Test function
async function testReportGeneration() {
  console.log('Testing report generation...');
  
  try {
    // Generate HTML report
    const html = await generateHtmlReport(testParams);
    
    // Check if the report was generated successfully
    if (html && html.includes('<!DOCTYPE html>')) {
      console.log('Report generated successfully!');
      
      // Check for error messages in the report
      if (html.includes('Error Generating Report')) {
        console.error('Report contains error message!');
        
        // Extract error message
        const errorMatch = html.match(/<p class="error-message">(.*?)<\/p>/s);
        if (errorMatch && errorMatch[1]) {
          console.error('Error message:', errorMatch[1]);
        }
      } else {
        console.log('Report does not contain error messages.');
      }
      
      // Check for data in the report
      if (html.includes('No data available')) {
        console.warn('Report indicates no data is available.');
      } else {
        console.log('Report contains data.');
      }
    } else {
      console.error('Failed to generate report!');
    }
  } catch (error) {
    console.error('Error testing report generation:', error);
  }
}

// Run the test
testReportGeneration().then(() => {
  console.log('Test completed.');
}).catch(error => {
  console.error('Test failed:', error);
});
