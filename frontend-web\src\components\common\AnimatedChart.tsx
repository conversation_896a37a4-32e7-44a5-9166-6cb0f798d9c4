import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, IconButton, Menu, MenuItem, useTheme, alpha, Tooltip } from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
// Import optimized icons
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as Pie<PERSON>hartIcon,
  <PERSON><PERSON><PERSON> as Bar<PERSON>hartIcon,
  Show<PERSON>hart as LineChartIcon,
  Insert<PERSON>hart as AreaChartIcon, // Using InsertChart as a replacement for AreaChart
  Info
} from '../../utils/iconImports';
import { motion } from 'framer-motion';

type ChartType = 'pie' | 'bar' | 'line' | 'area';

interface AnimatedChartProps {
  title: string;
  subtitle?: string;
  data: any[];
  type?: ChartType;
  dataKeys: string[];
  xAxisDataKey?: string;
  height?: number;
  colors?: string[];
  accentColor?: string;
  backgroundImage?: string;
  delay?: number;
  allowChartTypeChange?: boolean;
  onFilter?: () => void;
  tooltip?: string;
}

const AnimatedChart: React.FC<AnimatedChartProps> = ({
  title,
  subtitle,
  data,
  type = 'bar',
  dataKeys,
  xAxisDataKey = 'name',
  height = 300,
  colors,
  accentColor,
  backgroundImage,
  delay = 0,
  allowChartTypeChange = false,
  onFilter,
  tooltip
}) => {
  const theme = useTheme();
  const [chartType, setChartType] = useState<ChartType>(type);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [chartTypeMenuAnchorEl, setChartTypeMenuAnchorEl] = useState<null | HTMLElement>(null);

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
    '#8884d8',
    '#82ca9d',
    '#ffc658',
    '#ff8042'
  ];

  const chartColors = colors || defaultColors;
  const mainColor = accentColor || theme.palette.primary.main;

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleChartTypeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setChartTypeMenuAnchorEl(event.currentTarget);
  };

  const handleChartTypeMenuClose = () => {
    setChartTypeMenuAnchorEl(null);
  };

  const handleChartTypeChange = (newType: ChartType) => {
    setChartType(newType);
    handleChartTypeMenuClose();
  };

  const renderChart = () => {
    switch (chartType) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill={mainColor}
                dataKey={dataKeys[0]}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                ))}
              </Pie>
              <RechartsTooltip
                formatter={(value: number, name: string) => [
                  `${value}`, name
                ]}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisDataKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={chartColors[index % chartColors.length]}
                  stackId={dataKeys.length > 1 ? "stack" : undefined}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisDataKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={chartColors[index % chartColors.length]}
                  activeDot={{ r: 8 }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={xAxisDataKey} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              {dataKeys.map((key, index) => (
                <Area
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stackId={dataKeys.length > 1 ? "1" : undefined}
                  stroke={chartColors[index % chartColors.length]}
                  fill={alpha(chartColors[index % chartColors.length], 0.6)}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  const getChartTypeIcon = (type: ChartType) => {
    switch (type) {
      case 'pie': return <PieChartIcon />;
      case 'bar': return <BarChartIcon />;
      case 'line': return <LineChartIcon />;
      case 'area': return <AreaChartIcon />;
      default: return <BarChartIcon />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
    >
      <Card
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          height: '100%',
          position: 'relative',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
        }}
      >
        {/* Background Image */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.05,
              zIndex: 0
            }}
          />
        )}

        {/* Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: `1px solid ${theme.palette.divider}`,
            position: 'relative',
            zIndex: 1
          }}
        >
          <Box>
            <Box display="flex" alignItems="center">
              <Typography variant="h6" fontWeight="bold">
                {title}
              </Typography>
              {tooltip && (
                <Tooltip title={tooltip}>
                  <IconButton size="small">
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>

          <Box display="flex" alignItems="center">
            {allowChartTypeChange && (
              <>
                <CustomButton
                  startIcon={getChartTypeIcon(chartType)}
                  size="small"
                  onClick={handleChartTypeMenuOpen}
                  sx={{ mr: 1 }}
                >
                  {chartType.charAt(0).toUpperCase() + chartType.slice(1)}
                </CustomButton>
                <Menu
                  anchorEl={chartTypeMenuAnchorEl}
                  open={Boolean(chartTypeMenuAnchorEl)}
                  onClose={handleChartTypeMenuClose}
                >
                  <MenuItem onClick={() => handleChartTypeChange('bar')}>
                    <BarChartIcon sx={{ mr: 1 }} /> Bar Chart
                  </MenuItem>
                  <MenuItem onClick={() => handleChartTypeChange('line')}>
                    <LineChartIcon sx={{ mr: 1 }} /> Line Chart
                  </MenuItem>
                  <MenuItem onClick={() => handleChartTypeChange('area')}>
                    <AreaChartIcon sx={{ mr: 1 }} /> Area Chart
                  </MenuItem>
                  <MenuItem onClick={() => handleChartTypeChange('pie')}>
                    <PieChartIcon sx={{ mr: 1 }} /> Pie Chart
                  </MenuItem>
                </Menu>
              </>
            )}

            {onFilter && (
              <IconButton size="small" onClick={onFilter}>
                <FilterList />
              </IconButton>
            )}

            <IconButton size="small" onClick={handleMenuOpen}>
              <MoreVert />
            </IconButton>

            <Menu
              anchorEl={menuAnchorEl}
              open={Boolean(menuAnchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleMenuClose}>Download as PNG</MenuItem>
              <MenuItem onClick={handleMenuClose}>Download Data</MenuItem>
              <MenuItem onClick={handleMenuClose}>View Full Screen</MenuItem>
            </Menu>
          </Box>
        </Box>

        {/* Chart */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 1,
            height: `calc(100% - 70px)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 2
          }}
        >
          {data.length === 0 ? (
            <Typography variant="body2" color="text.secondary" align="center">
              No data available
            </Typography>
          ) : (
            renderChart()
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimatedChart;
