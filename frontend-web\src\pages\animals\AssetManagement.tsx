import React, { useState, useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { motion } from 'framer-motion';
import AssetManagementDashboard from './AssetManagementDashboard';
import RetirementTracking from './RetirementTracking';

const AssetManagement = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Routes>
        <Route path="/" element={<AssetManagementDashboard />} />
        <Route path="/retirement" element={<RetirementTracking />} />
      </Routes>
    </motion.div>
  );
};

export default AssetManagement;
