import React, { useState } from 'react';
import { Box, Typography, Tabs, Tab, Paper, useTheme, alpha } from '@mui/material';
import { Settings as SettingsIcon, Person, Palette, Security, Notifications, Language } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { DashboardCard, withSubModuleTranslation } from '../../components/common';
import ThemeSettings from '../../components/settings/ThemeSettings';
import UserManagement from '../../components/settings/UserManagement';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = (props) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

interface SettingsProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const Settings: React.FC<SettingsProps> = ({
  translate: propTranslate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const { user } = useAuth();
  const { translate: contextTranslate } = useLanguage();
  const [tabValue, setTabValue] = useState(0);

  // Use the translate prop if provided, otherwise use the context translate
  const translate = propTranslate || contextTranslate;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          {translateSubModule ? translateSubModule('title', 'Settings') : translate ? translate('settings.title', { fallback: 'Settings' }) : 'Settings'}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          {translateSubModule ? translateSubModule('subtitle', 'Configure your application settings and preferences') : translate ? translate('settings.subtitle', { fallback: 'Configure your application settings and preferences' }) : 'Configure your application settings and preferences'}
        </Typography>

        <Paper
          sx={{
            borderRadius: '16px',
            overflow: 'hidden',
            boxShadow: '0 6px 24px rgba(0,0,0,0.08)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          }}
        >
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  py: 2,
                  px: 3,
                  fontSize: '1.1rem',
                  fontWeight: 'medium',
                  textTransform: 'none',
                  minWidth: 120
                }
              }}
            >
              <Tab icon={<Palette />} label={translateModuleField ? translateModuleField('theme', 'Theme') : 'Theme'} iconPosition="start" />
              {(user?.role === 'admin' || user?.role === 'manager') && (
                <Tab icon={<Person />} label={translateModuleField ? translateModuleField('users', 'Users') : 'Users'} iconPosition="start" />
              )}
              <Tab icon={<Security />} label={translateModuleField ? translateModuleField('security', 'Security') : 'Security'} iconPosition="start" />
              <Tab icon={<Notifications />} label={translateModuleField ? translateModuleField('notifications', 'Notifications') : 'Notifications'} iconPosition="start" />
              <Tab icon={<Language />} label={translateModuleField ? translateModuleField('language', 'Language') : 'Language'} iconPosition="start" />
            </Tabs>
          </Box>

          <Box sx={{ p: 3 }}>
            <TabPanel value={tabValue} index={0}>
              <ThemeSettings />
            </TabPanel>

            {(user?.role === 'admin' || user?.role === 'manager') && (
              <TabPanel value={tabValue} index={1}>
                <UserManagement />
              </TabPanel>
            )}

            <TabPanel value={tabValue} index={user?.role === 'admin' || user?.role === 'manager' ? 2 : 1}>
              <DashboardCard title={translateModuleField ? translateModuleField('security_settings', 'Security Settings') : 'Security Settings'} icon={<Security />}>
                <Typography variant="body1">
                  {translateModuleField ? translateModuleField('security_future_update', 'Security settings will be implemented in a future update.') : 'Security settings will be implemented in a future update.'}
                </Typography>
              </DashboardCard>
            </TabPanel>

            <TabPanel value={tabValue} index={user?.role === 'admin' || user?.role === 'manager' ? 3 : 2}>
              <DashboardCard title={translateModuleField ? translateModuleField('notification_settings', 'Notification Settings') : 'Notification Settings'} icon={<Notifications />}>
                <Typography variant="body1">
                  {translateModuleField ? translateModuleField('notification_future_update', 'Notification settings will be implemented in a future update.') : 'Notification settings will be implemented in a future update.'}
                </Typography>
              </DashboardCard>
            </TabPanel>

            <TabPanel value={tabValue} index={user?.role === 'admin' || user?.role === 'manager' ? 4 : 3}>
              <DashboardCard title={translateModuleField ? translateModuleField('language_settings', 'Language Settings') : 'Language Settings'} icon={<Language />}>
                <Typography variant="body1">
                  {translateModuleField ? translateModuleField('language_future_update', 'Language settings will be implemented in a future update.') : 'Language settings will be implemented in a future update.'}
                </Typography>
              </DashboardCard>
            </TabPanel>
          </Box>
        </Paper>
      </Box>
    </motion.div>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(Settings, 'settings', 'page');