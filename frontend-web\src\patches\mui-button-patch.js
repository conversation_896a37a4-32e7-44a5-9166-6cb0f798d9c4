/**
 * This patch fixes the "Cannot read properties of undefined (reading 'dark')" error
 * by monkey-patching the MUI Button component to handle missing theme properties.
 */

// Try to patch the Button component
try {
  // Get the Button component
  const Button = require('@mui/material/Button').default;
  
  // Store the original render method
  const originalRender = Button.render;
  
  // Override the render method to handle missing theme properties
  if (originalRender) {
    Button.render = function(props, ref) {
      // Ensure props exists
      props = props || {};
      
      // Create a safe theme if needed
      if (!props.theme || !props.theme.palette || !props.theme.palette.primary || !props.theme.palette.primary.dark) {
        // Create a minimal theme with the required properties
        const safeTheme = {
          palette: {
            primary: {
              main: '#3AA99F',
              light: '#4FBEB4',
              dark: '#2A8A82',
              contrastText: '#ffffff'
            },
            secondary: {
              main: '#38B2AC',
              light: '#4FD1CB',
              dark: '#2C8A84',
              contrastText: '#ffffff'
            }
          }
        };
        
        // Merge with existing theme if it exists
        if (props.theme) {
          props.theme = { ...props.theme, ...safeTheme };
          
          // Ensure palette exists
          if (!props.theme.palette) {
            props.theme.palette = safeTheme.palette;
          } else {
            // Ensure primary palette exists
            if (!props.theme.palette.primary) {
              props.theme.palette.primary = safeTheme.palette.primary;
            } else {
              // Ensure primary.dark exists
              if (!props.theme.palette.primary.dark) {
                props.theme.palette.primary.dark = props.theme.palette.primary.main || safeTheme.palette.primary.dark;
              }
            }
            
            // Ensure secondary palette exists
            if (!props.theme.palette.secondary) {
              props.theme.palette.secondary = safeTheme.palette.secondary;
            } else {
              // Ensure secondary.dark exists
              if (!props.theme.palette.secondary.dark) {
                props.theme.palette.secondary.dark = props.theme.palette.secondary.main || safeTheme.palette.secondary.dark;
              }
            }
          }
        } else {
          props.theme = safeTheme;
        }
      }
      
      // Call the original render method
      return originalRender.call(this, props, ref);
    };
  }
  
  console.log('MUI Button patch applied successfully');
} catch (error) {
  console.error('Failed to apply MUI Button patch:', error);
}
