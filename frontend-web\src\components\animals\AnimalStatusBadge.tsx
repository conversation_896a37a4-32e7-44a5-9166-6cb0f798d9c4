import React from 'react';
import { Chip, SxProps, Theme } from '@mui/material';

export interface AnimalStatusBadgeProps {
  status: string;
  sx?: SxProps<Theme>;
}

const AnimalStatusBadge: React.FC<AnimalStatusBadgeProps> = ({ status, sx }) => {
  const getStatusConfig = (status: string) => {
    switch (status.toLowerCase()) {
      case 'healthy':
        return { color: 'success', label: 'Healthy' };
      case 'attention':
        return { color: 'warning', label: 'Needs Attention' };
      case 'critical':
        return { color: 'error', label: 'Critical' };
      case 'pregnant':
        return { color: 'info', label: 'Pregnant' };
      default:
        return { color: 'default', label: status };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Chip
      label={config.label}
      color={config.color as any}
      size="small"
      className="font-medium"
      sx={sx}
    />
  );
};

export default AnimalStatusBadge;