import React, { useMemo, useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Container, Typography, useTheme, alpha, Breadcrumbs, Link, useMediaQuery, Chip, Tooltip, IconButton } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
// Removed ThemeContext import as we're using fixed teal colors
import { getModuleTheme } from '../../utils/backgroundThemes';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { useLanguage } from '../../contexts/LanguageContext';

interface ModuleHeaderProps {
  title: string;
  subtitle?: string;
  module?: string;
  submodule?: string;
  actionLabel?: string;
  onAction?: () => void | Promise<void>;
  actionIcon?: React.ReactNode;
  breadcrumbs?: Array<{label: string; path?: string}>;
  icon?: React.ReactNode;
  status?: 'success' | 'warning' | 'error' | 'info';
  statusText?: string;
  expandable?: boolean;
  infoTooltip?: string;
  secondaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  tags?: Array<{label: string; color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info'}>;
  disabled?: boolean;
  backgroundImage?: string;
}

const ModuleHeader: React.FC<ModuleHeaderProps> = ({
  title,
  subtitle,
  module,
  submodule,
  actionLabel,
  onAction,
  actionIcon,
  breadcrumbs,
  icon,
  status,
  statusText,
  expandable = false,
  infoTooltip,
  secondaryAction,
  tags,
  disabled = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [expanded, setExpanded] = useState(!isMobile);
  const { translate } = useLanguage();

  // Translate title, subtitle and action label if module and submodule are provided
  const translatedTitle = module && submodule ?
    translate(`${module}.${submodule}`, { fallback: title }) : title;
  const translatedSubtitle = module && submodule ?
    translate(`${module}.${submodule}.subtitle`, { fallback: subtitle }) : subtitle;
  const translatedActionLabel = module && submodule && actionLabel ?
    translate(`${module}.${submodule}.action`, { fallback: actionLabel }) : actionLabel;

  // Using fixed teal colors for consistent styling across all modules
  // This ensures all module headers have the same visual appearance

  // Get theme for the module - always using teal color scheme for consistency
  const moduleTheme = useMemo(() => {
    // Always use the teal color scheme for consistent styling
    return getModuleTheme(module || '', '#3AA99F', '#2A8A82');
  }, [module]);

  // Handle resize to adjust expanded state
  useEffect(() => {
    setExpanded(!isMobile);
  }, [isMobile]);

  // Toggle expanded state
  const toggleExpanded = () => {
    setExpanded(prev => !prev);
  };

  // Get status color
  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'info':
      default:
        return theme.palette.info.main;
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        mb: 4,
        overflow: 'hidden',
        borderRadius: { xs: 0, md: '0 0 16px 16px' },
        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
      }}
    >
      {/* Solid Color Background */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: moduleTheme.backgroundImage
            ? `linear-gradient(135deg, ${alpha('#3AA99F', 0.85)}, ${alpha('#2A8A82', 0.75)}), url(${moduleTheme.backgroundImage})`
            : `linear-gradient(135deg, #3AA99F, #2A8A82)`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: 0
        }}
      />

      {/* Subtle Pattern Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url(https://www.transparenttextures.com/patterns/cubes.png)',
          opacity: 0.05,
          zIndex: 1
        }}
      />

      {/* Content */}
      <Container
        maxWidth="lg"
        sx={{
          position: 'relative',
          zIndex: 10,
          py: { xs: 3, md: 5 },
          color: 'white',
        }}
      >
        {/* Top Bar with Breadcrumbs and Expand/Collapse */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          {/* Breadcrumbs */}
          {breadcrumbs && breadcrumbs.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              style={{ flex: 1, overflow: 'hidden' }}
            >
              <Breadcrumbs
                separator={<NavigateNextIcon fontSize="small" sx={{ color: 'white', opacity: 0.7 }} />}
                aria-label="breadcrumb"
                sx={{
                  '& .MuiBreadcrumbs-ol': {
                    flexWrap: isMobile ? 'wrap' : 'nowrap',
                  },
                  '& .MuiBreadcrumbs-li': {
                    color: 'white',
                    opacity: 0.7,
                    fontSize: '0.875rem',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: isMobile ? '100px' : 'none',
                  }
                }}
              >
                {breadcrumbs.map((crumb, index) => {
                  const isLast = index === breadcrumbs.length - 1;
                  return isLast ? (
                    <Typography key={index} color="white" fontWeight="medium" fontSize="0.875rem">
                      {crumb.label}
                    </Typography>
                  ) : (
                    <Link
                      key={index}
                      color="inherit"
                      href={crumb.path || '#'}
                      sx={{
                        textDecoration: 'none',
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      {crumb.label}
                    </Link>
                  );
                })}
              </Breadcrumbs>
            </motion.div>
          )}

          {/* Status Indicator */}
          {status && statusText && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            >
              <Chip
                label={statusText}
                size="small"
                sx={{
                  backgroundColor: alpha(getStatusColor(), 0.2),
                  color: 'white',
                  fontWeight: 'medium',
                  border: `1px solid ${alpha(getStatusColor(), 0.5)}`,
                  ml: 1
                }}
              />
            </motion.div>
          )}

          {/* Expand/Collapse Button */}
          {expandable && (
            <IconButton
              size="small"
              onClick={toggleExpanded}
              sx={{
                color: 'white',
                ml: 1,
                backgroundColor: alpha('#ffffff', 0.1),
                '&:hover': {
                  backgroundColor: alpha('#ffffff', 0.2),
                }
              }}
            >
              {expanded ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
          )}
        </Box>

        {/* Main Content */}
        <AnimatePresence>
          {(!expandable || expanded) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: { xs: 'column', sm: 'row' },
                  justifyContent: 'space-between',
                  alignItems: { xs: 'flex-start', sm: 'center' },
                }}
              >
                <Box>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {icon && (
                        <Box sx={{ fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' } }}>
                          {icon}
                        </Box>
                      )}
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography
                          variant="h4"
                          component="h1"
                          fontWeight="bold"
                          sx={{
                            fontSize: { xs: '1.75rem', sm: '2rem', md: '2.25rem' },
                            textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            letterSpacing: '-0.5px'
                          }}
                        >
                          {translatedTitle}
                        </Typography>

                        {/* Info Tooltip */}
                        {infoTooltip && (
                          <Tooltip title={infoTooltip} arrow placement="top">
                            <IconButton size="small" sx={{ color: 'white', opacity: 0.7, ml: 1 }}>
                              <InfoOutlinedIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </Box>
                  </motion.div>

                  {/* Tags */}
                  {tags && tags.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                    >
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1, mb: 1 }}>
                        {tags.map((tag, index) => (
                          <Chip
                            key={index}
                            label={tag.label}
                            size="small"
                            color={tag.color || 'primary'}
                            variant="outlined"
                            sx={{
                              backgroundColor: alpha(theme.palette[tag.color || 'primary'].main, 0.1),
                              color: 'white',
                              borderColor: alpha(theme.palette[tag.color || 'primary'].main, 0.3),
                            }}
                          />
                        ))}
                      </Box>
                    </motion.div>
                  )}

                  {subtitle && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                    >
                      <Typography
                        variant="subtitle1"
                        sx={{
                          opacity: 0.9,
                          maxWidth: 600,
                          fontSize: { xs: '0.95rem', sm: '1rem', md: '1.1rem' },
                          lineHeight: 1.5,
                          textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                          mt: tags && tags.length > 0 ? 1 : 0
                        }}
                      >
                        {translatedSubtitle}
                      </Typography>
                    </motion.div>
                  )}
                </Box>

                {/* Action Buttons */}
                <Box sx={{
                  mt: { xs: 2, sm: 0 },
                  display: 'flex',
                  gap: 2,
                  flexDirection: { xs: 'column', sm: 'row' }
                }}>
                  {/* Secondary Action Button */}
                  {secondaryAction && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                    >
                      <CustomButton
                        variant="outlined"
                        onClick={secondaryAction.onClick}
                        startIcon={secondaryAction.icon}
                        sx={{
                          borderColor: alpha('#ffffff', 0.3),
                          color: 'white',
                          fontWeight: 600,
                          padding: '8px 16px',
                          textTransform: 'none',
                          '&:hover': {
                            borderColor: alpha('#ffffff', 0.5),
                            backgroundColor: alpha('#ffffff', 0.1),
                          }
                        }}
                      >
                        {secondaryAction.label}
                      </CustomButton>
                    </motion.div>
                  )}

                  {/* Primary Action Button */}
                  {actionLabel && onAction && (
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: secondaryAction ? 0.3 : 0.2 }}
                    >
                      <CustomButton
                        variant="contained"
                        color="secondary"
                        onClick={onAction}
                        startIcon={actionIcon}
                        disabled={disabled}
                        sx={{
                          background: `linear-gradient(to right, ${alpha('#ffffff', 0.2)}, ${alpha('#ffffff', 0.1)})`,
                          border: `1px solid ${alpha('#ffffff', 0.3)}`,
                          backdropFilter: 'blur(4px)',
                          color: 'white',
                          fontWeight: 600,
                          padding: '8px 16px',
                          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                          textTransform: 'none',
                          '&:hover': {
                            background: `linear-gradient(to right, ${alpha('#ffffff', 0.3)}, ${alpha('#ffffff', 0.2)})`,
                            boxShadow: '0 6px 8px rgba(0,0,0,0.15)',
                          }
                        }}
                      >
                        {translatedActionLabel}
                      </CustomButton>
                    </motion.div>
                  )}
                </Box>
              </Box>
            </motion.div>
          )}
        </AnimatePresence>
      </Container>
    </Box>
  );
};

export default ModuleHeader;
