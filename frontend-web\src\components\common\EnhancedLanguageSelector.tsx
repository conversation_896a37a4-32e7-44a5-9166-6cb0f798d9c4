import React, { useState } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Language,
  ExpandMore,
  Check,
  Public
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useLanguage, LANGUAGES, LANGUAGE_NAMES } from '../../contexts/LanguageContext';
import '../../styles/enhanced-language-selector.css';

interface EnhancedLanguageSelectorProps {
  variant?: 'standard' | 'compact' | 'floating' | 'futuristic';
  showLabel?: boolean;
  showFlag?: boolean;
  size?: 'small' | 'medium' | 'large';
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

// Language flag emojis mapping
const LANGUAGE_FLAGS = {
  [LANGUAGES.ENGLISH]: '🇬🇧',
  [LANGUAGES.AFRIKAANS]: '🇿🇦',
  [LANGUAGES.ZULU]: '🇿🇦',
  [LANGUAGES.XHOSA]: '🇿🇦',
  [LANGUAGES.SOTHO]: '🇿🇦',
  [LANGUAGES.TSWANA]: '🇿🇦',
  [LANGUAGES.VENDA]: '🇿🇦',
  [LANGUAGES.TSONGA]: '🇿🇦',
  [LANGUAGES.NDEBELE]: '🇿🇦',
  [LANGUAGES.SWATI]: '🇿🇦',
  [LANGUAGES.SEPEDI]: '🇿🇦',
  [LANGUAGES.HINDI]: '🇮🇳',
  [LANGUAGES.PORTUGUESE]: '🇵🇹'
};

// South African official languages (first 11)
const SA_OFFICIAL_LANGUAGES = [
  LANGUAGES.ENGLISH,
  LANGUAGES.AFRIKAANS,
  LANGUAGES.ZULU,
  LANGUAGES.XHOSA,
  LANGUAGES.SOTHO,
  LANGUAGES.TSWANA,
  LANGUAGES.VENDA,
  LANGUAGES.TSONGA,
  LANGUAGES.NDEBELE,
  LANGUAGES.SWATI,
  LANGUAGES.SEPEDI
];

const EnhancedLanguageSelector: React.FC<EnhancedLanguageSelectorProps> = ({
  variant = 'standard',
  showLabel = true,
  showFlag = true,
  size = 'medium',
  position = 'top-right'
}) => {
  const theme = useTheme();
  const { language, setLanguage, languageNames } = useLanguage();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageSelect = (selectedLanguage: string) => {
    setLanguage(selectedLanguage);
    handleClose();
  };

  const open = Boolean(anchorEl);

  // Futuristic variant
  if (variant === 'futuristic') {
    return (
      <Box
        sx={{
          position: 'relative',
          zIndex: 1000,
        }}
      >
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Tooltip title="Select Language / Kies Taal / Khetha Ulimi">
            <IconButton
              onClick={handleClick}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              sx={{
                background: 'rgba(0, 212, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(0, 212, 255, 0.3)',
                borderRadius: '12px',
                color: '#00D4FF',
                transition: 'all 0.3s ease',
                boxShadow: isHovered 
                  ? '0 0 20px rgba(0, 212, 255, 0.4), 0 0 40px rgba(0, 212, 255, 0.2)'
                  : '0 4px 12px rgba(0, 0, 0, 0.1)',
                '&:hover': {
                  background: 'rgba(0, 212, 255, 0.2)',
                  borderColor: '#00D4FF',
                  transform: 'translateY(-2px)',
                },
              }}
            >
              <Language />
            </IconButton>
          </Tooltip>
        </motion.div>

        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          PaperProps={{
            sx: {
              background: 'rgba(15, 23, 42, 0.9)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(0, 212, 255, 0.3)',
              borderRadius: '16px',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.2)',
              mt: 1,
              minWidth: 280,
            },
          }}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          {/* South African Official Languages Header */}
          <Box sx={{ px: 2, py: 1, borderBottom: '1px solid rgba(0, 212, 255, 0.2)' }}>
            <Typography 
              variant="caption" 
              sx={{ 
                color: '#00D4FF', 
                fontWeight: 600,
                textTransform: 'uppercase',
                letterSpacing: 1
              }}
            >
              🇿🇦 South African Official Languages
            </Typography>
          </Box>

          {SA_OFFICIAL_LANGUAGES.map((lang) => (
            <MenuItem
              key={lang}
              onClick={() => handleLanguageSelect(lang)}
              sx={{
                color: 'white',
                py: 1.5,
                px: 2,
                '&:hover': {
                  background: 'rgba(0, 212, 255, 0.1)',
                },
                borderLeft: language === lang ? '3px solid #00D4FF' : '3px solid transparent',
              }}
            >
              <ListItemIcon className="enhanced-lang-item-icon">
                <span className="enhanced-lang-flag">{LANGUAGE_FLAGS[lang]}</span>
              </ListItemIcon>
              <ListItemText 
                primary={languageNames[lang]}
                sx={{ 
                  '& .MuiListItemText-primary': { 
                    fontWeight: language === lang ? 600 : 400,
                    color: language === lang ? '#00D4FF' : 'white'
                  }
                }}
              />
              {language === lang && (
                <Check sx={{ color: '#00D4FF', ml: 1 }} />
              )}
            </MenuItem>
          ))}

          {/* Additional Languages */}
          <Box sx={{ px: 2, py: 1, borderTop: '1px solid rgba(0, 212, 255, 0.2)' }}>
            <Typography 
              variant="caption" 
              sx={{ 
                color: 'rgba(255, 255, 255, 0.6)', 
                fontWeight: 500,
                textTransform: 'uppercase',
                letterSpacing: 1
              }}
            >
              Additional Languages
            </Typography>
          </Box>

          {[LANGUAGES.HINDI, LANGUAGES.PORTUGUESE].map((lang) => (
            <MenuItem
              key={lang}
              onClick={() => handleLanguageSelect(lang)}
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                py: 1,
                px: 2,
                '&:hover': {
                  background: 'rgba(124, 58, 237, 0.1)',
                },
                borderLeft: language === lang ? '3px solid #7C3AED' : '3px solid transparent',
              }}
            >
              <ListItemIcon className="enhanced-lang-item-icon">
                <span className="enhanced-lang-flag">{LANGUAGE_FLAGS[lang]}</span>
              </ListItemIcon>
              <ListItemText 
                primary={languageNames[lang]}
                sx={{ 
                  '& .MuiListItemText-primary': { 
                    fontWeight: language === lang ? 600 : 400,
                    color: language === lang ? '#7C3AED' : 'rgba(255, 255, 255, 0.8)'
                  }
                }}
              />
              {language === lang && (
                <Check sx={{ color: '#7C3AED', ml: 1 }} />
              )}
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }

  // Compact variant
  if (variant === 'compact') {
    return (
      <Chip
        icon={<Language />}
        label={showFlag ? `${LANGUAGE_FLAGS[language]} ${languageNames[language]}` : languageNames[language]}
        onClick={handleClick}
        size={size}
        sx={{
          background: alpha(theme.palette.primary.main, 0.1),
          color: theme.palette.primary.main,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
          '&:hover': {
            background: alpha(theme.palette.primary.main, 0.2),
          },
        }}
      />
    );
  }

  // Standard variant
  return (
    <FormControl size={size} sx={{ minWidth: 180 }}>
      {showLabel && (
        <Typography variant="caption" sx={{ mb: 1, color: 'text.secondary' }}>
          Language / Taal / Ulimi
        </Typography>
      )}
      <Select
        value={language}
        onChange={(e) => setLanguage(e.target.value)}
        displayEmpty
        sx={{
          '& .MuiSelect-select': {
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          },
        }}
      >
        {SA_OFFICIAL_LANGUAGES.map((lang) => (
          <MenuItem key={lang} value={lang}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {showFlag && <span>{LANGUAGE_FLAGS[lang]}</span>}
              <span>{languageNames[lang]}</span>
            </Box>
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default EnhancedLanguageSelector;
