/**
 * Types for the Reports module
 */

export interface Report {
  id: string;
  title: string;
  type: ReportType;
  module: ReportModule;
  date: string;
  status: ReportStatus;
  format: ReportFormat;
  size: string;
  author: string;
  description: string;
  downloadUrl?: string;
  thumbnail?: string;
  tags?: string[];
  viewCount?: number;
  lastModified?: string;
  isShared?: boolean;
  permissions?: ReportPermission[];
}

export type ReportType = 
  | 'financial' 
  | 'health' 
  | 'breeding' 
  | 'feed' 
  | 'compliance' 
  | 'inventory'
  | 'market'
  | 'performance'
  | 'custom';

export type ReportModule = 
  | 'animals'
  | 'health'
  | 'breeding'
  | 'feed-management'
  | 'financial'
  | 'compliance'
  | 'commercial'
  | 'reports';

export type ReportStatus = 
  | 'generated' 
  | 'pending' 
  | 'failed'
  | 'draft'
  | 'archived';

export type ReportFormat = 
  | 'pdf' 
  | 'excel' 
  | 'csv' 
  | 'json'
  | 'html';

export type ReportPermission = 
  | 'view' 
  | 'edit' 
  | 'delete' 
  | 'share';

export interface ReportFilter {
  module?: ReportModule;
  type?: ReportType;
  status?: ReportStatus;
  format?: ReportFormat;
  dateFrom?: string;
  dateTo?: string;
  author?: string;
  searchTerm?: string;
}

export interface ReportAnalysis {
  id: string;
  reportId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  insights: ReportInsight[];
  visualizations: ReportVisualization[];
  recommendations: string[];
}

export interface ReportInsight {
  id: string;
  title: string;
  description: string;
  importance: 'high' | 'medium' | 'low';
  metrics: {
    name: string;
    value: number | string;
    trend?: {
      value: number;
      isPositive: boolean;
    };
  }[];
}

export interface ReportVisualization {
  id: string;
  title: string;
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'radar' | 'table';
  data: any;
  config: any;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  module: ReportModule;
  type: ReportType;
  fields: string[];
  visualizations: {
    type: string;
    config: any;
  }[];
  isDefault: boolean;
  createdBy: string;
  createdAt: string;
}
