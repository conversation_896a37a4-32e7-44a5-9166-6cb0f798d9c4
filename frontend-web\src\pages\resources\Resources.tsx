import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import ResourcesDashboard from './ResourcesDashboard';
import ResourcesList from './ResourcesList';
import ResourceDetail from './ResourceDetail';
import ResourceForm from './ResourceForm';
import ResourceMaintenance from './ResourceMaintenance';
import ResourceUtilization from './ResourceUtilization';
import Guidelines from './Guidelines';
import Training from './Training';
import Support from './Support';
import Documentation from './Documentation';
import Downloads from './Downloads';
import GovernmentResources from './GovernmentResources';

const Resources: React.FC = () => {
  return (
    <Routes>
      <Route index element={<ResourcesDashboard />} />
      <Route path="list" element={<ResourcesList />} />
      <Route path="detail/:id" element={<ResourceDetail />} />
      <Route path="new" element={<ResourceForm />} />
      <Route path="edit/:id" element={<ResourceForm />} />
      <Route path="maintenance" element={<ResourceMaintenance />} />
      <Route path="utilization" element={<ResourceUtilization />} />
      <Route path="guidelines" element={<Guidelines />} />
      <Route path="training" element={<Training />} />
      <Route path="support" element={<Support />} />
      <Route path="documentation" element={<Documentation />} />
      <Route path="downloads" element={<Downloads />} />
      <Route path="government" element={<GovernmentResources />} />
      <Route path="*" element={
        <Box p={4}>
          <Typography variant="h4">Resources Module</Typography>
          <Typography variant="body1" mt={2}>
            Please select a specific resource view from the navigation.
          </Typography>
        </Box>
      } />
    </Routes>
  );
};

export default Resources;
