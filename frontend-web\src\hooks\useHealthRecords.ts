import { useState, useEffect, useCallback } from 'react';
import { useMockData } from '../utils/config';
import api from '../services/apiService';
import { mockHealthRecords, mockHealthStats, mockVaccinations } from '../mocks/healthData';
import { HealthRecord, Vaccination, HealthStats } from '../types/health';
import { useSnackbar } from '../contexts/SnackbarContext';

export const useHealthRecords = () => {
  const [healthRecords, setHealthRecords] = useState<HealthRecord[]>([]);
  const [vaccinations, setVaccinations] = useState<Vaccination[]>([]);
  const [stats, setStats] = useState<HealthStats>(mockHealthStats);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showSnackbar } = useSnackbar();

  const fetchHealthData = useCallback(async () => {
    try {
      setLoading(true);

      // Use API
      const [recordsResponse, vaccinationsResponse] = await Promise.all([
        api.get('/health/records'),
        api.get('/health/vaccinations')
      ]);

      setHealthRecords(recordsResponse.data);
      setVaccinations(vaccinationsResponse.data);
      updateStats(recordsResponse.data);

      showSnackbar('Health records loaded successfully', 'success');
    } catch (err) {
      setError('Failed to fetch health records');
      showSnackbar('Failed to load health records', 'error');
    } finally {
      setLoading(false);
    }
  }, [showSnackbar]);

  const updateStats = (records: HealthRecord[]) => {
    const newStats: HealthStats = {
      totalRecords: records.length,
      byType: {
        vaccination: records.filter(r => r.type === 'vaccination').length,
        treatment: records.filter(r => r.type === 'treatment').length,
        checkup: records.filter(r => r.type === 'checkup').length,
        surgery: records.filter(r => r.type === 'surgery').length,
        quarantine: records.filter(r => r.type === 'quarantine').length
      },
      byStatus: {
        completed: records.filter(r => r.status === 'completed').length,
        scheduled: records.filter(r => r.status === 'scheduled').length,
        requiresFollowup: records.filter(r => r.status === 'cancelled').length,
        cancelled: records.filter(r => r.status === 'cancelled').length
      },
      pendingCount: records.filter(r => r.status === 'scheduled').length
    };
    setStats(newStats);
  };

  useEffect(() => {
    fetchHealthData();
  }, [fetchHealthData]);

  const addHealthRecord = async (record: Partial<HealthRecord>) => {
    try {
      setLoading(true);
      if (useMockData) {
        // Generate a new ID for the mock record
        const newId = `HR${(healthRecords.length + 1).toString().padStart(3, '0')}`;
        const newRecord: HealthRecord = {
          id: newId,
          animalId: record.animalId || '',
          date: record.date || new Date().toISOString().split('T')[0],
          type: record.type || 'checkup',
          description: record.description || '',
          performedBy: record.performedBy || '',
          cost: record.cost || 0,
          notes: record.notes || '',
          status: record.status || 'scheduled'
        };

        // Add to the local state
        const updatedRecords = [...healthRecords, newRecord];
        setHealthRecords(updatedRecords);
        updateStats(updatedRecords);
        showSnackbar('Health record added successfully', 'success');
      } else {
        // Use API
        const response = await api.post('/health/records', record);
        setHealthRecords(prev => [...prev, response.data]);
        updateStats([...healthRecords, response.data]);
        showSnackbar('Health record added successfully', 'success');
      }
    } catch (err) {
      setError('Failed to add health record');
      showSnackbar('Failed to add health record', 'error');
    } finally {
      setLoading(false);
    }
  };

  const updateHealthRecord = async (id: string, record: Partial<HealthRecord>) => {
    try {
      setLoading(true);
      if (useMockData) {
        // Update in the local state
        const updatedRecords = healthRecords.map(r =>
          r.id === id ? { ...r, ...record } : r
        );
        setHealthRecords(updatedRecords);
        updateStats(updatedRecords);
        showSnackbar('Health record updated successfully', 'success');
      } else {
        // Use API
        const response = await api.put(`/health/records/${id}`, record);
        setHealthRecords(prev => prev.map(r => r.id === id ? response.data : r));
        updateStats(healthRecords.map(r => r.id === id ? response.data : r));
        showSnackbar('Health record updated successfully', 'success');
      }
    } catch (err) {
      setError('Failed to update health record');
      showSnackbar('Failed to update health record', 'error');
    } finally {
      setLoading(false);
    }
  };

  const deleteHealthRecord = async (id: string) => {
    try {
      setLoading(true);
      if (useMockData) {
        // Remove from the local state
        const updatedRecords = healthRecords.filter(r => r.id !== id);
        setHealthRecords(updatedRecords);
        updateStats(updatedRecords);
        showSnackbar('Health record deleted successfully', 'success');
      } else {
        // Use API
        await api.delete(`/health/records/${id}`);
        const updatedRecords = healthRecords.filter(r => r.id !== id);
        setHealthRecords(updatedRecords);
        updateStats(updatedRecords);
        showSnackbar('Health record deleted successfully', 'success');
      }
    } catch (err) {
      setError('Failed to delete health record');
      showSnackbar('Failed to delete health record', 'error');
    } finally {
      setLoading(false);
    }
  };

  return {
    healthRecords,
    vaccinations,
    stats,
    loading,
    error,
    fetchHealthData,
    addHealthRecord,
    updateHealthRecord,
    deleteHealthRecord
  };
};

