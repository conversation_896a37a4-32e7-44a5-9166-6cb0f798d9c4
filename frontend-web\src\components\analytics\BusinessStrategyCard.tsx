import React from 'react';
import { Box, Typography, Grid, useTheme, alpha, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import BusinessAnalyticsCard from './BusinessAnalyticsCard';
import BusinessAnalyticsButton from './BusinessAnalyticsButton';

interface BusinessStrategyProps {
  id: string;
  name: string;
  description: string;
  potentialValue: number;
  implementationTimeframe: string;
  implementationStrategy: string;
  feasibility: number;
  opportunityScore: number;
  tags: string[];
  category: string;
}

interface BusinessStrategyCardProps {
  strategy: BusinessStrategyProps;
  onImplement?: (strategy: BusinessStrategyProps) => void;
  loading?: boolean;
  delay?: number;
  icon?: React.ReactNode;
}

/**
 * A card component for displaying business strategies
 */
const BusinessStrategyCard: React.FC<BusinessStrategyCardProps> = ({
  strategy,
  onImplement,
  loading = false,
  delay = 0,
  icon
}) => {
  const theme = useTheme();

  const getFeasibilityColor = (feasibility: number) => {
    if (feasibility >= 0.7) return theme.palette.success.main;
    if (feasibility >= 0.4) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const feasibilityColor = getFeasibilityColor(strategy.feasibility);

  return (
    <BusinessAnalyticsCard
      title={strategy.name}
      subtitle={`Potential Value: R ${strategy.potentialValue.toLocaleString()}`}
      icon={icon}
      loading={loading}
      delay={delay}
      actionLabel="Implement Strategy"
      onAction={() => onImplement && onImplement(strategy)}
    >
      <Box sx={{ mt: 2 }}>
        <Typography variant="body1" sx={{ mb: 3 }}>
          {strategy.description}
        </Typography>

        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Implementation Timeframe
              </Typography>
              <Typography variant="body1" fontWeight="medium">
                {strategy.implementationTimeframe}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box>
              <Typography variant="body2" color="text.secondary">
                Feasibility
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="body1" fontWeight="medium" color={feasibilityColor}>
                  {(strategy.feasibility * 100).toFixed(0)}%
                </Typography>
                <Box
                  sx={{
                    ml: 1,
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: feasibilityColor
                  }}
                />
              </Box>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Implementation Strategy
          </Typography>
          <Typography variant="body2">
            {strategy.implementationStrategy}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Category
          </Typography>
          <Chip
            label={strategy.category}
            size="small"
            sx={{
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main,
              fontWeight: 'medium'
            }}
          />
        </Box>

        <Box>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Tags
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {strategy.tags.map((tag, index) => (
              <Chip
                key={index}
                label={tag}
                size="small"
                sx={{
                  backgroundColor: alpha(theme.palette.secondary.main, 0.1),
                  color: theme.palette.secondary.main
                }}
              />
            ))}
          </Box>
        </Box>
      </Box>
    </BusinessAnalyticsCard>
  );
};

export default BusinessStrategyCard;
