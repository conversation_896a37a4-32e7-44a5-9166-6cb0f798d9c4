import React, { ReactNode } from 'react';
import { Box, Paper, Typography, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { ArrowUpward, ArrowDownward } from '../../utils/iconImports';
import { getSafeElevation } from '../../utils/themeDefaults';

export interface SubModuleMetricsCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  color?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  moduleColor?: string;
}

/**
 * A standardized metrics card component for submodules
 */
const SubModuleMetricsCard: React.FC<SubModuleMetricsCardProps> = ({
  title,
  value,
  icon,
  color,
  trend,
  moduleColor
}) => {
  const theme = useTheme();
  
  // Use provided color, module color, or default to primary
  const cardColor = color || moduleColor || theme.palette.primary.main;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        elevation={getSafeElevation(theme, 1)}
        sx={{
          p: 3,
          borderRadius: 2,
          borderLeft: `4px solid ${cardColor}`,
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
          backdropFilter: 'blur(10px)',
          '&:hover': {
            boxShadow: `0 8px 25px ${alpha(cardColor, 0.15)}`,
            transform: 'translateY(-4px)',
            borderLeft: `4px solid ${alpha(cardColor, 0.8)}`,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)}, ${alpha(cardColor, 0.05)})`,
          },
          transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {icon && (
            <Box 
              sx={{ 
                mr: 2, 
                color: cardColor,
                backgroundColor: alpha(cardColor, 0.1),
                p: 1,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              {icon}
            </Box>
          )}
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {title}
            </Typography>
            <Typography variant="h4" fontWeight="bold" color="text.primary">
              {value}
            </Typography>
            {trend && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {trend.isPositive ? (
                  <ArrowUpward fontSize="small" sx={{ color: theme.palette.success.main }} />
                ) : (
                  <ArrowDownward fontSize="small" sx={{ color: theme.palette.error.main }} />
                )}
                <Typography 
                  variant="body2" 
                  sx={{ 
                    ml: 0.5, 
                    color: trend.isPositive ? theme.palette.success.main : theme.palette.error.main 
                  }}
                >
                  {trend.value}% {trend.label || ''}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default SubModuleMetricsCard;
