import React from 'react';

interface SimpleButtonProps {
  variant?: 'contained' | 'outlined' | 'text';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  onClick?: () => void;
  children: React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  disabled?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: React.CSSProperties;
  className?: string;
}

/**
 * A simple button component that doesn't rely on MUI
 * This helps prevent "Cannot read properties of undefined (reading 'dark')" errors
 */
const SimpleButton: React.FC<SimpleButtonProps> = ({
  variant = 'contained',
  color = 'primary',
  onClick,
  children,
  startIcon,
  endIcon,
  disabled = false,
  fullWidth = false,
  size = 'medium',
  style,
  className
}) => {
  // Define color mappings
  const colorMap: Record<string, { main: string, dark: string, light: string, contrastText: string }> = {
    primary: {
      main: '#3AA99F',
      dark: '#2A8A82',
      light: '#4FBEB4',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#38B2AC',
      dark: '#2C8A84',
      light: '#4FD1CB',
      contrastText: '#ffffff'
    },
    error: {
      main: '#ef4444',
      dark: '#dc2626',
      light: '#f87171',
      contrastText: '#ffffff'
    },
    warning: {
      main: '#f59e0b',
      dark: '#d97706',
      light: '#fbbf24',
      contrastText: '#ffffff'
    },
    info: {
      main: '#3b82f6',
      dark: '#2563eb',
      light: '#60a5fa',
      contrastText: '#ffffff'
    },
    success: {
      main: '#10b981',
      dark: '#059669',
      light: '#34d399',
      contrastText: '#ffffff'
    }
  };
  
  // Get color values
  const colorValues = colorMap[color] || colorMap.primary;
  
  // Define base styles
  const baseStyles: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '8px',
    cursor: disabled ? 'not-allowed' : 'pointer',
    fontWeight: 600,
    transition: 'all 0.2s ease-in-out',
    opacity: disabled ? 0.5 : 1,
    width: fullWidth ? '100%' : 'auto',
    textTransform: 'none',
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontSize: size === 'small' ? '0.8125rem' : size === 'large' ? '0.9375rem' : '0.875rem',
    padding: size === 'small' ? '4px 10px' : size === 'large' ? '12px 22px' : '8px 16px',
    minHeight: size === 'small' ? '32px' : size === 'large' ? '56px' : '48px',
    minWidth: size === 'small' ? '64px' : size === 'large' ? '112px' : '96px',
    boxSizing: 'border-box',
    border: 'none',
    outline: 'none',
    userSelect: 'none',
    textDecoration: 'none',
    position: 'relative',
    overflow: 'hidden'
  };
  
  // Apply variant-specific styles
  if (variant === 'contained') {
    baseStyles.backgroundColor = colorValues.main;
    baseStyles.color = colorValues.contrastText;
    baseStyles.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
  } else if (variant === 'outlined') {
    baseStyles.backgroundColor = 'transparent';
    baseStyles.color = colorValues.main;
    baseStyles.border = `1px solid ${colorValues.main}`;
  } else if (variant === 'text') {
    baseStyles.backgroundColor = 'transparent';
    baseStyles.color = colorValues.main;
    baseStyles.boxShadow = 'none';
  }
  
  // Combine with custom styles
  const combinedStyles: React.CSSProperties = {
    ...baseStyles,
    ...style
  };
  
  // Handle hover effect
  const [isHovered, setIsHovered] = React.useState(false);
  
  // Apply hover styles
  if (isHovered && !disabled) {
    if (variant === 'contained') {
      combinedStyles.backgroundColor = colorValues.dark;
      combinedStyles.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
      combinedStyles.transform = 'translateY(-2px)';
    } else if (variant === 'outlined') {
      combinedStyles.backgroundColor = `${colorValues.main}20`; // 20% opacity
    } else if (variant === 'text') {
      combinedStyles.backgroundColor = `${colorValues.main}10`; // 10% opacity
    }
  }
  
  return (
    <button
      style={combinedStyles}
      onClick={disabled ? undefined : onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      disabled={disabled}
      className={className}
    >
      {startIcon && <span style={{ marginRight: '8px', display: 'flex', alignItems: 'center' }}>{startIcon}</span>}
      {children}
      {endIcon && <span style={{ marginLeft: '8px', display: 'flex', alignItems: 'center' }}>{endIcon}</span>}
    </button>
  );
};

export default SimpleButton;
