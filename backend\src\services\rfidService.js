const { pool } = require('../config/db');
const NotificationService = require('../utils/notificationService');
const logger = require('../utils/logger');

class RFIDService {
    static async processReaderData(readerData) {
        const { readerId, readings } = readerData;
        
        try {
            for (const reading of readings) {
                const { tagId, signalStrength, batteryLevel } = reading;
                
                // Update animal location and battery level
                await pool.request()
                    .input('tagId', tagId)
                    .input('readerId', readerId)
                    .input('batteryLevel', batteryLevel)
                    .execute('dbo.sp_UpdateAnimalRFIDLocation');

                // Log location history
                await pool.request()
                    .input('tagId', tagId)
                    .input('readerId', readerId)
                    .input('signalStrength', signalStrength)
                    .input('batteryLevel', batteryLevel)
                    .execute('dbo.sp_LogRFIDLocation');

                // Check battery level and send alerts if low
                if (batteryLevel < 20) {
                    await this.sendLowBatteryAlert(tagId, batteryLevel);
                }
            }
        } catch (error) {
            logger.error('Error processing RFID data:', error);
            throw error;
        }
    }

    static async registerBatchTags(tags) {
        try {
            for (const tag of tags) {
                await pool.request()
                    .input('tagId', tag.tagId)
                    .input('animalId', tag.animalId)
                    .execute('dbo.sp_RegisterRFIDTag');
            }
        } catch (error) {
            logger.error('Error registering RFID tags:', error);
            throw error;
        }
    }

    static async getLocationHistory(animalId, startDate, endDate) {
        try {
            const result = await pool.request()
                .input('animalId', animalId)
                .input('startDate', startDate)
                .input('endDate', endDate)
                .execute('dbo.sp_GetAnimalLocationHistory');
            
            return result.recordset;
        } catch (error) {
            logger.error('Error fetching location history:', error);
            throw error;
        }
    }

    static async sendLowBatteryAlert(tagId, batteryLevel) {
        const notification = {
            title: 'Low RFID Battery Alert',
            message: `RFID tag ${tagId} has low battery (${batteryLevel}%)`,
            type: 'warning',
            priority: 'high'
        };
        
        await NotificationService.sendNotification(notification);
    }
}

module.exports = RFIDService;