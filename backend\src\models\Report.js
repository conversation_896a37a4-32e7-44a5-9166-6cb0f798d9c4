/**
 * Report Model
 * 
 * Stores information about generated reports in the system.
 */

const mongoose = require('mongoose');

const ReportSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  title: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: [
      'livestock',
      'financial',
      'breeding',
      'health',
      'feeding',
      'market',
      'compliance',
      'custom'
    ],
    required: true
  },
  module: {
    type: String,
    enum: [
      'animals',
      'health',
      'breeding',
      'feed-management',
      'financial',
      'compliance',
      'commercial',
      'reports'
    ],
    required: true
  },
  date: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['generated', 'pending', 'failed'],
    default: 'generated'
  },
  format: {
    type: String,
    enum: ['pdf', 'excel', 'csv', 'html', 'json'],
    default: 'pdf'
  },
  size: {
    type: String
  },
  author: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  downloadUrl: {
    type: String
  },
  thumbnail: {
    type: String
  },
  tags: [{
    type: String
  }],
  viewCount: {
    type: Number,
    default: 0
  },
  downloadCount: {
    type: Number,
    default: 0
  },
  lastModified: {
    type: Date,
    default: Date.now
  },
  isShared: {
    type: Boolean,
    default: false
  },
  permissions: [{
    userId: String,
    access: {
      type: String,
      enum: ['view', 'edit', 'delete'],
      default: 'view'
    }
  }],
  parameters: {
    type: Object
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Pre-save hook to update the updatedAt field
ReportSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create the model
const Report = mongoose.model('Report', ReportSchema);

module.exports = Report;
