import React, { useState } from 'react';
import { Box, Typography, alpha } from '@mui/material';
import { useTheme } from '@mui/material/styles';

interface CustomAnalyticsButtonProps {
  label: string;
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  icon?: React.ReactNode;
  variant?: 'contained' | 'outlined' | 'text';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
  disabled?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Custom button component for analytics module that doesn't rely on <PERSON><PERSON>ton
 * This avoids the "Cannot read properties of undefined (reading 'dark')" error
 */
const CustomAnalyticsButton: React.FC<CustomAnalyticsButtonProps> = ({
  label,
  onClick,
  icon,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  fullWidth = false,
  disabled = false,
  startIcon,
  endIcon,
  className,
  style
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [isActive, setIsActive] = useState(false);

  // Define color values based on theme
  const getColorValues = () => {
    const colorMap: Record<string, { main: string; dark: string; light: string; contrastText: string }> = {
      primary: {
        main: theme.palette.primary.main || '#3AA99F',
        dark: theme.palette.primary.main || '#2A8A82', // Fallback to main if dark is undefined
        light: theme.palette.primary.light || '#4FBEB4',
        contrastText: theme.palette.primary.contrastText || '#ffffff'
      },
      secondary: {
        main: theme.palette.secondary.main || '#38B2AC',
        dark: theme.palette.secondary.main || '#2C8A84',
        light: theme.palette.secondary.light || '#4FD1CB',
        contrastText: theme.palette.secondary.contrastText || '#ffffff'
      },
      success: {
        main: theme.palette.success.main || '#10b981',
        dark: theme.palette.success.main || '#059669',
        light: theme.palette.success.light || '#34d399',
        contrastText: '#ffffff'
      },
      error: {
        main: theme.palette.error.main || '#ef4444',
        dark: theme.palette.error.main || '#dc2626',
        light: theme.palette.error.light || '#f87171',
        contrastText: '#ffffff'
      },
      warning: {
        main: theme.palette.warning.main || '#f59e0b',
        dark: theme.palette.warning.main || '#d97706',
        light: theme.palette.warning.light || '#fbbf24',
        contrastText: '#ffffff'
      },
      info: {
        main: theme.palette.info.main || '#3b82f6',
        dark: theme.palette.info.main || '#2563eb',
        light: theme.palette.info.light || '#60a5fa',
        contrastText: '#ffffff'
      }
    };

    return colorMap[color] || colorMap.primary;
  };

  const colorValues = getColorValues();

  // Get base styles based on variant
  const getBaseStyles = (): React.CSSProperties => {
    const sizeStyles = {
      small: {
        padding: '6px 12px',
        fontSize: '0.8125rem',
        borderRadius: '4px',
        minHeight: '32px'
      },
      medium: {
        padding: '8px 16px',
        fontSize: '0.875rem',
        borderRadius: '4px',
        minHeight: '40px'
      },
      large: {
        padding: '10px 22px',
        fontSize: '0.9375rem',
        borderRadius: '4px',
        minHeight: '48px'
      }
    };

    const baseStyle: React.CSSProperties = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      boxSizing: 'border-box',
      outline: 0,
      margin: 0,
      cursor: disabled ? 'not-allowed' : 'pointer',
      userSelect: 'none',
      verticalAlign: 'middle',
      textDecoration: 'none',
      fontWeight: 500,
      fontFamily: theme.typography.fontFamily,
      lineHeight: 1.75,
      letterSpacing: '0.02857em',
      textTransform: 'uppercase',
      minWidth: '64px',
      transition: 'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms',
      width: fullWidth ? '100%' : 'auto',
      opacity: disabled ? 0.5 : 1,
      ...sizeStyles[size]
    };

    if (variant === 'contained') {
      return {
        ...baseStyle,
        color: colorValues.contrastText,
        backgroundColor: colorValues.main,
        boxShadow: '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)'
      };
    } else if (variant === 'outlined') {
      return {
        ...baseStyle,
        color: colorValues.main,
        backgroundColor: 'transparent',
        border: `1px solid ${colorValues.main}`
      };
    } else {
      return {
        ...baseStyle,
        color: colorValues.main,
        backgroundColor: 'transparent',
        padding: sizeStyles[size].padding
      };
    }
  };

  // Get hover styles
  const getHoverStyles = (): React.CSSProperties => {
    if (disabled) return {};

    if (variant === 'contained') {
      return {
        backgroundColor: colorValues.dark,
        boxShadow: '0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12)'
      };
    } else if (variant === 'outlined') {
      return {
        backgroundColor: alpha(colorValues.main, 0.04),
        border: `1px solid ${colorValues.main}`
      };
    } else {
      return {
        backgroundColor: alpha(colorValues.main, 0.04)
      };
    }
  };

  // Get active styles
  const getActiveStyles = (): React.CSSProperties => {
    if (disabled) return {};

    if (variant === 'contained') {
      return {
        boxShadow: '0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12)'
      };
    } else if (variant === 'outlined') {
      return {
        backgroundColor: alpha(colorValues.main, 0.12)
      };
    } else {
      return {
        backgroundColor: alpha(colorValues.main, 0.12)
      };
    }
  };

  const baseStyles = getBaseStyles();
  const hoverStyles = isHovered ? getHoverStyles() : {};
  const activeStyles = isActive ? getActiveStyles() : {};

  const combinedStyles = {
    ...baseStyles,
    ...hoverStyles,
    ...activeStyles,
    ...style
  };

  return (
    <Box
      className={`custom-analytics-button ${className || ''}`}
      sx={combinedStyles}
      onClick={disabled ? undefined : onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsActive(true)}
      onMouseUp={() => setIsActive(false)}
      role="button"
      tabIndex={disabled ? -1 : 0}
    >
      {startIcon && <Box sx={{ display: 'inherit', mr: 1 }}>{startIcon}</Box>}
      {icon && <Box sx={{ display: 'inherit', mr: 1 }}>{icon}</Box>}
      <Typography
        variant="button"
        component="span"
        sx={{
          fontSize: 'inherit',
          fontWeight: 'inherit',
          letterSpacing: 'inherit'
        }}
      >
        {label}
      </Typography>
      {endIcon && <Box sx={{ display: 'inherit', ml: 1 }}>{endIcon}</Box>}
    </Box>
  );
};

export default CustomAnalyticsButton;
