/**
 * MongoDB Connection Test Script
 * 
 * This script tests the connection to MongoDB Atlas and provides detailed error information
 * to help diagnose connection issues.
 * Run with: node test-mongodb-connection.js YOUR_PASSWORD
 */

const { MongoClient, ServerApiVersion } = require('mongodb');

// Get the password from command line arguments
const password = process.argv[2];

if (!password) {
  console.error('Error: No password provided');
  console.error('Usage: node test-mongodb-connection.js YOUR_PASSWORD');
  process.exit(1);
}

// MongoDB connection string with password
const MONGODB_URI = `mongodb+srv://luckyrakgama:${password}@cluster0.yvkuood.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0`;
const DB_NAME = 'ampd_livestock';

// Connection options
const options = {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  maxPoolSize: 50,
  minPoolSize: 5,
  retryWrites: true,
  retryReads: true
};

// Create a new MongoClient
const client = new MongoClient(MONGODB_URI, options);

// Redact sensitive information from URI for logging
const redactedUri = MONGODB_URI.replace(/:\/\/([^:]+):([^@]+)@/, '://$1:****@');
console.log('Connecting to MongoDB...');
console.log(`URI: ${redactedUri}`);
console.log(`Database: ${DB_NAME}`);

async function run() {
  try {
    // Connect to the MongoDB server
    await client.connect();
    console.log('✅ Connected to MongoDB server successfully!');

    // Get the database
    const db = client.db(DB_NAME);
    console.log(`✅ Connected to database: ${db.databaseName}`);

    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    console.log('✅ Ping command successful!');

    // List collections
    const collections = await db.listCollections().toArray();
    console.log(`✅ Found ${collections.length} collections in database`);
    
    // Log collection names
    if (collections.length > 0) {
      console.log('Collections:');
      for (const collection of collections) {
        const count = await db.collection(collection.name).countDocuments();
        console.log(`- ${collection.name}: ${count} documents`);
      }
    } else {
      console.log('No collections found in the database');
    }

    console.log('✅ MongoDB connection test completed successfully!');
  } catch (error) {
    console.error('❌ MongoDB connection test failed!');
    
    // Check for common connection errors
    if (error.name === 'MongoNetworkError') {
      console.error('Network error connecting to MongoDB. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Connection string is correct');
      console.error('- Network allows connection to MongoDB port');
    } else if (error.name === 'MongoServerSelectionError') {
      console.error('Server selection error. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Authentication credentials are correct');
      console.error('- MongoDB server is accessible from this network');
    } else if (error.message && error.message.includes('timeout')) {
      console.error('Connection timeout. Please check:');
      console.error('- MongoDB server is running');
      console.error('- Network latency is not too high');
      console.error('- Firewall settings allow connection');
    } else if (error.message && error.message.includes('authentication')) {
      console.error('Authentication failed. Please check:');
      console.error('- Username and password are correct');
      console.error('- User has appropriate permissions');
      console.error('- Authentication database is correct');
    }
    
    // Log the original error for debugging
    console.error('Original error:', error);
  } finally {
    // Close the connection
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the test
run().catch(console.error);
