/**
 * AgriIntel Comprehensive Theme System
 * Professional livestock management branding with gradient themes
 */

/* ===== ROOT VARIABLES ===== */
:root {
  /* AgriIntel Brand Colors */
  --agri-primary: #2E7D32;
  --agri-primary-light: #4CAF50;
  --agri-primary-dark: #1B5E20;
  --agri-secondary: #FF8F00;
  --agri-secondary-light: #FFB74D;
  --agri-secondary-dark: #E65100;
  
  /* Gradient Themes */
  --gradient-nature: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #81C784 100%);
  --gradient-earth: linear-gradient(135deg, #8D6E63 0%, #A1887F 50%, #BCAAA4 100%);
  --gradient-sky: linear-gradient(135deg, #1976D2 0%, #42A5F5 50%, #90CAF9 100%);
  --gradient-sunset: linear-gradient(135deg, #FF8F00 0%, #FFB74D 50%, #FFCC02 100%);
  --gradient-forest: linear-gradient(135deg, #388E3C 0%, #66BB6A 50%, #A5D6A7 100%);
  
  /* Glass Morphism Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);
  
  /* Typography */
  --font-primary: 'Inter', 'Roboto', sans-serif;
  --font-heading: 'Poppins', 'Inter', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* ===== GLOBAL STYLES ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  background: var(--gradient-nature);
  background-attachment: fixed;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  color: #333;
  line-height: 1.6;
}

/* Remove white blur backgrounds */
.MuiBackdrop-root {
  background-color: rgba(0, 0, 0, 0.5) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  backdrop-filter: blur(4px) !important;
}

/* Fix white overlay issues */
[style*="rgba(255, 255, 255"] {
  background: transparent !important;
}

/* ===== AGRIINTEL BRANDING ===== */
.agri-brand {
  font-family: var(--font-heading);
  font-weight: 700;
  background: var(--gradient-nature);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

.agri-brand-large {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.agri-brand-medium {
  font-size: 1.8rem;
  margin-bottom: var(--spacing-sm);
}

.agri-brand-small {
  font-size: 1.2rem;
}

.agri-slogan {
  font-style: italic;
  color: var(--agri-primary-dark);
  text-align: center;
  margin-top: var(--spacing-sm);
  font-size: 0.9rem;
}

/* ===== GRADIENT CARDS ===== */
.gradient-card {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-lg);
  margin: var(--spacing-md);
  transition: all 0.3s ease;
}

.gradient-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.gradient-card-nature {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);
  border-left: 4px solid var(--agri-primary);
}

.gradient-card-earth {
  background: linear-gradient(135deg, rgba(141, 110, 99, 0.1) 0%, rgba(161, 136, 127, 0.1) 100%);
  border-left: 4px solid #8D6E63;
}

.gradient-card-sky {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(66, 165, 245, 0.1) 100%);
  border-left: 4px solid #1976D2;
}

.gradient-card-sunset {
  background: linear-gradient(135deg, rgba(255, 143, 0, 0.1) 0%, rgba(255, 183, 77, 0.1) 100%);
  border-left: 4px solid var(--agri-secondary);
}

/* ===== INTERACTIVE ELEMENTS ===== */
.interactive-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.interactive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.interactive-card:hover::before {
  left: 100%;
}

/* ===== BUTTONS ===== */
.agri-button {
  background: var(--gradient-nature);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-family: var(--font-primary);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.agri-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.agri-button-secondary {
  background: var(--gradient-sunset);
}

.agri-button-outline {
  background: transparent;
  border: 2px solid var(--agri-primary);
  color: var(--agri-primary);
}

.agri-button-outline:hover {
  background: var(--agri-primary);
  color: white;
}

/* ===== NAVIGATION ===== */
.agri-nav {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-md);
}

.agri-sidebar {
  background: linear-gradient(180deg, rgba(46, 125, 50, 0.95) 0%, rgba(27, 94, 32, 0.95) 100%);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-right: 1px solid var(--glass-border);
}

/* ===== FORMS ===== */
.agri-form {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.agri-input {
  border: 2px solid rgba(46, 125, 50, 0.3);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: var(--font-primary);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.agri-input:focus {
  border-color: var(--agri-primary);
  box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
  outline: none;
}

/* ===== TABLES ===== */
.agri-table {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.agri-table th {
  background: var(--gradient-nature);
  color: white;
  padding: var(--spacing-md);
  font-weight: 600;
}

.agri-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid rgba(46, 125, 50, 0.1);
}

.agri-table tr:hover {
  background: rgba(46, 125, 50, 0.05);
}

/* ===== LIVESTOCK IMAGERY BACKGROUNDS ===== */
.livestock-bg-cattle {
  background: linear-gradient(135deg, rgba(46, 125, 50, 0.9), rgba(27, 94, 32, 0.9));
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.livestock-bg-farm {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.9), rgba(46, 125, 50, 0.9));
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.livestock-bg-pasture {
  background: linear-gradient(135deg, rgba(129, 199, 132, 0.9), rgba(76, 175, 80, 0.9));
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .agri-brand-large {
    font-size: 2rem;
  }
  
  .agri-brand-medium {
    font-size: 1.5rem;
  }
  
  .gradient-card {
    margin: var(--spacing-sm);
    padding: var(--spacing-md);
  }
  
  .agri-form {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .agri-brand-large {
    font-size: 1.8rem;
  }
  
  .gradient-card {
    margin: var(--spacing-xs);
    padding: var(--spacing-sm);
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
  background: var(--gradient-nature);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bg-glass {
  background: var(--glass-bg);
  -webkit-backdrop-filter: var(--glass-backdrop);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.shadow-agri {
  box-shadow: var(--shadow-lg);
}

.rounded-agri {
  border-radius: var(--radius-lg);
}
