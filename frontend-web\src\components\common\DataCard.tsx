import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Divider,
  Skeleton,
  useTheme,
  alpha,
  CardProps
} from '@mui/material';
import { motion } from 'framer-motion';
import StatusIndicator, { StatusType } from './StatusIndicator';
import { MetricStatus } from '../../constants/routes';
import { useThemeContext } from '../../contexts/ThemeContext';

export interface DataCardProps extends Omit<CardProps, 'children'> {
  title: string;
  value: string | number | React.ReactNode;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    label?: string;
    direction: 'up' | 'down' | 'neutral';
  };
  status?: StatusType;
  loading?: boolean;
  footer?: React.ReactNode;
  accentColor?: string;
  valuePrefix?: string;
  valueSuffix?: string;
  delay?: number;
  variant?: 'default' | 'outlined' | 'elevation';
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  change?: number; // For backward compatibility
  changeLabel?: string; // For backward compatibility
  bgClass?: string; // For backward compatibility
  metricStatus?: MetricStatus; // For backward compatibility
}

const DataCard: React.FC<DataCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  status,
  loading = false,
  footer,
  accentColor,
  valuePrefix = '',
  valueSuffix = '',
  delay = 0,
  variant = 'default',
  size = 'medium',
  onClick,
  // Handle backward compatibility
  change,
  changeLabel = 'from last period',
  bgClass,
  metricStatus,
  ...cardProps
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Convert legacy props to new format if provided
  const legacyTrend = change !== undefined ? {
    value: Math.abs(change),
    label: changeLabel,
    direction: change > 0 ? 'up' : 'down'
  } : undefined;

  const finalTrend = trend || legacyTrend;

  // Convert legacy status to new format
  const getLegacyStatus = (): StatusType | undefined => {
    if (!metricStatus) return undefined;

    switch (metricStatus) {
      case 'positive': return 'success';
      case 'negative': return 'error';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  const finalStatus = status || getLegacyStatus();

  // Card style variants
  const cardVariants: Record<string, any> = {
    default: {
      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
      border: 'none',
      backgroundColor: accentColor ? alpha(accentColor, 0.03) : alpha(theme.palette.primary.main, 0.03),
    },
    outlined: {
      boxShadow: 'none',
      border: `1px solid ${alpha(theme.palette.divider, 0.8)}`,
      backgroundColor: accentColor ? alpha(accentColor, 0.05) : alpha(theme.palette.primary.main, 0.05),
    },
    elevation: {
      boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
      border: 'none',
      backgroundColor: accentColor ? alpha(accentColor, 0.02) : alpha(theme.palette.primary.main, 0.02),
    },
  };

  // Size variants
  const sizeVariants = {
    small: {
      padding: 2,
      titleSize: '0.875rem',
      valueSize: '1.5rem',
      subtitleSize: '0.75rem',
      iconSize: '2rem',
    },
    medium: {
      padding: 3,
      titleSize: '1rem',
      valueSize: '2rem',
      subtitleSize: '0.875rem',
      iconSize: '2.5rem',
    },
    large: {
      padding: 4,
      titleSize: '1.25rem',
      valueSize: '2.5rem',
      subtitleSize: '1rem',
      iconSize: '3rem',
    },
  };

  // Get trend color
  const getTrendColor = () => {
    if (!finalTrend) return theme.palette.text.secondary;

    switch (finalTrend.direction) {
      case 'up':
        return themeColor.primary; // Use theme color instead of success
      case 'down':
        return theme.palette.error.main;
      case 'neutral':
      default:
        return theme.palette.info.main;
    }
  };

  // Get trend icon
  const getTrendIcon = () => {
    if (!finalTrend) return null;

    switch (finalTrend.direction) {
      case 'up':
        return '↑';
      case 'down':
        return '↓';
      case 'neutral':
      default:
        return '→';
    }
  };

  // Render loading state
  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay }}
      >
        <Card
          {...cardProps}
          sx={{
            borderRadius: '12px',
            overflow: 'hidden',
            position: 'relative',
            cursor: onClick ? 'pointer' : 'default',
            ...cardVariants[variant],
            ...cardProps.sx
          }}
        >
          <CardContent sx={{ p: sizeVariants[size].padding }}>
            <Box display="flex" justifyContent="space-between" alignItems="flex-start">
              <Box>
                <Skeleton variant="text" width={100} height={24} />
                <Skeleton variant="text" width={120} height={40} sx={{ mt: 1 }} />
                {subtitle && <Skeleton variant="text" width={80} height={20} sx={{ mt: 1 }} />}
              </Box>
              {icon && <Skeleton variant="circular" width={40} height={40} />}
            </Box>
            {footer && (
              <>
                <Divider sx={{ my: 2 }} />
                <Skeleton variant="text" width="100%" height={24} />
              </>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
      whileHover={onClick ? { y: -5 } : undefined}
    >
      <Card
        {...cardProps}
        className={bgClass} // For backward compatibility
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          transition: 'all 0.3s ease',
          cursor: onClick ? 'pointer' : 'default',
          '&:hover': onClick ? {
            boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
          } : {},
          '&::before': accentColor ? {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: accentColor,
            zIndex: 1
          } : {},
          ...cardVariants[variant],
          ...cardProps.sx
        }}
        onClick={onClick}
      >
        <CardContent sx={{ p: sizeVariants[size].padding }}>
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box>
              <Box display="flex" alignItems="center" mb={1}>
                <Typography
                  variant="subtitle1"
                  fontWeight="medium"
                  sx={{
                    fontSize: sizeVariants[size].titleSize,
                    color: theme.palette.text.secondary
                  }}
                >
                  {title}
                </Typography>

                {finalStatus && (
                  <Box ml={1}>
                    <StatusIndicator
                      status={finalStatus}
                      size="small"
                      iconOnly
                    />
                  </Box>
                )}
              </Box>

              <Typography
                variant="h4"
                fontWeight="bold"
                sx={{
                  fontSize: sizeVariants[size].valueSize,
                  lineHeight: 1.2,
                  letterSpacing: '-0.5px',
                  color: accentColor || theme.palette.text.primary
                }}
              >
                {valuePrefix}{value}{valueSuffix}
              </Typography>

              {(subtitle || finalTrend) && (
                <Box display="flex" alignItems="center" mt={0.5}>
                  {subtitle && (
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: sizeVariants[size].subtitleSize,
                        color: theme.palette.text.secondary
                      }}
                    >
                      {subtitle}
                    </Typography>
                  )}

                  {finalTrend && (
                    <Box
                      display="flex"
                      alignItems="center"
                      ml={subtitle ? 1 : 0}
                      sx={{
                        color: getTrendColor(),
                        fontSize: sizeVariants[size].subtitleSize,
                        fontWeight: 'medium'
                      }}
                    >
                      <Box component="span" sx={{ mr: '2px' }}>{getTrendIcon()}</Box>
                      <Box component="span">{finalTrend.value}%</Box>
                      {finalTrend.label && (
                        <Typography
                          component="span"
                          variant="body2"
                          sx={{
                            ml: 0.5,
                            color: theme.palette.text.secondary,
                            fontSize: 'inherit'
                          }}
                        >
                          {finalTrend.label}
                        </Typography>
                      )}
                    </Box>
                  )}
                </Box>
              )}
            </Box>

            {icon && (
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '12px',
                  backgroundColor: alpha(accentColor || themeColor.primary, 0.1),
                  color: accentColor || themeColor.primary,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: sizeVariants[size].iconSize,
                }}
              >
                {icon}
              </Box>
            )}
          </Box>

          {footer && (
            <>
              <Divider sx={{ my: 2 }} />
              <Box>{footer}</Box>
            </>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default DataCard;
