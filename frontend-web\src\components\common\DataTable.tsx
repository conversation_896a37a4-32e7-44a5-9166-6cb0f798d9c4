/**
 * Enhanced Data Table Component
 *
 * This component provides a consistent data table interface
 * that can be used across all modules in the application.
 */

import React, { useState, useEffect } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, TablePagination, TableSortLabel, TextField, InputAdornment, Box, Typography, Chip, Tooltip, alpha, useTheme, CircularProgress, Alert } from '@mui/material';
import {
  Visibility, Edit, Delete, Search, FilterList, Refresh,
  Download, MoreVert
} from '../../utils/iconImports';
import StatusBadge from './StatusBadge';
import { motion } from 'framer-motion';
import { useTranslation } from '../../hooks/useTranslation';

// Define column type
export interface Column<T> {
  id: keyof T | 'actions';
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  format?: (value: any, row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  hidden?: boolean;
}

// Define props
interface DataTableProps<T> {
  title?: string;
  subtitle?: string;
  columns: Column<T>[];
  rows: T[];
  keyField?: keyof T;
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onExport?: () => void;
  onRowClick?: (row: T) => void;
  onView?: (row: T) => void;
  onEdit?: (row: T) => void;
  onDelete?: (id: string) => void;
  showActions?: boolean;
  searchable?: boolean;
  searchFields?: (keyof T)[];
  filterable?: boolean;
  filters?: React.ReactNode;
  pagination?: boolean;
  initialRowsPerPage?: number;
  rowsPerPageOptions?: number[];
  emptyMessage?: string;
  className?: string;
  showHeader?: boolean;
  dense?: boolean;
  getRowClassName?: (row: T) => string;
  highlightedRow?: string | number;
  actions?: {
    label: string;
    icon: React.ReactNode;
    onClick: (row: T) => void;
  }[];
}

function DataTable<T extends Record<string, any>>(
  {
    title,
    subtitle,
    columns,
    rows,
    keyField = 'id' as keyof T,
    loading = false,
    error = null,
    onRefresh,
    onExport,
    onRowClick,
    onView,
    onEdit,
    onDelete,
    showActions = true,
    searchable = true,
    searchFields = [],
    filterable = false,
    filters,
    pagination = true,
    initialRowsPerPage = 10,
    rowsPerPageOptions = [5, 10, 25, 50],
    emptyMessage = 'No data available',
    className,
    showHeader = true,
    dense = false,
    getRowClassName,
    highlightedRow,
    actions = []
  }: DataTableProps<T>
) {
  const theme = useTheme();
  const { translate } = useTranslation();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [orderBy, setOrderBy] = useState<string | null>(null);
  const [order, setOrder] = useState<'asc' | 'desc'>('asc');
  const [filteredRows, setFilteredRows] = useState<T[]>(rows);

  // Visible columns (excluding hidden ones)
  const visibleColumns = columns.filter(column => !column.hidden);

  // Update filtered rows when rows, search term, or sort changes
  useEffect(() => {
    let result = [...rows];

    // Apply search
    if (searchTerm && searchFields.length > 0) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      result = result.filter(row => {
        return searchFields.some(field => {
          const value = row[field];
          if (value === null || value === undefined) return false;
          return String(value).toLowerCase().includes(lowerCaseSearchTerm);
        });
      });
    }

    // Apply sort
    if (orderBy) {
      result.sort((a, b) => {
        const aValue = a[orderBy];
        const bValue = b[orderBy];

        if (aValue === bValue) return 0;
        if (aValue === null || aValue === undefined) return order === 'asc' ? -1 : 1;
        if (bValue === null || bValue === undefined) return order === 'asc' ? 1 : -1;

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return order === 'asc'
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        return order === 'asc'
          ? aValue < bValue ? -1 : 1
          : aValue < bValue ? 1 : -1;
      });
    }

    setFilteredRows(result);
    setPage(0);
  }, [rows, searchTerm, searchFields, orderBy, order]);

  // Handle page change
  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle sort
  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  // Get current page data
  const displayedRows = pagination
    ? filteredRows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : filteredRows;

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header with search and actions */}
      {(title || searchable) && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
            flexWrap: 'wrap',
            gap: 2
          }}
        >
          {title && (
            <Box>
              <Typography variant="h6" component="h2">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          )}

          <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
            {searchable && (
              <TextField
                size="small"
                placeholder={translate ? translate('common.search', { fallback: 'Search...' }) : 'Search...'}
                value={searchTerm}
                onChange={handleSearchChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{ width: { xs: '100%', sm: 200 } }}
              />
            )}

            {filterable && (
              <Tooltip title={translate ? translate('common.filter', { fallback: 'Filter' }) : 'Filter'}>
                <IconButton onClick={() => setShowFilters(!showFilters)}>
                  <FilterList />
                </IconButton>
              </Tooltip>
            )}

            {onRefresh && (
              <Tooltip title={translate ? translate('common.refresh', { fallback: 'Refresh' }) : 'Refresh'}>
                <IconButton onClick={onRefresh} disabled={loading}>
                  <Refresh />
                </IconButton>
              </Tooltip>
            )}

            {onExport && (
              <Tooltip title={translate ? translate('common.export', { fallback: 'Export' }) : 'Export'}>
                <IconButton onClick={onExport} disabled={loading}>
                  <Download />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>
      )}

      {/* Filters */}
      {filterable && showFilters && (
        <Box sx={{ mb: 2 }}>
          {filters}
        </Box>
      )}

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Loading indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Table */}
      <Paper
        sx={{
          width: '100%',
          overflow: 'hidden',
          bgcolor: alpha(theme.palette.background.paper, 0.8),
          borderRadius: 2,
          boxShadow: theme.shadows[2]
        }}
        className={className}
      >
        <TableContainer>
          <Table size={dense ? 'small' : 'medium'} stickyHeader>
            {showHeader && (
              <TableHead>
                <TableRow>
                  {visibleColumns.map((column) => (
                    <TableCell
                      key={String(column.id)}
                      align={column.align || 'left'}
                      style={{ minWidth: column.minWidth }}
                      sx={{ fontWeight: 'bold' }}
                    >
                      {column.sortable !== false ? (
                        <TableSortLabel
                          active={orderBy === column.id}
                          direction={orderBy === column.id ? order : 'asc'}
                          onClick={() => handleRequestSort(column.id as string)}
                        >
                          {column.label}
                        </TableSortLabel>
                      ) : (
                        column.label
                      )}
                    </TableCell>
                  ))}
                  {showActions && (onView || onEdit || onDelete || actions.length > 0) && (
                    <TableCell align="right">
                      {translate ? translate('common.actions', { fallback: 'Actions' }) : 'Actions'}
                    </TableCell>
                  )}
                </TableRow>
              </TableHead>
            )}
            <TableBody>
              {!loading && displayedRows.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={visibleColumns.length + (showActions ? 1 : 0)}
                    align="center"
                    sx={{ py: 4 }}
                  >
                    <Typography variant="body1">
                      {translate ? translate('common.no_data', { fallback: emptyMessage }) : emptyMessage}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                displayedRows.map((row, index) => {
                  const isHighlighted = highlightedRow !== undefined && row[keyField] === highlightedRow;
                  const rowClassName = getRowClassName ? getRowClassName(row) : '';

                  return (
                    <TableRow
                      hover
                      key={String(row[keyField]) || index}
                      onClick={onRowClick ? () => onRowClick(row) : undefined}
                      sx={{
                        cursor: onRowClick ? 'pointer' : 'default',
                        bgcolor: isHighlighted ? alpha(theme.palette.primary.main, 0.1) : 'inherit',
                        '&:hover': {
                          bgcolor: isHighlighted
                            ? alpha(theme.palette.primary.main, 0.15)
                            : alpha(theme.palette.action.hover, 0.1)
                        },
                        ...(rowClassName ? { className: rowClassName } : {})
                      }}
                    >
                      {visibleColumns.map((column) => (
                        <TableCell key={String(column.id)} align={column.align || 'left'}>
                          {column.format ? column.format(row[column.id as keyof T], row) : row[column.id as keyof T]}
                        </TableCell>
                      ))}
                      {showActions && (onView || onEdit || onDelete || actions.length > 0) && (
                        <TableCell align="right">
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 0.5 }}>
                            {onView && (
                              <Tooltip title={translate ? translate('common.view', { fallback: 'View' }) : 'View'}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onView(row);
                                  }}
                                >
                                  <Visibility fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {onEdit && (
                              <Tooltip title={translate ? translate('common.edit', { fallback: 'Edit' }) : 'Edit'}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEdit(row);
                                  }}
                                >
                                  <Edit fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {onDelete && (
                              <Tooltip title={translate ? translate('common.delete', { fallback: 'Delete' }) : 'Delete'}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onDelete(String(row[keyField]));
                                  }}
                                >
                                  <Delete fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                            {actions.map((action, i) => (
                              <Tooltip key={i} title={action.label}>
                                <IconButton
                                  size="small"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    action.onClick(row);
                                  }}
                                >
                                  {action.icon}
                                </IconButton>
                              </Tooltip>
                            ))}
                          </Box>
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {pagination && (
          <TablePagination
            rowsPerPageOptions={rowsPerPageOptions}
            component="div"
            count={filteredRows.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        )}
      </Paper>
    </Box>
  );
}

export default DataTable;
