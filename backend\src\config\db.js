// This file now serves as a wrapper for MongoDB connection
// to maintain backward compatibility with existing code
const mongodb = require('./mongodb');
const logger = require('../utils/logger');

// Export the MongoDB connection functions
async function connectDB() {
  try {
    return await mongodb.connectDB();
  } catch (err) {
    logger.error('Error connecting to MongoDB:', err.message);
    throw err;
  }
}

module.exports = {
  connectDB,
  getDB: mongodb.getDB,
  getCollection: mongodb.getCollection,
  client: mongodb.client,
  disconnectDB: mongodb.disconnectDB
};
