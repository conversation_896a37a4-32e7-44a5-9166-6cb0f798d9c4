/**
 * Settings Schema
 * 
 * Defines the MongoDB schema for the settings collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// System Settings Schema
const systemSettingsSchema = new Schema({
  name: {
    type: String,
    required: true,
    default: 'AgriIntel Livestock Management System'
  },
  logo: {
    url: {
      type: String
    },
    altText: {
      type: String
    }
  },
  theme: {
    default: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    },
    primaryColor: {
      type: String,
      default: '#3AA99F'
    },
    secondaryColor: {
      type: String,
      default: '#38B2AC'
    },
    allowUserCustomization: {
      type: Boolean,
      default: true
    }
  },
  language: {
    default: {
      type: String,
      default: 'en'
    },
    available: [{
      code: {
        type: String,
        required: true
      },
      name: {
        type: String,
        required: true
      },
      isActive: {
        type: Boolean,
        default: true
      }
    }]
  },
  dateFormat: {
    type: String,
    default: 'MM/DD/YYYY'
  },
  timeFormat: {
    type: String,
    default: 'hh:mm A'
  },
  currency: {
    code: {
      type: String,
      default: 'USD'
    },
    symbol: {
      type: String,
      default: '$'
    },
    position: {
      type: String,
      enum: ['before', 'after'],
      default: 'before'
    }
  },
  weightUnit: {
    type: String,
    enum: ['kg', 'lb'],
    default: 'kg'
  },
  temperatureUnit: {
    type: String,
    enum: ['C', 'F'],
    default: 'C'
  },
  notifications: {
    email: {
      enabled: {
        type: Boolean,
        default: true
      },
      server: {
        host: {
          type: String
        },
        port: {
          type: Number
        },
        secure: {
          type: Boolean,
          default: true
        },
        auth: {
          user: {
            type: String
          },
          pass: {
            type: String
          }
        }
      },
      from: {
        name: {
          type: String
        },
        email: {
          type: String
        }
      }
    },
    push: {
      enabled: {
        type: Boolean,
        default: false
      },
      provider: {
        type: String,
        enum: ['firebase', 'onesignal', 'custom', 'none'],
        default: 'none'
      },
      config: {
        type: Map,
        of: Schema.Types.Mixed
      }
    }
  },
  backup: {
    automatic: {
      enabled: {
        type: Boolean,
        default: true
      },
      frequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly'],
        default: 'daily'
      },
      time: {
        type: String,
        default: '00:00'
      },
      retentionCount: {
        type: Number,
        default: 7
      }
    },
    storage: {
      type: {
        type: String,
        enum: ['local', 's3', 'google', 'azure', 'custom'],
        default: 'local'
      },
      config: {
        type: Map,
        of: Schema.Types.Mixed
      }
    }
  },
  security: {
    passwordPolicy: {
      minLength: {
        type: Number,
        default: 8
      },
      requireUppercase: {
        type: Boolean,
        default: true
      },
      requireLowercase: {
        type: Boolean,
        default: true
      },
      requireNumbers: {
        type: Boolean,
        default: true
      },
      requireSpecialChars: {
        type: Boolean,
        default: true
      },
      expiryDays: {
        type: Number,
        default: 90
      }
    },
    sessionTimeout: {
      type: Number,
      default: 30 // minutes
    },
    maxLoginAttempts: {
      type: Number,
      default: 5
    },
    lockoutDuration: {
      type: Number,
      default: 15 // minutes
    },
    twoFactorAuth: {
      enabled: {
        type: Boolean,
        default: false
      },
      method: {
        type: String,
        enum: ['email', 'sms', 'app', 'none'],
        default: 'none'
      }
    }
  },
  farm: {
    name: {
      type: String
    },
    address: {
      street: {
        type: String
      },
      city: {
        type: String
      },
      state: {
        type: String
      },
      postalCode: {
        type: String
      },
      country: {
        type: String
      }
    },
    contact: {
      phone: {
        type: String
      },
      email: {
        type: String
      },
      website: {
        type: String
      }
    },
    registration: {
      taxId: {
        type: String
      },
      businessId: {
        type: String
      },
      licenses: [{
        type: {
          type: String
        },
        number: {
          type: String
        },
        issuedBy: {
          type: String
        },
        issuedDate: {
          type: Date
        },
        expiryDate: {
          type: Date
        }
      }]
    }
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// User Settings Schema
const userSettingsSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  theme: {
    mode: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'system'
    },
    color: {
      type: String
    }
  },
  language: {
    type: String,
    default: 'en'
  },
  dashboard: {
    layout: {
      type: Schema.Types.Mixed
    },
    widgets: [{
      type: {
        type: String,
        required: true
      },
      position: {
        x: {
          type: Number
        },
        y: {
          type: Number
        },
        w: {
          type: Number
        },
        h: {
          type: Number
        }
      },
      config: {
        type: Map,
        of: Schema.Types.Mixed
      }
    }]
  },
  notifications: {
    email: {
      enabled: {
        type: Boolean,
        default: true
      },
      digest: {
        type: String,
        enum: ['immediate', 'daily', 'weekly', 'none'],
        default: 'immediate'
      }
    },
    push: {
      enabled: {
        type: Boolean,
        default: true
      },
      devices: [{
        token: {
          type: String
        },
        platform: {
          type: String,
          enum: ['ios', 'android', 'web', 'other'],
          default: 'web'
        },
        lastUsed: {
          type: Date
        }
      }]
    }
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes
userSettingsSchema.index({ user: 1 });

// Pre-save hook to update timestamps
systemSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

userSettingsSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const SystemSettings = mongoose.model('SystemSettings', systemSettingsSchema);
const UserSettings = mongoose.model('UserSettings', userSettingsSchema);

module.exports = {
  SystemSettings,
  UserSettings
};
