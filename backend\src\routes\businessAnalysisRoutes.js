const express = require('express');
const router = express.Router();
const businessAnalysisController = require('../controllers/businessAnalysisController');
const mockDataController = require('../controllers/mockDataController');

/**
 * Business Analysis Routes
 */
module.exports = (useMockData = false) => {
  // Controller would be selected based on mock data flag
  const controller = useMockData
    ? mockDataController
    : businessAnalysisController;

  // Define routes
  router.get('/', (req, res) => {
    res.status(200).json({ message: 'Business Analysis API' });
  });

  // Dashboard data
  router.get('/dashboard', controller.getBusinessAnalyticsDashboard);
  
  // Business strategy data
  router.get('/strategy', controller.getBusinessStrategy);
  
  // KPIs
  router.get('/kpis', controller.getBusinessKPIs);
  router.post('/kpis', controller.createBusinessKPI);
  router.put('/kpis/:id', controller.updateBusinessKPI);
  
  // Predictions
  router.get('/predictions', controller.getBusinessPredictions);
  
  // Insights
  router.get('/insights', controller.getBusinessInsights);
  router.post('/insights', controller.createBusinessInsight);
  
  // Risks
  router.get('/risks', controller.getBusinessRisks);
  
  // Opportunities
  router.get('/opportunities', controller.getBusinessOpportunities);
  
  // Strategic goals
  router.get('/goals', controller.getStrategicGoals);
  
  // Strategic initiatives
  router.get('/initiatives', controller.getStrategicInitiatives);
  
  // SWOT analysis
  router.get('/swot', controller.getSWOTAnalysis);
  router.put('/swot', controller.updateSWOTAnalysis);
  
  // Strategic scenarios
  router.get('/scenarios', controller.getStrategicScenarios);

  return router;
};
