/**
 * Resources Management Controller
 * Handles resources, government information, and auction data
 */

const mongodb = require('../../config/mongodb');
const logger = require('../../utils/logger');
const { ObjectId } = require('mongodb');

class ResourcesController {
  // Get all resources
  async getResources(req, res) {
    try {
      const db = mongodb.getDB();
      const collection = db.collection('resources');
      
      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 20;
      const skip = (page - 1) * limit;
      const category = req.query.category;
      const type = req.query.type;
      
      // Build filter
      const filter = {};
      if (category) filter.category = category;
      if (type) filter.type = type;
      
      // Get resources with pagination
      const resources = await collection
        .find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .toArray();
      
      const total = await collection.countDocuments(filter);
      
      res.json({
        success: true,
        data: resources,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      logger.error('Error fetching resources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch resources',
        error: error.message
      });
    }
  }

  // Get government resources
  async getGovernmentResources(req, res) {
    try {
      const governmentResources = [
        {
          id: 'daff',
          title: 'Department of Agriculture, Forestry and Fisheries',
          description: 'Official government department for agricultural support and regulations',
          url: 'https://www.daff.gov.za',
          category: 'government',
          type: 'department',
          services: [
            'Agricultural subsidies',
            'Livestock registration',
            'Disease control programs',
            'Export permits',
            'Training programs'
          ]
        },
        {
          id: 'nda',
          title: 'National Department of Agriculture',
          description: 'National agricultural policies and support programs',
          url: 'https://www.nda.agric.za',
          category: 'government',
          type: 'department',
          services: [
            'Land reform programs',
            'Agricultural development',
            'Rural development',
            'Food security initiatives'
          ]
        },
        {
          id: 'arc',
          title: 'Agricultural Research Council',
          description: 'Research and development for agricultural advancement',
          url: 'https://www.arc.agric.za',
          category: 'research',
          type: 'institution',
          services: [
            'Research publications',
            'Technology transfer',
            'Training and education',
            'Laboratory services'
          ]
        },
        {
          id: 'rppo',
          title: 'Red Meat Producers Organisation',
          description: 'Industry organization for red meat producers',
          url: 'https://www.rpo.co.za',
          category: 'industry',
          type: 'organization',
          services: [
            'Market information',
            'Industry advocacy',
            'Training programs',
            'Quality standards'
          ]
        }
      ];

      res.json({
        success: true,
        data: governmentResources,
        message: 'Government resources retrieved successfully'
      });
    } catch (error) {
      logger.error('Error fetching government resources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch government resources',
        error: error.message
      });
    }
  }

  // Get auction information
  async getAuctionInformation(req, res) {
    try {
      const auctionData = [
        {
          id: 'vryburg',
          name: 'Vryburg Livestock Auction',
          location: 'Vryburg, North West',
          nextAuctionDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
          contact: '+27 53 927 1001',
          email: '<EMAIL>',
          specialties: ['Cattle', 'Sheep', 'Goats'],
          auctionDays: ['Wednesday', 'Saturday'],
          description: 'One of the largest livestock auctions in South Africa'
        },
        {
          id: 'bloemfontein',
          name: 'Bloemfontein Livestock Market',
          location: 'Bloemfontein, Free State',
          nextAuctionDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // In 5 days
          contact: '+27 51 447 3042',
          email: '<EMAIL>',
          specialties: ['Cattle', 'Sheep'],
          auctionDays: ['Tuesday', 'Thursday'],
          description: 'Premier livestock market in the Free State'
        },
        {
          id: 'potchefstroom',
          name: 'Potchefstroom Livestock Auction',
          location: 'Potchefstroom, North West',
          nextAuctionDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000), // In 10 days
          contact: '+27 18 294 3500',
          email: '<EMAIL>',
          specialties: ['Cattle', 'Pigs', 'Sheep'],
          auctionDays: ['Monday', 'Friday'],
          description: 'Modern auction facilities with online bidding'
        },
        {
          id: 'nelspruit',
          name: 'Nelspruit Livestock Market',
          location: 'Nelspruit, Mpumalanga',
          nextAuctionDate: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000), // In 12 days
          contact: '+27 13 752 3456',
          email: '<EMAIL>',
          specialties: ['Cattle', 'Goats'],
          auctionDays: ['Wednesday'],
          description: 'Serving the Lowveld region with quality livestock'
        }
      ];

      res.json({
        success: true,
        data: auctionData,
        message: 'Auction information retrieved successfully'
      });
    } catch (error) {
      logger.error('Error fetching auction information:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch auction information',
        error: error.message
      });
    }
  }

  // Get educational resources
  async getEducationalResources(req, res) {
    try {
      const educationalResources = [
        {
          id: 'livestock-management-basics',
          title: 'Livestock Management Basics',
          description: 'Comprehensive guide to livestock management principles',
          category: 'education',
          type: 'guide',
          topics: [
            'Animal nutrition',
            'Health management',
            'Breeding practices',
            'Housing and facilities',
            'Record keeping'
          ],
          difficulty: 'beginner',
          estimatedTime: '2 hours'
        },
        {
          id: 'cattle-breeding-guide',
          title: 'Cattle Breeding Best Practices',
          description: 'Advanced guide to cattle breeding and genetics',
          category: 'education',
          type: 'guide',
          topics: [
            'Genetic selection',
            'Breeding programs',
            'Artificial insemination',
            'Pregnancy management',
            'Calf management'
          ],
          difficulty: 'intermediate',
          estimatedTime: '3 hours'
        },
        {
          id: 'disease-prevention',
          title: 'Disease Prevention and Control',
          description: 'Essential knowledge for livestock health management',
          category: 'health',
          type: 'guide',
          topics: [
            'Vaccination schedules',
            'Biosecurity measures',
            'Common diseases',
            'Treatment protocols',
            'Emergency procedures'
          ],
          difficulty: 'intermediate',
          estimatedTime: '2.5 hours'
        },
        {
          id: 'financial-planning',
          title: 'Farm Financial Planning',
          description: 'Guide to financial management for livestock operations',
          category: 'business',
          type: 'guide',
          topics: [
            'Budgeting and forecasting',
            'Cost analysis',
            'Profit optimization',
            'Investment planning',
            'Risk management'
          ],
          difficulty: 'advanced',
          estimatedTime: '4 hours'
        }
      ];

      res.json({
        success: true,
        data: educationalResources,
        message: 'Educational resources retrieved successfully'
      });
    } catch (error) {
      logger.error('Error fetching educational resources:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch educational resources',
        error: error.message
      });
    }
  }

  // Get resources overview
  async getResourcesOverview(req, res) {
    try {
      res.json({
        success: true,
        message: 'Resources module is operational',
        data: {
          module: 'resources',
          status: 'active',
          features: [
            'Government Resources Access',
            'Auction Information',
            'Educational Materials',
            'Industry Contacts',
            'Regulatory Information'
          ],
          categories: [
            'Government Departments',
            'Livestock Auctions',
            'Educational Guides',
            'Industry Organizations',
            'Research Institutions'
          ]
        }
      });
    } catch (error) {
      logger.error('Error in resources overview:', error);
      res.status(500).json({
        success: false,
        message: 'Resources module error',
        error: error.message
      });
    }
  }
}

module.exports = new ResourcesController();
