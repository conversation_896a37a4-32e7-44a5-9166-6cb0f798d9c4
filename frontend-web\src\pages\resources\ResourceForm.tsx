import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Paper } from '@mui/material';

const ResourceForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditing = !!id;

  return (
    <Box p={4}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5">{isEditing ? 'Edit Resource' : 'Add New Resource'}</Typography>
        <Typography variant="body1" mt={2}>
          {isEditing ? `Editing resource ID: ${id}` : 'Create a new resource'}
        </Typography>
      </Paper>
    </Box>
  );
};

export default ResourceForm;
