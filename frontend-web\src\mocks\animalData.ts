import { generateMockData, randomDate, randomNumber, randomChoice } from '../utils/mockDataGenerator';
import { Animal, AnimalStats } from '../hooks/useAnimalData';

const speciesList = ['Cattle', 'Sheep', 'Goat', 'Game', 'Horse'];
const cattleBreeds = ['<PERSON><PERSON>', 'Bonsmara', 'Afrikaner', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Beefmaster'];
const sheepBreeds = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>master', '<PERSON><PERSON><PERSON>', '<PERSON> Rooy'];
const goatBreeds = ['Boer', 'Kalahari Red', 'Savanna', 'Indigenous Veld', 'Angora'];
const gameBreeds = ['Springbok', 'Impala', 'Kudu', 'Blesbok', 'Wildebeest'];
const horseBreeds = ['Boerperd', 'Nooitgedacht', 'Basuto Pony', 'SA Warmblood', 'Thoroughbred'];
const genders = ['Male', 'Female'];
const statuses = ['Active', 'Sold', 'Deceased', 'Quarantined', 'Breeding'];
const locations = ['Veld A', 'Veld B', 'Kraal 1', 'Kraal 2', 'Boma 1', 'Boma 2', 'Quarantine'];
const healthStatuses = ['healthy', 'sick', 'injured', 'pregnant'];

// Custom animals with Sotho, Tswana, and Afrikaans names (no Zulu names)
const customAnimals: Animal[] = [
  {
    id: '1',
    tagNumber: 'CTL-2020-001',
    name: 'Tshepiso',
    species: 'Cattle',
    type: 'Beef',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2020-05-15',
    status: 'Active',
    location: 'Veld A',
    weight: 580,
    purchaseDate: '2020-06-10',
    purchasePrice: 15000,
    healthStatus: 'healthy',
    rfidTag: 'RFID00001',
    notes: 'Tswana name meaning "Promise". Excellent breeding cow.',
    imageUrl: '/images/animals/cattle-1.jpeg'
  },
  {
    id: '2',
    tagNumber: 'CTL-2019-002',
    name: 'Tumelo',
    species: 'Cattle',
    type: 'Beef',
    breed: 'Bonsmara',
    gender: 'Male',
    birthDate: '2019-08-22',
    status: 'Active',
    location: 'Kraal 1',
    weight: 780,
    purchaseDate: '2019-10-05',
    purchasePrice: 22000,
    healthStatus: 'healthy',
    rfidTag: 'RFID00002',
    notes: 'Tswana name meaning "Faith". Strong bull with excellent genetics.',
    imageUrl: '/images/animals/cattle-2.avif',
    sireId: '',
    damId: ''
  },
  {
    id: '3',
    tagNumber: 'CTL-2021-003',
    name: 'Lerato',
    species: 'Cattle',
    type: 'Beef',
    breed: 'Afrikaner',
    gender: 'Female',
    birthDate: '2021-03-10',
    status: 'Active',
    location: 'Veld B',
    weight: 450,
    purchaseDate: '2021-04-15',
    purchasePrice: 14000,
    healthStatus: 'pregnant',
    rfidTag: 'RFID00003',
    notes: 'Sotho name meaning "Love". High quality beef producer.',
    imageUrl: '/images/animals/cattle-3.jpeg',
    sireId: '2',
    damId: ''
  },
  {
    id: '4',
    tagNumber: 'CTL-2020-004',
    name: 'Botha',
    species: 'Cattle',
    type: 'Beef',
    breed: 'Drakensberger',
    gender: 'Male',
    birthDate: '2020-11-05',
    status: 'Active',
    location: 'Veld A',
    weight: 650,
    purchaseDate: '2021-01-20',
    purchasePrice: 18500,
    healthStatus: 'healthy',
    rfidTag: 'RFID00004',
    notes: 'Afrikaans surname commonly used as a name. Consistent growth rate.',
    imageUrl: '/images/animals/cattle-4.jpeg',
    sireId: '',
    damId: ''
  },
  {
    id: '5',
    tagNumber: 'CTL-2022-005',
    name: 'Pule',
    species: 'Cattle',
    type: 'Beef',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2022-02-14',
    status: 'Active',
    location: 'Kraal 2',
    weight: 320,
    purchaseDate: undefined,
    purchasePrice: undefined,
    healthStatus: 'healthy',
    rfidTag: 'RFID00005',
    notes: 'Sotho name meaning "Rain". Born on farm, daughter of Tshepiso.',
    imageUrl: '/images/animals/cattle-5.avif',
    parentId: '1',
    parentTagNumber: 'CTL-2020-001',
    sireId: '2',
    damId: '1'
  },
  {
    id: '6',
    tagNumber: 'SHP-2021-001',
    name: 'Naledi',
    species: 'Sheep',
    type: 'Meat',
    breed: 'Dorper',
    gender: 'Female',
    birthDate: '2021-09-12',
    status: 'Active',
    location: 'East Paddock',
    weight: 65,
    purchaseDate: '2022-01-15',
    purchasePrice: 2500,
    healthStatus: 'healthy',
    rfidTag: 'RFID00006',
    notes: 'Sotho name meaning "Star". Good wool quality.',
    imageUrl: '/images/animals/sheep-1.jpeg'
  },
  {
    id: '7',
    tagNumber: 'GOT-2022-001',
    name: 'Kgosi',
    species: 'Goat',
    type: 'Meat',
    breed: 'Boer Goat',
    gender: 'Male',
    birthDate: '2022-03-05',
    status: 'Active',
    location: 'West Paddock',
    weight: 75,
    purchaseDate: '2022-05-20',
    purchasePrice: 3000,
    healthStatus: 'healthy',
    rfidTag: 'RFID00007',
    notes: 'Tswana name meaning "Chief". Excellent meat producer.',
    imageUrl: '/images/animals/goat-1.jpeg'
  },
  {
    id: '8',
    tagNumber: 'CTL-2021-006',
    name: 'Thabo',
    species: 'Cattle',
    type: 'Dairy',
    breed: 'Jersey',
    gender: 'Male',
    birthDate: '2021-06-18',
    status: 'Active',
    location: 'Dairy Barn',
    weight: 520,
    purchaseDate: '2021-08-10',
    purchasePrice: 16000,
    healthStatus: 'healthy',
    rfidTag: 'RFID00008',
    notes: 'Sotho name meaning "Joy". Good dairy genetics.',
    imageUrl: '/images/animals/cattle-6.jpeg'
  },
  {
    id: '9',
    tagNumber: 'SHP-2022-002',
    name: 'Bokkie',
    species: 'Sheep',
    type: 'Wool',
    breed: 'Merino',
    gender: 'Female',
    birthDate: '2022-01-25',
    status: 'Active',
    location: 'South Paddock',
    weight: 58,
    purchaseDate: '2022-03-15',
    purchasePrice: 2800,
    healthStatus: 'healthy',
    rfidTag: 'RFID00009',
    notes: 'Afrikaans name meaning "Little Buck/Deer". Premium wool quality.',
    imageUrl: '/images/animals/sheep-2.jpeg'
  },
  {
    id: '10',
    tagNumber: 'GOT-2021-002',
    name: 'Venter',
    species: 'Goat',
    type: 'Dairy',
    breed: 'Saanen',
    gender: 'Female',
    birthDate: '2021-11-10',
    status: 'Active',
    location: 'East Paddock',
    weight: 68,
    purchaseDate: '2022-02-05',
    purchasePrice: 3200,
    healthStatus: 'healthy',
    rfidTag: 'RFID00010',
    notes: 'Afrikaans surname commonly used as a name. Good milk producer.',
    imageUrl: '/images/animals/goat-2.jpeg'
  }
];

export const mockAnimals: Animal[] = customAnimals;

export const mockAnimalStats: AnimalStats = {
  totalAnimals: mockAnimals.length,
  healthPercentage: Math.round(mockAnimals.filter(a => a.healthStatus === 'healthy').length / mockAnimals.length * 100),
  pendingCheckups: Math.floor(mockAnimals.length * 0.15),
  averageGrowthRate: 1.2,
  activeAnimals: mockAnimals.filter(a => a.status === 'Active').length,
  recentAdditions: Math.floor(mockAnimals.length * 0.1),
  bySpecies: {
    Cattle: mockAnimals.filter(a => a.species === 'Cattle').length,
    Sheep: mockAnimals.filter(a => a.species === 'Sheep').length,
    Goat: mockAnimals.filter(a => a.species === 'Goat').length,
    Game: mockAnimals.filter(a => a.species === 'Game').length
  },
  byStatus: {
    active: mockAnimals.filter(a => a.status === 'Active').length,
    sold: mockAnimals.filter(a => a.status === 'Sold').length,
    deceased: mockAnimals.filter(a => a.status === 'Deceased').length,
    quarantine: mockAnimals.filter(a => a.status === 'Quarantined').length
  },
  byLocation: {
    'Veld A': mockAnimals.filter(a => a.location === 'Veld A').length,
    'Veld B': mockAnimals.filter(a => a.location === 'Veld B').length,
    'Kraal 1': mockAnimals.filter(a => a.location === 'Kraal 1').length,
    'Kraal 2': mockAnimals.filter(a => a.location === 'Kraal 2').length
  },
  byHealth: {
    healthy: mockAnimals.filter(a => a.healthStatus === 'healthy').length,
    sick: mockAnimals.filter(a => a.healthStatus === 'sick').length,
    injured: mockAnimals.filter(a => a.healthStatus === 'injured').length,
    pregnant: mockAnimals.filter(a => a.healthStatus === 'pregnant').length
  },
  // Asset Management stats
  retiredAnimals: 5,
  nearingRetirement: 3,
  retirementByReason: {
    age: 2,
    breeding: 2,
    health: 1,
    other: 0
  },
  valueOfActiveAssets: 850000,
  valueOfRetiredAssets: 150000,
  totalAssetValue: 1000000,
  averageRoi: 15
};

export const mockAnimalMetrics = {
  weightGain: {
    lastMonth: "+3.2%",
    lastQuarter: "+8.5%"
  },
  mortality: {
    lastMonth: "0%",
    lastQuarter: "0.3%"
  },
  birthRate: {
    lastMonth: "3",
    lastQuarter: "8"
  },
  healthIncidents: {
    lastMonth: "1",
    lastQuarter: "3"
  }
};

export const mockAnimalAlerts = [
  {
    id: "ALT001",
    animalId: "3", // Lerato
    animalTagNumber: "CTL-2021-003",
    type: "health",
    priority: "high",
    message: "Upcoming calving check required",
    created_at: "2024-03-15"
  },
  {
    id: "ALT002",
    animalId: "6", // Mock sheep
    animalTagNumber: "SHP-2024-001",
    type: "maintenance",
    priority: "medium",
    message: "Scheduled for wool shearing",
    created_at: "2024-03-14"
  },
  {
    id: "ALT003",
    animalId: "7", // Mock goat
    animalTagNumber: "GOT-2024-001",
    type: "health",
    priority: "low",
    message: "Due for routine health check",
    created_at: "2024-03-13"
  }
];

export const mockAnimalDocuments = [
  {
    id: "DOC001",
    title: "Vaccination Record",
    type: "Medical",
    status: "Current",
    issueDate: "2024-02-15",
    expiryDate: "2025-02-15",
    issuedBy: "Dr. Anderson"
  },
  {
    id: "DOC002",
    title: "Breeding Certificate",
    type: "Certification",
    status: "Current",
    issueDate: "2024-01-20",
    expiryDate: "2024-12-31",
    issuedBy: "State Agriculture Dept"
  }
];

export const mockAnimalCertifications = [
  {
    id: "CERT001",
    name: "Livestock Identification and Traceability System (LITS)",
    status: "Active",
    issueDate: "2024-01-01",
    expiryDate: "2024-12-31",
    issuedBy: "Department of Agriculture, Land Reform and Rural Development"
  },
  {
    id: "CERT002",
    name: "SA Stud Book Registration",
    status: "Active",
    issueDate: "2024-02-01",
    expiryDate: "2025-01-31",
    issuedBy: "SA Stud Book and Animal Improvement Association"
  },
  {
    id: "CERT003",
    name: "Red Meat Abattoir Association Compliance",
    status: "Active",
    issueDate: "2023-11-15",
    expiryDate: "2024-11-14",
    issuedBy: "Red Meat Abattoir Association"
  }
];

export const mockAnimalInspections = [
  {
    id: "INSP001",
    type: "Animal Health and Welfare",
    date: "2024-03-01",
    inspector: "Dr. Nkosi",
    status: "Passed",
    notes: "All animals in excellent condition"
  },
  {
    id: "INSP002",
    type: "Veterinary Fence Compliance",
    date: "2024-02-15",
    inspector: "Thabo Molefe",
    status: "Passed",
    notes: "All fencing meets regulatory requirements"
  },
  {
    id: "INSP003",
    type: "Disease Control Measures",
    date: "2024-01-20",
    inspector: "Dr. van der Merwe",
    status: "Passed with Recommendations",
    notes: "Additional dipping schedule recommended for summer months"
  }
];







