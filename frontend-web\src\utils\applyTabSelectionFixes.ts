/**
 * Utility to apply tab selection fixes to all components
 * This script can be run to fix selection issues across the application
 */

import { getSelectableTabsStyles, getSelectableTabPanelStyles, getSelectableTabContentStyles } from './tabSelectionUtils';

/**
 * Apply tab selection fixes to all components
 * This function can be called from any component to ensure proper tab selection
 */
export const applyTabSelectionFixes = () => {
  try {
    // Find all tab elements in the DOM
    const tabElements = document.querySelectorAll('.MuiTabs-root');
    const tabPanelElements = document.querySelectorAll('[role="tabpanel"]');
    const tabContentElements = document.querySelectorAll('.MuiTabPanel-root');
    const tabsScrollerElements = document.querySelectorAll('.MuiTabs-scroller');
    const tabsFlexContainerElements = document.querySelectorAll('.MuiTabs-flexContainer');
    const tabElements2 = document.querySelectorAll('.MuiTab-root');
    const mainNavTabs = document.querySelectorAll('[role="tab"]');

    // Find specific theme settings tabs
    const themeTab0 = document.getElementById('theme-tab-0');
    const themeTab1 = document.getElementById('theme-tab-1');
    const themeTab2 = document.getElementById('theme-tab-2');
    const themeTabPanel0 = document.getElementById('theme-tabpanel-0');
    const themeTabPanel1 = document.getElementById('theme-tabpanel-1');
    const themeTabPanel2 = document.getElementById('theme-tabpanel-2');

    // Apply styles to specific theme tabs
    [themeTab0, themeTab1, themeTab2].forEach(element => {
      if (element) {
        element.style.setProperty('position', 'relative', 'important');
        element.style.setProperty('z-index', '10', 'important');
        element.style.setProperty('pointer-events', 'auto', 'important');
        element.style.setProperty('cursor', 'pointer', 'important');
      }
    });

    // Apply styles to specific theme tab panels
    [themeTabPanel0, themeTabPanel1, themeTabPanel2].forEach(element => {
      if (element) {
        element.style.setProperty('position', 'relative', 'important');
        element.style.setProperty('z-index', '5', 'important');
        element.style.setProperty('pointer-events', 'auto', 'important');
      }
    });

    // Apply styles to tab elements
    tabElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to individual tab buttons
    tabElements2.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });

    // Apply styles to tabs scroller
    tabsScrollerElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tabs flex container
    tabsFlexContainerElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tab panel elements
    tabPanelElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '5', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tab content elements
    tabContentElements.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '5', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to all buttons in tabs
    const buttonsInTabs = document.querySelectorAll('.MuiTabs-root button, [role="tabpanel"] button, .MuiTabPanel-root button');
    buttonsInTabs.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '10', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });

    // Apply styles to main navigation tabs
    mainNavTabs.forEach(element => {
      (element as HTMLElement).style.setProperty('position', 'relative', 'important');
      (element as HTMLElement).style.setProperty('z-index', '100', 'important');
      (element as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (element as HTMLElement).style.setProperty('cursor', 'pointer', 'important');

      // Add click event listener to ensure tab clicks are registered
      element.addEventListener('click', (e) => {
        console.log('Tab clicked via enhanced global handler');
        // Prevent event from being blocked
        e.stopPropagation();
      });
    });

    // Apply styles to the main navigation container
    const mainNavContainer = document.querySelector('.MuiAppBar-root');
    if (mainNavContainer) {
      (mainNavContainer as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (mainNavContainer as HTMLElement).style.setProperty('z-index', '1200', 'important');
    }

    // Apply styles to any overlays that might be blocking clicks
    const overlays = document.querySelectorAll('.MuiBackdrop-root, .MuiModal-backdrop');
    overlays.forEach(element => {
      if (!(element as HTMLElement).classList.contains('MuiBackdrop-open')) {
        (element as HTMLElement).style.setProperty('pointer-events', 'none', 'important');
      }
    });

    // Set up a MutationObserver to apply fixes to dynamically added tabs
    const observer = new MutationObserver((mutations) => {
      let shouldFix = false;

      mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
          mutation.addedNodes.forEach(node => {
            if (node instanceof HTMLElement) {
              if (node.querySelector('[role="tab"]') ||
                  node.getAttribute('role') === 'tab' ||
                  node.classList.contains('MuiTabs-root') ||
                  node.classList.contains('MuiTab-root')) {
                shouldFix = true;
              }
            }
          });
        }
      });

      if (shouldFix) {
        applyTabSelectionFixes();
      }
    });

    // Start observing the document body
    observer.observe(document.body, { childList: true, subtree: true });

  } catch (error) {
    console.error('Error applying tab selection fixes:', error);
  }
};

/**
 * Apply tab selection fixes to a specific component
 * @param element - The DOM element to apply fixes to
 */
export const applyTabSelectionFixesToElement = (element: HTMLElement) => {
  try {
    // Find all tab elements in the element
    const tabElements = element.querySelectorAll('.MuiTabs-root');
    const tabPanelElements = element.querySelectorAll('[role="tabpanel"]');
    const tabContentElements = element.querySelectorAll('.MuiTabPanel-root');
    const tabsScrollerElements = element.querySelectorAll('.MuiTabs-scroller');
    const tabsFlexContainerElements = element.querySelectorAll('.MuiTabs-flexContainer');
    const tabElements2 = element.querySelectorAll('.MuiTab-root');
    const mainNavTabs = element.querySelectorAll('[role="tab"]');

    // Find specific theme settings tabs within this element
    const themeTab0 = element.querySelector('#theme-tab-0');
    const themeTab1 = element.querySelector('#theme-tab-1');
    const themeTab2 = element.querySelector('#theme-tab-2');
    const themeTabPanel0 = element.querySelector('#theme-tabpanel-0');
    const themeTabPanel1 = element.querySelector('#theme-tabpanel-1');
    const themeTabPanel2 = element.querySelector('#theme-tabpanel-2');

    // Apply styles to specific theme tabs
    [themeTab0, themeTab1, themeTab2].forEach(tab => {
      if (tab) {
        (tab as HTMLElement).style.setProperty('position', 'relative', 'important');
        (tab as HTMLElement).style.setProperty('z-index', '10', 'important');
        (tab as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
        (tab as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
      }
    });

    // Apply styles to specific theme tab panels
    [themeTabPanel0, themeTabPanel1, themeTabPanel2].forEach(panel => {
      if (panel) {
        (panel as HTMLElement).style.setProperty('position', 'relative', 'important');
        (panel as HTMLElement).style.setProperty('z-index', '5', 'important');
        (panel as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      }
    });

    // Apply styles to tab elements
    tabElements.forEach(tabElement => {
      (tabElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (tabElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (tabElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to individual tab buttons
    tabElements2.forEach(tabElement => {
      (tabElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (tabElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (tabElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (tabElement as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });

    // Apply styles to tabs scroller
    tabsScrollerElements.forEach(scrollerElement => {
      (scrollerElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (scrollerElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (scrollerElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tabs flex container
    tabsFlexContainerElements.forEach(containerElement => {
      (containerElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (containerElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (containerElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tab panel elements
    tabPanelElements.forEach(tabPanelElement => {
      (tabPanelElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (tabPanelElement as HTMLElement).style.setProperty('z-index', '5', 'important');
      (tabPanelElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to tab content elements
    tabContentElements.forEach(tabContentElement => {
      (tabContentElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (tabContentElement as HTMLElement).style.setProperty('z-index', '5', 'important');
      (tabContentElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
    });

    // Apply styles to all buttons in tabs
    const buttonsInTabs = element.querySelectorAll('.MuiTabs-root button, [role="tabpanel"] button, .MuiTabPanel-root button');
    buttonsInTabs.forEach(buttonElement => {
      (buttonElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (buttonElement as HTMLElement).style.setProperty('z-index', '10', 'important');
      (buttonElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (buttonElement as HTMLElement).style.setProperty('cursor', 'pointer', 'important');
    });

    // Apply styles to main navigation tabs
    mainNavTabs.forEach(tabElement => {
      (tabElement as HTMLElement).style.setProperty('position', 'relative', 'important');
      (tabElement as HTMLElement).style.setProperty('z-index', '100', 'important');
      (tabElement as HTMLElement).style.setProperty('pointer-events', 'auto', 'important');
      (tabElement as HTMLElement).style.setProperty('cursor', 'pointer', 'important');

      // Add click event listener to ensure tab clicks are registered
      tabElement.addEventListener('click', (e) => {
        console.log('Tab clicked via enhanced element handler');
        // Prevent event from being blocked
        e.stopPropagation();
      });
    });

    // Apply styles to any overlays that might be blocking clicks
    const overlays = element.querySelectorAll('.MuiBackdrop-root, .MuiModal-backdrop');
    overlays.forEach(overlayElement => {
      if (!(overlayElement as HTMLElement).classList.contains('MuiBackdrop-open')) {
        (overlayElement as HTMLElement).style.setProperty('pointer-events', 'none', 'important');
      }
    });

  } catch (error) {
    console.error('Error applying tab selection fixes to element:', error);
  }
};
