import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ComplianceDashboard from './compliance/ComplianceDashboard';
import Documents from './compliance/Documents';
import BrandmarksCertificates from '../components/modules/compliance/BrandmarksCertificates';

const Compliance: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<ComplianceDashboard />} />
      <Route path="/dashboard" element={<ComplianceDashboard />} />
      <Route path="/brandmarks" element={<BrandmarksCertificates />} />
      <Route path="/documents" element={<Documents />} />
    </Routes>
  );
};

export default Compliance;
