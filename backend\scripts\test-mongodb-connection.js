/**
 * Test MongoDB Connection Script
 *
 * This script tests the MongoDB connection and displays database information
 */

require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
const mongodb = require('../src/config/mongodb');
const logger = require('../src/utils/logger');

console.log('Environment Variables:', process.env);

process.env.MONGODB_DB_NAME = 'ampd_livestock';

async function testConnection() {
  try {
    logger.info('Testing MongoDB connection...');

    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB successfully!');

    // Get database information
    const dbInfo = await db.command({ dbStats: 1 });
    logger.info(`Database: ${db.databaseName}`);
    logger.info(`Collections: ${dbInfo.collections}`);
    logger.info(`Documents: ${dbInfo.objects}`);
    logger.info(`Storage size: ${(dbInfo.storageSize / 1024 / 1024).toFixed(2)} MB`);

    // List collections
    const collections = await db.listCollections().toArray();
    logger.info(`Found ${collections.length} collections:`);

    // Get document counts for each collection
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      logger.info(`- ${collection.name}: ${count} documents`);

      // Show sample document if collection has documents
      if (count > 0) {
        const sample = await db.collection(collection.name).findOne({});
        logger.info(`  Sample document fields: ${Object.keys(sample).join(', ')}`);
      }
    }

    // Check connection status
    const connectionStatus = await mongodb.checkConnection();
    logger.info(`Connection status: ${connectionStatus ? 'Connected' : 'Disconnected'}`);

    // Get connection status
    const status = mongodb.getConnectionStatus();
    logger.info(`Connection details: ${JSON.stringify(status)}`);

    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    logger.info('Test completed successfully');

    return true;
  } catch (error) {
    logger.error('Error testing MongoDB connection:', error);
    return false;
  }
}

// Run the test
testConnection()
  .then(success => {
    if (success) {
      logger.info('MongoDB connection test completed successfully');
      process.exit(0);
    } else {
      logger.error('MongoDB connection test failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during MongoDB connection test:', error);
    process.exit(1);
  });
