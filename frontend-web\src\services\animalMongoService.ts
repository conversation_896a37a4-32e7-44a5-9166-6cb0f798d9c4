import { MongoDbService } from './mongoDbService';
import { Animal } from '../types/animal';
import { AnimalModel, toAnimal, toAnimalModel } from '../models/AnimalModel';
import { ObjectId } from '../utils/mockMongoDbClient';
import axios from 'axios';

// MongoDB connection string from environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

/**
 * Service for animal-related operations using MongoDB
 */
class AnimalMongoService extends MongoDbService<AnimalModel> {
  constructor() {
    super('animals');
  }

  /**
   * Get all animals
   */
  async getAllAnimals(): Promise<Animal[]> {
    try {
      // First try to get data from the API
      const response = await axios.get(`${API_BASE_URL}/animals`);
      if (response.data && Array.isArray(response.data)) {
        return response.data.map(toAnimal);
      }

      // If API fails or returns unexpected data, try direct MongoDB connection
      console.log('Falling back to direct MongoDB connection');
      const animals = await this.findAll();
      return animals.map(toAnimal);
    } catch (error) {
      console.error('Error in getAllAnimals:', error);
      // Last resort - try direct MongoDB connection
      try {
        const animals = await this.findAll();
        return animals.map(toAnimal);
      } catch (mongoError) {
        console.error('Error in direct MongoDB connection:', mongoError);
        return [];
      }
    }
  }

  /**
   * Get animal by ID
   * @param id Animal ID
   */
  async getAnimalById(id: string): Promise<Animal | null> {
    try {
      // First try to get data from the API
      const response = await axios.get(`${API_BASE_URL}/animals/${id}`);
      if (response.data && response.data.animal) {
        return toAnimal(response.data.animal);
      }

      // If API fails or returns unexpected data, try direct MongoDB connection
      console.log('Falling back to direct MongoDB connection for animal ID:', id);
      const animal = await this.findById(id);
      return animal ? toAnimal(animal) : null;
    } catch (error) {
      console.error(`Error in getAnimalById for ID ${id}:`, error);
      // Last resort - try direct MongoDB connection
      try {
        const animal = await this.findById(id);
        return animal ? toAnimal(animal) : null;
      } catch (mongoError) {
        console.error(`Error in direct MongoDB connection for animal ID ${id}:`, mongoError);
        return null;
      }
    }
  }

  /**
   * Create a new animal
   * @param animal Animal data
   */
  async createAnimal(animal: Animal): Promise<Animal> {
    const animalModel = toAnimalModel(animal);
    const result = await this.create(animalModel);
    return {
      ...animal,
      id: result.insertedId.toString()
    };
  }

  /**
   * Update an animal
   * @param id Animal ID
   * @param animal Animal data
   */
  async updateAnimal(id: string, animal: Partial<Animal>): Promise<boolean> {
    const { id: _, createdAt, updatedAt, ...rest } = animal;

    // Convert string dates to Date objects if they exist
    const updateData: Partial<AnimalModel> = {
      ...rest,
      updatedAt: new Date(),
      birthDate: rest.birthDate ? (typeof rest.birthDate === 'string' ? rest.birthDate : (rest.birthDate as Date).toISOString()) : undefined
    };

    const result = await this.updateById(id, updateData);
    return result.modifiedCount > 0;
  }

  /**
   * Delete an animal
   * @param id Animal ID
   */
  async deleteAnimal(id: string): Promise<boolean> {
    const result = await this.deleteById(id);
    return result.deletedCount > 0;
  }

  /**
   * Find animals by species
   * @param species Animal species
   */
  async findBySpecies(species: string): Promise<Animal[]> {
    const animals = await this.findAll({ species });
    return animals.map(toAnimal);
  }

  /**
   * Find animals by breed
   * @param breed Animal breed
   */
  async findByBreed(breed: string): Promise<Animal[]> {
    const animals = await this.findAll({ breed });
    return animals.map(toAnimal);
  }

  /**
   * Find animals by status
   * @param status Animal status
   */
  async findByStatus(status: string): Promise<Animal[]> {
    const animals = await this.findAll({ status });
    return animals.map(toAnimal);
  }

  /**
   * Find animals by age range
   * @param minAge Minimum age in months
   * @param maxAge Maximum age in months
   */
  async findByAgeRange(minAge: number, maxAge: number): Promise<Animal[]> {
    const animals = await this.findAll({
      ageInMonths: { $gte: minAge, $lte: maxAge }
    });
    return animals.map(toAnimal);
  }
}

export const animalMongoService = new AnimalMongoService();
