import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  SpeedDial, 
  SpeedDialIcon, 
  SpeedDialAction, 
  useTheme, 
  alpha,
  Tooltip,
  Typography
} from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  Add as AddIcon,
  Pets,
  LocalHospital,
  Restaurant,
  MonetizationOn,
  Spa,
  Store,
  Assignment,
  EventNote,
  BarChart,
  Settings
} from '@mui/icons-material';
import { ROUTES } from '../../constants/routes';

interface QuickActionsProps {
  position?: {
    bottom?: number | string;
    right?: number | string;
    top?: number | string;
    left?: number | string;
  };
}

/**
 * QuickActions Component
 * 
 * Provides a floating speed dial with quick access to common actions
 */
const QuickActions: React.FC<QuickActionsProps> = ({ 
  position = { bottom: 24, right: 24 } 
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  
  const [open, setOpen] = useState(false);
  const [activeTooltip, setActiveTooltip] = useState<string | null>(null);
  
  const handleOpen = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setActiveTooltip(null);
  };
  
  // Define quick actions
  const actions = [
    { 
      icon: <Pets />, 
      name: translate ? translate('quick_actions.add_animal', { fallback: 'Add Animal' }) : 'Add Animal',
      tooltip: translate ? translate('quick_actions.add_animal_desc', { fallback: 'Register a new animal' }) : 'Register a new animal',
      color: theme.palette.primary.main,
      action: () => navigate(`${ROUTES.ANIMALS}/new`)
    },
    { 
      icon: <LocalHospital />, 
      name: translate ? translate('quick_actions.health_record', { fallback: 'Health Record' }) : 'Health Record',
      tooltip: translate ? translate('quick_actions.health_record_desc', { fallback: 'Record a health event' }) : 'Record a health event',
      color: theme.palette.error.main,
      action: () => navigate(`${ROUTES.HEALTH}/new-record`)
    },
    { 
      icon: <Spa />, 
      name: translate ? translate('quick_actions.breeding', { fallback: 'Breeding' }) : 'Breeding',
      tooltip: translate ? translate('quick_actions.breeding_desc', { fallback: 'Record breeding activity' }) : 'Record breeding activity',
      color: theme.palette.secondary.main,
      action: () => navigate(`${ROUTES.BREEDING}/new-record`)
    },
    { 
      icon: <Restaurant />, 
      name: translate ? translate('quick_actions.feeding', { fallback: 'Feeding' }) : 'Feeding',
      tooltip: translate ? translate('quick_actions.feeding_desc', { fallback: 'Update feeding schedule' }) : 'Update feeding schedule',
      color: theme.palette.warning.main,
      action: () => navigate(`${ROUTES.FEED_MANAGEMENT}/schedules/new`)
    },
    { 
      icon: <MonetizationOn />, 
      name: translate ? translate('quick_actions.transaction', { fallback: 'Transaction' }) : 'Transaction',
      tooltip: translate ? translate('quick_actions.transaction_desc', { fallback: 'Record financial transaction' }) : 'Record financial transaction',
      color: theme.palette.info.main,
      action: () => navigate(`${ROUTES.FINANCIAL}/transactions/new`)
    },
    { 
      icon: <Store />, 
      name: translate ? translate('quick_actions.market', { fallback: 'Market' }) : 'Market',
      tooltip: translate ? translate('quick_actions.market_desc', { fallback: 'Create market listing' }) : 'Create market listing',
      color: theme.palette.success.dark,
      action: () => navigate(`${ROUTES.COMMERCIAL}/new-listing`)
    },
    { 
      icon: <Assignment />, 
      name: translate ? translate('quick_actions.task', { fallback: 'Task' }) : 'Task',
      tooltip: translate ? translate('quick_actions.task_desc', { fallback: 'Create new task' }) : 'Create new task',
      color: theme.palette.grey[700],
      action: () => navigate('/dashboard/tasks/new')
    }
  ];
  
  return (
    <Box sx={{ position: 'fixed', ...position, zIndex: 1050 }}>
      <SpeedDial
        ariaLabel="Quick Actions"
        icon={<SpeedDialIcon />}
        onClose={handleClose}
        onOpen={handleOpen}
        open={open}
        direction="up"
        FabProps={{
          sx: {
            bgcolor: themeColor.primary,
            '&:hover': {
              bgcolor: themeColor.dark,
            },
            boxShadow: `0 8px 16px ${alpha(themeColor.primary, 0.3)}`
          }
        }}
      >
        {actions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={
              <Box 
                sx={{ 
                  bgcolor: alpha(action.color, 0.1),
                  color: action.color,
                  borderRadius: '50%',
                  p: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {action.icon}
              </Box>
            }
            tooltipTitle={action.name}
            tooltipOpen
            onClick={() => {
              handleClose();
              action.action();
            }}
            FabProps={{
              onMouseEnter: () => setActiveTooltip(action.name),
              onMouseLeave: () => setActiveTooltip(null),
              sx: {
                bgcolor: 'background.paper',
                '&:hover': {
                  bgcolor: alpha(action.color, 0.1),
                  transform: 'scale(1.05)'
                },
                transition: 'all 0.3s ease'
              }
            }}
          />
        ))}
      </SpeedDial>
      
      {/* Enhanced tooltip with description */}
      <AnimatePresence>
        {open && activeTooltip && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
            style={{
              position: 'absolute',
              right: '100%',
              top: '50%',
              transform: 'translateY(-50%)',
              marginRight: 16,
              zIndex: 1060
            }}
          >
            <Box
              sx={{
                bgcolor: 'background.paper',
                boxShadow: theme.shadows[3],
                borderRadius: 2,
                p: 2,
                maxWidth: 200,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}
            >
              <Typography variant="subtitle2" fontWeight="bold">
                {activeTooltip}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {actions.find(a => a.name === activeTooltip)?.tooltip}
              </Typography>
            </Box>
          </motion.div>
        )}
      </AnimatePresence>
    </Box>
  );
};

export default QuickActions;
