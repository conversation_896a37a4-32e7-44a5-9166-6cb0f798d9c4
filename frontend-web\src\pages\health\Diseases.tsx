import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, LinearProgress, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  CalendarToday,
  CheckCircle,
  Warning,
  Info,
  LocalHospital,
  Notifications,
  Event
 } from '../../utils/iconImports';
import { mockAnimals } from '../../mocks/animalData';
import {  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AnimatedBackgroundCard , CustomButton } from '../../components/common';

interface DiseaseRecord {
  id: string;
  animalId: string;
  animalName: string;
  breed: string;
  diseaseName: string;
  diagnosisDate: string;
  symptoms: string[];
  severity: 'mild' | 'moderate' | 'severe';
  status: 'active' | 'recovering' | 'resolved' | 'chronic';
  treatment: string;
  diagnosedBy: string;
  quarantine: boolean;
  notes: string;
}

const Diseases: React.FC = () => {
  const theme = useTheme();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<DiseaseRecord | null>(null);
  const [diseaseRecords, setDiseaseRecords] = useState<DiseaseRecord[]>([
    {
      id: 'DIS001',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      breed: 'Nguni',
      diseaseName: 'Heartwater',
      diagnosisDate: '2024-03-01',
      symptoms: ['Fever', 'Neurological signs', 'Loss of appetite'],
      severity: 'moderate',
      status: 'recovering',
      treatment: 'Oxytetracycline treatment, supportive care',
      diagnosedBy: 'Dr. van Niekerk',
      quarantine: true,
      notes: 'Responding well to treatment, continue monitoring'
    },
    {
      id: 'DIS002',
      animalId: 'ANM002',
      animalName: 'Tumelo',
      breed: 'Bonsmara',
      diseaseName: 'Lumpy skin disease',
      diagnosisDate: '2024-02-15',
      symptoms: ['Skin nodules', 'Fever', 'Reduced milk production'],
      severity: 'moderate',
      status: 'recovering',
      treatment: 'Antibiotics for secondary infections, anti-inflammatory medication',
      diagnosedBy: 'Dr. Botha',
      quarantine: true,
      notes: 'Skin lesions beginning to heal'
    },
    {
      id: 'DIS003',
      animalId: 'ANM004',
      animalName: 'Katlego',
      breed: 'Dorper',
      diseaseName: 'Blue tongue',
      diagnosisDate: '2024-02-25',
      symptoms: ['Fever', 'Facial swelling', 'Oral lesions'],
      severity: 'severe',
      status: 'active',
      treatment: 'Supportive care, anti-inflammatory medication',
      diagnosedBy: 'Dr. Mkhize',
      quarantine: true,
      notes: 'Critical case, intensive monitoring required'
    },
    {
      id: 'DIS004',
      animalId: 'ANM008',
      animalName: 'Lerato',
      breed: 'Nguni',
      diseaseName: 'Redwater (Babesiosis)',
      diagnosisDate: '2024-01-20',
      symptoms: ['Fever', 'Anemia', 'Red urine'],
      severity: 'moderate',
      status: 'resolved',
      treatment: 'Diminazene aceturate, blood transfusion',
      diagnosedBy: 'Dr. van Niekerk',
      quarantine: false,
      notes: 'Fully recovered, maintain tick control'
    },
    {
      id: 'DIS005',
      animalId: 'ANM010',
      animalName: 'Naledi',
      breed: 'Bonsmara',
      diseaseName: 'Foot rot',
      diagnosisDate: '2024-03-05',
      symptoms: ['Lameness', 'Swollen foot', 'Foul odor'],
      severity: 'mild',
      status: 'active',
      treatment: 'Hoof trimming, topical antibiotics, foot bath',
      diagnosedBy: 'Dr. Viljoen',
      quarantine: false,
      notes: 'Early detection, good prognosis'
    }
  ]);

  const handleAddNew = () => {
    setSelectedRecord(null);
    setOpenDialog(true);
  };

  const handleEdit = (record: DiseaseRecord) => {
    setSelectedRecord(record);
    setOpenDialog(true);
  };

  const handleDelete = (id: string) => {
    setDiseaseRecords(diseaseRecords.filter(record => record.id !== id));
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'active':
        return <Chip label="Active" color="error" size="small" />;
      case 'recovering':
        return <Chip label="Recovering" color="warning" size="small" />;
      case 'resolved':
        return <Chip label="Resolved" color="success" size="small" />;
      case 'chronic':
        return <Chip label="Chronic" color="secondary" size="small" />;
      default:
        return <Chip label={status} size="small" />;
    }
  };

  const getSeverityChip = (severity: string) => {
    switch (severity) {
      case 'mild':
        return <Chip label="Mild" color="success" size="small" />;
      case 'moderate':
        return <Chip label="Moderate" color="warning" size="small" />;
      case 'severe':
        return <Chip label="Severe" color="error" size="small" />;
      default:
        return <Chip label={severity} size="small" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDiseasesByType = () => {
    const diseaseTypes: Record<string, number> = {};

    diseaseRecords.forEach(record => {
      if (diseaseTypes[record.diseaseName]) {
        diseaseTypes[record.diseaseName]++;
      } else {
        diseaseTypes[record.diseaseName] = 1;
      }
    });

    return Object.entries(diseaseTypes).map(([type, count]) => ({ type, count }));
  };

  const getActiveQuarantineCount = () => {
    return diseaseRecords.filter(record => record.quarantine && (record.status === 'active' || record.status === 'recovering')).length;
  };

  return (
    <Box>
      <ModuleHeader
        title="Disease Management"
        subtitle="Track and manage livestock diseases, treatments, and health status"
        module="health"
        submodule="diseases"
        actionLabel="Add Disease Record"
        actionIcon={<Add />}
        onAction={handleAddNew}
      />

      <Box sx={{ px: 3, pb: 5 }}>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Disease Status"
              module="health"
              uniqueId="disease-status"
              icon={<LocalHospital />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="error.main">
                        {diseaseRecords.filter(record => record.status === 'active').length}
                      </Typography>
                      <Typography variant="body2">Active Cases</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(255, 152, 0, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="warning.main">
                        {diseaseRecords.filter(record => record.status === 'recovering').length}
                      </Typography>
                      <Typography variant="body2">Recovering</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {diseaseRecords.filter(record => record.status === 'resolved').length}
                      </Typography>
                      <Typography variant="body2">Resolved</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(156, 39, 176, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="secondary.main">
                        {diseaseRecords.filter(record => record.status === 'chronic').length}
                      </Typography>
                      <Typography variant="body2">Chronic</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Quarantine Status
                  </Typography>
                  <Box p={2} bgcolor={getActiveQuarantineCount() > 0 ? "rgba(211, 47, 47, 0.1)" : "rgba(46, 125, 50, 0.1)"} borderRadius={1} display="flex" alignItems="center" justifyContent="space-between">
                    <Typography variant="body1" fontWeight="medium">
                      Animals in Quarantine
                    </Typography>
                    <Chip
                      label={getActiveQuarantineCount()}
                      color={getActiveQuarantineCount() > 0 ? "error" : "success"}
                    />
                  </Box>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Disease Types"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<Info />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2}>
                {getDiseasesByType().map(({ type, count }) => (
                  <Box key={type} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle2" fontWeight="bold">
                        {type}
                      </Typography>
                      <Chip label={count} size="small" color="primary" />
                    </Box>
                  </Box>
                ))}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Critical Cases"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<Warning />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2}>
                {diseaseRecords
                  .filter(record => record.severity === 'severe' && record.status === 'active')
                  .map(record => (
                    <Box key={record.id} mb={2} p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {record.animalName} - {record.diseaseName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Diagnosed: {formatDate(record.diagnosisDate)}
                        </Typography>
                        {getSeverityChip(record.severity)}
                      </Box>
                      <Box mt={1}>
                        <Typography variant="body2" color="text.secondary">
                          {record.notes}
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                {diseaseRecords.filter(record => record.severity === 'severe' && record.status === 'active').length === 0 && (
                  <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                    <Typography variant="body2" color="text.secondary">No critical cases</Typography>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent sx={{ p: 0 }}>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                  <TableCell sx={{ color: 'white' }}>Animal</TableCell>
                  <TableCell sx={{ color: 'white' }}>Breed</TableCell>
                  <TableCell sx={{ color: 'white' }}>Disease</TableCell>
                  <TableCell sx={{ color: 'white' }}>Diagnosis Date</TableCell>
                  <TableCell sx={{ color: 'white' }}>Severity</TableCell>
                  <TableCell sx={{ color: 'white' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white' }}>Quarantine</TableCell>
                  <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {diseaseRecords.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>{record.animalName}</TableCell>
                    <TableCell>{record.breed}</TableCell>
                    <TableCell>{record.diseaseName}</TableCell>
                    <TableCell>{formatDate(record.diagnosisDate)}</TableCell>
                    <TableCell>{getSeverityChip(record.severity)}</TableCell>
                    <TableCell>{getStatusChip(record.status)}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.quarantine ? "Yes" : "No"}
                        color={record.quarantine ? "error" : "default"}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex">
                        <IconButton size="small" color="primary" onClick={() => handleEdit(record)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small" color="error" onClick={() => handleDelete(record.id)}>
                          <Delete fontSize="small" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          </CardContent>
        </Card>
      </Box>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedRecord ? 'Edit Disease Record' : 'Add New Disease Record'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="animal-select-label">Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  label="Animal"
                  defaultValue={selectedRecord?.animalId || ''}
                >
                  {mockAnimals.map(animal => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.name} ({animal.breed})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="disease-select-label">Disease</InputLabel>
                <Select
                  labelId="disease-select-label"
                  id="disease-select"
                  label="Disease"
                  defaultValue={selectedRecord?.diseaseName || ''}
                >
                  <MenuItem value="Heartwater">Heartwater</MenuItem>
                  <MenuItem value="Redwater (Babesiosis)">Redwater (Babesiosis)</MenuItem>
                  <MenuItem value="Corridor disease">Corridor disease</MenuItem>
                  <MenuItem value="Lumpy skin disease">Lumpy skin disease</MenuItem>
                  <MenuItem value="Blue tongue">Blue tongue</MenuItem>
                  <MenuItem value="Foot rot">Foot rot</MenuItem>
                  <MenuItem value="Mastitis">Mastitis</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="diagnosisDate"
                label="Diagnosis Date"
                type="date"
                defaultValue={selectedRecord?.diagnosisDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="severity-select-label">Severity</InputLabel>
                <Select
                  labelId="severity-select-label"
                  id="severity-select"
                  label="Severity"
                  defaultValue={selectedRecord?.severity || 'mild'}
                >
                  <MenuItem value="mild">Mild</MenuItem>
                  <MenuItem value="moderate">Moderate</MenuItem>
                  <MenuItem value="severe">Severe</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-select-label">Status</InputLabel>
                <Select
                  labelId="status-select-label"
                  id="status-select"
                  label="Status"
                  defaultValue={selectedRecord?.status || 'active'}
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="recovering">Recovering</MenuItem>
                  <MenuItem value="resolved">Resolved</MenuItem>
                  <MenuItem value="chronic">Chronic</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="quarantine-select-label">Quarantine</InputLabel>
                <Select
                  labelId="quarantine-select-label"
                  id="quarantine-select"
                  label="Quarantine"
                  defaultValue={selectedRecord?.quarantine ? "yes" : "no"}
                >
                  <MenuItem value="yes">Yes</MenuItem>
                  <MenuItem value="no">No</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="symptoms"
                label="Symptoms (comma separated)"
                defaultValue={selectedRecord?.symptoms.join(', ') || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="treatment"
                label="Treatment"
                multiline
                rows={2}
                defaultValue={selectedRecord?.treatment || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="diagnosedBy"
                label="Diagnosed By"
                defaultValue={selectedRecord?.diagnosedBy || ''}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={3}
                defaultValue={selectedRecord?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton variant="contained" color="primary" onClick={handleCloseDialog}>
            {selectedRecord ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Diseases;
