import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { CircularProgress, Box, Typography } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactElement;
  requiredPermission?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermission
}): React.ReactElement => {
  const { user, isLoading, hasPermission } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [redirecting, setRedirecting] = useState(false);

  useEffect(() => {
    // Prevent multiple redirects
    if (redirecting) return;

    // Check if token exists in localStorage
    const token = localStorage.getItem('token');

    // If not loading and no user or no token, redirect to login
    if (!isLoading && (!user || !token)) {
      console.log('No authenticated user, redirecting to login');
      setRedirecting(true);

      // Small timeout to prevent immediate redirect and allow state to update
      const timeoutId = setTimeout(() => {
        navigate('/login', {
          state: { from: location.pathname },
          replace: true
        });
      }, 100);

      return () => clearTimeout(timeoutId);
    }

    // If user exists but doesn't have required permission
    if (!isLoading && user && requiredPermission && !hasPermission(requiredPermission)) {
      console.log(`User lacks permission: ${requiredPermission}, redirecting to dashboard`);
      setRedirecting(true);

      // Small timeout to prevent immediate redirect and allow state to update
      const timeoutId = setTimeout(() => {
        // Redirect to dashboard with access denied message
        navigate('/dashboard', {
          state: {
            accessDenied: true,
            message: `You don't have permission to access ${location.pathname}`
          },
          replace: true
        });
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [user, isLoading, location.pathname, navigate, requiredPermission, hasPermission, redirecting]);

  // Show loading indicator while checking authentication
  if (isLoading || redirecting) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body1" color="textSecondary">
          {redirecting ? 'Redirecting...' : 'Loading...'}
        </Typography>
      </Box>
    );
  }

  // If no user or missing permission, don't render children (will redirect in useEffect)
  if (!user || (requiredPermission && !hasPermission(requiredPermission))) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography variant="body1" color="textSecondary">
          Verifying access...
        </Typography>
      </Box>
    );
  }

  // User is authenticated and has permission
  return children;
};

export default ProtectedRoute;
