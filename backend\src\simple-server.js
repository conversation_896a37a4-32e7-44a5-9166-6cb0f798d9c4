const express = require('express');
const cors = require('cors');
const path = require('path');
const jwt = require('jsonwebtoken');

const app = express();
// Hardcode the port to 3001 to avoid any environment variable issues
const PORT = 3001;
console.log(`Using hardcoded port: ${PORT}`);

// Configure CORS
const corsOptions = {
  origin: '*', // Allow all origins for testing
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
console.log('CORS configured with options:', corsOptions);
app.use(cors(corsOptions));
app.use(express.json());

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Add a simple health check route
app.get('/health', (req, res) => {
  console.log('Health check endpoint called');
  res.status(200).json({ status: 'ok' });
});

// Add a root route for testing
app.get('/', (req, res) => {
  console.log('Root endpoint called');
  res.status(200).json({ message: 'MayCaiphus Livestock Management API' });
});

// Add an API status endpoint
app.get('/api/status', (req, res) => {
  console.log('API status endpoint called');
  res.status(200).json({ status: 'ok', message: 'API is running' });
});

// Mock user data
const users = [
  {
    id: '1',
    username: 'admin',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME',
    role: 'admin',
    name: 'May Rakgama',
    email: '<EMAIL>',
    permissions: ['all']
  },
  {
    id: '2',
    username: 'manager',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME',
    role: 'manager',
    name: 'Manager User',
    email: '<EMAIL>',
    permissions: ['view_all', 'edit_all']
  },
  {
    id: '3',
    username: 'staff',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME',
    role: 'staff',
    name: 'Staff User',
    email: '<EMAIL>',
    permissions: ['view_all']
  },
  {
    id: '4',
    username: 'vet',
    password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME',
    role: 'veterinarian',
    name: 'Veterinarian User',
    email: '<EMAIL>',
    permissions: ['view_health', 'edit_health']
  }
];

// Authentication routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  console.log(`Login attempt for ${username}`);

  // Find user
  const user = users.find(u => u.username === username && u.password === password);

  if (!user) {
    console.log(`Login failed for ${username}: Invalid credentials`);
    return res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }

  console.log(`Login successful for ${username}`);

  // Generate JWT token
  const token = jwt.sign(
    { id: user.id, username: user.username, role: user.role },
    'maycaiphus-livestock-secret-key',
    { expiresIn: '24h' }
  );

  // Return user info and token in the format expected by the frontend
  res.json({
    success: true,
    user: {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      permissions: user.permissions
    },
    token
  });
});

// Protected route example
app.get('/api/auth/me', (req, res) => {
  // Get token from header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }

  const token = authHeader.split(' ')[1];

  try {
    // Verify token
    const decoded = jwt.verify(
      token,
      'maycaiphus-livestock-secret-key'
    );

    // Find user
    const user = users.find(u => u.id === decoded.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Return user info in the format expected by the frontend
    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      }
    });
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
});

// Start the server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server listening on 0.0.0.0:${PORT}`);
});

module.exports = app; // Export for testing
