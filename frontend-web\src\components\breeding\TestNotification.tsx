import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, TextField, FormControl, InputLabel, Select, MenuItem, Chip, FormControlLabel, Switch, CircularProgress, Alert, Divider, useTheme, alpha, Grid } from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { format, addDays, addHours, setHours, setMinutes } from 'date-fns';
import {
  Notifications as NotificationsIcon,
  Phone as PhoneIcon,
  RecordVoiceOver as VoiceIcon,
  Email as EmailIcon,
  WhatsApp as WhatsAppIcon,
  Send as SendIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon
} from '../../utils/iconImports';
import { testNotificationService } from '../../services/testNotificationService';
import { NotificationType } from '../../services/notificationService';
import { useSnackbar } from '../../hooks/useSnackbar';
import { notificationHistoryService } from '../../services/notificationHistoryService';
import { useRuralNotificationService } from '../../services/ruralNotificationService';

interface TestNotificationProps {
  onNotificationSent?: () => void;
}

const TestNotification: React.FC<TestNotificationProps> = ({ onNotificationSent }) => {
  const theme = useTheme();
  const { showSnackbar } = useSnackbar();
  const ruralNotificationService = useRuralNotificationService();

  // Animal information
  const [animalId, setAnimalId] = useState('CTL001');
  const [animalName, setAnimalName] = useState('Bella');

  // Birth date and time
  const tomorrow = addDays(new Date(), 1);
  const birthDateTime = setHours(setMinutes(tomorrow, 0), 6); // Tomorrow at 6:00 AM
  const [selectedBirthDateTime, setSelectedBirthDateTime] = useState<Date>(birthDateTime);

  // Notification time
  const today = new Date();
  const notificationTime = setHours(setMinutes(today, 0), 17); // Today at 17:00
  const [selectedNotificationTime, setSelectedNotificationTime] = useState<Date>(notificationTime);

  // Recipient information
  const [recipientName, setRecipientName] = useState('Farm Manager');
  const [recipientPhone, setRecipientPhone] = useState('0761246783'); // Changed to first additional number
  const [recipientEmail, setRecipientEmail] = useState('');

  // Additional recipients for critical alerts
  const additionalPhones = ['0828808204', '0766535887'];
  const [useAdditionalRecipients, setUseAdditionalRecipients] = useState(true);

  // Notification types
  const [selectedNotificationTypes, setSelectedNotificationTypes] = useState<NotificationType[]>(['sms', 'voice', 'whatsapp']);

  // Offline mode for rural areas with poor connectivity
  const [offlineMode, setOfflineMode] = useState(true);

  // Send immediately or schedule
  const [sendImmediately, setSendImmediately] = useState(false);

  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Result state
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    scheduledTime?: Date;
  } | null>(null);

  // Handle sending the test notification
  const handleSendTestNotification = async () => {
    setIsLoading(true);
    setResult(null);

    try {
      // Set offline mode based on user preference
      ruralNotificationService.setOfflineMode(offlineMode);

      if (sendImmediately) {
        // Send notification immediately using rural notification service
        const daysRemaining = Math.ceil((selectedBirthDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

        const results = await ruralNotificationService.sendBirthPredictionAlert({
          animalId,
          animalName,
          daysRemaining,
          expectedDueDate: selectedBirthDateTime,
          riskLevel: daysRemaining <= 3 ? 'high' : daysRemaining <= 7 ? 'medium' : 'low',
          specialCare: [
            'Regular monitoring',
            'Ensure clean water supply',
            'Prepare calving area'
          ],
          recipient: {
            name: recipientName,
            phone: recipientPhone,
            email: recipientEmail
          },
          notificationTypes: selectedNotificationTypes
        });

        // Check results
        const successCount = results.filter(r => r.result.success).length;

        if (successCount === results.length) {
          setResult({
            success: true,
            message: `All ${successCount} notifications sent successfully`
          });
          showSnackbar(`All ${successCount} notifications sent successfully`, 'success');
        } else if (successCount > 0) {
          setResult({
            success: true,
            message: `${successCount}/${results.length} notifications sent successfully`
          });
          showSnackbar(`${successCount}/${results.length} notifications sent successfully`, 'warning');
        } else {
          setResult({
            success: false,
            message: 'Failed to send notifications'
          });
          showSnackbar('Failed to send notifications', 'error');
        }
      } else {
        // Schedule notification
        const scheduleResult = await testNotificationService.scheduleTestNotification(
          animalId,
          animalName,
          selectedBirthDateTime,
          selectedNotificationTime,
          {
            name: recipientName,
            phone: recipientPhone,
            email: recipientEmail
          },
          selectedNotificationTypes
        );

        setResult({
          success: scheduleResult.success,
          message: scheduleResult.message,
          scheduledTime: scheduleResult.scheduledTime
        });

        if (scheduleResult.success) {
          showSnackbar(scheduleResult.message, 'success');
        } else {
          showSnackbar(scheduleResult.message, 'error');
        }
      }

      // Notify parent component
      if (onNotificationSent) {
        onNotificationSent();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setResult({
        success: false,
        message: `Error: ${errorMessage}`
      });
      showSnackbar(`Error: ${errorMessage}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  // Get notification type icon
  const getNotificationTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'sms':
        return <PhoneIcon fontSize="small" />;
      case 'voice':
        return <VoiceIcon fontSize="small" />;
      case 'email':
        return <EmailIcon fontSize="small" />;
      case 'whatsapp':
        return <WhatsAppIcon fontSize="small" />;
      case 'app':
        return <NotificationsIcon fontSize="small" />;
      default:
        return <NotificationsIcon fontSize="small" />;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Card
        sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          overflow: 'hidden',
          mb: 4,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
        }}
      >
        <CardContent>
          <Box display="flex" alignItems="center" mb={2}>
            <NotificationsIcon color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              Test Birth Notification
            </Typography>
          </Box>

          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {/* Animal Information */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Animal Information
              </Typography>

              <TextField
                label="Animal ID"
                fullWidth
                value={animalId}
                onChange={(e) => setAnimalId(e.target.value)}
                margin="normal"
                required
              />

              <TextField
                label="Animal Name"
                fullWidth
                value={animalName}
                onChange={(e) => setAnimalName(e.target.value)}
                margin="normal"
                required
              />

              <DateTimePicker
                label="Expected Birth Date & Time"
                value={selectedBirthDateTime}
                onChange={(newValue) => newValue && setSelectedBirthDateTime(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    margin: 'normal',
                    required: true
                  }
                }}
              />
            </Grid>

            {/* Recipient Information */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Recipient Information
              </Typography>

              <TextField
                label="Recipient Name"
                fullWidth
                value={recipientName}
                onChange={(e) => setRecipientName(e.target.value)}
                margin="normal"
                required
              />

              <TextField
                label="Phone Number"
                fullWidth
                value={recipientPhone}
                onChange={(e) => setRecipientPhone(e.target.value)}
                margin="normal"
                required={selectedNotificationTypes.some(t => t === 'sms' || t === 'voice' || t === 'whatsapp')}
                helperText="Required for SMS, voice calls, and WhatsApp"
              />

              <TextField
                label="Email Address"
                fullWidth
                value={recipientEmail}
                onChange={(e) => setRecipientEmail(e.target.value)}
                margin="normal"
                required={selectedNotificationTypes.includes('email')}
                helperText="Required for email notifications"
              />
            </Grid>

            {/* Notification Settings */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Notification Settings
              </Typography>

              <FormControl fullWidth margin="normal">
                <InputLabel id="notification-types-label">Notification Methods</InputLabel>
                <Select
                  labelId="notification-types-label"
                  multiple
                  value={selectedNotificationTypes}
                  onChange={(e) => setSelectedNotificationTypes(e.target.value as NotificationType[])}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip
                          key={value}
                          label={value.toUpperCase()}
                          size="small"
                          icon={getNotificationTypeIcon(value)}
                        />
                      ))}
                    </Box>
                  )}
                >
                  <MenuItem value="sms">
                    <Chip
                      icon={<PhoneIcon />}
                      label="SMS"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2">Text message alerts</Typography>
                  </MenuItem>
                  <MenuItem value="voice">
                    <Chip
                      icon={<VoiceIcon />}
                      label="VOICE"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2">Automated voice calls</Typography>
                  </MenuItem>
                  <MenuItem value="whatsapp">
                    <Chip
                      icon={<WhatsAppIcon />}
                      label="WHATSAPP"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2">WhatsApp messages</Typography>
                  </MenuItem>
                  <MenuItem value="email">
                    <Chip
                      icon={<EmailIcon />}
                      label="EMAIL"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2">Email notifications</Typography>
                  </MenuItem>
                  <MenuItem value="app">
                    <Chip
                      icon={<NotificationsIcon />}
                      label="APP"
                      size="small"
                      sx={{ mr: 1 }}
                    />
                    <Typography variant="body2">In-app notifications</Typography>
                  </MenuItem>
                </Select>
              </FormControl>

              <Box mt={2} mb={2} display="flex" flexDirection="column" gap={1}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={sendImmediately}
                      onChange={(e) => setSendImmediately(e.target.checked)}
                      color="primary"
                    />
                  }
                  label="Send immediately"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={offlineMode}
                      onChange={(e) => setOfflineMode(e.target.checked)}
                      color="secondary"
                    />
                  }
                  label="Offline mode (for rural areas with poor connectivity)"
                />

                <FormControlLabel
                  control={
                    <Switch
                      checked={useAdditionalRecipients}
                      onChange={(e) => setUseAdditionalRecipients(e.target.checked)}
                      color="warning"
                    />
                  }
                  label="Include additional recipients for critical alerts"
                />
              </Box>

              {!sendImmediately && (
                <DateTimePicker
                  label="Notification Time"
                  value={selectedNotificationTime}
                  onChange={(newValue) => newValue && setSelectedNotificationTime(newValue)}
                  slotProps={{
                    textField: {
                      fullWidth: true,
                      margin: 'normal',
                      required: true
                    }
                  }}
                />
              )}
            </Grid>

            {/* Actions */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="flex-end" mt={2}>
                <CustomButton
                  variant="contained"
                  color="primary"
                  startIcon={sendImmediately ? <SendIcon /> : <ScheduleIcon />}
                  onClick={handleSendTestNotification}
                  disabled={isLoading ||
                    !animalId ||
                    !animalName ||
                    !selectedBirthDateTime ||
                    !recipientName ||
                    (selectedNotificationTypes.some(t => t === 'sms' || t === 'voice' || t === 'whatsapp') && !recipientPhone) ||
                    (selectedNotificationTypes.includes('email') && !recipientEmail) ||
                    (selectedNotificationTypes.length === 0) ||
                    (!sendImmediately && !selectedNotificationTime)
                  }
                >
                  {isLoading ? (
                    <>
                      <CircularProgress size={24} color="inherit" sx={{ mr: 1 }} />
                      Processing...
                    </>
                  ) : (
                    sendImmediately ? 'Send Now' : 'Schedule Notification'
                  )}
                </CustomButton>
              </Box>
            </Grid>

            {/* Result */}
            {result && (
              <Grid item xs={12}>
                <Alert severity={result.success ? 'success' : 'error'} sx={{ mt: 2 }}>
                  {result.message}
                  {result.scheduledTime && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Scheduled for: {format(result.scheduledTime, 'yyyy-MM-dd HH:mm:ss')}
                    </Typography>
                  )}
                </Alert>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    </LocalizationProvider>
  );
};

export default TestNotification;
