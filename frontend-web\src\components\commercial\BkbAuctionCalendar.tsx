import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, Grid, IconButton, Chip, Divider, CircularProgress, useTheme, alpha, Tooltip, ToggleButtonGroup, ToggleButton } from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  CalendarMonth,
  ViewList,
  Refresh,
  FilterList,
  OpenInNew
} from '../../utils/iconImports';
import { useBkbAuctions } from '../../contexts/commercial/BkbAuctionContext';
import { BkbAuction } from '../../types/commercial/bkbAuction';
import { format, isSameDay, addDays } from 'date-fns';
import BkbAuctionFilters from './BkbAuctionFilters';
import { useLanguage } from '../../contexts/LanguageContext';

const BkbAuctionCalendar: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const {
    currentMonth,
    loading,
    error,
    nextMonth,
    prevMonth,
    currentDate,
    refreshData,
    subscribeToCalendar,
    fetchMonth
  } = useBkbAuctions();

  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // Initialize with tomorrow's date
  useEffect(() => {
    const tomorrow = addDays(new Date(), 1);
    fetchMonth(tomorrow.getFullYear(), tomorrow.getMonth());
  }, [fetchMonth]);

  const handleViewChange = (
    _: React.MouseEvent<HTMLElement>,
    newView: 'calendar' | 'list' | null
  ) => {
    if (newView !== null) {
      setViewMode(newView);
    }
  };

  const handleSubscribe = () => {
    window.open(subscribeToCalendar(), '_blank');
  };

  const getAuctionColor = (auction: BkbAuction) => {
    switch (auction.type) {
      case 'weekly':
        return theme.palette.primary.main;
      case 'monthly':
        return theme.palette.secondary.main;
      case 'fortnightly':
        return theme.palette.success.main;
      case 'special':
        return theme.palette.warning.main;
      case 'prime':
        return theme.palette.info.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const renderCalendarHeader = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'space-between' }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <IconButton onClick={prevMonth} disabled={loading}>
          <ChevronLeft />
        </IconButton>

        <Typography variant="h5" component="h2" sx={{ mx: 2, fontWeight: 'bold' }}>
          {format(currentDate, 'MMMM yyyy')}
        </Typography>

        <IconButton onClick={nextMonth} disabled={loading}>
          <ChevronRight />
        </IconButton>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title={translate ? translate('commercial.bkb.refresh', { fallback: 'Refresh data' }) : 'Refresh data'}>
          <IconButton onClick={refreshData} disabled={loading} color="primary">
            <Refresh />
          </IconButton>
        </Tooltip>

        <Tooltip title={translate ? translate('commercial.bkb.filter', { fallback: 'Filter auctions' }) : 'Filter auctions'}>
          <IconButton
            onClick={() => setShowFilters(!showFilters)}
            color={showFilters ? 'primary' : 'default'}
          >
            <FilterList />
          </IconButton>
        </Tooltip>

        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewChange}
          size="small"
        >
          <ToggleButton value="calendar">
            <CalendarMonth fontSize="small" />
          </ToggleButton>
          <ToggleButton value="list">
            <ViewList fontSize="small" />
          </ToggleButton>
        </ToggleButtonGroup>

        <CustomButton
          variant="contained"
          color="primary"
          size="small"
          onClick={handleSubscribe}
          endIcon={<OpenInNew />}
          sx={{ ml: 1 }}
        >
          {translate ? translate('commercial.bkb.subscribe', { fallback: 'Subscribe' }) : 'Subscribe'}
        </CustomButton>
      </Box>
    </Box>
  );

  const renderAuctionItem = (auction: BkbAuction) => {
    const auctionColor = getAuctionColor(auction);

    return (
      <Box
        key={auction.id}
        sx={{
          p: 1,
          mb: 1,
          borderLeft: `3px solid ${auctionColor}`,
          backgroundColor: alpha(auctionColor, 0.05),
          borderRadius: 1,
          '&:hover': {
            backgroundColor: alpha(auctionColor, 0.1),
            cursor: 'pointer'
          }
        }}
        onClick={() => window.open(auction.url, '_blank')}
      >
        <Typography variant="subtitle2" fontWeight="bold" noWrap>
          {auction.title}
        </Typography>
        <Typography variant="caption" display="block" color="text.secondary">
          {auction.startTime} - {auction.endTime}
        </Typography>
        <Box sx={{ mt: 0.5, display: 'flex', gap: 0.5 }}>
          <Chip
            label={auction.type.toUpperCase()}
            size="small"
            sx={{
              height: 20,
              fontSize: '0.6rem',
              backgroundColor: alpha(auctionColor, 0.2),
              color: theme.palette.getContrastText(alpha(auctionColor, 0.2))
            }}
          />
          <Chip
            label={auction.location}
            size="small"
            sx={{
              height: 20,
              fontSize: '0.6rem',
              backgroundColor: alpha(theme.palette.grey[500], 0.1)
            }}
          />
        </Box>
      </Box>
    );
  };

  const renderCalendarView = () => {
    if (!currentMonth) return null;

    // Create header row with day names
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    // Get the first day of the month
    const firstDay = new Date(currentMonth.year, currentMonth.month, 1).getDay();
    // Adjust for Monday as first day (0 = Monday, 6 = Sunday)
    const adjustedFirstDay = firstDay === 0 ? 6 : firstDay - 1;

    // Create calendar grid
    const calendarDays = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < adjustedFirstDay; i++) {
      calendarDays.push(
        <Grid item xs={12 / 7} key={`empty-${i}`}>
          <Paper
            elevation={0}
            sx={{
              height: 120,
              p: 1,
              backgroundColor: alpha(theme.palette.grey[200], 0.3),
              borderRadius: 1
            }}
          />
        </Grid>
      );
    }

    // Add cells for each day of the month
    currentMonth.days.forEach((day) => {
      const isToday = isSameDay(day.date, new Date());
      const isTomorrow = isSameDay(day.date, addDays(new Date(), 1));

      calendarDays.push(
        <Grid item xs={12 / 7} key={day.date.toISOString()}>
          <Paper
            elevation={0}
            sx={{
              height: 120,
              p: 1,
              backgroundColor: isToday
                ? alpha(theme.palette.primary.main, 0.05)
                : isTomorrow
                  ? alpha(theme.palette.secondary.main, 0.05)
                  : 'white',
              border: isToday
                ? `1px solid ${theme.palette.primary.main}`
                : isTomorrow
                  ? `1px solid ${theme.palette.secondary.main}`
                  : '1px solid transparent',
              borderRadius: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontWeight: isToday || isTomorrow ? 'bold' : 'normal',
                  color: isToday
                    ? theme.palette.primary.main
                    : isTomorrow
                      ? theme.palette.secondary.main
                      : 'inherit',
                }}
              >
                {format(day.date, 'd')}
              </Typography>

              {isToday && (
                <Chip
                  label={translate ? translate('common.today', { fallback: 'Today' }) : 'Today'}
                  size="small"
                  color="primary"
                  sx={{ height: 16, fontSize: '0.6rem' }}
                />
              )}

              {isTomorrow && (
                <Chip
                  label={translate ? translate('common.tomorrow', { fallback: 'Tomorrow' }) : 'Tomorrow'}
                  size="small"
                  color="secondary"
                  sx={{ height: 16, fontSize: '0.6rem' }}
                />
              )}
            </Box>

            <Box sx={{ overflowY: 'auto', flex: 1 }}>
              {day.auctions.map(auction => renderAuctionItem(auction))}
            </Box>
          </Paper>
        </Grid>
      );
    });

    return (
      <Box>
        <Grid container spacing={1} sx={{ mb: 1 }}>
          {dayNames.map(day => (
            <Grid item xs={12 / 7} key={day}>
              <Typography
                variant="subtitle2"
                align="center"
                sx={{ fontWeight: 'bold', color: theme.palette.text.secondary }}
              >
                {translate ? translate(`common.days.${day.toLowerCase()}`, { fallback: day }) : day}
              </Typography>
            </Grid>
          ))}
        </Grid>

        <Grid container spacing={1}>
          {calendarDays}
        </Grid>
      </Box>
    );
  };

  const renderListView = () => {
    if (!currentMonth) return null;

    // Flatten all auctions from the month
    const allAuctions = currentMonth.days.flatMap(day =>
      day.auctions.map(auction => ({
        ...auction,
        date: day.date
      }))
    );

    // Sort by date
    allAuctions.sort((a, b) => a.date.getTime() - b.date.getTime());

    if (allAuctions.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            {translate ? translate('commercial.bkb.no_auctions', { fallback: 'No auctions found for this month' }) : 'No auctions found for this month'}
          </Typography>
        </Box>
      );
    }

    // Group by date
    const auctionsByDate: Record<string, BkbAuction[]> = {};

    allAuctions.forEach(auction => {
      const dateKey = format(auction.date, 'yyyy-MM-dd');
      if (!auctionsByDate[dateKey]) {
        auctionsByDate[dateKey] = [];
      }
      auctionsByDate[dateKey].push(auction);
    });

    return (
      <Box>
        {Object.entries(auctionsByDate).map(([dateKey, auctions]) => {
          const date = new Date(dateKey);
          const isToday = isSameDay(date, new Date());
          const isTomorrow = isSameDay(date, addDays(new Date(), 1));

          return (
            <Box key={dateKey} sx={{ mb: 3 }}>
              <Typography
                variant="subtitle1"
                sx={{
                  fontWeight: 'bold',
                  color: isToday
                    ? theme.palette.primary.main
                    : isTomorrow
                      ? theme.palette.secondary.main
                      : 'inherit',
                  display: 'flex',
                  alignItems: 'center',
                  mb: 1
                }}
              >
                {format(date, 'EEEE, MMMM d, yyyy')}
                {isToday && (
                  <Chip
                    label={translate ? translate('common.today', { fallback: 'Today' }) : 'Today'}
                    size="small"
                    color="primary"
                    sx={{ ml: 1, height: 20 }}
                  />
                )}
                {isTomorrow && (
                  <Chip
                    label={translate ? translate('common.tomorrow', { fallback: 'Tomorrow' }) : 'Tomorrow'}
                    size="small"
                    color="secondary"
                    sx={{ ml: 1, height: 20 }}
                  />
                )}
              </Typography>

              <Divider sx={{ mb: 1 }} />

              <Grid container spacing={2}>
                {auctions.map(auction => (
                  <Grid item xs={12} sm={6} md={4} key={auction.id}>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        borderTop: `3px solid ${getAuctionColor(auction)}`,
                        height: '100%',
                        '&:hover': {
                          boxShadow: theme.shadows[3],
                          cursor: 'pointer'
                        }
                      }}
                      onClick={() => window.open(auction.url, '_blank')}
                    >
                      <Typography variant="h6" gutterBottom>
                        {auction.title}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <Chip
                          label={auction.type.toUpperCase()}
                          size="small"
                          sx={{ mr: 1 }}
                          color="primary"
                        />
                        <Chip
                          label={auction.category.toUpperCase()}
                          size="small"
                          color="secondary"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        <strong>{translate ? translate('commercial.bkb.location', { fallback: 'Location' }) : 'Location'}:</strong> {auction.location}
                      </Typography>

                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        <strong>{translate ? translate('commercial.bkb.time', { fallback: 'Time' }) : 'Time'}:</strong> {auction.startTime} - {auction.endTime}
                      </Typography>

                      {auction.description && (
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          {auction.description}
                        </Typography>
                      )}
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </Box>
          );
        })}
      </Box>
    );
  };

  /**
   * Render error state with retry button
   */
  const renderError = () => (
    <Box
      sx={{
        textAlign: 'center',
        py: 4,
        px: 2,
        borderRadius: 2,
        backgroundColor: alpha(theme.palette.error.light, 0.1),
        border: `1px solid ${alpha(theme.palette.error.main, 0.3)}`
      }}
    >
      <Typography variant="h6" color="error" gutterBottom>
        {translate ? translate('commercial.bkb.error_title', { fallback: 'Data Loading Error' }) : 'Data Loading Error'}
      </Typography>
      <Typography variant="body1" color="error" paragraph>
        {error}
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        {translate ?
          translate('commercial.bkb.error_message', {
            fallback: 'There was a problem loading the auction data. Please try again or contact support if the problem persists.'
          }) :
          'There was a problem loading the auction data. Please try again or contact support if the problem persists.'
        }
      </Typography>
      <CustomButton
        variant="contained"
        color="primary"
        onClick={refreshData}
        startIcon={<Refresh />}
        sx={{ mt: 1 }}
      >
        {translate ? translate('common.retry', { fallback: 'Retry' }) : 'Retry'}
      </CustomButton>
    </Box>
  );

  /**
   * Render loading state
   */
  const renderLoading = () => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', py: 6 }}>
      <CircularProgress size={48} sx={{ mb: 2 }} />
      <Typography variant="body1" color="text.secondary">
        {translate ?
          translate('commercial.bkb.loading', { fallback: 'Loading auction data...' }) :
          'Loading auction data...'
        }
      </Typography>
    </Box>
  );

  return (
    <Box sx={{ mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" fontWeight="bold" color="primary">
          BKB {translate ? translate('commercial.bkb.livestock_auctions', { fallback: 'Livestock Auctions' }) : 'Livestock Auctions'}
        </Typography>
      </Box>

      {renderCalendarHeader()}

      {showFilters && (
        <Box sx={{ mb: 3 }}>
          <BkbAuctionFilters />
        </Box>
      )}

      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
          p: 2
        }}
      >
        {loading ? renderLoading() :
         error ? renderError() :
         viewMode === 'calendar' ? renderCalendarView() : renderListView()}
      </Paper>
    </Box>
  );
};

export default BkbAuctionCalendar;
