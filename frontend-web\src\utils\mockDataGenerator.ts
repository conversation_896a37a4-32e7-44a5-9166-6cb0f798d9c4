// Helper function to generate realistic mock data
export const generateMockData = (count: number, generator: (index: number) => any) => {
  return Array.from({ length: count }, (_, i) => generator(i));
};

// Generate random date within range
export const randomDate = (start: Date, end: Date) => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// Generate random number within range
export const randomNumber = (min: number, max: number, decimals = 0) => {
  const value = min + Math.random() * (max - min);
  return Number(value.toFixed(decimals));
};

// Random selection from array
export const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};