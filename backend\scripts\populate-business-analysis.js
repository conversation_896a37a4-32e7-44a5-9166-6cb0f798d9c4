/**
 * Business Analysis MongoDB Data Population Script
 *
 * This script populates the MongoDB database with realistic data for the Business Analysis module.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../src/utils/logger');
const fs = require('fs');
const path = require('path');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  tlsAllowInvalidCertificates: true, // For development only
  tlsAllowInvalidHostnames: true, // For development only
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000, // 30 seconds timeout
  socketTimeoutMS: 45000, // 45 seconds timeout
});

// Generate Business KPIs
function generateBusinessKPIs() {
  return [
    {
      id: 'kpi-1',
      name: 'Revenue Growth',
      value: 15.7,
      target: 20,
      unit: '%',
      status: 'warning',
      trend: 5.2,
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-2',
      name: 'Profit Margin',
      value: 22.3,
      target: 25,
      unit: '%',
      status: 'warning',
      trend: 1.5,
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-3',
      name: 'Feed Efficiency',
      value: 1.8,
      target: 1.5,
      unit: 'ratio',
      status: 'warning',
      trend: -0.2,
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-4',
      name: 'Breeding Success Rate',
      value: 78,
      target: 85,
      unit: '%',
      status: 'warning',
      trend: 3,
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-5',
      name: 'Animal Health Index',
      value: 92,
      target: 95,
      unit: '%',
      status: 'success',
      trend: 2,
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-6',
      name: 'Operational Efficiency',
      value: 68,
      target: 75,
      unit: '%',
      status: 'warning',
      trend: 5,
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-7',
      name: 'Animal Growth Rate',
      value: 1.2,
      target: 1.5,
      unit: 'kg/day',
      status: 'warning',
      trend: 0.1,
      category: 'animals',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-8',
      name: 'Cost per Animal',
      value: 1250,
      target: 1000,
      unit: 'ZAR',
      status: 'danger',
      trend: 50,
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-9',
      name: 'Mortality Rate',
      value: 2.1,
      target: 2,
      unit: '%',
      status: 'warning',
      trend: -0.3,
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'kpi-10',
      name: 'Return on Investment',
      value: 18.5,
      target: 20,
      unit: '%',
      status: 'warning',
      trend: 2.5,
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Business Predictions
function generateBusinessPredictions() {
  return [
    {
      id: 'pred-1',
      name: 'Revenue Forecast (Next Quarter)',
      currentValue: 1250000,
      predictedValue: 1375000,
      confidence: 0.85,
      timeframe: 'Q3 2023',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-2',
      name: 'Feed Cost Projection',
      currentValue: 350000,
      predictedValue: 385000,
      confidence: 0.9,
      timeframe: 'Next 6 months',
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-3',
      name: 'Expected Calving Rate',
      currentValue: 78,
      predictedValue: 82,
      confidence: 0.75,
      timeframe: 'Next breeding season',
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-4',
      name: 'Disease Outbreak Risk',
      currentValue: 15,
      predictedValue: 25,
      confidence: 0.65,
      timeframe: 'Winter 2023',
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-5',
      name: 'Market Price Trend',
      currentValue: 38.5,
      predictedValue: 42.75,
      confidence: 0.7,
      timeframe: 'Q4 2023',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-6',
      name: 'Operational Cost Forecast',
      currentValue: 850000,
      predictedValue: 892500,
      confidence: 0.85,
      timeframe: 'Next fiscal year',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-7',
      name: 'Expected Growth Performance',
      currentValue: 1.2,
      predictedValue: 1.35,
      confidence: 0.8,
      timeframe: 'Next 3 months',
      category: 'animals',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'pred-8',
      name: 'Projected ROI',
      currentValue: 18.5,
      predictedValue: 21.2,
      confidence: 0.75,
      timeframe: 'Next fiscal year',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Business Insights
function generateBusinessInsights() {
  return [
    {
      id: 'insight-1',
      title: 'Feed Efficiency Opportunity',
      description: 'Analysis shows that adjusting the feed composition could improve feed efficiency by 15% and reduce costs.',
      impact: 'high',
      actionable: true,
      suggestedAction: 'Consult with nutritionist to reformulate feed mix with higher protein content.',
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-2',
      title: 'Breeding Timing Optimization',
      description: 'Historical data indicates that shifting breeding season by 2 weeks could improve calving rates by 8%.',
      impact: 'medium',
      actionable: true,
      suggestedAction: 'Adjust breeding schedule for next season and monitor results.',
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-3',
      title: 'Preventative Health Savings',
      description: 'Implementing a more comprehensive vaccination program could reduce treatment costs by 35%.',
      impact: 'high',
      actionable: true,
      suggestedAction: 'Develop enhanced vaccination protocol with veterinarian.',
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-4',
      title: 'Market Timing Advantage',
      description: 'Selling animals in March instead of January could increase revenue by 12% based on historical price trends.',
      impact: 'high',
      actionable: true,
      suggestedAction: 'Adjust feeding and growth plans to target March market.',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-5',
      title: 'Labor Efficiency Gap',
      description: 'Current labor allocation is 25% less efficient than industry benchmarks.',
      impact: 'medium',
      actionable: true,
      suggestedAction: 'Review work processes and implement task management system.',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-6',
      title: 'Genetic Selection Impact',
      description: 'Current breeding stock selection criteria could be optimized for better growth performance.',
      impact: 'medium',
      actionable: true,
      suggestedAction: 'Revise selection criteria to prioritize feed conversion efficiency.',
      category: 'animals',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-7',
      title: 'Water Management Improvement',
      description: 'Water quality testing indicates potential for improved animal health through water treatment.',
      impact: 'medium',
      actionable: true,
      suggestedAction: 'Install water filtration system at main water points.',
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'insight-8',
      title: 'Pasture Rotation Efficiency',
      description: 'Current pasture rotation schedule is suboptimal based on grass regrowth patterns.',
      impact: 'medium',
      actionable: true,
      suggestedAction: 'Implement new 30-day rotation schedule with rest periods.',
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Business Risks
function generateBusinessRisks() {
  return [
    {
      id: 'risk-1',
      name: 'Drought Impact',
      description: 'Extended drought could severely impact feed availability and increase costs.',
      probability: 0.4,
      impact: 0.8,
      severity: 0.32,
      mitigationStrategy: 'Develop drought management plan and maintain 6-month feed reserve.',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'risk-2',
      name: 'Disease Outbreak',
      description: 'Risk of major disease outbreak affecting herd health and productivity.',
      probability: 0.3,
      impact: 0.9,
      severity: 0.27,
      mitigationStrategy: 'Enhance biosecurity measures and vaccination protocols.',
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'risk-3',
      name: 'Market Price Volatility',
      description: 'Significant market price fluctuations affecting revenue predictability.',
      probability: 0.7,
      impact: 0.6,
      severity: 0.42,
      mitigationStrategy: 'Develop forward contracts and diversify market channels.',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'risk-4',
      name: 'Regulatory Changes',
      description: 'New regulations could increase compliance costs and operational complexity.',
      probability: 0.5,
      impact: 0.5,
      severity: 0.25,
      mitigationStrategy: 'Stay informed through industry associations and prepare contingency plans.',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'risk-5',
      name: 'Feed Cost Inflation',
      description: 'Rapid increase in feed costs affecting profitability.',
      probability: 0.6,
      impact: 0.7,
      severity: 0.42,
      mitigationStrategy: 'Secure long-term supply contracts and explore alternative feed sources.',
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'risk-6',
      name: 'Breeding Failure',
      description: 'Lower than expected breeding success affecting herd growth.',
      probability: 0.4,
      impact: 0.6,
      severity: 0.24,
      mitigationStrategy: 'Implement enhanced breeding protocols and monitoring systems.',
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Business Opportunities
function generateBusinessOpportunities() {
  return [
    {
      id: 'opp-1',
      name: 'Premium Market Segment',
      description: 'Opportunity to enter premium organic market with higher margins.',
      potentialValue: 500000,
      feasibility: 0.7,
      opportunityScore: 350000,
      implementationStrategy: 'Convert 30% of production to organic standards over 18 months.',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'opp-2',
      name: 'Feed Production Integration',
      description: 'Vertical integration opportunity to produce own feed and reduce costs.',
      potentialValue: 350000,
      feasibility: 0.6,
      opportunityScore: 210000,
      implementationStrategy: 'Lease land for feed production and invest in basic processing equipment.',
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'opp-3',
      name: 'Genetic Improvement Program',
      description: 'Implement advanced genetic selection to improve productivity.',
      potentialValue: 450000,
      feasibility: 0.8,
      opportunityScore: 360000,
      implementationStrategy: 'Partner with genetics company and implement AI program.',
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'opp-4',
      name: 'Direct-to-Consumer Channel',
      description: 'Develop direct sales channel to capture higher margins.',
      potentialValue: 600000,
      feasibility: 0.5,
      opportunityScore: 300000,
      implementationStrategy: 'Build e-commerce platform and local delivery capability.',
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'opp-5',
      name: 'Renewable Energy Implementation',
      description: 'Install solar power to reduce operational costs.',
      potentialValue: 250000,
      feasibility: 0.9,
      opportunityScore: 225000,
      implementationStrategy: 'Install solar panels on main buildings with battery storage.',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'opp-6',
      name: 'Waste-to-Compost Program',
      description: 'Convert animal waste to compost for additional revenue stream.',
      potentialValue: 180000,
      feasibility: 0.8,
      opportunityScore: 144000,
      implementationStrategy: 'Set up composting operation and develop local market channels.',
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Strategic Goals
function generateStrategicGoals() {
  return [
    {
      id: 'goal-1',
      name: 'Increase Operational Efficiency',
      description: 'Improve overall operational efficiency to reduce costs and increase productivity.',
      targetDate: new Date('2024-12-31'),
      progress: 35,
      status: 'in_progress',
      kpis: ['kpi-3', 'kpi-6', 'kpi-7'],
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'goal-2',
      name: 'Expand Market Reach',
      description: 'Develop new market channels and increase customer base.',
      targetDate: new Date('2024-06-30'),
      progress: 20,
      status: 'in_progress',
      kpis: ['kpi-1', 'kpi-2', 'kpi-10'],
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'goal-3',
      name: 'Enhance Animal Health',
      description: 'Implement comprehensive health management system to improve animal welfare and productivity.',
      targetDate: new Date('2023-12-31'),
      progress: 65,
      status: 'in_progress',
      kpis: ['kpi-5', 'kpi-9'],
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'goal-4',
      name: 'Optimize Breeding Program',
      description: 'Improve breeding success rates and genetic quality of herd.',
      targetDate: new Date('2024-03-31'),
      progress: 40,
      status: 'in_progress',
      kpis: ['kpi-4'],
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'goal-5',
      name: 'Reduce Feed Costs',
      description: 'Optimize feed formulation and sourcing to reduce costs while maintaining quality.',
      targetDate: new Date('2023-09-30'),
      progress: 75,
      status: 'in_progress',
      kpis: ['kpi-3', 'kpi-8'],
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate Strategic Initiatives
function generateStrategicInitiatives() {
  return [
    {
      id: 'init-1',
      name: 'Feed Optimization Project',
      description: 'Analyze and reformulate feed compositions to improve efficiency and reduce costs.',
      startDate: new Date('2023-01-15'),
      endDate: new Date('2023-09-30'),
      budget: 75000,
      status: 'in_progress',
      progress: 70,
      owner: 'Nutrition Manager',
      relatedGoalIds: ['goal-1', 'goal-5'],
      category: 'feeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'init-2',
      name: 'Market Expansion Initiative',
      description: 'Develop new market channels including direct-to-consumer and premium segments.',
      startDate: new Date('2023-03-01'),
      endDate: new Date('2024-06-30'),
      budget: 150000,
      status: 'in_progress',
      progress: 25,
      owner: 'Marketing Director',
      relatedGoalIds: ['goal-2'],
      category: 'financial',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'init-3',
      name: 'Health Management System Implementation',
      description: 'Deploy comprehensive health tracking and management system.',
      startDate: new Date('2023-02-15'),
      endDate: new Date('2023-12-31'),
      budget: 120000,
      status: 'in_progress',
      progress: 60,
      owner: 'Veterinary Manager',
      relatedGoalIds: ['goal-3'],
      category: 'health',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'init-4',
      name: 'Genetic Improvement Program',
      description: 'Implement advanced genetic selection and breeding techniques.',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2024-03-31'),
      budget: 200000,
      status: 'in_progress',
      progress: 45,
      owner: 'Breeding Manager',
      relatedGoalIds: ['goal-4'],
      category: 'breeding',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'init-5',
      name: 'Operational Efficiency Overhaul',
      description: 'Review and optimize all operational processes and workflows.',
      startDate: new Date('2023-04-01'),
      endDate: new Date('2024-12-31'),
      budget: 100000,
      status: 'in_progress',
      progress: 30,
      owner: 'Operations Manager',
      relatedGoalIds: ['goal-1'],
      category: 'operational',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

// Generate SWOT Analysis
function generateSWOTAnalysis() {
  return {
    id: 'swot-1',
    strengths: [
      'Experienced management team',
      'High-quality breeding stock',
      'Modern facilities and equipment',
      'Strong local reputation',
      'Efficient operational processes'
    ],
    weaknesses: [
      'High feed costs',
      'Limited market channels',
      'Seasonal cash flow variations',
      'Dependence on key personnel',
      'Limited technology adoption'
    ],
    opportunities: [
      'Growing demand for premium products',
      'Potential for vertical integration',
      'New export markets opening',
      'Technology advancements in breeding',
      'Government support programs'
    ],
    threats: [
      'Increasing regulatory requirements',
      'Climate change and weather volatility',
      'Disease outbreaks',
      'Rising input costs',
      'Increasing competition'
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  };
}

// Generate Strategic Scenarios
function generateStrategicScenarios() {
  return [
    {
      id: 'scenario-1',
      name: 'Market Expansion',
      description: 'Scenario where market demand increases significantly due to export opportunities.',
      probability: 0.4,
      impact: 0.8,
      financialImpact: 1200000,
      responseStrategy: 'Increase production capacity, secure additional supply chain partnerships, and enhance quality control processes.',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'scenario-2',
      name: 'Severe Disease Outbreak',
      description: 'Scenario where a major disease outbreak affects the industry.',
      probability: 0.2,
      impact: 0.9,
      financialImpact: -800000,
      responseStrategy: 'Implement emergency biosecurity protocols, isolate affected areas, and activate alternative supply arrangements.',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'scenario-3',
      name: 'Regulatory Tightening',
      description: 'Scenario where significant new regulations increase compliance requirements.',
      probability: 0.6,
      impact: 0.5,
      financialImpact: -300000,
      responseStrategy: 'Proactively upgrade facilities and processes, engage with regulatory bodies, and develop compliance expertise.',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'scenario-4',
      name: 'Technology Disruption',
      description: 'Scenario where new technologies significantly change production methods.',
      probability: 0.3,
      impact: 0.7,
      financialImpact: -500000,
      responseStrategy: 'Invest in R&D, form technology partnerships, and gradually implement proven innovations.',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
}

/**
 * Populate MongoDB with Business Analysis data
 */
async function populateBusinessAnalysis() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database
    const db = client.db(dbName);
    
    // Define collections and their data
    const collections = [
      { name: 'business_kpis', data: generateBusinessKPIs(), minCount: 10 },
      { name: 'business_predictions', data: generateBusinessPredictions(), minCount: 8 },
      { name: 'business_insights', data: generateBusinessInsights(), minCount: 8 },
      { name: 'business_risks', data: generateBusinessRisks(), minCount: 6 },
      { name: 'business_opportunities', data: generateBusinessOpportunities(), minCount: 6 },
      { name: 'strategic_goals', data: generateStrategicGoals(), minCount: 5 },
      { name: 'strategic_initiatives', data: generateStrategicInitiatives(), minCount: 5 },
      { name: 'swot_analysis', data: [generateSWOTAnalysis()], minCount: 1 },
      { name: 'strategic_scenarios', data: generateStrategicScenarios(), minCount: 4 }
    ];

    // Populate collections
    for (const { name, data, minCount } of collections) {
      const collection = db.collection(name);
      
      // Check if collection already has data
      const existingCount = await collection.countDocuments();
      if (existingCount >= minCount) {
        logger.info(`Collection ${name} already has ${existingCount} documents. Skipping population.`);
        continue;
      }
      
      // Drop existing data if any
      if (existingCount > 0) {
        logger.info(`Dropping existing data in ${name} collection...`);
        await collection.deleteMany({});
      }
      
      // Insert data
      logger.info(`Populating ${name} collection with ${data.length} documents...`);
      const result = await collection.insertMany(data);
      logger.info(`Successfully inserted ${result.insertedCount} documents into ${name} collection`);
    }

    logger.info('Business Analysis data population completed successfully');
  } catch (error) {
    logger.error('Error populating Business Analysis data:', error);
  } finally {
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the population script
populateBusinessAnalysis();
