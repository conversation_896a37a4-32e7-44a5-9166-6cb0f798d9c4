const fs = require('fs').promises;
const csv = require('csv-parse');
const xlsx = require('xlsx');
const path = require('path');
const logger = require('./logger');

class FileProcessor {
  async parseFile(file) {
    const extension = path.extname(file.originalname).toLowerCase();
    
    switch (extension) {
      case '.csv':
        return await this.parseCsv(file.path);
      case '.xlsx':
      case '.xls':
        return await this.parseExcel(file.path);
      default:
        throw new Error(`Unsupported file type: ${extension}`);
    }
  }

  async parseCsv(filePath) {
    const fileContent = await fs.readFile(filePath, 'utf-8');
    
    return new Promise((resolve, reject) => {
      csv.parse(fileContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true
      }, (err, records) => {
        if (err) {
          reject(new Error(`CSV parsing failed: ${err.message}`));
        } else {
          resolve(this.normalizeRecords(records));
        }
      });
    });
  }

  async parseExcel(filePath) {
    const workbook = xlsx.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    const records = xlsx.utils.sheet_to_json(worksheet, {
      raw: false,
      dateNF: 'yyyy-mm-dd'
    });

    return this.normalizeRecords(records);
  }

  normalizeRecords(records) {
    return records.map(record => {
      const normalized = {};
      
      // Convert keys to camelCase and normalize values
      for (const [key, value] of Object.entries(record)) {
        const normalizedKey = this.toCamelCase(key);
        normalized[normalizedKey] = this.normalizeValue(value);
      }

      return normalized;
    });
  }

  toCamelCase(str) {
    return str
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase());
  }

  normalizeValue(value) {
    if (typeof value !== 'string') return value;
    
    // Convert empty strings to null
    if (value.trim() === '') return null;
    
    // Try to parse dates
    const dateValue = new Date(value);
    if (value.includes('-') && !isNaN(dateValue.getTime())) {
      return dateValue;
    }
    
    // Try to parse numbers
    const numValue = Number(value);
    if (!isNaN(numValue) && value.trim() !== '') {
      return numValue;
    }
    
    return value.trim();
  }

  static async cleanupTempFile(filePath) {
    try {
      await fs.unlink(filePath);
      logger.debug(`Cleaned up temporary file: ${filePath}`);
    } catch (error) {
      logger.error(`Failed to cleanup temporary file ${filePath}:`, error);
    }
  }
}

module.exports = new FileProcessor();