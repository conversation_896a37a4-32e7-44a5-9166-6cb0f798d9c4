const logger = require('../utils/logger');
const { getCollection, getModel } = require('../config/mongodb');
const mongoose = require('mongoose');

/**
 * Asset Management Controller
 * Handles asset management operations including animal retirement tracking
 */
const assetController = {
  /**
   * Get all animal assets
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalAssets: async (req, res) => {
    try {
      const collection = await getCollection('animal_assets');
      const assets = await collection.find({}).toArray();
      res.status(200).json(assets);
    } catch (error) {
      logger.error('Error fetching animal assets:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get animal asset by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalAssetById: async (req, res) => {
    try {
      const { id } = req.params;
      const collection = await getCollection('animal_assets');
      const asset = await collection.findOne({ animalId: id });
      
      if (!asset) {
        return res.status(404).json({ error: 'Asset not found' });
      }
      
      res.status(200).json(asset);
    } catch (error) {
      logger.error('Error fetching animal asset:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Create animal asset
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createAnimalAsset: async (req, res) => {
    try {
      const assetData = req.body;
      const collection = await getCollection('animal_assets');
      
      // Check if asset already exists for this animal
      const existingAsset = await collection.findOne({ animalId: assetData.animalId });
      if (existingAsset) {
        return res.status(400).json({ error: 'Asset already exists for this animal' });
      }
      
      // Add timestamps
      assetData.createdAt = new Date();
      assetData.updatedAt = new Date();
      
      const result = await collection.insertOne(assetData);
      res.status(201).json({ ...assetData, _id: result.insertedId });
    } catch (error) {
      logger.error('Error creating animal asset:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Update animal asset
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateAnimalAsset: async (req, res) => {
    try {
      const { id } = req.params;
      const assetData = req.body;
      const collection = await getCollection('animal_assets');
      
      // Update timestamp
      assetData.updatedAt = new Date();
      
      const result = await collection.updateOne(
        { animalId: id },
        { $set: assetData }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Asset not found' });
      }
      
      res.status(200).json({ message: 'Asset updated successfully' });
    } catch (error) {
      logger.error('Error updating animal asset:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Retire animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  retireAnimal: async (req, res) => {
    try {
      const { id } = req.params;
      const { retirementDate, retirementReason, retirementNotes } = req.body;
      
      // Update animal status
      const animalCollection = await getCollection('animals');
      const animalResult = await animalCollection.updateOne(
        { _id: id },
        { 
          $set: { 
            status: 'Retired',
            retirementDate,
            retirementReason,
            retirementNotes,
            updatedAt: new Date()
          } 
        }
      );
      
      if (animalResult.matchedCount === 0) {
        return res.status(404).json({ error: 'Animal not found' });
      }
      
      // Update asset record if exists
      const assetCollection = await getCollection('animal_assets');
      await assetCollection.updateOne(
        { animalId: id },
        { 
          $set: { 
            status: 'Retired',
            retirementDate,
            retirementReason,
            retirementNotes,
            updatedAt: new Date()
          } 
        },
        { upsert: true } // Create if doesn't exist
      );
      
      res.status(200).json({ message: 'Animal retired successfully' });
    } catch (error) {
      logger.error('Error retiring animal:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get retirement candidates
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getRetirementCandidates: async (req, res) => {
    try {
      const animalCollection = await getCollection('animals');
      
      // Define retirement criteria
      const retirementAge = {
        'Cattle': 10, // years
        'Sheep': 6,
        'Goat': 7,
        'Pig': 5
      };
      
      const breedingLimit = {
        'Cattle': 8, // number of times
        'Sheep': 6,
        'Goat': 6,
        'Pig': 8
      };
      
      // Get all active animals
      const animals = await animalCollection.find({ status: { $ne: 'Retired' } }).toArray();
      
      // Filter retirement candidates
      const candidates = animals.filter(animal => {
        // Calculate age in years
        const birthDate = new Date(animal.birthDate);
        const today = new Date();
        const ageInYears = Math.floor((today - birthDate) / (365.25 * 24 * 60 * 60 * 1000));
        
        // Get breeding count
        const breedingCount = animal.breedingCount || 0;
        
        // Get retirement criteria for this animal type
        const ageLimit = retirementAge[animal.type] || 10;
        const breedingCountLimit = breedingLimit[animal.type] || 8;
        
        // Check if animal meets retirement criteria
        return ageInYears >= ageLimit || breedingCount >= breedingCountLimit;
      });
      
      res.status(200).json(candidates);
    } catch (error) {
      logger.error('Error fetching retirement candidates:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  },

  /**
   * Get retirement statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getRetirementStats: async (req, res) => {
    try {
      const animalCollection = await getCollection('animals');
      
      // Get all animals
      const animals = await animalCollection.find({}).toArray();
      
      // Count retired animals
      const retiredAnimals = animals.filter(animal => animal.status === 'Retired');
      
      // Group retired animals by reason
      const retirementReasons = {};
      retiredAnimals.forEach(animal => {
        const reason = animal.retirementReason || 'Other';
        retirementReasons[reason] = (retirementReasons[reason] || 0) + 1;
      });
      
      // Calculate asset values
      const activeValue = animals
        .filter(animal => animal.status !== 'Retired')
        .reduce((sum, animal) => sum + (animal.purchasePrice || 0), 0);
      
      const retiredValue = retiredAnimals
        .reduce((sum, animal) => sum + (animal.purchasePrice || 0), 0);
      
      // Return statistics
      res.status(200).json({
        totalAnimals: animals.length,
        activeAnimals: animals.length - retiredAnimals.length,
        retiredAnimals: retiredAnimals.length,
        retirementByReason: retirementReasons,
        valueOfActiveAssets: activeValue,
        valueOfRetiredAssets: retiredValue
      });
    } catch (error) {
      logger.error('Error fetching retirement statistics:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
};

module.exports = assetController;
