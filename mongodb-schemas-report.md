# MongoDB Schemas Analysis

- **Total collections with schemas:** 27
- **Total collections used in code:** 25
- **Collections without schemas:** 25

## Collections Without Schemas

- `animals`
- `animal_assets`
- `users`
- `business_kpis`
- `business_predictions`
- `business_insights`
- `business_risks`
- `business_opportunities`
- `strategic_goals`
- `strategic_initiatives`
- `swot_analysis`
- `strategic_scenarios`
- `business`
- `financial`
- `health_records`
- `vaccinations`
- `breeding_records`
- `calving_records`
- `feeding_records`
- `feed_inventory`
- `inventory`
- `pastures`
- `expenses`
- `reports`
- `settings`

## Collections With Schemas

- `assetschema`
- `businessanalysisschema`
- `report`
- `schemas`
- `animal`
- `breedingrecord`
- `birthrecord`
- `heatdetection`
- `businessstrategy`
- `marketanalysis`
- `predictiveanalysis`
- `feedingrecord`
- `feedingschedule`
- `transaction`
- `budget`
- `asset`
- `healthrecord`
- `vaccinationschedule`
- `inventoryitem`
- `inventorytransaction`
- `supplier`
- `reportresult`
- `systemsettings`
- `usersettings`
- `user`
- `supplierproduct`
- `userpermission`

## All Collections Used in Code

- `animals` ❌
- `animal_assets` ❌
- `users` ❌
- `business_kpis` ❌
- `business_predictions` ❌
- `business_insights` ❌
- `business_risks` ❌
- `business_opportunities` ❌
- `strategic_goals` ❌
- `strategic_initiatives` ❌
- `swot_analysis` ❌
- `strategic_scenarios` ❌
- `business` ❌
- `financial` ❌
- `health_records` ❌
- `vaccinations` ❌
- `breeding_records` ❌
- `calving_records` ❌
- `feeding_records` ❌
- `feed_inventory` ❌
- `inventory` ❌
- `pastures` ❌
- `expenses` ❌
- `reports` ❌
- `settings` ❌
