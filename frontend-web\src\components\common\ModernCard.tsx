import React, { ReactNode } from 'react';
import { CustomButton } from '../common';
import { <PERSON>, Card, CardContent, CardHeader, CardActions, Typography, IconButton, Divider, Chip, useTheme, alpha, Tooltip, Skeleton, Avatar } from '@mui/material';
import { motion } from 'framer-motion';
import { MoreVert, Info } from '../../utils/iconImports';
import { useLanguage } from '../../contexts/LanguageContext';
import { ErrorBoundary } from '.';
import { getSafeElevation, getSafeAnimation } from '../../utils/themeDefaults';

export interface ModernCardAction {
  label: string;
  onClick: () => void;
  icon?: ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  variant?: 'text' | 'outlined' | 'contained';
  disabled?: boolean;
}

export interface ModernCardProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
  content?: ReactNode;
  footer?: ReactNode;
  actions?: ModernCardAction[];
  onMoreClick?: () => void;
  loading?: boolean;
  error?: string;
  badge?: {
    label: string;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  };
  tooltip?: string;
  backgroundImage?: string;
  accentColor?: string;
  module?: string;
  elevation?: number;
  onClick?: () => void;
  minHeight?: number | string;
  maxHeight?: number | string;
  children?: ReactNode;
}

const ModernCard: React.FC<ModernCardProps> = ({
  title,
  subtitle,
  icon,
  content,
  footer,
  actions,
  onMoreClick,
  loading = false,
  error,
  badge,
  tooltip,
  backgroundImage,
  accentColor,
  module,
  elevation = 2,
  onClick,
  minHeight,
  maxHeight,
  children
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return accentColor || theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  return (
    <ErrorBoundary>
      <Card
        elevation={0}
        onClick={onClick}
        sx={{
        borderRadius: 3,
        overflow: 'hidden',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        cursor: onClick ? 'pointer' : 'default',
        transition: getSafeAnimation(theme, 'medium'),
        backgroundColor: 'transparent',
        backdropFilter: 'blur(10px)',
        border: 'none',
        background: `linear-gradient(135deg,
          ${alpha(moduleColor, 0.15)},
          ${alpha(moduleColor, 0.05)})`,
        boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
        '&:hover': onClick ? {
          boxShadow: `0 8px 30px ${alpha(theme.palette.common.black, 0.1)}`,
          transform: 'translateY(-4px)',
          background: `linear-gradient(135deg,
            ${alpha(moduleColor, 0.2)},
            ${alpha(moduleColor, 0.1)})`,
        } : {},
        minHeight,
        maxHeight,
        backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined,
        backgroundSize: backgroundImage ? 'cover' : undefined,
        backgroundPosition: backgroundImage ? 'center' : undefined,
        '&::before': backgroundImage ? {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg,
            ${alpha(moduleColor, 0.85)},
            ${alpha(moduleColor, 0.65)})`,
          backdropFilter: 'blur(8px)',
          zIndex: 0
        } : undefined
      }}
    >
      {/* Accent Border - Removed for better blending */}

      {/* Card Header */}
      <CardHeader
        avatar={icon ? (
          <Avatar
            sx={{
              bgcolor: alpha(moduleColor, 0.1),
              color: moduleColor,
              boxShadow: getSafeElevation(theme, 1)
            }}
          >
            {icon}
          </Avatar>
        ) : undefined}
        title={
          loading ? (
            <Skeleton width="60%" height={24} />
          ) : (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" fontWeight="bold" sx={{ mr: 1 }}>
                {title}
              </Typography>

              {badge && (
                <Chip
                  label={badge.label}
                  color={badge.color || 'primary'}
                  size="small"
                  sx={{
                    height: 20,
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }}
                />
              )}

              {tooltip && (
                <Tooltip title={tooltip} arrow>
                  <IconButton size="small" sx={{ ml: 0.5 }}>
                    <Info fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          )
        }
        subheader={
          loading ? (
            <Skeleton width="40%" height={20} />
          ) : subtitle ? (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          ) : undefined
        }
        action={
          onMoreClick && (
            <IconButton onClick={(e) => {
              e.stopPropagation();
              onMoreClick();
            }}>
              <MoreVert />
            </IconButton>
          )
        }
        sx={{
          zIndex: 1,
          pb: 0
        }}
      />

      {/* Card Content */}
      <CardContent
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          zIndex: 1,
          pt: 2
        }}
      >
        {loading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Skeleton variant="rectangular" width="100%" height={100} />
            <Skeleton width="90%" height={20} />
            <Skeleton width="80%" height={20} />
            <Skeleton width="60%" height={20} />
          </Box>
        ) : error ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: theme.palette.error.main,
              p: 3,
              textAlign: 'center'
            }}
          >
            <Typography variant="body1" color="error" gutterBottom>
              {error}
            </Typography>
            <CustomButton
              variant="outlined"
              color="error"
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                window.location.reload();
              }}
              sx={{ mt: 2 }}
            >
              {translate('common.refresh')}
            </CustomButton>
          </Box>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            style={{ height: '100%' }}
          >
            {content || children}
          </motion.div>
        )}
      </CardContent>

      {/* Card Footer */}
      {(footer || (actions && actions.length > 0)) && (
        <>
          <Divider sx={{ mx: 2 }} />
          <CardActions
            sx={{
              p: 2,
              justifyContent: actions ? 'flex-end' : 'flex-start',
              zIndex: 1
            }}
          >
            {footer || (
              <Box sx={{ display: 'flex', gap: 1 }}>
                {actions?.map((action, index) => (
                  <CustomButton
                    key={index}
                    variant={action.variant || 'text'}
                    color={action.color || 'primary'}
                    startIcon={action.icon}
                    onClick={(e) => {
                      e.stopPropagation();
                      action.onClick();
                    }}
                    disabled={action.disabled}
                    size="small"
                  >
                    {action.label}
                  </CustomButton>
                ))}
              </Box>
            )}
          </CardActions>
        </>
      )}
    </Card>
    </ErrorBoundary>
  );
};

export default ModernCard;
