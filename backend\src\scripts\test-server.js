/**
 * <PERSON><PERSON>t to test if the server is running
 *
 * This script tests if the server is running
 * Run with: node src/scripts/test-server.js
 */

const http = require('http');

// Test server
function testServer() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        console.log(`Status: ${res.statusCode}`);
        console.log('Response:', data);
        resolve(data);
      });
    });

    req.on('error', (error) => {
      console.error('Error:', error.message);
      reject(error);
    });

    req.end();
  });
}

// Run test
testServer()
  .then(() => {
    console.log('Server is running');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Server is not running');
    process.exit(1);
  });
