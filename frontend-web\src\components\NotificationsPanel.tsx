import React, { useState, useEffect } from 'react';
import CustomButton from './common/CustomButton';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, IconButton, List, ListItem, ListItemAvatar, ListItemText, Avatar, Divider, Paper, Tabs, Tab, Badge, alpha, useTheme } from '@mui/material';
import {
  Close,
  Notifications,
  NotificationsActive,
  CheckCircle,
  Warning,
  Error,
  Info,
  Delete,
  DoneAll
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { StatusChip } from '../design/DesignSystem';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  isRead: boolean;
  link?: string;
}

interface NotificationsPanelProps {
  onClose: () => void;
}

const NotificationsPanel: React.FC<NotificationsPanelProps> = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const navigate = useNavigate();
  const theme = useTheme();
  const { translate } = useLanguage();

  // Fetch notifications on mount
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      // Mock notifications
      const mockNotifications: Notification[] = [
        {
          id: '1',
          title: 'Vaccination Due',
          message: 'Cattle-2023-001 is due for vaccination tomorrow',
          type: 'warning',
          timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
          isRead: false,
          link: '/health/vaccinations'
        },
        {
          id: '2',
          title: 'Feed Stock Low',
          message: 'Hay stock is running low (15% remaining)',
          type: 'warning',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
          isRead: false,
          link: '/feeding/inventory'
        },
        {
          id: '3',
          title: 'Health Check Complete',
          message: 'Monthly health check completed for all animals',
          type: 'success',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
          isRead: true,
          link: '/health/records'
        },
        {
          id: '4',
          title: 'System Update',
          message: 'System updated to version 2.5.0',
          type: 'info',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
          isRead: true,
          link: '/settings/system'
        },
        {
          id: '5',
          title: 'Payment Overdue',
          message: 'Invoice #INV-2023-042 is overdue by 3 days',
          type: 'error',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 36).toISOString(), // 1.5 days ago
          isRead: false,
          link: '/financial/invoices'
        }
      ];

      setNotifications(mockNotifications);
      setIsLoading(false);
    }, 1000);

    // Add event listener for Escape key
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);

    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Mark notification as read
  const markAsRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, isRead: true }))
    );
  };

  // Delete notification
  const deleteNotification = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notification => notification.id !== id)
    );
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Handle notification click
  const handleNotificationClick = (notification: Notification) => {
    markAsRead(notification.id);

    if (notification.link) {
      navigate(notification.link);
      onClose();
    }
  };

  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 0) return true; // All
    if (activeTab === 1) return !notification.isRead; // Unread
    return notification.isRead; // Read
  });

  // Get icon for notification type
  const getIconForType = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle sx={{ color: theme.palette.success.main }} />;
      case 'warning':
        return <Warning sx={{ color: theme.palette.warning.main }} />;
      case 'error':
        return <Error sx={{ color: theme.palette.error.main }} />;
      case 'info':
      default:
        return <Info sx={{ color: theme.palette.info.main }} />;
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / (1000 * 60));
    const diffHours = Math.round(diffMs / (1000 * 60 * 60));
    const diffDays = Math.round(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) {
      return `${diffMins} ${translate('common.minutes_ago')}`;
    } else if (diffHours < 24) {
      return `${diffHours} ${translate('common.hours_ago')}`;
    } else {
      return `${diffDays} ${translate('common.days_ago')}`;
    }
  };

  // Count unread notifications
  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        maxWidth: '400px',
        zIndex: theme.zIndex.drawer + 2,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Paper
        elevation={4}
        sx={{
          height: '100%',
          borderRadius: 0,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: alpha(theme.palette.background.paper, 0.95),
          backdropFilter: 'blur(10px)',
          borderLeft: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <NotificationsActive color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              {translate('common.notifications')}
            </Typography>
            {unreadCount > 0 && (
              <Badge
                badgeContent={unreadCount}
                color="error"
                sx={{ ml: 1 }}
              />
            )}
          </Box>

          <IconButton edge="end" onClick={onClose}>
            <Close />
          </IconButton>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab
              label={translate('common.all')}
              icon={<Badge badgeContent={notifications.length} color="primary" />}
              iconPosition="end"
            />
            <Tab
              label={translate('common.unread')}
              icon={<Badge badgeContent={unreadCount} color="error" />}
              iconPosition="end"
            />
            <Tab
              label={translate('common.read')}
              icon={<Badge badgeContent={notifications.length - unreadCount} color="default" />}
              iconPosition="end"
            />
          </Tabs>
        </Box>

        {/* Actions */}
        <Box
          sx={{
            p: 1,
            display: 'flex',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <CustomButton
            size="small"
            startIcon={<DoneAll />}
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            {translate('common.mark_all_read')}
          </CustomButton>

          <CustomButton
            size="small"
            startIcon={<Delete />}
            onClick={clearAllNotifications}
            disabled={notifications.length === 0}
            color="error"
          >
            {translate('common.clear_all')}
          </CustomButton>
        </Box>

        {/* Notifications List */}
        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          {isLoading ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                {translate('common.loading')}...
              </Typography>
            </Box>
          ) : filteredNotifications.length > 0 ? (
            <List>
              {filteredNotifications.map((notification) => (
                <React.Fragment key={notification.id}>
                  <ListItem
                    button
                    onClick={() => handleNotificationClick(notification)}
                    sx={{
                      bgcolor: notification.isRead
                        ? 'transparent'
                        : alpha(theme.palette.primary.main, 0.05),
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.1)
                      },
                      position: 'relative',
                      pl: 2,
                      pr: 6
                    }}
                  >
                    {!notification.isRead && (
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          bottom: 0,
                          width: 4,
                          bgcolor: theme.palette.primary.main
                        }}
                      />
                    )}

                    <ListItemAvatar>
                      <Avatar
                        sx={{
                          bgcolor: alpha(
                            theme.palette[notification.type === 'info' ? 'info' : notification.type].main,
                            0.1
                          ),
                          color: theme.palette[notification.type === 'info' ? 'info' : notification.type].main
                        }}
                      >
                        {getIconForType(notification.type)}
                      </Avatar>
                    </ListItemAvatar>

                    <ListItemText
                      primary={
                        <Typography
                          variant="subtitle2"
                          fontWeight={notification.isRead ? 'normal' : 'bold'}
                        >
                          {notification.title}
                        </Typography>
                      }
                      secondary={
                        <>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {formatTimestamp(notification.timestamp)}
                          </Typography>
                        </>
                      }
                    />

                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteNotification(notification.id);
                      }}
                      sx={{
                        position: 'absolute',
                        right: 8,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        opacity: 0.5,
                        '&:hover': {
                          opacity: 1
                        }
                      }}
                    >
                      <Delete fontSize="small" />
                    </IconButton>
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                {translate('common.no_notifications')}
              </Typography>
            </Box>
          )}
        </Box>

        {/* Footer */}
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            textAlign: 'center'
          }}
        >
          <Typography variant="body2" color="text.secondary">
            {translate('common.notification_settings')}
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default NotificationsPanel;
