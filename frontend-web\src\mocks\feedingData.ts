import { FeedingRecord, FeedInventory, FeedingPlan } from '../types/feeding';
import { generateMockData, randomDate, randomNumber, randomChoice } from '../utils/mockDataGenerator';
import { mockAnimals } from './animalData';

const feedTypes = ['Lucerne', 'Maize', 'Eragrostis', 'Teff', 'Molasses Meal', 'Protein Supplement', 'Mineral Lick', 'Veld Grazing'];
const units = ['kg', 'tons', 'bales'];
const locations = ['Veld A', 'Veld B', 'Kraal 1', 'Kraal 2', 'Feed Lot'];
const suppliers = ['Afgri Animal Feeds', 'Kaap Agri', 'Obaro', 'GWK', 'BKB', 'OVK'];
const feedCategories = ['concentrate', 'forage', 'supplement', 'mineral', 'other'];

export const mockFeedingRecords: FeedingRecord[] = generateMockData(10, (i) => {
  const feedType = randomChoice(feedTypes);
  const date = randomDate(new Date(2023, 0, 1), new Date());
  const quantity = randomNumber(50, 1000);
  const costPerUnit = randomNumber(5, 20, 2); // Higher costs in Rands
  const totalCost = costPerUnit * quantity;

  // Create animal group ID and name
  const animalGroupId = `G${randomNumber(1, 5)}`;
  const animalGroup = randomChoice(['Beef Cattle', 'Breeding Ewes', 'Boer Goats', 'Weaned Lambs', 'Finishing Cattle']);

  // Create feed ID
  const feedId = `FI${randomNumber(1, 10).toString().padStart(3, '0')}`;

  return {
    id: `FR${(i + 1).toString().padStart(3, '0')}`,
    date,
    feedId,
    feedType,
    quantity,
    unit: randomChoice(units),
    location: randomChoice(locations),
    animalGroupId,
    animalGroup,
    costPerUnit,
    totalCost,
    notes: `${feedType} feeding for ${animalGroup}`
  };
});

export const mockFeedInventory: FeedInventory[] = generateMockData(10, (i) => {
  const name = randomChoice(feedTypes);
  const quantity = randomNumber(500, 5000);
  const purchaseDate = randomDate(new Date(2023, 0, 1), new Date());
  const expiryDate = new Date(purchaseDate);
  expiryDate.setMonth(expiryDate.getMonth() + randomNumber(3, 12));
  // Calculate total cost based on cost per unit and quantity
  const costPerUnit = randomNumber(5, 20, 2); // Higher costs in Rands
  const totalCost = costPerUnit * quantity;
  const type = randomChoice(feedCategories) as 'concentrate' | 'forage' | 'supplement' | 'mineral' | 'other';
  const unit = randomChoice(units) as 'kg' | 'tons' | 'bales';
  const status = randomChoice(['available', 'low', 'reorder', 'expired']) as 'available' | 'low' | 'reorder' | 'expired';
  const minimumStock = Math.floor(quantity * 0.2);
  const reorderLevel = Math.floor(quantity * 0.3);

  return {
    id: `FI${(i + 1).toString().padStart(3, '0')}`,
    name,
    type,
    quantity,
    unit,
    location: randomChoice(['Storage Shed', 'Feed Silo', 'Warehouse', 'Feed Store']),
    supplier: randomChoice(suppliers),
    purchaseDate,
    expiryDate,
    batchNumber: `B${randomNumber(1000, 9999)}`,
    costPerUnit,
    nutritionalInfo: {
      protein: randomNumber(5, 30, 1),
      fiber: randomNumber(5, 40, 1),
      energy: randomNumber(1, 5, 2),
      minerals: {
        calcium: randomNumber(0.1, 2, 2),
        phosphorus: randomNumber(0.1, 1, 2)
      },
      moisture: randomNumber(5, 30, 1)
    },
    minimumStock,
    status,
    reorderLevel,
    capacity: quantity + randomNumber(1000, 5000),
    lastUpdated: new Date(),
    notes: `${name} stock - Batch ${randomNumber(1000, 9999)}`,
    totalCost
  };
});

export const mockFeedingPlans: FeedingPlan[] = generateMockData(5, (i) => {
  const startDate = randomDate(new Date(2023, 0, 1), new Date());
  const endDate = new Date(startDate);
  endDate.setMonth(endDate.getMonth() + randomNumber(1, 6));
  const status = randomChoice(['active', 'completed', 'draft']) as 'active' | 'completed' | 'draft';
  const isActive = status === 'active';

  return {
    id: `FP${(i + 1).toString().padStart(3, '0')}`,
    name: `Feeding Plan ${i+1}`,
    animalGroupId: `G${randomNumber(1, 5)}`,
    feedItems: generateMockData(randomNumber(1, 4), (j) => ({
      feedId: `FI${randomNumber(1, 10).toString().padStart(3, '0')}`,
      quantity: randomNumber(5, 50),
      frequency: randomChoice(['daily', 'weekly', 'monthly']) as 'daily' | 'weekly' | 'monthly',
      timeOfDay: randomChoice(['morning', 'afternoon', 'evening'])
    })),
    startDate,
    endDate,
    status,
    isActive
  };
});

// Add mock feeding stats
export const mockFeedingStats = {
  totalFeedUsedToday: 450,
  totalFeedCostToday: 6750.75, // In Rands
  feedInventoryValue: 125000.50, // In Rands
  lowStockItems: 2,
  activeFeedingPlans: 3,
  feedingRecordsThisWeek: 28,
  feedUsageByType: {
    concentrate: 320,
    forage: 100,
    mineral: 30
  }
};

// Re-export the types
export type { FeedingRecord, FeedInventory, FeedingPlan };








