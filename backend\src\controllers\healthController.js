// Note: Using mock data for now - database integration to be added later
const logger = require('../utils/logger');

const healthController = {
  // Get health overview (for /api/health route)
  getHealth: async (req, res) => {
    try {
      // Mock health overview data
      const healthOverview = {
        summary: {
          totalRecords: 89,
          recentCheckups: 12,
          pendingTreatments: 3,
          upcomingVaccinations: 8,
          healthAlerts: 2
        },
        recentRecords: [
          {
            id: '1',
            animalId: 'A001',
            animalTag: 'COW001',
            type: 'Vaccination',
            description: 'Annual vaccination program',
            date: '2024-12-20',
            performedBy: 'Dr. <PERSON>',
            status: 'completed'
          },
          {
            id: '2',
            animalId: 'A002',
            animalTag: 'COW002',
            type: 'Health Check',
            description: 'Routine health examination',
            date: '2024-12-19',
            performedBy: 'Dr. <PERSON>',
            status: 'completed'
          }
        ],
        healthAlerts: [
          {
            id: '3',
            animalId: 'A003',
            animalTag: 'COW003',
            type: 'Treatment Required',
            description: 'Follow-up treatment needed',
            priority: 'high',
            dueDate: '2024-12-25'
          }
        ]
      };

      res.json({
        success: true,
        data: healthOverview
      });
    } catch (error) {
      logger.error('Error fetching health overview:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch health overview'
      });
    }
  },

  // Health Management
  getHealthRecords: async (req, res) => {
    try {
      const { animalId, type, fromDate, toDate } = req.query;

      // Mock health records data
      let healthRecords = [
        {
          id: '1',
          animalId: 'A001',
          animalTag: 'COW001',
          date: '2024-12-20',
          type: 'Vaccination',
          description: 'Annual vaccination program - FMD, BVD, IBR',
          performedBy: 'Dr. Sarah Johnson',
          cost: 150,
          notes: 'No adverse reactions observed',
          followUpDate: '2025-12-20',
          status: 'completed'
        },
        {
          id: '2',
          animalId: 'A002',
          animalTag: 'COW002',
          date: '2024-12-19',
          type: 'Health Check',
          description: 'Routine health examination',
          performedBy: 'Dr. Michael Brown',
          cost: 75,
          notes: 'Animal in good health, normal vital signs',
          status: 'completed'
        },
        {
          id: '3',
          animalId: 'A003',
          animalTag: 'COW003',
          date: '2024-12-18',
          type: 'Treatment',
          description: 'Antibiotic treatment for respiratory infection',
          performedBy: 'Dr. Sarah Johnson',
          cost: 200,
          notes: 'Prescribed 7-day course of antibiotics',
          followUpDate: '2024-12-25',
          status: 'ongoing'
        }
      ];

      // Filter by parameters
      if (animalId) {
        healthRecords = healthRecords.filter(record => record.animalId === animalId);
      }
      if (type) {
        healthRecords = healthRecords.filter(record => record.type.toLowerCase().includes(type.toLowerCase()));
      }
      if (fromDate) {
        healthRecords = healthRecords.filter(record => record.date >= fromDate);
      }
      if (toDate) {
        healthRecords = healthRecords.filter(record => record.date <= toDate);
      }

      res.json({
        success: true,
        data: healthRecords,
        total: healthRecords.length
      });
    } catch (error) {
      logger.error('Error fetching health records:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch health records'
      });
    }
  },

  getVaccinations: async (req, res) => {
    try {
      const { animalId, vaccineType } = req.query;

      // Mock vaccination data
      let vaccinations = [
        {
          id: '1',
          animalId: 'A001',
          animalTag: 'COW001',
          vaccineType: 'FMD',
          vaccineName: 'Foot and Mouth Disease Vaccine',
          date: '2024-12-20',
          nextDueDate: '2025-12-20',
          batchNumber: 'FMD2024120001',
          performedBy: 'Dr. Sarah Johnson',
          notes: 'Annual vaccination completed successfully'
        },
        {
          id: '2',
          animalId: 'A002',
          animalTag: 'COW002',
          vaccineType: 'BVD',
          vaccineName: 'Bovine Viral Diarrhea Vaccine',
          date: '2024-12-19',
          nextDueDate: '2025-12-19',
          batchNumber: 'BVD2024121901',
          performedBy: 'Dr. Michael Brown',
          notes: 'No adverse reactions observed'
        },
        {
          id: '3',
          animalId: 'A003',
          animalTag: 'COW003',
          vaccineType: 'IBR',
          vaccineName: 'Infectious Bovine Rhinotracheitis Vaccine',
          date: '2024-12-18',
          nextDueDate: '2025-12-18',
          batchNumber: 'IBR2024121801',
          performedBy: 'Dr. Sarah Johnson',
          notes: 'Routine vaccination completed'
        }
      ];

      // Filter by parameters
      if (animalId) {
        vaccinations = vaccinations.filter(vaccination => vaccination.animalId === animalId);
      }
      if (vaccineType) {
        vaccinations = vaccinations.filter(vaccination => vaccination.vaccineType.toLowerCase().includes(vaccineType.toLowerCase()));
      }

      res.json({
        success: true,
        data: vaccinations,
        total: vaccinations.length
      });
    } catch (error) {
      logger.error('Error fetching vaccinations:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch vaccinations'
      });
    }
  },

  createHealthRecord: async (req, res) => {
    try {
      const { animalId, date, type, description, performedBy, cost, notes, followUpDate, status } = req.body;

      // Mock creation - generate new health record
      const newRecord = {
        id: Date.now().toString(),
        animalId,
        date,
        type,
        description,
        performedBy,
        cost,
        notes,
        followUpDate,
        status: status || 'completed',
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        data: newRecord,
        message: 'Health record created successfully'
      });
    } catch (error) {
      logger.error('Error creating health record:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to create health record'
      });
    }
  },

  updateHealthRecord: async (req, res) => {
    try {
      const { id } = req.params;
      const { animalId, date, type, description, performedBy, cost, notes, followUpDate, status } = req.body;

      // Mock update - return updated record
      const updatedRecord = {
        id,
        animalId,
        date,
        type,
        description,
        performedBy,
        cost,
        notes,
        followUpDate,
        status,
        updatedAt: new Date().toISOString()
      };

      res.json({
        success: true,
        data: updatedRecord,
        message: 'Health record updated successfully'
      });
    } catch (error) {
      logger.error('Error updating health record:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to update health record'
      });
    }
  },

  deleteHealthRecord: async (req, res) => {
    try {
      const { id } = req.params;

      // Mock deletion - return success
      res.json({
        success: true,
        message: `Health record ${id} deleted successfully`
      });
    } catch (error) {
      logger.error('Error deleting health record:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to delete health record'
      });
    }
  }
};

module.exports = healthController;
