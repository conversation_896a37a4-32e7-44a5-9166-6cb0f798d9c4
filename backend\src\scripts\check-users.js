/**
 * <PERSON>ript to check users in MongoDB
 * 
 * This script lists all users in the MongoDB database
 * Run with: node src/scripts/check-users.js
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const DB_NAME = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Connect to MongoDB and check users
async function checkUsers() {
  let client;

  try {
    console.log('Connecting to MongoDB...');
    console.log(`URI: ${MONGODB_URI}`);
    console.log(`Database: ${DB_NAME}`);
    
    // Connect to MongoDB
    client = new MongoClient(MONGODB_URI);
    await client.connect();
    console.log('Connected to MongoDB');

    // Get database and collection
    const db = client.db(DB_NAME);
    const usersCollection = db.collection('users');

    // Count users
    const userCount = await usersCollection.countDocuments();
    console.log(`Found ${userCount} users in the database`);

    // List all users
    const users = await usersCollection.find({}).toArray();
    
    console.log('\nUser List:');
    console.log('==========');
    
    users.forEach((user, index) => {
      console.log(`\nUser ${index + 1}:`);
      console.log(`Username: ${user.username}`);
      console.log(`Name: ${user.firstName} ${user.lastName}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Status: ${user.status || 'active'}`);
      console.log(`Permissions: ${user.permissions ? user.permissions.join(', ') : 'none'}`);
    });

    console.log('\nCheck completed successfully');
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    // Close MongoDB connection
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the check
checkUsers()
  .then(() => {
    console.log('Check completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('Check failed:', error);
    process.exit(1);
  });
