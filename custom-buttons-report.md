# Button Usage Analysis

- **Files using <PERSON><PERSON> Button:** 126
- **Files using CustomButton:** 49
- **Files with no button usage:** 159

## Files Using MUI Button

- `components\animals\AnimalList.tsx` (Import: Yes, Usage: No)
- `components\animals\AnimalQuickView.tsx` (Import: Yes, Usage: No)
- `components\animals\AnimalsTable.tsx` (Import: Yes, Usage: No)
- `components\animals\RFIDManagement.tsx` (Import: Yes, Usage: No)
- `components\breeding\BreedingRecordModal.tsx` (Import: Yes, Usage: No)
- `components\breeding\NotificationConfig.tsx` (Import: Yes, Usage: No)
- `components\breeding\NotificationHistory.tsx` (Import: Yes, Usage: No)
- `components\commercial\AuctionModal.tsx` (Import: Yes, Usage: No)
- `components\commercial\BkbAuctionCalendar.tsx` (Import: Yes, Usage: No)
- `components\commercial\OrderModal.tsx` (Import: Yes, Usage: No)
- `components\commercial\SupplierModal.tsx` (Import: Yes, Usage: No)
- `components\commercial\SuppliersDirectory.tsx` (Import: Yes, Usage: No)
- `components\common\AnimatedBackgroundCard.tsx` (Import: Yes, Usage: No)
- `components\common\AnimatedChart.tsx` (Import: Yes, Usage: No)
- `components\common\CrudComponent.tsx` (Import: Yes, Usage: No)
- `components\common\DashboardCard.tsx` (Import: Yes, Usage: No)
- `components\common\DataTable.tsx` (Import: Yes, Usage: No)
- `components\common\EnhancedChart.tsx` (Import: Yes, Usage: No)
- `components\common\EnhancedDataTable.tsx` (Import: Yes, Usage: No)
- `components\common\EnhancedSponsorCarousel.tsx` (Import: Yes, Usage: No)
- `components\common\EnhancedTable.tsx` (Import: Yes, Usage: No)
- `components\common\FarmerResourceLinks.tsx` (Import: Yes, Usage: No)
- `components\common\ModernCard.tsx` (Import: Yes, Usage: No)
- `components\common\ModernChart.tsx` (Import: Yes, Usage: No)
- `components\common\ModernDashboard.tsx` (Import: Yes, Usage: No)
- `components\common\ModernDataTable.tsx` (Import: Yes, Usage: No)
- `components\common\ModuleHeader.tsx` (Import: Yes, Usage: No)
- `components\common\OfflineIndicator.tsx` (Import: Yes, Usage: No)
- `components\common\ResponsiveChartContainer.tsx` (Import: Yes, Usage: No)
- `components\common\ResponsiveDataCard.tsx` (Import: Yes, Usage: No)
- `components\common\ResponsiveNavTabs.tsx` (Import: Yes, Usage: No)
- `components\common\SponsorCarousel.tsx` (Import: Yes, Usage: No)
- `components\common\SubModuleChart.tsx` (Import: Yes, Usage: No)
- `components\common\SubModuleDataTable.tsx` (Import: Yes, Usage: No)
- `components\common\SubModuleReport.tsx` (Import: Yes, Usage: No)
- `components\common\VirtualizedTable.tsx` (Import: Yes, Usage: No)
- `components\feeding\FeedingRecordModal.tsx` (Import: Yes, Usage: No)
- `components\feeding\FeedInventoryModal.tsx` (Import: Yes, Usage: No)
- `components\GlobalSearch.tsx` (Import: Yes, Usage: No)
- `components\health\HealthRecordModal.tsx` (Import: Yes, Usage: No)
- `components\login\EnhancedLoginForm.tsx` (Import: Yes, Usage: No)
- `components\modules\animals\AnimalProfileCard.tsx` (Import: Yes, Usage: No)
- `components\modules\breeding\BreedingDashboard.tsx` (Import: Yes, Usage: No)
- `components\modules\commercial\MarketForecast.tsx` (Import: Yes, Usage: No)
- `components\modules\commercial\MarketplaceListingDetail.tsx` (Import: Yes, Usage: No)
- `components\modules\commercial\MarketplaceSuppliers.tsx` (Import: Yes, Usage: No)
- `components\modules\feed\FeedManagementDashboard.tsx` (Import: Yes, Usage: No)
- `components\modules\feeding\FeedingRecordsList.tsx` (Import: Yes, Usage: No)
- `components\modules\financial\FinancialManagementDashboard.tsx` (Import: Yes, Usage: No)
- `components\modules\financial\TransactionCard.tsx` (Import: Yes, Usage: No)
- `components\Navbar.tsx` (Import: Yes, Usage: No)
- `components\NotificationsPanel.tsx` (Import: Yes, Usage: No)
- `components\QuickActions.tsx` (Import: Yes, Usage: No)
- `components\reports\HtmlReportViewer.tsx` (Import: Yes, Usage: No)
- `components\settings\EnhancedThemeSettings.tsx` (Import: Yes, Usage: No)
- `components\settings\UserManagement.tsx` (Import: Yes, Usage: No)
- `components\settings\UserPermissions.tsx` (Import: Yes, Usage: No)
- `components\WeatherWidget.tsx` (Import: Yes, Usage: No)
- `design\DesignSystem.tsx` (Import: Yes, Usage: No)
- `layouts\EnhancedDashboardLayout.tsx` (Import: Yes, Usage: No)
- `layouts\UnifiedDashboardLayout.tsx` (Import: Yes, Usage: No)
- `layouts\UnifiedModuleLayout.tsx` (Import: Yes, Usage: No)
- `pages\animals\AnimalsDashboard.tsx` (Import: Yes, Usage: No)
- `pages\animals\AnimalTracking.tsx` (Import: Yes, Usage: No)
- `pages\animals\AssetManagementDashboard.tsx` (Import: Yes, Usage: No)
- `pages\animals\RetirementTracking.tsx` (Import: Yes, Usage: No)
- `pages\animals\Tracking.tsx` (Import: Yes, Usage: No)
- `pages\breeding\BirthPredictions.tsx` (Import: Yes, Usage: No)
- `pages\breeding\BirthRecords.tsx` (Import: Yes, Usage: No)
- `pages\breeding\BreedingDashboard.tsx` (Import: Yes, Usage: No)
- `pages\breeding\BreedingRecords.tsx` (Import: Yes, Usage: No)
- `pages\breeding\BreedingSchedule.tsx` (Import: Yes, Usage: No)
- `pages\breeding\HeatCalendar.tsx` (Import: Yes, Usage: No)
- `pages\breeding\Schedule.tsx` (Import: Yes, Usage: No)
- `pages\commercial\Auctions.tsx` (Import: Yes, Usage: No)
- `pages\commercial\CommercialDashboard.tsx` (Import: Yes, Usage: No)
- `pages\commercial\Orders.tsx` (Import: Yes, Usage: No)
- `pages\commercial\Pricing.tsx` (Import: Yes, Usage: No)
- `pages\commercial\SupplierDetail.tsx` (Import: Yes, Usage: No)
- `pages\commercial\Suppliers.tsx` (Import: Yes, Usage: No)
- `pages\compliance\Certifications.tsx` (Import: Yes, Usage: No)
- `pages\compliance\ComplianceDashboard.tsx` (Import: Yes, Usage: No)
- `pages\compliance\Inspections.tsx` (Import: Yes, Usage: No)
- `pages\dashboard\Dashboard.tsx` (Import: Yes, Usage: No)
- `pages\feeding\FeedingDashboard.tsx` (Import: Yes, Usage: No)
- `pages\feeding\FeedingInventory.tsx` (Import: Yes, Usage: No)
- `pages\feeding\FeedingPlans.tsx` (Import: Yes, Usage: No)
- `pages\feeding\Inventory.tsx` (Import: Yes, Usage: No)
- `pages\feeding\Suppliers.tsx` (Import: Yes, Usage: No)
- `pages\financial\FinancialBudgets.tsx` (Import: Yes, Usage: No)
- `pages\financial\FinancialDashboard.tsx` (Import: Yes, Usage: No)
- `pages\financial\FinancialOverview.tsx` (Import: Yes, Usage: No)
- `pages\financial\FinancialTransactions.tsx` (Import: Yes, Usage: No)
- `pages\financial\Forecast.tsx` (Import: Yes, Usage: No)
- `pages\financial\Invoices.tsx` (Import: Yes, Usage: No)
- `pages\financial\ROI.tsx` (Import: Yes, Usage: No)
- `pages\health\Appointments.tsx` (Import: Yes, Usage: No)
- `pages\health\Diseases.tsx` (Import: Yes, Usage: No)
- `pages\health\HealthDashboard.tsx` (Import: Yes, Usage: No)
- `pages\health\HealthRecords.tsx` (Import: Yes, Usage: No)
- `pages\health\Records.tsx` (Import: Yes, Usage: No)
- `pages\health\Treatments.tsx` (Import: Yes, Usage: No)
- `pages\health\Vaccinations.tsx` (Import: Yes, Usage: No)
- `pages\Reports.tsx` (Import: Yes, Usage: No)
- `pages\reports\Analysis.tsx` (Import: Yes, Usage: No)
- `pages\reports\Custom.tsx` (Import: Yes, Usage: No)
- `pages\reports\FeedingReports.tsx` (Import: Yes, Usage: No)
- `pages\reports\Financial.tsx` (Import: Yes, Usage: No)
- `pages\reports\FinancialReport.tsx` (Import: Yes, Usage: No)
- `pages\reports\FinancialReports.tsx` (Import: Yes, Usage: No)
- `pages\reports\Health.tsx` (Import: Yes, Usage: No)
- `pages\reports\HealthReports.tsx` (Import: Yes, Usage: No)
- `pages\reports\Market.tsx` (Import: Yes, Usage: No)
- `pages\reports\Performance.tsx` (Import: Yes, Usage: No)
- `pages\reports\PredictiveAnalysis.tsx` (Import: Yes, Usage: No)
- `pages\reports\ReportsDashboard.tsx` (Import: Yes, Usage: No)
- `pages\resources\Documentation.tsx` (Import: Yes, Usage: No)
- `pages\resources\Downloads.tsx` (Import: Yes, Usage: No)
- `pages\resources\Guidelines.tsx` (Import: Yes, Usage: No)
- `pages\resources\ResourceMaintenance.tsx` (Import: Yes, Usage: No)
- `pages\resources\ResourcesDashboard.tsx` (Import: Yes, Usage: No)
- `pages\resources\ResourcesList.tsx` (Import: Yes, Usage: No)
- `pages\resources\Support.tsx` (Import: Yes, Usage: No)
- `pages\resources\Training.tsx` (Import: Yes, Usage: No)
- `pages\Settings.tsx` (Import: Yes, Usage: No)
- `pages\settings\UserManagement.tsx` (Import: Yes, Usage: No)

## Files Using CustomButton

- `App.tsx`
- `components\admin\DataMigration.tsx`
- `components\animals\AnimalFilters.tsx`
- `components\animals\AnimalForm.tsx`
- `components\animals\RealTimeTracking.tsx`
- `components\animals\RFIDTracking.tsx`
- `components\breeding\BreedingList.tsx`
- `components\breeding\TestNotification.tsx`
- `components\commercial\BkbAuctionFilters.tsx`
- `components\common\AnimatedDashboardCard.tsx`
- `components\common\BlendedImageCard.tsx`
- `components\common\CustomButton.tsx`
- `components\common\DirectFixButton.tsx`
- `components\common\ErrorBoundary.tsx`
- `components\common\FallbackUI.tsx`
- `components\common\FeaturedDealCard.tsx`
- `components\common\GlobalSafeButton.tsx`
- `components\common\ImageTest.tsx`
- `components\common\LazyLoadFallback.tsx`
- `components\common\ModuleDashboard.tsx`
- `components\common\ModuleHeaderCard.tsx`
- `components\common\ModuleItemCard.tsx`
- `components\common\ModulePage.tsx`
- `components\common\ProductCard.tsx`
- `components\common\SubModuleActionPanel.tsx`
- `components\common\ThemeSafeButton.tsx`
- `components\LanguageTest.tsx`
- `components\modules\animals\AnimalMarketplaceCard.tsx`
- `components\modules\animals\AnimalMarketplaceGrid.tsx`
- `components\modules\animals\AnimalsDashboard.tsx`
- `components\modules\feeding\FeedingDashboard.tsx`
- `components\modules\health\HealthRecords.tsx`
- `components\modules\reports\MetricsChart.tsx`
- `components\modules\reports\ReportGenerator.tsx`
- `components\settings\ThemeSettings.tsx`
- `pages\animals\AnimalDetail.tsx`
- `pages\animals\AnimalForm.tsx`
- `pages\animals\AnimalsList.tsx`
- `pages\animals\HealthPrediction.tsx`
- `pages\breeding\PregnancyTracking.tsx`
- `pages\commercial\Marketplace.tsx`
- `pages\feeding\FeedingSchedules.tsx`
- `pages\financial\FinancialReports.tsx`
- `pages\reports\ReportPage.tsx`
- `pages\resources\GovernmentResources.tsx`
- `pages\settings\BackupSettings.tsx`
- `pages\settings\DatabaseSettings.tsx`
- `pages\TranslationTest.tsx`
- `utils\animationUtils.tsx`
