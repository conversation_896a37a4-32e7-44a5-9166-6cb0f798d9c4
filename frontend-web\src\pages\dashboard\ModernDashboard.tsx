import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Grid,
  Typography,
  Box,
  Card,
  CardContent,
  Divider,
  IconButton,
  alpha,
  useTheme,
  Chip,
  Paper
} from '@mui/material';
import {
  Pets,
  LocalHospital,
  Spa,
  Restaurant,
  MonetizationOn,
  BarChart,
  Notifications,
  Add,
  ArrowForward,
  Settings,
  Description,
  Assignment,
  Dashboard as DashboardIcon,
  TrendingUp,
  CalendarToday,
  Assessment,
  Analytics,
  Refresh
} from '../../utils/iconImports';
import Store from '@mui/icons-material/Store';
// Removed useThemeContext import as it was deleted
import { useLanguage } from '../../contexts/LanguageContext';
import { ROUTES } from '../../constants/routes';

// Import components
import {
  StandardDashboard,
  ModernChart,
  AnimatedBackgroundCard,
  CustomButton
} from '../../components/common';
import SponsorRotator from '../../components/dashboard/SponsorRotator';
import WeatherWidget from '../../components/dashboard/WeatherWidget';
import QuickActions from '../../components/dashboard/QuickActions';
import TaskManager from '../../components/dashboard/TaskManager';
import NotificationCenter from '../../components/dashboard/NotificationCenter';

// Import data hooks
import { useAnimalData } from '../../hooks/useAnimalData';
import { useFinancialData } from '../../hooks/useFinancialData';
import { useHealthRecords } from '../../hooks/useHealthRecords';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';

// Initial data for dashboard stats
const initialDashboardStats = {
  totalAnimals: 0,
  activeAnimals: 0,
  pendingTasks: 0,
  healthAlerts: 0,
  feedingSchedules: 0,
  recentBirths: 0,
  financialTransactions: 0,
  complianceDocuments: 0,
  marketOrders: 0,
  totalIncome: 0,
  totalExpenses: 0,
  profit: 0,
  profitMargin: '0'
};

// Mock data for charts
const animalDistributionData = [
  { name: 'Cattle', value: 120 },
  { name: 'Sheep', value: 200 },
  { name: 'Goats', value: 80 },
  { name: 'Pigs', value: 40 },
];

const monthlyActivityData = [
  { month: 'Jan', animals: 12, health: 8, breeding: 5, financial: 10 },
  { month: 'Feb', animals: 15, health: 10, breeding: 7, financial: 12 },
  { month: 'Mar', animals: 18, health: 12, breeding: 9, financial: 15 },
  { month: 'Apr', animals: 14, health: 9, breeding: 6, financial: 11 },
  { month: 'May', animals: 20, health: 15, breeding: 10, financial: 18 },
  { month: 'Jun', animals: 25, health: 18, breeding: 12, financial: 22 }
];

const recentActivityData = [
  {
    id: 1,
    title: 'New animal added: TAG045 (Angus Bull)',
    time: 'Today, 14:30',
    type: 'animal',
    status: 'new',
    path: '/animals/details/TAG045'
  },
  {
    id: 2,
    title: 'Health alert: TAG023 requires vaccination',
    time: 'Today, 11:15',
    type: 'health',
    status: 'alert',
    path: '/health/alerts/TAG023'
  },
  {
    id: 3,
    title: 'Breeding record updated: TAG018 confirmed pregnant',
    time: 'Yesterday, 16:45',
    type: 'breeding',
    status: 'update',
    path: '/breeding/records/TAG018'
  },
  {
    id: 4,
    title: 'Feed inventory updated: 500kg of feed added',
    time: 'Yesterday, 09:30',
    type: 'feed',
    status: 'update',
    path: '/feed/inventory'
  }
];

const upcomingTasksData = [
  {
    id: 1,
    title: 'Vaccination Schedule',
    description: '5 animals require vaccination',
    dueDate: 'Due Today',
    priority: 'high',
    path: '/health/vaccination'
  },
  {
    id: 2,
    title: 'Feed Inventory Check',
    description: 'Monthly inventory verification',
    dueDate: 'Due Tomorrow',
    priority: 'medium',
    path: '/feed/inventory'
  },
  {
    id: 3,
    title: 'Breeding Program Review',
    description: 'Quarterly breeding program assessment',
    dueDate: 'Due in 3 days',
    priority: 'medium',
    path: '/breeding/program'
  },
  {
    id: 4,
    title: 'Financial Report Generation',
    description: 'Monthly financial summary',
    dueDate: 'Due in 5 days',
    priority: 'low',
    path: '/financial/reports'
  }
];

// Sample tasks for the task manager - we'll use a function to create these with translations
const createSampleTasks = (translate?: (key: string, options?: any) => string) => [
  {
    id: '1',
    title: translate ? translate('tasks.sample.vaccination', { fallback: 'Vaccinate cattle' }) : 'Vaccinate cattle',
    description: translate ? translate('tasks.sample.vaccination_desc', { fallback: 'Administer quarterly vaccinations to all cattle' }) : 'Administer quarterly vaccinations to all cattle',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    priority: 'high' as const,
    completed: false,
    category: 'health',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
  },
  {
    id: '2',
    title: translate ? translate('tasks.sample.feed_order', { fallback: 'Order feed supplies' }) : 'Order feed supplies',
    description: translate ? translate('tasks.sample.feed_order_desc', { fallback: 'Restock feed inventory before it runs out' }) : 'Restock feed inventory before it runs out',
    dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
    priority: 'medium' as const,
    completed: false,
    category: 'feeding',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000) // 3 days ago
  },
  {
    id: '3',
    title: translate ? translate('tasks.sample.maintenance', { fallback: 'Fence maintenance' }) : 'Fence maintenance',
    description: translate ? translate('tasks.sample.maintenance_desc', { fallback: 'Repair damaged fencing in the north pasture' }) : 'Repair damaged fencing in the north pasture',
    dueDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago (overdue)
    priority: 'high' as const,
    completed: false,
    category: 'maintenance',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
  },
  {
    id: '4',
    title: translate ? translate('tasks.sample.financial_report', { fallback: 'Prepare financial report' }) : 'Prepare financial report',
    description: translate ? translate('tasks.sample.financial_report_desc', { fallback: 'Compile monthly financial summary for review' }) : 'Compile monthly financial summary for review',
    dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    priority: 'medium' as const,
    completed: false,
    category: 'financial',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
  },
  {
    id: '5',
    title: translate ? translate('tasks.sample.breeding', { fallback: 'Update breeding records' }) : 'Update breeding records',
    description: translate ? translate('tasks.sample.breeding_desc', { fallback: 'Record recent breeding activities and update expected birth dates' }) : 'Record recent breeding activities and update expected birth dates',
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
    priority: 'low' as const,
    completed: true,
    category: 'breeding',
    createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000) // 4 days ago
  }
];

// Create a default version of sample tasks without translations
const sampleTasks = createSampleTasks();

interface ModernDashboardProps {
  activeTabIndex?: number;
  onTabChange?: (index: number) => void;
}

const ModernDashboard: React.FC<ModernDashboardProps> = ({
  activeTabIndex: externalActiveTabIndex,
  onTabChange: externalTabChangeHandler
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();

  // State for dashboard stats
  const [dashboardStats, setDashboardStats] = useState(initialDashboardStats);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<number | null>(null);
  const [selectedTask, setSelectedTask] = useState<number | null>(null);

  // Use external tab state if provided, otherwise use internal state
  const [internalActiveTabIndex, setInternalActiveTabIndex] = useState(0);
  const activeTabIndex = externalActiveTabIndex !== undefined ? externalActiveTabIndex : internalActiveTabIndex;

  // Get data from hooks
  const { animals, loading: animalsLoading } = useAnimalData();
  const { transactions, loading: financialLoading } = useFinancialData();
  const { healthRecords, loading: healthLoading } = useHealthRecords();
  const { breedingRecords, loading: breedingLoading } = useBreedingRecords();

  // Update dashboard stats with real data when available
  useEffect(() => {
    if (!animalsLoading && !financialLoading && !healthLoading && !breedingLoading) {
      try {
        // Ensure animals is an array
        const animalsArray = Array.isArray(animals) ? animals : [];

        // Calculate total income and expenses
        const totalIncome = Array.isArray(transactions)
          ? transactions
              .filter(t => t && t.type === 'income')
              .reduce((sum, t) => sum + (t.amount || 0), 0)
          : 0;

        const totalExpenses = Array.isArray(transactions)
          ? transactions
              .filter(t => t && t.type === 'expense')
              .reduce((sum, t) => sum + (t.amount || 0), 0)
          : 0;

        // Calculate profit
        const profit = totalIncome - totalExpenses;
        const profitMargin = totalIncome > 0 ? (profit / totalIncome) * 100 : 0;

        // Get recent health records (last 30 days)
        const recentHealthRecords = Array.isArray(healthRecords)
          ? healthRecords.filter(r => r && r.date &&
              new Date(r.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
          : [];

        // Get recent breeding records (last 30 days)
        const recentBreedingRecords = Array.isArray(breedingRecords)
          ? breedingRecords.filter(r => r && r.date &&
              new Date(r.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
          : [];

        // Count active animals safely
        const activeAnimals = Array.isArray(animalsArray)
          ? animalsArray.filter(a => a && a.status === 'Active').length
          : 0;

        // Count health alerts safely
        const healthAlertsCount = Array.isArray(healthRecords)
          ? healthRecords.filter(r => r && r.priority === 'high').length
          : 0;

        // Count recent births safely
        const recentBirthsCount = Array.isArray(breedingRecords)
          ? breedingRecords.filter(r => r && r.status === 'confirmed' && r.date &&
              new Date(r.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length
          : 0;

        // Count market orders safely
        const marketOrdersCount = Array.isArray(transactions)
          ? Math.ceil(transactions.filter(t => t && t.type === 'income' && t.category === 'sales' && t.date &&
              new Date(t.date) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)).length * 0.7)
          : 0;

        setDashboardStats({
          totalAnimals: animalsArray.length,
          activeAnimals: activeAnimals,
          pendingTasks: Math.floor(recentHealthRecords.length * 0.5) + Math.floor(recentBreedingRecords.length * 0.3),
          healthAlerts: healthAlertsCount,
          feedingSchedules: Math.ceil(animalsArray.length * 0.25), // Estimate based on animal count
          recentBirths: recentBirthsCount,
          financialTransactions: Array.isArray(transactions) ? transactions.length : 0,
          complianceDocuments: Math.ceil(animalsArray.length * 0.2), // Estimate based on animal count
          marketOrders: marketOrdersCount,
          totalIncome,
          totalExpenses,
          profit,
          profitMargin: profitMargin.toFixed(1)
        });
      } catch (error) {
        console.error('Error updating dashboard stats:', error);
        // Set default values in case of error
        setDashboardStats(initialDashboardStats);
      }
    }
  }, [animals, transactions, healthRecords, breedingRecords, animalsLoading, financialLoading, healthLoading, breedingLoading]);

  // Handle refresh data
  const handleRefreshData = useCallback(() => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => {
      setIsLoading(false);
    }, 1500);
  }, []);

  // Handle activity click
  const handleActivityClick = (id: number, path: string) => {
    setSelectedActivity(id);
    setTimeout(() => {
      navigate(path);
    }, 300);
  };

  // Handle task click
  const handleTaskClick = (id: number, path: string) => {
    setSelectedTask(id);
    setTimeout(() => {
      navigate(path);
    }, 300);
  };

  // Handle tab change
  const handleTabChange = (index: number) => {
    console.log('ModernDashboard: Tab changed to:', index);
    console.log('Previous active tab:', activeTabIndex);
    console.log('Tabs available:', dashboardTabs.map(tab => tab.label));

    // Use external handler if provided, otherwise use internal state
    if (externalTabChangeHandler) {
      console.log('Using external tab change handler');
      externalTabChangeHandler(index);
    } else {
      console.log('Using internal tab change handler');
      setInternalActiveTabIndex(index);
    }

    // Force a re-render after a short delay to ensure state updates are applied
    setTimeout(() => {
      console.log('Active tab after update:', activeTabIndex);
    }, 100);
  };

  // Prepare dashboard stats
  const dashboardStatsForDisplay = [
    {
      label: translate ? translate('dashboard.total_animals', { fallback: 'Total Animals' }) : 'Total Animals',
      value: dashboardStats.totalAnimals.toString(),
      icon: <Pets />,
      color: theme.palette.primary.main,
      trend: {
        value: 8,
        isPositive: true,
        label: translate ? translate('dashboard.since_last_month', { fallback: 'since last month' }) : 'since last month'
      }
    },
    {
      label: translate ? translate('dashboard.pending_tasks', { fallback: 'Pending Tasks' }) : 'Pending Tasks',
      value: dashboardStats.pendingTasks.toString(),
      icon: <Assignment />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: false,
        label: translate ? translate('dashboard.since_last_month', { fallback: 'since last month' }) : 'since last month'
      }
    },
    {
      label: translate ? translate('dashboard.health_alerts', { fallback: 'Health Alerts' }) : 'Health Alerts',
      value: dashboardStats.healthAlerts.toString(),
      icon: <LocalHospital />,
      color: theme.palette.error.main,
      trend: {
        value: 2,
        isPositive: false,
        label: translate ? translate('dashboard.since_last_month', { fallback: 'since last month' }) : 'since last month'
      }
    },
    {
      label: translate ? translate('dashboard.recent_births', { fallback: 'Recent Births' }) : 'Recent Births',
      value: dashboardStats.recentBirths.toString(),
      icon: <Spa />,
      color: theme.palette.success.main,
      trend: {
        value: 5,
        isPositive: true,
        label: translate ? translate('dashboard.since_last_month', { fallback: 'since last month' }) : 'since last month'
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translate ? translate('dashboard.add_animal', { fallback: 'Add Animal' }) : 'Add Animal',
      icon: <Add />,
      onClick: () => navigate('/animals/new'),
      color: 'primary'
    },
    {
      label: translate ? translate('dashboard.refresh', { fallback: 'Refresh' }) : 'Refresh',
      icon: <Refresh />,
      onClick: handleRefreshData,
      color: 'primary'
    }
  ];

  // Prepare dashboard tabs
  const dashboardTabs = [
    {
      label: translate ? translate('dashboard.overview', { fallback: 'Overview' }) : 'Overview',
      icon: <DashboardIcon />,
      content: (
        <Box>
          {/* Sponsor Rotator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            style={{ marginBottom: '24px' }}
          >
            <SponsorRotator interval={10000} />
          </motion.div>

          {/* Weather Widget */}
          <Box mb={3}>
            <WeatherWidget location="Pretoria, South Africa" refreshInterval={1800000} />
          </Box>

          {/* Charts Section */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.animal_distribution', { fallback: 'Animal Distribution' }) : 'Animal Distribution'}
                subtitle={translate ? translate('dashboard.animal_distribution_desc', { fallback: 'Distribution of animals by species' }) : 'Distribution of animals by species'}
                data={animalDistributionData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="dashboard"
                tooltip={translate ? translate('dashboard.animal_distribution_help', { fallback: 'Shows the distribution of animals across different species' }) : 'Shows the distribution of animals across different species'}
                formatValue={(value) => `${value} animals`}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.monthly_activity', { fallback: 'Monthly Activity' }) : 'Monthly Activity'}
                subtitle={translate ? translate('dashboard.monthly_activity_desc', { fallback: 'Activity across different modules' }) : 'Activity across different modules'}
                data={monthlyActivityData}
                type="line"
                dataKeys={['animals', 'health', 'breeding', 'financial']}
                xAxisDataKey="month"
                height={350}
                accentColor={theme.palette.secondary.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="dashboard"
                tooltip={translate ? translate('dashboard.monthly_activity_help', { fallback: 'Shows activity trends across different modules' }) : 'Shows activity trends across different modules'}
                formatValue={(value) => `${value} actions`}
              />
            </Grid>
          </Grid>

          {/* Financial Summary Section */}
          <Box mb={4}>
            <AnimatedBackgroundCard
              title={translate ? translate('dashboard.financial_summary', { fallback: 'Financial Summary' }) : 'Financial Summary'}
              subtitle={translate ? translate('dashboard.financial_summary_desc', { fallback: 'Key financial metrics and trends' }) : 'Key financial metrics and trends'}
              icon={<MonetizationOn />}
              accentColor={theme.palette.info.main}
              secondaryColor={theme.palette.info.dark}
              delay={0.2}
              actionLabel={translate ? translate('dashboard.view_details', { fallback: 'View Details' }) : 'View Details'}
              onAction={() => navigate(ROUTES.FINANCIAL)}
            >
              <Grid container spacing={2} sx={{ mt: 1 }}>
                {/* Total Income */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.financial.total_income', { fallback: 'Total Income' }) : 'Total Income'}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" color="success.main">
                      R{dashboardStats.totalIncome.toLocaleString()}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Box
                        sx={{
                          width: '100%',
                          height: 4,
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.success.main, 0.2),
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            height: '100%',
                            width: `${Math.min(100, (dashboardStats.totalIncome / (dashboardStats.totalIncome + dashboardStats.totalExpenses)) * 100)}%`,
                            bgcolor: theme.palette.success.main,
                            borderRadius: 2
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Total Expenses */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.error.main, 0.1),
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.financial.total_expenses', { fallback: 'Total Expenses' }) : 'Total Expenses'}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" color="error.main">
                      R{dashboardStats.totalExpenses.toLocaleString()}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Box
                        sx={{
                          width: '100%',
                          height: 4,
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.error.main, 0.2),
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            height: '100%',
                            width: `${Math.min(100, (dashboardStats.totalExpenses / (dashboardStats.totalIncome + dashboardStats.totalExpenses)) * 100)}%`,
                            bgcolor: theme.palette.error.main,
                            borderRadius: 2
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Net Profit */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.financial.net_profit', { fallback: 'Net Profit' }) : 'Net Profit'}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" color="primary.main">
                      R{dashboardStats.profit.toLocaleString()}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Typography variant="caption" color={dashboardStats.profit > 0 ? 'success.main' : 'error.main'}>
                        {dashboardStats.profit > 0 ? '+' : ''}{((dashboardStats.profit / dashboardStats.totalIncome) * 100).toFixed(1)}%
                        {translate ? translate('dashboard.financial.of_revenue', { fallback: ' of revenue' }) : ' of revenue'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>

                {/* Profit Margin */}
                <Grid item xs={12} sm={6} md={3}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.warning.main, 0.1),
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center'
                    }}
                  >
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.financial.profit_margin', { fallback: 'Profit Margin' }) : 'Profit Margin'}
                    </Typography>
                    <Typography variant="h5" fontWeight="bold" color="warning.main">
                      {dashboardStats.profitMargin}%
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Box
                        sx={{
                          width: '100%',
                          height: 4,
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.warning.main, 0.2),
                          position: 'relative',
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            height: '100%',
                            width: `${Math.min(100, parseFloat(dashboardStats.profitMargin))}%`,
                            bgcolor: theme.palette.warning.main,
                            borderRadius: 2
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                </Grid>
              </Grid>

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <CustomButton
                  size="small"
                  onClick={() => navigate(`${ROUTES.FINANCIAL}/reports`)}
                >
                  {translate ? translate('dashboard.financial.generate_report', { fallback: 'Generate Report' }) : 'Generate Report'}
                </CustomButton>
              </Box>
            </AnimatedBackgroundCard>
          </Box>

          {/* Recent Activity and Upcoming Tasks */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.recent_activity', { fallback: 'Recent Activity' }) : 'Recent Activity'}
                subtitle={translate ? translate('dashboard.recent_activity_desc', { fallback: 'Latest updates and changes' }) : 'Latest updates and changes'}
                icon={<Description />}
                height={400}
                accentColor={theme.palette.primary.main}
                secondaryColor={theme.palette.primary.dark}
                delay={0.3}
                actionLabel={translate ? translate('dashboard.view_all', { fallback: 'View All' }) : 'View All'}
                onAction={() => navigate('/dashboard/activity')}
              >
                <Box sx={{ mt: 2 }}>
                  {recentActivityData.map((activity) => (
                    <Box
                      key={activity.id}
                      sx={{
                        p: 2,
                        mb: 2,
                        borderRadius: '8px',
                        bgcolor: selectedActivity === activity.id
                          ? alpha(theme.palette.primary.main, 0.1)
                          : alpha(theme.palette.background.paper, 0.05),
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          transform: 'translateY(-2px)',
                          transition: 'all 0.3s ease'
                        },
                        cursor: 'pointer',
                        transition: 'all 0.3s ease'
                      }}
                      onClick={() => handleActivityClick(activity.id, activity.path)}
                    >
                      <Box display="flex" alignItems="center">
                        <Box
                          sx={{
                            width: 10,
                            height: 10,
                            borderRadius: '50%',
                            bgcolor: activity.status === 'alert'
                              ? theme.palette.error.main
                              : activity.status === 'new'
                                ? theme.palette.success.main
                                : theme.palette.info.main,
                            mr: 2
                          }}
                        />
                        <Box flex="1">
                          <Typography variant="body2" fontWeight="medium">{activity.title}</Typography>
                          <Typography variant="caption" color="text.secondary">{activity.time}</Typography>
                        </Box>
                        <IconButton size="small">
                          <ArrowForward fontSize="small" />
                        </IconButton>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            <Grid item xs={12} md={6}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.upcoming_tasks', { fallback: 'Upcoming Tasks' }) : 'Upcoming Tasks'}
                subtitle={translate ? translate('dashboard.upcoming_tasks_desc', { fallback: 'Tasks that need your attention' }) : 'Tasks that need your attention'}
                icon={<Assignment />}
                height={400}
                accentColor={theme.palette.warning.main}
                secondaryColor={theme.palette.warning.dark}
                delay={0.4}
                actionLabel={translate ? translate('dashboard.view_all', { fallback: 'View All' }) : 'View All'}
                onAction={() => navigate('/dashboard/tasks')}
              >
                <Box sx={{ mt: 2, maxHeight: 320, overflow: 'auto' }}>
                  <TaskManager initialTasks={createSampleTasks(translate)} />
                </Box>
              </AnimatedBackgroundCard>
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate ? translate('dashboard.modules', { fallback: 'Modules' }) : 'Modules',
      icon: <Assessment />,
      content: (
        <Box>
          <Grid container spacing={3}>
            {/* Animal Management */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.animals.title', { fallback: 'Animal Management' }) : 'Animal Management'}
                subtitle={translate ? translate('dashboard.animals.description', { fallback: 'Manage your livestock inventory' }) : 'Manage your livestock inventory'}
                icon={<Pets />}
                backgroundImage="/images/modules/animals/cattle-1.jpeg"
                accentColor={theme.palette.primary.main}
                secondaryColor={theme.palette.primary.dark}
                delay={0.1}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.ANIMALS)}
                tags={[
                  { label: `${dashboardStats.totalAnimals} Animals`, color: 'primary' },
                  { label: `${dashboardStats.activeAnimals} Active`, color: 'success' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.animals.content', { fallback: 'Track and manage all your animals with comprehensive profiles, health records, and growth tracking.' }) : 'Track and manage all your animals with comprehensive profiles, health records, and growth tracking.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.ANIMALS}/profiles`);
                      }}
                    >
                      {translate ? translate('dashboard.animals.profiles', { fallback: 'Profiles' }) : 'Profiles'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.ANIMALS}/new`);
                      }}
                    >
                      {translate ? translate('dashboard.animals.add', { fallback: 'Add New' }) : 'Add New'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            {/* Breeding Management */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.breeding.title', { fallback: 'Breeding Management' }) : 'Breeding Management'}
                subtitle={translate ? translate('dashboard.breeding.description', { fallback: 'Track breeding cycles and pregnancies' }) : 'Track breeding cycles and pregnancies'}
                icon={<Spa />}
                backgroundImage="/images/modules/breeding/breeding-main.png"
                accentColor={theme.palette.secondary.main}
                secondaryColor={theme.palette.secondary.dark}
                delay={0.2}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.BREEDING)}
                tags={[
                  { label: `${dashboardStats.recentBirths} Recent Births`, color: 'success' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.breeding.content', { fallback: 'Manage breeding programs, track heat cycles, and monitor pregnancies for optimal reproductive performance.' }) : 'Manage breeding programs, track heat cycles, and monitor pregnancies for optimal reproductive performance.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.BREEDING}/records`);
                      }}
                    >
                      {translate ? translate('dashboard.breeding.records', { fallback: 'Records' }) : 'Records'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.BREEDING}/heat-calendar`);
                      }}
                    >
                      {translate ? translate('dashboard.breeding.calendar', { fallback: 'Calendar' }) : 'Calendar'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            {/* Health Management */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.health.title', { fallback: 'Health Management' }) : 'Health Management'}
                subtitle={translate ? translate('dashboard.health.description', { fallback: 'Monitor animal health and treatments' }) : 'Monitor animal health and treatments'}
                icon={<LocalHospital />}
                backgroundImage="/images/modules/health/health-main.png"
                accentColor={theme.palette.success.main}
                secondaryColor={theme.palette.success.dark}
                delay={0.3}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.HEALTH)}
                tags={[
                  { label: `${dashboardStats.healthAlerts} Alerts`, color: 'error' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.health.content', { fallback: 'Track vaccinations, treatments, and health records to maintain optimal herd health and prevent disease outbreaks.' }) : 'Track vaccinations, treatments, and health records to maintain optimal herd health and prevent disease outbreaks.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.HEALTH}/records`);
                      }}
                    >
                      {translate ? translate('dashboard.health.records', { fallback: 'Records' }) : 'Records'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.HEALTH}/treatments`);
                      }}
                    >
                      {translate ? translate('dashboard.health.treatments', { fallback: 'Treatments' }) : 'Treatments'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            {/* Feed Management */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.feeding.title', { fallback: 'Feed Management' }) : 'Feed Management'}
                subtitle={translate ? translate('dashboard.feeding.description', { fallback: 'Manage feed inventory and schedules' }) : 'Manage feed inventory and schedules'}
                icon={<Restaurant />}
                backgroundImage="/images/modules/feeding/feed-main.jpeg"
                accentColor={theme.palette.warning.main}
                secondaryColor={theme.palette.warning.dark}
                delay={0.4}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.FEED_MANAGEMENT)}
                tags={[
                  { label: `${dashboardStats.feedingSchedules} Schedules`, color: 'warning' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.feeding.content', { fallback: 'Track feed inventory, create feeding schedules, and monitor nutrition for optimal animal growth and health.' }) : 'Track feed inventory, create feeding schedules, and monitor nutrition for optimal animal growth and health.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.FEED_MANAGEMENT}/inventory`);
                      }}
                    >
                      {translate ? translate('dashboard.feeding.inventory', { fallback: 'Inventory' }) : 'Inventory'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.FEED_MANAGEMENT}/schedules`);
                      }}
                    >
                      {translate ? translate('dashboard.feeding.schedules', { fallback: 'Schedules' }) : 'Schedules'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            {/* Financial Management */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.financial.title', { fallback: 'Financial Management' }) : 'Financial Management'}
                subtitle={translate ? translate('dashboard.financial.description', { fallback: 'Track expenses and revenue' }) : 'Track expenses and revenue'}
                icon={<MonetizationOn />}
                backgroundImage="/images/modules/financial/financial-main.jpg"
                accentColor={theme.palette.info.main}
                secondaryColor={theme.palette.info.dark}
                delay={0.5}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.FINANCIAL)}
                tags={[
                  { label: `${dashboardStats.profitMargin}% Profit`, color: 'info' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.financial.content', { fallback: 'Track income, expenses, and generate financial reports to monitor farm profitability and financial health.' }) : 'Track income, expenses, and generate financial reports to monitor farm profitability and financial health.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.FINANCIAL}/overview`);
                      }}
                    >
                      {translate ? translate('dashboard.financial.overview', { fallback: 'Overview' }) : 'Overview'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.FINANCIAL}/transactions`);
                      }}
                    >
                      {translate ? translate('dashboard.financial.transactions', { fallback: 'Transactions' }) : 'Transactions'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>

            {/* Commercial */}
            <Grid item xs={12} sm={6} md={4}>
              <AnimatedBackgroundCard
                title={translate ? translate('dashboard.commercial.title', { fallback: 'Commercial' }) : 'Commercial'}
                subtitle={translate ? translate('dashboard.commercial.description', { fallback: 'Manage market operations and sales' }) : 'Manage market operations and sales'}
                icon={<Store />}
                backgroundImage="/images/modules/commercial/commercial-main.webp"
                accentColor={theme.palette.secondary.dark}
                secondaryColor={theme.palette.secondary.main}
                delay={0.6}
                actionLabel={translate ? translate('dashboard.open', { fallback: 'Open' }) : 'Open'}
                onAction={() => navigate(ROUTES.COMMERCIAL)}
                tags={[
                  { label: `${dashboardStats.marketOrders} Orders`, color: 'secondary' }
                ]}
              >
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {translate ? translate('dashboard.commercial.content', { fallback: 'Manage sales, track orders, and monitor market trends to optimize your commercial operations.' }) : 'Manage sales, track orders, and monitor market trends to optimize your commercial operations.'}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.COMMERCIAL}/orders`);
                      }}
                    >
                      {translate ? translate('dashboard.commercial.orders', { fallback: 'Orders' }) : 'Orders'}
                    </CustomButton>
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`${ROUTES.COMMERCIAL}/products`);
                      }}
                    >
                      {translate ? translate('dashboard.commercial.products', { fallback: 'Products' }) : 'Products'}
                    </CustomButton>
                  </Box>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate ? translate('dashboard.analytics', { fallback: 'Analytics' }) : 'Analytics',
      icon: <Analytics />,
      content: (
        <Box>
          {/* Farm Performance Score */}
          <Box mb={4}>
            <AnimatedBackgroundCard
              title={translate ? translate('dashboard.analytics.farm_score', { fallback: 'Farm Performance Score' }) : 'Farm Performance Score'}
              subtitle={translate ? translate('dashboard.analytics.farm_score_desc', { fallback: 'Overall health and performance metrics' }) : 'Overall health and performance metrics'}
              icon={<TrendingUp />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              overlay="gradient"
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 3 }}>
                <Box
                  sx={{
                    position: 'relative',
                    width: 200,
                    height: 200,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  {/* Circular progress background */}
                  <Box
                    sx={{
                      position: 'absolute',
                      width: '100%',
                      height: '100%',
                      borderRadius: '50%',
                      background: `conic-gradient(
                        ${theme.palette.success.main} 0%,
                        ${theme.palette.success.main} ${dashboardStats.profitMargin}%,
                        ${alpha(theme.palette.grey[300], 0.2)} ${dashboardStats.profitMargin}%,
                        ${alpha(theme.palette.grey[300], 0.2)} 100%
                      )`,
                      boxShadow: `0 0 30px ${alpha(theme.palette.success.main, 0.3)}`,
                    }}
                  />

                  {/* Inner circle */}
                  <Box
                    sx={{
                      position: 'absolute',
                      width: '80%',
                      height: '80%',
                      borderRadius: '50%',
                      background: alpha(theme.palette.background.paper, 0.9),
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h3" fontWeight="bold" color="primary">
                      {dashboardStats.profitMargin}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {translate ? translate('dashboard.analytics.performance', { fallback: 'Performance' }) : 'Performance'}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Grid container spacing={2} sx={{ mt: 2 }}>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 1, borderRadius: 1, bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <Typography variant="h6" fontWeight="bold" color="primary">
                      {dashboardStats.totalAnimals}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.analytics.animals', { fallback: 'Animals' }) : 'Animals'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 1, borderRadius: 1, bgcolor: alpha(theme.palette.success.main, 0.1) }}>
                    <Typography variant="h6" fontWeight="bold" color="success.main">
                      {dashboardStats.recentBirths}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.analytics.births', { fallback: 'Births' }) : 'Births'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 1, borderRadius: 1, bgcolor: alpha(theme.palette.error.main, 0.1) }}>
                    <Typography variant="h6" fontWeight="bold" color="error.main">
                      {dashboardStats.healthAlerts}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.analytics.alerts', { fallback: 'Alerts' }) : 'Alerts'}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center', p: 1, borderRadius: 1, bgcolor: alpha(theme.palette.info.main, 0.1) }}>
                    <Typography variant="h6" fontWeight="bold" color="info.main">
                      R{dashboardStats.totalIncome.toLocaleString()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {translate ? translate('dashboard.analytics.revenue', { fallback: 'Revenue' }) : 'Revenue'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </AnimatedBackgroundCard>
          </Box>

          {/* Financial Analytics */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.analytics.income_expense', { fallback: 'Income vs Expenses' }) : 'Income vs Expenses'}
                subtitle={translate ? translate('dashboard.analytics.income_expense_desc', { fallback: 'Monthly financial performance' }) : 'Monthly financial performance'}
                data={[
                  { month: 'Jan', income: dashboardStats.totalIncome * 0.15, expenses: dashboardStats.totalExpenses * 0.14 },
                  { month: 'Feb', income: dashboardStats.totalIncome * 0.18, expenses: dashboardStats.totalExpenses * 0.16 },
                  { month: 'Mar', income: dashboardStats.totalIncome * 0.22, expenses: dashboardStats.totalExpenses * 0.19 },
                  { month: 'Apr', income: dashboardStats.totalIncome * 0.19, expenses: dashboardStats.totalExpenses * 0.21 },
                  { month: 'May', income: dashboardStats.totalIncome * 0.25, expenses: dashboardStats.totalExpenses * 0.22 },
                  { month: 'Jun', income: dashboardStats.totalIncome * 0.28, expenses: dashboardStats.totalExpenses * 0.24 }
                ]}
                type="bar"
                dataKeys={['income', 'expenses']}
                xAxisDataKey="month"
                height={350}
                accentColor={theme.palette.info.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="financial"
                formatValue={(value) => `R${value.toLocaleString()}`}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.analytics.profit_margin', { fallback: 'Profit Margin Trend' }) : 'Profit Margin Trend'}
                subtitle={translate ? translate('dashboard.analytics.profit_margin_desc', { fallback: 'Monthly profit margin percentage' }) : 'Monthly profit margin percentage'}
                data={[
                  { month: 'Jan', margin: parseFloat(dashboardStats.profitMargin) * 0.85 },
                  { month: 'Feb', margin: parseFloat(dashboardStats.profitMargin) * 0.9 },
                  { month: 'Mar', margin: parseFloat(dashboardStats.profitMargin) * 1.05 },
                  { month: 'Apr', margin: parseFloat(dashboardStats.profitMargin) * 0.95 },
                  { month: 'May', margin: parseFloat(dashboardStats.profitMargin) * 1.1 },
                  { month: 'Jun', margin: parseFloat(dashboardStats.profitMargin) * 1.15 }
                ]}
                type="line"
                dataKeys={['margin']}
                xAxisDataKey="month"
                height={350}
                accentColor={theme.palette.success.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="financial"
                formatValue={(value) => `${value.toFixed(1)}%`}
              />
            </Grid>
          </Grid>

          {/* Livestock Analytics */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.analytics.animal_growth', { fallback: 'Animal Growth Rates' }) : 'Animal Growth Rates'}
                subtitle={translate ? translate('dashboard.analytics.animal_growth_desc', { fallback: 'Average weight gain by species' }) : 'Average weight gain by species'}
                data={[
                  { species: 'Cattle', rate: 1.2 },
                  { species: 'Sheep', rate: 0.5 },
                  { species: 'Goats', rate: 0.4 },
                  { species: 'Pigs', rate: 0.8 }
                ]}
                type="bar"
                dataKeys={['rate']}
                xAxisDataKey="species"
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="animals"
                formatValue={(value) => `${value} kg/day`}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate ? translate('dashboard.analytics.health_metrics', { fallback: 'Health Metrics' }) : 'Health Metrics'}
                subtitle={translate ? translate('dashboard.analytics.health_metrics_desc', { fallback: 'Key health indicators by category' }) : 'Key health indicators by category'}
                data={[
                  { category: 'Vaccination', compliance: 92, issues: 8 },
                  { category: 'Nutrition', compliance: 85, issues: 15 },
                  { category: 'Disease', compliance: 95, issues: 5 },
                  { category: 'Parasites', compliance: 88, issues: 12 }
                ]}
                type="radar"
                dataKeys={['compliance', 'issues']}
                xAxisDataKey="category"
                height={350}
                accentColor={theme.palette.success.main}
                allowChartTypeChange={true}
                module="health"
                formatValue={(value) => `${value}%`}
              />
            </Grid>
          </Grid>
        </Box>
      )
    }
  ];

  // Sample notifications for the notification center
  const sampleNotifications = [
    {
      id: '1',
      title: translate ? translate('notifications.sample.health_alert', { fallback: 'Health Alert' }) : 'Health Alert',
      message: translate ? translate('notifications.sample.health_alert_message', { fallback: 'Animal TAG045 requires vaccination today' }) : 'Animal TAG045 requires vaccination today',
      type: 'warning' as const,
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      read: false,
      category: 'health'
    },
    {
      id: '2',
      title: translate ? translate('notifications.sample.birth_alert', { fallback: 'Birth Alert' }) : 'Birth Alert',
      message: translate ? translate('notifications.sample.birth_alert_message', { fallback: 'Animal TAG018 is due to give birth in 3 days' }) : 'Animal TAG018 is due to give birth in 3 days',
      type: 'info' as const,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      read: false,
      category: 'breeding'
    },
    {
      id: '3',
      title: translate ? translate('notifications.sample.feed_alert', { fallback: 'Feed Alert' }) : 'Feed Alert',
      message: translate ? translate('notifications.sample.feed_alert_message', { fallback: 'Feed inventory is running low (25% remaining)' }) : 'Feed inventory is running low (25% remaining)',
      type: 'error' as const,
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
      read: true,
      category: 'feeding'
    },
    {
      id: '4',
      title: translate ? translate('notifications.sample.financial_alert', { fallback: 'Financial Alert' }) : 'Financial Alert',
      message: translate ? translate('notifications.sample.financial_alert_message', { fallback: 'Monthly financial report is ready for review' }) : 'Monthly financial report is ready for review',
      type: 'success' as const,
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      read: false,
      category: 'financial'
    }
  ];

  return (
    <>
      <StandardDashboard
        title={translate ? translate('dashboard.title', { fallback: 'Dashboard' }) : 'Dashboard'}
        subtitle={translate ? translate('dashboard.subtitle', { fallback: 'Smart Farming, Smarter Decisions - Welcome to AgriIntel Livestock Management System' }) : 'Smart Farming, Smarter Decisions - Welcome to AgriIntel Livestock Management System'}
        icon={<DashboardIcon />}
        stats={dashboardStatsForDisplay}
        actions={dashboardActions}
        isLoading={isLoading}
        loadingMessage={translate ? translate('dashboard.loading', { fallback: 'Loading dashboard data...' }) : 'Loading dashboard data...'}
        onRefresh={handleRefreshData}
        module="dashboard"
        extraHeaderContent={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <NotificationCenter initialNotifications={sampleNotifications} />
          </Box>
        }
      >
        {/* Custom Tab Implementation */}
        <Box sx={{ mb: 4 }}>
          <Paper
            elevation={1}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              mb: 3
            }}
          >
            <Box
              sx={{
                display: 'flex',
                borderBottom: 'none',
                overflowX: 'auto',
                '&::-webkit-scrollbar': {
                  height: 4
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.2),
                  borderRadius: 2
                }
              }}
            >
              {dashboardTabs.map((tab, index) => (
                <Box
                  key={index}
                  onClick={() => {
                    console.log('Custom tab clicked:', index, tab.label);
                    handleTabChange(index);
                  }}
                  sx={{
                    py: 2,
                    px: 3,
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    borderBottom: index === activeTabIndex
                      ? `3px solid ${theme.palette.primary.main}`
                      : '3px solid transparent',
                    color: index === activeTabIndex
                      ? theme.palette.primary.main
                      : theme.palette.text.secondary,
                    fontWeight: index === activeTabIndex ? 600 : 400,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.05)
                    },
                    whiteSpace: 'nowrap'
                  }}
                >
                  {tab.icon && (
                    <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                      {tab.icon}
                    </Box>
                  )}
                  <Typography variant="subtitle2">
                    {tab.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>

          <Box>
            {dashboardTabs[activeTabIndex].content}
          </Box>
        </Box>
      </StandardDashboard>

      {/* Quick Actions */}
      <QuickActions position={{ bottom: 24, right: 24 }} />
    </>
  );
};

export default ModernDashboard;
