import React from 'react';
import { Box, Typography, Tooltip, useTheme, alpha, BoxProps } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import {
  CheckCircleOutlined,
  ErrorOutlined,
  WarningAmberOutlined,
  InfoOutlined,
  HourglassEmptyOutlined,
  BlockOutlined
} from '@mui/icons-material';

export type StatusType = 'success' | 'error' | 'warning' | 'info' | 'pending' | 'inactive';

export interface StatusIndicatorProps extends BoxProps {
  status: StatusType;
  label?: string;
  showIcon?: boolean;
  showLabel?: boolean;
  size?: 'small' | 'medium' | 'large';
  tooltip?: string;
  iconOnly?: boolean;
  variant?: 'default' | 'outlined' | 'filled';
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  showIcon = true,
  showLabel = true,
  size = 'medium',
  tooltip,
  iconOnly = false,
  variant = 'default',
  ...boxProps
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const { translate } = useLanguage();
  const themeColor = availableColors[currentColor];

  // Get status configuration
  const getStatusConfig = () => {
    const configs = {
      success: {
        color: themeColor.primary, // Use theme color instead of success
        icon: <CheckCircleOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.success', { fallback: 'Success' }) : 'Success'
      },
      error: {
        color: theme.palette.error.main,
        icon: <ErrorOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.error', { fallback: 'Error' }) : 'Error'
      },
      warning: {
        color: theme.palette.warning.main,
        icon: <WarningAmberOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.warning', { fallback: 'Warning' }) : 'Warning'
      },
      info: {
        color: theme.palette.info.main,
        icon: <InfoOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.info', { fallback: 'Info' }) : 'Info'
      },
      pending: {
        color: theme.palette.warning.light,
        icon: <HourglassEmptyOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.pending', { fallback: 'Pending' }) : 'Pending'
      },
      inactive: {
        color: theme.palette.text.disabled,
        icon: <BlockOutlined fontSize="inherit" />,
        defaultLabel: translate ? translate('status.inactive', { fallback: 'Inactive' }) : 'Inactive'
      }
    };

    return configs[status];
  };

  const config = getStatusConfig();
  const displayLabel = label || config.defaultLabel;

  // Get size configuration
  const getSizeConfig = () => {
    const sizes = {
      small: {
        iconSize: '14px',
        fontSize: '0.75rem',
        padding: '2px 6px',
        dotSize: '6px',
        gap: '4px'
      },
      medium: {
        iconSize: '16px',
        fontSize: '0.875rem',
        padding: '3px 8px',
        dotSize: '8px',
        gap: '6px'
      },
      large: {
        iconSize: '20px',
        fontSize: '1rem',
        padding: '4px 10px',
        dotSize: '10px',
        gap: '8px'
      }
    };

    return sizes[size];
  };

  const sizeConfig = getSizeConfig();

  // Get variant styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'outlined':
        return {
          backgroundColor: 'transparent',
          border: `1px solid ${config.color}`,
          color: config.color
        };
      case 'filled':
        return {
          backgroundColor: config.color,
          color: theme.palette.getContrastText(config.color)
        };
      case 'default':
      default:
        return {
          backgroundColor: alpha(config.color, 0.1),
          color: config.color
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Render icon-only version
  if (iconOnly) {
    const content = (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: config.color,
          fontSize: sizeConfig.iconSize
        }}
      >
        {config.icon}
      </Box>
    );

    return tooltip ? (
      <Tooltip title={tooltip} arrow>
        {content}
      </Tooltip>
    ) : content;
  }

  // Render full status indicator
  const content = (
    <Box
      display="flex"
      alignItems="center"
      borderRadius="16px"
      {...boxProps}
      sx={{
        ...variantStyles,
        padding: sizeConfig.padding,
        fontSize: sizeConfig.fontSize,
        fontWeight: 500,
        display: 'inline-flex',
        alignItems: 'center',
        gap: sizeConfig.gap,
        ...boxProps.sx
      }}
    >
      {showIcon && (
        <Box sx={{ fontSize: sizeConfig.iconSize, display: 'flex', alignItems: 'center' }}>
          {config.icon}
        </Box>
      )}

      {!showIcon && (
        <Box
          sx={{
            width: sizeConfig.dotSize,
            height: sizeConfig.dotSize,
            borderRadius: '50%',
            backgroundColor: config.color
          }}
        />
      )}

      {showLabel && (
        <Typography
          variant="inherit"
          component="span"
          sx={{
            fontSize: 'inherit',
            fontWeight: 'inherit',
            lineHeight: 1
          }}
        >
          {displayLabel}
        </Typography>
      )}
    </Box>
  );

  return tooltip ? (
    <Tooltip title={tooltip} arrow>
      {content}
    </Tooltip>
  ) : content;
};

export default StatusIndicator;
