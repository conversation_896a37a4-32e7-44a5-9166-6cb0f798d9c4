import { useState, useEffect, useCallback } from 'react';
import { useMockData } from '../utils/config';
import api from '../services/apiService';
import {
  mockFeedingRecords,
  mockFeedInventory,
  mockFeedingPlans,
  mockFeedingStats
} from '../mocks/feedingData';
import { FeedingRecord, FeedInventory, FeedingPlan, FeedingStats } from '../types/feeding';
import { getCollection } from '../utils/mongoDbClient';
import { useMongoDb } from '../contexts/SimpleMongoDbContext';

export const useFeedingData = () => {
  const { isConnected } = useMongoDb();
  const [feedingRecords, setFeedingRecords] = useState<FeedingRecord[]>([]);
  const [feedInventory, setFeedInventory] = useState<FeedInventory[]>([]);
  const [feedingPlans, setFeedingPlans] = useState<FeedingPlan[]>([]);
  const [stats, setStats] = useState<FeedingStats>({
    totalFeedUsedToday: 0,
    totalFeedCostToday: 0,
    feedInventoryValue: 0,
    lowStockItems: 0,
    activeFeedingPlans: 0,
    feedingRecordsThisWeek: 0,
    feedUsageByType: {
      concentrate: 0,
      forage: 0,
      mineral: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Calculate feed usage by type
  const calculateFeedUsageByType = useCallback((type: string): number => {
    return feedingRecords
      .filter(record => feedInventory.find(item => item.id === record.feedId)?.type === type)
      .reduce((sum, record) => sum + record.quantity, 0);
  }, [feedingRecords, feedInventory]);

  // Update statistics
  const updateStats = useCallback(() => {
    const today = new Date();
    const todayRecords = feedingRecords.filter(record => {
      const recordDate = new Date(record.date);
      return recordDate.toDateString() === today.toDateString();
    });

    const newStats: FeedingStats = {
      totalFeedUsedToday: todayRecords.reduce((sum, record) => sum + record.quantity, 0),
      totalFeedCostToday: todayRecords.reduce((sum, record) => sum + (record.quantity * record.costPerUnit), 0),
      feedInventoryValue: feedInventory.reduce((sum, item) => sum + (item.quantity * item.costPerUnit), 0),
      lowStockItems: feedInventory.filter(item => item.quantity < item.reorderLevel).length,
      activeFeedingPlans: feedingPlans.filter(plan => plan.isActive).length,
      feedingRecordsThisWeek: feedingRecords.filter(record => {
        const recordDate = new Date(record.date);
        const diffTime = Math.abs(today.getTime() - recordDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 7;
      }).length,
      feedUsageByType: {
        concentrate: calculateFeedUsageByType('concentrate'),
        forage: calculateFeedUsageByType('forage'),
        mineral: calculateFeedUsageByType('mineral')
      }
    };

    setStats(newStats);
  }, [feedingRecords, feedInventory, feedingPlans, calculateFeedUsageByType]);

  // Fetch all feeding data
  const fetchFeedingData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collections
          const feedingRecordsCollection = await getCollection('feeding_records');
          const feedInventoryCollection = await getCollection('feed_inventory');
          const feedingPlansCollection = await getCollection('feeding_plans');

          // Fetch data
          const recordsData = await feedingRecordsCollection.find({}).toArray();
          const inventoryData = await feedInventoryCollection.find({}).toArray();
          const plansData = await feedingPlansCollection.find({}).toArray();

          // Set state
          setFeedingRecords(recordsData);
          setFeedInventory(inventoryData);
          setFeedingPlans(plansData);

          console.log('Fetched feeding data from MongoDB:', {
            records: recordsData.length,
            inventory: inventoryData.length,
            plans: plansData.length
          });
        } catch (mongoError) {
          console.error('Error fetching from MongoDB, falling back to mock data:', mongoError);
          // Fall back to mock data
          setFeedingRecords(mockFeedingRecords);
          setFeedInventory(mockFeedInventory);
          setFeedingPlans(mockFeedingPlans);
          setStats(mockFeedingStats);
        }
      } else if (useMockData) {
        // Use mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        setFeedingRecords(mockFeedingRecords);
        setFeedInventory(mockFeedInventory);
        setFeedingPlans(mockFeedingPlans);
        setStats(mockFeedingStats);
      } else {
        // Use API
        const [recordsRes, inventoryRes, plansRes] = await Promise.all([
          api.get('/feeding/records'),
          api.get('/feeding/inventory'),
          api.get('/feeding/plans')
        ]);

        setFeedingRecords(recordsRes.data);
        setFeedInventory(inventoryRes.data);
        setFeedingPlans(plansRes.data);
      }

      updateStats();
    } catch (err) {
      console.error('Failed to fetch feeding data:', err);
      setError('Failed to fetch feeding data');
    } finally {
      setLoading(false);
    }
  };

  // CRUD operations for feeding records
  const addFeedingRecord = async (record: Omit<FeedingRecord, 'id'>) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collections
          const feedingRecordsCollection = await getCollection('feeding_records');
          const feedInventoryCollection = await getCollection('feed_inventory');

          // Create new record with generated ID
          const newRecord: FeedingRecord = {
            ...record,
            id: `record-${Date.now()}`,
          };

          // Insert record into MongoDB
          await feedingRecordsCollection.insertOne(newRecord);

          // Update inventory quantity
          const inventoryItem = await feedInventoryCollection.findOne({ id: record.feedId });
          if (inventoryItem) {
            const newQuantity = inventoryItem.quantity - record.quantity;
            let newStatus: FeedInventory['status'] = inventoryItem.status;

            if (newQuantity <= 0) {
              newStatus = 'reorder';
            } else if (newQuantity < inventoryItem.minimumStock) {
              newStatus = 'low';
            } else {
              newStatus = 'available';
            }

            // Update inventory in MongoDB
            await feedInventoryCollection.updateOne(
              { id: record.feedId },
              { $set: { quantity: newQuantity, status: newStatus } }
            );
          }

          // Refresh data
          await fetchFeedingData();
          return newRecord;
        } catch (mongoError) {
          console.error('Error adding feeding record to MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Create new record with generated ID
        const newRecord: FeedingRecord = {
          ...record,
          id: `record-${Date.now()}`,
        };

        // Update state
        setFeedingRecords(prev => [...prev, newRecord]);

        // Update inventory quantity
        const updatedInventory = feedInventory.map(item => {
          if (item.id === record.feedId) {
            const newQuantity = item.quantity - record.quantity;
            let newStatus: FeedInventory['status'] = item.status;

            if (newQuantity <= 0) {
              newStatus = 'reorder';
            } else if (newQuantity < item.minimumStock) {
              newStatus = 'low';
            } else {
              newStatus = 'available';
            }

            return {
              ...item,
              quantity: newQuantity,
              status: newStatus
            };
          }
          return item;
        });

        setFeedInventory(updatedInventory);
        updateStats();

        return newRecord;
      } else {
        // Use real API
        const response = await api.post('/feeding/records', record);
        await fetchFeedingData(); // Refresh all data
        return response.data;
      }
    } catch (err) {
      console.error('Error adding feeding record:', err);
      setError('Failed to add feeding record');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateFeedingRecord = async (id: string, record: Partial<FeedingRecord>) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collections
          const feedingRecordsCollection = await getCollection('feeding_records');
          const feedInventoryCollection = await getCollection('feed_inventory');

          // Find the original record
          const originalRecord = await feedingRecordsCollection.findOne({ id });
          if (!originalRecord) throw new Error('Record not found');

          // Update record in MongoDB
          await feedingRecordsCollection.updateOne(
            { id },
            { $set: record }
          );

          // If feed quantity changed, update inventory
          if (record.quantity !== undefined && record.quantity !== originalRecord.quantity) {
            const quantityDiff = originalRecord.quantity - record.quantity;

            // Get inventory item
            const inventoryItem = await feedInventoryCollection.findOne({ id: originalRecord.feedId });
            if (inventoryItem) {
              const newQuantity = inventoryItem.quantity + quantityDiff;
              let newStatus: FeedInventory['status'] = 'available';

              if (newQuantity <= 0) {
                newStatus = 'reorder';
              } else if (newQuantity < inventoryItem.minimumStock) {
                newStatus = 'low';
              } else {
                newStatus = 'available';
              }

              // Update inventory in MongoDB
              await feedInventoryCollection.updateOne(
                { id: originalRecord.feedId },
                { $set: { quantity: newQuantity, status: newStatus } }
              );
            }
          }

          // Refresh data
          await fetchFeedingData();

          // Return updated record
          const updatedRecord = await feedingRecordsCollection.findOne({ id });
          return updatedRecord;
        } catch (mongoError) {
          console.error('Error updating feeding record in MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the original record
        const originalRecord = feedingRecords.find(r => r.id === id);
        if (!originalRecord) throw new Error('Record not found');

        // Update record
        const updatedRecord: FeedingRecord = { ...originalRecord, ...record };

        // Update state
        setFeedingRecords(prev => prev.map(r => r.id === id ? updatedRecord : r));

        // If feed quantity changed, update inventory
        if (record.quantity !== undefined && record.quantity !== originalRecord.quantity) {
          const quantityDiff = originalRecord.quantity - record.quantity;

          const updatedInventory = feedInventory.map(item => {
            if (item.id === originalRecord.feedId) {
              const newQuantity = item.quantity + quantityDiff;
              let newStatus: FeedInventory['status'] = 'available';

              if (newQuantity <= 0) {
                newStatus = 'reorder';
              } else if (newQuantity < item.minimumStock) {
                newStatus = 'low';
              } else {
                newStatus = 'available';
              }

              return {
                ...item,
                quantity: newQuantity,
                status: newStatus
              };
            }
            return item;
          });

          setFeedInventory(updatedInventory);
        }

        updateStats();
        return updatedRecord;
      } else {
        // Use real API
        const response = await api.put(`/feeding/records/${id}`, record);
        await fetchFeedingData(); // Refresh all data
        return response.data;
      }
    } catch (err) {
      console.error('Error updating feeding record:', err);
      setError('Failed to update feeding record');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteFeedingRecord = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collections
          const feedingRecordsCollection = await getCollection('feeding_records');
          const feedInventoryCollection = await getCollection('feed_inventory');

          // Find the record to delete
          const recordToDelete = await feedingRecordsCollection.findOne({ id });
          if (!recordToDelete) throw new Error('Record not found');

          // Delete record from MongoDB
          await feedingRecordsCollection.deleteOne({ id });

          // Return feed to inventory
          const inventoryItem = await feedInventoryCollection.findOne({ id: recordToDelete.feedId });
          if (inventoryItem) {
            const newQuantity = inventoryItem.quantity + recordToDelete.quantity;
            let newStatus: FeedInventory['status'] = 'available';

            if (newQuantity <= 0) {
              newStatus = 'reorder';
            } else if (newQuantity < inventoryItem.minimumStock) {
              newStatus = 'low';
            } else {
              newStatus = 'available';
            }

            // Update inventory in MongoDB
            await feedInventoryCollection.updateOne(
              { id: recordToDelete.feedId },
              { $set: { quantity: newQuantity, status: newStatus } }
            );
          }

          // Refresh data
          await fetchFeedingData();
          return true;
        } catch (mongoError) {
          console.error('Error deleting feeding record from MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the record to delete
        const recordToDelete = feedingRecords.find(r => r.id === id);
        if (!recordToDelete) throw new Error('Record not found');

        // Update state
        setFeedingRecords(prev => prev.filter(r => r.id !== id));

        // Return feed to inventory
        const updatedInventory = feedInventory.map(item => {
          if (item.id === recordToDelete.feedId) {
            const newQuantity = item.quantity + recordToDelete.quantity;
            let newStatus: FeedInventory['status'] = 'available';

            if (newQuantity <= 0) {
              newStatus = 'reorder';
            } else if (newQuantity < item.minimumStock) {
              newStatus = 'low';
            } else {
              newStatus = 'available';
            }

            return {
              ...item,
              quantity: newQuantity,
              status: newStatus
            };
          }
          return item;
        });

        setFeedInventory(updatedInventory);
        updateStats();

        return true;
      } else {
        // Use real API
        await api.delete(`/feeding/records/${id}`);
        await fetchFeedingData(); // Refresh all data
        return true;
      }
    } catch (err) {
      console.error('Error deleting feeding record:', err);
      setError('Failed to delete feeding record');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // CRUD operations for feed inventory
  const addFeedInventory = async (item: Omit<FeedInventory, 'id'>) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collection
          const feedInventoryCollection = await getCollection('feed_inventory');

          // Create new inventory item with generated ID
          const newItem: FeedInventory = {
            ...item,
            id: `feed-${Date.now()}`,
          };

          // Insert item into MongoDB
          await feedInventoryCollection.insertOne(newItem);

          // Refresh data
          await fetchFeedingData();
          return newItem;
        } catch (mongoError) {
          console.error('Error adding feed inventory to MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Create new inventory item with generated ID
        const newItem: FeedInventory = {
          ...item,
          id: `feed-${Date.now()}`,
        };

        // Update state
        setFeedInventory(prev => [...prev, newItem]);
        updateStats();

        return newItem;
      } else {
        // Use real API
        const response = await api.post('/feeding/inventory', item);
        await fetchFeedingData(); // Refresh all data
        return response.data;
      }
    } catch (err) {
      console.error('Error adding feed inventory:', err);
      setError('Failed to add feed inventory');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateFeedInventory = async (id: string, item: Partial<FeedInventory>) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collection
          const feedInventoryCollection = await getCollection('feed_inventory');

          // Find the original item
          const originalItem = await feedInventoryCollection.findOne({ id });
          if (!originalItem) throw new Error('Inventory item not found');

          // Update item in MongoDB
          await feedInventoryCollection.updateOne(
            { id },
            { $set: item }
          );

          // Refresh data
          await fetchFeedingData();

          // Return updated item
          const updatedItem = await feedInventoryCollection.findOne({ id });
          return updatedItem;
        } catch (mongoError) {
          console.error('Error updating feed inventory in MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Find the original item
        const originalItem = feedInventory.find(i => i.id === id);
        if (!originalItem) throw new Error('Inventory item not found');

        // Update item
        const updatedItem: FeedInventory = { ...originalItem, ...item };

        // Update state
        setFeedInventory(prev => prev.map(i => i.id === id ? updatedItem : i));
        updateStats();

        return updatedItem;
      } else {
        // Use real API
        const response = await api.put(`/feeding/inventory/${id}`, item);
        await fetchFeedingData(); // Refresh all data
        return response.data;
      }
    } catch (err) {
      console.error('Error updating feed inventory:', err);
      setError('Failed to update feed inventory');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deleteFeedInventory = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        // Use MongoDB
        try {
          // Get collections
          const feedInventoryCollection = await getCollection('feed_inventory');
          const feedingRecordsCollection = await getCollection('feeding_records');

          // Check if this feed is used in any feeding records
          const recordsWithFeed = await feedingRecordsCollection.find({ feedId: id }).toArray();
          if (recordsWithFeed.length > 0) {
            throw new Error('Cannot delete feed that is used in feeding records');
          }

          // Delete item from MongoDB
          await feedInventoryCollection.deleteOne({ id });

          // Refresh data
          await fetchFeedingData();
          return true;
        } catch (mongoError) {
          console.error('Error deleting feed inventory from MongoDB:', mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check if there are any feeding records using this feed
        const recordsUsingFeed = feedingRecords.some(record => record.feedId === id);
        if (recordsUsingFeed) {
          throw new Error('Cannot delete feed that is used in feeding records');
        }

        // Update state
        setFeedInventory(prev => prev.filter(i => i.id !== id));
        updateStats();

        return true;
      } else {
        // Use real API
        await api.delete(`/feeding/inventory/${id}`);
        await fetchFeedingData(); // Refresh all data
        return true;
      }
    } catch (err) {
      console.error('Error deleting feed inventory:', err);
      setError('Failed to delete feed inventory');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Generate Excel report
  const generateExcelReport = async (reportType: 'records' | 'inventory' | 'plans') => {
    try {
      setLoading(true);
      setError(null);

      if (isConnected && !useMockData) {
        try {
          // Get data from MongoDB
          let data: any[] = [];

          if (reportType === 'records') {
            const feedingRecordsCollection = await getCollection('feeding_records');
            data = await feedingRecordsCollection.find({}).toArray();
          } else if (reportType === 'inventory') {
            const feedInventoryCollection = await getCollection('feed_inventory');
            data = await feedInventoryCollection.find({}).toArray();
          } else if (reportType === 'plans') {
            const feedingPlansCollection = await getCollection('feeding_plans');
            data = await feedingPlansCollection.find({}).toArray();
          }

          // In a real implementation, this would generate an Excel file
          // For now, we'll just return a filename
          console.log(`Generated ${reportType} report with ${data.length} records`);
          return `${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx`;
        } catch (mongoError) {
          console.error(`Error generating ${reportType} report from MongoDB:`, mongoError);
          throw mongoError;
        }
      } else if (useMockData) {
        // Simulate report generation
        await new Promise(resolve => setTimeout(resolve, 1500));
        return `${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx`;
      } else {
        // Use real API
        const response = await api.get(`/feeding/reports/${reportType}`);
        return response.data.reportUrl;
      }
    } catch (err) {
      console.error(`Error generating ${reportType} report:`, err);
      setError(`Failed to generate ${reportType} report`);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchFeedingData();
  }, []);

  return {
    feedingRecords,
    feedInventory,
    feedingPlans,
    stats,
    loading,
    error,
    fetchFeedingData,
    addFeedingRecord,
    updateFeedingRecord,
    deleteFeedingRecord,
    addFeedInventory,
    updateFeedInventory,
    deleteFeedInventory,
    generateExcelReport
  };
};
