import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, Box, Chip, IconButton, Avatar, useTheme, alpha, List, ListItem, ListItemText, ListItemAvatar, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, Snackbar, Menu, MenuItem,  } from '@mui/material';
import { getCardStyle, getTableHeaderStyle, getTableRowStyle, getChipStyle } from '../../utils/cardStyles';
import {
  AttachMoney,
  Add,
  Restaurant,
  Warning,
  Search,
  FilterList,
  MoreVert,
  ArrowForward,
  Schedule,
  Edit,
  Delete,
  GetApp,
 } from '../../utils/iconImports';
import { ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend } from 'recharts';
import {  AnimatedBackgroundCard, ModuleContainer, ModuleHeaderCard, withSubModuleTranslation, StandardDashboard, ModernChart , CustomButton } from '../../components/common';
import AnimatedChart from '../../components/common/AnimatedChart';
import { useFeedingData } from '../../hooks/useFeedingData';
import { FeedingRecord, FeedInventory } from '../../types/feeding';
import FeedingRecordModal from '../../components/feeding/FeedingRecordModal';
import FeedInventoryModal from '../../components/feeding/FeedInventoryModal';

interface FeedingDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const FeedingDashboard: React.FC<FeedingDashboardProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const {
    feedingRecords,
    feedInventory,
    stats,
    loading,
    error,
    addFeedingRecord,
    updateFeedingRecord,
    deleteFeedingRecord,
    addFeedInventory,
    updateFeedInventory,
    deleteFeedInventory,
    generateExcelReport
  } = useFeedingData();

  // State for modals
  const [recordModalOpen, setRecordModalOpen] = useState(false);
  const [inventoryModalOpen, setInventoryModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<FeedingRecord | null>(null);
  const [selectedInventory, setSelectedInventory] = useState<FeedInventory | null>(null);

  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItemId, setSelectedItemId] = useState<string>('');
  const [menuType, setMenuType] = useState<'record' | 'inventory'>('record');

  // State for alerts
  const [alert, setAlert] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info' | 'warning'}>({open: false, message: '', severity: 'info'});

  // State for loading
  const [actionLoading, setActionLoading] = useState(false);


  // State for chart interactions
  const [selectedFeedType, setSelectedFeedType] = useState<string | null>(null);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });
  const [monthlyFeedUsageData, setMonthlyFeedUsageData] = useState<any[]>([]);

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // Handler functions
  function handleMenuOpen(event: React.MouseEvent<HTMLElement>, id: string, type: 'record' | 'inventory') {
    setMenuAnchorEl(event.currentTarget);
    setSelectedItemId(id);
    setMenuType(type);
  }

  function handleMenuClose() {
    setMenuAnchorEl(null);
  }

  function handleEdit() {
    handleMenuClose();
    if (menuType === 'record') {
      const record = feedingRecords.find(r => r.id === selectedItemId);
      if (record) {
        setSelectedRecord(record);
        setRecordModalOpen(true);
      }
    } else {
      const item = feedInventory.find(i => i.id === selectedItemId);
      if (item) {
        setSelectedInventory(item);
        setInventoryModalOpen(true);
      }
    }
  }

  function handleDelete() {
    handleMenuClose();
    if (menuType === 'record') {
      handleDeleteRecord(selectedItemId);
    } else {
      handleDeleteInventory(selectedItemId);
    }
  }

  async function handleSaveRecord(record: Omit<FeedingRecord, 'id'>) {
    try {
      setActionLoading(true);
      await addFeedingRecord(record);
      setAlert({
        open: true,
        message: 'Feeding record added successfully',
        severity: 'success'
      });
      setRecordModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error adding feeding record',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleUpdateRecord(id: string, record: Partial<FeedingRecord>) {
    try {
      setActionLoading(true);
      await updateFeedingRecord(id, record);
      setAlert({
        open: true,
        message: 'Feeding record updated successfully',
        severity: 'success'
      });
      setRecordModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error updating feeding record',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleDeleteRecord(id: string) {
    try {
      setActionLoading(true);
      await deleteFeedingRecord(id);
      setAlert({
        open: true,
        message: 'Feeding record deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error deleting feeding record',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleSaveInventory(item: Omit<FeedInventory, 'id'>) {
    try {
      setActionLoading(true);
      await addFeedInventory(item);
      setAlert({
        open: true,
        message: 'Feed inventory added successfully',
        severity: 'success'
      });
      setInventoryModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error adding feed inventory',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleUpdateInventory(id: string, item: Partial<FeedInventory>) {
    try {
      setActionLoading(true);
      await updateFeedInventory(id, item);
      setAlert({
        open: true,
        message: 'Feed inventory updated successfully',
        severity: 'success'
      });
      setInventoryModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error updating feed inventory',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleDeleteInventory(id: string) {
    try {
      setActionLoading(true);
      await deleteFeedInventory(id);
      setAlert({
        open: true,
        message: 'Feed inventory deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error deleting feed inventory',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  async function handleGenerateReport(reportType: 'records' | 'inventory' | 'plans') {
    try {
      setActionLoading(true);
      const reportUrl = await generateExcelReport(reportType);
      setAlert({
        open: true,
        message: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully`,
        severity: 'success'
      });
      console.log(`Report URL: ${reportUrl}`);
    } catch (error) {
      setAlert({
        open: true,
        message: `Error generating ${reportType} report`,
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  }

  useEffect(() => {
    // Generate monthly feed usage data when feedingRecords are loaded
    if (feedingRecords.length > 0) {
      // This is a simplified example - in a real app, you would aggregate the actual data
      const mockMonthlyData = [
        { month: 'Jan', concentrate: 320, forage: 240, supplement: 100 },
        { month: 'Feb', concentrate: 300, forage: 230, supplement: 110 },
        { month: 'Mar', concentrate: 340, forage: 260, supplement: 120 },
        { month: 'Apr', concentrate: 360, forage: 270, supplement: 130 },
        { month: 'May', concentrate: 380, forage: 290, supplement: 140 },
        { month: 'Jun', concentrate: 400, forage: 300, supplement: 150 },
      ];
      setMonthlyFeedUsageData(mockMonthlyData);
    }
  }, [feedingRecords]);

  // Prepare data for feed type chart
  const feedTypeData = stats ? Object.entries(stats.feedUsageByType).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  })) : [];

  // Get low stock items for display
  const lowStockItems = feedInventory
    ? feedInventory
        .filter(item => item.status === 'low' || item.status === 'reorder')
        .slice(0, 5)
    : [];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">Error loading feeding data. Please try again later.</Alert>
      </Box>
    );
  }

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translateModuleField ? translateModuleField('feed_used_today', "Feed Used Today") : "Feed Used Today",
      value: `${stats?.totalFeedUsedToday || 0} kg`,
      icon: <Restaurant />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('inventory_value', "Inventory Value") : "Inventory Value",
      value: `R${stats?.feedInventoryValue.toLocaleString() || '0'}`,
      icon: <AttachMoney />,
      color: theme.palette.success.main,
      trend: {
        value: 12,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('low_stock_items', "Low Stock Items") : "Low Stock Items",
      value: stats?.lowStockItems || 0,
      icon: <Warning />,
      color: theme.palette.warning.main,
      trend: {
        value: 2,
        isPositive: false,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('active_feeding_plans', "Active Feeding Plans") : "Active Feeding Plans",
      value: stats?.activeFeedingPlans || 0,
      icon: <Schedule />,
      color: theme.palette.info.main,
      trend: {
        value: 1,
        isPositive: true,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translateModuleField ? translateModuleField('add_feeding_record', "Add Feeding Record") : "Add Feeding Record",
      icon: <Add />,
      onClick: () => {
        setSelectedRecord(null);
        setRecordModalOpen(true);
      },
      color: 'primary'
    }
  ];

  return (
    <StandardDashboard
      title={translateSubModule ? translateSubModule('title', "Feed Management Dashboard") : "Feed Management Dashboard"}
      subtitle={translateSubModule ? translateSubModule('subtitle', "Monitor AgriIntel feed inventory, track consumption, and manage feeding plans efficiently") : "Monitor AgriIntel feed inventory, track consumption, and manage feeding plans efficiently"}
      icon={<Restaurant />}
      stats={dashboardStats}
      actions={dashboardActions}
      isLoading={loading || actionLoading}
      loadingMessage={translateModuleField ? translateModuleField('loading', "Loading AgriIntel feeding data...") : "Loading AgriIntel feeding data..."}
      onRefresh={() => console.log('Refreshing feeding data')}
      module="feed"
      backgroundImage="/images/modules/feeding/feed-main.jpeg"
      backgroundPosition="center"
    >
      <Box sx={{ px: 3, pb: 5 }}>

      {/* Stats Cards */}
      <Card sx={{ ...getCardStyle('feed', theme), mb: 3, mt: 4 }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="bold" color="white" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } }}>
            {translateModuleField ? translateModuleField('feed_statistics', "Feed Statistics") : "Feed Statistics"}
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <AnimatedBackgroundCard
            title={translateModuleField ? translateModuleField('feed_used_today', "Feed Used Today") : "Feed Used Today"}
            module="feed"
            backgroundImage="/images/modules/feeding/feed-main.jpeg" /* Feed managemnt.jpeg */
            icon={<Restaurant />}
            accentColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            delay={0.1}
            height="100%"
            overlay="gradient"
          >
            <Box mt={3}>
              <Typography variant="h3" fontWeight="bold" sx={{ fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' }, letterSpacing: '-0.5px' }}>
                {`${stats?.totalFeedUsedToday || 0} kg`}
              </Typography>
              <Box
                sx={{
                  display: 'inline-block',
                  px: 1.5,
                  py: 0.7,
                  borderRadius: 1.5,
                  mt: 2,
                  bgcolor: 'success.100',
                  color: 'success.800',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Typography variant="body2" fontWeight="medium" sx={{ fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' } }}>
                  Daily Target Met
                </Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <AnimatedBackgroundCard
            title={translateModuleField ? translateModuleField('inventory_value', "Inventory Value") : "Inventory Value"}
            module="feed"
            backgroundImage="/images/modules/feeding/feed-2.jpeg" /* feed management 2.jpeg */
            icon={<AttachMoney />}
            accentColor={theme.palette.success.main}
            secondaryColor={theme.palette.success.dark}
            delay={0.2}
            height="100%"
            overlay="gradient"
          >
            <Box mt={3}>
              <Typography variant="h3" fontWeight="bold" sx={{ fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' }, letterSpacing: '-0.5px' }}>
                {`R${stats?.feedInventoryValue.toLocaleString() || '0'}`}
              </Typography>
              <Box
                sx={{
                  display: 'inline-block',
                  px: 1.5,
                  py: 0.7,
                  borderRadius: 1.5,
                  mt: 2,
                  bgcolor: 'success.100',
                  color: 'success.800',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Typography variant="body2" fontWeight="medium" sx={{ fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' } }}>
                  +12% vs last month
                </Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <AnimatedBackgroundCard
            title={translateModuleField ? translateModuleField('low_stock_items', "Low Stock Items") : "Low Stock Items"}
            module="feed"
            backgroundImage="/images/modules/feeding/feed-sheep.jpeg" /* feed managemnt sheep.jpeg */
            icon={<Warning />}
            accentColor={theme.palette.warning.main}
            secondaryColor={theme.palette.warning.dark}
            delay={0.3}
            height="100%"
            overlay="gradient"
            onAction={() => navigate('/feeding/inventory')}
          >
            <Box mt={3}>
              <Typography variant="h3" fontWeight="bold" sx={{ fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' }, letterSpacing: '-0.5px' }}>
                {stats?.lowStockItems || 0}
              </Typography>
              <Box
                sx={{
                  display: 'inline-block',
                  px: 1.5,
                  py: 0.7,
                  borderRadius: 1.5,
                  mt: 2,
                  bgcolor: 'warning.100',
                  color: 'warning.800',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Typography variant="body2" fontWeight="medium" sx={{ fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' } }}>
                  Needs attention
                </Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <AnimatedBackgroundCard
            title={translateModuleField ? translateModuleField('active_feeding_plans', "Active Feeding Plans") : "Active Feeding Plans"}
            module="feed"
            backgroundImage="/images/modules/feeding/feed-main.jpeg" /* Feed managemnt.jpeg */
            icon={<Schedule />}
            accentColor={theme.palette.info.main}
            secondaryColor={theme.palette.info.dark}
            delay={0.4}
            height="100%"
            overlay="gradient"
            onAction={() => navigate('/feeding/plans')}
          >
            <Box mt={3}>
              <Typography variant="h3" fontWeight="bold" sx={{ fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' }, letterSpacing: '-0.5px' }}>
                {stats?.activeFeedingPlans || 0}
              </Typography>
              <Box
                sx={{
                  display: 'inline-block',
                  px: 1.5,
                  py: 0.7,
                  borderRadius: 1.5,
                  mt: 2,
                  bgcolor: 'info.100',
                  color: 'info.800',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <Typography variant="body2" fontWeight="medium" sx={{ fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' } }}>
                  View Plans
                </Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>
      </Grid>

      {/* Charts and Lists Section */}
      <Card sx={{ ...getCardStyle('feed', theme), mb: 3, mt: 5 }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="bold" color="white" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } }}>
            Feed Analysis
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <ModernChart
            title={translateModuleField ? translateModuleField('feed_usage_by_type', "Feed Usage by Type") : "Feed Usage by Type"}
            subtitle={translateModuleField ? translateModuleField('feed_usage_by_type_desc', "Distribution of feed usage by type") : "Distribution of feed usage by type"}
            data={feedTypeData}
            type="pie"
            dataKeys={['value']}
            height={350}
            accentColor={theme.palette.primary.main}
            allowChartTypeChange={true}
            module="feed"
            tooltip={translateModuleField ? translateModuleField('feed_usage_by_type_help', "Shows the distribution of different types of feed used") : "Shows the distribution of different types of feed used"}
            formatValue={(value) => `${value} kg`}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <AnimatedBackgroundCard
            title="Low Stock Alerts"
            module="feed"
            backgroundImage="/images/modules/feeding/feed-sheep.jpeg" /* feed managemnt sheep.jpeg */
            icon={<Warning />}
            accentColor={theme.palette.warning.main}
            secondaryColor={theme.palette.warning.dark}
            delay={0.2}
            height="100%"
            overlay="gradient"
          >
            <List sx={{ '& .MuiListItem-root': { py: { xs: 2, md: 2.5 } } }}>
              {lowStockItems.length > 0 ? (
                lowStockItems.map((item) => (
                  <ListItem key={item.id} divider sx={{ borderColor: 'rgba(255,255,255,0.1)' }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)' }}>
                        <Warning sx={{ color: 'white' }} />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={`${item.name} (${item.quantity} ${item.unit})`}
                      secondary={`Status: ${item.status} - Location: ${item.location}`}
                      primaryTypographyProps={{
                        sx: { fontSize: { xs: '1rem', sm: '1.1rem', md: '1.15rem' }, fontWeight: 500, color: 'white' }
                      }}
                      secondaryTypographyProps={{
                        sx: { fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' }, color: 'rgba(255,255,255,0.7)' }
                      }}
                    />
                    <Chip
                      label={item.status.toUpperCase()}
                      size="small"
                      sx={{
                        mr: 1,
                        fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                        ...getChipStyle('feed', theme, item.status === 'reorder' ? 'error' : 'warning')
                      }}
                    />
                    <IconButton size="small" onClick={() => navigate(`/feeding/inventory/${item.id}`)} sx={{ color: 'white' }}>
                      <ArrowForward fontSize="small" />
                    </IconButton>
                  </ListItem>
                ))
              ) : (
                <ListItem>
                  <ListItemText
                    primary="No low stock items"
                    primaryTypographyProps={{
                      sx: { fontSize: { xs: '1rem', sm: '1.1rem', md: '1.15rem' }, color: 'white' }
                    }}
                  />
                </ListItem>
              )}
            </List>
          </AnimatedBackgroundCard>
        </Grid>
      </Grid>

      {/* Monthly Feed Usage Chart */}
      <Card sx={{ ...getCardStyle('feed', theme), mb: 3, mt: 5 }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="bold" color="white" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } }}>
            Monthly Feed Usage
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12}>
          <ModernChart
            title={translateModuleField ? translateModuleField('monthly_feed_usage', "Monthly Feed Usage") : "Monthly Feed Usage"}
            subtitle={translateModuleField ? translateModuleField('monthly_feed_usage_desc', "Track feed consumption over time") : "Track feed consumption over time"}
            data={monthlyFeedUsageData}
            type="bar"
            dataKeys={['concentrate', 'forage', 'supplement']}
            xAxisDataKey="month"
            height={400}
            accentColor={theme.palette.primary.main}
            allowChartTypeChange={true}
            allowTimeRangeChange={true}
            module="feed"
            tooltip={translateModuleField ? translateModuleField('monthly_feed_usage_help', "Shows feed usage by month and type") : "Shows feed usage by month and type"}
            formatValue={(value) => `${value} kg`}
            onRefresh={() => {
              // Refresh data logic would go here
              console.log('Refreshing monthly feed usage data');
            }}
            onClick={(data) => {
              if (data && data.activePayload && data.activePayload.length > 0) {
                const payload = data.activePayload[0].payload;
                setSelectedChart('monthlyUsage');
                setChartTooltip({
                  show: true,
                  content: `${payload.month}: Concentrate: ${payload.concentrate}kg, Forage: ${payload.forage}kg, Supplement: ${payload.supplement}kg`
                });
              }
            }}
          />
        </Grid>
      </Grid>

      {/* Chart Interaction Feedback */}
      {chartTooltip.show && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ ...getCardStyle('feed', theme), mb: 4 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" fontWeight="bold" color="white">
                  {selectedChart === 'feedType' ? 'Feed Type Details' : 'Feed Usage Details'}
                </Typography>
                <IconButton size="small" onClick={() => setChartTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>
                  <MoreVert />
                </IconButton>
              </Box>
              <Typography variant="body1" mt={1} color="white">
                {chartTooltip.content}
              </Typography>
              {selectedFeedType && (
                <Box mt={2}>
                  <CustomButton
                    variant="outlined"
                    size="small"
                    onClick={() => navigate(`/feeding/inventory?type=${selectedFeedType}`)}
                    sx={{ color: 'white', borderColor: 'white' }}
                  >
                    View {selectedFeedType} Inventory
                  </CustomButton>
                  <Typography variant="body2" color="white" mt={1} sx={{ opacity: 0.8 }}>
                    {selectedChart === 'feedType' && 'Analyze feed usage patterns to optimize nutrition and reduce waste.'}
                    {selectedChart === 'monthlyUsage' && 'Monthly trends help forecast future feed requirements and budget planning.'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Recent Feeding Records */}
      <Card sx={{ ...getCardStyle('feed', theme), mb: 3, mt: 5 }}>
        <CardContent sx={{ py: 2 }}>
          <Typography variant="h4" fontWeight="bold" color="white" sx={{ fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' } }}>
            Recent Feeding Records
          </Typography>
        </CardContent>
      </Card>

      <Grid container spacing={3} mb={4}>
        <Grid item xs={12}>
          <AnimatedBackgroundCard
            title="Recent Feeding Records"
            module="feed"
            backgroundImage="/images/modules/feeding/feed-2.jpeg" /* feed management 2.jpeg */
            icon={<Restaurant />}
            accentColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            delay={0.4}
            height="auto"
            overlay="gradient"
            action={(
              <Box>
                <IconButton size="small" sx={{ color: 'white' }} onClick={() => handleGenerateReport('records')}>
                  <GetApp />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Search />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <FilterList />
                </IconButton>
                <CustomButton
                  variant="outlined"
                  size="small"
                  startIcon={<Add />}
                  onClick={() => {
                    setSelectedRecord(null);
                    setRecordModalOpen(true);
                  }}
                  sx={{
                    ml: 1,
                    fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' },
                    padding: { xs: '6px 12px', sm: '6px 16px', md: '8px 20px' },
                    color: 'white',
                    borderColor: 'white'
                  }}
                >
                  Add Record
                </CustomButton>
              </Box>
            )}
          >
            <TableContainer component={Paper} sx={{ boxShadow: 'none', mt: 3, borderRadius: 2, overflow: 'hidden', bgcolor: 'transparent' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead sx={{ ...getTableHeaderStyle('feed', theme) }}>
                  <TableRow>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>ID</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Date</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Feed Type</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Quantity</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Location</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Animals</TableCell>
                    <TableCell sx={{ py: 2, fontSize: '1rem', fontWeight: 700, color: 'white' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedingRecords.slice(0, 5).map((record) => (
                    <TableRow
                      key={record.id}
                      hover
                      sx={{ ...getTableRowStyle('feed', theme) }}
                    >
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', color: 'white' }}>{record.id}</TableCell>
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', color: 'white' }}>{new Date(record.date).toLocaleDateString()}</TableCell>
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', color: 'white' }}>
                        <Chip
                          label={record.feedType || 'Unknown'}
                          size="small"
                          sx={{
                            ...getChipStyle('feed', theme, 'primary'),
                            fontWeight: 500
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', fontWeight: 500, color: 'white' }}>{record.quantity} {record.unit || 'units'}</TableCell>
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', color: 'white' }}>{record.location || 'Unknown'}</TableCell>
                      <TableCell sx={{ py: 2.5, fontSize: '0.95rem', color: 'white' }}>{record.animalGroupId ? 1 : 0} groups</TableCell>
                      <TableCell sx={{ py: 2.5 }}>
                        <IconButton
                          size="medium"
                          onClick={(e) => handleMenuOpen(e, record.id, 'record')}
                          sx={{ color: 'white' }}
                        >
                          <MoreVert />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </AnimatedBackgroundCard>
        </Grid>
      </Grid>
      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            elevation: 3,
            sx: {
              minWidth: 180,
              borderRadius: 2,
              mt: 1,
              boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
              '& .MuiMenuItem-root': {
                py: 1.5,
                px: 2,
                fontSize: '0.95rem',
                borderRadius: 1,
                mx: 0.5,
                my: 0.25,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                }
              }
            }
          }
        }}
      >
        <MenuItem onClick={handleEdit}>
          <Edit sx={{ mr: 1.5, color: theme.palette.primary.main }} /> Edit
        </MenuItem>
        <MenuItem onClick={handleDelete}>
          <Delete sx={{ mr: 1.5, color: theme.palette.error.main }} /> Delete
        </MenuItem>
      </Menu>

      {/* Modals */}
      <Box>
        <FeedingRecordModal
          open={recordModalOpen}
          onClose={() => setRecordModalOpen(false)}
          record={selectedRecord}
          feedInventory={feedInventory}
          onSave={handleSaveRecord}
          onUpdate={handleUpdateRecord}
          onDelete={handleDeleteRecord}
          loading={actionLoading}
        />

        <FeedInventoryModal
          open={inventoryModalOpen}
          onClose={() => setInventoryModalOpen(false)}
          feedItem={selectedInventory}
          onSave={handleSaveInventory}
          onUpdate={handleUpdateInventory}
          onDelete={handleDeleteInventory}
          loading={actionLoading}
        />
      </Box>

      {/* Alert Snackbar */}
      <Snackbar
        open={alert.open}
        autoHideDuration={6000}
        onClose={() => setAlert({ ...alert, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setAlert({ ...alert, open: false })} severity={alert.severity} sx={{ width: '100%' }}>
          {alert.message}
        </Alert>
      </Snackbar>
      </Box>
    </StandardDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(FeedingDashboard, 'feeding', 'dashboard');
