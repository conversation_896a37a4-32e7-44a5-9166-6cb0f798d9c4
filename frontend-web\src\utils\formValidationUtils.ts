/**
 * Comprehensive form validation and submission utilities
 * Ensures all forms have proper validation, error handling, and data persistence
 */

import { useState, useCallback } from 'react';
import { useSnackbar } from '../hooks/useSnackbar';

// Form validation rules
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationRules {
  [key: string]: ValidationRule;
}

// Form state interface
export interface FormState {
  values: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Validation functions
export const validateField = (value: any, rule: ValidationRule): string | null => {
  // Required validation
  if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return 'This field is required';
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    if (rule.minLength && value.length < rule.minLength) {
      return `Minimum length is ${rule.minLength} characters`;
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return `Maximum length is ${rule.maxLength} characters`;
    }

    if (rule.pattern && !rule.pattern.test(value)) {
      return 'Invalid format';
    }
  }

  // Custom validation
  if (rule.custom) {
    return rule.custom(value);
  }

  return null;
};

export const validateForm = (values: Record<string, any>, rules: ValidationRules): Record<string, string> => {
  const errors: Record<string, string> = {};

  Object.keys(rules).forEach(fieldName => {
    const value = values[fieldName];
    const rule = rules[fieldName];
    const error = validateField(value, rule);
    
    if (error) {
      errors[fieldName] = error;
    }
  });

  return errors;
};

// Common validation rules
export const commonValidationRules = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: string) => {
      if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
        return 'Please enter a valid email address';
      }
      return null;
    }
  },
  password: {
    required: true,
    minLength: 6,
    custom: (value: string) => {
      if (value && value.length < 6) {
        return 'Password must be at least 6 characters long';
      }
      return null;
    }
  },
  username: {
    required: true,
    minLength: 3,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_]+$/,
    custom: (value: string) => {
      if (value && !/^[a-zA-Z0-9_]+$/.test(value)) {
        return 'Username can only contain letters, numbers, and underscores';
      }
      return null;
    }
  },
  name: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-zA-Z\s]+$/,
    custom: (value: string) => {
      if (value && !/^[a-zA-Z\s]+$/.test(value)) {
        return 'Name can only contain letters and spaces';
      }
      return null;
    }
  },
  tagNumber: {
    required: true,
    pattern: /^[A-Z0-9-]+$/,
    custom: (value: string) => {
      if (value && !/^[A-Z0-9-]+$/.test(value)) {
        return 'Tag number can only contain uppercase letters, numbers, and hyphens';
      }
      return null;
    }
  },
  weight: {
    required: true,
    custom: (value: any) => {
      const num = parseFloat(value);
      if (isNaN(num) || num <= 0) {
        return 'Weight must be a positive number';
      }
      return null;
    }
  },
  date: {
    required: true,
    custom: (value: any) => {
      if (value && isNaN(Date.parse(value))) {
        return 'Please enter a valid date';
      }
      return null;
    }
  }
};

// Form hook for managing form state and validation
export const useFormValidation = (
  initialValues: Record<string, any>,
  validationRules: ValidationRules,
  onSubmit: (values: Record<string, any>) => Promise<void>
) => {
  const [formState, setFormState] = useState<FormState>({
    values: initialValues,
    errors: {},
    touched: {},
    isSubmitting: false,
    isValid: true
  });

  const { showSnackbar } = useSnackbar();

  const setValue = useCallback((name: string, value: any) => {
    setFormState(prev => ({
      ...prev,
      values: { ...prev.values, [name]: value },
      touched: { ...prev.touched, [name]: true }
    }));
  }, []);

  const setError = useCallback((name: string, error: string) => {
    setFormState(prev => ({
      ...prev,
      errors: { ...prev.errors, [name]: error }
    }));
  }, []);

  const clearError = useCallback((name: string) => {
    setFormState(prev => {
      const newErrors = { ...prev.errors };
      delete newErrors[name];
      return {
        ...prev,
        errors: newErrors
      };
    });
  }, []);

  const validateSingleField = useCallback((name: string, value: any) => {
    const rule = validationRules[name];
    if (!rule) return;

    const error = validateField(value, rule);
    if (error) {
      setError(name, error);
    } else {
      clearError(name);
    }
  }, [validationRules, setError, clearError]);

  const validateAllFields = useCallback(() => {
    const errors = validateForm(formState.values, validationRules);
    setFormState(prev => ({
      ...prev,
      errors,
      isValid: Object.keys(errors).length === 0
    }));
    return Object.keys(errors).length === 0;
  }, [formState.values, validationRules]);

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }

    const isValid = validateAllFields();
    if (!isValid) {
      showSnackbar('Please fix the errors before submitting', 'error');
      return;
    }

    setFormState(prev => ({ ...prev, isSubmitting: true }));

    try {
      await onSubmit(formState.values);
      showSnackbar('Form submitted successfully', 'success');
    } catch (error) {
      console.error('Form submission error:', error);
      showSnackbar(
        error instanceof Error ? error.message : 'An error occurred while submitting the form',
        'error'
      );
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }));
    }
  }, [formState.values, validateAllFields, onSubmit, showSnackbar]);

  const reset = useCallback(() => {
    setFormState({
      values: initialValues,
      errors: {},
      touched: {},
      isSubmitting: false,
      isValid: true
    });
  }, [initialValues]);

  return {
    values: formState.values,
    errors: formState.errors,
    touched: formState.touched,
    isSubmitting: formState.isSubmitting,
    isValid: formState.isValid,
    setValue,
    setError,
    clearError,
    validateSingleField,
    validateAllFields,
    handleSubmit,
    reset
  };
};

// Utility to get field props for form inputs
export const getFieldProps = (
  name: string,
  formState: any,
  setValue: (name: string, value: any) => void,
  validateSingleField: (name: string, value: any) => void
) => ({
  name,
  value: formState.values[name] || '',
  error: !!formState.errors[name],
  helperText: formState.errors[name],
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setValue(name, value);
    if (formState.touched[name]) {
      validateSingleField(name, value);
    }
  },
  onBlur: () => {
    validateSingleField(name, formState.values[name]);
  }
});
