import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Grid, Typography, Card, CardContent, TextField, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, useTheme, alpha, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, FormControl, InputLabel, Select, MenuItem, InputAdornment, Tooltip, Alert, Snackbar, Divider } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { motion } from 'framer-motion';
import {
  Add,
  Edit,
  Delete,
  Search,
  FilterList,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  MonetizationOn,
  Close,
  Refresh,
  Save,
  Cancel
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { useCommercialData } from '../../hooks/useCommercialData';
import { PriceUpdate } from '../../types/commercial';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip as RechartsTooltip, 
  Legend as RechartsLegend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';

// Helper function to format currency in ZAR
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-ZA', {
    style: 'currency',
    currency: 'ZAR',
    minimumFractionDigits: 2
  }).format(amount);
};

// Helper function to calculate price change percentage
const calculatePriceChange = (oldPrice: number, newPrice: number) => {
  return ((newPrice - oldPrice) / oldPrice) * 100;
};

const Pricing: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { priceUpdates, loading, error } = useCommercialData();

  // State for price update dialog
  const [openDialog, setOpenDialog] = useState(false);
  const [currentPriceUpdate, setCurrentPriceUpdate] = useState<Partial<PriceUpdate>>({});
  const [isNewUpdate, setIsNewUpdate] = useState(true);

  // State for filtering and pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' as 'success' | 'error' });

  // Derived state for filtered price updates
  const filteredPriceUpdates = priceUpdates
    .filter(update => {
      const matchesSearch = 
        update.productType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        update.reason.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = filterCategory === 'all' || 
        (filterCategory === 'increase' && update.newPrice > update.oldPrice) ||
        (filterCategory === 'decrease' && update.newPrice < update.oldPrice) ||
        (filterCategory === 'livestock' && 
          (update.productType.includes('Cattle') || 
           update.productType.includes('Sheep') || 
           update.productType.includes('Goats'))) ||
        (filterCategory === 'feed' && 
          (update.productType.includes('Feed') || 
           update.productType.includes('Lucerne') || 
           update.productType.includes('Supplements')));
      
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => new Date(b.effectiveDate).getTime() - new Date(a.effectiveDate).getTime());

  // Prepare data for charts
  const priceChangeByCategory = React.useMemo(() => {
    const categories = new Map<string, { count: number, totalChange: number }>();
    
    priceUpdates.forEach(update => {
      // Extract main category (e.g., "Nguni Cattle" -> "Cattle")
      let category = update.productType;
      if (category.includes('Cattle')) category = 'Cattle';
      else if (category.includes('Sheep')) category = 'Sheep';
      else if (category.includes('Goats')) category = 'Goats';
      else if (category.includes('Feed') || category.includes('Lucerne')) category = 'Feed';
      else if (category.includes('Chemicals') || category.includes('Supplements')) category = 'Supplies';
      
      const change = calculatePriceChange(update.oldPrice, update.newPrice);
      
      if (categories.has(category)) {
        const current = categories.get(category)!;
        categories.set(category, {
          count: current.count + 1,
          totalChange: current.totalChange + change
        });
      } else {
        categories.set(category, { count: 1, totalChange: change });
      }
    });
    
    return Array.from(categories.entries()).map(([category, data]) => ({
      category,
      averageChange: data.totalChange / data.count
    }));
  }, [priceUpdates]);

  // Prepare data for price trend chart
  const priceTrendData = React.useMemo(() => {
    // Group by month and calculate average changes
    const monthlyData = new Map<string, { totalOldPrice: number, totalNewPrice: number, count: number }>();
    
    priceUpdates.forEach(update => {
      const month = new Date(update.effectiveDate).toLocaleDateString('en-ZA', { month: 'short', year: 'numeric' });
      
      if (monthlyData.has(month)) {
        const current = monthlyData.get(month)!;
        monthlyData.set(month, {
          totalOldPrice: current.totalOldPrice + update.oldPrice,
          totalNewPrice: current.totalNewPrice + update.newPrice,
          count: current.count + 1
        });
      } else {
        monthlyData.set(month, {
          totalOldPrice: update.oldPrice,
          totalNewPrice: update.newPrice,
          count: 1
        });
      }
    });
    
    return Array.from(monthlyData.entries())
      .map(([month, data]) => ({
        month,
        averageOldPrice: data.totalOldPrice / data.count,
        averageNewPrice: data.totalNewPrice / data.count,
        percentChange: ((data.totalNewPrice - data.totalOldPrice) / data.totalOldPrice) * 100
      }))
      .sort((a, b) => {
        // Sort by date (assuming format "MMM YYYY")
        const [aMonth, aYear] = a.month.split(' ');
        const [bMonth, bYear] = b.month.split(' ');
        
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const aMonthIndex = months.indexOf(aMonth);
        const bMonthIndex = months.indexOf(bMonth);
        
        if (aYear !== bYear) return parseInt(aYear) - parseInt(bYear);
        return aMonthIndex - bMonthIndex;
      });
  }, [priceUpdates]);

  // Handle dialog open for new price update
  const handleAddPriceUpdate = () => {
    setCurrentPriceUpdate({
      productType: '',
      oldPrice: 0,
      newPrice: 0,
      effectiveDate: new Date(),
      reason: '',
      updatedBy: 'Admin'
    });
    setIsNewUpdate(true);
    setOpenDialog(true);
  };

  // Handle dialog open for editing existing price update
  const handleEditPriceUpdate = (update: PriceUpdate) => {
    setCurrentPriceUpdate({ ...update });
    setIsNewUpdate(false);
    setOpenDialog(true);
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setCurrentPriceUpdate({});
  };

  // Handle save price update
  const handleSavePriceUpdate = () => {
    // Validate form
    if (!currentPriceUpdate.productType || !currentPriceUpdate.reason) {
      setSnackbar({
        open: true,
        message: 'Please fill in all required fields',
        severity: 'error'
      });
      return;
    }

    // In a real app, this would call an API to save the data
    // For now, we'll just show a success message
    setSnackbar({
      open: true,
      message: isNewUpdate ? 'Price update added successfully' : 'Price update edited successfully',
      severity: 'success'
    });
    
    handleCloseDialog();
  };

  // Handle delete price update
  const handleDeletePriceUpdate = (id: string) => {
    // In a real app, this would call an API to delete the data
    // For now, we'll just show a success message
    setSnackbar({
      open: true,
      message: 'Price update deleted successfully',
      severity: 'success'
    });
  };

  // Handle pagination change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <ModuleHeader
        title="Price Management"
        subtitle="Monitor and manage product pricing, track price changes, and analyze pricing trends"
        module="commercial"
        submodule="pricing"
        actionLabel="Add Price Update"
        actionIcon={<Add />}
        onAction={handleAddPriceUpdate}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Status Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Price Changes"
              subtitle={`${priceUpdates.length} total price updates`}
              backgroundImage="https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg"
              icon={<AttachMoney />}
              accentColor={theme.palette.primary.main}
              delay={0.1}
              height={140}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Price Increases"
              subtitle={`${priceUpdates.filter(u => u.newPrice > u.oldPrice).length} products with price increases`}
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<TrendingUp />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.2}
              height={140}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Price Decreases"
              subtitle={`${priceUpdates.filter(u => u.newPrice < u.oldPrice).length} products with price decreases`}
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<TrendingDown />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.3}
              height={140}
            />
          </Grid>
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={6}>
            <Card sx={{
              borderRadius: '12px',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              height: '100%',
              background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
            }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" mb={2}>
                  Average Price Changes by Category
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={priceChangeByCategory}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" />
                      <YAxis 
                        tickFormatter={(value) => `${value.toFixed(1)}%`}
                        label={{ value: 'Average Change %', angle: -90, position: 'insideLeft' }}
                      />
                      <RechartsTooltip 
                        formatter={(value: number) => [`${value.toFixed(2)}%`, 'Avg. Change']}
                      />
                      <RechartsLegend />
                      <Bar 
                        dataKey="averageChange" 
                        name="Average Price Change" 
                        fill={theme.palette.primary.main}
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card sx={{
              borderRadius: '12px',
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              height: '100%',
              background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
            }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" mb={2}>
                  Price Trend Over Time
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={priceTrendData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis 
                        yAxisId="left"
                        tickFormatter={(value) => `${value.toFixed(1)}%`}
                        label={{ value: 'Change %', angle: -90, position: 'insideLeft' }}
                      />
                      <YAxis 
                        yAxisId="right" 
                        orientation="right"
                        tickFormatter={(value) => `R${value}`}
                        label={{ value: 'Price (ZAR)', angle: 90, position: 'insideRight' }}
                      />
                      <RechartsTooltip />
                      <RechartsLegend />
                      <Line 
                        yAxisId="right"
                        type="monotone" 
                        dataKey="averageOldPrice" 
                        name="Old Price" 
                        stroke={theme.palette.grey[500]}
                        strokeDasharray="5 5"
                      />
                      <Line 
                        yAxisId="right"
                        type="monotone" 
                        dataKey="averageNewPrice" 
                        name="New Price" 
                        stroke={theme.palette.primary.main}
                        activeDot={{ r: 8 }}
                      />
                      <Line 
                        yAxisId="left"
                        type="monotone" 
                        dataKey="percentChange" 
                        name="% Change" 
                        stroke={theme.palette.secondary.main}
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Price Updates Table */}
        <Card sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.default, 0.95)})`
        }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
              <Typography variant="h6" fontWeight="bold">
                Price Updates
              </Typography>
              <CustomButton
                variant="contained"
                color="primary"
                startIcon={<Add />}
                onClick={handleAddPriceUpdate}
                sx={{ borderRadius: '8px' }}
              >
                Add Price Update
              </CustomButton>
            </Box>

            {/* Filters */}
            <Box display="flex" gap={2} mb={3} flexWrap="wrap">
              <TextField
                placeholder="Search products or reasons..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                sx={{ minWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
              <FormControl size="small" sx={{ minWidth: 200 }}>
                <InputLabel id="filter-category-label">Filter By</InputLabel>
                <Select
                  labelId="filter-category-label"
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  label="Filter By"
                  startAdornment={
                    <InputAdornment position="start">
                      <FilterList />
                    </InputAdornment>
                  }
                >
                  <MenuItem value="all">All Updates</MenuItem>
                  <MenuItem value="increase">Price Increases</MenuItem>
                  <MenuItem value="decrease">Price Decreases</MenuItem>
                  <MenuItem value="livestock">Livestock</MenuItem>
                  <MenuItem value="feed">Feed & Supplements</MenuItem>
                </Select>
              </FormControl>
              <CustomButton
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => {
                  setSearchTerm('');
                  setFilterCategory('all');
                }}
              >
                Reset Filters
              </CustomButton>
            </Box>

            {/* Table */}
            <TableContainer component={Paper} sx={{ boxShadow: 'none', mb: 2 }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableCell>Product</TableCell>
                    <TableCell align="right">Old Price</TableCell>
                    <TableCell align="right">New Price</TableCell>
                    <TableCell align="right">Change</TableCell>
                    <TableCell>Effective Date</TableCell>
                    <TableCell>Reason</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredPriceUpdates
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((update) => {
                      const priceChange = calculatePriceChange(update.oldPrice, update.newPrice);
                      const isIncrease = priceChange > 0;
                      
                      return (
                        <TableRow key={update.id} hover>
                          <TableCell>{update.productType}</TableCell>
                          <TableCell align="right">{formatCurrency(update.oldPrice)}</TableCell>
                          <TableCell align="right">{formatCurrency(update.newPrice)}</TableCell>
                          <TableCell align="right">
                            <Chip
                              icon={isIncrease ? <TrendingUp /> : <TrendingDown />}
                              label={`${priceChange.toFixed(2)}%`}
                              color={isIncrease ? 'error' : 'success'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {new Date(update.effectiveDate).toLocaleDateString('en-ZA')}
                          </TableCell>
                          <TableCell>{update.reason}</TableCell>
                          <TableCell align="right">
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                onClick={() => handleEditPriceUpdate(update)}
                                sx={{ color: theme.palette.primary.main }}
                              >
                                <Edit fontSize="small" />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                onClick={() => handleDeletePriceUpdate(update.id)}
                                sx={{ color: theme.palette.error.main }}
                              >
                                <Delete fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  {filteredPriceUpdates.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                        <Typography variant="body1" color="text.secondary">
                          No price updates found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={filteredPriceUpdates.length}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </CardContent>
        </Card>
      </Box>

      {/* Price Update Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">
              {isNewUpdate ? 'Add Price Update' : 'Edit Price Update'}
            </Typography>
            <IconButton onClick={handleCloseDialog} size="small">
              <Close />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Product Type"
                fullWidth
                value={currentPriceUpdate.productType || ''}
                onChange={(e) => setCurrentPriceUpdate({ ...currentPriceUpdate, productType: e.target.value })}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="Old Price (ZAR)"
                fullWidth
                type="number"
                value={currentPriceUpdate.oldPrice || ''}
                onChange={(e) => setCurrentPriceUpdate({ ...currentPriceUpdate, oldPrice: parseFloat(e.target.value) })}
                required
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">R</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                label="New Price (ZAR)"
                fullWidth
                type="number"
                value={currentPriceUpdate.newPrice || ''}
                onChange={(e) => setCurrentPriceUpdate({ ...currentPriceUpdate, newPrice: parseFloat(e.target.value) })}
                required
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">R</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <DatePicker
                label="Effective Date"
                value={currentPriceUpdate.effectiveDate ? new Date(currentPriceUpdate.effectiveDate) : null}
                onChange={(date) => setCurrentPriceUpdate({ ...currentPriceUpdate, effectiveDate: date as Date })}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Reason for Change"
                fullWidth
                multiline
                rows={3}
                value={currentPriceUpdate.reason || ''}
                onChange={(e) => setCurrentPriceUpdate({ ...currentPriceUpdate, reason: e.target.value })}
                required
                margin="normal"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <CustomButton
            variant="outlined"
            onClick={handleCloseDialog}
            startIcon={<Cancel />}
          >
            Cancel
          </CustomButton>
          <CustomButton
            variant="contained"
            onClick={handleSavePriceUpdate}
            color="primary"
            startIcon={<Save />}
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Pricing;
