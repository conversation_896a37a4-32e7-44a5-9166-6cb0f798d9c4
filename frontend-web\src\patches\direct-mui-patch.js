/**
 * This file contains direct patches for MUI components to fix runtime errors.
 */

// Fix for "Cannot read properties of undefined (reading 'dark')" error in Button component
(function fixButtonDarkError() {
  try {
    // Find the Button component in the window object
    if (window.__MUI_Button) {
      console.log('Found MUI Button component, applying patch...');
      
      // Store the original render method
      const originalRender = window.__MUI_Button.render;
      
      // Override the render method
      window.__MUI_Button.render = function(props, ref) {
        // Ensure props exists
        props = props || {};
        
        // Ensure theme exists
        if (!props.theme) {
          props.theme = {};
        }
        
        // Ensure palette exists
        if (!props.theme.palette) {
          props.theme.palette = {};
        }
        
        // Ensure primary exists
        if (!props.theme.palette.primary) {
          props.theme.palette.primary = {};
        }
        
        // Ensure primary.dark exists
        if (!props.theme.palette.primary.dark) {
          props.theme.palette.primary.dark = props.theme.palette.primary.main || '#2A8A82';
        }
        
        // Call the original render method
        return originalRender.call(this, props, ref);
      };
      
      console.log('MUI Button patch applied successfully');
    } else {
      console.log('MUI Button component not found, patch not applied');
    }
  } catch (error) {
    console.error('Error applying MUI Button patch:', error);
  }
})();

// Export a function to apply the patch manually
export function applyMuiPatches() {
  try {
    // Find all Button components in the DOM
    const buttonElements = document.querySelectorAll('.MuiButton-root');
    
    // Apply the patch to each Button component
    buttonElements.forEach(button => {
      // Get the React component instance
      const instance = button[Object.keys(button).find(key => key.startsWith('__reactInternalInstance$'))];
      
      if (instance && instance.memoizedProps) {
        // Ensure theme exists
        if (!instance.memoizedProps.theme) {
          instance.memoizedProps.theme = {};
        }
        
        // Ensure palette exists
        if (!instance.memoizedProps.theme.palette) {
          instance.memoizedProps.theme.palette = {};
        }
        
        // Ensure primary exists
        if (!instance.memoizedProps.theme.palette.primary) {
          instance.memoizedProps.theme.palette.primary = {};
        }
        
        // Ensure primary.dark exists
        if (!instance.memoizedProps.theme.palette.primary.dark) {
          instance.memoizedProps.theme.palette.primary.dark = instance.memoizedProps.theme.palette.primary.main || '#2A8A82';
        }
      }
    });
    
    console.log('MUI patches applied manually to', buttonElements.length, 'Button components');
  } catch (error) {
    console.error('Error applying MUI patches manually:', error);
  }
}
