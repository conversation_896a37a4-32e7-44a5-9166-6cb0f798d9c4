import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, Chip, CircularProgress, Alert } from '@mui/material';
import { motion } from 'framer-motion';
import { Refresh, LocationOn, SignalCellular4Bar, BatteryFull, BatteryAlert } from '../../utils/iconImports';
import GoogleMapComponent from '../maps/GoogleMapComponent';
import { useTheme } from '@mui/material/styles';

interface RealTimeLocation {
  id: string;
  animalId: string;
  animalName: string;
  tagId: string;
  timestamp: string;
  position: {
    lat: number;
    lng: number;
  };
  batteryLevel: number;
  signalStrength: number;
  location: string;
}

interface RealTimeTrackingProps {
  animalIds?: string[];
  refreshInterval?: number; // in seconds
}

const RealTimeTracking: React.FC<RealTimeTrackingProps> = ({
  animalIds = [],
  refreshInterval = 30
}) => {
  const [locations, setLocations] = useState<RealTimeLocation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const theme = useTheme();

  // Mock data for demonstration
  const mockLocations: RealTimeLocation[] = [
    {
      id: 'loc1',
      animalId: 'ANM001',
      animalName: 'Tshepiso',
      tagId: 'RFID-CTL-2024-001',
      timestamp: new Date().toISOString(),
      position: {
        lat: -25.7461,
        lng: 28.1881
      },
      batteryLevel: 85,
      signalStrength: 92,
      location: 'Veld A'
    },
    {
      id: 'loc2',
      animalId: 'ANM002',
      animalName: 'Kgosi',
      tagId: 'RFID-CTL-2024-002',
      timestamp: new Date().toISOString(),
      position: {
        lat: -25.7465,
        lng: 28.1885
      },
      batteryLevel: 72,
      signalStrength: 88,
      location: 'Kraal 1'
    },
    {
      id: 'loc3',
      animalId: 'ANM003',
      animalName: 'Lesedi',
      tagId: 'RFID-CTL-2024-003',
      timestamp: new Date().toISOString(),
      position: {
        lat: -25.7470,
        lng: 28.1890
      },
      batteryLevel: 15,
      signalStrength: 65,
      location: 'Dipping Area'
    },
    {
      id: 'loc4',
      animalId: 'ANM004',
      animalName: 'Pule',
      tagId: 'RFID-SHP-2024-001',
      timestamp: new Date().toISOString(),
      position: {
        lat: -25.7475,
        lng: 28.1895
      },
      batteryLevel: 90,
      signalStrength: 95,
      location: 'Boma 1'
    },
    {
      id: 'loc5',
      animalId: 'ANM005',
      animalName: 'Lerato',
      tagId: 'RFID-GT-2024-001',
      timestamp: new Date().toISOString(),
      position: {
        lat: -25.7480,
        lng: 28.1900
      },
      batteryLevel: 45,
      signalStrength: 78,
      location: 'Boma 2'
    }
  ];

  // Filter locations based on animalIds if provided
  const filteredLocations = animalIds.length > 0
    ? mockLocations.filter(loc => animalIds.includes(loc.animalId))
    : mockLocations;

  // Function to fetch real-time locations
  const fetchLocations = async () => {
    setLoading(true);
    setError(null);

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate a delay and use mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Add some randomness to simulate movement
      const updatedLocations = filteredLocations.map(loc => ({
        ...loc,
        timestamp: new Date().toISOString(),
        position: {
          lat: loc.position.lat + (Math.random() * 0.001 - 0.0005),
          lng: loc.position.lng + (Math.random() * 0.001 - 0.0005)
        },
        batteryLevel: Math.max(0, Math.min(100, loc.batteryLevel + (Math.random() * 2 - 1))),
        signalStrength: Math.max(0, Math.min(100, loc.signalStrength + (Math.random() * 5 - 2.5)))
      }));

      setLocations(updatedLocations);
      setLastUpdated(new Date());
    } catch (err) {
      setError('Failed to fetch real-time locations');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchLocations();
  }, []);

  // Set up interval for real-time updates
  useEffect(() => {
    const intervalId = setInterval(fetchLocations, refreshInterval * 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [refreshInterval]);

  // Prepare map locations
  const mapLocations = locations.map(loc => ({
    id: loc.id,
    position: loc.position,
    title: `${loc.animalName} (${loc.tagId})`,
    info: `Location: ${loc.location}\nLast updated: ${new Date(loc.timestamp).toLocaleTimeString()}\nBattery: ${loc.batteryLevel}%\nSignal: ${loc.signalStrength}%`
  }));

  return (
    <Box>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: 2
      }}>
        <Typography variant="h6" fontWeight="bold">
          Real-Time Animal Tracking
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {lastUpdated && (
            <Typography variant="body2" color="text.secondary">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </Typography>
          )}
          <CustomButton
            startIcon={<Refresh />}
            onClick={fetchLocations}
            variant="outlined"
            size="small"
            disabled={loading}
          >
            Refresh
          </CustomButton>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper sx={{ p: 0, borderRadius: 2, overflow: 'hidden', mb: 3 }}>
        <Box sx={{ height: 400 }}>
          {loading && locations.length === 0 ? (
            <Box sx={{
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CircularProgress />
            </Box>
          ) : (
            <GoogleMapComponent
              locations={mapLocations}
              height="100%"
              zoom={15}
            />
          )}
        </Box>
      </Paper>

      <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
        Active Animals
      </Typography>

      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
        {locations.map((loc, index) => (
          <motion.div
            key={loc.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
          >
            <Paper
              sx={{
                p: 2,
                width: { xs: '100%', sm: 'calc(50% - 8px)', md: 'calc(33.33% - 11px)', lg: 'calc(25% - 12px)' },
                borderLeft: `4px solid ${loc.batteryLevel < 20 ? theme.palette.error.main : theme.palette.success.main}`,
                transition: 'transform 0.2s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                }
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  {loc.animalName}
                </Typography>
                <Chip
                  size="small"
                  label={loc.tagId}
                  color="primary"
                  variant="outlined"
                />
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <LocationOn fontSize="small" color="error" />
                <Typography variant="body2">
                  {loc.location}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {loc.batteryLevel > 20 ? (
                    <BatteryFull fontSize="small" sx={{ color: theme.palette.success.main }} />
                  ) : (
                    <BatteryAlert fontSize="small" color="error" />
                  )}
                  <Typography variant="body2">
                    {loc.batteryLevel}%
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <SignalCellular4Bar fontSize="small" sx={{
                    color: loc.signalStrength > 70
                      ? theme.palette.success.main
                      : loc.signalStrength > 30
                        ? theme.palette.warning.main
                        : theme.palette.error.main
                  }} />
                  <Typography variant="body2">
                    {loc.signalStrength}%
                  </Typography>
                </Box>

                <Typography variant="caption" color="text.secondary">
                  {new Date(loc.timestamp).toLocaleTimeString()}
                </Typography>
              </Box>
            </Paper>
          </motion.div>
        ))}
      </Box>
    </Box>
  );
};

export default RealTimeTracking;
