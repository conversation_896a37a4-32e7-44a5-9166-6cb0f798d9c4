/**
 * Health Schema
 * 
 * Defines the MongoDB schema for the health collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Medication Schema
const medicationSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['antibiotic', 'vaccine', 'antiparasitic', 'vitamin', 'supplement', 'other'],
    default: 'other'
  },
  dosage: {
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      required: true,
      enum: ['mg', 'g', 'ml', 'l', 'cc', 'tablet', 'capsule', 'other'],
      default: 'ml'
    }
  },
  route: {
    type: String,
    enum: ['oral', 'injection', 'topical', 'inhalation', 'other'],
    default: 'oral'
  },
  frequency: {
    type: String,
    enum: ['once', 'daily', 'twice daily', 'three times daily', 'weekly', 'monthly', 'as needed', 'other'],
    default: 'once'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date
  },
  notes: {
    type: String
  }
});

// Health Record Schema
const healthRecordSchema = new Schema({
  animal: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  recordType: {
    type: String,
    enum: ['examination', 'vaccination', 'treatment', 'surgery', 'injury', 'illness', 'routine', 'other'],
    default: 'examination'
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  performer: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  veterinarian: {
    name: {
      type: String,
      trim: true
    },
    contact: {
      type: String,
      trim: true
    },
    clinic: {
      type: String,
      trim: true
    }
  },
  diagnosis: {
    type: String
  },
  symptoms: [{
    type: String,
    trim: true
  }],
  temperature: {
    value: {
      type: Number,
      min: 0
    },
    unit: {
      type: String,
      enum: ['C', 'F'],
      default: 'C'
    }
  },
  weight: {
    value: {
      type: Number,
      min: 0
    },
    unit: {
      type: String,
      enum: ['kg', 'g', 'lb', 'oz'],
      default: 'kg'
    }
  },
  medications: [medicationSchema],
  procedures: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    cost: {
      type: Number,
      min: 0
    }
  }],
  labResults: [{
    test: {
      type: String,
      required: true,
      trim: true
    },
    result: {
      type: String,
      required: true
    },
    normalRange: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  documents: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  followUp: {
    required: {
      type: Boolean,
      default: false
    },
    date: {
      type: Date
    },
    notes: {
      type: String
    }
  },
  cost: {
    type: Number,
    min: 0
  },
  notes: {
    type: String
  },
  status: {
    type: String,
    enum: ['active', 'resolved', 'ongoing', 'scheduled', 'cancelled'],
    default: 'active'
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Vaccination Schedule Schema
const vaccinationScheduleSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String
  },
  species: {
    type: String,
    required: true,
    trim: true
  },
  vaccinations: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    description: {
      type: String
    },
    ageInMonths: {
      type: Number,
      required: true,
      min: 0
    },
    isBooster: {
      type: Boolean,
      default: false
    },
    frequency: {
      type: String,
      enum: ['once', 'annual', 'biannual', 'custom'],
      default: 'once'
    },
    customFrequencyMonths: {
      type: Number,
      min: 1
    },
    isRequired: {
      type: Boolean,
      default: true
    },
    notes: {
      type: String
    }
  }],
  isActive: {
    type: Boolean,
    default: true
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
healthRecordSchema.index({ animal: 1, date: -1 });
healthRecordSchema.index({ recordType: 1 });
healthRecordSchema.index({ status: 1 });
healthRecordSchema.index({ 'followUp.date': 1 });

vaccinationScheduleSchema.index({ species: 1 });
vaccinationScheduleSchema.index({ isActive: 1 });

// Pre-save hook to update timestamps
healthRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

vaccinationScheduleSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const HealthRecord = mongoose.model('HealthRecord', healthRecordSchema);
const VaccinationSchedule = mongoose.model('VaccinationSchedule', vaccinationScheduleSchema);

module.exports = {
  HealthRecord,
  VaccinationSchedule
};
