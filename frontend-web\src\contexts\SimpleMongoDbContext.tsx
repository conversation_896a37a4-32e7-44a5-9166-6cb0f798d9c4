import React, { createContext, useContext, useState, useEffect } from 'react';

// Define the context type
interface MongoDbContextType {
  isConnected: boolean;
  isLoading: boolean;
  error: Error | null;
  mongoStats: any;
  syncMockData: () => Promise<boolean>;
  refreshStats: () => Promise<void>;
  getStats: () => Promise<void>;
  services: {
    breeding: any;
    health: any;
    animals: any;
  };
}

// Create the context
const MongoDbContext = createContext<MongoDbContextType | undefined>(undefined);

// Simple provider component that provides mock functionality
export const MongoDbProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [mongoStats, setMongoStats] = useState<any>(null);

  // Mock functions
  const syncMockData = async (): Promise<boolean> => {
    console.log('Mock syncMockData called');
    return true;
  };

  const refreshStats = async (): Promise<void> => {
    console.log('Mock refreshStats called');
  };

  const getStats = async (): Promise<void> => {
    console.log('Mock getStats called');
  };

  // Mock services
  const services = {
    breeding: {},
    health: {},
    animals: {},
  };

  return (
    <MongoDbContext.Provider value={{
      isConnected,
      isLoading,
      error,
      services,
      mongoStats,
      syncMockData,
      refreshStats,
      getStats
    }}>
      {children}
    </MongoDbContext.Provider>
  );
};

// Custom hook to use the MongoDB context
export const useMongoDb = () => {
  const context = useContext(MongoDbContext);
  if (context === undefined) {
    throw new Error('useMongoDb must be used within a MongoDbProvider');
  }
  return context;
};
