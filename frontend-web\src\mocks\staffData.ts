export interface Employee {
  id: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    dateOfBirth: Date;
    gender: string;
    contact: {
      email: string;
      phone: string;
      address: string;
    };
  };
  employmentInfo: {
    position: string;
    department: string;
    startDate: Date;
    status: 'active' | 'on-leave' | 'terminated';
    employmentType: 'full-time' | 'part-time' | 'contractor';
    salary: number;
  };
  qualifications: {
    education: string[];
    certifications: string[];
    skills: string[];
  };
  schedule: WorkSchedule[];
  documents: string[]; // URLs to contracts, certificates, etc.
}

export interface WorkSchedule {
  id: string;
  employeeId: string;
  date: Date;
  shift: 'morning' | 'afternoon' | 'night';
  startTime: string;
  endTime: string;
  tasks: string[];
  status: 'scheduled' | 'in-progress' | 'completed';
}

export interface TimeSheet {
  id: string;
  employeeId: string;
  period: {
    start: Date;
    end: Date;
  };
  entries: TimeEntry[];
  totalHours: number;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
}

export interface TimeEntry {
  date: Date;
  clockIn: string;
  clockOut: string;
  breaks: {
    start: string;
    end: string;
  }[];
  tasks: string[];
  notes: string;
}

export const mockEmployees: Employee[] = [
  {
    id: 'EMP001',
    personalInfo: {
      firstName: 'John',
      lastName: 'Smith',
      dateOfBirth: new Date('1990-05-15'),
      gender: 'male',
      contact: {
        email: '<EMAIL>',
        phone: '555-0123',
        address: '123 Farm Road'
      }
    },
    employmentInfo: {
      position: 'Farm Manager',
      department: 'Operations',
      startDate: new Date('2023-01-15'),
      status: 'active',
      employmentType: 'full-time',
      salary: 55000
    },
    qualifications: {
      education: ['Bachelor of Agricultural Science'],
      certifications: ['Livestock Management Certificate'],
      skills: ['Animal Husbandry', 'Farm Operations', 'Team Management']
    },
    schedule: [
      {
        id: 'WS001',
        employeeId: 'EMP001',
        date: new Date('2024-01-22'),
        shift: 'morning',
        startTime: '06:00',
        endTime: '14:00',
        tasks: ['Feed Distribution', 'Health Checks', 'Team Briefing'],
        status: 'scheduled'
      }
    ],
    documents: ['contract.pdf', 'certifications.pdf']
  }
];