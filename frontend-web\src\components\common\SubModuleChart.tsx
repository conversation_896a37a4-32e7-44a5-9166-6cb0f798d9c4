import React, { useState } from 'react';
import { Box, Paper, Typography, useTheme, alpha, IconButton, Tooltip, Menu, MenuItem } from '@mui/material';
import { motion } from 'framer-motion';
import { MoreVert, GetApp, Print, Share, Info } from '../../utils/iconImports';
import { getSafeElevation } from '../../utils/themeDefaults';
import { useLanguage } from '../../contexts/LanguageContext';

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
  }[];
}

export interface SubModuleChartProps {
  title: string;
  description?: string;
  type: 'bar' | 'line' | 'pie' | 'area' | 'doughnut';
  data: ChartData;
  height?: number;
  moduleColor?: string;
  onExport?: () => void;
  onPrint?: () => void;
  onShare?: () => void;
}

/**
 * A standardized chart component for submodules
 */
const SubModuleChart: React.FC<SubModuleChartProps> = ({
  title,
  description,
  type,
  data,
  height = 300,
  moduleColor,
  onExport,
  onPrint,
  onShare
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Use provided module color or default to primary
  const chartColor = moduleColor || theme.palette.primary.main;
  
  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [showDescription, setShowDescription] = useState(false);
  
  // Handle menu open/close
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };
  
  // Toggle description
  const handleToggleDescription = () => {
    setShowDescription(!showDescription);
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        elevation={getSafeElevation(theme, 1)}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(chartColor, 0.1)}`,
          '&:hover': {
            boxShadow: `0 8px 25px ${alpha(chartColor, 0.15)}`,
          },
          transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
        }}
      >
        {/* Chart Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: `1px solid ${alpha(chartColor, 0.1)}`,
          }}
        >
          <Typography variant="h6" fontWeight="bold" color={chartColor}>
            {title}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Info Button */}
            {description && (
              <Tooltip title={showDescription ? translate('common.hide_info') : translate('common.show_info')}>
                <IconButton
                  size="small"
                  onClick={handleToggleDescription}
                  sx={{
                    backgroundColor: showDescription ? alpha(chartColor, 0.2) : alpha(chartColor, 0.1),
                    color: chartColor,
                    '&:hover': {
                      backgroundColor: alpha(chartColor, 0.2),
                    },
                  }}
                >
                  <Info fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            
            {/* Menu Button */}
            <Tooltip title={translate('common.options')}>
              <IconButton
                size="small"
                onClick={handleMenuOpen}
                sx={{
                  backgroundColor: alpha(chartColor, 0.1),
                  color: chartColor,
                  '&:hover': {
                    backgroundColor: alpha(chartColor, 0.2),
                  },
                }}
              >
                <MoreVert fontSize="small" />
              </IconButton>
            </Tooltip>
            
            {/* Menu */}
            <Menu
              anchorEl={menuAnchorEl}
              open={Boolean(menuAnchorEl)}
              onClose={handleMenuClose}
              PaperProps={{
                elevation: 3,
                sx: {
                  borderRadius: 2,
                  minWidth: 150,
                  overflow: 'hidden',
                  mt: 1,
                  '& .MuiMenuItem-root': {
                    px: 2,
                    py: 1,
                    '&:hover': {
                      backgroundColor: alpha(chartColor, 0.1),
                    },
                  },
                },
              }}
            >
              {onExport && (
                <MenuItem onClick={() => { handleMenuClose(); onExport(); }}>
                  <GetApp fontSize="small" sx={{ mr: 1 }} />
                  {translate('common.export')}
                </MenuItem>
              )}
              
              {onPrint && (
                <MenuItem onClick={() => { handleMenuClose(); onPrint(); }}>
                  <Print fontSize="small" sx={{ mr: 1 }} />
                  {translate('common.print')}
                </MenuItem>
              )}
              
              {onShare && (
                <MenuItem onClick={() => { handleMenuClose(); onShare(); }}>
                  <Share fontSize="small" sx={{ mr: 1 }} />
                  {translate('common.share')}
                </MenuItem>
              )}
            </Menu>
          </Box>
        </Box>
        
        {/* Description */}
        {description && showDescription && (
          <Box
            sx={{
              p: 2,
              backgroundColor: alpha(chartColor, 0.05),
              borderBottom: `1px solid ${alpha(chartColor, 0.1)}`,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          </Box>
        )}
        
        {/* Chart Content */}
        <Box sx={{ p: 2, height: height }}>
          {/* This is a placeholder for the actual chart implementation */}
          <Box 
            sx={{ 
              height: '100%', 
              width: '100%', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              border: `1px dashed ${alpha(chartColor, 0.3)}`,
              borderRadius: 1,
              flexDirection: 'column',
              gap: 2
            }}
          >
            <Typography variant="body1" color={chartColor} fontWeight="medium">
              {type.toUpperCase()} Chart
            </Typography>
            
            <Typography variant="body2" color="text.secondary">
              {data.labels.join(', ')}
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', justifyContent: 'center' }}>
              {data.datasets.map((dataset, index) => (
                <Box 
                  key={index}
                  sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1,
                    backgroundColor: alpha(chartColor, 0.1),
                    borderRadius: 1,
                    px: 1,
                    py: 0.5
                  }}
                >
                  <Box 
                    sx={{ 
                      width: 12, 
                      height: 12, 
                      borderRadius: '50%', 
                      backgroundColor: Array.isArray(dataset.backgroundColor) 
                        ? dataset.backgroundColor[0] 
                        : dataset.backgroundColor || chartColor
                    }} 
                  />
                  <Typography variant="caption" color="text.primary">
                    {dataset.label}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default SubModuleChart;
