export interface HealthRecord {
  id: string;
  _id?: string; // MongoDB ID
  animalId: string | number;
  date: string;
  type: string;
  description: string;
  performedBy: string;
  cost: number;
  notes: string;
  followUpDate?: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  priority?: 'low' | 'medium' | 'high';
}

export interface Vaccination {
  id: string;
  animalId: string | number;
  vaccineType: string;
  date: string;
  expiryDate: string;
  batchNumber: string;
  administeredBy: string;
  notes: string;
}

export interface HealthStats {
  totalRecords: number;
  totalAnimals: number;
  byType: {
    vaccination: number;
    treatment: number;
    checkup: number;
    surgery: number;
    quarantine: number;
  };
  byStatus: {
    completed: number;
    scheduled: number;
    requiresFollowup: number;
    cancelled: number;
  };
  pendingCount: number;
  activeTreatments?: number;
  vaccinationsDue?: number;
  healthAlerts?: number;
}
