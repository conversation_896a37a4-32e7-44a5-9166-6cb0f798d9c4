/**
 * Check MongoDB Connection
 * 
 * This script directly checks the MongoDB connection without using any environment variables.
 */

const { MongoClient } = require('mongodb');

// MongoDB connection string - directly set here
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

console.log('Testing MongoDB connection...');
console.log(`Using connection string: ${uri.replace(/:[^:]*@/, ':****@')}`);
console.log(`Using database: ${dbName}`);

async function testConnection() {
  const client = new MongoClient(uri);
  
  try {
    await client.connect();
    console.log('Successfully connected to MongoDB!');
    
    const db = client.db(dbName);
    console.log(`Connected to database: ${db.databaseName}`);
    
    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    console.log('Database ping successful!');
    
    // List collections
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections in database ${db.databaseName}`);
    
    // Log collection names
    collections.forEach(collection => {
      console.log(`- ${collection.name}`);
    });
    
    return true;
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    return false;
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the test
testConnection()
  .then(success => {
    console.log('Test completed with result:', success ? 'SUCCESS' : 'FAILURE');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
