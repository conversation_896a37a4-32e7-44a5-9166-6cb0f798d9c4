/**
 * financial Controller
 * 
 * Controller for financial module
 */

const { getCollection, getModel } = require('../config/mongodb');
const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * Get all financial
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAll = async (req, res) => {
  try {
    // Try to use the Financial model
    try {
      const Financial = getModel('Financial');
      const items = await Financial.find({});
      return res.status(200).json({ success: true, data: items });
    } catch (modelError) {
      logger.warn(`Financial model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('financial');
      const items = await collection.find({}).toArray();
      return res.status(200).json({ success: true, data: items });
    }
  } catch (error) {
    logger.error(`Error in getAll financial: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Get financial by ID
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getById = async (req, res) => {
  try {
    const id = req.params.id;
    
    // Try to use the Financial model
    try {
      const Financial = getModel('Financial');
      const item = await Financial.findById(id);
      
      if (!item) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      return res.status(200).json({ success: true, data: item });
    } catch (modelError) {
      logger.warn(`Financial model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('financial');
      const item = await collection.findOne({ _id: id });
      
      if (!item) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      return res.status(200).json({ success: true, data: item });
    }
  } catch (error) {
    logger.error(`Error in getById financial: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Create financial
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const create = async (req, res) => {
  try {
    const newItem = req.body;
    
    if (!newItem) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }
    
    // Try to use the Financial model
    try {
      const Financial = getModel('Financial');
      const item = new Financial(newItem);
      const savedItem = await item.save();
      
      return res.status(201).json({ success: true, data: savedItem });
    } catch (modelError) {
      logger.warn(`Financial model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('financial');
      const result = await collection.insertOne(newItem);
      
      return res.status(201).json({ success: true, data: { _id: result.insertedId, ...newItem } });
    }
  } catch (error) {
    logger.error(`Error in create financial: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Update financial
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const update = async (req, res) => {
  try {
    const id = req.params.id;
    const updates = req.body;
    
    if (!updates) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }
    
    // Try to use the Financial model
    try {
      const Financial = getModel('Financial');
      const updatedItem = await Financial.findByIdAndUpdate(id, updates, { new: true });
      
      if (!updatedItem) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      return res.status(200).json({ 
        success: true, 
        message: 'Financial updated successfully',
        data: updatedItem
      });
    } catch (modelError) {
      logger.warn(`Financial model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('financial');
      const result = await collection.updateOne({ _id: id }, { $set: updates });
      
      if (result.matchedCount === 0) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      // Get the updated item to return
      const updatedItem = await collection.findOne({ _id: id });
      
      return res.status(200).json({ 
        success: true, 
        message: 'Financial updated successfully',
        data: updatedItem
      });
    }
  } catch (error) {
    logger.error(`Error in update financial: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Delete financial
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const deleteFinancial = async (req, res) => {
  try {
    const id = req.params.id;
    
    // Try to use the Financial model
    try {
      const Financial = getModel('Financial');
      const deletedItem = await Financial.findByIdAndDelete(id);
      
      if (!deletedItem) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      return res.status(200).json({ success: true, message: 'Financial deleted successfully' });
    } catch (modelError) {
      logger.warn(`Financial model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('financial');
      const result = await collection.deleteOne({ _id: id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ success: false, message: 'Financial not found' });
      }
      
      return res.status(200).json({ success: true, message: 'Financial deleted successfully' });
    }
  } catch (error) {
    logger.error(`Error in delete financial: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: deleteFinancial
};
