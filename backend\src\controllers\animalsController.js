/**
 * Animals Controller
 *
 * Controller for animals module
 */

const { getCollection, getModel } = require('../config/mongodb');
const logger = require('../utils/logger');
const mongoose = require('mongoose');

/**
 * Get all animals
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAll = async (req, res) => {
  try {
    // Try to use the Animal model
    try {
      const Animal = getModel('Animal');
      const items = await Animal.find({});
      return res.status(200).json({ success: true, data: items });
    } catch (modelError) {
      logger.warn(`Animal model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('animals');
      const items = await collection.find({}).toArray();
      return res.status(200).json({ success: true, data: items });
    }
  } catch (error) {
    logger.error(`Error in getAll animals: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Get animals by ID
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getById = async (req, res) => {
  try {
    const id = req.params.id;

    // Try to use the Animal model
    try {
      const Animal = getModel('Animal');
      const item = await Animal.findById(id);

      if (!item) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, data: item });
    } catch (modelError) {
      logger.warn(`Animal model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('animals');
      const item = await collection.findOne({ _id: id });

      if (!item) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, data: item });
    }
  } catch (error) {
    logger.error(`Error in getById animals: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Create animals
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const create = async (req, res) => {
  try {
    const newItem = req.body;

    if (!newItem) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }

    // Try to use the Animal model
    try {
      const Animal = getModel('Animal');
      const animal = new Animal(newItem);
      const savedAnimal = await animal.save();

      return res.status(201).json({ success: true, data: savedAnimal });
    } catch (modelError) {
      logger.warn(`Animal model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('animals');
      const result = await collection.insertOne(newItem);

      return res.status(201).json({ success: true, data: { _id: result.insertedId, ...newItem } });
    }
  } catch (error) {
    logger.error(`Error in create animals: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Update animals
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const update = async (req, res) => {
  try {
    const id = req.params.id;
    const updates = req.body;

    if (!updates) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }

    // Try to use the Animal model
    try {
      const Animal = getModel('Animal');
      const updatedAnimal = await Animal.findByIdAndUpdate(id, updates, { new: true });

      if (!updatedAnimal) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, message: 'Animal updated successfully', data: updatedAnimal });
    } catch (modelError) {
      logger.warn(`Animal model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('animals');
      const result = await collection.updateOne({ _id: id }, { $set: updates });

      if (result.matchedCount === 0) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, message: 'Animal updated successfully' });
    }
  } catch (error) {
    logger.error(`Error in update animals: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Delete animals
 *
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const deleteAnimals = async (req, res) => {
  try {
    const id = req.params.id;

    // Try to use the Animal model
    try {
      const Animal = getModel('Animal');
      const deletedAnimal = await Animal.findByIdAndDelete(id);

      if (!deletedAnimal) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, message: 'Animal deleted successfully' });
    } catch (modelError) {
      logger.warn(`Animal model not available, falling back to collection: ${modelError.message}`);

      // Fallback to collection if model is not available
      const collection = await getCollection('animals');
      const result = await collection.deleteOne({ _id: id });

      if (result.deletedCount === 0) {
        return res.status(404).json({ success: false, message: 'Animal not found' });
      }

      return res.status(200).json({ success: true, message: 'Animal deleted successfully' });
    }
  } catch (error) {
    logger.error(`Error in delete animals: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: deleteAnimals
};
