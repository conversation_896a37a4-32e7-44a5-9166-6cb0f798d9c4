/**
 * Scraper Service
 * 
 * Service for scraping agricultural supplier websites and extracting relevant data.
 */

const axios = require('axios');
const cheerio = require('cheerio');
const logger = require('../utils/logger');
const Supplier = require('../models/Supplier');
const SupplierProduct = require('../models/SupplierProduct');

// List of supplier websites to scrape
const SUPPLIER_WEBSITES = [
  {
    name: 'AFGRI',
    url: 'https://www.afgri.co.za',
    logo: 'https://www.afgri.co.za/wp-content/themes/afgri/images/logo.png',
    selectors: {
      products: '.product-item',
      productName: '.product-title',
      productPrice: '.product-price',
      productImage: '.product-image img',
      productDescription: '.product-description',
      contacts: '.contact-info',
      locations: '.branch-locations .location'
    }
  },
  {
    name: 'BK<PERSON>',
    url: 'https://www.bkb.co.za',
    logo: 'https://www.bkb.co.za/wp-content/uploads/2020/07/bkb-logo.png',
    selectors: {
      products: '.product-card',
      productName: '.product-name',
      productPrice: '.product-price',
      productImage: '.product-image img',
      productDescription: '.product-desc',
      contacts: '.contact-details',
      locations: '.branch-list .branch'
    }
  },
  {
    name: 'Senwes',
    url: 'https://www.senwes.co.za',
    logo: 'https://www.senwes.co.za/images/logo.png',
    selectors: {
      products: '.product-listing',
      productName: '.product-title',
      productPrice: '.product-price',
      productImage: '.product-img',
      productDescription: '.product-description',
      contacts: '.contact-section',
      locations: '.branch-locations .branch'
    }
  },
  {
    name: 'Kaap Agri',
    url: 'https://www.kaapagri.co.za',
    logo: 'https://www.kaapagri.co.za/wp-content/themes/kaapagri/images/logo.png',
    selectors: {
      products: '.product-item',
      productName: '.product-name',
      productPrice: '.product-price',
      productImage: '.product-image',
      productDescription: '.product-description',
      contacts: '.contact-info',
      locations: '.store-locator .store'
    }
  },
  {
    name: 'OVK',
    url: 'https://www.ovk.co.za',
    logo: 'https://www.ovk.co.za/wp-content/uploads/2019/01/ovk-logo.png',
    selectors: {
      products: '.product-card',
      productName: '.product-title',
      productPrice: '.product-price',
      productImage: '.product-image',
      productDescription: '.product-description',
      contacts: '.contact-details',
      locations: '.branch-list .branch'
    }
  }
];

/**
 * Scrape a supplier website and extract data
 * @param {Object} supplierConfig - Configuration for the supplier website
 * @returns {Object} - Extracted supplier data
 */
async function scrapeSupplierWebsite(supplierConfig) {
  try {
    logger.info(`Scraping website for ${supplierConfig.name}: ${supplierConfig.url}`);
    
    const response = await axios.get(supplierConfig.url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      timeout: 10000 // 10 seconds timeout
    });
    
    const $ = cheerio.load(response.data);
    
    // Extract basic supplier information
    const supplierData = {
      name: supplierConfig.name,
      logo: supplierConfig.logo,
      website: supplierConfig.url,
      description: $('meta[name="description"]').attr('content') || '',
      shortDescription: $('meta[name="description"]').attr('content')?.substring(0, 150) || '',
      contacts: [],
      locations: [],
      featuredProducts: [],
      dataSources: [{
        url: supplierConfig.url,
        lastScraped: new Date(),
        status: 'success'
      }]
    };
    
    // Extract contact information
    if ($(supplierConfig.selectors.contacts).length) {
      $(supplierConfig.selectors.contacts).each((i, el) => {
        const phoneMatch = $(el).text().match(/(\+\d{1,3}[-\s]?)?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}/);
        const emailMatch = $(el).text().match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
        
        if (phoneMatch) {
          supplierData.contacts.push({
            type: 'phone',
            value: phoneMatch[0],
            label: 'Main Office'
          });
        }
        
        if (emailMatch) {
          supplierData.contacts.push({
            type: 'email',
            value: emailMatch[0],
            label: 'General Inquiries'
          });
        }
      });
    }
    
    // Extract location information
    if ($(supplierConfig.selectors.locations).length) {
      $(supplierConfig.selectors.locations).each((i, el) => {
        const locationName = $(el).find('h3, h4, .location-name').first().text().trim();
        const address = $(el).find('.address, .location-address').text().trim();
        
        if (locationName && address) {
          supplierData.locations.push({
            name: locationName,
            address: address,
            phone: $(el).find('.phone, .location-phone').text().trim() || undefined,
            email: $(el).find('.email, .location-email').text().trim() || undefined
          });
        }
      });
    }
    
    // Extract product information
    if ($(supplierConfig.selectors.products).length) {
      $(supplierConfig.selectors.products).each((i, el) => {
        if (i >= 5) return false; // Limit to 5 featured products
        
        const productName = $(el).find(supplierConfig.selectors.productName).text().trim();
        const productPrice = $(el).find(supplierConfig.selectors.productPrice).text().trim();
        const productImage = $(el).find(supplierConfig.selectors.productImage).attr('src');
        const productDescription = $(el).find(supplierConfig.selectors.productDescription).text().trim();
        
        if (productName) {
          // Extract price as number
          let priceValue = null;
          if (productPrice) {
            const priceMatch = productPrice.match(/[\d\s,.]+/);
            if (priceMatch) {
              priceValue = parseFloat(priceMatch[0].replace(/[^\d.]/g, ''));
            }
          }
          
          // Determine product category based on keywords
          let category = 'other';
          const lowerName = productName.toLowerCase();
          
          if (lowerName.includes('feed') || lowerName.includes('hay') || lowerName.includes('silage')) {
            category = 'livestock-feed';
          } else if (lowerName.includes('tractor') || lowerName.includes('equipment') || lowerName.includes('machine')) {
            category = 'equipment';
          } else if (lowerName.includes('seed') || lowerName.includes('plant')) {
            category = 'seeds';
          } else if (lowerName.includes('fertilizer') || lowerName.includes('nutrient')) {
            category = 'fertilizer';
          } else if (lowerName.includes('chemical') || lowerName.includes('pesticide') || lowerName.includes('herbicide')) {
            category = 'chemicals';
          } else if (lowerName.includes('irrigation') || lowerName.includes('water')) {
            category = 'irrigation';
          } else if (lowerName.includes('medicine') || lowerName.includes('vaccine') || lowerName.includes('health')) {
            category = 'animal-health';
          }
          
          supplierData.featuredProducts.push({
            name: productName,
            category: category,
            description: productDescription || `Product from ${supplierConfig.name}`,
            imageUrl: productImage ? new URL(productImage, supplierConfig.url).href : undefined,
            price: priceValue,
            currency: 'ZAR',
            availability: 'in-stock',
            url: $(el).find('a').attr('href') ? new URL($(el).find('a').attr('href'), supplierConfig.url).href : undefined
          });
        }
      });
    }
    
    // Determine supplier categories based on products
    const categories = new Set();
    supplierData.featuredProducts.forEach(product => {
      if (product.category) {
        categories.add(product.category);
      }
    });
    
    supplierData.categories = Array.from(categories);
    
    // If no categories were found, add a default one
    if (supplierData.categories.length === 0) {
      supplierData.categories = ['retail'];
    }
    
    logger.info(`Successfully scraped data for ${supplierConfig.name}`);
    return supplierData;
    
  } catch (error) {
    logger.error(`Error scraping website for ${supplierConfig.name}:`, error);
    return {
      name: supplierConfig.name,
      logo: supplierConfig.logo,
      website: supplierConfig.url,
      description: `${supplierConfig.name} is a supplier of agricultural products and services in South Africa.`,
      shortDescription: `${supplierConfig.name} - Agricultural supplier`,
      categories: ['retail'],
      contacts: [],
      locations: [],
      featuredProducts: [],
      dataSources: [{
        url: supplierConfig.url,
        lastScraped: new Date(),
        status: 'failed',
        errorMessage: error.message
      }]
    };
  }
}

/**
 * Scrape all supplier websites and update the database
 */
async function scrapeAllSuppliers() {
  logger.info('Starting to scrape all supplier websites');
  
  for (const supplierConfig of SUPPLIER_WEBSITES) {
    try {
      const supplierData = await scrapeSupplierWebsite(supplierConfig);
      
      // Check if supplier already exists in the database
      let supplier = await Supplier.findOne({ name: supplierData.name });
      
      if (supplier) {
        // Update existing supplier
        logger.info(`Updating existing supplier: ${supplierData.name}`);
        
        // Update basic information
        supplier.logo = supplierData.logo;
        supplier.description = supplierData.description || supplier.description;
        supplier.shortDescription = supplierData.shortDescription || supplier.shortDescription;
        supplier.website = supplierData.website;
        supplier.categories = [...new Set([...supplier.categories, ...supplierData.categories])];
        
        // Add new contacts if they don't exist
        supplierData.contacts.forEach(newContact => {
          const contactExists = supplier.contacts.some(
            existingContact => existingContact.type === newContact.type && existingContact.value === newContact.value
          );
          
          if (!contactExists) {
            supplier.contacts.push(newContact);
          }
        });
        
        // Add new locations if they don't exist
        supplierData.locations.forEach(newLocation => {
          const locationExists = supplier.locations.some(
            existingLocation => existingLocation.name === newLocation.name && existingLocation.address === newLocation.address
          );
          
          if (!locationExists) {
            supplier.locations.push(newLocation);
          }
        });
        
        // Update featured products
        // First, remove old featured products that were scraped from this source
        supplier.featuredProducts = supplier.featuredProducts.filter(product => {
          return !product.url || !product.url.includes(supplierConfig.url);
        });
        
        // Then add the new products
        supplier.featuredProducts.push(...supplierData.featuredProducts);
        
        // Update data source information
        const dataSourceIndex = supplier.dataSources.findIndex(ds => ds.url === supplierData.dataSources[0].url);
        
        if (dataSourceIndex >= 0) {
          supplier.dataSources[dataSourceIndex] = supplierData.dataSources[0];
        } else {
          supplier.dataSources.push(supplierData.dataSources[0]);
        }
        
        await supplier.save();
        logger.info(`Successfully updated supplier: ${supplierData.name}`);
        
      } else {
        // Create new supplier
        logger.info(`Creating new supplier: ${supplierData.name}`);
        supplier = new Supplier(supplierData);
        await supplier.save();
        logger.info(`Successfully created supplier: ${supplierData.name}`);
      }
      
    } catch (error) {
      logger.error(`Error processing supplier ${supplierConfig.name}:`, error);
    }
  }
  
  logger.info('Finished scraping all supplier websites');
}

module.exports = {
  scrapeSupplierWebsite,
  scrapeAllSuppliers,
  SUPPLIER_WEBSITES
};
