import React from 'react';
import { ROUTES } from '../../../constants/routes';
import ModuleDashboard, { ModuleMetric, QuickAction } from '../../ModuleDashboard';

const FinancialDashboard: React.FC = () => {
  const metrics: ModuleMetric[] = [
    { label: 'Monthly Revenue', value: '$12,450', trend: '+8%', status: 'positive' },
    { label: 'Expenses', value: '$8,320', trend: '-3%', status: 'positive' },
    { label: 'Net Profit', value: '$4,130', trend: '+15%', status: 'positive' },
    { label: 'ROI', value: '18.5%', trend: '+2%', status: 'positive' }
  ];

  const quickActions: QuickAction[] = [
    { name: 'Add Transaction', icon: '💰', path: `${ROUTES.FINANCIAL}/transaction` },
    { name: 'View Reports', icon: '📊', path: `${ROUTES.FINANCIAL}/reports` },
    { name: 'Budgets', icon: '📑', path: `${ROUTES.FINANCIAL}/budgets` },
    { name: 'Cost Analysis', icon: '📈', path: `${ROUTES.FINANCIAL}/analysis` }
  ];

  return (
    <ModuleDashboard
      title="Financial Management"
      description="Track finances and analyze profitability"
      metrics={metrics}
      quickActions={quickActions}
    />
  );
};

export default FinancialDashboard;