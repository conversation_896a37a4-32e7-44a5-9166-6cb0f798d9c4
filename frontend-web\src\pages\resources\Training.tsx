import React, { useState, useRef } from 'react';
import { useAccessibility } from '../../hooks/useAccessibility';
import AccessibleWrapper from '../../components/common/AccessibleWrapper';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab, InputAdornment, CardMedia, CardActions } from '@mui/material';
import {
  Add,
  School,
  Search,
  FilterList,
  Visibility,
  Edit,
  Delete,
  VideoLibrary,
  MenuBook,
  Person,
  CalendarToday,
  AccessTime,
  LocalOffer,
  PlayArrow,
  OndemandVideo,
  Article,
  Event
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  <PERSON><PERSON><PERSON>Header, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { useResourcesDataContext } from '../../contexts/DataContext';
// Define TrainingResource type
interface TrainingResource {
  id: string;
  title: string;
  type: 'video' | 'course' | 'document' | 'webinar';
  description: string;
  duration?: string;
  instructor?: string;
  level?: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  dateAdded: string;
  viewCount: number;
  completionCount: number;
  thumbnailUrl?: string;
  resourceUrl?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Training: React.FC = () => {
  const theme = useTheme();
  const { trainingResources, loading, error } = useResourcesDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedResource, setSelectedResource] = useState<TrainingResource | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const { handleKeyboardNavigation, getAriaAttributes } = useAccessibility();
  const dialogRef = useRef<HTMLDivElement>(null);

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    type: 'video' as 'video' | 'document' | 'course' | 'webinar',
    description: '',
    duration: '',
    instructor: '',
    tags: [''],
    url: ''  // Keep as url to match the form field name
  });

  // Get unique types from training resources
  const types = trainingResources ? Array.from(new Set(trainingResources.map((resource: TrainingResource) => resource.type))) : [];

  // Filter resources based on type and search term
  const filteredResources = trainingResources?.filter((resource: TrainingResource) => {
    const matchesType = filterType === 'all' || resource.type === filterType;
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (resource.instructor && resource.instructor.toLowerCase().includes(searchTerm.toLowerCase())) ||
      resource.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesType && matchesSearch;
  }) || [];

  // Calculate statistics
  const trainingStats = {
    total: trainingResources?.length || 0,
    videos: trainingResources?.filter((r: TrainingResource) => r.type === 'video').length || 0,
    courses: trainingResources?.filter((r: TrainingResource) => r.type === 'course').length || 0,
    documents: trainingResources?.filter((r: TrainingResource) => r.type === 'document').length || 0,
    webinars: trainingResources?.filter((r: TrainingResource) => r.type === 'webinar').length || 0,
    mostViewed: trainingResources?.length
      ? trainingResources.reduce((prev: TrainingResource, current: TrainingResource) => (prev.viewCount > current.viewCount) ? prev : current).title
      : 'N/A',
    mostCompleted: trainingResources?.length
      ? trainingResources.reduce((prev: TrainingResource, current: TrainingResource) => (prev.completionCount > current.completionCount) ? prev : current).title
      : 'N/A'
  };

  // Handle dialog open/close
  const handleOpenDialog = (resource?: TrainingResource) => {
    if (resource) {
      setSelectedResource(resource);
      setFormData({
        id: resource.id,
        title: resource.title,
        type: resource.type,
        description: resource.description,
        duration: resource.duration || '',
        instructor: resource.instructor || '',
        tags: resource.tags,
        url: resource.resourceUrl || ''
      });
    } else {
      setSelectedResource(null);
      setFormData({
        id: `TRAIN-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        title: '',
        type: 'video',
        description: '',
        duration: '',
        instructor: '',
        tags: [''],
        url: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedResource(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTypeChange = (e: any) => {
    setFormData(prev => ({ ...prev, type: e.target.value }));
  };

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags];
    newTags[index] = value;
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  const handleAddTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const handleRemoveTag = (index: number) => {
    const newTags = [...formData.tags];
    newTags.splice(index, 1);
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  // Handle save resource
  const handleSaveResource = () => {
    // In a real app, this would call an API to save the resource
    console.log('Saving training resource:', formData);

    // For now, just close the dialog
    handleCloseDialog();
  };

  // Handle delete resource
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the resource
    console.log('Deleting training resource:', id);
  };

  // Handle view resource
  const handleViewResource = (resource: TrainingResource) => {
    // In a real app, this would open the resource or redirect to a detailed view
    console.log('Viewing training resource:', resource);

    // For now, just open the dialog in view mode
    handleOpenDialog(resource);
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video':
        return <VideoLibrary />;
      case 'document':
        return <Article />;
      case 'course':
        return <MenuBook />;
      case 'webinar':
        return <Event />;
      default:
        return <School />;
    }
  };

  // Get type color
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'video':
        return 'primary';
      case 'document':
        return 'info';
      case 'course':
        return 'success';
      case 'webinar':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    {
      id: 'type',
      label: 'Type',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          icon={getTypeIcon(value)}
          label={value.charAt(0).toUpperCase() + value.slice(1)}
          color={getTypeColor(value) as any}
          size="small"
        />
      )
    },
    { id: 'instructor', label: 'Instructor', minWidth: 150 },
    { id: 'duration', label: 'Duration', minWidth: 120 },
    { id: 'dateAdded', label: 'Date Added', minWidth: 120 },
    {
      id: 'tags',
      label: 'Tags',
      minWidth: 200,
      format: (value: string[]) => (
        <Box>
          {value.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              sx={{ mr: 0.5, mb: 0.5 }}
            />
          ))}
        </Box>
      )
    },
    {
      id: 'viewCount',
      label: 'Views',
      minWidth: 80,
      align: 'right'
    }
  ];

  // Get thumbnail for resource
  const getResourceThumbnail = (resource: TrainingResource) => {
    if (resource.thumbnailUrl) {
      return resource.thumbnailUrl;
    }

    // Default thumbnails based on type
    switch (resource.type) {
      case 'video':
        return 'https://images.unsplash.com/photo-1590856029826-c7a73142bbf1?q=80&w=500&auto=format&fit=crop';
      case 'document':
        return 'https://images.unsplash.com/photo-1512486130939-2c4f79935e4f?q=80&w=500&auto=format&fit=crop';
      case 'course':
        return 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?q=80&w=500&auto=format&fit=crop';
      case 'webinar':
        return 'https://images.unsplash.com/photo-1591115765373-5207764f72e4?q=80&w=500&auto=format&fit=crop';
      default:
        return 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?q=80&w=500&auto=format&fit=crop';
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Training Resources"
        subtitle="Access and manage training materials for livestock management"
        module="resources"
        submodule="training"
        actionLabel="Add Training"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Resources"
                  subtitle={trainingStats.total.toString()}
                  module="resources"
                  uniqueId="total-training"
                  icon={<School />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Videos"
                  subtitle={trainingStats.videos.toString()}
                  module="resources"
                  uniqueId="videos"
                  icon={<VideoLibrary />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Courses"
                  subtitle={trainingStats.courses.toString()}
                  module="resources"
                  uniqueId="courses"
                  icon={<MenuBook />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Webinars"
                  subtitle={trainingStats.webinars.toString()}
                  module="resources"
                  uniqueId="webinars"
                  icon={<Event />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200, mr: 2 }}>
              <InputLabel id="type-filter-label">Resource Type</InputLabel>
              <Select
                labelId="type-filter-label"
                id="type-filter"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Resource Type"
              >
                <MenuItem value="all" role="option" aria-selected={filterType === 'all'}>All Types</MenuItem>
                {types.map((type) => (
                  <MenuItem
                    key={type as React.Key}
                    value={String(type)}
                    role="option"
                    aria-selected={filterType === type}
                  >
                    {String(type).charAt(0).toUpperCase() + String(type).slice(1)}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              placeholder="Search resources..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Tabs
            value={viewMode === 'grid' ? 0 : 1}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : 'table')}
            indicatorColor="primary"
            textColor="primary"
            aria-label="Training resources view mode selection"
            role="tablist"
          >
            <Tab
              label="Grid View"
              id="training-tab-0"
              aria-controls="training-tabpanel-0"
              role="tab"
            />
            <Tab
              label="Table View"
              id="training-tab-1"
              aria-controls="training-tabpanel-1"
              role="tab"
            />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            role="tabpanel"
            id="training-tabpanel-0"
            aria-labelledby="training-tab-0"
          >
            <Grid container spacing={3}>
              {filteredResources.map((resource: TrainingResource) => (
                <Grid item xs={12} sm={6} md={4} key={resource.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      display: 'flex',
                      flexDirection: 'column'
                    }}
                  >
                    <CardMedia
                      component="img"
                      height="160"
                      image={getResourceThumbnail(resource)}
                      alt={resource.title}
                      sx={{ position: 'relative' }}
                    />
                    <Box
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        zIndex: 1
                      }}
                    >
                      <Chip
                        icon={getTypeIcon(resource.type)}
                        label={resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                        color={getTypeColor(resource.type) as any}
                        size="small"
                      />
                    </Box>
                    {resource.type === 'video' && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '80px',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          bgcolor: 'rgba(0,0,0,0.5)',
                          borderRadius: '50%',
                          width: 48,
                          height: 48,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => handleViewResource(resource)}
                      >
                        <PlayArrow sx={{ color: 'white', fontSize: 32 }} />
                      </Box>
                    )}
                    <CardContent sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" component="h2" fontWeight="bold" gutterBottom>
                        {resource.title}
                      </Typography>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {resource.instructor && (
                          <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Person fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            {resource.instructor}
                          </Box>
                        )}
                        {resource.duration && (
                          <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <AccessTime fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            {resource.duration}
                          </Box>
                        )}
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          {resource.dateAdded}
                        </Box>
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        {resource.description}
                      </Typography>

                      <Box sx={{ mb: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Tags:
                          </Typography>
                        </Box>
                        <Box>
                          {resource.tags.map((tag: string, index: number) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Views:</strong> {resource.viewCount}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Completions:</strong> {resource.completionCount}
                        </Typography>
                      </Box>
                    </CardContent>

                    <CardActions sx={{ justifyContent: 'flex-end', p: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => handleViewResource(resource)}
                        aria-label={`View ${resource.title}`}
                      >
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(resource)}
                        aria-label={`Edit ${resource.title}`}
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(resource.id)}
                        aria-label={`Delete ${resource.title}`}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </CardActions>
                  </Card>
                </Grid>
              ))}

              {filteredResources.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No training resources found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new training resource
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                      aria-label="Add new training resource"
                    >
                      Add Training Resource
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            role="tabpanel"
            id="training-tabpanel-1"
            aria-labelledby="training-tab-1"
          >
            <EnhancedDataTable
              title="Training Resources"
              subtitle="Access and manage training materials for livestock management"
              columns={columns as any}
              data={filteredResources}
              keyField="id"
              loading={loading}
              onView={(row: any) => handleViewResource(row)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['title', 'description', 'instructor', 'tags']}
              emptyMessage="No training resources found"
              accentColor={theme.palette.primary.main}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Resource Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        ref={dialogRef}
        aria-labelledby="training-dialog-title"
        aria-describedby="training-dialog-description"
      >
        <DialogTitle id="training-dialog-title">
          {selectedResource ? 'Edit Training Resource' : 'Add New Training Resource'}
        </DialogTitle>
        <DialogContent dividers id="training-dialog-description">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Resource ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="type-label">Resource Type</InputLabel>
                <Select
                  labelId="type-label"
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleTypeChange}
                  label="Resource Type"
                >
                  <MenuItem value="video">Video</MenuItem>
                  <MenuItem value="document">Document</MenuItem>
                  <MenuItem value="course">Course</MenuItem>
                  <MenuItem value="webinar">Webinar</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter resource title"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Instructor/Author"
                name="instructor"
                value={formData.instructor}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter instructor or author name"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Duration"
                name="duration"
                value={formData.duration}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., 45 minutes, 2 hours, 3 weeks"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                placeholder="Enter a detailed description of the resource"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="URL"
                name="url"
                value={formData.url || ''}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter URL to the resource"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              {formData.tags.map((tag, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={tag}
                    onChange={(e) => handleTagChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Tag ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveTag(index)}
                    disabled={formData.tags.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddTag}
                size="small"
                sx={{ mt: 1 }}
                aria-label="Add new tag"
              >
                Add Tag
              </CustomButton>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton
            onClick={handleCloseDialog}
            aria-label="Cancel and close dialog"
          >
            Cancel
          </CustomButton>
          <CustomButton
            onClick={handleSaveResource}
            variant="contained"
            color="primary"
            aria-label={selectedResource ? 'Save changes to training resource' : 'Add new training resource'}
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Training;
