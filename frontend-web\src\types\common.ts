// Common types used across the application

export type MetricStatus = 'positive' | 'negative' | 'neutral' | 'warning';

export interface ModuleMetric {
  label: string;
  value: string | number;
  trend?: string;
  status?: MetricStatus;
  icon?: React.ReactNode;
}

export interface QuickAction {
  name: string;
  icon: React.ReactNode | string;
  path: string;
  color?: string;
}

export interface SubModule {
  name: string;
  path: string;
  count?: number;
  status?: string;
  icon?: React.ReactNode | string;
}

export interface ModuleData {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  stats: Record<string, string | number>;
  subModules: SubModule[];
  bgClass?: string;
}