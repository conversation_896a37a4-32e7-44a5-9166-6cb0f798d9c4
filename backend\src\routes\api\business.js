/**
 * Business Analysis API Routes
 * 
 * This module provides API routes for business analysis.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const logger = require('../../utils/logger');

/**
 * @route GET /api/business/strategies
 * @desc Get all business strategies
 * @access Private
 */
router.get('/strategies', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const { 
      status, 
      search,
      sort = 'name', 
      order = 'asc',
      limit = 100, 
      page = 1 
    } = req.query;
    
    // Build query
    const query = {};
    if (status) query.status = status;
    
    // Add search if provided
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };
    
    // Get strategies
    const strategies = await ApiService.find('BusinessStrategy', query, options);
    
    // Get total count
    const total = await ApiService.count('BusinessStrategy', query);
    
    // Return strategies
    res.json({
      success: true,
      data: strategies,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/business/strategies/:id
 * @desc Get business strategy by ID
 * @access Private
 */
router.get('/strategies/:id', authenticate, async (req, res, next) => {
  try {
    // Get strategy
    const strategy = await ApiService.findById('BusinessStrategy', req.params.id);
    
    // Check if strategy exists
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: 'Business strategy not found'
      });
    }
    
    // Return strategy
    res.json({
      success: true,
      data: strategy
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/business/strategies
 * @desc Create a new business strategy
 * @access Private
 */
router.post('/strategies', authenticate, requirePermission(['create_business_strategy']), async (req, res, next) => {
  try {
    // Create strategy
    const strategy = await ApiService.create('BusinessStrategy', {
      ...req.body,
      createdBy: req.user.id
    });
    
    // Return strategy
    res.status(201).json({
      success: true,
      data: strategy
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route PUT /api/business/strategies/:id
 * @desc Update a business strategy
 * @access Private
 */
router.put('/strategies/:id', authenticate, requirePermission(['update_business_strategy']), async (req, res, next) => {
  try {
    // Update strategy
    const strategy = await ApiService.updateById('BusinessStrategy', req.params.id, req.body);
    
    // Check if strategy exists
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: 'Business strategy not found'
      });
    }
    
    // Return strategy
    res.json({
      success: true,
      data: strategy
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route DELETE /api/business/strategies/:id
 * @desc Delete a business strategy
 * @access Private
 */
router.delete('/strategies/:id', authenticate, requirePermission(['delete_business_strategy']), async (req, res, next) => {
  try {
    // Get strategy
    const strategy = await ApiService.findById('BusinessStrategy', req.params.id);
    
    // Check if strategy exists
    if (!strategy) {
      return res.status(404).json({
        success: false,
        message: 'Business strategy not found'
      });
    }
    
    // Delete strategy
    await ApiService.deleteById('BusinessStrategy', req.params.id);
    
    // Return success
    res.json({
      success: true,
      data: strategy
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/business/market-analysis
 * @desc Get all market analysis records
 * @access Private
 */
router.get('/market-analysis', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const { 
      marketType, 
      startDate, 
      endDate, 
      sort = 'date', 
      order = 'desc',
      limit = 100, 
      page = 1 
    } = req.query;
    
    // Build query
    const query = {};
    if (marketType) query.marketType = marketType;
    
    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };
    
    // Get market analysis records
    const marketAnalysis = await ApiService.find('MarketAnalysis', query, options);
    
    // Get total count
    const total = await ApiService.count('MarketAnalysis', query);
    
    // Return market analysis records
    res.json({
      success: true,
      data: marketAnalysis,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/business/market-analysis
 * @desc Create a new market analysis record
 * @access Private
 */
router.post('/market-analysis', authenticate, requirePermission(['create_market_analysis']), async (req, res, next) => {
  try {
    // Create market analysis record
    const marketAnalysis = await ApiService.create('MarketAnalysis', {
      ...req.body,
      createdBy: req.user.id
    });
    
    // Return market analysis record
    res.status(201).json({
      success: true,
      data: marketAnalysis
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/business/predictive-analysis
 * @desc Get all predictive analysis records
 * @access Private
 */
router.get('/predictive-analysis', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const { 
      analysisType, 
      startDate, 
      endDate, 
      sort = 'date', 
      order = 'desc',
      limit = 100, 
      page = 1 
    } = req.query;
    
    // Build query
    const query = {};
    if (analysisType) query.analysisType = analysisType;
    
    // Add date range if provided
    if (startDate || endDate) {
      query.date = {};
      if (startDate) query.date.$gte = new Date(startDate);
      if (endDate) query.date.$lte = new Date(endDate);
    }
    
    // Build options
    const options = {
      sort: { [sort]: order === 'desc' ? -1 : 1 },
      limit: parseInt(limit),
      skip: (parseInt(page) - 1) * parseInt(limit)
    };
    
    // Get predictive analysis records
    const predictiveAnalysis = await ApiService.find('PredictiveAnalysis', query, options);
    
    // Get total count
    const total = await ApiService.count('PredictiveAnalysis', query);
    
    // Return predictive analysis records
    res.json({
      success: true,
      data: predictiveAnalysis,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/business/predictive-analysis
 * @desc Create a new predictive analysis record
 * @access Private
 */
router.post('/predictive-analysis', authenticate, requirePermission(['create_predictive_analysis']), async (req, res, next) => {
  try {
    // Create predictive analysis record
    const predictiveAnalysis = await ApiService.create('PredictiveAnalysis', {
      ...req.body,
      createdBy: req.user.id
    });
    
    // Return predictive analysis record
    res.status(201).json({
      success: true,
      data: predictiveAnalysis
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/business/kpis
 * @desc Get business KPIs
 * @access Private
 */
router.get('/kpis', authenticate, async (req, res, next) => {
  try {
    // Parse query parameters
    const { 
      period = 'monthly', 
      startDate = new Date(new Date().getFullYear(), 0, 1).toISOString(), // Default to start of current year
      endDate = new Date().toISOString() // Default to current date
    } = req.query;
    
    // Convert dates to Date objects
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    
    // Build query
    const query = {
      period,
      date: { $gte: startDateObj, $lte: endDateObj }
    };
    
    // Get KPIs
    const kpis = await ApiService.find('BusinessKpi', query, { sort: { date: 1 } });
    
    // Calculate current KPIs if none exist
    if (kpis.length === 0) {
      // Get total revenue
      const totalRevenue = await ApiService.aggregate('Transaction', [
        { 
          $match: { 
            type: 'income',
            transactionDate: { $gte: startDateObj, $lte: endDateObj }
          } 
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);
      
      // Get total expenses
      const totalExpenses = await ApiService.aggregate('Transaction', [
        { 
          $match: { 
            type: 'expense',
            transactionDate: { $gte: startDateObj, $lte: endDateObj }
          } 
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);
      
      // Calculate profit
      const revenue = totalRevenue.length > 0 ? totalRevenue[0].total : 0;
      const expenses = totalExpenses.length > 0 ? totalExpenses[0].total : 0;
      const profit = revenue - expenses;
      
      // Calculate profit margin
      const profitMargin = revenue > 0 ? (profit / revenue) * 100 : 0;
      
      // Get total animal value
      const animalValue = await ApiService.aggregate('Animal', [
        { $match: { status: 'active' } },
        { $group: { _id: null, total: { $sum: '$estimatedValue' } } }
      ]);
      
      // Calculate ROI
      const totalAssetValue = (animalValue.length > 0 ? animalValue[0].total : 0);
      const roi = totalAssetValue > 0 ? (profit / totalAssetValue) * 100 : 0;
      
      // Create KPI object
      const calculatedKpis = {
        date: new Date(),
        period,
        metrics: {
          revenue,
          expenses,
          profit,
          profitMargin,
          roi,
          animalValue: totalAssetValue,
          feedCosts: 0, // Would need to calculate from transactions
          healthcareCosts: 0, // Would need to calculate from transactions
          laborCosts: 0, // Would need to calculate from transactions
          otherCosts: 0 // Would need to calculate from transactions
        },
        targets: {
          revenue: 0,
          expenses: 0,
          profit: 0,
          profitMargin: 0,
          roi: 0
        }
      };
      
      // Return calculated KPIs
      return res.json({
        success: true,
        data: [calculatedKpis],
        isCalculated: true
      });
    }
    
    // Return KPIs
    res.json({
      success: true,
      data: kpis,
      isCalculated: false
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route POST /api/business/kpis
 * @desc Create a new business KPI record
 * @access Private
 */
router.post('/kpis', authenticate, requirePermission(['create_business_kpi']), async (req, res, next) => {
  try {
    // Create KPI record
    const kpi = await ApiService.create('BusinessKpi', {
      ...req.body,
      createdBy: req.user.id
    });
    
    // Return KPI record
    res.status(201).json({
      success: true,
      data: kpi
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
