/**
 * Ensure Admin User Script
 * 
 * This script ensures that the admin user exists with full permissions.
 * It will create the admin user if it doesn't exist or update it if it does.
 * Run with: node scripts/ensure-admin-user.js
 */

require('dotenv').config();
const mongodb = require('../src/config/mongodb');
const bcrypt = require('bcryptjs');
const logger = require('../src/utils/logger');
const { ObjectId } = require('mongodb');

// Default admin user with full permissions
const adminUser = {
  username: 'admin',
  email: '<EMAIL>',
  password: 'Admin@123',
  firstName: 'May',
  lastName: 'Rakgama',
  role: 'admin',
  permissions: ['*'], // All permissions
  isActive: true,
  lastLogin: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
};

// Hash password
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

// Create or update admin user
async function ensureAdminUser() {
  try {
    logger.info('Ensuring admin user exists with full permissions...');
    
    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB');
    
    // Check if users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      logger.info('Users collection does not exist, creating...');
      await db.createCollection('users');
      
      // Create index on username and email
      await db.collection('users').createIndex({ username: 1 }, { unique: true });
      await db.collection('users').createIndex({ email: 1 }, { unique: true });
    }
    
    // Check if admin user already exists
    const existingUser = await db.collection('users').findOne({ username: adminUser.username });
    if (existingUser) {
      logger.info(`Admin user '${adminUser.username}' already exists, updating permissions...`);
      
      // Update admin user with full permissions
      await db.collection('users').updateOne(
        { username: adminUser.username },
        { 
          $set: { 
            role: 'admin', 
            isActive: true, 
            permissions: ['*'],
            updatedAt: new Date() 
          } 
        }
      );
      logger.info('Admin user updated successfully with full permissions');
    } else {
      // Hash password
      adminUser.password = await hashPassword(adminUser.password);
      
      // Insert admin user
      await db.collection('users').insertOne(adminUser);
      logger.info(`Admin user '${adminUser.username}' created successfully with full permissions`);
    }
    
    // Ensure user permissions collection exists
    const permCollections = await db.listCollections({ name: 'user_permissions' }).toArray();
    if (permCollections.length === 0) {
      logger.info('User permissions collection does not exist, creating...');
      await db.createCollection('user_permissions');
    }
    
    // Check if admin permissions exist
    const existingPerms = await db.collection('user_permissions').findOne({ 
      username: adminUser.username 
    });
    
    if (existingPerms) {
      logger.info('Updating admin user permissions...');
      await db.collection('user_permissions').updateOne(
        { username: adminUser.username },
        { 
          $set: { 
            permissions: {
              all: true,
              modules: {
                dashboard: { view: true, edit: true },
                animals: { view: true, edit: true, delete: true },
                health: { view: true, edit: true, delete: true },
                breeding: { view: true, edit: true, delete: true },
                feeding: { view: true, edit: true, delete: true },
                pasture: { view: true, edit: true, delete: true },
                financial: { view: true, edit: true, delete: true },
                inventory: { view: true, edit: true, delete: true },
                reports: { view: true, edit: true, generate: true },
                users: { view: true, edit: true, delete: true, create: true },
                settings: { view: true, edit: true }
              }
            },
            updatedAt: new Date()
          } 
        }
      );
    } else {
      logger.info('Creating admin user permissions...');
      await db.collection('user_permissions').insertOne({
        userId: existingUser ? existingUser._id : new ObjectId(),
        username: adminUser.username,
        role: 'admin',
        permissions: {
          all: true,
          modules: {
            dashboard: { view: true, edit: true },
            animals: { view: true, edit: true, delete: true },
            health: { view: true, edit: true, delete: true },
            breeding: { view: true, edit: true, delete: true },
            feeding: { view: true, edit: true, delete: true },
            pasture: { view: true, edit: true, delete: true },
            financial: { view: true, edit: true, delete: true },
            inventory: { view: true, edit: true, delete: true },
            reports: { view: true, edit: true, generate: true },
            users: { view: true, edit: true, delete: true, create: true },
            settings: { view: true, edit: true }
          }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    logger.info('Admin user permissions set successfully');
    
    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    
    return true;
  } catch (error) {
    logger.error('Error ensuring admin user:', error);
    return false;
  }
}

// Run the script
ensureAdminUser()
  .then(success => {
    if (success) {
      logger.info('Admin user setup completed successfully');
      process.exit(0);
    } else {
      logger.error('Admin user setup failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during admin user setup:', error);
    process.exit(1);
  });
