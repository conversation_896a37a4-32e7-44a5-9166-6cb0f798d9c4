/**
 * Run All MongoDB Scripts
 * 
 * This script runs all MongoDB population scripts to ensure all collections are properly populated.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Path to scripts directory
const scriptsDir = __dirname;

// List of scripts to run in order
const scripts = [
  'populate-mongodb.js',
  'populate-business-analysis.js',
  'migrate-mongodb-data.js'
];

// Function to run a script
const runScript = (scriptPath) => {
  return new Promise((resolve, reject) => {
    console.log(`Running ${scriptPath}...`);
    
    const process = spawn('node', [scriptPath], { stdio: 'inherit' });
    
    process.on('close', (code) => {
      if (code === 0) {
        console.log(`${scriptPath} completed successfully`);
        resolve();
      } else {
        console.error(`${scriptPath} failed with code ${code}`);
        reject(new Error(`Script ${scriptPath} failed with code ${code}`));
      }
    });
    
    process.on('error', (err) => {
      console.error(`Error running ${scriptPath}:`, err);
      reject(err);
    });
  });
};

// Run all scripts in sequence
async function runAllScripts() {
  try {
    console.log('Starting MongoDB population scripts...');
    
    for (const script of scripts) {
      const scriptPath = path.join(scriptsDir, script);
      
      // Check if script exists
      if (fs.existsSync(scriptPath)) {
        await runScript(scriptPath);
      } else {
        console.warn(`Script ${script} not found, skipping...`);
      }
    }
    
    console.log('All MongoDB population scripts completed successfully');
  } catch (error) {
    console.error('Error running MongoDB population scripts:', error);
    process.exit(1);
  }
}

// Run the scripts
runAllScripts();
