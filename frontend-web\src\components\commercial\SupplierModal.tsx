import React, { useState, useEffect } from 'react';
import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Grid, IconButton, Typography, FormControl, InputLabel, Select, CircularProgress, Chip, Box, Divider, Rating, SelectChangeEvent,  } from '@mui/material';
import {  Close, Save, Delete, Add  } from '../../utils/iconImports';
import { DatePicker } from '@mui/x-date-pickers';
import { Supplier } from '../../types/commercial';
import dayjs from 'dayjs';
import {  withSubModuleTranslation , CustomButton } from '../common';

interface SupplierModalProps {
  open: boolean;
  onClose: () => void;
  supplier?: Supplier | null;
  onSave: (supplier: Omit<Supplier, 'id'>) => void;
  onUpdate?: (id: string, supplier: Partial<Supplier>) => void;
  onDelete?: (id: string) => void;
  loading?: boolean;
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const SupplierModal: React.FC<SupplierModalProps> = ({
  open,
  onClose,
  supplier,
  onSave,
  onUpdate,
  onDelete,
  loading = false,
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const isEditMode = !!supplier;

  // Form state
  const [formData, setFormData] = useState<Partial<Supplier>>({
    name: '',
    contactPerson: '',
    email: '',
    phone: '',
    address: '',
    products: [],
    rating: 3,
    status: 'active',
    lastOrderDate: undefined,
    notes: '',
  });

  // Product input state
  const [productInput, setProductInput] = useState('');

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when supplier changes
  useEffect(() => {
    if (supplier) {
      setFormData({
        name: supplier.name,
        contactPerson: supplier.contactPerson,
        email: supplier.email,
        phone: supplier.phone,
        address: supplier.address,
        products: [...supplier.products],
        rating: supplier.rating,
        status: supplier.status,
        lastOrderDate: supplier.lastOrderDate,
        notes: supplier.notes,
      });
    } else {
      // Reset form for new supplier
      setFormData({
        name: '',
        contactPerson: '',
        email: '',
        phone: '',
        address: '',
        products: [],
        rating: 3,
        status: 'active',
        lastOrderDate: undefined,
        notes: '',
      });
    }
    setProductInput('');
    setErrors({});
  }, [supplier, open]);

  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle date changes
  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, lastOrderDate: date.toDate() }));

      // Clear error when field is edited
      if (errors.lastOrderDate) {
        setErrors(prev => ({ ...prev, lastOrderDate: '' }));
      }
    } else {
      setFormData(prev => ({ ...prev, lastOrderDate: undefined }));
    }
  };

  // Handle rating change
  const handleRatingChange = (_: React.SyntheticEvent, newValue: number | null) => {
    setFormData(prev => ({ ...prev, rating: newValue || 0 }));
  };

  // Add a product
  const handleAddProduct = () => {
    if (!productInput.trim()) return;

    const updatedProducts = [...(formData.products || [])];
    if (!updatedProducts.includes(productInput.trim())) {
      updatedProducts.push(productInput.trim());
      setFormData(prev => ({ ...prev, products: updatedProducts }));
    }
    setProductInput('');
  };

  // Remove a product
  const handleRemoveProduct = (product: string) => {
    const updatedProducts = (formData.products || []).filter(p => p !== product);
    setFormData(prev => ({ ...prev, products: updatedProducts }));
  };

  // This function is now replaced with an inline function in the onKeyDown prop

  // Validate the form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name) newErrors.name = translate ? translate('commercial.supplier.error.name_required', { fallback: 'Supplier name is required' }) : 'Supplier name is required';
    if (!formData.contactPerson) newErrors.contactPerson = translate ? translate('commercial.supplier.error.contact_required', { fallback: 'Contact person is required' }) : 'Contact person is required';

    if (!formData.email) {
      newErrors.email = translate ? translate('commercial.supplier.error.email_required', { fallback: 'Email is required' }) : 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = translate ? translate('commercial.supplier.error.email_invalid', { fallback: 'Email is invalid' }) : 'Email is invalid';
    }

    if (!formData.phone) {
      newErrors.phone = translate ? translate('commercial.supplier.error.phone_required', { fallback: 'Phone number is required' }) : 'Phone number is required';
    }

    if (!formData.address) newErrors.address = translate ? translate('commercial.supplier.error.address_required', { fallback: 'Address is required' }) : 'Address is required';
    if (!formData.status) newErrors.status = translate ? translate('commercial.supplier.error.status_required', { fallback: 'Status is required' }) : 'Status is required';

    if (!formData.products || formData.products.length === 0) {
      newErrors.products = translate ? translate('commercial.supplier.error.products_required', { fallback: 'At least one product is required' }) : 'At least one product is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) return;

    const supplierData = {
      ...formData,
      products: formData.products || [],
    } as Omit<Supplier, 'id'>;

    if (isEditMode && supplier && onUpdate) {
      onUpdate(supplier.id, supplierData);
    } else {
      onSave(supplierData);
    }
  };

  // Handle supplier deletion
  const handleDelete = () => {
    if (isEditMode && supplier && onDelete) {
      onDelete(supplier.id);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', pb: 1 }}>
        <Typography variant="h5" component="div" fontWeight="bold">
          {isEditMode
            ? (translateSubModule ? translateSubModule('edit_title', 'Edit Supplier') : 'Edit Supplier')
            : (translateSubModule ? translateSubModule('new_title', 'New Supplier') : 'New Supplier')}
        </Typography>
        <IconButton onClick={onClose} size="small">
          <Close />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {translateModuleField ? translateModuleField('basic_information', 'Basic Information') : 'Basic Information'}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('supplier_name', 'Supplier Name') : 'Supplier Name'}
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              error={!!errors.name}
              helperText={errors.name}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('contact_person', 'Contact Person') : 'Contact Person'}
              name="contactPerson"
              value={formData.contactPerson || ''}
              onChange={handleChange}
              error={!!errors.contactPerson}
              helperText={errors.contactPerson}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('email', 'Email') : 'Email'}
              name="email"
              type="email"
              value={formData.email || ''}
              onChange={handleChange}
              error={!!errors.email}
              helperText={errors.email}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('phone', 'Phone') : 'Phone'}
              name="phone"
              value={formData.phone || ''}
              onChange={handleChange}
              error={!!errors.phone}
              helperText={errors.phone}
              required
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('address', 'Address') : 'Address'}
              name="address"
              value={formData.address || ''}
              onChange={handleChange}
              error={!!errors.address}
              helperText={errors.address}
              required
            />
          </Grid>

          {/* Additional Information */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {translateModuleField ? translateModuleField('additional_information', 'Additional Information') : 'Additional Information'}
            </Typography>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth required error={!!errors.status}>
              <InputLabel>{translateModuleField ? translateModuleField('status', 'Status') : 'Status'}</InputLabel>
              <Select
                name="status"
                value={formData.status || ''}
                onChange={handleSelectChange}
                label={translateModuleField ? translateModuleField('status', 'Status') : 'Status'}
              >
                <MenuItem value="active">{translate ? translate('common.status.active', { fallback: 'Active' }) : 'Active'}</MenuItem>
                <MenuItem value="inactive">{translate ? translate('common.status.inactive', { fallback: 'Inactive' }) : 'Inactive'}</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <DatePicker
              label={translateModuleField ? translateModuleField('last_order_date', 'Last Order Date') : 'Last Order Date'}
              value={formData.lastOrderDate ? dayjs(formData.lastOrderDate) : null}
              onChange={handleDateChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  helperText: translate ? translate('common.optional', { fallback: 'Optional' }) : 'Optional'
                }
              }}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography component="legend" gutterBottom>
              {translateModuleField ? translateModuleField('supplier_rating', 'Supplier Rating') : 'Supplier Rating'}
            </Typography>
            <Rating
              name="rating"
              value={formData.rating || 0}
              onChange={handleRatingChange}
              precision={0.5}
              size="large"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label={translateModuleField ? translateModuleField('notes', 'Notes') : 'Notes'}
              name="notes"
              value={formData.notes || ''}
              onChange={handleChange}
              multiline
              rows={2}
            />
          </Grid>

          {/* Products */}
          <Grid item xs={12} sx={{ mt: 2 }}>
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {translateModuleField ? translateModuleField('products_supplied', 'Products Supplied') : 'Products Supplied'}
            </Typography>

            <Box display="flex" alignItems="flex-start" mb={2}>
              <TextField
                fullWidth
                label={translateModuleField ? translateModuleField('add_product', 'Add Product') : 'Add Product'}
                value={productInput}
                onChange={(e) => setProductInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddProduct())}
                error={!!errors.products}
                helperText={errors.products}
                sx={{ mr: 1 }}
              />
              <CustomButton
                variant="contained"
                onClick={handleAddProduct}
                startIcon={<Add />}
                sx={{ mt: 1 }}
              >
                {translate ? translate('common.add', { fallback: 'Add' }) : 'Add'}
              </CustomButton>
            </Box>

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {(formData.products || []).map((product) => (
                <Chip
                  key={product}
                  label={product}
                  onDelete={() => handleRemoveProduct(product)}
                  color="primary"
                  variant="outlined"
                />
              ))}

              {(formData.products || []).length === 0 && (
                <Typography variant="body2" color="text.secondary">
                  {translate ? translate('commercial.supplier.no_products', { fallback: 'No products added yet. Add products that this supplier provides.' }) : 'No products added yet. Add products that this supplier provides.'}
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        {isEditMode && onDelete && (
          <CustomButton
            onClick={handleDelete}
            color="error"
            startIcon={<Delete />}
            disabled={loading}
          >
            {translate ? translate('common.delete', { fallback: 'Delete' }) : 'Delete'}
          </CustomButton>
        )}
        <Box flexGrow={1} />
        <CustomButton onClick={onClose} disabled={loading}>
          {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
        </CustomButton>
        <CustomButton
          onClick={handleSubmit}
          variant="contained"
          startIcon={<Save />}
          disabled={loading}
        >
          {loading ? (
            <CircularProgress size={24} />
          ) : (
            isEditMode
              ? (translate ? translate('common.update', { fallback: 'Update' }) : 'Update')
              : (translate ? translate('common.save', { fallback: 'Save' }) : 'Save')
          )}
        </CustomButton>
      </DialogActions>
    </Dialog>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(SupplierModal, 'commercial', 'supplier');
