// Breeding Record Type
export interface BreedingRecord {
  id: string;
  femaleId: string;
  femaleName: string;
  femaleType: string;
  maleId: string;
  maleName: string;
  maleType: string;
  date: string;
  type: 'natural' | 'artificial';
  status: 'pending' | 'confirmed' | 'unsuccessful';
  expectedDueDate: string;
  technician: string;
  notes: string;
  followUpDates: string[];
}

// Birth Record Type
export interface BirthRecord {
  id: string;
  breedingRecordId: string;
  motherId: string;
  motherName: string;
  motherType: string;
  fatherId: string;
  fatherName: string;
  fatherType: string;
  date: string;
  location: string;
  numberOfOffspring: number;
  offspring: {
    id: string;
    tagNumber: string;
    name: string;
    gender: string;
    birthWeight: number;
    status: 'healthy' | 'weak' | 'stillborn' | 'deceased';
    notes: string;
  }[];
  complications: boolean;
  complicationNotes: string;
  assistanceProvided: boolean;
  assistanceNotes: string;
  attendedBy: string;
}

// Mock Breeding Records
export const mockBreedingRecords: BreedingRecord[] = [
  {
    id: 'BR001',
    femaleId: '1',
    femaleName: 'Tshepiso',
    femaleType: 'Cattle',
    maleId: '3',
    maleName: 'Lesedi',
    maleType: 'Cattle',
    date: '2024-01-15',
    type: 'natural',
    status: 'confirmed',
    expectedDueDate: '2024-10-25',
    technician: 'Thabo Mokoena',
    notes: 'Successful breeding in pasture',
    followUpDates: ['2024-02-15', '2024-03-15', '2024-04-15']
  },
  {
    id: 'BR002',
    femaleId: '2',
    femaleName: 'Tumelo',
    femaleType: 'Cattle',
    maleId: '3',
    maleName: 'Lesedi',
    maleType: 'Cattle',
    date: '2024-01-20',
    type: 'natural',
    status: 'confirmed',
    expectedDueDate: '2024-10-30',
    technician: 'Thabo Mokoena',
    notes: 'Successful breeding in pasture',
    followUpDates: ['2024-02-20', '2024-03-20', '2024-04-20']
  },
  {
    id: 'BR003',
    femaleId: '8',
    femaleName: 'Naledi',
    femaleType: 'Sheep',
    maleId: '15',
    maleName: 'Thabo',
    maleType: 'Sheep',
    date: '2024-02-10',
    type: 'natural',
    status: 'confirmed',
    expectedDueDate: '2024-07-10',
    technician: 'Lerato Molefe',
    notes: 'Successful breeding in pen',
    followUpDates: ['2024-03-10', '2024-04-10', '2024-05-10']
  },
  {
    id: 'BR004',
    femaleId: '12',
    femaleName: 'Tau',
    femaleType: 'Goat',
    maleId: '16',
    maleName: 'Kagiso',
    maleType: 'Goat',
    date: '2024-02-15',
    type: 'natural',
    status: 'confirmed',
    expectedDueDate: '2024-07-15',
    technician: 'Lerato Molefe',
    notes: 'Successful breeding in pen',
    followUpDates: ['2024-03-15', '2024-04-15', '2024-05-15']
  },
  {
    id: 'BR005',
    femaleId: '5',
    femaleName: 'Kgosi',
    femaleType: 'Cattle',
    maleId: '7',
    maleName: 'Pule',
    maleType: 'Cattle',
    date: '2024-03-01',
    type: 'artificial',
    status: 'pending',
    expectedDueDate: '2024-12-10',
    technician: 'Dr. van Niekerk',
    notes: 'AI procedure completed by experienced technician',
    followUpDates: ['2024-04-01', '2024-05-01', '2024-06-01']
  },
  {
    id: 'BR006',
    femaleId: '18',
    femaleName: 'Dineo',
    femaleType: 'Cattle',
    maleId: '3',
    maleName: 'Lesedi',
    maleType: 'Cattle',
    date: '2024-03-10',
    type: 'artificial',
    status: 'pending',
    expectedDueDate: '2024-12-20',
    technician: 'Dr. Botha',
    notes: 'AI procedure completed by experienced technician',
    followUpDates: ['2024-04-10', '2024-05-10', '2024-06-10']
  },
  {
    id: 'BR007',
    femaleId: '20',
    femaleName: 'Mpho',
    femaleType: 'Sheep',
    maleId: '15',
    maleName: 'Thabo',
    maleType: 'Sheep',
    date: '2024-03-15',
    type: 'natural',
    status: 'unsuccessful',
    expectedDueDate: '2024-08-15',
    technician: 'Lerato Molefe',
    notes: 'Breeding attempt unsuccessful, will try again next cycle',
    followUpDates: ['2024-04-15']
  },
  {
    id: 'BR008',
    femaleId: '22',
    femaleName: 'Tebogo',
    femaleType: 'Goat',
    maleId: '16',
    maleName: 'Kagiso',
    maleType: 'Goat',
    date: '2024-03-20',
    type: 'natural',
    status: 'pending',
    expectedDueDate: '2024-08-20',
    technician: 'Lerato Molefe',
    notes: 'Breeding in pen, monitoring for confirmation',
    followUpDates: ['2024-04-20', '2024-05-20']
  }
];

// Mock Birth Records
export const mockBirthRecords: BirthRecord[] = [
  {
    id: 'BIR001',
    breedingRecordId: 'BR001',
    motherId: '1',
    motherName: 'Tshepiso',
    motherType: 'Cattle',
    fatherId: '3',
    fatherName: 'Lesedi',
    fatherType: 'Cattle',
    date: '2023-10-25',
    location: 'Calving Pen 1',
    numberOfOffspring: 1,
    offspring: [
      {
        id: '30',
        tagNumber: 'CTL-2023-030',
        name: 'Bokang',
        gender: 'Male',
        birthWeight: 35,
        status: 'healthy',
        notes: 'Strong calf, standing and nursing within 30 minutes'
      }
    ],
    complications: false,
    complicationNotes: '',
    assistanceProvided: false,
    assistanceNotes: '',
    attendedBy: 'Thabo Mokoena'
  },
  {
    id: 'BIR002',
    breedingRecordId: 'BR002',
    motherId: '2',
    motherName: 'Tumelo',
    motherType: 'Cattle',
    fatherId: '3',
    fatherName: 'Lesedi',
    fatherType: 'Cattle',
    date: '2023-10-30',
    location: 'Calving Pen 2',
    numberOfOffspring: 1,
    offspring: [
      {
        id: '31',
        tagNumber: 'CTL-2023-031',
        name: 'Karabo',
        gender: 'Female',
        birthWeight: 32,
        status: 'healthy',
        notes: 'Normal birth, calf nursing well'
      }
    ],
    complications: false,
    complicationNotes: '',
    assistanceProvided: false,
    assistanceNotes: '',
    attendedBy: 'Thabo Mokoena'
  },
  {
    id: 'BIR003',
    breedingRecordId: 'BR003',
    motherId: '8',
    motherName: 'Naledi',
    motherType: 'Sheep',
    fatherId: '15',
    fatherName: 'Thabo',
    fatherType: 'Sheep',
    date: '2023-07-10',
    location: 'Lambing Pen 1',
    numberOfOffspring: 2,
    offspring: [
      {
        id: '32',
        tagNumber: 'SHP-2023-032',
        name: 'Khumo',
        gender: 'Male',
        birthWeight: 4.2,
        status: 'healthy',
        notes: 'Strong lamb, nursing well'
      },
      {
        id: '33',
        tagNumber: 'SHP-2023-033',
        name: 'Katlego',
        gender: 'Female',
        birthWeight: 3.8,
        status: 'healthy',
        notes: 'Slightly smaller but healthy, nursing well'
      }
    ],
    complications: false,
    complicationNotes: '',
    assistanceProvided: false,
    assistanceNotes: '',
    attendedBy: 'Lerato Molefe'
  },
  {
    id: 'BIR004',
    breedingRecordId: 'BR004',
    motherId: '12',
    motherName: 'Tau',
    motherType: 'Goat',
    fatherId: '16',
    fatherName: 'Kagiso',
    fatherType: 'Goat',
    date: '2023-07-15',
    location: 'Kidding Pen 1',
    numberOfOffspring: 2,
    offspring: [
      {
        id: '34',
        tagNumber: 'GOT-2023-034',
        name: 'Lebo',
        gender: 'Female',
        birthWeight: 3.5,
        status: 'healthy',
        notes: 'Strong kid, nursing well'
      },
      {
        id: '35',
        tagNumber: 'GOT-2023-035',
        name: 'Lesego',
        gender: 'Female',
        birthWeight: 3.2,
        status: 'healthy',
        notes: 'Healthy kid, nursing well'
      }
    ],
    complications: false,
    complicationNotes: '',
    assistanceProvided: false,
    assistanceNotes: '',
    attendedBy: 'Lerato Molefe'
  }
];

// Breeding Statistics
export const mockBreedingStats = {
  totalBreedings: mockBreedingRecords.length,
  successRate: mockBreedingRecords.filter(r => r.status === 'confirmed').length / 
    mockBreedingRecords.length * 100,
  pendingConfirmations: mockBreedingRecords.filter(r => r.status === 'pending').length,
  unsuccessfulBreedings: mockBreedingRecords.filter(r => r.status === 'unsuccessful').length,
  totalBirths: mockBirthRecords.length,
  totalOffspring: mockBirthRecords.reduce((sum, r) => sum + r.numberOfOffspring, 0),
  byType: {
    natural: mockBreedingRecords.filter(r => r.type === 'natural').length,
    artificial: mockBreedingRecords.filter(r => r.type === 'artificial').length
  },
  byAnimalType: {
    Cattle: mockBreedingRecords.filter(r => r.femaleType === 'Cattle').length,
    Sheep: mockBreedingRecords.filter(r => r.femaleType === 'Sheep').length,
    Goat: mockBreedingRecords.filter(r => r.femaleType === 'Goat').length
  },
  twinRate: mockBirthRecords.filter(r => r.numberOfOffspring > 1).length / 
    mockBirthRecords.length * 100,
  complicationRate: mockBirthRecords.filter(r => r.complications).length / 
    mockBirthRecords.length * 100,
  assistanceRate: mockBirthRecords.filter(r => r.assistanceProvided).length / 
    mockBirthRecords.length * 100,
  upcomingBirths: mockBreedingRecords.filter(r => 
    r.status === 'confirmed' && 
    new Date(r.expectedDueDate) > new Date()).length
};
