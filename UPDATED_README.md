# AgriIntel - Smart Farming, Smarter Decisions

A comprehensive intelligent livestock management system for tracking animals, feeding, health, breeding, financial, and business analytics.

## Table of Contents

1. [Features](#features)
2. [Installation](#installation)
3. [Configuration](#configuration)
4. [Running the Application](#running-the-application)
5. [Testing](#testing)
6. [Project Structure](#project-structure)
7. [API Documentation](#api-documentation)
8. [Contributing](#contributing)
9. [License](#license)

## Features

- **Animal Management**: Track animals, their details, and lifecycle events
- **Feeding Management**: Schedule and track feeding routines
- **Health Management**: Record health events, treatments, and vaccinations
- **Breeding Management**: Track breeding records, births, and genetics
- **Financial Management**: Track income, expenses, and financial performance
- **Inventory Management**: Manage feed, supplies, and equipment
- **Business Analysis**: Analyze farm performance and make data-driven decisions
- **User Management**: Manage users with different roles and permissions
- **Reports**: Generate comprehensive reports for all aspects of the farm

## Installation

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (v4.4 or higher)
- npm (v6 or higher)

### Clone the Repository

```bash
git clone https://github.com/yourusername/ampd-livestock.git
cd ampd-livestock
```

### Install Dependencies

```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend-web
npm install
```

## Configuration

### Backend Configuration

Create a `.env` file in the `backend` directory with the following variables:

```
# Server Configuration
PORT=3001
NODE_ENV=development
USE_MOCK_DATA=false

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/ampd_livestock
MONGODB_DB_NAME=AMPD Live Stock

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
```

### Frontend Configuration

Create a `.env` file in the `frontend-web` directory with the following variables:

```
# API Configuration
REACT_APP_API_URL=http://localhost:3001/api

# Default Settings
REACT_APP_DEFAULT_LANGUAGE=en
REACT_APP_DEFAULT_THEME=light
REACT_APP_DEFAULT_COLOR=metalBlue
```

## Running the Application

### Using the Start Script

The easiest way to run the application is to use the start script:

```bash
node scripts/start-application.js
```

This will start both the backend and frontend servers.

### Manual Start

#### Backend

```bash
cd backend
npm start
```

The backend server will start on http://localhost:3001.

#### Frontend

```bash
cd frontend-web
npm start
```

The frontend server will start on http://localhost:3000.

## Testing

### Using the Test Script

To test the application, run:

```bash
node scripts/test-application.js
```

This will run a series of tests to verify that the application is working correctly.

### Manual Testing

You can also test the application manually by:

1. Starting the application using the start script
2. Opening http://localhost:3000 in your browser
3. Logging in with the default admin credentials:
   - Username: admin
   - Password: Admin@123

## Project Structure

```
ampd-livestock/
├── backend/                # Backend server
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── controllers/    # API controllers
│   │   ├── middleware/     # Express middleware
│   │   ├── models/         # MongoDB models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── app.js          # Express app
│   └── package.json
├── frontend-web/           # Frontend React app
│   ├── public/             # Static files
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts
│   │   ├── hooks/          # Custom hooks
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── utils/          # Utility functions
│   │   └── App.tsx         # Main app component
│   └── package.json
├── scripts/                # Utility scripts
└── README.md
```

## API Documentation

The API documentation is available at http://localhost:3001/api-docs when the backend server is running.

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add my feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
