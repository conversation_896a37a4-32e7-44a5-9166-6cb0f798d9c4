const db = require('../config/database');
const logger = require('../utils/logger');
const { AnimalNotFoundError, ValidationError } = require('../utils/errors');

class AnimalService {
  async updateHealth(animalId, data) {
    const animal = await this.findAnimal(animalId);
    
    const healthRecord = {
      animalId,
      date: new Date(),
      type: data.type,
      diagnosis: data.diagnosis,
      treatment: data.treatment,
      notes: data.notes,
      veterinarian: data.veterinarian,
      followUpDate: data.followUpDate
    };

    await db.transaction(async (trx) => {
      await trx('health_records').insert(healthRecord);
      await trx('animals')
        .where({ id: animalId })
        .update({ 
          health_status: data.status,
          last_health_check: new Date()
        });
    });

    return { success: true, animalId, healthRecord };
  }

  async updateWeight(animalId, data) {
    const animal = await this.findAnimal(animalId);
    
    const weightRecord = {
      animalId,
      date: new Date(),
      weight: data.weight,
      units: data.units || 'kg',
      recorded_by: data.recordedBy
    };

    await db.transaction(async (trx) => {
      await trx('weight_records').insert(weightRecord);
      await trx('animals')
        .where({ id: animalId })
        .update({ 
          current_weight: data.weight,
          last_weight_date: new Date()
        });
    });

    return { success: true, animalId, weightRecord };
  }

  async updateLocation(animalId, data) {
    const animal = await this.findAnimal(animalId);
    
    const locationUpdate = {
      animalId,
      date: new Date(),
      previous_location: animal.current_location,
      new_location: data.location,
      reason: data.reason,
      moved_by: data.movedBy
    };

    await db.transaction(async (trx) => {
      await trx('location_history').insert(locationUpdate);
      await trx('animals')
        .where({ id: animalId })
        .update({ 
          current_location: data.location,
          last_location_update: new Date()
        });
    });

    return { success: true, animalId, locationUpdate };
  }

  async updateStatus(animalId, data) {
    const animal = await this.findAnimal(animalId);
    
    const statusUpdate = {
      animalId,
      date: new Date(),
      previous_status: animal.status,
      new_status: data.status,
      reason: data.reason,
      notes: data.notes,
      updated_by: data.updatedBy
    };

    await db.transaction(async (trx) => {
      await trx('status_history').insert(statusUpdate);
      await trx('animals')
        .where({ id: animalId })
        .update({ 
          status: data.status,
          last_status_update: new Date()
        });
    });

    return { success: true, animalId, statusUpdate };
  }

  async findAnimal(animalId) {
    const animal = await db('animals')
      .where({ id: animalId })
      .first();

    if (!animal) {
      throw new AnimalNotFoundError(`Animal with ID ${animalId} not found`);
    }

    return animal;
  }
}

module.exports = new AnimalService();