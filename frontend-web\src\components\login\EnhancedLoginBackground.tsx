import React, { useState, useEffect } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';

interface EnhancedLoginBackgroundProps {
  images: string[];
  interval?: number;
  opacity?: number;
  children?: React.ReactNode;
}

/**
 * EnhancedLoginBackground - A high-quality background component for the login page
 * Features smooth transitions between images and theme-based gradient overlays
 */
const EnhancedLoginBackground: React.FC<EnhancedLoginBackgroundProps> = ({
  images,
  interval = 8000,
  opacity = 0.7,
  children
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  // Get theme colors
  const primaryColor = availableColors[currentColor].primary;
  const secondaryColor = availableColors[currentColor].secondary;
  
  // Rotate through images
  useEffect(() => {
    const timer = setInterval(() => {
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
        setIsTransitioning(false);
      }, 500); // Transition duration
    }, interval);
    
    return () => clearInterval(timer);
  }, [images.length, interval]);
  
  return (
    <Box sx={{
      position: 'relative',
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      borderRadius: { xs: 0, md: 2 }
    }}>
      {/* Background Images with Transitions */}
      <AnimatePresence>
        <motion.div
          key={currentImageIndex}
          initial={{ opacity: 0 }}
          animate={{ opacity: isTransitioning ? 0 : 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5, ease: 'easeInOut' }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${images[currentImageIndex]})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            zIndex: 0
          }}
        />
      </AnimatePresence>
      
      {/* Gradient Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, ${alpha(primaryColor, opacity)}, ${alpha(secondaryColor, opacity)})`,
          zIndex: 1
        }}
      />
      
      {/* Animated Particles */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 2,
          opacity: 0.1
        }}
      >
        <motion.div
          initial={{ scale: 0, x: '10%', y: '10%' }}
          animate={{
            scale: [0, 1.2, 1],
            x: ['10%', '15%', '10%'],
            y: ['10%', '5%', '10%']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
          style={{
            position: 'absolute',
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.common.white, 0.5)} 0%, transparent 70%)`,
            filter: 'blur(40px)'
          }}
        />
        <motion.div
          initial={{ scale: 0, x: '80%', y: '80%' }}
          animate={{
            scale: [0, 1.5, 1.2],
            x: ['80%', '75%', '80%'],
            y: ['80%', '85%', '80%']
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
          style={{
            position: 'absolute',
            width: '250px',
            height: '250px',
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.common.white, 0.5)} 0%, transparent 70%)`,
            filter: 'blur(40px)'
          }}
        />
      </Box>
      
      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 3,
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default EnhancedLoginBackground;
