import React, { useState, Suspense, lazy } from 'react';
import CustomButton from '../components/common/CustomButton';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, TextField, Switch, FormControlLabel, Divider, Box, Tabs, Tab, List, ListItem, ListItemText, ListItemIcon, Avatar, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, InputLabel, Select, MenuItem } from '@mui/material';
import {
  Save,
  Person,
  Notifications,
  Storage,
  CloudSync,
  ColorLens,
  Edit,
  Add,
  Delete,
  Group,
  Security
} from '../utils/iconImports';

// Lazy load the ThemeSettings component
const ThemeSettings = lazy(() => import('./settings/ThemeSettings'));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

// Mock data
const mockUser = {
  id: 'USR001',
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'Administrator',
  lastLogin: '2024-03-07 14:30',
  twoFactorEnabled: true
};

const mockUsers = [
  { id: 'USR002', name: 'John Smith', email: '<EMAIL>', role: 'Manager', status: 'Active' },
  { id: 'USR003', name: 'Sarah Johnson', email: '<EMAIL>', role: 'Veterinarian', status: 'Active' },
  { id: 'USR004', name: 'Michael Brown', email: '<EMAIL>', role: 'Field Worker', status: 'Inactive' }
];

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [openUserDialog, setOpenUserDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [autoBackup, setAutoBackup] = useState(true);
  const [syncEnabled, setSyncEnabled] = useState(true);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenUserDialog = (user?: any) => {
    setSelectedUser(user || null);
    setOpenUserDialog(true);
  };

  const handleCloseUserDialog = () => {
    setOpenUserDialog(false);
    setSelectedUser(null);
  };

  const handleSaveUser = () => {
    // Save logic would go here
    handleCloseUserDialog();
  };

  const handleDeleteUser = (id: string) => {
    // Delete logic would go here
    console.log(`Deleting user with ID: ${id}`);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="p-6 dashboard-bg min-h-screen">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <Typography variant="h3" className="text-gray-800 font-bold mb-2">
          Settings
        </Typography>
        <Typography variant="subtitle1" className="text-gray-600 max-w-2xl">
          Configure your AMPD Livestock Management System preferences, user accounts, and system settings.
        </Typography>
      </motion.div>

      {/* Settings Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="settings tabs">
          <Tab label="Account" icon={<Person />} iconPosition="start" />
          <Tab label="Users & Permissions" icon={<Group />} iconPosition="start" />
          <Tab label="Appearance" icon={<ColorLens />} iconPosition="start" />
          <Tab label="Notifications" icon={<Notifications />} iconPosition="start" />
          <Tab label="System" icon={<Storage />} iconPosition="start" />
          <Tab label="Database" icon={<CloudSync />} iconPosition="start" />
        </Tabs>
      </Box>

      {/* Account Settings */}
      <TabPanel value={tabValue} index={0}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md">
                  <CardContent className="flex flex-col items-center py-6">
                    <Avatar
                      sx={{ width: 100, height: 100, mb: 2, bgcolor: 'primary.main' }}
                    >
                      {mockUser.name.charAt(0)}
                    </Avatar>
                    <Typography variant="h6" className="font-bold">
                      {mockUser.name}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" className="mb-4">
                      {mockUser.role}
                    </Typography>
                    <CustomButton
                      variant="outlined"
                      size="small"
                      className="mb-2"
                    >
                      Change Avatar
                    </CustomButton>
                    <Typography variant="caption" color="textSecondary">
                      Last login: {mockUser.lastLogin}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={8}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Profile Information
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Full Name"
                          fullWidth
                          defaultValue={mockUser.name}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Email"
                          fullWidth
                          defaultValue={mockUser.email}
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Phone Number"
                          fullWidth
                          defaultValue="+27 82 123 4567"
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Position/Role"
                          fullWidth
                          defaultValue={mockUser.role}
                          margin="normal"
                          disabled
                        />
                      </Grid>
                    </Grid>

                    <Divider className="my-4" />

                    <Typography variant="h6" className="font-bold mb-4">
                      Security
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Current Password"
                          type="password"
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="New Password"
                          type="password"
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <TextField
                          label="Confirm New Password"
                          type="password"
                          fullWidth
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} md={6} className="flex items-center">
                        <FormControlLabel
                          control={
                            <Switch
                              checked={mockUser.twoFactorEnabled}
                              color="primary"
                            />
                          }
                          label="Enable Two-Factor Authentication"
                        />
                      </Grid>
                    </Grid>

                    <Box className="flex justify-end mt-4">
                      <CustomButton
                        variant="contained"
                        color="primary"
                        startIcon={<Save />}
                        className="bg-primary-600"
                      >
                        Save Changes
                      </CustomButton>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      </TabPanel>

      {/* Users & Permissions */}
      <TabPanel value={tabValue} index={1}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div variants={itemVariants}>
            <Card className="shadow-md mb-6">
              <CardContent>
                <div className="flex justify-between items-center mb-4">
                  <Typography variant="h6" className="font-bold">
                    User Management
                  </Typography>
                  <CustomButton
                    variant="contained"
                    startIcon={<Group />}
                    href="/settings/users"
                    className="bg-primary-600"
                  >
                    Manage Users
                  </CustomButton>
                </div>

                <Typography variant="body1" paragraph>
                  Manage user accounts, roles, and permissions for the AMPD Livestock Management system.
                  Only administrators can add new users and assign access rights to different modules.
                </Typography>

                <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Person />}
                    href="/settings/users"
                    fullWidth
                    sx={{ justifyContent: 'flex-start', py: 1.5 }}
                  >
                    User Accounts Management
                  </CustomButton>

                  <CustomButton
                    variant="outlined"
                    startIcon={<Security />}
                    href="/settings/users"
                    fullWidth
                    sx={{ justifyContent: 'flex-start', py: 1.5 }}
                  >
                    Role-based Access Control
                  </CustomButton>
                </Box>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Card className="shadow-md">
              <CardContent>
                <Typography variant="h6" className="font-bold mb-4">
                  Role Permissions
                </Typography>

                <div className="overflow-x-auto">
                  <table className="min-w-full bg-white">
                    <thead className="bg-gray-100">
                      <tr>
                        <th className="py-3 px-4 text-left">Permission</th>
                        <th className="py-3 px-4 text-center">Administrator</th>
                        <th className="py-3 px-4 text-center">Manager</th>
                        <th className="py-3 px-4 text-center">Veterinarian</th>
                        <th className="py-3 px-4 text-center">Field Worker</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="py-3 px-4">View Dashboard</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">Manage Animals</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">Manage Health Records</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">-</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">Manage Breeding</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">-</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">Financial Management</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">-</td>
                        <td className="py-3 px-4 text-center">-</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">User Management</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">-</td>
                        <td className="py-3 px-4 text-center">-</td>
                        <td className="py-3 px-4 text-center">-</td>
                      </tr>
                      <tr className="border-b">
                        <td className="py-3 px-4">System Settings</td>
                        <td className="py-3 px-4 text-center">✓</td>
                        <td className="py-3 px-4 text-center">-</td>
                        <td className="py-3 px-4 text-center">-</td>
                        <td className="py-3 px-4 text-center">-</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <Box className="flex justify-end mt-4">
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    className="mr-2"
                  >
                    Edit Roles
                  </CustomButton>
                  <CustomButton
                    variant="contained"
                    color="primary"
                    className="bg-primary-600"
                  >
                    Save Changes
                  </CustomButton>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </TabPanel>

      {/* Appearance Settings */}
      <TabPanel value={tabValue} index={2}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Import and use the ThemeSettings component */}
          <Suspense fallback={<div>Loading theme settings...</div>}>
            <ThemeSettings />
          </Suspense>
        </motion.div>
      </TabPanel>

      {/* Notification Settings */}
      <TabPanel value={tabValue} index={3}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div variants={itemVariants}>
            <Card className="shadow-md mb-6">
              <CardContent>
                <Typography variant="h6" className="font-bold mb-4">
                  Notification Preferences
                </Typography>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <Notifications />
                    </ListItemIcon>
                    <ListItemText
                      primary="Enable Notifications"
                      secondary="Receive system notifications"
                    />
                    <Switch
                      checked={notificationsEnabled}
                      onChange={() => setNotificationsEnabled(!notificationsEnabled)}
                      color="primary"
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                  <ListItem>
                    <ListItemIcon>
                      <Notifications />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email Notifications"
                      secondary="Receive notifications via email"
                    />
                    <Switch
                      checked={emailNotifications}
                      onChange={() => setEmailNotifications(!emailNotifications)}
                      color="primary"
                      disabled={!notificationsEnabled}
                    />
                  </ListItem>
                  <Divider variant="inset" component="li" />
                  <ListItem>
                    <ListItemIcon>
                      <Notifications />
                    </ListItemIcon>
                    <ListItemText
                      primary="SMS Notifications"
                      secondary="Receive notifications via SMS"
                    />
                    <Switch
                      checked={smsNotifications}
                      onChange={() => setSmsNotifications(!smsNotifications)}
                      color="primary"
                      disabled={!notificationsEnabled}
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={itemVariants}>
            <Card className="shadow-md">
              <CardContent>
                <Typography variant="h6" className="font-bold mb-4">
                  Notification Types
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Health Alerts"
                          secondary="Notifications about animal health issues"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Breeding Events"
                          secondary="Notifications about breeding cycles and events"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Feeding Schedules"
                          secondary="Notifications about feeding schedules"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                    </List>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="System Updates"
                          secondary="Notifications about system updates"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Market Alerts"
                          secondary="Notifications about market price changes"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Task Reminders"
                          secondary="Reminders about pending tasks"
                        />
                        <Switch
                          defaultChecked
                          color="primary"
                          disabled={!notificationsEnabled}
                        />
                      </ListItem>
                    </List>
                  </Grid>
                </Grid>

                <Box className="flex justify-end mt-4">
                  <CustomButton
                    variant="contained"
                    color="primary"
                    className="bg-primary-600"
                  >
                    Save Notification Settings
                  </CustomButton>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </TabPanel>

      {/* System Settings */}
      <TabPanel value={tabValue} index={4}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      Data Management
                    </Typography>

                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <Storage />
                        </ListItemIcon>
                        <ListItemText
                          primary="Automatic Backups"
                          secondary="Schedule regular system backups"
                        />
                        <Switch
                          checked={autoBackup}
                          onChange={() => setAutoBackup(!autoBackup)}
                          color="primary"
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                      <ListItem>
                        <ListItemIcon>
                          <CloudSync />
                        </ListItemIcon>
                        <ListItemText
                          primary="Cloud Synchronization"
                          secondary="Sync data with cloud storage"
                        />
                        <Switch
                          checked={syncEnabled}
                          onChange={() => setSyncEnabled(!syncEnabled)}
                          color="primary"
                        />
                      </ListItem>
                      <Divider variant="inset" component="li" />
                      <ListItem>
                        <ListItemIcon>
                          <Storage />
                        </ListItemIcon>
                        <FormControl fullWidth>
                          <InputLabel>Backup Frequency</InputLabel>
                          <Select
                            label="Backup Frequency"
                            defaultValue="daily"
                            disabled={!autoBackup}
                          >
                            <MenuItem value="hourly">Hourly</MenuItem>
                            <MenuItem value="daily">Daily</MenuItem>
                            <MenuItem value="weekly">Weekly</MenuItem>
                            <MenuItem value="monthly">Monthly</MenuItem>
                          </Select>
                        </FormControl>
                      </ListItem>
                    </List>

                    <Box className="flex justify-between mt-4">
                      <CustomButton
                        variant="outlined"
                        color="primary"
                      >
                        Backup Now
                      </CustomButton>
                      <CustomButton
                        variant="contained"
                        color="primary"
                        className="bg-primary-600"
                      >
                        Save Settings
                      </CustomButton>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <Card className="shadow-md">
                  <CardContent>
                    <Typography variant="h6" className="font-bold mb-4">
                      System Information
                    </Typography>

                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Version"
                          secondary="AMPD Livestock Management v2.5.3"
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Last Update"
                          secondary="March 1, 2025"
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Database Size"
                          secondary="1.2 GB"
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="Storage Usage"
                          secondary="45% (4.5 GB of 10 GB)"
                        />
                      </ListItem>
                      <Divider component="li" />
                      <ListItem>
                        <ListItemText
                          primary="License"
                          secondary="Premium (Expires: December 31, 2025)"
                        />
                      </ListItem>
                    </List>

                    <Box className="flex justify-between mt-4">
                      <CustomButton
                        variant="outlined"
                        color="primary"
                      >
                        Check for Updates
                      </CustomButton>
                      <CustomButton
                        variant="outlined"
                        color="secondary"
                      >
                        System Diagnostics
                      </CustomButton>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>
      </TabPanel>

      {/* User Dialog */}
      <Dialog open={openUserDialog} onClose={handleCloseUserDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <div className="space-y-4 py-4">
            <TextField
              label="Full Name"
              fullWidth
              defaultValue={selectedUser?.name || ''}
              margin="normal"
            />
            <TextField
              label="Email"
              fullWidth
              defaultValue={selectedUser?.email || ''}
              margin="normal"
            />
            <TextField
              label="Phone Number"
              fullWidth
              defaultValue={selectedUser?.phone || ''}
              margin="normal"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel>Role</InputLabel>
              <Select
                label="Role"
                defaultValue={selectedUser?.role || 'Field Worker'}
              >
                <MenuItem value="Administrator">Administrator</MenuItem>
                <MenuItem value="Manager">Manager</MenuItem>
                <MenuItem value="Veterinarian">Veterinarian</MenuItem>
                <MenuItem value="Field Worker">Field Worker</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth margin="normal">
              <InputLabel>Status</InputLabel>
              <Select
                label="Status"
                defaultValue={selectedUser?.status || 'Active'}
              >
                <MenuItem value="Active">Active</MenuItem>
                <MenuItem value="Inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
            {!selectedUser && (
              <>
                <TextField
                  label="Password"
                  type="password"
                  fullWidth
                  margin="normal"
                />
                <TextField
                  label="Confirm Password"
                  type="password"
                  fullWidth
                  margin="normal"
                />
              </>
            )}
          </div>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseUserDialog} color="primary">
            Cancel
          </CustomButton>
          <CustomButton onClick={handleSaveUser} color="primary" variant="contained">
            {selectedUser ? 'Update User' : 'Add User'}
          </CustomButton>
        </DialogActions>
      </Dialog>
      {/* Database Settings */}
      <TabPanel value={tabValue} index={5}>
        <iframe
          src="/settings/database"
          style={{ width: '100%', height: '800px', border: 'none' }}
          title="Database Settings"
        />
      </TabPanel>
    </div>
  );
};

export default Settings;
