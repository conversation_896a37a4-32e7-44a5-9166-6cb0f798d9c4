/**
 * Notification Service for AMPD Livestock
 * Handles SMS, voice calls, and other notification methods
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');
const execPromise = util.promisify(exec);

// Configuration
const config = {
  // Gammu configuration
  gammu: {
    enabled: true,
    configPath: path.join(__dirname, '../config/gammurc'),
    device: '/dev/ttyUSB0', // Update based on your GSM modem
    connection: 'at115200'
  },
  // Voice call configuration
  voice: {
    enabled: true,
    ttsEngine: 'pyttsx3', // 'pyttsx3' or 'espeak'
    audioDir: path.join(__dirname, '../audio')
  },
  // WhatsApp configuration
  whatsapp: {
    enabled: false,
    apiKey: process.env.WHATSAPP_API_KEY || ''
  },
  // Email configuration
  email: {
    enabled: false,
    smtpServer: process.env.SMTP_SERVER || '',
    smtpPort: process.env.SMTP_PORT || 587,
    smtpUser: process.env.SMTP_USER || '',
    smtpPassword: process.env.SMTP_PASSWORD || '',
    fromEmail: process.env.FROM_EMAIL || '<EMAIL>'
  }
};

// Ensure audio directory exists
if (!fs.existsSync(config.voice.audioDir)) {
  fs.mkdirSync(config.voice.audioDir, { recursive: true });
}

/**
 * Notification Service
 */
const notificationService = {
  /**
   * Initialize the notification service
   */
  init: async () => {
    try {
      // Check if Gammu is installed and configured
      if (config.gammu.enabled) {
        try {
          const { stdout } = await execPromise('gammu --version');
          console.log('Gammu detected:', stdout.trim());

          // Create Gammu config file if it doesn't exist
          if (!fs.existsSync(config.gammu.configPath)) {
            const gammuConfig = `[gammu]
device = ${config.gammu.device}
connection = ${config.gammu.connection}
`;
            fs.writeFileSync(config.gammu.configPath, gammuConfig);
            console.log('Created Gammu configuration file at', config.gammu.configPath);
          }

          // Test Gammu configuration
          try {
            const { stdout: identifyOutput } = await execPromise(`gammu --config "${config.gammu.configPath}" --identify`);
            console.log('Gammu device identified:', identifyOutput.trim());
          } catch (error) {
            console.warn('Gammu device identification failed:', error.message);
            console.warn('SMS functionality may not work correctly');
          }
        } catch (error) {
          console.warn('Gammu not detected:', error.message);
          console.warn('SMS functionality will be simulated');
          config.gammu.enabled = false;
        }
      }

      // Check if text-to-speech is available
      if (config.voice.enabled) {
        try {
          if (config.voice.ttsEngine === 'pyttsx3') {
            await execPromise('python -c "import pyttsx3"');
            console.log('pyttsx3 detected for text-to-speech');
          } else if (config.voice.ttsEngine === 'espeak') {
            await execPromise('espeak --version');
            console.log('espeak detected for text-to-speech');
          }
        } catch (error) {
          console.warn('Text-to-speech engine not detected:', error.message);
          console.warn('Voice call functionality will be simulated');
          config.voice.enabled = false;
        }
      }

      console.log('Notification service initialized');
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  },

  /**
   * Send an SMS message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  sendSMS: async (to, message, priority = 'medium') => {
    console.log(`Sending SMS to ${to} with priority ${priority}:`, message);

    try {
      if (config.gammu.enabled) {
        // Use Gammu to send SMS
        const command = `gammu --config "${config.gammu.configPath}" sendsms TEXT ${to} -text "${message.replace(/"/g, '\\"')}"`;
        const { stdout, stderr } = await execPromise(command);

        if (stderr) {
          console.error('Error sending SMS:', stderr);
          return {
            success: false,
            error: stderr
          };
        }

        console.log('SMS sent successfully:', stdout);
        return {
          success: true,
          messageId: `sms-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          timestamp: new Date().toISOString()
        };
      } else {
        // Simulate sending SMS
        console.log('Simulating SMS send (Gammu not enabled)');

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return {
          success: true,
          messageId: `sms-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
          timestamp: new Date().toISOString(),
          simulated: true
        };
      }
    } catch (error) {
      console.error('Error sending SMS:', error);
      return {
        success: false,
        error: error.message || 'Unknown error sending SMS'
      };
    }
  },

  /**
   * Generate audio file from text
   * @param {string} text - Text to convert to speech
   * @param {string} filename - Output filename (without extension)
   * @returns {Promise<string>} - Path to the generated audio file
   */
  generateAudio: async (text, filename) => {
    try {
      const outputPath = path.join(config.voice.audioDir, `${filename}.wav`);
      const mp3Path = path.join(config.voice.audioDir, `${filename}.mp3`);

      if (config.voice.enabled) {
        if (config.voice.ttsEngine === 'pyttsx3') {
          // Use pyttsx3 (Python TTS)
          // Fix Windows path escaping issue by using raw strings and forward slashes
          const fixedOutputPath = outputPath.replace(/\\/g, '/');
          const pythonScript = `
import pyttsx3
engine = pyttsx3.init()
engine.setProperty('rate', 150)
engine.save_to_file("""${text}""", r"${fixedOutputPath}")
engine.runAndWait()
`;
          const tempScriptPath = path.join(config.voice.audioDir, `${filename}.py`);
          fs.writeFileSync(tempScriptPath, pythonScript);

          await execPromise(`python "${tempScriptPath}"`);

          // Clean up temporary script
          fs.unlinkSync(tempScriptPath);

          // Convert to MP3 if ffmpeg is available
          try {
            await execPromise(`ffmpeg -y -i "${outputPath}" "${mp3Path}"`);
            console.log('Converted audio to MP3:', mp3Path);
            return mp3Path;
          } catch (error) {
            console.warn('Could not convert to MP3:', error.message);
            return outputPath;
          }
        } else if (config.voice.ttsEngine === 'espeak') {
          // Use espeak
          await execPromise(`espeak -w "${outputPath}" "${text}"`);

          // Convert to MP3 if ffmpeg is available
          try {
            await execPromise(`ffmpeg -y -i "${outputPath}" "${mp3Path}"`);
            console.log('Converted audio to MP3:', mp3Path);
            return mp3Path;
          } catch (error) {
            console.warn('Could not convert to MP3:', error.message);
            return outputPath;
          }
        }
      }

      // If TTS is not enabled or fails, create a dummy file
      fs.writeFileSync(outputPath, 'Dummy audio file');
      console.log('Created dummy audio file (TTS not enabled)');
      return outputPath;
    } catch (error) {
      console.error('Error generating audio:', error);
      throw error;
    }
  },

  /**
   * Make a voice call
   * @param {string} to - Recipient phone number
   * @param {string} message - Message to speak
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  makeVoiceCall: async (to, message, priority = 'high') => {
    console.log(`Making voice call to ${to} with priority ${priority}:`, message);

    try {
      // Generate audio file from text
      const filename = `call-${Date.now()}`;
      const audioPath = await notificationService.generateAudio(message, filename);

      // In a real implementation, this would use Asterisk or similar to make the call
      // For now, we'll simulate a successful call
      console.log(`Would play audio file ${audioPath} to ${to} in a real implementation`);

      // Simulate call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      return {
        success: true,
        callId: `call-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        audioPath,
        simulated: true
      };
    } catch (error) {
      console.error('Error making voice call:', error);
      return {
        success: false,
        error: error.message || 'Unknown error making voice call'
      };
    }
  },

  /**
   * Send a WhatsApp message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  sendWhatsApp: async (to, message, priority = 'medium') => {
    console.log(`Sending WhatsApp message to ${to} with priority ${priority}:`, message);

    try {
      if (config.whatsapp.enabled && config.whatsapp.apiKey) {
        // In a real implementation, this would use the WhatsApp Business API
        // For now, we'll simulate a successful message
        console.log('WhatsApp API not implemented yet');
      }

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        messageId: `whatsapp-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      return {
        success: false,
        error: error.message || 'Unknown error sending WhatsApp message'
      };
    }
  },

  /**
   * Send an email
   * @param {string} to - Recipient email address
   * @param {string} subject - Email subject
   * @param {string} message - Email body
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  sendEmail: async (to, subject, message, priority = 'low') => {
    console.log(`Sending email to ${to} with priority ${priority}:`, subject, message);

    try {
      if (config.email.enabled) {
        // In a real implementation, this would use nodemailer or similar
        // For now, we'll simulate a successful email
        console.log('Email sending not implemented yet');
      }

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        messageId: `email-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    } catch (error) {
      console.error('Error sending email:', error);
      return {
        success: false,
        error: error.message || 'Unknown error sending email'
      };
    }
  },

  /**
   * Send a birth prediction alert using multiple notification methods
   * @param {object} params - Alert parameters
   * @returns {Promise<Array>} - Results for each notification type
   */
  sendBirthPredictionAlert: async ({
    animalId,
    animalName,
    daysRemaining,
    recipient,
    notificationTypes,
    customMessage,
    priority
  }) => {
    // Determine priority based on days remaining if not specified
    if (!priority) {
      if (daysRemaining <= 2) {
        priority = 'critical';
      } else if (daysRemaining <= 7) {
        priority = 'high';
      } else if (daysRemaining <= 14) {
        priority = 'medium';
      } else {
        priority = 'low';
      }
    }

    // Create the base message
    const baseMessage = customMessage ||
      `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days.`;

    // Add urgency indicator based on priority
    let message = baseMessage;
    if (priority === 'critical') {
      message = `URGENT: ${baseMessage} IMMEDIATE ATTENTION REQUIRED.`;
    } else if (priority === 'high') {
      message = `IMPORTANT: ${baseMessage} Please prepare accordingly.`;
    }

    // Create email subject
    const subject = `${priority === 'critical' ? 'URGENT: ' : ''}Birth Alert for ${animalName} - ${daysRemaining} days remaining`;

    // Send notifications using each specified method
    const results = [];

    for (const type of notificationTypes) {
      let result;

      switch (type) {
        case 'sms':
          if (recipient.phone) {
            result = await notificationService.sendSMS(recipient.phone, message, priority);
            results.push({ type, result });
          }
          break;
        case 'voice':
          if (recipient.phone) {
            result = await notificationService.makeVoiceCall(recipient.phone, message, priority);
            results.push({ type, result });
          }
          break;
        case 'whatsapp':
          if (recipient.phone) {
            result = await notificationService.sendWhatsApp(recipient.phone, message, priority);
            results.push({ type, result });
          }
          break;
        case 'email':
          if (recipient.email) {
            result = await notificationService.sendEmail(recipient.email, subject, message, priority);
            results.push({ type, result });
          }
          break;
      }
    }

    return results;
  }
};

// Initialize the notification service
notificationService.init();

module.exports = notificationService;
