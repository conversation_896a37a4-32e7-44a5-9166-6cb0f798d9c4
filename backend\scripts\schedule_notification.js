/**
 * Schedule a notification for a specific time
 * This script schedules a birth alert notification to be sent at a specified time
 */

const notificationService = require('../services/notificationService');

/**
 * Schedule a birth alert notification
 * @param {object} options - Notification options
 * @returns {Promise<void>}
 */
async function scheduleNotification(options) {
  const {
    animalId,
    animalName,
    birthDateTime,
    notificationTime,
    recipient,
    notificationTypes
  } = options;

  // Calculate time until notification should be sent
  const now = new Date();
  const scheduledTime = new Date(notificationTime);
  const timeUntilNotification = scheduledTime.getTime() - now.getTime();

  // Calculate days remaining until birth
  const birthTime = new Date(birthDateTime);
  const hoursUntilBirth = (birthTime.getTime() - scheduledTime.getTime()) / (1000 * 60 * 60);
  const daysRemaining = Math.ceil(hoursUntilBirth / 24);

  console.log(`Scheduling notification for ${animalName} (ID: ${animalId})`);
  console.log(`Birth date/time: ${birthTime.toLocaleString()}`);
  console.log(`Notification time: ${scheduledTime.toLocaleString()}`);
  console.log(`Time until notification: ${Math.floor(timeUntilNotification / 1000 / 60)} minutes`);
  console.log(`Days remaining until birth at notification time: ${daysRemaining}`);
  console.log(`Notification types: ${notificationTypes.join(', ')}`);
  console.log(`Recipient: ${recipient.name} (${recipient.phone})`);

  // If notification time is in the past, send immediately
  if (timeUntilNotification <= 0) {
    console.log('Notification time is in the past, sending immediately');
    await sendNotification({
      animalId,
      animalName,
      daysRemaining,
      recipient,
      notificationTypes
    });
    return;
  }

  // Schedule the notification
  console.log(`Waiting ${Math.floor(timeUntilNotification / 1000 / 60)} minutes to send notification...`);

  // Set a timeout to send the notification at the specified time
  setTimeout(async () => {
    console.log(`It's time! Sending notification for ${animalName} now...`);
    await sendNotification({
      animalId,
      animalName,
      daysRemaining,
      recipient,
      notificationTypes
    });
  }, timeUntilNotification);

  // Keep the script running until the notification is sent
  console.log('Notification scheduled. Keep this script running until the notification is sent.');
  console.log(`Scheduled for: ${scheduledTime.toLocaleTimeString()}`);
}

/**
 * Send the notification
 * @param {object} options - Notification options
 * @returns {Promise<void>}
 */
async function sendNotification(options) {
  const {
    animalId,
    animalName,
    daysRemaining,
    recipient,
    notificationTypes
  } = options;

  try {
    // Determine priority based on days remaining
    let priority = 'low';
    if (daysRemaining <= 1) {
      priority = 'critical';
    } else if (daysRemaining <= 2) {
      priority = 'high';
    } else if (daysRemaining <= 7) {
      priority = 'medium';
    }

    // Send the notification
    const results = await notificationService.sendBirthPredictionAlert({
      animalId,
      animalName,
      daysRemaining,
      recipient,
      notificationTypes,
      priority
    });

    // Log the results
    console.log('Notification results:');
    results.forEach(result => {
      console.log(`- ${result.type}: ${result.result.success ? 'Success' : 'Failed'}`);
      if (!result.result.success) {
        console.log(`  Error: ${result.result.error}`);
      }
    });

    const successCount = results.filter(r => r.result.success).length;
    if (successCount === results.length) {
      console.log(`All ${successCount} notifications sent successfully`);
    } else if (successCount > 0) {
      console.log(`${successCount}/${results.length} notifications sent successfully`);
    } else {
      console.log('All notifications failed');
    }
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}

/**
 * Parse a time string in HH:MM format
 * @param {string} timeStr - Time string in HH:MM format
 * @returns {Date} - Date object with the specified time
 */
function parseTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  const date = new Date();
  date.setHours(hours, minutes, 0, 0);
  return date;
}

/**
 * Fix Windows path issues in Python scripts
 * @param {string} path - Windows path
 * @returns {string} - Fixed path
 */
function fixWindowsPath(path) {
  return path.replace(/\\/g, '/');
}

/**
 * Main function
 */
async function main() {
  // Get command line arguments
  const args = process.argv.slice(2);

  // Default values
  const defaults = {
    animalId: 'CTL001',
    animalName: 'Bella',
    birthDateTime: new Date(new Date().getTime() + 24 * 60 * 60 * 1000).setHours(6, 0, 0, 0), // Tomorrow at 6:00 AM
    notificationTime: parseTime('17:00'), // Today at 17:00
    recipientName: 'Farm Manager',
    recipientPhone: '0794484159',
    notificationTypes: ['sms', 'voice', 'whatsapp']
  };

  // Parse command line arguments
  let options = { ...defaults };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--animal-id' && args[i + 1]) {
      options.animalId = args[i + 1];
      i++;
    } else if (arg === '--animal-name' && args[i + 1]) {
      options.animalName = args[i + 1];
      i++;
    } else if (arg === '--birth-time' && args[i + 1]) {
      options.birthDateTime = parseTime(args[i + 1]);
      i++;
    } else if (arg === '--notification-time' && args[i + 1]) {
      options.notificationTime = parseTime(args[i + 1]);
      i++;
    } else if (arg === '--recipient-name' && args[i + 1]) {
      options.recipientName = args[i + 1];
      i++;
    } else if (arg === '--recipient-phone' && args[i + 1]) {
      options.recipientPhone = args[i + 1];
      i++;
    } else if (arg === '--notification-types' && args[i + 1]) {
      options.notificationTypes = args[i + 1].split(',');
      i++;
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
Usage: node schedule_notification.js [options]

Options:
  --animal-id ID             Animal ID (default: ${defaults.animalId})
  --animal-name NAME         Animal name (default: ${defaults.animalName})
  --birth-time HH:MM         Birth time (default: 06:00 tomorrow)
  --notification-time HH:MM  Notification time (default: 17:00 today)
  --recipient-name NAME      Recipient name (default: ${defaults.recipientName})
  --recipient-phone PHONE    Recipient phone number (default: ${defaults.recipientPhone})
  --notification-types TYPES Comma-separated list of notification types (default: ${defaults.notificationTypes.join(',')})
  --help, -h                 Show this help message
      `);
      process.exit(0);
    }
  }

  // Schedule the notification
  await scheduleNotification({
    animalId: options.animalId,
    animalName: options.animalName,
    birthDateTime: options.birthDateTime,
    notificationTime: options.notificationTime,
    recipient: {
      name: options.recipientName,
      phone: options.recipientPhone
    },
    notificationTypes: options.notificationTypes
  });
}

// Run the main function
main().catch(console.error);
