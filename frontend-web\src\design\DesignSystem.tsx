import React from 'react';
import { alpha, useTheme } from '@mui/material/styles';
import { Box, Typography, Card, CardContent, IconButton, Chip, Avatar, LinearProgress } from '@mui/material';
import { motion } from 'framer-motion';

// Animation presets
export const animations = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.5 }
  },
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  },
  slideRight: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5 }
  },
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5 }
  },
  staggerChildren: {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  },
  pulse: {
    animate: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse'
      }
    }
  },
  float: {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        repeatType: 'reverse'
      }
    }
  }
};

// Card styles with theme awareness
export const cardStyles = (theme: any, isActive = false) => ({
  borderRadius: 2,
  boxShadow: isActive 
    ? `0 8px 32px ${alpha(theme.palette.primary.main, 0.15)}`
    : '0 4px 20px rgba(0,0,0,0.08)',
  border: isActive
    ? `1px solid ${alpha(theme.palette.primary.main, 0.3)}`
    : `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    boxShadow: `0 12px 28px ${alpha(theme.palette.primary.main, 0.12)}`,
    transform: 'translateY(-5px)'
  }
});

// Button styles with theme awareness
export const buttonStyles = (theme: any, variant = 'primary') => {
  const styles = {
    primary: {
      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
      color: theme.palette.primary.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
      }
    },
    secondary: {
      background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
      color: theme.palette.secondary.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.secondary.dark}, ${theme.palette.secondary.main})`,
      }
    },
    success: {
      background: `linear-gradient(135deg, ${theme.palette.success.main}, ${theme.palette.success.dark})`,
      color: theme.palette.success.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.success.dark}, ${theme.palette.success.main})`,
      }
    },
    error: {
      background: `linear-gradient(135deg, ${theme.palette.error.main}, ${theme.palette.error.dark})`,
      color: theme.palette.error.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.error.dark}, ${theme.palette.error.main})`,
      }
    },
    warning: {
      background: `linear-gradient(135deg, ${theme.palette.warning.main}, ${theme.palette.warning.dark})`,
      color: theme.palette.warning.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.warning.dark}, ${theme.palette.warning.main})`,
      }
    },
    info: {
      background: `linear-gradient(135deg, ${theme.palette.info.main}, ${theme.palette.info.dark})`,
      color: theme.palette.info.contrastText,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.info.dark}, ${theme.palette.info.main})`,
      }
    },
    glass: {
      background: alpha(theme.palette.background.paper, 0.7),
      backdropFilter: 'blur(10px)',
      color: theme.palette.text.primary,
      border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      '&:hover': {
        background: alpha(theme.palette.background.paper, 0.9),
      }
    }
  };
  
  return {
    borderRadius: 2,
    boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
    textTransform: 'none',
    fontWeight: 600,
    padding: '10px 24px',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: '-100%',
      width: '100%',
      height: '100%',
      background: `linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)`,
      transition: 'all 0.5s ease',
    },
    '&:hover': {
      boxShadow: '0 6px 16px rgba(0,0,0,0.2)',
      '&::before': {
        left: '100%',
      }
    },
    ...styles[variant]
  };
};

// Advanced data visualization components
interface DataCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: string;
  [key: string]: any;
}

export const DataCard: React.FC<DataCardProps> = ({ title, value, icon, trend, color, ...props }) => {
  const theme = useTheme();
  const cardColor = color || theme.palette.primary.main;
  
  return (
    <Card 
      component={motion.div}
      whileHover={{ y: -5 }}
      sx={{
        borderRadius: 2,
        boxShadow: `0 8px 32px ${alpha(cardColor, 0.15)}`,
        border: `1px solid ${alpha(cardColor, 0.1)}`,
        overflow: 'visible',
        height: '100%',
        ...props.sx
      }}
    >
      <CardContent sx={{ p: 3, position: 'relative' }}>
        <Box sx={{ 
          position: 'absolute', 
          top: -20, 
          left: 20, 
          width: 56, 
          height: 56, 
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: `linear-gradient(135deg, ${cardColor}, ${alpha(cardColor, 0.7)})`,
          color: '#fff',
          boxShadow: `0 4px 20px ${alpha(cardColor, 0.3)}`
        }}>
          {icon}
        </Box>
        
        <Box sx={{ pt: 3, pl: 1 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {title}
          </Typography>
          
          <Typography variant="h4" component="div" fontWeight="bold" sx={{ mb: 1 }}>
            {value}
          </Typography>
          
          {trend && (
            <Chip
              size="small"
              label={`${trend.isPositive ? '+' : ''}${trend.value}%`}
              sx={{
                bgcolor: trend.isPositive ? alpha(theme.palette.success.main, 0.1) : alpha(theme.palette.error.main, 0.1),
                color: trend.isPositive ? theme.palette.success.main : theme.palette.error.main,
                fontWeight: 'bold',
                borderRadius: 1
              }}
            />
          )}
        </Box>
      </CardContent>
    </Card>
  );
};

interface ProgressIndicatorProps {
  value: number;
  max: number;
  label: string;
  color?: string;
  size?: 'small' | 'medium' | 'large';
  [key: string]: any;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({ 
  value, 
  max, 
  label, 
  color,
  size = 'medium',
  ...props 
}) => {
  const theme = useTheme();
  const progressColor = color || theme.palette.primary.main;
  const percentage = Math.round((value / max) * 100);
  
  const sizeMap = {
    small: {
      height: 4,
      labelSize: 'body2',
      valueSize: 'body1'
    },
    medium: {
      height: 6,
      labelSize: 'body1',
      valueSize: 'h6'
    },
    large: {
      height: 8,
      labelSize: 'h6',
      valueSize: 'h4'
    }
  };
  
  return (
    <Box sx={{ width: '100%', ...props.sx }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <Typography variant={sizeMap[size].labelSize as any} color="text.secondary">
          {label}
        </Typography>
        <Typography variant={sizeMap[size].valueSize as any} fontWeight="bold">
          {value} / {max}
        </Typography>
      </Box>
      
      <LinearProgress
        variant="determinate"
        value={percentage}
        sx={{
          height: sizeMap[size].height,
          borderRadius: sizeMap[size].height / 2,
          bgcolor: alpha(progressColor, 0.1),
          '& .MuiLinearProgress-bar': {
            background: `linear-gradient(90deg, ${progressColor}, ${alpha(progressColor, 0.7)})`,
            borderRadius: sizeMap[size].height / 2
          }
        }}
      />
      
      <Typography variant="body2" color="text.secondary" align="right" sx={{ mt: 0.5 }}>
        {percentage}%
      </Typography>
    </Box>
  );
};

interface StatusChipProps {
  label: string;
  status: 'success' | 'warning' | 'error' | 'info' | 'default';
  icon?: React.ReactNode;
  [key: string]: any;
}

export const StatusChip: React.FC<StatusChipProps> = ({ label, status, icon, ...props }) => {
  const theme = useTheme();
  
  const statusColors = {
    success: {
      bg: alpha(theme.palette.success.main, 0.1),
      color: theme.palette.success.main,
      border: alpha(theme.palette.success.main, 0.3)
    },
    warning: {
      bg: alpha(theme.palette.warning.main, 0.1),
      color: theme.palette.warning.main,
      border: alpha(theme.palette.warning.main, 0.3)
    },
    error: {
      bg: alpha(theme.palette.error.main, 0.1),
      color: theme.palette.error.main,
      border: alpha(theme.palette.error.main, 0.3)
    },
    info: {
      bg: alpha(theme.palette.info.main, 0.1),
      color: theme.palette.info.main,
      border: alpha(theme.palette.info.main, 0.3)
    },
    default: {
      bg: alpha(theme.palette.grey[500], 0.1),
      color: theme.palette.grey[700],
      border: alpha(theme.palette.grey[500], 0.3)
    }
  };
  
  return (
    <Chip
      icon={icon}
      label={label}
      sx={{
        bgcolor: statusColors[status].bg,
        color: statusColors[status].color,
        border: `1px solid ${statusColors[status].border}`,
        fontWeight: 600,
        '& .MuiChip-icon': {
          color: statusColors[status].color
        },
        ...props.sx
      }}
      {...props}
    />
  );
};

interface GlassCardProps {
  children: React.ReactNode;
  blur?: number;
  opacity?: number;
  [key: string]: any;
}

export const GlassCard: React.FC<GlassCardProps> = ({ 
  children, 
  blur = 10,
  opacity = 0.7,
  ...props 
}) => {
  const theme = useTheme();
  
  return (
    <Box
      component={motion.div}
      whileHover={{ y: -5 }}
      sx={{
        borderRadius: 2,
        padding: 3,
        background: alpha(theme.palette.background.paper, opacity),
        backdropFilter: `blur(${blur}px)`,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
        transition: 'all 0.3s ease',
        ...props.sx
      }}
    >
      {children}
    </Box>
  );
};

// Export all components and styles
export default {
  animations,
  cardStyles,
  buttonStyles,
  DataCard,
  ProgressIndicator,
  StatusChip,
  GlassCard
};
