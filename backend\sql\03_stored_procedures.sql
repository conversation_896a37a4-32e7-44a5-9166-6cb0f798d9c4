-- AMPD Livestock Management System Stored Procedures
-- SQL Server (SSMS) Stored Procedures

-- Drop existing stored procedures if they exist
IF OBJECT_ID('dbo.sp_GetAnimalById', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetAnimalById;
IF OBJECT_ID('dbo.sp_GetAnimalsByType', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetAnimalsByType;
IF OBJECT_ID('dbo.sp_GetAnimalsByStatus', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetAnimalsByStatus;
IF OBJECT_ID('dbo.sp_GetAnimalsByLocation', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetAnimalsByLocation;
IF OBJECT_ID('dbo.sp_CreateAnimal', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_CreateAnimal;
IF OBJECT_ID('dbo.sp_UpdateAnimal', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_UpdateAnimal;
IF OBJECT_ID('dbo.sp_DeleteAnimal', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_DeleteAnimal;
IF OBJECT_ID('dbo.sp_GetHealthRecordsByAnimal', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetHealthRecordsByAnimal;
IF OBJECT_ID('dbo.sp_CreateHealthRecord', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_CreateHealthRecord;
IF OBJECT_ID('dbo.sp_GetBreedingRecordsByAnimal', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetBreedingRecordsByAnimal;
IF OBJECT_ID('dbo.sp_CreateBreedingRecord', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_CreateBreedingRecord;
IF OBJECT_ID('dbo.sp_GetAnimalStatistics', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetAnimalStatistics;
IF OBJECT_ID('dbo.sp_GetWeightHistory', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetWeightHistory;
IF OBJECT_ID('dbo.sp_GetFeedInventory', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetFeedInventory;
IF OBJECT_ID('dbo.sp_UpdateFeedInventory', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_UpdateFeedInventory;
IF OBJECT_ID('dbo.sp_GetUserByUsername', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_GetUserByUsername;
IF OBJECT_ID('dbo.sp_CreateUser', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_CreateUser;
IF OBJECT_ID('dbo.sp_LogAuditEvent', 'P') IS NOT NULL DROP PROCEDURE dbo.sp_LogAuditEvent;

-- Create stored procedure to get animal by ID
CREATE PROCEDURE dbo.sp_GetAnimalById
    @id INT
AS
BEGIN
    SELECT a.*, 
           l.name as location_name,
           u1.username as created_by_username,
           u2.username as updated_by_username
    FROM dbo.Animals a
    LEFT JOIN dbo.Locations l ON a.location_id = l.id
    LEFT JOIN dbo.Users u1 ON a.created_by = u1.id
    LEFT JOIN dbo.Users u2 ON a.updated_by = u2.id
    WHERE a.id = @id;
END;
GO

-- Create stored procedure to get animals by type
CREATE PROCEDURE dbo.sp_GetAnimalsByType
    @animalType NVARCHAR(50),
    @page INT = 1,
    @pageSize INT = 10
AS
BEGIN
    DECLARE @offset INT = (@page - 1) * @pageSize;
    
    SELECT a.*, 
           l.name as location_name
    FROM dbo.Animals a
    LEFT JOIN dbo.Locations l ON a.location_id = l.id
    WHERE a.animal_type = @animalType
    ORDER BY a.id
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY;
    
    SELECT COUNT(*) as total_count
    FROM dbo.Animals
    WHERE animal_type = @animalType;
END;
GO

-- Create stored procedure to get animals by status
CREATE PROCEDURE dbo.sp_GetAnimalsByStatus
    @status NVARCHAR(50),
    @page INT = 1,
    @pageSize INT = 10
AS
BEGIN
    DECLARE @offset INT = (@page - 1) * @pageSize;
    
    SELECT a.*, 
           l.name as location_name
    FROM dbo.Animals a
    LEFT JOIN dbo.Locations l ON a.location_id = l.id
    WHERE a.status = @status
    ORDER BY a.id
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY;
    
    SELECT COUNT(*) as total_count
    FROM dbo.Animals
    WHERE status = @status;
END;
GO

-- Create stored procedure to get animals by location
CREATE PROCEDURE dbo.sp_GetAnimalsByLocation
    @locationId INT,
    @page INT = 1,
    @pageSize INT = 10
AS
BEGIN
    DECLARE @offset INT = (@page - 1) * @pageSize;
    
    SELECT a.*, 
           l.name as location_name
    FROM dbo.Animals a
    LEFT JOIN dbo.Locations l ON a.location_id = l.id
    WHERE a.location_id = @locationId
    ORDER BY a.id
    OFFSET @offset ROWS
    FETCH NEXT @pageSize ROWS ONLY;
    
    SELECT COUNT(*) as total_count
    FROM dbo.Animals
    WHERE location_id = @locationId;
END;
GO

-- Create stored procedure to create a new animal
CREATE PROCEDURE dbo.sp_CreateAnimal
    @tagNumber NVARCHAR(50),
    @name NVARCHAR(100) = NULL,
    @animalType NVARCHAR(50),
    @breed NVARCHAR(100) = NULL,
    @gender NVARCHAR(20),
    @birthDate DATE = NULL,
    @weight DECIMAL(10, 2) = NULL,
    @status NVARCHAR(50) = 'active',
    @healthStatus NVARCHAR(50) = 'healthy',
    @locationId INT = NULL,
    @parentFemaleId INT = NULL,
    @parentMaleId INT = NULL,
    @purchaseDate DATE = NULL,
    @purchasePrice DECIMAL(10, 2) = NULL,
    @rfidTag NVARCHAR(100) = NULL,
    @notes NVARCHAR(MAX) = NULL,
    @createdBy INT
AS
BEGIN
    -- Check if tag number already exists
    IF EXISTS (SELECT 1 FROM dbo.Animals WHERE tag_number = @tagNumber)
    BEGIN
        RAISERROR('An animal with this tag number already exists.', 16, 1);
        RETURN;
    END
    
    INSERT INTO dbo.Animals (
        tag_number, name, animal_type, breed, gender, birth_date, weight, 
        status, health_status, location_id, parent_female_id, parent_male_id, 
        purchase_date, purchase_price, rfid_tag, notes, created_by
    )
    OUTPUT INSERTED.id, INSERTED.tag_number, INSERTED.animal_type
    VALUES (
        @tagNumber, @name, @animalType, @breed, @gender, @birthDate, @weight,
        @status, @healthStatus, @locationId, @parentFemaleId, @parentMaleId,
        @purchaseDate, @purchasePrice, @rfidTag, @notes, @createdBy
    );
    
    -- Update location count if location is provided
    IF @locationId IS NOT NULL
    BEGIN
        UPDATE dbo.Locations
        SET current_count = current_count + 1
        WHERE id = @locationId;
    END
END;
GO

-- Create stored procedure to update an animal
CREATE PROCEDURE dbo.sp_UpdateAnimal
    @id INT,
    @tagNumber NVARCHAR(50) = NULL,
    @name NVARCHAR(100) = NULL,
    @animalType NVARCHAR(50) = NULL,
    @breed NVARCHAR(100) = NULL,
    @gender NVARCHAR(20) = NULL,
    @birthDate DATE = NULL,
    @weight DECIMAL(10, 2) = NULL,
    @status NVARCHAR(50) = NULL,
    @healthStatus NVARCHAR(50) = NULL,
    @locationId INT = NULL,
    @parentFemaleId INT = NULL,
    @parentMaleId INT = NULL,
    @purchaseDate DATE = NULL,
    @purchasePrice DECIMAL(10, 2) = NULL,
    @rfidTag NVARCHAR(100) = NULL,
    @notes NVARCHAR(MAX) = NULL,
    @updatedBy INT
AS
BEGIN
    -- Check if animal exists
    IF NOT EXISTS (SELECT 1 FROM dbo.Animals WHERE id = @id)
    BEGIN
        RAISERROR('Animal not found.', 16, 1);
        RETURN;
    END
    
    -- Check if tag number is being changed and if new tag number already exists
    IF @tagNumber IS NOT NULL AND @tagNumber <> (SELECT tag_number FROM dbo.Animals WHERE id = @id)
    BEGIN
        IF EXISTS (SELECT 1 FROM dbo.Animals WHERE tag_number = @tagNumber AND id <> @id)
        BEGIN
            RAISERROR('An animal with this tag number already exists.', 16, 1);
            RETURN;
        END
    END
    
    -- Get current location for location count update
    DECLARE @oldLocationId INT;
    SELECT @oldLocationId = location_id FROM dbo.Animals WHERE id = @id;
    
    -- Update animal
    UPDATE dbo.Animals
    SET 
        tag_number = ISNULL(@tagNumber, tag_number),
        name = ISNULL(@name, name),
        animal_type = ISNULL(@animalType, animal_type),
        breed = ISNULL(@breed, breed),
        gender = ISNULL(@gender, gender),
        birth_date = ISNULL(@birthDate, birth_date),
        weight = ISNULL(@weight, weight),
        status = ISNULL(@status, status),
        health_status = ISNULL(@healthStatus, health_status),
        location_id = ISNULL(@locationId, location_id),
        parent_female_id = ISNULL(@parentFemaleId, parent_female_id),
        parent_male_id = ISNULL(@parentMaleId, parent_male_id),
        purchase_date = ISNULL(@purchaseDate, purchase_date),
        purchase_price = ISNULL(@purchasePrice, purchase_price),
        rfid_tag = ISNULL(@rfidTag, rfid_tag),
        notes = ISNULL(@notes, notes),
        updated_by = @updatedBy,
        updated_at = GETDATE()
    OUTPUT 
        INSERTED.id, 
        INSERTED.tag_number, 
        INSERTED.animal_type,
        INSERTED.status,
        INSERTED.location_id
    WHERE id = @id;
    
    -- Update location counts if location is changed
    IF @locationId IS NOT NULL AND @locationId <> @oldLocationId
    BEGIN
        -- Decrement old location count
        IF @oldLocationId IS NOT NULL
        BEGIN
            UPDATE dbo.Locations
            SET current_count = current_count - 1
            WHERE id = @oldLocationId;
        END
        
        -- Increment new location count
        UPDATE dbo.Locations
        SET current_count = current_count + 1
        WHERE id = @locationId;
    END
END;
GO

-- Create stored procedure to delete an animal
CREATE PROCEDURE dbo.sp_DeleteAnimal
    @id INT
AS
BEGIN
    -- Check if animal exists
    IF NOT EXISTS (SELECT 1 FROM dbo.Animals WHERE id = @id)
    BEGIN
        RAISERROR('Animal not found.', 16, 1);
        RETURN;
    END
    
    -- Get location for count update
    DECLARE @locationId INT;
    SELECT @locationId = location_id FROM dbo.Animals WHERE id = @id;
    
    -- Delete animal
    DELETE FROM dbo.Animals
    WHERE id = @id;
    
    -- Update location count
    IF @locationId IS NOT NULL
    BEGIN
        UPDATE dbo.Locations
        SET current_count = current_count - 1
        WHERE id = @locationId;
    END
    
    SELECT 'Animal deleted successfully' as message;
END;
GO

-- Create stored procedure to get health records by animal
CREATE PROCEDURE dbo.sp_GetHealthRecordsByAnimal
    @animalId INT
AS
BEGIN
    SELECT h.*, 
           a.tag_number,
           u1.username as created_by_username
    FROM dbo.HealthRecords h
    JOIN dbo.Animals a ON h.animal_id = a.id
    LEFT JOIN dbo.Users u1 ON h.created_by = u1.id
    WHERE h.animal_id = @animalId
    ORDER BY h.date DESC;
END;
GO

-- Create stored procedure to create a health record
CREATE PROCEDURE dbo.sp_CreateHealthRecord
    @animalId INT,
    @recordType NVARCHAR(50),
    @date DATETIME2,
    @diagnosis NVARCHAR(255) = NULL,
    @treatment NVARCHAR(255) = NULL,
    @medication NVARCHAR(100) = NULL,
    @dosage NVARCHAR(50) = NULL,
    @veterinarian NVARCHAR(100) = NULL,
    @cost DECIMAL(10, 2) = NULL,
    @followUpDate DATE = NULL,
    @status NVARCHAR(50),
    @notes NVARCHAR(MAX) = NULL,
    @createdBy INT
AS
BEGIN
    -- Check if animal exists
    IF NOT EXISTS (SELECT 1 FROM dbo.Animals WHERE id = @animalId)
    BEGIN
        RAISERROR('Animal not found.', 16, 1);
        RETURN;
    END
    
    INSERT INTO dbo.HealthRecords (
        animal_id, record_type, date, diagnosis, treatment, medication, 
        dosage, veterinarian, cost, follow_up_date, status, notes, created_by
    )
    OUTPUT INSERTED.id, INSERTED.animal_id, INSERTED.record_type, INSERTED.date
    VALUES (
        @animalId, @recordType, @date, @diagnosis, @treatment, @medication,
        @dosage, @veterinarian, @cost, @followUpDate, @status, @notes, @createdBy
    );
    
    -- Update animal health status if it's a treatment or vaccination
    IF @recordType IN ('treatment', 'vaccination')
    BEGIN
        UPDATE dbo.Animals
        SET health_status = CASE 
                               WHEN @recordType = 'treatment' AND @status <> 'completed' THEN 'treatment'
                               ELSE health_status
                            END,
            updated_at = GETDATE(),
            updated_by = @createdBy
        WHERE id = @animalId;
    END
END;
GO

-- Create stored procedure to get breeding records by animal
CREATE PROCEDURE dbo.sp_GetBreedingRecordsByAnimal
    @animalId INT
AS
BEGIN
    -- Get records where animal is female
    SELECT b.*, 
           af.tag_number as female_tag,
           am.tag_number as male_tag,
           u1.username as created_by_username
    FROM dbo.BreedingRecords b
    JOIN dbo.Animals af ON b.female_id = af.id
    LEFT JOIN dbo.Animals am ON b.male_id = am.id
    LEFT JOIN dbo.Users u1 ON b.created_by = u1.id
    WHERE b.female_id = @animalId
    
    UNION
    
    -- Get records where animal is male
    SELECT b.*, 
           af.tag_number as female_tag,
           am.tag_number as male_tag,
           u1.username as created_by_username
    FROM dbo.BreedingRecords b
    JOIN dbo.Animals af ON b.female_id = af.id
    JOIN dbo.Animals am ON b.male_id = am.id
    LEFT JOIN dbo.Users u1 ON b.created_by = u1.id
    WHERE b.male_id = @animalId
    
    ORDER BY breeding_date DESC;
END;
GO

-- Create stored procedure to create a breeding record
CREATE PROCEDURE dbo.sp_CreateBreedingRecord
    @femaleId INT,
    @maleId INT = NULL,
    @breedingDate DATE,
    @breedingMethod NVARCHAR(50),
    @aiBatchNumber NVARCHAR(50) = NULL,
    @aiTechnician NVARCHAR(100) = NULL,
    @aiCost DECIMAL(10, 2) = NULL,
    @expectedDueDate DATE = NULL,
    @status NVARCHAR(50),
    @notes NVARCHAR(MAX) = NULL,
    @createdBy INT
AS
BEGIN
    -- Check if female animal exists
    IF NOT EXISTS (SELECT 1 FROM dbo.Animals WHERE id = @femaleId)
    BEGIN
        RAISERROR('Female animal not found.', 16, 1);
        RETURN;
    END
    
    -- Check if male animal exists if provided
    IF @maleId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM dbo.Animals WHERE id = @maleId)
    BEGIN
        RAISERROR('Male animal not found.', 16, 1);
        RETURN;
    END
    
    -- Check if female is actually female
    IF (SELECT gender FROM dbo.Animals WHERE id = @femaleId) <> 'female'
    BEGIN
        RAISERROR('Selected animal is not female.', 16, 1);
        RETURN;
    END
    
    -- Check if male is actually male if provided
    IF @maleId IS NOT NULL AND (SELECT gender FROM dbo.Animals WHERE id = @maleId) <> 'male'
    BEGIN
        RAISERROR('Selected animal is not male.', 16, 1);
        RETURN;
    END
    
    -- Calculate expected due date if not provided (approximately 9 months for cattle)
    IF @expectedDueDate IS NULL
    BEGIN
        SET @expectedDueDate = DATEADD(MONTH, 9, @breedingDate);
    END
    
    INSERT INTO dbo.BreedingRecords (
        female_id, male_id, breeding_date, breeding_method, ai_batch_number,
        ai_technician, ai_cost, expected_due_date, status, notes, created_by
    )
    OUTPUT INSERTED.id, INSERTED.female_id, INSERTED.breeding_date, INSERTED.status
    VALUES (
        @femaleId, @maleId, @breedingDate, @breedingMethod, @aiBatchNumber,
        @aiTechnician, @aiCost, @expectedDueDate, @status, @notes, @createdBy
    );
    
    -- Update female animal status to 'pregnant' if status is 'in-progress'
    IF @status = 'in-progress'
    BEGIN
        UPDATE dbo.Animals
        SET status = 'pregnant',
            updated_at = GETDATE(),
            updated_by = @createdBy
        WHERE id = @femaleId;
    END
END;
GO

-- Create stored procedure to get animal statistics
CREATE PROCEDURE dbo.sp_GetAnimalStatistics
AS
BEGIN
    -- Overall statistics
    SELECT 
        COUNT(*) as total_animals,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_animals,
        SUM(CASE WHEN status = 'pregnant' THEN 1 ELSE 0 END) as pregnant_animals,
        SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold_animals,
        SUM(CASE WHEN status = 'deceased' THEN 1 ELSE 0 END) as deceased_animals,
        SUM(CASE WHEN gender = 'male' THEN 1 ELSE 0 END) as male_animals,
        SUM(CASE WHEN gender = 'female' THEN 1 ELSE 0 END) as female_animals,
        AVG(weight) as average_weight
    FROM dbo.Animals;
    
    -- Statistics by animal type
    SELECT animal_type, COUNT(*) as count
    FROM dbo.Animals
    GROUP BY animal_type
    ORDER BY count DESC;
    
    -- Statistics by location
    SELECT l.name as location, COUNT(a.id) as count
    FROM dbo.Locations l
    LEFT JOIN dbo.Animals a ON l.id = a.location_id
    GROUP BY l.name
    ORDER BY count DESC;
    
    -- Health statistics
    SELECT health_status, COUNT(*) as count
    FROM dbo.Animals
    GROUP BY health_status
    ORDER BY count DESC;
END;
GO

-- Create stored procedure to get weight history for an animal
CREATE PROCEDURE dbo.sp_GetWeightHistory
    @animalId INT
AS
BEGIN
    -- Get initial weight from animal record
    SELECT 
        0 as record_id,
        id as animal_id,
        weight,
        birth_date as date,
        'Initial weight' as notes
    FROM dbo.Animals
    WHERE id = @animalId AND weight IS NOT NULL AND birth_date IS NOT NULL
    
    UNION
    
    -- Get weight records
    SELECT 
        id as record_id,
        animal_id,
        weight,
        date,
        notes
    FROM dbo.WeightRecords
    WHERE animal_id = @animalId
    
    ORDER BY date;
END;
GO

-- Create stored procedure to get feed inventory
CREATE PROCEDURE dbo.sp_GetFeedInventory
AS
BEGIN
    SELECT f.*, 
           u1.username as created_by_username,
           u2.username as updated_by_username
    FROM dbo.FeedInventory f
    LEFT JOIN dbo.Users u1 ON f.created_by = u1.id
    LEFT JOIN dbo.Users u2 ON f.updated_by = u2.id
    ORDER BY f.name;
END;
GO

-- Create stored procedure to update feed inventory
CREATE PROCEDURE dbo.sp_UpdateFeedInventory
    @id INT,
    @quantity DECIMAL(10, 2),
    @updatedBy INT
AS
BEGIN
    -- Check if feed inventory exists
    IF NOT EXISTS (SELECT 1 FROM dbo.FeedInventory WHERE id = @id)
    BEGIN
        RAISERROR('Feed inventory item not found.', 16, 1);
        RETURN;
    END
    
    UPDATE dbo.FeedInventory
    SET quantity = @quantity,
        updated_by = @updatedBy,
        updated_at = GETDATE()
    OUTPUT 
        INSERTED.id, 
        INSERTED.name, 
        INSERTED.quantity,
        INSERTED.unit
    WHERE id = @id;
END;
GO

-- Create stored procedure to get user by username
CREATE PROCEDURE dbo.sp_GetUserByUsername
    @username NVARCHAR(50)
AS
BEGIN
    SELECT u.*, r.name as role_name, r.permissions
    FROM dbo.Users u
    JOIN dbo.UserRoles r ON u.role_id = r.id
    WHERE u.username = @username;
END;
GO

-- Create stored procedure to create a user
CREATE PROCEDURE dbo.sp_CreateUser
    @username NVARCHAR(50),
    @email NVARCHAR(100),
    @password NVARCHAR(255),
    @firstName NVARCHAR(50),
    @lastName NVARCHAR(50),
    @roleId INT
AS
BEGIN
    -- Check if username already exists
    IF EXISTS (SELECT 1 FROM dbo.Users WHERE username = @username)
    BEGIN
        RAISERROR('Username already exists.', 16, 1);
        RETURN;
    END
    
    -- Check if email already exists
    IF EXISTS (SELECT 1 FROM dbo.Users WHERE email = @email)
    BEGIN
        RAISERROR('Email already exists.', 16, 1);
        RETURN;
    END
    
    -- Check if role exists
    IF NOT EXISTS (SELECT 1 FROM dbo.UserRoles WHERE id = @roleId)
    BEGIN
        RAISERROR('Role not found.', 16, 1);
        RETURN;
    END
    
    INSERT INTO dbo.Users (
        username, email, password, first_name, last_name, role_id
    )
    OUTPUT 
        INSERTED.id, 
        INSERTED.username, 
        INSERTED.email,
        INSERTED.first_name,
        INSERTED.last_name,
        INSERTED.role_id
    VALUES (
        @username, @email, @password, @firstName, @lastName, @roleId
    );
END;
GO

-- Create stored procedure to log audit events
CREATE PROCEDURE dbo.sp_LogAuditEvent
    @userId INT,
    @action NVARCHAR(50),
    @entityType NVARCHAR(50),
    @entityId INT = NULL,
    @details NVARCHAR(MAX) = NULL,
    @ipAddress NVARCHAR(50) = NULL
AS
BEGIN
    INSERT INTO dbo.AuditLogs (
        user_id, action, entity_type, entity_id, details, ip_address
    )
    VALUES (
        @userId, @action, @entityType, @entityId, @details, @ipAddress
    );
END;
GO