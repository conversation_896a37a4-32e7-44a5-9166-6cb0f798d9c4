/**
 * MongoDB Data Migration Script
 *
 * This script migrates consistent data to MongoDB.
 * It creates properly structured data with relationships between collections.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const { ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  tlsAllowInvalidCertificates: true, // For development only
  tlsAllowInvalidHostnames: true, // For development only
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000, // 30 seconds timeout
  socketTimeoutMS: 45000, // 45 seconds timeout
});

// Create consistent data with Sotho and Tswana names (no Zulu names)
// Animals - exactly 10 records
const animals = [
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2020-001',
    name: 'Tshepiso',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2020-05-15',
    weight: 580,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Veld A',
    purchaseDate: '2020-06-10',
    purchasePrice: 15000,
    notes: 'Tswana name meaning "Promise". Excellent breeding cow.',
    rfidTag: 'RFID00001',
    imageUrl: '/images/animals/cattle-small-1.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2019-002',
    name: 'Tumelo',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Bonsmara',
    gender: 'Male',
    birthDate: '2019-08-22',
    weight: 780,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Kraal 1',
    purchaseDate: '2019-10-05',
    purchasePrice: 22000,
    notes: 'Tswana name meaning "Faith". Strong bull with excellent genetics.',
    rfidTag: 'RFID00002',
    imageUrl: '/images/animals/cattle-small-2.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2021-003',
    name: 'Lerato',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Afrikaner',
    gender: 'Female',
    birthDate: '2021-03-10',
    weight: 450,
    status: 'Active',
    healthStatus: 'pregnant',
    location: 'Veld B',
    purchaseDate: '2021-04-15',
    purchasePrice: 14000,
    notes: 'Sotho name meaning "Love". High quality beef producer.',
    rfidTag: 'RFID00003',
    imageUrl: '/images/animals/cattle-small-3.jpg',
    sireId: '2', // Reference to Tumelo
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2020-004',
    name: 'Thabo',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Drakensberger',
    gender: 'Male',
    birthDate: '2020-11-05',
    weight: 650,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Veld A',
    purchaseDate: '2021-01-20',
    purchasePrice: 18500,
    notes: 'Tswana name meaning "Joy". Consistent growth rate.',
    rfidTag: 'RFID00004',
    imageUrl: '/images/animals/cattle-small-4.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2022-005',
    name: 'Pule',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2022-02-14',
    weight: 320,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Kraal 2',
    notes: 'Sotho name meaning "Rain". Born on farm, daughter of Tshepiso.',
    rfidTag: 'RFID00005',
    imageUrl: '/images/animals/cattle-small-5.jpg',
    parentId: '1',
    parentTagNumber: 'CTL-2020-001',
    sireId: '2',
    damId: '1',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2021-006',
    name: 'Kgosi',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Bonsmara',
    gender: 'Male',
    birthDate: '2021-06-18',
    weight: 520,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Kraal 1',
    purchaseDate: '2021-08-10',
    purchasePrice: 17500,
    notes: 'Tswana name meaning "Chief". Excellent stud potential.',
    rfidTag: 'RFID00006',
    imageUrl: '/images/animals/cattle-small-6.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2020-007',
    name: 'Naledi',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2020-09-12',
    weight: 490,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Veld B',
    purchaseDate: '2020-11-05',
    purchasePrice: 14500,
    notes: 'Sotho/Tswana name meaning "Star". Good milk producer.',
    rfidTag: 'RFID00007',
    imageUrl: '/images/animals/cattle-small-7.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2022-008',
    name: 'Mpho',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Afrikaner',
    gender: 'Female',
    birthDate: '2022-01-30',
    weight: 340,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Kraal 2',
    purchaseDate: '2022-03-15',
    purchasePrice: 12000,
    notes: 'Sotho/Tswana name meaning "Gift". Young heifer with good potential.',
    rfidTag: 'RFID00008',
    imageUrl: '/images/animals/cattle-small-8.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2019-009',
    name: 'Tau',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Bonsmara',
    gender: 'Male',
    birthDate: '2019-05-08',
    weight: 820,
    status: 'Active',
    healthStatus: 'healthy',
    location: 'Veld A',
    purchaseDate: '2019-07-20',
    purchasePrice: 25000,
    notes: 'Sotho/Tswana name meaning "Lion". Mature bull with excellent conformation.',
    rfidTag: 'RFID00009',
    imageUrl: '/images/animals/cattle-small-9.jpg',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    tagNumber: 'CTL-2021-010',
    name: 'Lesedi',
    type: 'Cattle',
    species: 'Cattle',
    breed: 'Nguni',
    gender: 'Female',
    birthDate: '2021-11-25',
    weight: 380,
    status: 'Active',
    healthStatus: 'pregnant',
    location: 'Veld B',
    purchaseDate: '2022-01-10',
    purchasePrice: 13500,
    notes: 'Tswana name meaning "Light". First-time pregnant heifer.',
    rfidTag: 'RFID00010',
    imageUrl: '/images/animals/cattle-small-10.jpg',
    sireId: '9', // Reference to Tau
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Health Records - exactly 10 records
const healthRecords = [
  {
    _id: new ObjectId(),
    animalId: animals[0]._id.toString(),
    animalTagNumber: animals[0].tagNumber,
    animalName: animals[0].name,
    recordType: 'Vaccination',
    description: 'Annual vaccination against Brucellosis',
    date: '2024-05-10',
    performedBy: 'Dr. Johan Smith',
    status: 'completed',
    notes: 'Animal responded well to vaccination',
    followUpDate: '2025-05-10',
    medications: ['Brucellosis vaccine'],
    cost: 350,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[1]._id.toString(),
    animalTagNumber: animals[1].tagNumber,
    animalName: animals[1].name,
    recordType: 'Treatment',
    description: 'Antibiotic treatment for respiratory infection',
    date: '2024-05-12',
    performedBy: 'Dr. Maria Nkosi',
    status: 'completed',
    notes: 'Full course of antibiotics administered',
    followUpDate: '2024-05-19',
    medications: ['Penicillin', 'Anti-inflammatory'],
    cost: 580,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[2]._id.toString(),
    animalTagNumber: animals[2].tagNumber,
    animalName: animals[2].name,
    recordType: 'Examination',
    description: 'Routine health check and pregnancy confirmation',
    date: '2024-05-15',
    performedBy: 'Dr. Johan Smith',
    status: 'completed',
    notes: 'Pregnancy confirmed, approximately 4 months along',
    followUpDate: '2024-06-15',
    medications: [],
    cost: 250,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[3]._id.toString(),
    animalTagNumber: animals[3].tagNumber,
    animalName: animals[3].name,
    recordType: 'Surgery',
    description: 'Minor wound treatment and suturing',
    date: '2024-05-18',
    performedBy: 'Dr. Maria Nkosi',
    status: 'monitoring',
    notes: 'Wound on left flank sutured, monitor for infection',
    followUpDate: '2024-05-25',
    medications: ['Antibiotics', 'Pain medication'],
    cost: 750,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[4]._id.toString(),
    animalTagNumber: animals[4].tagNumber,
    animalName: animals[4].name,
    recordType: 'Vaccination',
    description: 'Booster shot for Anthrax prevention',
    date: '2024-05-20',
    performedBy: 'Dr. Johan Smith',
    status: 'scheduled',
    notes: 'First vaccination in series',
    followUpDate: '2024-06-20',
    medications: ['Anthrax vaccine'],
    cost: 320,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[5]._id.toString(),
    animalTagNumber: animals[5].tagNumber,
    animalName: animals[5].name,
    recordType: 'Deworming',
    description: 'Routine deworming treatment',
    date: '2024-05-22',
    performedBy: 'Dr. Johan Smith',
    status: 'completed',
    notes: 'Standard dose administered orally',
    followUpDate: '2024-08-22',
    medications: ['Ivermectin'],
    cost: 280,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[6]._id.toString(),
    animalTagNumber: animals[6].tagNumber,
    animalName: animals[6].name,
    recordType: 'Hoof Trimming',
    description: 'Routine hoof maintenance',
    date: '2024-05-25',
    performedBy: 'Pieter van Wyk',
    status: 'completed',
    notes: 'All four hooves trimmed, no issues found',
    followUpDate: '2024-08-25',
    medications: [],
    cost: 200,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[7]._id.toString(),
    animalTagNumber: animals[7].tagNumber,
    animalName: animals[7].name,
    recordType: 'Vaccination',
    description: 'Vaccination against Blackleg',
    date: '2024-05-28',
    performedBy: 'Dr. Maria Nkosi',
    status: 'completed',
    notes: 'First vaccination for young heifer',
    followUpDate: '2024-11-28',
    medications: ['Blackleg vaccine'],
    cost: 300,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[8]._id.toString(),
    animalTagNumber: animals[8].tagNumber,
    animalName: animals[8].name,
    recordType: 'Fertility Test',
    description: 'Breeding soundness examination',
    date: '2024-06-01',
    performedBy: 'Dr. Johan Smith',
    status: 'completed',
    notes: 'Excellent fertility, approved for breeding program',
    followUpDate: '2024-12-01',
    medications: [],
    cost: 850,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[9]._id.toString(),
    animalTagNumber: animals[9].tagNumber,
    animalName: animals[9].name,
    recordType: 'Pregnancy Check',
    description: 'Ultrasound pregnancy confirmation',
    date: '2024-06-05',
    performedBy: 'Dr. Maria Nkosi',
    status: 'scheduled',
    notes: 'First pregnancy check for heifer',
    followUpDate: '2024-07-05',
    medications: [],
    cost: 400,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Breeding Records - exactly 10 records
const breedingRecords = [
  {
    _id: new ObjectId(),
    femaleId: animals[0]._id.toString(),
    femaleTagNumber: animals[0].tagNumber,
    femaleName: animals[0].name,
    maleId: animals[1]._id.toString(),
    maleTagNumber: animals[1].tagNumber,
    maleName: animals[1].name,
    date: '2023-05-20',
    status: 'successful',
    expectedDueDate: '2024-02-25',
    notes: 'Natural breeding, successful conception',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[2]._id.toString(),
    femaleTagNumber: animals[2].tagNumber,
    femaleName: animals[2].name,
    maleId: animals[1]._id.toString(),
    maleTagNumber: animals[1].tagNumber,
    maleName: animals[1].name,
    date: '2023-12-15',
    status: 'confirmed',
    expectedDueDate: '2024-09-20',
    notes: 'Artificial insemination, pregnancy confirmed',
    method: 'Artificial Insemination',
    technician: 'Dr. Johan Smith',
    cost: 1200,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[4]._id.toString(),
    femaleTagNumber: animals[4].tagNumber,
    femaleName: animals[4].name,
    maleId: animals[5]._id.toString(),
    maleTagNumber: animals[5].tagNumber,
    maleName: animals[5].name,
    date: '2024-01-10',
    status: 'pending',
    expectedDueDate: '2024-10-15',
    notes: 'Natural breeding, confirmation pending',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[6]._id.toString(),
    femaleTagNumber: animals[6].tagNumber,
    femaleName: animals[6].name,
    maleId: animals[8]._id.toString(),
    maleTagNumber: animals[8].tagNumber,
    maleName: animals[8].name,
    date: '2024-02-05',
    status: 'confirmed',
    expectedDueDate: '2024-11-10',
    notes: 'Natural breeding, pregnancy confirmed via ultrasound',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[7]._id.toString(),
    femaleTagNumber: animals[7].tagNumber,
    femaleName: animals[7].name,
    maleId: animals[1]._id.toString(),
    maleTagNumber: animals[1].tagNumber,
    maleName: animals[1].name,
    date: '2024-03-15',
    status: 'scheduled',
    expectedDueDate: '2024-12-20',
    notes: 'Scheduled for artificial insemination',
    method: 'Artificial Insemination',
    technician: 'Dr. Maria Nkosi',
    cost: 1500,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[9]._id.toString(),
    femaleTagNumber: animals[9].tagNumber,
    femaleName: animals[9].name,
    maleId: animals[8]._id.toString(),
    maleTagNumber: animals[8].tagNumber,
    maleName: animals[8].name,
    date: '2024-01-25',
    status: 'confirmed',
    expectedDueDate: '2024-10-30',
    notes: 'Natural breeding, pregnancy confirmed',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[0]._id.toString(),
    femaleTagNumber: animals[0].tagNumber,
    femaleName: animals[0].name,
    maleId: animals[8]._id.toString(),
    maleTagNumber: animals[8].tagNumber,
    maleName: animals[8].name,
    date: '2024-04-10',
    status: 'scheduled',
    expectedDueDate: '2025-01-15',
    notes: 'Scheduled for next breeding cycle',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[2]._id.toString(),
    femaleTagNumber: animals[2].tagNumber,
    femaleName: animals[2].name,
    maleId: animals[5]._id.toString(),
    maleTagNumber: animals[5].tagNumber,
    maleName: animals[5].name,
    date: '2023-08-05',
    status: 'unsuccessful',
    expectedDueDate: null,
    notes: 'Failed conception, will retry next cycle',
    method: 'Artificial Insemination',
    technician: 'Dr. Johan Smith',
    cost: 1200,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[6]._id.toString(),
    femaleTagNumber: animals[6].tagNumber,
    femaleName: animals[6].name,
    maleId: animals[1]._id.toString(),
    maleTagNumber: animals[1].tagNumber,
    maleName: animals[1].name,
    date: '2023-09-12',
    status: 'unsuccessful',
    expectedDueDate: null,
    notes: 'Failed conception, health check recommended',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    femaleId: animals[4]._id.toString(),
    femaleTagNumber: animals[4].tagNumber,
    femaleName: animals[4].name,
    maleId: animals[3]._id.toString(),
    maleTagNumber: animals[3].tagNumber,
    maleName: animals[3].name,
    date: '2023-11-20',
    status: 'successful',
    expectedDueDate: '2024-08-25',
    notes: 'First breeding for young heifer, successful',
    method: 'Natural',
    technician: 'N/A',
    cost: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Birth Records - exactly 10 records
const birthRecords = [
  {
    _id: new ObjectId(),
    breedingRecordId: breedingRecords[0]._id.toString(),
    motherId: animals[0]._id.toString(),
    motherTagNumber: animals[0].tagNumber,
    motherName: animals[0].name,
    fatherId: animals[1]._id.toString(),
    fatherTagNumber: animals[1].tagNumber,
    fatherName: animals[1].name,
    birthDate: '2024-02-25',
    location: 'Kraal 2',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: animals[4]._id.toString(),
        tagNumber: animals[4].tagNumber,
        name: animals[4].name,
        gender: animals[4].gender
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Smooth birth, healthy calf',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: breedingRecords[9]._id.toString(),
    motherId: animals[4]._id.toString(),
    motherTagNumber: animals[4].tagNumber,
    motherName: animals[4].name,
    fatherId: animals[3]._id.toString(),
    fatherTagNumber: animals[3].tagNumber,
    fatherName: animals[3].name,
    birthDate: '2024-03-10',
    location: 'Kraal 2',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'new-calf-1',
        tagNumber: 'CTL-2024-011',
        name: 'Bokang',
        gender: 'Male'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Healthy male calf, first offspring from this heifer',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: breedingRecords[1]._id.toString(),
    motherId: animals[2]._id.toString(),
    motherTagNumber: animals[2].tagNumber,
    motherName: animals[2].name,
    fatherId: animals[1]._id.toString(),
    fatherTagNumber: animals[1].tagNumber,
    fatherName: animals[1].name,
    birthDate: '2024-04-05',
    location: 'Veld B',
    numberOfOffspring: 2,
    offspringDetails: [
      {
        id: 'new-calf-2',
        tagNumber: 'CTL-2024-012',
        name: 'Thabang',
        gender: 'Male'
      },
      {
        id: 'new-calf-3',
        tagNumber: 'CTL-2024-013',
        name: 'Dineo',
        gender: 'Female'
      }
    ],
    complications: 'None',
    assistanceProvided: true,
    notes: 'Twin birth, both calves healthy. Assistance required for second calf.',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: breedingRecords[3]._id.toString(),
    motherId: animals[6]._id.toString(),
    motherTagNumber: animals[6].tagNumber,
    motherName: animals[6].name,
    fatherId: animals[8]._id.toString(),
    fatherTagNumber: animals[8].tagNumber,
    fatherName: animals[8].name,
    birthDate: '2024-04-15',
    location: 'Kraal 1',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'new-calf-4',
        tagNumber: 'CTL-2024-014',
        name: 'Karabo',
        gender: 'Female'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Healthy female calf with good weight',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: breedingRecords[5]._id.toString(),
    motherId: animals[9]._id.toString(),
    motherTagNumber: animals[9].tagNumber,
    motherName: animals[9].name,
    fatherId: animals[8]._id.toString(),
    fatherTagNumber: animals[8].tagNumber,
    fatherName: animals[8].name,
    birthDate: '2024-05-01',
    location: 'Veld B',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'new-calf-5',
        tagNumber: 'CTL-2024-015',
        name: 'Katlego',
        gender: 'Male'
      }
    ],
    complications: 'Minor',
    assistanceProvided: true,
    notes: 'Slight complication during birth, but calf is healthy',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: 'historical-1',
    motherId: animals[0]._id.toString(),
    motherTagNumber: animals[0].tagNumber,
    motherName: animals[0].name,
    fatherId: animals[1]._id.toString(),
    fatherTagNumber: animals[1].tagNumber,
    fatherName: animals[1].name,
    birthDate: '2023-03-15',
    location: 'Kraal 2',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'historical-calf-1',
        tagNumber: 'CTL-2023-001',
        name: 'Kagiso',
        gender: 'Female'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Historical birth record, healthy female calf',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: 'historical-2',
    motherId: animals[2]._id.toString(),
    motherTagNumber: animals[2].tagNumber,
    motherName: animals[2].name,
    fatherId: animals[8]._id.toString(),
    fatherTagNumber: animals[8].tagNumber,
    fatherName: animals[8].name,
    birthDate: '2023-04-20',
    location: 'Veld A',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'historical-calf-2',
        tagNumber: 'CTL-2023-002',
        name: 'Tumisang',
        gender: 'Male'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Historical birth record, healthy male calf',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: 'historical-3',
    motherId: animals[6]._id.toString(),
    motherTagNumber: animals[6].tagNumber,
    motherName: animals[6].name,
    fatherId: animals[1]._id.toString(),
    fatherTagNumber: animals[1].tagNumber,
    fatherName: animals[1].name,
    birthDate: '2023-05-10',
    location: 'Kraal 1',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'historical-calf-3',
        tagNumber: 'CTL-2023-003',
        name: 'Tshiamo',
        gender: 'Female'
      }
    ],
    complications: 'Minor',
    assistanceProvided: true,
    notes: 'Historical birth record, minor complications but calf healthy',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: 'historical-4',
    motherId: animals[0]._id.toString(),
    motherTagNumber: animals[0].tagNumber,
    motherName: animals[0].name,
    fatherId: animals[8]._id.toString(),
    fatherTagNumber: animals[8].tagNumber,
    fatherName: animals[8].name,
    birthDate: '2023-06-15',
    location: 'Veld B',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'historical-calf-4',
        tagNumber: 'CTL-2023-004',
        name: 'Letlhogonolo',
        gender: 'Male'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Historical birth record, healthy male calf',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    breedingRecordId: 'historical-5',
    motherId: animals[7]._id.toString(),
    motherTagNumber: animals[7].tagNumber,
    motherName: animals[7].name,
    fatherId: animals[5]._id.toString(),
    fatherTagNumber: animals[5].tagNumber,
    fatherName: animals[5].name,
    birthDate: '2023-07-20',
    location: 'Kraal 2',
    numberOfOffspring: 1,
    offspringDetails: [
      {
        id: 'historical-calf-5',
        tagNumber: 'CTL-2023-005',
        name: 'Kefilwe',
        gender: 'Female'
      }
    ],
    complications: 'None',
    assistanceProvided: false,
    notes: 'Historical birth record, healthy female calf',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Feeding Records - exactly 10 records
const feedingRecords = [
  {
    _id: new ObjectId(),
    animalId: animals[0]._id.toString(),
    animalTagNumber: animals[0].tagNumber,
    animalName: animals[0].name,
    feedType: 'Hay',
    quantity: 10,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '08:00',
    location: 'Veld A',
    cost: 50,
    notes: 'Regular morning feeding',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[1]._id.toString(),
    animalTagNumber: animals[1].tagNumber,
    animalName: animals[1].name,
    feedType: 'Grain Mix',
    quantity: 5,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '08:15',
    location: 'Kraal 1',
    cost: 75,
    notes: 'High protein mix for breeding bull',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[2]._id.toString(),
    animalTagNumber: animals[2].tagNumber,
    animalName: animals[2].name,
    feedType: 'Pregnancy Supplement',
    quantity: 2,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '08:30',
    location: 'Veld B',
    cost: 120,
    notes: 'Special supplement for pregnant cow',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[3]._id.toString(),
    animalTagNumber: animals[3].tagNumber,
    animalName: animals[3].name,
    feedType: 'Grain Mix',
    quantity: 4,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '08:45',
    location: 'Veld A',
    cost: 60,
    notes: 'Standard feed for male cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[4]._id.toString(),
    animalTagNumber: animals[4].tagNumber,
    animalName: animals[4].name,
    feedType: 'Hay',
    quantity: 8,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '09:00',
    location: 'Kraal 2',
    cost: 40,
    notes: 'Regular feed for young heifer',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[5]._id.toString(),
    animalTagNumber: animals[5].tagNumber,
    animalName: animals[5].name,
    feedType: 'Grain Mix',
    quantity: 5,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '09:15',
    location: 'Kraal 1',
    cost: 75,
    notes: 'High protein mix for breeding bull',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[6]._id.toString(),
    animalTagNumber: animals[6].tagNumber,
    animalName: animals[6].name,
    feedType: 'Hay',
    quantity: 9,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '09:30',
    location: 'Veld B',
    cost: 45,
    notes: 'Regular feed for female cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[7]._id.toString(),
    animalTagNumber: animals[7].tagNumber,
    animalName: animals[7].name,
    feedType: 'Hay',
    quantity: 8,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '09:45',
    location: 'Kraal 2',
    cost: 40,
    notes: 'Regular feed for young heifer',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    animalId: animals[8]._id.toString(),
    animalTagNumber: animals[8].tagNumber,
    animalName: animals[8].name,
    feedType: 'Grain Mix',
    quantity: 6,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '10:00',
    location: 'Veld A',
    cost: 90,
    notes: 'High protein mix for mature bull',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    groupId: 'Group1',
    groupName: 'Cattle Group A',
    feedType: 'Hay',
    quantity: 50,
    unit: 'kg',
    feedingDate: '2024-05-01',
    feedingTime: '16:00',
    location: 'Veld A',
    cost: 250,
    notes: 'Afternoon group feeding',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Feed Inventory - exactly 10 records
const feedInventory = [
  {
    _id: new ObjectId(),
    name: 'Lucerne Hay',
    type: 'Hay',
    quantity: 2000,
    unit: 'kg',
    location: 'Feed Storage 1',
    purchaseDate: '2024-04-15',
    purchasePrice: 10000,
    supplier: 'Local Farm Supplies',
    expiryDate: '2024-10-15',
    nutritionalInfo: {
      protein: '18%',
      fiber: '25%',
      energy: 'Medium'
    },
    notes: 'Good quality lucerne hay',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Cattle Feed Mix',
    type: 'Grain',
    quantity: 1000,
    unit: 'kg',
    location: 'Feed Storage 2',
    purchaseDate: '2024-04-20',
    purchasePrice: 15000,
    supplier: 'Agri Feeds Ltd',
    expiryDate: '2024-08-20',
    nutritionalInfo: {
      protein: '16%',
      fiber: '10%',
      energy: 'High'
    },
    notes: 'High energy feed mix for cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Pregnancy Supplement',
    type: 'Supplement',
    quantity: 500,
    unit: 'kg',
    location: 'Feed Storage 2',
    purchaseDate: '2024-04-25',
    purchasePrice: 12500,
    supplier: 'Agri Feeds Ltd',
    expiryDate: '2024-07-25',
    nutritionalInfo: {
      protein: '22%',
      calcium: 'High',
      vitamins: 'A, D, E'
    },
    notes: 'Special supplement for pregnant animals',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Eragrostis Hay',
    type: 'Hay',
    quantity: 1500,
    unit: 'kg',
    location: 'Feed Storage 1',
    purchaseDate: '2024-04-10',
    purchasePrice: 7500,
    supplier: 'Local Farm Supplies',
    expiryDate: '2024-10-10',
    nutritionalInfo: {
      protein: '10%',
      fiber: '30%',
      energy: 'Medium-Low'
    },
    notes: 'Standard hay for maintenance feeding',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Calf Starter Feed',
    type: 'Grain',
    quantity: 800,
    unit: 'kg',
    location: 'Feed Storage 2',
    purchaseDate: '2024-04-18',
    purchasePrice: 16000,
    supplier: 'Agri Feeds Ltd',
    expiryDate: '2024-07-18',
    nutritionalInfo: {
      protein: '20%',
      fiber: '8%',
      energy: 'High'
    },
    notes: 'Specialized feed for calves under 6 months',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Mineral Blocks',
    type: 'Supplement',
    quantity: 50,
    unit: 'blocks',
    location: 'Feed Storage 3',
    purchaseDate: '2024-04-05',
    purchasePrice: 5000,
    supplier: 'Agri Supplies Co.',
    expiryDate: '2025-04-05',
    nutritionalInfo: {
      minerals: 'Calcium, Phosphorus, Magnesium, Sodium',
      salt: 'High',
      trace_elements: 'Zinc, Copper, Selenium'
    },
    notes: 'Free-choice mineral supplementation for grazing cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Molasses',
    type: 'Supplement',
    quantity: 400,
    unit: 'liters',
    location: 'Feed Storage 3',
    purchaseDate: '2024-04-22',
    purchasePrice: 6000,
    supplier: 'Agri Supplies Co.',
    expiryDate: '2024-10-22',
    nutritionalInfo: {
      energy: 'High',
      sugar: 'Very High',
      protein: '3%'
    },
    notes: 'Used as palatability enhancer and energy source',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Protein Lick',
    type: 'Supplement',
    quantity: 300,
    unit: 'kg',
    location: 'Feed Storage 3',
    purchaseDate: '2024-04-12',
    purchasePrice: 9000,
    supplier: 'Agri Feeds Ltd',
    expiryDate: '2024-10-12',
    nutritionalInfo: {
      protein: '40%',
      urea: 'Present',
      minerals: 'Calcium, Phosphorus'
    },
    notes: 'Winter protein supplementation for grazing cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Grass Silage',
    type: 'Silage',
    quantity: 5000,
    unit: 'kg',
    location: 'Silage Pit 1',
    purchaseDate: '2023-11-10',
    purchasePrice: 15000,
    supplier: 'Self-produced',
    expiryDate: '2024-11-10',
    nutritionalInfo: {
      protein: '12%',
      fiber: '25%',
      energy: 'Medium-High'
    },
    notes: 'Fermented grass silage from last season',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Maize Silage',
    type: 'Silage',
    quantity: 8000,
    unit: 'kg',
    location: 'Silage Pit 2',
    purchaseDate: '2023-12-15',
    purchasePrice: 24000,
    supplier: 'Self-produced',
    expiryDate: '2024-12-15',
    nutritionalInfo: {
      protein: '8%',
      starch: '30%',
      energy: 'High'
    },
    notes: 'High-energy maize silage for lactating cows',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Financial Records - exactly 10 records
const financialRecords = [
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Feed',
    amount: 10000,
    date: '2024-04-15',
    description: 'Purchase of Lucerne Hay',
    paymentMethod: 'Bank Transfer',
    reference: 'INV-2024-001',
    relatedEntityType: 'Feed',
    relatedEntityId: feedInventory[0]._id.toString(),
    relatedEntityName: feedInventory[0].name,
    taxDeductible: true,
    receipt: '/documents/receipts/inv-2024-001.pdf',
    notes: 'Bulk purchase for the season',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Veterinary',
    amount: 2500,
    date: '2024-05-10',
    description: 'Veterinary services for vaccinations',
    paymentMethod: 'Cash',
    reference: 'REC-2024-001',
    relatedEntityType: 'Health',
    relatedEntityId: healthRecords[0]._id.toString(),
    relatedEntityName: 'Vaccination Services',
    taxDeductible: true,
    receipt: '/documents/receipts/rec-2024-001.pdf',
    notes: 'Annual vaccination program',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Income',
    category: 'Sales',
    amount: 25000,
    date: '2024-03-15',
    description: 'Sale of cattle',
    paymentMethod: 'Bank Transfer',
    reference: 'SALE-2024-001',
    relatedEntityType: 'Animal',
    relatedEntityId: 'SOLD-001',
    relatedEntityName: 'Cattle Sale',
    taxDeductible: false,
    receipt: '/documents/receipts/sale-2024-001.pdf',
    notes: 'Sale of 2 cattle to local buyer',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Feed',
    amount: 15000,
    date: '2024-04-20',
    description: 'Purchase of Cattle Feed Mix',
    paymentMethod: 'Bank Transfer',
    reference: 'INV-2024-002',
    relatedEntityType: 'Feed',
    relatedEntityId: feedInventory[1]._id.toString(),
    relatedEntityName: feedInventory[1].name,
    taxDeductible: true,
    receipt: '/documents/receipts/inv-2024-002.pdf',
    notes: 'Regular feed purchase',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Equipment',
    amount: 8500,
    date: '2024-04-05',
    description: 'Purchase of new fencing materials',
    paymentMethod: 'Credit Card',
    reference: 'INV-2024-003',
    relatedEntityType: 'Inventory',
    relatedEntityId: 'INV-FENCE-001',
    relatedEntityName: 'Fencing Materials',
    taxDeductible: true,
    receipt: '/documents/receipts/inv-2024-003.pdf',
    notes: 'Repair and extension of paddock fencing',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Labor',
    amount: 12000,
    date: '2024-04-30',
    description: 'Monthly wages for farm workers',
    paymentMethod: 'Bank Transfer',
    reference: 'PAY-2024-004',
    relatedEntityType: 'Labor',
    relatedEntityId: 'STAFF-APR-2024',
    relatedEntityName: 'Staff Wages',
    taxDeductible: true,
    receipt: '/documents/receipts/pay-2024-004.pdf',
    notes: 'April 2024 wages for 3 farm workers',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Income',
    category: 'Sales',
    amount: 18500,
    date: '2024-04-10',
    description: 'Sale of beef to local butchery',
    paymentMethod: 'Bank Transfer',
    reference: 'SALE-2024-002',
    relatedEntityType: 'Product',
    relatedEntityId: 'BEEF-001',
    relatedEntityName: 'Beef Sale',
    taxDeductible: false,
    receipt: '/documents/receipts/sale-2024-002.pdf',
    notes: 'Sale of processed beef from slaughtered cattle',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Utilities',
    amount: 3500,
    date: '2024-04-25',
    description: 'Electricity bill',
    paymentMethod: 'Debit Order',
    reference: 'UTIL-2024-001',
    relatedEntityType: 'Utility',
    relatedEntityId: 'ELEC-APR-2024',
    relatedEntityName: 'Electricity',
    taxDeductible: true,
    receipt: '/documents/receipts/util-2024-001.pdf',
    notes: 'Monthly electricity bill for farm operations',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Expense',
    category: 'Fuel',
    amount: 4500,
    date: '2024-04-18',
    description: 'Diesel purchase for farm vehicles',
    paymentMethod: 'Credit Card',
    reference: 'FUEL-2024-001',
    relatedEntityType: 'Fuel',
    relatedEntityId: 'DIESEL-APR-2024',
    relatedEntityName: 'Diesel',
    taxDeductible: true,
    receipt: '/documents/receipts/fuel-2024-001.pdf',
    notes: 'Monthly fuel for tractors and farm vehicles',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    recordType: 'Income',
    category: 'Subsidy',
    amount: 15000,
    date: '2024-04-28',
    description: 'Government agricultural subsidy',
    paymentMethod: 'Bank Transfer',
    reference: 'GOV-2024-001',
    relatedEntityType: 'Subsidy',
    relatedEntityId: 'SUBSIDY-2024-Q2',
    relatedEntityName: 'Agricultural Subsidy',
    taxDeductible: false,
    receipt: '/documents/receipts/gov-2024-001.pdf',
    notes: 'Quarterly subsidy for sustainable farming practices',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Inventory - exactly 10 records
const inventory = [
  {
    _id: new ObjectId(),
    category: 'Equipment',
    name: 'Tractor',
    description: 'John Deere 5075E',
    quantity: 1,
    unit: 'each',
    location: 'Equipment Shed',
    purchaseDate: '2020-01-15',
    purchasePrice: 450000,
    supplier: 'John Deere Dealer',
    condition: 'Good',
    lastMaintenanceDate: '2024-01-10',
    nextMaintenanceDate: '2024-07-10',
    notes: 'Regular maintenance required every 6 months',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Supplies',
    name: 'Ear Tags',
    description: 'Plastic numbered tags',
    quantity: 150,
    unit: 'each',
    location: 'Supply Room',
    purchaseDate: '2024-02-01',
    purchasePrice: 3000,
    supplier: 'Farm Supplies Ltd',
    condition: 'New',
    notes: 'For new calves and replacements',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Medicine',
    name: 'Antibiotics',
    description: 'Penicillin 100ml bottles',
    quantity: 10,
    unit: 'bottles',
    location: 'Medicine Cabinet',
    purchaseDate: '2024-01-20',
    purchasePrice: 5000,
    supplier: 'Vet Supplies Ltd',
    condition: 'New',
    expirationDate: '2025-01-20',
    notes: 'Keep refrigerated',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Equipment',
    name: 'Water Pump',
    description: 'Submersible water pump 1.5HP',
    quantity: 2,
    unit: 'each',
    location: 'Equipment Shed',
    purchaseDate: '2023-08-10',
    purchasePrice: 12000,
    supplier: 'Farm Equipment Ltd',
    condition: 'Good',
    lastMaintenanceDate: '2024-02-15',
    nextMaintenanceDate: '2024-08-15',
    notes: 'Used for borehole water extraction',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Equipment',
    name: 'Feed Mixer',
    description: 'Vertical TMR mixer 5m³',
    quantity: 1,
    unit: 'each',
    location: 'Equipment Shed',
    purchaseDate: '2022-05-20',
    purchasePrice: 180000,
    supplier: 'Agricultural Machinery Co.',
    condition: 'Good',
    lastMaintenanceDate: '2024-03-10',
    nextMaintenanceDate: '2024-09-10',
    notes: 'Used for mixing feed rations',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Supplies',
    name: 'Fencing Wire',
    description: 'High-tensile galvanized wire',
    quantity: 10,
    unit: 'rolls',
    location: 'Storage Barn',
    purchaseDate: '2024-03-15',
    purchasePrice: 8500,
    supplier: 'Farm Supplies Ltd',
    condition: 'New',
    notes: 'For fence repairs and new paddock divisions',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Medicine',
    name: 'Dewormer',
    description: 'Ivermectin 1% injectable solution',
    quantity: 5,
    unit: 'bottles',
    location: 'Medicine Cabinet',
    purchaseDate: '2024-02-10',
    purchasePrice: 3500,
    supplier: 'Vet Supplies Ltd',
    condition: 'New',
    expirationDate: '2026-02-10',
    notes: 'For internal and external parasite control',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Equipment',
    name: 'Cattle Crush',
    description: 'Heavy-duty steel cattle restraint',
    quantity: 1,
    unit: 'each',
    location: 'Handling Facility',
    purchaseDate: '2021-11-05',
    purchasePrice: 65000,
    supplier: 'Livestock Equipment Co.',
    condition: 'Good',
    lastMaintenanceDate: '2024-01-20',
    nextMaintenanceDate: '2024-07-20',
    notes: 'Used for animal restraint during treatments',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Supplies',
    name: 'Cattle Lick Blocks',
    description: 'Mineral supplement blocks',
    quantity: 20,
    unit: 'blocks',
    location: 'Feed Storage',
    purchaseDate: '2024-03-01',
    purchasePrice: 4000,
    supplier: 'Agri Feeds Ltd',
    condition: 'New',
    expirationDate: '2025-03-01',
    notes: 'For free-choice mineral supplementation',
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    category: 'Equipment',
    name: 'Water Troughs',
    description: 'Concrete water troughs 500L',
    quantity: 8,
    unit: 'each',
    location: 'Various Paddocks',
    purchaseDate: '2022-09-15',
    purchasePrice: 24000,
    supplier: 'Farm Construction Ltd',
    condition: 'Good',
    notes: 'Distributed across grazing paddocks',
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Users
const users = [
  {
    _id: new ObjectId(),
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'admin123'
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    permissions: [
      { module: 'animals', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'health', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'breeding', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'feeding', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'financial', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'inventory', actions: ['view', 'create', 'update', 'delete'] },
      { module: 'users', actions: ['view', 'create', 'update', 'delete'] }
    ],
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    username: 'manager',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'manager123'
    firstName: 'Farm',
    lastName: 'Manager',
    role: 'manager',
    permissions: [
      { module: 'animals', actions: ['view', 'create', 'update'] },
      { module: 'health', actions: ['view', 'create', 'update'] },
      { module: 'breeding', actions: ['view', 'create', 'update'] },
      { module: 'feeding', actions: ['view', 'create', 'update'] },
      { module: 'financial', actions: ['view'] },
      { module: 'inventory', actions: ['view', 'create', 'update'] }
    ],
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    username: 'staff',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'staff123'
    firstName: 'Staff',
    lastName: 'Member',
    role: 'staff',
    permissions: [
      { module: 'animals', actions: ['view'] },
      { module: 'health', actions: ['view'] },
      { module: 'breeding', actions: ['view'] },
      { module: 'feeding', actions: ['view', 'create'] },
      { module: 'inventory', actions: ['view'] }
    ],
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

/**
 * Migrate data to MongoDB
 */
async function migrateToMongoDB() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database
    const db = client.db(dbName);

    // Define collections and their data - all collections should have exactly 10 records (except users)
    const collections = [
      { name: 'users', data: users, minCount: 3 },
      { name: 'animals', data: animals, minCount: 10 },
      { name: 'health_records', data: healthRecords, minCount: 10 },
      { name: 'breeding_records', data: breedingRecords, minCount: 10 },
      { name: 'birth_records', data: birthRecords, minCount: 10 },
      { name: 'feeding_records', data: feedingRecords, minCount: 10 },
      { name: 'feed_inventory', data: feedInventory, minCount: 10 },
      { name: 'financial_records', data: financialRecords, minCount: 10 },
      { name: 'inventory', data: inventory, minCount: 10 }
    ];

    // Migrate each collection - force drop and recreate all collections except users
    for (const collection of collections) {
      const { name, data, minCount } = collection;
      const dbCollection = db.collection(name);

      // Check if collection already has data
      const existingCount = await dbCollection.countDocuments();

      // For users collection, keep existing data if it meets minimum count
      if (name === 'users' && existingCount >= minCount) {
        logger.info(`${name} collection already has ${existingCount} documents. Skipping migration.`);
      } else {
        // Drop existing data for all other collections
        if (existingCount > 0) {
          logger.info(`Dropping existing ${existingCount} documents from ${name} collection.`);
          await dbCollection.deleteMany({});
        }

        // Insert data
        const result = await dbCollection.insertMany(data);
        logger.info(`${result.insertedCount} ${name} inserted into MongoDB`);
      }
    }

    logger.info('Migration completed successfully');
  } catch (error) {
    logger.error('Migration failed:', error);
  } finally {
    // Close the MongoDB connection
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the migration
migrateToMongoDB();
