import React, { useState } from 'react';
import { <PERSON>, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab, InputAdornment, Link, Paper } from '@mui/material';
import {
  Add,
  HelpOutline,
  Search,
  FilterList,
  Visibility,
  Edit,
  Delete,
  QuestionAnswer,
  ContactSupport,
  Build,
  CalendarToday,
  Email,
  Phone,
  LocalOffer,
  LiveHelp,
  Info,
  Warning,
  Person
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { useResourcesDataContext } from '../../contexts/DataContext';
// Define SupportResource type
interface SupportResource {
  id: string;
  title: string;
  type: 'faq' | 'contact' | 'troubleshooting';
  description: string;
  content: string;
  contactInfo?: {
    name: string;
    email: string;
    phone?: string;
  };
  tags: string[];
  lastUpdated: string;
  viewCount: number;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Support: React.FC = () => {
  const theme = useTheme();
  const { supportResources, loading, error } = useResourcesDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedResource, setSelectedResource] = useState<SupportResource | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    title: '',
    type: 'faq' as 'faq' | 'contact' | 'troubleshooting',
    description: '',
    content: '',
    contactInfo: {
      name: '',
      email: '',
      phone: ''
    },
    tags: ['']
  });

  // Get unique types from support resources
  const types = supportResources ? Array.from(new Set(supportResources.map((resource: SupportResource) => resource.type))) : [];

  // Filter resources based on type and search term
  const filteredResources = supportResources?.filter((resource: SupportResource) => {
    const matchesType = filterType === 'all' || resource.type === filterType;
    const matchesSearch =
      resource.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.tags.some((tag: string) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesType && matchesSearch;
  }) || [];

  // Calculate statistics
  const supportStats = {
    total: supportResources?.length || 0,
    faqs: supportResources?.filter((r: SupportResource) => r.type === 'faq').length || 0,
    contacts: supportResources?.filter((r: SupportResource) => r.type === 'contact').length || 0,
    troubleshooting: supportResources?.filter((r: SupportResource) => r.type === 'troubleshooting').length || 0,
    mostViewed: supportResources?.length
      ? supportResources.reduce((prev: SupportResource, current: SupportResource) => (prev.viewCount > current.viewCount) ? prev : current).title
      : 'N/A'
  };

  // Handle dialog open/close
  const handleOpenDialog = (resource?: SupportResource) => {
    if (resource) {
      setSelectedResource(resource);
      setFormData({
        id: resource.id,
        title: resource.title,
        type: resource.type,
        description: resource.description,
        content: resource.content,
        contactInfo: resource.contactInfo ? {
          name: resource.contactInfo.name,
          email: resource.contactInfo.email,
          phone: resource.contactInfo.phone || ''
        } : {
          name: '',
          email: '',
          phone: ''
        },
        tags: resource.tags
      });
    } else {
      setSelectedResource(null);
      setFormData({
        id: `SUPP-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        title: '',
        type: 'faq',
        description: '',
        content: '',
        contactInfo: {
          name: '',
          email: '',
          phone: ''
        },
        tags: ['']
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedResource(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleTypeChange = (e: any) => {
    setFormData(prev => ({ ...prev, type: e.target.value }));
  };

  const handleContactInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      contactInfo: {
        ...prev.contactInfo,
        [name]: value
      }
    }));
  };

  const handleTagChange = (index: number, value: string) => {
    const newTags = [...formData.tags];
    newTags[index] = value;
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  const handleAddTag = () => {
    setFormData(prev => ({
      ...prev,
      tags: [...prev.tags, '']
    }));
  };

  const handleRemoveTag = (index: number) => {
    const newTags = [...formData.tags];
    newTags.splice(index, 1);
    setFormData(prev => ({ ...prev, tags: newTags }));
  };

  // Handle save resource
  const handleSaveResource = () => {
    // In a real app, this would call an API to save the resource
    console.log('Saving support resource:', formData);

    // For now, just close the dialog
    handleCloseDialog();
  };

  // Handle delete resource
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the resource
    console.log('Deleting support resource:', id);
  };

  // Handle view resource
  const handleViewResource = (resource: SupportResource) => {
    // In a real app, this would open the resource or redirect to a detailed view
    console.log('Viewing support resource:', resource);

    // For now, just open the dialog in view mode
    handleOpenDialog(resource);
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'faq':
        return <QuestionAnswer />;
      case 'contact':
        return <ContactSupport />;
      case 'troubleshooting':
        return <Build />;
      default:
        return <HelpOutline />;
    }
  };

  // Get type color
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'faq':
        return 'primary';
      case 'contact':
        return 'success';
      case 'troubleshooting':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'title', label: 'Title', minWidth: 200 },
    {
      id: 'type',
      label: 'Type',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          icon={getTypeIcon(value)}
          label={value.charAt(0).toUpperCase() + value.slice(1)}
          color={getTypeColor(value) as any}
          size="small"
        />
      )
    },
    { id: 'description', label: 'Description', minWidth: 200 },
    { id: 'lastUpdated', label: 'Last Updated', minWidth: 120 },
    {
      id: 'tags',
      label: 'Tags',
      minWidth: 200,
      format: (value: string[]) => (
        <Box>
          {value.map((tag, index) => (
            <Chip
              key={index}
              label={tag}
              size="small"
              sx={{ mr: 0.5, mb: 0.5 }}
            />
          ))}
        </Box>
      )
    },
    {
      id: 'viewCount',
      label: 'Views',
      minWidth: 80,
      align: 'right'
    }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Support Resources"
        subtitle="Access help, FAQs, and contact information for livestock management assistance"
        module="resources"
        submodule="support"
        actionLabel="Add Support Resource"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Resources"
                  subtitle={supportStats.total.toString()}
                  module="resources"
                  uniqueId="total-support"
                  icon={<HelpOutline />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="FAQs"
                  subtitle={supportStats.faqs.toString()}
                  module="resources"
                  uniqueId="faqs"
                  icon={<QuestionAnswer />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Contact Information"
                  subtitle={supportStats.contacts.toString()}
                  module="resources"
                  uniqueId="contacts"
                  icon={<ContactSupport />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Troubleshooting"
                  subtitle={supportStats.troubleshooting.toString()}
                  module="resources"
                  uniqueId="troubleshooting"
                  icon={<Build />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Quick Contact Card */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Paper
            elevation={3}
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.dark, 0.2)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={8}>
                <Typography variant="h5" fontWeight="bold" gutterBottom>
                  Need Immediate Assistance?
                </Typography>
                <Typography variant="body1" paragraph>
                  Our support team is available Monday to Friday, 8:00 AM - 5:00 PM SAST to help with any questions or issues you may have.
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, mt: 2 }}>
                  <Box component="a" href="mailto:<EMAIL>" sx={{ textDecoration: 'none' }}>
                    <CustomButton
                      variant="contained"
                      color="primary"
                      startIcon={<Email />}
                    >
                      Email Support
                    </CustomButton>
                  </Box>
                  <Box component="a" href="tel:+27123456789" sx={{ textDecoration: 'none' }}>
                    <CustomButton
                      variant="outlined"
                      color="primary"
                      startIcon={<Phone />}
                    >
                      Call Support: +27 12 345 6789
                    </CustomButton>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'center' }}>
                <Box
                  component="img"
                  src="https://img.freepik.com/free-vector/flat-design-illustration-customer-support_23-2148887720.jpg?w=740&t=st=1687441911~exp=1687442511~hmac=5e8ecc8d0e8b9afa4b9e0c573b0b0c7e5b5c5b5c5b5c5b5c5b5c5b5c5b5c5b5c"
                  alt="Customer Support"
                  sx={{
                    maxWidth: '100%',
                    maxHeight: 200,
                    borderRadius: 2,
                    boxShadow: 3
                  }}
                />
              </Grid>
            </Grid>
          </Paper>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200, mr: 2 }}>
              <InputLabel id="type-filter-label">Resource Type</InputLabel>
              <Select
                labelId="type-filter-label"
                id="type-filter"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                label="Resource Type"
              >
                <MenuItem value="all">All Types</MenuItem>
                {types.map((type) => (
                  <MenuItem key={type as React.Key} value={String(type)}>{String(type).charAt(0).toUpperCase() + String(type).slice(1)}</MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              placeholder="Search resources..."
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search fontSize="small" />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <Tabs
            value={viewMode === 'grid' ? 0 : 1}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : 'table')}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Grid View" />
            <Tab label="Table View" />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              {filteredResources.map((resource: SupportResource) => (
                <Grid item xs={12} sm={6} md={4} key={resource.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '4px',
                        background: resource.type === 'faq'
                          ? theme.palette.primary.main
                          : resource.type === 'contact'
                            ? theme.palette.success.main
                            : theme.palette.warning.main
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar
                            sx={{
                              bgcolor: resource.type === 'faq'
                                ? alpha(theme.palette.primary.main, 0.1)
                                : resource.type === 'contact'
                                  ? alpha(theme.palette.success.main, 0.1)
                                  : alpha(theme.palette.warning.main, 0.1),
                              color: resource.type === 'faq'
                                ? theme.palette.primary.main
                                : resource.type === 'contact'
                                  ? theme.palette.success.main
                                  : theme.palette.warning.main,
                              mr: 1
                            }}
                          >
                            {getTypeIcon(resource.type)}
                          </Avatar>
                          <Typography variant="h6" component="h2" fontWeight="bold">
                            {resource.title}
                          </Typography>
                        </Box>
                        <Chip
                          label={resource.type.charAt(0).toUpperCase() + resource.type.slice(1)}
                          color={getTypeColor(resource.type) as any}
                          size="small"
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          Last Updated: {resource.lastUpdated}
                        </Box>
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        {resource.description}
                      </Typography>

                      {resource.type === 'contact' && resource.contactInfo && (
                        <Box sx={{ mb: 1.5 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Contact Information:
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Person fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            <Typography variant="body2">
                              {resource.contactInfo.name}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                            <Email fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                            <Typography variant="body2">
                              {resource.contactInfo.email}
                            </Typography>
                          </Box>
                          {resource.contactInfo.phone && (
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                              <Phone fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                              <Typography variant="body2">
                                {resource.contactInfo.phone}
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      )}

                      <Box sx={{ mb: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <LocalOffer fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          <Typography variant="body2" fontWeight="bold">
                            Tags:
                          </Typography>
                        </Box>
                        <Box>
                          {resource.tags.map((tag: string, index: number) => (
                            <Chip
                              key={index}
                              label={tag}
                              size="small"
                              sx={{ mr: 0.5, mb: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Views:</strong> {resource.viewCount}
                        </Typography>
                      </Box>
                    </CardContent>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                      <IconButton size="small" onClick={() => handleViewResource(resource)}>
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleOpenDialog(resource)}>
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleDelete(resource.id)}>
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  </Card>
                </Grid>
              ))}

              {filteredResources.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No support resources found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new support resource
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                    >
                      Add Support Resource
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EnhancedDataTable
              title="Support Resources"
              subtitle="Access help, FAQs, and contact information for livestock management assistance"
              columns={columns as any}
              data={filteredResources}
              keyField="id"
              loading={loading}
              onView={(row: any) => handleViewResource(row)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['title', 'description', 'content', 'tags']}
              emptyMessage="No support resources found"
              accentColor={theme.palette.primary.main}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Resource Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedResource ? 'Edit Support Resource' : 'Add New Support Resource'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Resource ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="type-label">Resource Type</InputLabel>
                <Select
                  labelId="type-label"
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleTypeChange}
                  label="Resource Type"
                >
                  <MenuItem value="faq">FAQ</MenuItem>
                  <MenuItem value="contact">Contact Information</MenuItem>
                  <MenuItem value="troubleshooting">Troubleshooting</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Enter resource title"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={2}
                placeholder="Enter a brief description"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={4}
                placeholder="Enter detailed content"
              />
            </Grid>

            {formData.type === 'contact' && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Contact Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      label="Contact Name"
                      name="name"
                      value={formData.contactInfo.name}
                      onChange={handleContactInfoChange}
                      fullWidth
                      margin="normal"
                      placeholder="Enter contact name or department"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Email"
                      name="email"
                      value={formData.contactInfo.email}
                      onChange={handleContactInfoChange}
                      fullWidth
                      margin="normal"
                      placeholder="Enter email address"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      label="Phone"
                      name="phone"
                      value={formData.contactInfo.phone}
                      onChange={handleContactInfoChange}
                      fullWidth
                      margin="normal"
                      placeholder="Enter phone number"
                    />
                  </Grid>
                </Grid>
              </Grid>
            )}

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Tags
              </Typography>
              {formData.tags.map((tag, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={tag}
                    onChange={(e) => handleTagChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Tag ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveTag(index)}
                    disabled={formData.tags.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddTag}
                size="small"
                sx={{ mt: 1 }}
              >
                Add Tag
              </CustomButton>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSaveResource}
            variant="contained"
            color="primary"
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Support;
