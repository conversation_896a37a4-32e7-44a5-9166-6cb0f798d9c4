/**
 * User Management API Routes
 *
 * This module provides API routes for user management.
 */

const express = require('express');
const router = express.Router();
const ApiService = require('../../services/apiService');
const { authenticate, authorize, requirePermission } = require('../../middleware/authMiddleware');
const { hashPassword } = require('../../services/authService');
const logger = require('../../utils/logger');
const { validateBody, validateQuery, validateParams } = require('../../middleware/validationMiddleware');
const {
  createUserSchema,
  updateUserSchema,
  getUsersQuerySchema,
  userIdParamSchema,
  roles,
  permissions,
  statuses
} = require('../../validation/userValidation');

/**
 * @route GET /api/users
 * @desc Get all users
 * @access Private (Admin only)
 */
router.get('/', authenticate, authorize(['admin']), validateQuery(getUsersQuerySchema), async (req, res, next) => {
  try {
    // Parse query parameters
    const {
      role,
      status,
      search,
      sort = 'username',
      order = 'asc',
      limit = 100,
      page = 1
    } = req.query;

    // Build query
    const query = {};
    if (role) query.role = role;
    if (status) query.status = status;

    // Add search if provided
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ];
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get users collection
    const usersCollection = db.collection('users');

    // Get users with pagination
    const users = await usersCollection.find(query)
      .sort({ [sort]: order === 'desc' ? -1 : 1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .toArray();

    // Get total count
    const total = await usersCollection.countDocuments(query);

    // Remove passwords from response
    const usersWithoutPasswords = users.map(user => {
      const userObj = { ...user };
      delete userObj.password;
      return userObj;
    });

    // Return users
    res.json({
      success: true,
      data: usersWithoutPasswords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    logger.error('Error getting users:', error);
    next(error);
  }
});

/**
 * @route GET /api/users/:id
 * @desc Get user by ID
 * @access Private (Admin or own user)
 */
router.get('/:id', authenticate, validateParams(userIdParamSchema), async (req, res, next) => {
  try {
    // Check if user is admin or requesting own user
    if (req.user.role !== 'admin' && req.user.id !== req.params.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this user'
      });
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get users collection
    const usersCollection = db.collection('users');

    // Get user by ID
    const user = await usersCollection.findOne({ _id: req.params.id });

    // Check if user exists
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Remove password from response
    const userWithoutPassword = { ...user };
    delete userWithoutPassword.password;

    // Return user
    res.json({
      success: true,
      data: userWithoutPassword
    });
  } catch (error) {
    logger.error('Error getting user by ID:', error);
    next(error);
  }
});

/**
 * @route POST /api/users
 * @desc Create a new user
 * @access Private (Admin only)
 */
router.post('/', authenticate, authorize(['admin']), validateBody(createUserSchema), async (req, res, next) => {
  try {
    const { username, email, password, firstName, lastName, role, permissions } = req.body;

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get users collection
    const usersCollection = db.collection('users');

    // Check if username or email already exists
    const existingUser = await usersCollection.findOne({
      $or: [
        { username },
        { email }
      ]
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'Username or email already exists'
      });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create new user object
    const newUser = {
      username,
      email,
      password: hashedPassword,
      firstName,
      lastName,
      role: role || 'user',
      permissions: permissions || [],
      status: 'active',
      lastLogin: null,
      createdBy: req.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert user into database
    const result = await usersCollection.insertOne(newUser);

    // Get the inserted user
    const user = await usersCollection.findOne({ _id: result.insertedId });

    // Remove password from response
    const userWithoutPassword = { ...user };
    delete userWithoutPassword.password;

    // Return user
    res.status(201).json({
      success: true,
      data: userWithoutPassword
    });
  } catch (error) {
    logger.error('Error creating user:', error);
    next(error);
  }
});

/**
 * @route PUT /api/users/:id
 * @desc Update a user
 * @access Private (Admin or own user)
 */
router.put('/:id', authenticate, validateParams(userIdParamSchema), validateBody(updateUserSchema), async (req, res, next) => {
  try {
    // Check if user is admin or updating own user
    const isAdmin = req.user.role === 'admin';
    const isOwnUser = req.user.id === req.params.id;

    if (!isAdmin && !isOwnUser) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this user'
      });
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get users collection
    const usersCollection = db.collection('users');

    // Get existing user
    const existingUser = await usersCollection.findOne({ _id: req.params.id });

    // Check if user exists
    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prepare update data
    const updateData = { ...req.body };

    // Remove fields that can only be updated by admin
    if (!isAdmin) {
      delete updateData.role;
      delete updateData.permissions;
      delete updateData.status;
    }

    // Hash password if provided
    if (updateData.password) {
      updateData.password = await hashPassword(updateData.password);
    }

    // Add updated timestamp
    updateData.updatedAt = new Date();

    // Update user
    await usersCollection.updateOne(
      { _id: req.params.id },
      { $set: updateData }
    );

    // Get updated user
    const updatedUser = await usersCollection.findOne({ _id: req.params.id });

    // Remove password from response
    const userWithoutPassword = { ...updatedUser };
    delete userWithoutPassword.password;

    // Return user
    res.json({
      success: true,
      data: userWithoutPassword
    });
  } catch (error) {
    logger.error('Error updating user:', error);
    next(error);
  }
});

/**
 * @route DELETE /api/users/:id
 * @desc Delete a user
 * @access Private (Admin only)
 */
router.delete('/:id', authenticate, authorize(['admin']), validateParams(userIdParamSchema), async (req, res, next) => {
  try {
    // Check if trying to delete admin user
    if (req.params.id === req.user.id) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete your own user account'
      });
    }

    // Get MongoDB connection
    const mongodb = require('../../config/mongodb');
    const { db } = await mongodb.connectDB();

    // Get users collection
    const usersCollection = db.collection('users');

    // Get user
    const user = await usersCollection.findOne({ _id: req.params.id });

    // Check if user exists
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete user
    await usersCollection.deleteOne({ _id: req.params.id });

    // Return success
    res.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting user:', error);
    next(error);
  }
});

/**
 * @route GET /api/users/roles
 * @desc Get all user roles
 * @access Private (Admin only)
 */
router.get('/roles', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    // Define available roles
    const roles = [
      {
        name: 'admin',
        description: 'Full access to all system features'
      },
      {
        name: 'manager',
        description: 'Access to most system features except user management'
      },
      {
        name: 'super_user',
        description: 'Access to all data with limited administrative capabilities'
      },
      {
        name: 'staff',
        description: 'Regular access to system features with limited editing capabilities'
      },
      {
        name: 'veterinarian',
        description: 'Access to animal health records and limited breeding information'
      },
      {
        name: 'viewer',
        description: 'Read-only access to system data'
      }
    ];

    // Return roles
    res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @route GET /api/users/permissions
 * @desc Get all user permissions
 * @access Private (Admin only)
 */
router.get('/permissions', authenticate, authorize(['admin']), async (req, res, next) => {
  try {
    // Define available permissions
    const permissions = [
      // Animal Management
      { name: 'view_animals', description: 'View animal records' },
      { name: 'create_animal', description: 'Create new animal records' },
      { name: 'update_animal', description: 'Update existing animal records' },
      { name: 'delete_animal', description: 'Delete animal records' },

      // Health Management
      { name: 'view_health_records', description: 'View health records' },
      { name: 'create_health_record', description: 'Create new health records' },
      { name: 'update_health_record', description: 'Update existing health records' },
      { name: 'delete_health_record', description: 'Delete health records' },

      // Breeding Management
      { name: 'view_breeding_records', description: 'View breeding records' },
      { name: 'create_breeding_record', description: 'Create new breeding records' },
      { name: 'update_breeding_record', description: 'Update existing breeding records' },
      { name: 'delete_breeding_record', description: 'Delete breeding records' },
      { name: 'view_birth_records', description: 'View birth records' },
      { name: 'create_birth_record', description: 'Create new birth records' },
      { name: 'update_birth_record', description: 'Update existing birth records' },
      { name: 'delete_birth_record', description: 'Delete birth records' },

      // Financial Management
      { name: 'view_transactions', description: 'View financial transactions' },
      { name: 'create_transaction', description: 'Create new financial transactions' },
      { name: 'update_transaction', description: 'Update existing financial transactions' },
      { name: 'delete_transaction', description: 'Delete financial transactions' },
      { name: 'view_financial_reports', description: 'View financial reports' },

      // Inventory Management
      { name: 'view_inventory', description: 'View inventory items' },
      { name: 'create_inventory', description: 'Create new inventory items' },
      { name: 'update_inventory', description: 'Update existing inventory items' },
      { name: 'delete_inventory', description: 'Delete inventory items' },

      // Reports
      { name: 'view_reports', description: 'View reports' },
      { name: 'create_reports', description: 'Create new reports' },
      { name: 'export_reports', description: 'Export reports' },

      // User Management
      { name: 'view_users', description: 'View user accounts' },
      { name: 'create_user', description: 'Create new user accounts' },
      { name: 'update_user', description: 'Update existing user accounts' },
      { name: 'delete_user', description: 'Delete user accounts' }
    ];

    // Return permissions
    res.json({
      success: true,
      data: permissions
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
