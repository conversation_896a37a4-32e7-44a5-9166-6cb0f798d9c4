/* Dashboard.css */

.dashboard-container {
  padding: 24px;
  width: 100%;
}

.dashboard-header {
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.dashboard-subtitle {
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 16px;
}

.dashboard-stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.dashboard-card {
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dashboard-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-card-title {
  font-size: 1.1rem;
  font-weight: 600;
}

.dashboard-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

.dashboard-card-content {
  margin-bottom: 16px;
}

.dashboard-card-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.dashboard-card-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.dashboard-card-footer {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
}

.dashboard-card-trend-positive {
  color: #2e7d32;
  display: flex;
  align-items: center;
}

.dashboard-card-trend-negative {
  color: #d32f2f;
  display: flex;
  align-items: center;
}

.dashboard-section {
  margin-bottom: 32px;
}

.dashboard-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-section-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.dashboard-chart-container {
  height: 300px;
  margin-bottom: 24px;
}

.dashboard-table-container {
  margin-bottom: 24px;
}

.dashboard-grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

/* Module icon */
.dashboard-module-icon {
  color: #ffffff;
}

/* Count badge */
.dashboard-count-badge {
  background-color: rgba(25, 118, 210, 0.1);
  color: #1976d2;
}

/* Activity item */
.dashboard-activity-item {
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Task item */
.dashboard-task-item {
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 8px;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Animal management section */
.animal-management-card {
  background-color: rgba(25, 118, 210, 0.05);
}

/* Health management section */
.health-management-card {
  background-color: rgba(46, 125, 50, 0.05);
}

/* Breeding management section */
.breeding-management-card {
  background-color: rgba(156, 39, 176, 0.05);
}

/* Feeding management section */
.feeding-management-card {
  background-color: rgba(237, 108, 2, 0.05);
}

/* Financial management section */
.financial-management-card {
  background-color: rgba(2, 136, 209, 0.05);
}

/* Inventory management section */
.inventory-management-card {
  background-color: rgba(211, 47, 47, 0.05);
}

/* Business analysis section */
.business-analysis-card {
  background-color: rgba(123, 31, 162, 0.05);
}

/* Reports section */
.reports-card {
  background-color: rgba(0, 150, 136, 0.05);
}
