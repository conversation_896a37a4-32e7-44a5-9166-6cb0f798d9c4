import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, Grid, FormControl, InputLabel, Select, MenuItem, Chip, Divider, Alert, CircularProgress, useTheme, alpha } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { BlendedBackgroundCard, CommercialBackgroundCard } from '../common';

import {
  Refresh,
  History,
  Timeline,
  Download
} from '../../utils/iconImports';
import GoogleMapComponent from '../maps/GoogleMapComponent';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip as ChartTooltip, Legend } from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend
);

// Mock data for RFID tracking
interface AnimalLocation {
  id: string;
  animalId: string;
  animalName: string;
  tagId: string;
  timestamp: string;
  latitude: number;
  longitude: number;
  batteryLevel: number;
  status: 'active' | 'inactive' | 'warning';
}

interface RFIDTrackingProps {
  onRefresh?: () => void;
}



// South African farm location (example)
const center = {
  lat: -29.0852,
  lng: 26.1596
};

// Mock animal location data
const mockAnimalLocations: AnimalLocation[] = [
  {
    id: '1',
    animalId: 'ANM001',
    animalName: 'Tshepiso',
    tagId: 'RFID-001',
    timestamp: '2024-04-15T08:45:22',
    latitude: -29.0852,
    longitude: 26.1596,
    batteryLevel: 85,
    status: 'active'
  },
  {
    id: '2',
    animalId: 'ANM002',
    animalName: 'Kgosi',
    tagId: 'RFID-002',
    timestamp: '2024-04-15T09:12:05',
    latitude: -29.0872,
    longitude: 26.1626,
    batteryLevel: 72,
    status: 'active'
  },
  {
    id: '3',
    animalId: 'ANM003',
    animalName: 'Lesedi',
    tagId: 'RFID-003',
    timestamp: '2024-04-15T07:30:18',
    latitude: -29.0832,
    longitude: 26.1576,
    batteryLevel: 15,
    status: 'warning'
  },
  {
    id: '4',
    animalId: 'ANM004',
    animalName: 'Pule',
    tagId: 'RFID-004',
    timestamp: '2024-04-15T10:05:33',
    latitude: -29.0892,
    longitude: 26.1546,
    batteryLevel: 45,
    status: 'active'
  },
  {
    id: '5',
    animalId: 'ANM005',
    animalName: 'Lerato',
    tagId: 'RFID-005',
    timestamp: '2024-04-15T08:22:47',
    latitude: -29.0812,
    longitude: 26.1616,
    batteryLevel: 65,
    status: 'active'
  }
];

// Mock historical data for a single animal
const mockHistoricalData = [
  { timestamp: '2024-04-15T06:00:00', latitude: -29.0852, longitude: 26.1596 },
  { timestamp: '2024-04-15T07:00:00', latitude: -29.0872, longitude: 26.1626 },
  { timestamp: '2024-04-15T08:00:00', latitude: -29.0832, longitude: 26.1576 },
  { timestamp: '2024-04-15T09:00:00', latitude: -29.0892, longitude: 26.1546 },
  { timestamp: '2024-04-15T10:00:00', latitude: -29.0812, longitude: 26.1616 }
];

const RFIDTracking: React.FC<RFIDTrackingProps> = ({ onRefresh }) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const [animalLocations, setAnimalLocations] = useState<AnimalLocation[]>(mockAnimalLocations);
  const [selectedAnimal, setSelectedAnimal] = useState<AnimalLocation | null>(null);
  const [showHistoricalPath, setShowHistoricalPath] = useState(false);
  const [historicalData, setHistoricalData] = useState(mockHistoricalData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [mapCenter, setMapCenter] = useState(center);
  const [zoom, setZoom] = useState(14);

  // Get animal images for background
  const animalImages = [
    '/images/modules/animals/cattle-1.jpeg',
    '/images/modules/animals/cattle-2.avif',
    '/images/modules/animals/cattle-3.jpeg',
    '/images/modules/animals/cattle-4.jpeg',
    '/images/modules/animals/cattle-5.avif'
  ];

  // Map configuration

  // Filter animals based on status
  const filteredAnimals = filterStatus === 'all'
    ? animalLocations
    : animalLocations.filter(animal => animal.status === filterStatus);

  const handleRefresh = () => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      // In a real app, this would fetch fresh data from the server
      setAnimalLocations(mockAnimalLocations);
      setSuccess('Location data refreshed');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);

    if (onRefresh) {
      onRefresh();
    }
  };

  const handleAnimalSelect = (animal: AnimalLocation) => {
    setSelectedAnimal(animal);
    setMapCenter({ lat: animal.latitude, lng: animal.longitude });
    setZoom(16);
  };

  const handleShowHistoricalPath = () => {
    if (!selectedAnimal) return;

    setLoading(true);
    // Simulate API call to get historical data
    setTimeout(() => {
      setHistoricalData(mockHistoricalData);
      setShowHistoricalPath(true);
      setSuccess('Historical path loaded');
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    }, 1000);
  };

  const handleExportData = () => {
    // In a real app, this would export the data to a CSV file
    setSuccess('Data exported successfully');

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'inactive':
        return theme.palette.error.main;
      default:
        return themeColor.primary;
    }
  };

  const getBatteryColor = (level: number) => {
    if (level > 70) return theme.palette.success.main;
    if (level > 30) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // Historical path coordinates would be used here for a Polyline component if needed

  // Prepare data for the movement chart
  const movementChartData = {
    labels: historicalData.map(point => {
      const date = new Date(point.timestamp);
      return `${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    }),
    datasets: [
      {
        label: 'Movement Distance (m)',
        data: [0, 120, 80, 150, 100],
        borderColor: themeColor.primary,
        backgroundColor: alpha(themeColor.primary, 0.2),
        tension: 0.4
      }
    ]
  };

  const movementChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Animal Movement Over Time'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Distance (meters)'
        }
      },
      x: {
        title: {
          display: true,
          text: 'Time'
        }
      }
    }
  };



  return (
    <Box>
      {/* Header with actions */}
      <CommercialBackgroundCard
        images={animalImages}
        interval={10000}
        primaryColor={themeColor.primary}
        secondaryColor={themeColor.secondary}
        opacity={0.7}
        height="auto"
        sx={{ mb: 3, p: 2 }}
      >
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: { xs: 'wrap', sm: 'nowrap' },
          gap: 2
        }}>
          <Typography variant="h6" fontWeight="bold" color="white">
            RFID Tracking System
          </Typography>

          <Box sx={{
            display: 'flex',
            gap: 1,
            flexWrap: { xs: 'wrap', sm: 'nowrap' }
          }}>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel id="status-filter-label">Status</InputLabel>
              <Select
                labelId="status-filter-label"
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="warning">Warning</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>

            <CustomButton
              variant="outlined"
              startIcon={<Download />}
              size="small"
              onClick={handleExportData}
            >
              Export
            </CustomButton>

            <CustomButton
              variant="contained"
              startIcon={<Refresh />}
              size="small"
              onClick={handleRefresh}
              disabled={loading}
            >
              {loading ? <CircularProgress size={24} /> : 'Refresh'}
            </CustomButton>
          </Box>
        </Box>
      </CommercialBackgroundCard>

      {/* Alerts */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 2 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {success && (
        <Alert
          severity="success"
          sx={{ mb: 2 }}
          onClose={() => setSuccess(null)}
        >
          {success}
        </Alert>
      )}

      {/* Main content */}
      <Grid container spacing={3}>
        {/* Map */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 0, borderRadius: 2, overflow: 'hidden', height: '100%', border: `1px solid ${alpha(themeColor.primary, 0.1)}` }}>
            <GoogleMapComponent
              locations={filteredAnimals.map(animal => ({
                id: animal.id.toString(),
                position: { lat: animal.latitude, lng: animal.longitude },
                title: animal.animalName,
                info: `ID: ${animal.animalId}<br/>Tag: ${animal.tagId}<br/>Last seen: ${new Date(animal.timestamp).toLocaleTimeString()}<br/>Battery: ${animal.batteryLevel}%`
              }))}
              center={selectedAnimal ? { lat: selectedAnimal.latitude, lng: selectedAnimal.longitude } : mapCenter}
              zoom={zoom}
              height="100%"
            />
          </Paper>
        </Grid>

        {/* Animal list */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, borderRadius: 2, height: '100%', border: `1px solid ${alpha(themeColor.primary, 0.1)}` }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                Tracked Animals
              </Typography>
              <Chip
                label={`${filteredAnimals.length} animals`}
                size="small"
                color="primary"
              />
            </Box>

            <Divider sx={{ mb: 2 }} />

            <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
              {filteredAnimals.map((animal) => (
                <Paper
                  key={animal.id}
                  sx={{
                    p: 2,
                    mb: 2,
                    borderRadius: 2,
                    cursor: 'pointer',
                    borderLeft: `4px solid ${getStatusColor(animal.status)}`,
                    bgcolor: selectedAnimal?.id === animal.id ? alpha(themeColor.primary, 0.1) : 'transparent',
                    '&:hover': {
                      bgcolor: alpha(themeColor.primary, 0.05)
                    }
                  }}
                  onClick={() => handleAnimalSelect(animal)}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {animal.animalName}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        ID: {animal.animalId}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Last seen: {new Date(animal.timestamp).toLocaleTimeString()}
                      </Typography>
                    </Box>
                    <Box>
                      <Chip
                        label={`${animal.batteryLevel}%`}
                        size="small"
                        sx={{
                          bgcolor: getBatteryColor(animal.batteryLevel),
                          color: 'white',
                          fontSize: '0.7rem'
                        }}
                      />
                    </Box>
                  </Box>
                </Paper>
              ))}
            </Box>

            {selectedAnimal && (
              <Box sx={{ mt: 2 }}>
                <Divider sx={{ mb: 2 }} />
                <Typography variant="subtitle2" fontWeight="bold">
                  Selected Animal Actions
                </Typography>
                <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                  <CustomButton
                    variant="outlined"
                    startIcon={<History />}
                    size="small"
                    onClick={handleShowHistoricalPath}
                    disabled={loading || showHistoricalPath}
                  >
                    Show Path
                  </CustomButton>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Timeline />}
                    size="small"
                    onClick={() => setShowHistoricalPath(false)}
                    disabled={!showHistoricalPath}
                  >
                    Hide Path
                  </CustomButton>
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Movement chart */}
        {selectedAnimal && showHistoricalPath && (
          <Grid item xs={12}>
            <BlendedBackgroundCard
              backgroundImage={animalImages[0]}
              primaryColor={themeColor.primary}
              secondaryColor={themeColor.secondary}
              opacity={0.05}
              height="auto"
              sx={{ p: 3 }}
            >
              <Typography variant="subtitle1" fontWeight="bold" mb={2}>
                Movement Analysis: {selectedAnimal.animalName}
              </Typography>
              <Box sx={{ height: 300, bgcolor: 'rgba(255,255,255,0.9)', p: 2, borderRadius: 1 }}>
                <Line data={movementChartData} options={movementChartOptions} />
              </Box>
            </BlendedBackgroundCard>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default RFIDTracking;
