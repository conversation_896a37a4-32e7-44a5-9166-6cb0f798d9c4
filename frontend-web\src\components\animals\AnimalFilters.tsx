import React from 'react';
import { CustomButton } from '../common';
import { Paper, TextField, Select, MenuItem, FormControl, InputLabel, Grid, Chip,  } from '@mui/material';
import {  FilterList, Clear  } from '../../utils/iconImports';

interface FilterProps {
  filters: {
    search: string;
    species: string[];
    healthStatus: string[];
    location: string;
    ageRange: {
      min: number;
      max: number;
    };
  };
  onFilterChange: (filters: any) => void;
  onClearFilters: () => void;
}

const AnimalFilters: React.FC<FilterProps> = ({
  filters,
  onFilterChange,
  onClearFilters,
}) => {
  const handleChange = (field: string, value: any) => {
    onFilterChange({ ...filters, [field]: value });
  };

  return (
    <Paper className="p-4 mb-4">
      <Grid container spacing={2}>
        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            label="Search"
            value={filters.search}
            onChange={(e) => handleChange('search', e.target.value)}
            placeholder="Tag number, breed..."
          />
        </Grid>
        <Grid item xs={12} md={3}>
          <FormControl fullWidth>
            <InputLabel>Species</InputLabel>
            <Select
              multiple
              value={filters.species}
              onChange={(e) => handleChange('species', e.target.value)}
              renderValue={(selected) => (
                <div className="flex flex-wrap gap-1">
                  {(selected as string[]).map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </div>
              )}
            >
              <MenuItem value="cattle">Cattle</MenuItem>
              <MenuItem value="sheep">Sheep</MenuItem>
              <MenuItem value="goat">Goat</MenuItem>
              <MenuItem value="pig">Pig</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={3}>
          <FormControl fullWidth>
            <InputLabel>Health Status</InputLabel>
            <Select
              multiple
              value={filters.healthStatus}
              onChange={(e) => handleChange('healthStatus', e.target.value)}
              renderValue={(selected) => (
                <div className="flex flex-wrap gap-1">
                  {(selected as string[]).map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </div>
              )}
            >
              <MenuItem value="healthy">Healthy</MenuItem>
              <MenuItem value="sick">Sick</MenuItem>
              <MenuItem value="pregnant">Pregnant</MenuItem>
              <MenuItem value="quarantine">Quarantine</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            label="Location"
            value={filters.location}
            onChange={(e) => handleChange('location', e.target.value)}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <div className="flex gap-4">
            <TextField
              type="number"
              label="Min Age (months)"
              value={filters.ageRange.min}
              onChange={(e) => handleChange('ageRange', { 
                ...filters.ageRange, 
                min: parseInt(e.target.value) 
              })}
            />
            <TextField
              type="number"
              label="Max Age (months)"
              value={filters.ageRange.max}
              onChange={(e) => handleChange('ageRange', { 
                ...filters.ageRange, 
                max: parseInt(e.target.value) 
              })}
            />
          </div>
        </Grid>
        <Grid item xs={12} md={6} className="flex justify-end items-center gap-2">
          <CustomButton
            variant="outlined"
            startIcon={<Clear />}
            onClick={onClearFilters}
          >
            Clear Filters
          </CustomButton>
          <CustomButton
            variant="contained"
            startIcon={<FilterList />}
            onClick={() => {/* Apply filters */}}
          >
            Apply Filters
          </CustomButton>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AnimalFilters;