import {
  CommercialOrder,
  OrderItem,
  Supplier,
  Auction,
  AuctionItem,
  PriceUpdate,
  CommercialStats
} from '../types/commercial';

// Helper function to generate random dates within a range
const randomDate = (start: Date, end: Date): Date => {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
};

// Helper function to generate random numbers within a range
const randomNumber = (min: number, max: number, decimals = 0): number => {
  const num = Math.random() * (max - min) + min;
  return Number(num.toFixed(decimals));
};

// Helper function to choose a random item from an array
const randomChoice = <T>(items: T[]): T => {
  return items[Math.floor(Math.random() * items.length)];
};

// Generate mock orders
export const mockOrders: CommercialOrder[] = Array.from({ length: 4 }, (_, i) => {
  const orderDate = randomDate(new Date(2023, 0, 1), new Date());
  const orderItems: OrderItem[] = Array.from({ length: randomNumber(1, 5) }, (_, j) => {
    const quantity = randomNumber(1, 10);
    const unitPrice = randomNumber(1000, 20000, 2);
    return {
      id: `ITEM${i}${j}`,
      productId: `PROD${randomNumber(1, 100)}`,
      productName: randomChoice([
        'Nguni Bull',
        'Bonsmara Heifer',
        'Dorper Sheep',
        'Boer Goat',
        'Lucerne Bales',
        'Dip Chemicals',
        'Lick Supplements'
      ]),
      quantity,
      unitPrice,
      totalPrice: quantity * unitPrice,
      animalId: randomChoice(['A001', 'A002', 'A003', 'A004', undefined])
    };
  });

  const totalAmount = orderItems.reduce((sum, item) => sum + item.totalPrice, 0);

  return {
    id: `ORD${(i + 1).toString().padStart(3, '0')}`,
    orderNumber: `ORD-${randomNumber(10000, 99999)}`,
    date: orderDate,
    customer: randomChoice([
      'Beefmaster Kimberley',
      'Karan Beef',
      'Cavalier Abattoir',
      'Sparta Beef',
      'Chalmar Beef',
      'Clover Dairy'
    ]),
    items: orderItems,
    totalAmount,
    status: randomChoice(['pending', 'processing', 'shipped', 'delivered', 'cancelled']),
    paymentStatus: randomChoice(['pending', 'paid', 'partial', 'refunded']),
    shippingAddress: randomChoice([
      '10 Industrial Rd, Kimberley, Northern Cape',
      'Farm Knoppiesfontein, Heidelberg, Gauteng',
      '25 Slaughter Rd, Cullinan, Gauteng',
      'R59 Redan Road, Vereeniging, Gauteng',
      'Farm Doornfontein, Delmas, Mpumalanga'
    ]),
    notes: randomChoice([
      'Deliver during business hours only',
      'Call before delivery',
      'Leave at gate if no one is present',
      undefined
    ]),
    createdAt: orderDate,
    updatedAt: new Date(orderDate.getTime() + randomNumber(1, 10) * 24 * 60 * 60 * 1000)
  };
});

// Generate mock suppliers
export const mockSuppliers: Supplier[] = Array.from({ length: 4 }, (_, i) => {
  return {
    id: `SUP${(i + 1).toString().padStart(3, '0')}`,
    name: randomChoice([
      'Afgri Animal Feeds',
      'Kaap Agri',
      'Obaro',
      'GWK',
      'BKB',
      'OVK'
    ]),
    contactPerson: randomChoice([
      'Johan van der Merwe',
      'Pieter Venter',
      'Thabo Molefe',
      'Sipho Ndlovu',
      'Anele Dlamini',
      'Hendrik Botha'
    ]),
    email: `contact${i}@example.com`,
    phone: `+27-${randomNumber(10, 99)}-${randomNumber(100, 999)}-${randomNumber(1000, 9999)}`,
    address: randomChoice([
      '10 Industrial Ave, Centurion, Gauteng',
      '25 Voortrekker Rd, Stellenbosch, Western Cape',
      '15 Boeremark St, Potchefstroom, North West',
      '7 Agri Park, Bethlehem, Free State',
      '42 Landbou Rd, Nelspruit, Mpumalanga'
    ]),
    products: Array.from({ length: randomNumber(1, 5) }, () =>
      randomChoice([
        'Feed',
        'Supplements',
        'Equipment',
        'Veterinary Supplies',
        'Seeds',
        'Fertilizers'
      ])
    ),
    rating: randomNumber(1, 5, 1),
    status: randomChoice(['active', 'inactive']),
    lastOrderDate: randomDate(new Date(2023, 0, 1), new Date()),
    notes: randomChoice([
      'Reliable supplier with good prices',
      'Sometimes delivers late',
      'Excellent quality products',
      undefined
    ])
  };
});

// Generate mock auctions
export const mockAuctions: Auction[] = Array.from({ length: 4 }, (_, i) => {
  const auctionDate = randomDate(new Date(), new Date(new Date().setMonth(new Date().getMonth() + 6)));
  const auctionItems: AuctionItem[] = Array.from({ length: randomNumber(5, 15) }, (_, j) => {
    const type = randomChoice(['livestock', 'equipment', 'feed', 'other']) as 'livestock' | 'equipment' | 'feed' | 'other';
    const startingPrice = randomNumber(
      type === 'livestock' ? 1000 : 100,
      type === 'livestock' ? 5000 : 1000,
      2
    );

    return {
      id: `AUCITEM${i}${j}`,
      type,
      description: randomChoice([
        'Premium Nguni Bull, 3 years old',
        'Bonsmara Heifer, ready for breeding',
        'Dorper Sheep, quality meat producer',
        'Massey Ferguson Tractor, good condition',
        'Lucerne Bales, premium quality',
        'Dip Chemicals, bulk quantity',
        'Game Capture Equipment, lightly used'
      ]),
      startingPrice,
      currentBid: Math.random() > 0.3 ? startingPrice + randomNumber(100, 1000, 2) : undefined,
      numberOfBids: randomNumber(0, 10),
      images: Array.from({ length: randomNumber(1, 3) }, (_, k) => `image${j}${k}.jpg`),
      status: randomChoice(['available', 'sold', 'withdrawn']),
      animalId: type === 'livestock' ? `A${randomNumber(100, 999)}` : undefined
    };
  });

  return {
    id: `AUC${(i + 1).toString().padStart(3, '0')}`,
    title: randomChoice([
      'Vryburg Livestock Auction 2024',
      'Ermelo Cattle Sale',
      'Bultfontein Stud Auction',
      'Standerton Game Auction',
      'Vrede Agricultural Equipment Sale',
      'Kroonstad Farmers Auction'
    ]),
    date: auctionDate,
    status: auctionDate < new Date() ? randomChoice(['completed', 'cancelled']) :
            auctionDate.getDate() === new Date().getDate() ? 'active' : 'upcoming',
    location: randomChoice([
      'Vryburg Auction Pens',
      'Ermelo Agricultural Showgrounds',
      'Bultfontein Farmers Hall',
      'Standerton Auction Venue',
      'Vrede Community Center',
      'BKB Auction Facility, Kroonstad'
    ]),
    description: randomChoice([
      'Premium Nguni and Bonsmara cattle auction with stud animals',
      'Quarterly auction of tractors and farm implements',
      'Special game auction featuring rare antelope species',
      'Drought relief auction with quality breeding stock',
      'Annual sheep and goat auction with registered breeders'
    ]),
    items: auctionItems,
    registeredBidders: randomNumber(20, 100),
    minimumBid: randomNumber(100, 500),
    endTime: new Date(auctionDate.getTime() + 3 * 60 * 60 * 1000) // 3 hours after start
  };
});

// Generate mock price updates
export const mockPriceUpdates: PriceUpdate[] = Array.from({ length: 4 }, (_, i) => {
  const oldPrice = randomNumber(50, 500, 2);
  const priceChange = randomNumber(-50, 100, 2);
  const newPrice = oldPrice + priceChange;

  return {
    id: `PU${(i + 1).toString().padStart(3, '0')}`,
    productType: randomChoice([
      'Nguni Cattle',
      'Bonsmara Cattle',
      'Dorper Sheep',
      'Boer Goats',
      'Lucerne',
      'Maize Feed',
      'Dip Chemicals',
      'Lick Supplements'
    ]),
    oldPrice,
    newPrice,
    effectiveDate: randomDate(new Date(), new Date(new Date().setMonth(new Date().getMonth() + 3))),
    reason: randomChoice([
      'Drought conditions',
      'Seasonal adjustment',
      'Import cost changes',
      'Fuel price increase',
      'Exchange rate fluctuation',
      'Local market demand'
    ]),
    updatedBy: randomChoice([
      'System',
      'Admin',
      'Market Manager',
      'Price Committee'
    ])
  };
});

// Generate mock commercial stats
export const mockCommercialStats: CommercialStats = {
  totalOrders: mockOrders.length,
  pendingOrders: mockOrders.filter(order => order.status === 'pending').length,
  totalRevenue: mockOrders.reduce((sum, order) => sum + order.totalAmount, 0),
  monthlyRevenue: mockOrders
    .filter(order => {
      const orderDate = new Date(order.date);
      const currentDate = new Date();
      return orderDate.getMonth() === currentDate.getMonth() &&
             orderDate.getFullYear() === currentDate.getFullYear();
    })
    .reduce((sum, order) => sum + order.totalAmount, 0),
  activeSuppliers: mockSuppliers.filter(supplier => supplier.status === 'active').length,
  pendingAuctions: mockAuctions.filter(auction => auction.status === 'upcoming').length,
  averageOrderValue: mockOrders.reduce((sum, order) => sum + order.totalAmount, 0) / mockOrders.length,

  topSellingProducts: [
    { productName: 'Nguni Bull', quantity: 15, revenue: 450000 },
    { productName: 'Bonsmara Heifer', quantity: 12, revenue: 360000 },
    { productName: 'Lucerne Bales', quantity: 500, revenue: 250000 },
    { productName: 'Dip Chemicals', quantity: 30, revenue: 150000 },
    { productName: 'Dorper Sheep', quantity: 25, revenue: 125000 }
  ],

  revenueByMonth: [
    { month: 'Jan', revenue: 350000 },
    { month: 'Feb', revenue: 280000 },
    { month: 'Mar', revenue: 420000 },
    { month: 'Apr', revenue: 380000 },
    { month: 'May', revenue: 520000 },
    { month: 'Jun', revenue: 480000 }
  ],

  ordersByStatus: [
    { status: 'pending', count: mockOrders.filter(order => order.status === 'pending').length },
    { status: 'processing', count: mockOrders.filter(order => order.status === 'processing').length },
    { status: 'shipped', count: mockOrders.filter(order => order.status === 'shipped').length },
    { status: 'delivered', count: mockOrders.filter(order => order.status === 'delivered').length },
    { status: 'cancelled', count: mockOrders.filter(order => order.status === 'cancelled').length }
  ]
};
