@echo off
echo ===================================================
echo    AMPD Livestock Management Cleanup Utility
echo ===================================================
echo.
echo This script will clean up unnecessary files from the project.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause > nul

echo.
echo [1/4] Removing unnecessary files...
del /q install-polyfills.bat 2>nul
del /q copy-images.ps1 2>nul
del /q copy-nested-images.js 2>nul
del /q remove-duplicate-frontend.js 2>nul
del /q run-migration.js 2>nul
del /q setup-images.js 2>nul
del /q test-script.js 2>nul
echo Removed unnecessary script files

echo.
echo [2/4] Cleaning up backup folders...
if exist "all-import-fixes-backups" (
    rmdir /s /q "all-import-fixes-backups"
    echo Removed all-import-fixes-backups folder
)
if exist "import-fixes-backups" (
    rmdir /s /q "import-fixes-backups"
    echo Removed import-fixes-backups folder
)
if exist "frontend-api-calls-backups" (
    rmdir /s /q "frontend-api-calls-backups"
    echo Removed frontend-api-calls-backups folder
)
if exist "final-button-fix-backups" (
    rmdir /s /q "final-button-fix-backups"
    echo Removed final-button-fix-backups folder
)

echo.
echo [3/4] Cleaning up cache folders...
if exist "frontend-web\node_modules\.cache" (
    rmdir /s /q "frontend-web\node_modules\.cache"
    echo Cleaned frontend cache
)
if exist "backend\node_modules\.cache" (
    rmdir /s /q "backend\node_modules\.cache"
    echo Cleaned backend cache
)

echo.
echo [4/4] Cleanup complete!
echo.
echo The project structure has been cleaned up.
echo You can now run start-app.bat to start the application.
echo.
echo Press any key to exit...
pause > nul
