import React, { ReactNode } from 'react';
import { Box, Card, CardContent, Typography, useTheme, alpha, Skeleton, Tooltip, IconButton } from '@mui/material';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';

interface ResponsiveDataCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  trend?: {
    value: number;
    label?: string;
    isPercentage?: boolean;
    isPositive?: boolean;
    isNeutral?: boolean;
  };
  loading?: boolean;
  tooltip?: string;
  onClick?: () => void;
  accentColor?: string;
  backgroundImage?: string;
  footer?: ReactNode;
  valuePrefix?: string;
  valueSuffix?: string;
}

/**
 * ResponsiveDataCard component
 * Provides a consistent, responsive card for displaying key metrics
 */
const ResponsiveDataCard: React.FC<ResponsiveDataCardProps> = ({
  title,
  value,
  icon,
  trend,
  loading = false,
  tooltip,
  onClick,
  accentColor,
  backgroundImage,
  footer,
  valuePrefix = '',
  valueSuffix = ''
}) => {
  const theme = useTheme();
  const { isMobile } = useResponsive();

  // Get primary color
  const primaryColor = accentColor || theme.palette.primary.main;

  // Determine trend color and icon
  const getTrendColor = () => {
    if (!trend) return theme.palette.text.secondary;
    if (trend.isNeutral) return theme.palette.text.secondary;
    return trend.isPositive ? theme.palette.success.main : theme.palette.error.main;
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    if (trend.isNeutral) return <TrendingFlatIcon fontSize="small" />;
    return trend.isPositive ? 
      <TrendingUpIcon fontSize="small" /> : 
      <TrendingDownIcon fontSize="small" />;
  };

  return (
    <motion.div
      whileHover={{ 
        y: -5,
        transition: { duration: 0.2 }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card
        sx={{
          height: '100%',
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          cursor: onClick ? 'pointer' : 'default',
          position: 'relative',
          transition: 'all 0.3s ease',
          '&:hover': onClick ? {
            boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
          } : {},
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '4px',
            height: '100%',
            background: primaryColor,
            zIndex: 1
          }
        }}
        onClick={onClick}
      >
        {/* Background Image with Overlay */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.05,
              zIndex: 0
            }}
          />
        )}

        {/* Card Content */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 1,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            p: 3
          }}
        >
          {/* Card Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography 
                variant="subtitle1" 
                color="text.secondary"
                sx={{ 
                  fontSize: { xs: '0.875rem', sm: '0.9rem' },
                  fontWeight: 500
                }}
              >
                {title}
              </Typography>
              
              {tooltip && (
                <Tooltip title={tooltip} arrow placement="top">
                  <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
                    <InfoOutlinedIcon fontSize="small" sx={{ fontSize: '1rem', color: 'text.secondary' }} />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
            
            {icon && (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '8px',
                  background: alpha(primaryColor, 0.1),
                  color: primaryColor
                }}
              >
                {icon}
              </Box>
            )}
          </Box>

          {/* Card Value */}
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
            {loading ? (
              <Skeleton variant="text" width="80%" height={60} animation="wave" />
            ) : (
              <Typography 
                variant="h4" 
                component="div"
                sx={{ 
                  fontWeight: 700,
                  fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
                  lineHeight: 1.2,
                  mb: 1
                }}
              >
                {valuePrefix}{value}{valueSuffix}
              </Typography>
            )}

            {/* Trend Indicator */}
            {trend && !loading && (
              <Box 
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  color: getTrendColor(),
                  mt: 0.5
                }}
              >
                {getTrendIcon()}
                <Typography 
                  variant="body2" 
                  component="span"
                  sx={{ 
                    ml: 0.5,
                    fontWeight: 500
                  }}
                >
                  {trend.value}{trend.isPercentage ? '%' : ''} {trend.label || ''}
                </Typography>
              </Box>
            )}
          </Box>

          {/* Card Footer */}
          {footer && (
            <Box sx={{ mt: 2, pt: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.5)}` }}>
              {footer}
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ResponsiveDataCard;
