import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, useTheme, alpha, IconButton, Divider } from '@mui/material';
import {
  Add, Edit, Delete, Visibility,
  TrendingUp, TrendingDown, Warning, CheckCircle,
  CalendarToday, Pets, Assessment
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { ModuleHeader, AnimatedBackgroundCard, BlendedBackgroundCard } from '../../components/common';
import { useAnimalData } from '../../hooks/useAnimalData';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import { ROUTES } from '../../constants/routes';
import { formatDate, calculateAge } from '../../utils/formatters';
// Removed useThemeContext import as it was deleted
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';

// Define retirement criteria
const RETIREMENT_AGE = {
  'Cattle': 10, // years
  'Sheep': 6,
  'Goat': 7,
  'Pig': 5
};

const BREEDING_LIMIT = {
  'Cattle': 8, // number of times
  'Sheep': 6,
  'Goat': 6,
  'Pig': 8
};

const AssetManagementDashboard = () => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Use MongoDB if connected, otherwise use mock data
  const { isConnected } = useMongoDb();
  const { animals, loading } = useAnimalData();

  // State for retirement candidates
  const [retirementCandidates, setRetirementCandidates] = useState([]);
  const [assetStats, setAssetStats] = useState({
    totalAnimals: 0,
    activeAnimals: 0,
    retiredAnimals: 0,
    nearingRetirement: 0,
    retirementByReason: {
      age: 0,
      breeding: 0,
      health: 0,
      other: 0
    },
    valueOfActiveAssets: 0,
    valueOfRetiredAssets: 0
  });

  // Calculate retirement candidates and stats
  useEffect(() => {
    if (animals && animals.length > 0) {
      // Filter animals that should be retired based on age or breeding count
      const candidates = animals.filter(animal => {
        const ageInYears = calculateAge(animal.birthDate);
        const breedingCount = animal.breedingCount || 0;
        const ageLimit = RETIREMENT_AGE[animal.type] || 10;
        const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

        return (ageInYears >= ageLimit || breedingCount >= breedingLimit) && animal.status !== 'Retired';
      });

      // Count retired animals
      const retired = animals.filter(animal => animal.status === 'Retired');

      // Count animals nearing retirement (within 1 year or 1 breeding cycle)
      const nearing = animals.filter(animal => {
        const ageInYears = calculateAge(animal.birthDate);
        const breedingCount = animal.breedingCount || 0;
        const ageLimit = RETIREMENT_AGE[animal.type] || 10;
        const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

        return (
          (ageInYears >= ageLimit - 1 && ageInYears < ageLimit) ||
          (breedingCount >= breedingLimit - 1 && breedingCount < breedingLimit)
        ) && animal.status !== 'Retired';
      });

      // Calculate retirement reasons
      const retirementReasons = {
        age: 0,
        breeding: 0,
        health: 0,
        other: 0
      };

      retired.forEach(animal => {
        const ageInYears = calculateAge(animal.birthDate);
        const breedingCount = animal.breedingCount || 0;
        const ageLimit = RETIREMENT_AGE[animal.type] || 10;
        const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

        if (ageInYears >= ageLimit) {
          retirementReasons.age++;
        } else if (breedingCount >= breedingLimit) {
          retirementReasons.breeding++;
        } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {
          retirementReasons.health++;
        } else {
          retirementReasons.other++;
        }
      });

      // Calculate asset values (using purchase price or estimated value)
      const activeValue = animals
        .filter(animal => animal.status !== 'Retired')
        .reduce((sum, animal) => sum + (animal.purchasePrice || 0), 0);

      const retiredValue = retired
        .reduce((sum, animal) => sum + (animal.purchasePrice || 0), 0);

      // Update state
      setRetirementCandidates(candidates);
      setAssetStats({
        totalAnimals: animals.length,
        activeAnimals: animals.length - retired.length,
        retiredAnimals: retired.length,
        nearingRetirement: nearing.length,
        retirementByReason: retirementReasons,
        valueOfActiveAssets: activeValue,
        valueOfRetiredAssets: retiredValue
      });
    }
  }, [animals]);

  // Prepare chart data
  const statusData = [
    { name: 'Active', value: assetStats.activeAnimals, color: '#4caf50' },
    { name: 'Retired', value: assetStats.retiredAnimals, color: '#f44336' },
    { name: 'Nearing Retirement', value: assetStats.nearingRetirement, color: '#ff9800' }
  ];

  const reasonData = [
    { name: 'Age', value: assetStats.retirementByReason.age, color: '#3f51b5' },
    { name: 'Breeding Limit', value: assetStats.retirementByReason.breeding, color: '#9c27b0' },
    { name: 'Health Issues', value: assetStats.retirementByReason.health, color: '#e91e63' },
    { name: 'Other', value: assetStats.retirementByReason.other, color: '#607d8b' }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Asset Management"
        subtitle="Track and manage livestock assets, retirement planning, and performance metrics"
        module="animals"
        submodule="asset-management"
        actionLabel="Manage Retirement"
        actionIcon={<Add />}
        onAction={() => navigate(`${ROUTES.ASSET_MANAGEMENT}/retirement`)}
        icon={<Pets />}
      />

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4, mt: 1 }}>
        <Grid item xs={12} sm={6} md={3}>
          <BlendedBackgroundCard
            primaryColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            opacity={0.1}
          >
            <CardContent>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Total Animals
              </Typography>
              <Typography variant="h4" component="div" fontWeight="bold">
                {assetStats.totalAnimals}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Pets fontSize="small" color="primary" />
                <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                  Total livestock assets
                </Typography>
              </Box>
            </CardContent>
          </BlendedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <BlendedBackgroundCard
            primaryColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            opacity={0.1}
          >
            <CardContent>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Active Animals
              </Typography>
              <Typography variant="h4" component="div" fontWeight="bold">
                {assetStats.activeAnimals}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <CheckCircle fontSize="small" color="success" />
                <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                  Productive livestock
                </Typography>
              </Box>
            </CardContent>
          </BlendedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <BlendedBackgroundCard
            primaryColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            opacity={0.1}
          >
            <CardContent>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Retired Animals
              </Typography>
              <Typography variant="h4" component="div" fontWeight="bold">
                {assetStats.retiredAnimals}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <CalendarToday fontSize="small" color="error" />
                <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                  Non-productive assets
                </Typography>
              </Box>
            </CardContent>
          </BlendedBackgroundCard>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <BlendedBackgroundCard
            primaryColor={theme.palette.primary.main}
            secondaryColor={theme.palette.primary.dark}
            opacity={0.1}
          >
            <CardContent>
              <Typography variant="body2" color="textSecondary" gutterBottom>
                Nearing Retirement
              </Typography>
              <Typography variant="h4" component="div" fontWeight="bold">
                {assetStats.nearingRetirement}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                <Warning fontSize="small" color="warning" />
                <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                  Require attention
                </Typography>
              </Box>
            </CardContent>
          </BlendedBackgroundCard>
        </Grid>
      </Grid>

      {/* Charts and Analysis */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <AnimatedBackgroundCard
            title="Asset Status Distribution"
            icon={<Assessment />}
            accentColor={theme.palette.primary.main}
          >
            <Box height={300} mt={2}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} animals`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <AnimatedBackgroundCard
            title="Retirement Reasons"
            icon={<Assessment />}
            accentColor={theme.palette.primary.main}
          >
            <Box height={300} mt={2}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={reasonData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {reasonData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`${value} animals`, 'Count']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>
      </Grid>

      {/* Retirement Candidates */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          Retirement Candidates
        </Typography>
        <TableContainer component={Paper} sx={{ boxShadow: theme.shadows[2], borderRadius: 2 }}>
          <Table>
            <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <TableRow>
                <TableCell>Tag Number</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Age</TableCell>
                <TableCell>Breeding Count</TableCell>
                <TableCell>Retirement Reason</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {retirementCandidates.length > 0 ? (
                retirementCandidates.slice(0, 10).map((animal) => {
                  const ageInYears = calculateAge(animal.birthDate);
                  const breedingCount = animal.breedingCount || 0;
                  const ageLimit = RETIREMENT_AGE[animal.type] || 10;
                  const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

                  let retirementReason = '';
                  if (ageInYears >= ageLimit) {
                    retirementReason = 'Age';
                  } else if (breedingCount >= breedingLimit) {
                    retirementReason = 'Breeding Limit';
                  }

                  return (
                    <TableRow key={animal.id} hover>
                      <TableCell>{animal.tagNumber}</TableCell>
                      <TableCell>{animal.name}</TableCell>
                      <TableCell>{animal.type}</TableCell>
                      <TableCell>{ageInYears} years</TableCell>
                      <TableCell>{breedingCount}</TableCell>
                      <TableCell>
                        <Chip
                          label={retirementReason}
                          color={retirementReason === 'Age' ? 'primary' : 'secondary'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="primary"
                          component={Link}
                          to={`/animals/${animal.id}`}
                        >
                          <Visibility fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="secondary"
                          component={Link}
                          to={`${ROUTES.ASSET_MANAGEMENT}/retirement?id=${animal.id}`}
                        >
                          <Edit fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No retirement candidates found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Asset Value Summary */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          Asset Value Summary
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <BlendedBackgroundCard
              primaryColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              opacity={0.1}
            >
              <CardContent>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Value of Active Assets
                </Typography>
                <Typography variant="h4" component="div" fontWeight="bold">
                  R {assetStats.valueOfActiveAssets.toLocaleString()}
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TrendingUp fontSize="small" color="success" />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                    Productive asset value
                  </Typography>
                </Box>
              </CardContent>
            </BlendedBackgroundCard>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <BlendedBackgroundCard
              primaryColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              opacity={0.1}
            >
              <CardContent>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Value of Retired Assets
                </Typography>
                <Typography variant="h4" component="div" fontWeight="bold">
                  R {assetStats.valueOfRetiredAssets.toLocaleString()}
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <TrendingDown fontSize="small" color="error" />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                    Non-productive asset value
                  </Typography>
                </Box>
              </CardContent>
            </BlendedBackgroundCard>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <BlendedBackgroundCard
              primaryColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              opacity={0.1}
            >
              <CardContent>
                <Typography variant="body2" color="textSecondary" gutterBottom>
                  Total Asset Value
                </Typography>
                <Typography variant="h4" component="div" fontWeight="bold">
                  R {(assetStats.valueOfActiveAssets + assetStats.valueOfRetiredAssets).toLocaleString()}
                </Typography>
                <Box display="flex" alignItems="center" mt={1}>
                  <Assessment fontSize="small" color="primary" />
                  <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                    Total livestock value
                  </Typography>
                </Box>
              </CardContent>
            </BlendedBackgroundCard>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default AssetManagementDashboard;
