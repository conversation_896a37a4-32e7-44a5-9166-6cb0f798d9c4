import React, { useState, useEffect } from 'react';
import { Box, Card, CardContent, Typography, Switch, Grid, useTheme, alpha, Slider, Paper, IconButton, Tab, Tabs, Chip, Button, Divider } from '@mui/material';
import { useTabSelection } from '../../hooks/useTabSelection';
import { ThemeSafeWrapper } from '../common';
import { useThemeContext, ThemeColorKey } from '../../contexts/ThemeContext';
import {
  Palette,
  Animation,
  Refresh,
  ColorLens,
  Tune,
  Favorite,
  FavoriteBorder,
  AutoAwesome,
  GridView,
  ViewList,
  Image,
  FormatSize,
  Contrast,
  Check
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  // Use the tab selection hook to ensure tab panels are clickable
  const panelRef = useTabSelection();

  return (
    <ThemeSafeWrapper>
      <Box
        component="div"
        role="tabpanel"
        hidden={value !== index}
        id={`theme-tabpanel-${index}`}
        aria-labelledby={`theme-tab-${index}`}
        ref={panelRef}
        sx={{
          position: 'relative',
          zIndex: 5,
          pointerEvents: 'auto'
        }}
        {...other}
      >
        {value === index && (
          <Box sx={{ p: 3 }}>
            {children}
          </Box>
        )}
      </Box>
    </ThemeSafeWrapper>
  );
}

function a11yProps(index: number) {
  return {
    id: `theme-tab-${index}`,
    'aria-controls': `theme-tabpanel-${index}`,
  };
}

const EnhancedThemeSettings: React.FC = () => {
  const {
    blendMode,
    setBlendMode,
    currentColor,
    setThemeColor,
    availableColors,
    showThemeAnimations,
    toggleThemeAnimations,
    showBackgroundImages,
    toggleBackgroundImages
  } = useThemeContext();
  const theme = useTheme();

  // Use the tab selection hook to ensure tabs are clickable
  const tabsRef = useTabSelection();

  // Local state
  const [fontSize, setFontSize] = useState<number>(() => {
    const savedFontSize = localStorage.getItem('fontSize');
    return savedFontSize ? parseInt(savedFontSize) : 120; // Default to large (120%)
  });
  const [contrast, setContrast] = useState<number>(100);
  const [tabValue, setTabValue] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [favorites, setFavorites] = useState<ThemeColorKey[]>(() => {
    const saved = localStorage.getItem('favoriteColors');
    return saved ? JSON.parse(saved) : ['metalBlue', 'teal', 'emerald', 'violet', 'amber'];
  });

  // Group colors by category
  const colorCategories = {
    navyBlueGradients: ['midnightNavy', 'deepOcean', 'cosmicBlue', 'azureNight', 'sapphireGlow'],
    blues: ['teal', 'neonTeal', 'blue', 'metalBlue', 'darkBlue', 'navy', 'skyBlue', 'azure', 'dullBlue'],
    greens: ['emerald', 'green', 'darkGreen', 'lime', 'dullGreen', 'mint', 'forest'],
    purples: ['violet', 'purple', 'indigo', 'fuchsia', 'lavender', 'plum'],
    redsAndPinks: ['rose', 'maroon', 'crimson', 'pink', 'hotPink'],
    yellowsAndOranges: ['amber', 'orange', 'tangerine', 'gold', 'yellow'],
    earthTones: ['brown', 'earthBrown', 'coffee', 'sand'],
    neutrals: ['slate', 'graphite', 'charcoal'],
    gradients: ['oceanBreeze', 'sunsetGlow', 'forestMist', 'purpleHaze', 'goldenHour']
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle font size change
  const handleFontSizeChange = (_event: Event, newValue: number | number[]) => {
    setFontSize(newValue as number);
    document.documentElement.style.fontSize = `${newValue}%`;
  };

  // Handle contrast change
  const handleContrastChange = (_event: Event, newValue: number | number[]) => {
    setContrast(newValue as number);
    document.documentElement.style.setProperty('--contrast-factor', `${newValue}%`);
  };

  // Handle color selection
  const handleColorChange = (color: ThemeColorKey) => {
    setThemeColor(color);
  };

  // Toggle favorite color
  const toggleFavorite = (color: ThemeColorKey) => {
    setFavorites(prev => {
      if (prev.includes(color)) {
        return prev.filter(c => c !== color);
      } else {
        return [...prev, color];
      }
    });
  };

  // Save favorites to localStorage
  useEffect(() => {
    localStorage.setItem('favoriteColors', JSON.stringify(favorites));
  }, [favorites]);

  // Apply font size on component mount
  useEffect(() => {
    document.documentElement.style.fontSize = `${fontSize}%`;
  }, [fontSize]);

  // Apply settings
  const handleApplySettings = () => {
    // Apply font size
    document.documentElement.style.fontSize = `${fontSize}%`;

    // Apply animations
    document.documentElement.style.setProperty('--enable-animations', showThemeAnimations ? '1' : '0');

    // Apply contrast
    document.documentElement.style.setProperty('--contrast-factor', `${contrast}%`);

    // Save font size to localStorage
    localStorage.setItem('fontSize', fontSize.toString());
  };

  // Reset settings
  const handleResetSettings = () => {
    setFontSize(100);
    setContrast(100);
    document.documentElement.style.fontSize = '100%';
    document.documentElement.style.setProperty('--contrast-factor', '100%');
  };

  // Color item component
  const ColorItem = ({ color, category }: { color: ThemeColorKey, category?: string }) => {
    const colorData = availableColors[color];
    const isSelected = currentColor === color;
    const isFavorite = favorites.includes(color);

    return (
      <Box
        sx={{
          position: 'relative',
          mb: viewMode === 'list' ? 1 : 0
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: viewMode === 'list' ? 'row' : 'column',
            alignItems: viewMode === 'list' ? 'center' : 'flex-start',
            p: viewMode === 'list' ? 1 : 2,
            borderRadius: 2,
            border: `2px solid ${isSelected ? theme.palette.primary.main : 'transparent'}`,
            backgroundColor: alpha(theme.palette.background.paper, 0.7),
            backdropFilter: 'blur(8px)',
            boxShadow: isSelected
              ? `0 0 0 2px ${theme.palette.primary.main}, 0 4px 20px ${alpha(theme.palette.common.black, 0.15)}`
              : `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
            transition: 'all 0.3s ease',
            cursor: 'pointer',
            overflow: 'hidden',
            position: 'relative',
            pointerEvents: 'auto', // Ensure clickability
            zIndex: 10, // Ensure it's above other elements
            '&:hover': {
              boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.15)}`,
              backgroundColor: alpha(theme.palette.background.paper, 0.9),
              transform: 'scale(1.05)'
            },
            '&:active': {
              transform: 'scale(0.95)'
            },
            '&::before': isSelected ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              background: `linear-gradient(45deg, transparent, ${alpha(colorData.primary, 0.1)}, transparent)`,
              animation: 'shimmer 2s infinite'
            } : {}
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleColorChange(color);
          }}
        >
          <Box
            sx={{
              width: viewMode === 'list' ? 40 : '100%',
              height: viewMode === 'list' ? 40 : 100,
              borderRadius: viewMode === 'list' ? '50%' : 1,
              backgroundColor: colorData.primary,
              mb: viewMode === 'list' ? 0 : 2,
              mr: viewMode === 'list' ? 2 : 0,
              background: category === 'gradients'
                ? `linear-gradient(135deg, ${colorData.primary}, ${colorData.secondary})`
                : colorData.primary,
              boxShadow: `0 4px 12px ${alpha(colorData.primary, 0.3)}`
            }}
          />

          <Box sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography
              variant="subtitle2"
              fontWeight={isSelected ? 'bold' : 'medium'}
              sx={{
                color: isSelected ? theme.palette.primary.main : 'text.primary',
                mt: viewMode === 'list' ? 0 : 1
              }}
            >
              {colorData.name}
            </Typography>

            {viewMode === 'list' && (
              <Box sx={{
                display: 'flex',
                mt: 0.5,
                gap: 0.5
              }}>
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: colorData.primary }} />
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: colorData.secondary }} />
                <Box sx={{ width: 12, height: 12, borderRadius: '50%', backgroundColor: colorData.light }} />
              </Box>
            )}
          </Box>

          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              toggleFavorite(color);
            }}
            sx={{
              position: viewMode === 'list' ? 'relative' : 'absolute',
              top: viewMode === 'list' ? 'auto' : 8,
              right: viewMode === 'list' ? 'auto' : 8,
              color: isFavorite ? 'error.main' : 'text.secondary',
              backgroundColor: viewMode === 'list' ? 'transparent' : alpha(theme.palette.background.paper, 0.7),
              zIndex: 10,
              '&:hover': {
                backgroundColor: viewMode === 'list' ? 'transparent' : alpha(theme.palette.background.paper, 0.9),
                transform: 'scale(1.2)'
              },
              '&:active': {
                transform: 'scale(0.9)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            {isFavorite ? <Favorite fontSize="small" /> : <FavoriteBorder fontSize="small" />}
          </IconButton>
        </Box>
      </Box>
    );
  };

  return (
    <ThemeSafeWrapper>
      <Card sx={{
      borderRadius: '12px',
      overflow: 'hidden',
      mb: 4,
      boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
    }}>
      <Box sx={{
        p: 2,
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
        color: 'white',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
          <ColorLens sx={{ mr: 1 }} /> Theme Settings
        </Typography>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            size="small"
            onClick={() => setViewMode(prev => prev === 'grid' ? 'list' : 'grid')}
            sx={{ color: 'white' }}
          >
            {viewMode === 'grid' ? <ViewList /> : <GridView />}
          </IconButton>
        </Box>
      </Box>

      <Box sx={{ borderBottom: 1, borderColor: 'divider' }} ref={tabsRef}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
          sx={{
            '& .MuiTab-root': {
              minWidth: 'auto',
              px: 2,
              position: 'relative',
              zIndex: 10,
              pointerEvents: 'auto',
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.05),
                transform: 'scale(1.02)'
              },
              '&:active': {
                transform: 'scale(0.98)'
              }
            },
            '& .MuiTabs-indicator': {
              zIndex: 5
            }
          }}
        >
          <Tab
            icon={<Favorite />}
            iconPosition="start"
            label="Favorites"
            {...a11yProps(0)}
          />
          <Tab
            icon={<Palette />}
            iconPosition="start"
            label="All Colors"
            {...a11yProps(1)}
          />
          <Tab
            icon={<Tune />}
            iconPosition="start"
            label="Appearance"
            {...a11yProps(2)}
          />
          <Tab
            icon={<AutoAwesome />}
            iconPosition="start"
            label="Gradients"
            {...a11yProps(3)}
          />
        </Tabs>
      </Box>

      <CardContent>
        <TabPanel value={tabValue} index={0}>
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
            Your Favorite Colors
          </Typography>

          <Grid container spacing={2}>
            {favorites.length > 0 ? (
              favorites.map((color) => (
                <Grid item xs={viewMode === 'list' ? 12 : 6} sm={viewMode === 'list' ? 12 : 4} md={viewMode === 'list' ? 12 : 3} key={color}>
                  <ColorItem color={color} />
                </Grid>
              ))
            ) : (
              <Grid item xs={12}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    backgroundColor: alpha(theme.palette.background.paper, 0.7),
                    backdropFilter: 'blur(8px)'
                  }}
                >
                  <Typography variant="body1" color="text.secondary">
                    You haven't added any favorites yet. Click the heart icon on any color to add it to your favorites.
                  </Typography>
                </Paper>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {Object.entries(colorCategories).map(([category, colors]) => (
            <Box key={category} sx={{ mb: 4 }}>
              <Typography variant="subtitle1" fontWeight="medium" sx={{
                mb: 2,
                textTransform: 'capitalize',
                display: 'flex',
                alignItems: 'center'
              }}>
                <AutoAwesome sx={{ mr: 1, fontSize: 18 }} />
                {category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                <Chip
                  size="small"
                  label={colors.length}
                  sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                />
              </Typography>

              <Grid container spacing={2}>
                {colors.map((color) => (
                  <Grid item xs={viewMode === 'list' ? 12 : 6} sm={viewMode === 'list' ? 12 : 4} md={viewMode === 'list' ? 12 : 3} key={color}>
                    <ColorItem color={color as ThemeColorKey} category={category} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          ))}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            {/* Theme Blend Mode */}
            <Grid item xs={12}>
              <Box sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.7),
                backdropFilter: 'blur(8px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AutoAwesome sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle1" fontWeight="medium">Theme Style</Typography>
                </Box>

                <Grid container spacing={1}>
                  {[
                    { value: 'light', label: 'Light', icon: '☀️', description: 'Clean light theme' },
                    { value: 'dark', label: 'Dark', icon: '🌙', description: 'Dark theme for low light' },
                    { value: 'lightBlended', label: 'Light Blended', icon: '✨', description: 'Light with gradient backgrounds' },
                    { value: 'darkBlended', label: 'Dark Blended', icon: '🌟', description: 'Dark with gradient backgrounds' },
                    { value: 'day', label: 'Day Mode', icon: '🌅', description: 'Warm daylight theme' },
                    { value: 'night', label: 'Night Mode', icon: '🌃', description: 'Cool nighttime theme' },
                    { value: 'auto', label: 'Auto Mode', icon: '🕐', description: 'Changes based on time of day' }
                  ].map((option) => (
                    <Grid item xs={6} sm={4} md={3} lg={2} key={option.value}>
                      <Paper
                        sx={{
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          border: `2px solid ${blendMode === option.value ? theme.palette.primary.main : 'transparent'}`,
                          backgroundColor: blendMode === option.value
                            ? alpha(theme.palette.primary.main, 0.1)
                            : alpha(theme.palette.background.paper, 0.5),
                          transition: 'all 0.3s ease',
                          pointerEvents: 'auto',
                          zIndex: 10,
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.05),
                            transform: 'scale(1.02)'
                          },
                          '&:active': {
                            transform: 'scale(0.98)'
                          }
                        }}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setBlendMode(option.value as any);
                        }}
                      >
                        <Typography variant="h4" sx={{ mb: 1 }}>
                          {option.icon}
                        </Typography>
                        <Typography variant="subtitle2" fontWeight="medium" sx={{ mb: 0.5 }}>
                          {option.label}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {option.description}
                        </Typography>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Grid>

            {/* Animations */}
            <Grid item xs={12}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2,
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.7),
                backdropFilter: 'blur(8px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Animation sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle1" fontWeight="medium">Enable Animations</Typography>
                </Box>
                <Switch
                  checked={showThemeAnimations}
                  onChange={toggleThemeAnimations}
                  color="primary"
                />
              </Box>
            </Grid>

            {/* Background Images */}
            <Grid item xs={12}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2,
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.7),
                backdropFilter: 'blur(8px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Image sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle1" fontWeight="medium">Show Background Images</Typography>
                </Box>
                <Switch
                  checked={showBackgroundImages}
                  onChange={toggleBackgroundImages}
                  color="primary"
                />
              </Box>
            </Grid>

            {/* Font Size */}
            <Grid item xs={12}>
              <Box sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.7),
                backdropFilter: 'blur(8px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <FormatSize sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle1" fontWeight="medium">Font Size</Typography>
                </Box>
                <Slider
                  value={fontSize}
                  onChange={handleFontSizeChange}
                  aria-labelledby="font-size-slider"
                  valueLabelDisplay="auto"
                  step={10}
                  marks
                  min={70}
                  max={150}
                  sx={{ mt: 2 }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary">Smaller</Typography>
                  <Typography variant="caption" color="text.secondary">Larger</Typography>
                </Box>
              </Box>
            </Grid>

            {/* Contrast */}
            <Grid item xs={12}>
              <Box sx={{
                p: 2,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.paper, 0.7),
                backdropFilter: 'blur(8px)',
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Contrast sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="subtitle1" fontWeight="medium">Contrast</Typography>
                </Box>
                <Slider
                  value={contrast}
                  onChange={handleContrastChange}
                  aria-labelledby="contrast-slider"
                  valueLabelDisplay="auto"
                  step={10}
                  marks
                  min={70}
                  max={130}
                  sx={{ mt: 2 }}
                />
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                  <Typography variant="caption" color="text.secondary">Lower</Typography>
                  <Typography variant="caption" color="text.secondary">Higher</Typography>
                </Box>
              </Box>
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
                <Button
                  variant="outlined"
                  onClick={handleResetSettings}
                  startIcon={<Refresh />}
                >
                  Reset
                </Button>
                <Button
                  variant="contained"
                  onClick={handleApplySettings}
                  startIcon={<Check />}
                >
                  Apply Settings
                </Button>
              </Box>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 3 }}>
            Gradient Color Themes
          </Typography>

          <Grid container spacing={2}>
            {colorCategories.gradients.concat(colorCategories.navyBlueGradients).map((color) => (
              <Grid item xs={viewMode === 'list' ? 12 : 6} sm={viewMode === 'list' ? 12 : 4} md={viewMode === 'list' ? 12 : 3} key={color}>
                <ColorItem color={color as ThemeColorKey} category="gradients" />
              </Grid>
            ))}
          </Grid>

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
            Gradient Preview
          </Typography>

          <Paper
            sx={{
              p: 3,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${availableColors[currentColor].primary}, ${availableColors[currentColor].secondary})`,
              color: 'white',
              textAlign: 'center',
              minHeight: 120,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.1)',
                zIndex: 1
              }
            }}
          >
            <Box sx={{ position: 'relative', zIndex: 2 }}>
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
                {availableColors[currentColor].name}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                Current gradient theme preview
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 2 }}>
                <Chip
                  label={`Primary: ${availableColors[currentColor].primary}`}
                  size="small"
                  sx={{ backgroundColor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
                />
                <Chip
                  label={`Secondary: ${availableColors[currentColor].secondary}`}
                  size="small"
                  sx={{ backgroundColor: 'rgba(255, 255, 255, 0.2)', color: 'white' }}
                />
              </Box>
            </Box>
          </Paper>

          <Box sx={{ mt: 3, p: 2, backgroundColor: alpha(theme.palette.background.paper, 0.7), borderRadius: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              💡 <strong>Tip:</strong> Gradient themes work best with "Light Blended" or "Dark Blended" theme styles.
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Switch to the Appearance tab to enable blended backgrounds for the full gradient experience.
            </Typography>
          </Box>
        </TabPanel>
      </CardContent>
    </Card>
    </ThemeSafeWrapper>
  );
};

export default EnhancedThemeSettings;
