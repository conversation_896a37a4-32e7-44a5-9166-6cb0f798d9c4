import React from 'react';
import { Box, useMediaQuery, useTheme, BoxProps } from '@mui/material';

interface ResponsiveLayoutProps extends BoxProps {
  children: React.ReactNode;
  mobileBreakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  mobileLayout?: 'stack' | 'compact' | 'scroll' | 'adaptive';
  spacing?: number;
  mobileSpacing?: number;
  desktopSpacing?: number;
  centerContent?: boolean;
  fullHeight?: boolean;
  fullWidth?: boolean;
  maxWidth?: string | number;
  minHeight?: string | number;
  mobilePadding?: number | string;
  desktopPadding?: number | string;
  mobileDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  desktopDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
}

/**
 * A responsive layout component that adapts to different screen sizes
 */
const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  mobileBreakpoint = 'sm',
  mobileLayout = 'stack',
  spacing = 2,
  mobileSpacing = 1,
  desktopSpacing = 2,
  centerContent = false,
  fullHeight = false,
  fullWidth = true,
  maxWidth,
  minHeight,
  mobilePadding = 2,
  desktopPadding = 3,
  mobileDirection = 'column',
  desktopDirection = 'row',
  sx,
  ...boxProps
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(mobileBreakpoint));

  // Determine layout based on screen size and layout type
  const getFlexDirection = () => {
    if (isMobile) {
      return mobileDirection;
    }
    return desktopDirection;
  };

  // Determine spacing based on screen size
  const getSpacing = () => {
    if (isMobile) {
      return mobileSpacing;
    }
    return desktopSpacing;
  };

  // Determine padding based on screen size
  const getPadding = () => {
    if (isMobile) {
      return mobilePadding;
    }
    return desktopPadding;
  };

  // Get overflow style based on layout type
  const getOverflow = () => {
    if (isMobile && mobileLayout === 'scroll') {
      return 'auto';
    }
    return 'visible';
  };

  // Get flex wrap based on layout type
  const getFlexWrap = () => {
    if (isMobile && mobileLayout === 'adaptive') {
      return 'wrap';
    }
    return 'nowrap';
  };

  return (
    <Box
      display="flex"
      flexDirection={getFlexDirection()}
      gap={getSpacing()}
      padding={getPadding()}
      justifyContent={centerContent ? 'center' : 'flex-start'}
      alignItems={centerContent ? 'center' : 'stretch'}
      width={fullWidth ? '100%' : 'auto'}
      height={fullHeight ? '100%' : 'auto'}
      maxWidth={maxWidth}
      minHeight={minHeight}
      overflow={getOverflow()}
      flexWrap={getFlexWrap()}
      sx={{
        ...sx,
        ...(isMobile && mobileLayout === 'compact' && {
          '& > *': {
            flex: '0 0 auto',
          },
        }),
      }}
      {...boxProps}
    >
      {children}
    </Box>
  );
};

export default ResponsiveLayout;
