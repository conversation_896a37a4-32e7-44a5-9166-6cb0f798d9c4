/**
 * <PERSON><PERSON>t to run unit tests for the frontend application
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const testPatterns = [
  'src/components/common/*.test.tsx',
  'src/hooks/*.test.tsx'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

/**
 * Run tests with the specified pattern
 * @param {string} pattern Test file pattern
 */
function runTests(pattern) {
  console.log(`${colors.fg.cyan}Running tests matching: ${colors.bright}${pattern}${colors.reset}`);
  
  try {
    const command = `npx jest ${pattern} --verbose`;
    execSync(command, { stdio: 'inherit' });
    console.log(`${colors.fg.green}✓ Tests for ${pattern} completed successfully${colors.reset}`);
    return true;
  } catch (error) {
    console.error(`${colors.fg.red}✗ Tests for ${pattern} failed${colors.reset}`);
    console.error(error.message);
    return false;
  }
}

/**
 * Main function to run all tests
 */
function main() {
  console.log(`${colors.bright}${colors.fg.cyan}=== Running Frontend Unit Tests ===${colors.reset}\n`);
  
  let allTestsPassed = true;
  
  // Run tests for each pattern
  for (const pattern of testPatterns) {
    const success = runTests(pattern);
    if (!success) {
      allTestsPassed = false;
    }
    console.log(); // Add a blank line between test runs
  }
  
  // Print summary
  if (allTestsPassed) {
    console.log(`${colors.fg.green}${colors.bright}All tests passed successfully!${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`${colors.fg.red}${colors.bright}Some tests failed. Please check the output above.${colors.reset}`);
    process.exit(1);
  }
}

// Run the main function
main();
