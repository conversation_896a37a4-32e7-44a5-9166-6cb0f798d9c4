/* Background styles for AgriIntel Livestock Management System */

.bg-farm-landscape {
  background-image: url('https://wallpaperbat.com/img/178606-on-the-farm.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-cattle-herd {
  background-image: url('https://www.wallpaperflare.com/static/181/697/179/curiosity-herd-black-and-white-cattle-wallpaper.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Removed bg-livestock-1 through bg-livestock-5 */

/* Overlay styles for better text readability */
.bg-overlay-dark {
  position: relative;
}

.bg-overlay-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.bg-overlay-dark > * {
  position: relative;
  z-index: 2;
}

.bg-overlay-light {
  position: relative;
}

.bg-overlay-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

.bg-overlay-light > * {
  position: relative;
  z-index: 2;
}

/* Gradient overlays */
.bg-gradient-primary {
  background: linear-gradient(135deg, rgba(15, 118, 110, 0.9), rgba(15, 118, 110, 0.6));
}

.bg-gradient-dark {
  background: linear-gradient(135deg, rgba(55, 65, 81, 0.9), rgba(17, 24, 39, 0.8));
}

/* Background patterns */
.bg-pattern-1 {
  background-color: #f3f4f6;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%230f766e' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}

.bg-pattern-2 {
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230f766e' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Card with background image */
.card-with-bg {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-with-bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  z-index: 0;
}

.card-with-bg-content {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.85);
  padding: 1.5rem;
  height: 100%;
}

/* Dashboard background */
.dashboard-bg {
  background-color: #f9fafb;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230f766e' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
