import React, { useState, useEffect } from 'react';
import { Box, Typography, Link, useTheme, alpha, IconButton } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowBackIos, ArrowForwardIos } from '@mui/icons-material';

export interface Sponsor {
  id: string;
  name: string;
  logoUrl: string;
  websiteUrl: string;
  description?: string;
}

interface SponsorCarouselProps {
  sponsors: Sponsor[];
  autoRotateInterval?: number; // in milliseconds
  autoRotate?: boolean;
  rotationInterval?: number; // in milliseconds
  title?: string;
  subtitle?: string;
  height?: number;
  showControls?: boolean;
  showTitle?: boolean;
  overlay?: boolean;
}

const SponsorCarousel: React.FC<SponsorCarouselProps> = ({
  sponsors,
  autoRotateInterval = 10000, // Default to 10 seconds
  autoRotate = true,
  rotationInterval,
  title = "Our Partners",
  subtitle = "Useful resources for farmers",
  height = 120,
  showControls = false,
  showTitle = true,
  overlay = false
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const theme = useTheme();

  // Auto-rotate sponsors
  useEffect(() => {
    if (!autoRotate || sponsors.length <= 1 || isHovered) return;

    // Use rotationInterval if provided, otherwise fall back to autoRotateInterval
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % sponsors.length);
    }, rotationInterval || autoRotateInterval);

    return () => clearInterval(interval);
  }, [sponsors.length, autoRotateInterval, rotationInterval, autoRotate, isHovered]);

  // Handle manual navigation
  const goToSponsor = (index: number) => {
    setCurrentIndex(index);
  };

  // Handle opening sponsor website
  const openSponsorWebsite = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  if (!sponsors || sponsors.length === 0) {
    return null;
  }

  return (
    <Box
      sx={{
        width: '100%',
        overflow: 'hidden',
        borderRadius: '12px',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        bgcolor: alpha(theme.palette.background.paper, 0.8),
        backdropFilter: 'blur(8px)',
        p: showTitle ? 3 : 0,
      }}
    >
      {showTitle && (
        <Box mb={2}>
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            {title}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            {subtitle}
          </Typography>
        </Box>
      )}

      <Box
        sx={{
          position: 'relative',
          height: height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden',
          borderRadius: '8px',
          bgcolor: 'rgba(255,255,255,0.2)',
          backdropFilter: 'blur(5px)',
          boxShadow: 'inset 0 0 0 1px rgba(255,255,255,0.1)',
          padding: 2
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {showControls && (
          <>
            <IconButton
              sx={{
                position: 'absolute',
                left: 8,
                zIndex: 2,
                bgcolor: 'rgba(255,255,255,0.7)',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' },
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                width: 36,
                height: 36
              }}
              onClick={(e) => {
                e.stopPropagation();
                setCurrentIndex((prevIndex) => (prevIndex === 0 ? sponsors.length - 1 : prevIndex - 1));
              }}
            >
              <ArrowBackIos fontSize="small" />
            </IconButton>
            <IconButton
              sx={{
                position: 'absolute',
                right: 8,
                zIndex: 2,
                bgcolor: 'rgba(255,255,255,0.7)',
                '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' },
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                width: 36,
                height: 36
              }}
              onClick={(e) => {
                e.stopPropagation();
                setCurrentIndex((prevIndex) => (prevIndex === sponsors.length - 1 ? 0 : prevIndex + 1));
              }}
            >
              <ArrowForwardIos fontSize="small" />
            </IconButton>
          </>
        )}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
            }}
            onClick={() => openSponsorWebsite(sponsors[currentIndex].websiteUrl)}
          >
            <Box
              sx={{
                height: '100%',
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  height: height - 60, // Fixed height for image container
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(255,255,255,0.85)',
                  borderRadius: '8px',
                  padding: 2,
                  mb: 2,
                  boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
                  border: '1px solid rgba(255,255,255,0.9)'
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                  }}
                >
                  <img
                    src={sponsors[currentIndex].logoUrl}
                    alt={sponsors[currentIndex].name}
                    style={{
                      maxWidth: '90%',
                      maxHeight: '90%',
                      objectFit: 'contain',
                      display: 'block',
                    }}
                  />
                </Box>
              </Box>
              <Typography
                variant="subtitle2"
                color="textSecondary"
                align="center"
                fontWeight="medium"
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.85)',
                  borderRadius: '4px',
                  padding: '6px 12px',
                  width: 'fit-content',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                  border: '1px solid rgba(255,255,255,0.9)',
                  maxWidth: '90%',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
              >
                {sponsors[currentIndex].name}
              </Typography>
            </Box>
          </motion.div>
        </AnimatePresence>
      </Box>

      {/* Navigation dots */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 2,
        }}
      >
        {sponsors.map((_, index) => (
          <Box
            key={index}
            sx={{
              width: 8,
              height: 8,
              borderRadius: '50%',
              mx: 0.5,
              bgcolor: index === currentIndex ? theme.palette.primary.main : alpha(theme.palette.primary.main, 0.3),
              cursor: 'pointer',
              transition: 'all 0.3s ease',
            }}
            onClick={() => goToSponsor(index)}
          />
        ))}
      </Box>

      <Box mt={1} textAlign="center">
        <Link
          href={sponsors[currentIndex].websiteUrl}
          target="_blank"
          rel="noopener noreferrer"
          underline="hover"
          color="primary"
          sx={{ fontSize: '0.875rem' }}
        >
          Visit Website
        </Link>
      </Box>
    </Box>
  );
};

export default SponsorCarousel;
