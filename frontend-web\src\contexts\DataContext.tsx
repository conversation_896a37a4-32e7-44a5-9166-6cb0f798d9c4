import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAnimalData } from '../hooks/useAnimalData';
import { useBreedingRecords } from '../hooks/useBreedingRecords';
import { useHealthRecords } from '../hooks/useHealthRecords';
import { useCommercialData } from '../hooks/useCommercialData';
import { useFeedingData } from '../hooks/useFeedingData';
import { useInventoryData } from '../hooks/useInventoryData';
import { useFinancialData } from '../hooks/useFinancialData';
import { useComplianceData } from '../hooks/useComplianceData';
import { useResourcesData } from '../hooks/useResourcesData';

// Create a context for all data
const DataContext = createContext<any>(null);

// Create a provider component
export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Use all data hooks
  const animalData = useAnimalData();
  const breedingData = useBreedingRecords();
  const healthData = useHealthRecords();
  const commercialData = useCommercialData();
  const feedingData = useFeedingData();
  const inventoryData = useInventoryData();
  const financialData = useFinancialData();
  const complianceData = useComplianceData();
  const resourcesData = useResourcesData();

  // Combine all data
  const value = {
    animalData,
    breedingData,
    healthData,
    commercialData,
    feedingData,
    inventoryData,
    financialData,
    complianceData,
    resourcesData,

    // Add a method to refresh all data
    refreshAllData: async () => {
      try {
        // Use the correct method names for each data context based on their actual implementations
        if (animalData.refetch) await animalData.refetch();

        // For breeding data - no explicit fetch method, data is loaded in useEffect
        // We can trigger a re-render by updating a state variable if needed

        // For health data
        if (healthData.fetchHealthData) await healthData.fetchHealthData();

        // For commercial data
        if (commercialData.fetchCommercialData) await commercialData.fetchCommercialData();

        // For feeding data
        if (feedingData.fetchFeedingData) await feedingData.fetchFeedingData();

        // For inventory data
        if (inventoryData.fetchInventory) await inventoryData.fetchInventory();

        // For financial data
        if (financialData.fetchFinancialData) await financialData.fetchFinancialData();

        // For compliance data
        if (complianceData.fetchComplianceData) await complianceData.fetchComplianceData();

        // For resources data
        if (resourcesData.fetchResourcesData) await resourcesData.fetchResourcesData();
      } catch (error) {
        console.error('Error refreshing data:', error);
      }
    }
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

// Create a hook to use the data context
export const useData = () => {
  const context = useContext(DataContext);
  if (context === null) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

// Create hooks for specific data
export const useAnimalDataContext = () => {
  const { animalData } = useData();
  return animalData;
};

export const useBreedingDataContext = () => {
  const { breedingData } = useData();
  return breedingData;
};

export const useHealthDataContext = () => {
  const { healthData } = useData();
  return healthData;
};

export const useCommercialDataContext = () => {
  const { commercialData } = useData();
  return commercialData;
};

export const useFeedingDataContext = () => {
  const { feedingData } = useData();
  return feedingData;
};

export const useInventoryDataContext = () => {
  const { inventoryData } = useData();
  return inventoryData;
};

export const useFinancialDataContext = () => {
  const { financialData } = useData();
  return financialData;
};

export const useComplianceDataContext = () => {
  const { complianceData } = useData();
  return complianceData;
};

export const useResourcesDataContext = () => {
  const { resourcesData } = useData();
  return resourcesData;
};
