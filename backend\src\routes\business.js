/**
 * business Routes
 * 
 * API routes for business module
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const businessController = require('../controllers/businessController');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * GET /api/business
 * 
 * @route GET /
 * @group business - business operations
 * @returns {object} 200 - Success response
 */
router.get('/', businessController.getAll);

/**
 * GET /api/business/:id
 * 
 * @route GET /:id
 * @group business - business operations
 * @returns {object} 200 - Success response
 */
router.get('/:id', businessController.getById);

/**
 * POST /api/business
 * 
 * @route POST /
 * @group business - business operations
 * @returns {object} 201 - Created response
 */
router.post('/', businessController.create);

/**
 * PUT /api/business/:id
 * 
 * @route PUT /:id
 * @group business - business operations
 * @returns {object} 200 - Success response
 */
router.put('/:id', businessController.update);

/**
 * DELETE /api/business/:id
 * 
 * @route DELETE /:id
 * @group business - business operations
 * @returns {object} 200 - Success response
 */
router.delete('/:id', businessController.delete);

module.exports = router;
