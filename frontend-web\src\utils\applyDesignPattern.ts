import { Theme, alpha } from '@mui/material';

/**
 * Utility functions for applying consistent design patterns across all modules
 */

// List of modules that should keep their original styling
const EXCLUDED_MODULES = ['dashboard', 'login', 'commercial'];

// Metallic blue color scheme for all other modules
const METALLIC_BLUE = {
  primary: '#4A6FA5',
  secondary: '#3A5A8C',
};

/**
 * Check if a module is excluded from the design pattern
 * @param module The module name
 * @returns True if the module is excluded, false otherwise
 */
export const isExcludedModule = (module: string): boolean => {
  return EXCLUDED_MODULES.includes(module.toLowerCase());
};

/**
 * Get the appropriate color scheme for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns The primary and secondary colors for the module
 */
export const getModuleColors = (module: string, theme: Theme) => {
  const excluded = isExcludedModule(module);
  
  return {
    primary: excluded ? theme.palette.primary.main : METALLIC_BLUE.primary,
    secondary: excluded ? theme.palette.primary.dark : METALLIC_BLUE.secondary,
  };
};

/**
 * Get the gradient background style for a card
 * @param module The module name
 * @param theme The Material UI theme
 * @param opacity The opacity of the gradient (0-1)
 * @returns CSS properties for the gradient background
 */
export const getGradientBackground = (module: string, theme: Theme, opacity: number = 0.85) => {
  const colors = getModuleColors(module, theme);
  
  return {
    background: `linear-gradient(135deg, ${alpha(colors.primary, opacity)}, ${alpha(colors.secondary, opacity)})`,
    color: 'white',
  };
};

/**
 * Get the card style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the card
 */
export const getCardStyle = (module: string, theme: Theme) => {
  return {
    borderRadius: '12px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    overflow: 'hidden',
    ...getGradientBackground(module, theme),
  };
};

/**
 * Get the table header style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the table header
 */
export const getTableHeaderStyle = (module: string, theme: Theme) => {
  return {
    bgcolor: 'rgba(255,255,255,0.1)',
    '& .MuiTableCell-root': {
      color: 'white',
      fontWeight: 'bold',
    },
  };
};

/**
 * Get the table row style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the table row
 */
export const getTableRowStyle = (module: string, theme: Theme) => {
  return {
    '&:hover': { 
      bgcolor: 'rgba(255,255,255,0.1)' 
    },
    '& .MuiTableCell-root': {
      color: 'white',
    },
  };
};

/**
 * Get the chip style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @param color Optional color variant
 * @returns CSS properties for the chip
 */
export const getChipStyle = (module: string, theme: Theme, color?: string) => {
  let bgColor = 'rgba(255,255,255,0.2)';
  
  if (color === 'warning') {
    bgColor = 'rgba(255,193,7,0.2)';
  } else if (color === 'success') {
    bgColor = 'rgba(76,175,80,0.2)';
  } else if (color === 'error') {
    bgColor = 'rgba(244,67,54,0.2)';
  }
  
  return {
    bgcolor: bgColor,
    color: 'white',
  };
};

/**
 * Get the chart style for a module
 * @param module The module name
 * @returns CSS properties for charts
 */
export const getChartStyle = (module: string) => {
  return {
    '& .recharts-polar-grid-line': {
      stroke: 'rgba(255,255,255,0.3)',
    },
    '& .recharts-polar-angle-axis-tick-value': {
      fill: 'white',
    },
    '& .recharts-polar-radius-axis-tick-value': {
      fill: 'white',
    },
    '& .recharts-legend-item-text': {
      color: 'white',
    },
  };
};

/**
 * Apply the design pattern to a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns An object with all the styling functions for the module
 */
export const applyDesignPattern = (module: string, theme: Theme) => {
  return {
    isExcluded: isExcludedModule(module),
    colors: getModuleColors(module, theme),
    gradientBackground: getGradientBackground(module, theme),
    cardStyle: getCardStyle(module, theme),
    tableHeaderStyle: getTableHeaderStyle(module, theme),
    tableRowStyle: getTableRowStyle(module, theme),
    chipStyle: getChipStyle(module, theme),
    chartStyle: getChartStyle(module),
  };
};

export default applyDesignPattern;
