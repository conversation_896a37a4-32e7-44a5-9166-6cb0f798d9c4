import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import duration from 'dayjs/plugin/duration';

// Register plugins
dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(advancedFormat);
dayjs.extend(customParseFormat);
dayjs.extend(isBetween);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(weekOfYear);
dayjs.extend(quarterOfYear);
dayjs.extend(duration);

// Set default timezone to South Africa
dayjs.tz.setDefault('Africa/Johannesburg');

/**
 * Format a date in a localized format
 * @param date The date to format
 * @param format The format string (default: 'DD MMM YYYY')
 * @returns Formatted date string
 */
export const formatDate = (date: Date | string | null | undefined, format: string = 'DD MMM YYYY'): string => {
  if (!date) return '';
  return dayjs(date).format(format);
};

/**
 * Format a date as a relative time (e.g., "2 days ago")
 * @param date The date to format
 * @returns Relative time string
 */
export const formatRelativeTime = (date: Date | string | null | undefined): string => {
  if (!date) return '';
  return dayjs(date).fromNow();
};

/**
 * Check if a date is in the past
 * @param date The date to check
 * @returns True if the date is in the past
 */
export const isPastDate = (date: Date | string | null | undefined): boolean => {
  if (!date) return false;
  return dayjs(date).isBefore(dayjs());
};

/**
 * Check if a date is in the future
 * @param date The date to check
 * @returns True if the date is in the future
 */
export const isFutureDate = (date: Date | string | null | undefined): boolean => {
  if (!date) return false;
  return dayjs(date).isAfter(dayjs());
};

/**
 * Calculate the difference between two dates in days
 * @param date1 The first date
 * @param date2 The second date (default: current date)
 * @returns Number of days between the dates
 */
export const daysBetween = (
  date1: Date | string | null | undefined,
  date2: Date | string | null | undefined = new Date()
): number => {
  if (!date1 || !date2) return 0;
  return Math.abs(dayjs(date1).diff(dayjs(date2), 'day'));
};

/**
 * Get the start of a period
 * @param date The reference date
 * @param unit The unit (day, week, month, quarter, year)
 * @returns Date at the start of the period
 */
export const startOf = (
  date: Date | string | null | undefined,
  unit: 'day' | 'week' | 'month' | 'quarter' | 'year'
): Date => {
  if (!date) return dayjs().startOf(unit).toDate();
  return dayjs(date).startOf(unit).toDate();
};

/**
 * Get the end of a period
 * @param date The reference date
 * @param unit The unit (day, week, month, quarter, year)
 * @returns Date at the end of the period
 */
export const endOf = (
  date: Date | string | null | undefined,
  unit: 'day' | 'week' | 'month' | 'quarter' | 'year'
): Date => {
  if (!date) return dayjs().endOf(unit).toDate();
  return dayjs(date).endOf(unit).toDate();
};

/**
 * Add a duration to a date
 * @param date The reference date
 * @param amount The amount to add
 * @param unit The unit (day, week, month, year)
 * @returns The new date
 */
export const addToDate = (
  date: Date | string | null | undefined,
  amount: number,
  unit: 'day' | 'week' | 'month' | 'year'
): Date => {
  if (!date) return dayjs().add(amount, unit).toDate();
  return dayjs(date).add(amount, unit).toDate();
};

/**
 * Subtract a duration from a date
 * @param date The reference date
 * @param amount The amount to subtract
 * @param unit The unit (day, week, month, year)
 * @returns The new date
 */
export const subtractFromDate = (
  date: Date | string | null | undefined,
  amount: number,
  unit: 'day' | 'week' | 'month' | 'year'
): Date => {
  if (!date) return dayjs().subtract(amount, unit).toDate();
  return dayjs(date).subtract(amount, unit).toDate();
};

/**
 * Format a date range
 * @param startDate The start date
 * @param endDate The end date
 * @param format The format for individual dates (default: 'DD MMM YYYY')
 * @returns Formatted date range string
 */
export const formatDateRange = (
  startDate: Date | string | null | undefined,
  endDate: Date | string | null | undefined,
  format: string = 'DD MMM YYYY'
): string => {
  if (!startDate || !endDate) return '';
  return `${formatDate(startDate, format)} - ${formatDate(endDate, format)}`;
};

export default {
  formatDate,
  formatRelativeTime,
  isPastDate,
  isFutureDate,
  daysBetween,
  startOf,
  endOf,
  addToDate,
  subtractFromDate,
  formatDateRange
};
