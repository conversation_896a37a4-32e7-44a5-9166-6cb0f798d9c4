import { RFIDTag, mockRFIDTags, generateMockLocationHistory } from '../mocks/rfidData';

interface LocationHistoryPoint {
  timestamp: string;
  signalStrength: number;
  temperature: number;
}

const rfidService = {
  getTags: async (): Promise<RFIDTag[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockRFIDTags;
  },

  getLocationHistory: async (tagId: string, timeRange: number): Promise<LocationHistoryPoint[]> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    return generateMockLocationHistory(tagId, timeRange);
  },

  registerTags: async (tags: { tagId: string; animalId: string }[]): Promise<void> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    // In a real implementation, this would make an API call to register the tags
  },

  addListener: (callback: (data: any) => void) => {
    // Simulate real-time updates every 30 seconds
    const interval = setInterval(() => {
      const randomTag = mockRFIDTags[Math.floor(Math.random() * mockRFIDTags.length)];
      callback({
        type: 'TAG_UPDATE',
        tagId: randomTag.id,
        updates: {
          lastRead: new Date().toISOString(),
          batteryLevel: Math.max(0, randomTag.batteryLevel - Math.random() * 5),
          temperature: 20 + Math.random() * 5,
          humidity: 60 + Math.random() * 10
        }
      });
    }, 30000);

    return () => {
      clearInterval(interval);
    };
  }
};

export default rfidService;
