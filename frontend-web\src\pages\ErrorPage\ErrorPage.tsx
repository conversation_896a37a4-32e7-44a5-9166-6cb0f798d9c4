import React from 'react';
import { useNavigate } from 'react-router-dom';

const ErrorPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-6xl font-bold">404</h1>
      <h2 className="text-2xl mt-4">Page Not Found</h2>
      <p className="text-gray-600 mt-2 max-w-md text-center">
        The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
      </p>
      <button
        className="mt-8 px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
        onClick={() => navigate('/')}
      >
        Go Home
      </button>
    </div>
  );
};

export default ErrorPage;