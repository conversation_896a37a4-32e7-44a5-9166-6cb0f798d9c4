import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Snackbar, Alert, Badge, Tooltip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, Typography, List, ListItem, ListItemText, ListItemIcon, Divider, CircularProgress, useTheme, alpha } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { useOffline } from '../../contexts/OfflineContext';
import { useLanguage } from '../../contexts/LanguageContext';
import WifiOffIcon from '@mui/icons-material/WifiOff';
import CloudOffIcon from '@mui/icons-material/CloudOff';
import CloudSyncIcon from '@mui/icons-material/CloudSync';
import CloudDoneIcon from '@mui/icons-material/CloudDone';
import WarningIcon from '@mui/icons-material/Warning';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { formatRelativeTime } from '../../utils/dateUtils';

/**
 * OfflineIndicator component
 * Displays an indicator when the application is offline
 * and provides synchronization functionality
 */
const OfflineIndicator: React.FC = () => {
  const theme = useTheme();
  const { isOnline, pendingChanges, lastSyncTime, syncInProgress, synchronize } = useOffline();
  const { translate } = useLanguage();
  const [showSnackbar, setShowSnackbar] = useState<boolean>(false);
  const [showDialog, setShowDialog] = useState<boolean>(false);
  const [syncError, setSyncError] = useState<string | null>(null);

  // Show snackbar when going offline
  React.useEffect(() => {
    if (!isOnline) {
      setShowSnackbar(true);
    } else {
      // Auto-hide the offline snackbar when coming back online
      setShowSnackbar(false);
    }
  }, [isOnline]);

  // Handle sync button click
  const handleSync = async () => {
    if (!isOnline) {
      return;
    }

    setSyncError(null);

    try {
      const syncCount = await synchronize();
      console.log(`Successfully synchronized ${syncCount} pending changes`);
    } catch (error) {
      console.error('Synchronization error:', error);
      setSyncError(error instanceof Error ? error.message : 'Unknown error during synchronization');
    }
  };

  // Get the appropriate icon based on sync status
  const getSyncIcon = () => {
    if (!isOnline) {
      return <CloudOffIcon />;
    }

    if (syncInProgress) {
      return <CloudSyncIcon />;
    }

    if (pendingChanges > 0) {
      return <WarningIcon color="warning" />;
    }

    return <CloudDoneIcon color="success" />;
  };

  // Get the tooltip text based on sync status
  const getTooltipText = () => {
    if (!isOnline) {
      return translate('offline.tooltip_offline', { fallback: 'You are offline. Changes will be synchronized when you reconnect.' });
    }

    if (syncInProgress) {
      return translate('offline.tooltip_syncing', { fallback: 'Synchronizing changes...' });
    }

    if (pendingChanges > 0) {
      return translate('offline.tooltip_pending', { pendingChanges, fallback: `${pendingChanges} pending changes to synchronize` });
    }

    return translate('offline.tooltip_synced', { fallback: 'All changes are synchronized' });
  };

  return (
    <>
      {/* Offline Snackbar */}
      <Snackbar
        open={showSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="warning"
          icon={<WifiOffIcon />}
          action={
            <CustomButton
              color="inherit"
              size="small"
              onClick={() => setShowSnackbar(false)}
            >
              {translate('common.dismiss', { fallback: 'Dismiss' })}
            </CustomButton>
          }
        >
          {translate('offline.snackbar_message', { fallback: 'You are currently offline. Changes will be saved locally and synchronized when you reconnect.' })}
        </Alert>
      </Snackbar>

      {/* Sync Status Button */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 1000
        }}
      >
        <AnimatePresence>
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Tooltip title={getTooltipText()}>
              <Badge
                badgeContent={pendingChanges > 0 ? pendingChanges : 0}
                color="error"
                overlap="circular"
                invisible={pendingChanges === 0}
              >
                <IconButton
                  color={!isOnline ? 'error' : pendingChanges > 0 ? 'warning' : 'primary'}
                  onClick={() => setShowDialog(true)}
                  sx={{
                    backgroundColor: theme.palette.background.paper,
                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.background.paper, 0.9)
                    }
                  }}
                  size="large"
                >
                  {getSyncIcon()}
                </IconButton>
              </Badge>
            </Tooltip>
          </motion.div>
        </AnimatePresence>
      </Box>

      {/* Sync Dialog */}
      <Dialog
        open={showDialog}
        onClose={() => setShowDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {translate('offline.dialog_title', { fallback: 'Synchronization Status' })}
        </DialogTitle>

        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Alert
              severity={!isOnline ? 'error' : pendingChanges > 0 ? 'warning' : 'success'}
              icon={getSyncIcon()}
              sx={{ mb: 2 }}
            >
              {!isOnline
                ? translate('offline.alert_offline', { fallback: 'You are currently offline. Changes will be synchronized when you reconnect.' })
                : pendingChanges > 0
                  ? translate('offline.alert_pending', { pendingChanges, fallback: `You have ${pendingChanges} pending changes to synchronize.` })
                  : translate('offline.alert_synced', { fallback: 'All changes are synchronized.' })}
            </Alert>

            {syncError && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {syncError}
              </Alert>
            )}
          </Box>

          <List>
            <ListItem>
              <ListItemIcon>
                {isOnline ? <CloudDoneIcon color="success" /> : <CloudOffIcon color="error" />}
              </ListItemIcon>
              <ListItemText
                primary={translate('offline.connection_status', { fallback: 'Connection Status' })}
                secondary={isOnline
                  ? translate('offline.status_online', { fallback: 'Online' })
                  : translate('offline.status_offline', { fallback: 'Offline' })}
              />
            </ListItem>

            <Divider component="li" />

            <ListItem>
              <ListItemIcon>
                {pendingChanges > 0 ? <WarningIcon color="warning" /> : <CloudDoneIcon color="success" />}
              </ListItemIcon>
              <ListItemText
                primary={translate('offline.pending_changes', { fallback: 'Pending Changes' })}
                secondary={pendingChanges > 0
                  ? translate('offline.changes_waiting', { pendingChanges, fallback: `${pendingChanges} changes waiting to be synchronized` })
                  : translate('offline.no_pending_changes', { fallback: 'No pending changes' })}
              />
            </ListItem>

            <Divider component="li" />

            <ListItem>
              <ListItemIcon>
                <AccessTimeIcon />
              </ListItemIcon>
              <ListItemText
                primary={translate('offline.last_synchronized', { fallback: 'Last Synchronized' })}
                secondary={lastSyncTime
                  ? formatRelativeTime(new Date(lastSyncTime))
                  : translate('offline.never', { fallback: 'Never' })}
              />
            </ListItem>
          </List>
        </DialogContent>

        <DialogActions>
          <CustomButton onClick={() => setShowDialog(false)}>
            {translate ? translate('common.close', { fallback: 'Close' }) : 'Close'}
          </CustomButton>
          <CustomButton
            onClick={handleSync}
            disabled={!isOnline || pendingChanges === 0 || syncInProgress}
            variant="contained"
            color="primary"
            startIcon={syncInProgress ? <CircularProgress size={20} color="inherit" /> : <CloudSyncIcon />}
          >
            {syncInProgress
              ? (translate ? translate('offline.synchronizing', { fallback: 'Synchronizing...' }) : 'Synchronizing...')
              : (translate ? translate('offline.synchronize_now', { fallback: 'Synchronize Now' }) : 'Synchronize Now')}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default OfflineIndicator;
