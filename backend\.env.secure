# Secure Environment Configuration - Generated 2025-06-04T03:27:26.064Z
# IMPORTANT: Change all values before deploying to production

# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# MongoDB Configuration - REPLACE WITH YOUR VALUES
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
MONGODB_DB_NAME=ampd_livestock_production

# JWT Configuration - SECURE SECRETS GENERATED
JWT_SECRET=ff0dbd09390de818f2cb1b16041b064ef982daa14a6834836c37d09fb7ae134d33e49e69b42d0ece61c1cf7bf700b9881c03adbb05e66cd26193d1fe0392bdcb
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=895e2e61e62bdd7deea97baf63274a7609398a67365cefe987d35cf07977423e326c5826ad4810c64deee5d2c490bf39386c2a09fc0b38e645f6d2d2b946341b
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=a8677ff63b54c5bd86cf66e310f4bcfad82301a8bcae6973a74a3c42871e4a48511408a0f7a72fbaf25ca72916eecc9e61359461290b0e7aa1b0028f83524d08
CORS_ORIGIN=https://your-production-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Default Admin Credentials - CHANGE IMMEDIATELY
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=9Q9IpIYw2JInEZpG
DEFAULT_ADMIN_EMAIL=<EMAIL>

# Email Configuration
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/production.log

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# API Keys - REPLACE WITH YOUR VALUES
WEATHER_API_KEY=your_weather_api_key
GOVERNMENT_API_KEY=your_government_api_key
SMS_API_KEY=your_sms_api_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
ANALYTICS_API_KEY=your_analytics_key

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_BACKUP=true
