export const ROUTES = {
  // Main routes
  DASHBOARD: '/dashboard',
  ANIMALS: '/dashboard/animals',
  ANIMALS_NEW: '/dashboard/animals/new', // Fixed route for new animal creation
  HEALTH: '/dashboard/health',
  FEED: '/dashboard/feeding',  // Updated to match the sidebar and actual component
  BREEDING: '/dashboard/breeding',
  FINANCIAL: '/dashboard/financial',
  INVENTORY: '/dashboard/inventory',
  GROWTH: '/dashboard/growth',
  COMPLIANCE: '/dashboard/compliance',
  SALES: '/dashboard/sales',
  ASSET_MANAGEMENT: '/dashboard/animals/asset-management', // New route for Asset Management

  // Additional main routes
  COMMERCIAL: '/dashboard/commercial',
  REPORTS: '/dashboard/reports',
  FEED_MANAGEMENT: '/dashboard/feeding',  // Updated to match the sidebar and actual component

  // Analytics and monitoring
  ANALYTICS: '/dashboard/analytics',

  // Support and settings
  RESOURCES: '/dashboard/resources' as const,
  SETTINGS: '/dashboard/settings' as const,
} as const;

// Update ModuleMetric type to include 'warning' status
export type MetricStatus = 'positive' | 'negative' | 'neutral' | 'warning';

export const MODULE_PATHS = [
  { path: ROUTES.DASHBOARD, name: 'Dashboard' },
  { path: ROUTES.ANIMALS, name: 'Animals' },
  { path: ROUTES.HEALTH, name: 'Health' },
  { path: ROUTES.FEED, name: 'Feed' },
  { path: ROUTES.BREEDING, name: 'Breeding' },
  { path: ROUTES.FINANCIAL, name: 'Financial' },
  { path: ROUTES.INVENTORY, name: 'Inventory' },
  { path: ROUTES.GROWTH, name: 'Growth' },
  { path: ROUTES.COMPLIANCE, name: 'Compliance' },
  { path: ROUTES.SALES, name: 'Sales' },
  { path: ROUTES.ASSET_MANAGEMENT, name: 'Asset Management' },
  { path: ROUTES.COMMERCIAL, name: 'Commercial' },
  { path: ROUTES.REPORTS, name: 'Reports' },
  { path: ROUTES.RESOURCES, name: 'Resources' },
  { path: ROUTES.SETTINGS, name: 'Settings' },
] as const;
