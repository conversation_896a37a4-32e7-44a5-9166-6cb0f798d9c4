import React, { useState, useEffect } from 'react';
import { Box, Grid, Card, CardContent, Typography, useTheme, alpha, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, IconButton, Chip, Divider, Avatar, List, ListItem, ListItemAvatar, ListItemText, ListItemSecondaryAction, Tabs, Tab } from '@mui/material';
import {
  Add,
  Assignment,
  Warning,
  ErrorOutline,
  CalendarToday,
  Person,
  Description,
  Download,
  Edit,
  Delete,
  Visibility,
  CheckCircle,
  Error as ErrorIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  Mo<PERSON>leHeader, AnimatedBackgroundCard, EnhancedDataTable , CustomButton } from '../../components/common';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { useComplianceDataContext } from '../../contexts/DataContext';

// Define types for inspections
interface Inspection {
  id: string;
  type: string;
  date: string;
  inspector: string;
  status: 'passed' | 'failed' | 'pending';
  findings: string;
  actionItems?: string[];
  followUpDate?: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const Inspections: React.FC = () => {
  const theme = useTheme();
  const { inspections, loading, error, fetchComplianceData } = useComplianceDataContext();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedInspection, setSelectedInspection] = useState<Inspection | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Form state
  const [formData, setFormData] = useState({
    id: '',
    type: '',
    date: dayjs(),
    inspector: '',
    status: 'pending' as 'passed' | 'failed' | 'pending',
    findings: '',
    actionItems: [''],
    followUpDate: dayjs().add(30, 'day')
  });

  // Define Inspection interface
  interface Inspection {
    id: string;
    type: string;
    date: string;
    authority: string;
    inspector: string;
    status: 'passed' | 'failed' | 'pending';
    findings: string;
    actionItems: string[];
    followUpDate: dayjs.Dayjs;
  }

  // Calculate inspection statistics
  const inspectionStats = {
    total: inspections?.length || 0,
    passed: inspections?.filter((i: Inspection) => i.status === 'passed').length || 0,
    failed: inspections?.filter((i: Inspection) => i.status === 'failed').length || 0,
    pending: inspections?.filter((i: Inspection) => i.status === 'pending').length || 0
  };

  // Filter inspections based on status
  const filteredInspections = inspections?.filter((inspection: Inspection) => {
    if (filterStatus === 'all') return true;
    return inspection.status === filterStatus;
  }) || [];

  // Handle dialog open/close
  const handleOpenDialog = (inspection?: Inspection) => {
    if (inspection) {
      setSelectedInspection(inspection);
      setFormData({
        id: inspection.id,
        type: inspection.type,
        date: dayjs(inspection.date),
        inspector: inspection.inspector,
        status: inspection.status,
        findings: inspection.findings,
        actionItems: inspection.actionItems || [''],
        followUpDate: inspection.followUpDate ? dayjs(inspection.followUpDate) : dayjs().add(30, 'day')
      });
    } else {
      setSelectedInspection(null);
      setFormData({
        id: `INSP-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
        type: '',
        date: dayjs(),
        inspector: '',
        status: 'pending',
        findings: '',
        actionItems: [''],
        followUpDate: dayjs().add(30, 'day')
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedInspection(null);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleStatusChange = (e: any) => {
    setFormData(prev => ({ ...prev, status: e.target.value }));
  };

  const handleDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, date }));
    }
  };

  const handleFollowUpDateChange = (date: dayjs.Dayjs | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, followUpDate: date }));
    }
  };

  const handleActionItemChange = (index: number, value: string) => {
    const newActionItems = [...formData.actionItems];
    newActionItems[index] = value;
    setFormData(prev => ({ ...prev, actionItems: newActionItems }));
  };

  const handleAddActionItem = () => {
    setFormData(prev => ({
      ...prev,
      actionItems: [...prev.actionItems, '']
    }));
  };

  const handleRemoveActionItem = (index: number) => {
    const newActionItems = [...formData.actionItems];
    newActionItems.splice(index, 1);
    setFormData(prev => ({ ...prev, actionItems: newActionItems }));
  };

  // Handle save inspection
  const handleSaveInspection = () => {
    // In a real app, this would call an API to save the inspection
    console.log('Saving inspection:', formData);

    // For now, just close the dialog
    handleCloseDialog();

    // Refresh data
    fetchComplianceData && fetchComplianceData();
  };

  // Handle delete inspection
  const handleDelete = (id: string) => {
    // In a real app, this would call an API to delete the inspection
    console.log('Deleting inspection:', id);

    // Refresh data
    fetchComplianceData && fetchComplianceData();
  };

  // Define columns for the data table
  const columns = [
    { id: 'id', label: 'ID', minWidth: 80 },
    { id: 'type', label: 'Type', minWidth: 150 },
    { id: 'date', label: 'Date', minWidth: 120 },
    { id: 'inspector', label: 'Inspector', minWidth: 150 },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          label={value}
          size="small"
          color={
            value === 'passed' ? 'success' :
            value === 'failed' ? 'error' :
            'warning'
          }
        />
      )
    },
    { id: 'findings', label: 'Findings', minWidth: 200 }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Inspections"
        subtitle="Track and manage regulatory inspections for your livestock operation"
        module="compliance"
        submodule="inspections"
        actionLabel="Add Inspection"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Statistics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Inspections"
                  subtitle={inspectionStats.total.toString()}
                  module="compliance"
                  uniqueId="total-inspections"
                  icon={<Assignment />}
                  accentColor={theme.palette.primary.main}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Passed"
                  subtitle={inspectionStats.passed.toString()}
                  module="compliance"
                  uniqueId="passed-inspections"
                  icon={<CheckCircle />}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Failed"
                  subtitle={inspectionStats.failed.toString()}
                  module="compliance"
                  uniqueId="failed-inspections"
                  icon={<ErrorIcon />}
                  accentColor={theme.palette.error.main}
                  secondaryColor={theme.palette.error.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Pending"
                  subtitle={inspectionStats.pending.toString()}
                  module="compliance"
                  uniqueId="pending-inspections"
                  icon={<Warning />}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                  height={140}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Filters and View Toggle */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="status-filter-label">Status Filter</InputLabel>
            <Select
              labelId="status-filter-label"
              id="status-filter"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              label="Status Filter"
            >
              <MenuItem value="all">All Inspections</MenuItem>
              <MenuItem value="passed">Passed</MenuItem>
              <MenuItem value="failed">Failed</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
            </Select>
          </FormControl>

          <Tabs
            value={viewMode === 'grid' ? 0 : 1}
            onChange={(_, newValue) => setViewMode(newValue === 0 ? 'grid' : 'table')}
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label="Grid View" />
            <Tab label="Table View" />
          </Tabs>
        </Box>

        {/* Grid View */}
        {viewMode === 'grid' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Grid container spacing={3}>
              {filteredInspections.map((inspection: Inspection) => (
                <Grid item xs={12} sm={6} md={4} key={inspection.id}>
                  <Card
                    sx={{
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: 3,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      '&:hover': {
                        transform: 'translateY(-5px)',
                        boxShadow: 6
                      },
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '4px',
                        background:
                          inspection.status === 'passed' ? theme.palette.success.main :
                          inspection.status === 'failed' ? theme.palette.error.main :
                          theme.palette.warning.main
                      }
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" component="h2" fontWeight="bold">
                          {inspection.type}
                        </Typography>
                        <Chip
                          label={inspection.status}
                          size="small"
                          color={
                            inspection.status === 'passed' ? 'success' :
                            inspection.status === 'failed' ? 'error' :
                            'warning'
                          }
                        />
                      </Box>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <CalendarToday fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          {inspection.date}
                        </Box>
                        <Box component="span" sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                          <Person fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                          {inspection.inspector}
                        </Box>
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Typography variant="body2" sx={{ mb: 1.5 }}>
                        <strong>Findings:</strong> {inspection.findings}
                      </Typography>

                      {inspection.actionItems && inspection.actionItems.length > 0 && (
                        <Box sx={{ mb: 1.5 }}>
                          <Typography variant="body2" fontWeight="bold">
                            Action Items:
                          </Typography>
                          <List dense disablePadding>
                            {inspection.actionItems.map((item: string, index: number) => (
                              <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                                <ListItemText
                                  primary={item}
                                  primaryTypographyProps={{ variant: 'body2' }}
                                />
                              </ListItem>
                            ))}
                          </List>
                        </Box>
                      )}

                      {inspection.followUpDate && (
                        <Typography variant="body2" color="text.secondary">
                          <span><strong>Follow-up:</strong> {inspection.followUpDate.toString()}</span>
                        </Typography>
                      )}
                    </CardContent>

                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
                      <IconButton size="small" onClick={() => console.log('View', inspection.id)}>
                        <Visibility fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleOpenDialog(inspection)}>
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleDelete(inspection.id)}>
                        <Delete fontSize="small" />
                      </IconButton>
                    </Box>
                  </Card>
                </Grid>
              ))}

              {filteredInspections.length === 0 && (
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center', py: 5 }}>
                    <Typography variant="h6" color="text.secondary">
                      No inspections found
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Try changing your filter or add a new inspection
                    </Typography>
                    <CustomButton
                      variant="contained"
                      startIcon={<Add />}
                      sx={{ mt: 2 }}
                      onClick={() => handleOpenDialog()}
                    >
                      Add Inspection
                    </CustomButton>
                  </Box>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Table View */}
        {viewMode === 'table' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <EnhancedDataTable
              title="Inspections"
              subtitle="Track inspection records and findings"
              columns={columns}
              data={filteredInspections}
              keyField="id"
              loading={loading}
              onRefresh={fetchComplianceData}
              onView={(row: any) => console.log('View', row.id)}
              onEdit={(row: any) => handleOpenDialog(row)}
              onDelete={(row: any) => handleDelete(row.id)}
              searchable={true}
              searchFields={['type', 'inspector', 'findings', 'id']}
              emptyMessage="No inspections found"
              accentColor={theme.palette.primary.main}
            />
          </motion.div>
        )}
      </Box>

      {/* Add/Edit Inspection Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedInspection ? 'Edit Inspection' : 'Add New Inspection'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Inspection ID"
                name="id"
                value={formData.id}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="status-label">Status</InputLabel>
                <Select
                  labelId="status-label"
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleStatusChange}
                  label="Status"
                >
                  <MenuItem value="passed">Passed</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Inspection Type"
                name="type"
                value={formData.type}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="e.g., Health, Safety, Environmental"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Inspection Date"
                value={formData.date}
                onChange={handleDateChange}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Inspector"
                name="inspector"
                value={formData.inspector}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                placeholder="Name of the inspector"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Findings"
                name="findings"
                value={formData.findings}
                onChange={handleInputChange}
                fullWidth
                margin="normal"
                multiline
                rows={3}
                placeholder="Detailed findings from the inspection"
              />
            </Grid>

            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Action Items
              </Typography>
              {formData.actionItems.map((item, index) => (
                <Box key={index} sx={{ display: 'flex', mb: 1 }}>
                  <TextField
                    value={item}
                    onChange={(e) => handleActionItemChange(index, e.target.value)}
                    fullWidth
                    placeholder={`Action item ${index + 1}`}
                    size="small"
                  />
                  <IconButton
                    color="error"
                    onClick={() => handleRemoveActionItem(index)}
                    disabled={formData.actionItems.length <= 1}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              ))}
              <CustomButton
                startIcon={<Add />}
                onClick={handleAddActionItem}
                size="small"
                sx={{ mt: 1 }}
              >
                Add Action Item
              </CustomButton>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label="Follow-up Date"
                value={formData.followUpDate}
                onChange={handleFollowUpDateChange}
                slotProps={{ textField: { fullWidth: true, margin: 'normal' } }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSaveInspection}
            variant="contained"
            color="primary"
          >
            Save
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Inspections;
