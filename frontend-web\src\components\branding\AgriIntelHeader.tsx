/**
 * AgriIntel Branded Header Component
 * Professional livestock management branding with gradient themes
 */

import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  IconButton,
  Badge,
  Avatar,
  Menu,
  MenuItem,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Notifications,
  Settings,
  AccountCircle,
  Brightness4,
  Brightness7,
  Agriculture,
  Dashboard,
  Menu as MenuIcon
} from '@mui/icons-material';
import { useAgriIntelTheme } from '../theme/AgriIntelThemeProvider';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';

interface AgriIntelHeaderProps {
  onMenuToggle?: () => void;
  showMenuButton?: boolean;
  title?: string;
  subtitle?: string;
}

const AgriIntelHeader: React.FC<AgriIntelHeaderProps> = ({
  onMenuToggle,
  showMenuButton = true,
  title,
  subtitle
}) => {
  const theme = useTheme();
  const { theme: agriTheme, toggleMode } = useAgriIntelTheme();
  const { user } = useAuth();
  const { translate } = useTranslation();
  
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [notificationsAnchor, setNotificationsAnchor] = React.useState<null | HTMLElement>(null);

  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationsOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationsAnchor(event.currentTarget);
  };

  const handleNotificationsClose = () => {
    setNotificationsAnchor(null);
  };

  // Mock notifications
  const notifications = [
    { id: 1, message: 'Health check due for Cattle A001', type: 'warning', time: '2 hours ago' },
    { id: 2, message: 'New breeding record added', type: 'info', time: '4 hours ago' },
    { id: 3, message: 'Feed inventory running low', type: 'error', time: '1 day ago' }
  ];

  return (
    <AppBar 
      position="sticky" 
      elevation={0}
      sx={{
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)} 0%, ${alpha(theme.palette.primary.dark, 0.9)} 100%)`,
        backdropFilter: 'blur(10px)',
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between', px: { xs: 1, sm: 2 } }}>
        {/* Left Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {showMenuButton && (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={onMenuToggle}
              sx={{ 
                mr: 1,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.common.white, 0.1),
                }
              }}
            >
              <MenuIcon />
            </IconButton>
          )}
          
          {/* AgriIntel Branding */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Agriculture 
              sx={{ 
                fontSize: 32, 
                color: theme.palette.common.white,
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
              }} 
            />
            <Box>
              <Typography
                variant="h5"
                component="div"
                sx={{
                  fontFamily: '"Poppins", sans-serif',
                  fontWeight: 700,
                  background: 'linear-gradient(45deg, #ffffff 30%, #e8f5e8 90%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 2px 4px rgba(0,0,0,0.3)',
                  letterSpacing: '-0.5px'
                }}
              >
                AgriIntel
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  color: alpha(theme.palette.common.white, 0.8),
                  fontStyle: 'italic',
                  fontSize: '0.7rem',
                  lineHeight: 1,
                  display: 'block',
                  marginTop: '-2px'
                }}
              >
                Smart Livestock Management
              </Typography>
            </Box>
          </Box>

          {/* Page Title */}
          {title && (
            <Box sx={{ ml: 3, display: { xs: 'none', md: 'block' } }}>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 500 }}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" sx={{ color: alpha(theme.palette.common.white, 0.7) }}>
                  {subtitle}
                </Typography>
              )}
            </Box>
          )}
        </Box>

        {/* Right Section */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* User Subscription Tier */}
          {user && (
            <Chip
              label={user.subscriptionTier || 'Beta Access'}
              size="small"
              sx={{
                backgroundColor: alpha(theme.palette.common.white, 0.2),
                color: 'white',
                fontWeight: 600,
                display: { xs: 'none', sm: 'flex' }
              }}
            />
          )}

          {/* Theme Toggle */}
          <IconButton
            color="inherit"
            onClick={toggleMode}
            sx={{
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              }
            }}
          >
            {agriTheme.mode === 'dark' ? <Brightness7 /> : <Brightness4 />}
          </IconButton>

          {/* Notifications */}
          <IconButton
            color="inherit"
            onClick={handleNotificationsOpen}
            sx={{
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              }
            }}
          >
            <Badge badgeContent={notifications.length} color="error">
              <Notifications />
            </Badge>
          </IconButton>

          {/* Settings */}
          <IconButton
            color="inherit"
            sx={{
              display: { xs: 'none', sm: 'flex' },
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              }
            }}
          >
            <Settings />
          </IconButton>

          {/* User Profile */}
          <IconButton
            edge="end"
            aria-label="account of current user"
            aria-controls="primary-search-account-menu"
            aria-haspopup="true"
            onClick={handleProfileMenuOpen}
            color="inherit"
            sx={{
              '&:hover': {
                backgroundColor: alpha(theme.palette.common.white, 0.1),
              }
            }}
          >
            {user?.firstName ? (
              <Avatar 
                sx={{ 
                  width: 32, 
                  height: 32, 
                  backgroundColor: alpha(theme.palette.common.white, 0.2),
                  color: 'white',
                  fontSize: '0.9rem',
                  fontWeight: 600
                }}
              >
                {user.firstName.charAt(0)}{user.lastName?.charAt(0) || ''}
              </Avatar>
            ) : (
              <AccountCircle />
            )}
          </IconButton>
        </Box>

        {/* Profile Menu */}
        <Menu
          anchorEl={anchorEl}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          keepMounted
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          open={Boolean(anchorEl)}
          onClose={handleProfileMenuClose}
        >
          <MenuItem onClick={handleProfileMenuClose}>
            {translate('nav.profile', { fallback: 'Profile' })}
          </MenuItem>
          <MenuItem onClick={handleProfileMenuClose}>
            {translate('nav.settings', { fallback: 'Settings' })}
          </MenuItem>
          <MenuItem onClick={handleProfileMenuClose}>
            {translate('nav.logout', { fallback: 'Logout' })}
          </MenuItem>
        </Menu>

        {/* Notifications Menu */}
        <Menu
          anchorEl={notificationsAnchor}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          keepMounted
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          open={Boolean(notificationsAnchor)}
          onClose={handleNotificationsClose}
          PaperProps={{
            sx: { width: 320, maxHeight: 400 }
          }}
        >
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Typography variant="h6">
              {translate('notifications.title', { fallback: 'Notifications' })}
            </Typography>
          </Box>
          {notifications.map((notification) => (
            <MenuItem key={notification.id} onClick={handleNotificationsClose}>
              <Box>
                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                  {notification.message}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {notification.time}
                </Typography>
              </Box>
            </MenuItem>
          ))}
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default AgriIntelHeader;
