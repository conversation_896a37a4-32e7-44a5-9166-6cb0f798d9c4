import React, { useState, useEffect, useRef } from 'react';
import { Box, Skeleton, BoxProps } from '@mui/material';

interface LazyImageProps extends Omit<BoxProps, 'component'> {
  src: string;
  alt: string;
  fallbackSrc?: string; // Fallback image source
  height?: string | number;
  width?: string | number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  threshold?: number; // Intersection observer threshold
  placeholderColor?: string;
  loadingEffect?: 'fade' | 'blur' | 'skeleton';
  onLoad?: () => void;
  onError?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  fallbackSrc,
  height = '100%',
  width = '100%',
  objectFit = 'cover',
  threshold = 0.1,
  placeholderColor = '#f0f0f0',
  loadingEffect = 'fade',
  onLoad,
  onError,
  sx,
  ...boxProps
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [error, setError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          // Disconnect observer once image is in view
          if (observerRef.current) {
            observerRef.current.disconnect();
          }
        }
      },
      { threshold }
    );

    if (imgRef.current && observerRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold]);

  // Handle image load event
  const handleLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  // Handle image error event
  const handleError = () => {
    // If fallback is provided and not already using it, switch to fallback
    if (fallbackSrc && src !== fallbackSrc) {
      // Don't set error state, just switch the source
      if (imgRef.current) {
        imgRef.current.src = fallbackSrc;
      }
    } else {
      // No fallback or already using fallback, show error state
      setError(true);
      if (onError) onError();
    }
  };

  // Generate styles based on loading effect
  const getStyles = () => {
    const baseStyles = {
      width,
      height,
      objectFit,
      transition: 'opacity 0.3s, filter 0.5s',
      ...sx
    };

    if (!isInView) {
      return {
        ...baseStyles,
        opacity: 0
      };
    }

    if (!isLoaded && loadingEffect === 'fade') {
      return {
        ...baseStyles,
        opacity: 0
      };
    }

    if (!isLoaded && loadingEffect === 'blur') {
      return {
        ...baseStyles,
        filter: 'blur(10px)',
        opacity: 0.7
      };
    }

    return {
      ...baseStyles,
      opacity: 1,
      filter: 'blur(0)'
    };
  };

  return (
    <Box
      position="relative"
      width={width}
      height={height}
      overflow="hidden"
      {...boxProps}
    >
      {/* Placeholder/Skeleton */}
      {!isLoaded && isInView && loadingEffect === 'skeleton' && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          animation="wave"
        />
      )}

      {/* Placeholder color */}
      {!isLoaded && isInView && loadingEffect !== 'skeleton' && (
        <Box
          position="absolute"
          top={0}
          left={0}
          width="100%"
          height="100%"
          bgcolor={placeholderColor}
        />
      )}

      {/* Actual image */}
      {(isInView || isLoaded) && (
        <Box
          component="img"
          ref={imgRef}
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          sx={getStyles()}
        />
      )}

      {/* Error fallback */}
      {error && (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          height="100%"
          bgcolor="#f8f8f8"
          color="#999"
          fontSize="0.75rem"
          textAlign="center"
          p={2}
        >
          {alt || 'Image failed to load'}
        </Box>
      )}
    </Box>
  );
};

export default LazyImage;
