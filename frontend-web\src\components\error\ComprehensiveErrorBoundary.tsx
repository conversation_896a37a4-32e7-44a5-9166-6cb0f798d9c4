/**
 * Comprehensive Error Boundary Component
 * Advanced error handling with logging, recovery, and user feedback
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  <PERSON>,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Alert,
  Collapse,
  IconButton,
  Chip,
  Stack,
  useTheme
} from '@mui/material';
import {
  Error as ErrorIcon,
  Refresh,
  BugReport,
  ExpandMore,
  ExpandLess,
  Home,
  Support
} from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  allowRetry?: boolean;
  showReportButton?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  retryCount: number;
}

class ComprehensiveErrorBoundary extends Component<Props, State> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error Boundary caught an error:', error, errorInfo);
    }

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to external service (in production)
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error tracking service
    // like Sentry, LogRocket, or Bugsnag
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem('userId') || 'anonymous',
      retryCount: this.state.retryCount
    };

    // Example: Send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData),
      }).catch(err => {
        console.error('Failed to log error to service:', err);
      });
    }
  };

  private handleRetry = () => {
    const { retryCount } = this.state;
    
    // Limit retry attempts
    if (retryCount >= 3) {
      alert('Maximum retry attempts reached. Please refresh the page.');
      return;
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false,
      retryCount: retryCount + 1
    });

    // Add a small delay before retry
    this.retryTimeoutId = setTimeout(() => {
      // Force re-render
      this.forceUpdate();
    }, 100);
  };

  private handleRefresh = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportError = () => {
    const { error, errorInfo } = this.state;
    const errorReport = {
      error: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    };

    // Create mailto link with error details
    const subject = encodeURIComponent('AgriIntel Error Report');
    const body = encodeURIComponent(`
Error Report:
${JSON.stringify(errorReport, null, 2)}

Please describe what you were doing when this error occurred:
[Your description here]
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  render() {
    const { hasError, error, errorInfo, showDetails, retryCount } = this.state;
    const { children, fallback, allowRetry = true, showReportButton = true } = this.props;

    if (hasError) {
      // Custom fallback UI
      if (fallback) {
        return fallback;
      }

      return (
        <ErrorFallbackUI
          error={error}
          errorInfo={errorInfo}
          showDetails={showDetails}
          retryCount={retryCount}
          onRetry={allowRetry ? this.handleRetry : undefined}
          onRefresh={this.handleRefresh}
          onGoHome={this.handleGoHome}
          onReportError={showReportButton ? this.handleReportError : undefined}
          onToggleDetails={this.toggleDetails}
        />
      );
    }

    return children;
  }
}

// Error Fallback UI Component
interface ErrorFallbackUIProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  showDetails: boolean;
  retryCount: number;
  onRetry?: () => void;
  onRefresh: () => void;
  onGoHome: () => void;
  onReportError?: () => void;
  onToggleDetails: () => void;
}

const ErrorFallbackUI: React.FC<ErrorFallbackUIProps> = ({
  error,
  errorInfo,
  showDetails,
  retryCount,
  onRetry,
  onRefresh,
  onGoHome,
  onReportError,
  onToggleDetails
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${theme.palette.error.light}20 0%, ${theme.palette.error.main}20 100%)`,
        p: 2
      }}
    >
      <Card
        sx={{
          maxWidth: 600,
          width: '100%',
          boxShadow: theme.shadows[8]
        }}
      >
        <CardContent sx={{ textAlign: 'center', p: 4 }}>
          <ErrorIcon
            sx={{
              fontSize: 64,
              color: theme.palette.error.main,
              mb: 2
            }}
          />
          
          <Typography variant="h4" gutterBottom color="error">
            Oops! Something went wrong
          </Typography>
          
          <Typography variant="body1" color="text.secondary" paragraph>
            We're sorry, but something unexpected happened. Don't worry, your data is safe.
          </Typography>

          {retryCount > 0 && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              Retry attempt {retryCount}/3
            </Alert>
          )}

          <Stack direction="row" spacing={1} justifyContent="center" sx={{ mb: 2 }}>
            <Chip
              label={`Error: ${error?.name || 'Unknown'}`}
              color="error"
              variant="outlined"
              size="small"
            />
            <Chip
              label={new Date().toLocaleString()}
              variant="outlined"
              size="small"
            />
          </Stack>

          <Box sx={{ mb: 3 }}>
            <Button
              startIcon={showDetails ? <ExpandLess /> : <ExpandMore />}
              onClick={onToggleDetails}
              size="small"
              color="inherit"
            >
              {showDetails ? 'Hide' : 'Show'} Technical Details
            </Button>
          </Box>

          <Collapse in={showDetails}>
            <Alert severity="error" sx={{ textAlign: 'left', mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Error Message:
              </Typography>
              <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
                {error?.message}
              </Typography>
              
              {error?.stack && (
                <>
                  <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                    Stack Trace:
                  </Typography>
                  <Typography 
                    variant="body2" 
                    component="pre" 
                    sx={{ 
                      fontSize: '0.7rem',
                      maxHeight: 200,
                      overflow: 'auto',
                      backgroundColor: theme.palette.grey[100],
                      p: 1,
                      borderRadius: 1
                    }}
                  >
                    {error.stack}
                  </Typography>
                </>
              )}
            </Alert>
          </Collapse>
        </CardContent>

        <CardActions sx={{ justifyContent: 'center', p: 3, pt: 0 }}>
          <Stack direction="row" spacing={2} flexWrap="wrap" justifyContent="center">
            {onRetry && retryCount < 3 && (
              <Button
                variant="contained"
                startIcon={<Refresh />}
                onClick={onRetry}
                color="primary"
              >
                Try Again
              </Button>
            )}
            
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={onRefresh}
            >
              Refresh Page
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<Home />}
              onClick={onGoHome}
            >
              Go Home
            </Button>
            
            {onReportError && (
              <Button
                variant="outlined"
                startIcon={<BugReport />}
                onClick={onReportError}
                color="error"
              >
                Report Issue
              </Button>
            )}
          </Stack>
        </CardActions>
      </Card>
    </Box>
  );
};

// Hook for programmatic error handling
export const useErrorHandler = () => {
  const handleError = (error: Error, context?: string) => {
    console.error(`Error in ${context || 'unknown context'}:`, error);
    
    // You could also trigger a global error state here
    // or send the error to your error tracking service
  };

  return { handleError };
};

export default ComprehensiveErrorBoundary;
