import { getCollection } from '../utils/mockMongoDbClient';
import { ObjectId } from '../mocks/mockMongoDb';

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://luckyrakgama:<EMAIL>/AMPD Live Stock?retryWrites=true&w=majority&appName=Cluster0';

/**
 * Generic MongoDB service for CRUD operations
 */
export class MongoDbService<T> {
  private collectionName: string;

  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }

  /**
   * Find all documents in the collection
   * @param query Optional query filter
   * @param options Optional query options
   */
  async findAll(query = {}, options = {}) {
    try {
      const collection = await getCollection(this.collectionName);
      // Check if collection is a MockMongoService
      if ('findAll' in collection) {
        return await (collection as any).findAll(query);
      }
      // Otherwise, use MongoDB collection methods
      return await (collection as any).find(query, options).toArray();
    } catch (error) {
      console.error(`Error finding documents in ${this.collectionName}:`, error);
      // Return empty array on error
      return [];
    }
  }

  /**
   * Find a document by ID
   * @param id Document ID
   */
  async findById(id: string) {
    try {
      const collection = await getCollection(this.collectionName);

      // Check if collection is a MockMongoService
      if ('findById' in collection) {
        return await (collection as any).findById(id);
      }

      // Try to find by string ID first
      // Use any type to bypass TypeScript error
      let result = await (collection as any).findOne({ _id: id } as any);

      // If not found, try to find by ObjectId
      if (!result && id.match(/^[0-9a-fA-F]{24}$/)) {
        try {
          // Use our mock ObjectId instead of requiring mongodb
          result = await (collection as any).findOne({ _id: new ObjectId(id) });
        } catch (err) {
          console.error('Error converting to ObjectId:', err);
        }
      }

      return result;
    } catch (error) {
      console.error(`Error finding document by ID in ${this.collectionName}:`, error);
      return null;
    }
  }

  /**
   * Find a single document
   * @param query Query filter
   */
  async findOne(query = {}) {
    try {
      const collection = await getCollection(this.collectionName);
      return await (collection as any).findOne(query);
    } catch (error) {
      console.error(`Error finding document in ${this.collectionName}:`, error);
      return null;
    }
  }

  /**
   * Create a new document
   * @param data Document data
   */
  async create(data: T) {
    try {
      const collection = await getCollection(this.collectionName);
      const result = await (collection as any).insertOne(data as any);
      return result;
    } catch (error) {
      console.error(`Error creating document in ${this.collectionName}:`, error);
      return { insertedId: 'error-' + Date.now() };
    }
  }

  /**
   * Update a document by ID
   * @param id Document ID
   * @param data Updated data
   */
  async updateById(id: string, data: Partial<T>) {
    try {
      const collection = await getCollection(this.collectionName);
      let filter: any = { _id: id };

      // If ID is a valid ObjectId, try to use it
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        try {
          // Use our mock ObjectId instead of requiring mongodb
          filter = { _id: new ObjectId(id) };
        } catch (err) {
          console.error('Error converting to ObjectId:', err);
        }
      }

      const result = await (collection as any).updateOne(
        filter,
        { $set: data as any }
      );
      return result;
    } catch (error) {
      console.error(`Error updating document in ${this.collectionName}:`, error);
      return { modifiedCount: 0 };
    }
  }

  /**
   * Delete a document by ID
   * @param id Document ID
   */
  async deleteById(id: string) {
    try {
      const collection = await getCollection(this.collectionName);
      let filter: any = { _id: id };

      // If ID is a valid ObjectId, try to use it
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        try {
          // Use our mock ObjectId instead of requiring mongodb
          filter = { _id: new ObjectId(id) };
        } catch (err) {
          console.error('Error converting to ObjectId:', err);
        }
      }

      const result = await (collection as any).deleteOne(filter);
      return result;
    } catch (error) {
      console.error(`Error deleting document in ${this.collectionName}:`, error);
      return { deletedCount: 0 };
    }
  }

  /**
   * Count documents in the collection
   * @param query Optional query filter
   */
  async count(query = {}) {
    try {
      const collection = await getCollection(this.collectionName);
      return await (collection as any).countDocuments(query);
    } catch (error) {
      console.error(`Error counting documents in ${this.collectionName}:`, error);
      return 0;
    }
  }
}
