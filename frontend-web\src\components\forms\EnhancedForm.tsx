/**
 * Enhanced Form Component with Comprehensive Validation
 * Uses the form validation utilities for consistent form handling
 */

import React from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { Save, Cancel } from '@mui/icons-material';
import { useFormValidation, ValidationRules, commonValidationRules, getFieldProps } from '../../utils/formValidationUtils';

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'checkbox' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  multiline?: boolean;
  rows?: number;
  placeholder?: string;
  disabled?: boolean;
}

export interface EnhancedFormProps {
  title?: string;
  fields: FormField[];
  initialValues?: Record<string, any>;
  validationRules?: ValidationRules;
  onSubmit: (values: Record<string, any>) => Promise<void>;
  onCancel?: () => void;
  submitButtonText?: string;
  cancelButtonText?: string;
  showCancelButton?: boolean;
  loading?: boolean;
  error?: string;
  success?: string;
}

const EnhancedForm: React.FC<EnhancedFormProps> = ({
  title,
  fields,
  initialValues = {},
  validationRules = {},
  onSubmit,
  onCancel,
  submitButtonText = 'Save',
  cancelButtonText = 'Cancel',
  showCancelButton = true,
  loading: externalLoading = false,
  error: externalError,
  success: externalSuccess
}) => {
  // Generate validation rules for fields
  const generateValidationRules = (): ValidationRules => {
    const rules: ValidationRules = { ...validationRules };
    
    fields.forEach(field => {
      if (!rules[field.name]) {
        rules[field.name] = {};
      }
      
      // Set required if specified
      if (field.required) {
        rules[field.name].required = true;
      }
      
      // Apply common validation rules based on field type
      switch (field.type) {
        case 'email':
          rules[field.name] = { ...commonValidationRules.email, ...rules[field.name] };
          break;
        case 'password':
          rules[field.name] = { ...commonValidationRules.password, ...rules[field.name] };
          break;
        case 'number':
          rules[field.name] = {
            ...rules[field.name],
            custom: (value: any) => {
              if (value && isNaN(Number(value))) {
                return 'Please enter a valid number';
              }
              return null;
            }
          };
          break;
        case 'date':
          rules[field.name] = { ...commonValidationRules.date, ...rules[field.name] };
          break;
      }
    });
    
    return rules;
  };

  const {
    values,
    errors,
    touched,
    isSubmitting,
    isValid,
    setValue,
    validateSingleField,
    handleSubmit,
    reset
  } = useFormValidation(initialValues, generateValidationRules(), onSubmit);

  const renderField = (field: FormField) => {
    const fieldProps = getFieldProps(field.name, { values, errors, touched }, setValue, validateSingleField);

    switch (field.type) {
      case 'select':
        return (
          <FormControl fullWidth error={!!errors[field.name]} key={field.name}>
            <InputLabel>{field.label}</InputLabel>
            <Select
              {...fieldProps}
              label={field.label}
              disabled={field.disabled}
            >
              {field.options?.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {errors[field.name] && (
              <FormHelperText>{errors[field.name]}</FormHelperText>
            )}
          </FormControl>
        );

      case 'checkbox':
        return (
          <FormControlLabel
            key={field.name}
            control={
              <Checkbox
                checked={values[field.name] || false}
                onChange={(e) => setValue(field.name, e.target.checked)}
                disabled={field.disabled}
              />
            }
            label={field.label}
          />
        );

      case 'textarea':
        return (
          <TextField
            key={field.name}
            {...fieldProps}
            label={field.label}
            fullWidth
            multiline
            rows={field.rows || 4}
            placeholder={field.placeholder}
            disabled={field.disabled}
          />
        );

      default:
        return (
          <TextField
            key={field.name}
            {...fieldProps}
            label={field.label}
            type={field.type}
            fullWidth
            placeholder={field.placeholder}
            disabled={field.disabled}
            multiline={field.multiline}
            rows={field.multiline ? field.rows || 3 : undefined}
          />
        );
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      {title && (
        <Typography variant="h5" component="h2" gutterBottom>
          {title}
        </Typography>
      )}

      {externalError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {externalError}
        </Alert>
      )}

      {externalSuccess && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {externalSuccess}
        </Alert>
      )}

      <Grid container spacing={2}>
        {fields.map(field => (
          <Grid item xs={12} sm={field.type === 'checkbox' ? 12 : 6} key={field.name}>
            {renderField(field)}
          </Grid>
        ))}
      </Grid>

      <Box sx={{ mt: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
        {showCancelButton && onCancel && (
          <Button
            variant="outlined"
            onClick={onCancel}
            startIcon={<Cancel />}
            disabled={isSubmitting || externalLoading}
          >
            {cancelButtonText}
          </Button>
        )}
        
        <Button
          type="submit"
          variant="contained"
          startIcon={isSubmitting || externalLoading ? <CircularProgress size={20} /> : <Save />}
          disabled={isSubmitting || externalLoading || !isValid}
        >
          {isSubmitting || externalLoading ? 'Saving...' : submitButtonText}
        </Button>
      </Box>
    </Box>
  );
};

export default EnhancedForm;
