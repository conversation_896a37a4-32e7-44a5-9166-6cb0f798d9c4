// import { api } from '../utils/api';
import { useSnackbar } from '../hooks/useSnackbar';

export interface SMSMessage {
  to: string;
  message: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Service for sending SMS notifications
 */
export const smsService = {
  /**
   * Send an SMS message
   * @param message SMS message object
   * @returns Promise with the result
   */
  sendSMS: async (message: SMSMessage): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
      // In a real implementation, this would call the backend API
      // For now, we'll simulate a successful response
      console.log('Sending SMS:', message);

      // Simulate API call
      const mockResponse = {
        success: true,
        messageId: `sms-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString()
      };

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return mockResponse;
    } catch (error) {
      console.error('Error sending SMS:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error sending SMS'
      };
    }
  },

  /**
   * Send a birth prediction alert SMS
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param daysRemaining Days remaining until expected birth
   * @param staffPhone Staff phone number
   * @returns Promise with the result
   */
  sendBirthPredictionAlert: async (
    animalId: string,
    animalName: string,
    daysRemaining: number,
    staffPhone: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    const message = {
      to: staffPhone,
      message: `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days. Please prepare accordingly.`,
      priority: 'high' as const
    };

    return await smsService.sendSMS(message);
  },

  /**
   * Send a health alert SMS
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param issue Health issue description
   * @param staffPhone Staff phone number
   * @returns Promise with the result
   */
  sendHealthAlert: async (
    animalId: string,
    animalName: string,
    issue: string,
    staffPhone: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    const message = {
      to: staffPhone,
      message: `HEALTH ALERT: ${animalName} (ID: ${animalId}) has a health issue: ${issue}. Immediate attention required.`,
      priority: 'critical' as const
    };

    return await smsService.sendSMS(message);
  }
};

/**
 * Hook for using the SMS service with integrated snackbar notifications
 */
export const useSMSService = () => {
  const { showSnackbar } = useSnackbar();

  const sendSMS = async (message: SMSMessage) => {
    try {
      const result = await smsService.sendSMS(message);

      if (result.success) {
        showSnackbar('SMS sent successfully', 'success');
        return result;
      } else {
        showSnackbar(`Failed to send SMS: ${result.error}`, 'error');
        return result;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showSnackbar(`Error sending SMS: ${errorMessage}`, 'error');
      return { success: false, error: errorMessage };
    }
  };

  const sendBirthPredictionAlert = async (
    animalId: string,
    animalName: string,
    daysRemaining: number,
    staffPhone: string
  ) => {
    try {
      const result = await smsService.sendBirthPredictionAlert(
        animalId,
        animalName,
        daysRemaining,
        staffPhone
      );

      if (result.success) {
        showSnackbar(`Birth alert for ${animalName} sent successfully`, 'success');
        return result;
      } else {
        showSnackbar(`Failed to send birth alert: ${result.error}`, 'error');
        return result;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      showSnackbar(`Error sending birth alert: ${errorMessage}`, 'error');
      return { success: false, error: errorMessage };
    }
  };

  return {
    sendSMS,
    sendBirthPredictionAlert
  };
};

export default smsService;
