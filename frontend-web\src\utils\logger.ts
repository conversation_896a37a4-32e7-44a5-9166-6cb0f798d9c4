/**
 * Log levels for categorizing log messages
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

/**
 * Configuration for the logger
 */
interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteUrl?: string;
  batchSize?: number;
  includeTimestamp: boolean;
}

/**
 * Default configuration for the logger
 */
const defaultConfig: LoggerConfig = {
  minLevel: process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG,
  enableConsole: true,
  enableRemote: process.env.NODE_ENV === 'production',
  includeTimestamp: true,
  batchSize: 10
};

/**
 * Current configuration for the logger
 */
let config: LoggerConfig = { ...defaultConfig };

/**
 * Queue for batching log messages to send to remote server
 */
const logQueue: any[] = [];

/**
 * Configure the logger
 * @param newConfig The new configuration to apply
 */
export function configureLogger(newConfig: Partial<LoggerConfig>): void {
  config = { ...config, ...newConfig };
}

/**
 * Format a log message with additional context
 * @param level The log level
 * @param message The log message
 * @param context Additional context for the log message
 * @returns The formatted log message
 */
function formatLogMessage(level: LogLevel, message: string, context?: any): string {
  const timestamp = config.includeTimestamp ? new Date().toISOString() : '';
  const prefix = timestamp ? `[${timestamp}] [${level}]` : `[${level}]`;
  return `${prefix} ${message}`;
}

/**
 * Send a batch of log messages to the remote server
 */
function sendLogsToRemote(): void {
  if (!config.enableRemote || !config.remoteUrl || logQueue.length === 0) {
    return;
  }

  const batch = logQueue.splice(0, config.batchSize || 10);
  
  fetch(config.remoteUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ logs: batch }),
    keepalive: true
  }).catch(error => {
    // If sending fails, add the logs back to the queue
    logQueue.unshift(...batch);
    console.error('Failed to send logs to remote server:', error);
  });
}

/**
 * Log a message at the specified level
 * @param level The log level
 * @param message The log message
 * @param context Additional context for the log message
 */
function log(level: LogLevel, message: string, context?: any): void {
  // Check if the log level is enabled
  if (getLevelValue(level) < getLevelValue(config.minLevel)) {
    return;
  }

  const formattedMessage = formatLogMessage(level, message, context);
  const logEntry = {
    level,
    message,
    context,
    timestamp: new Date().toISOString()
  };

  // Log to console
  if (config.enableConsole) {
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, context);
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, context);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, context);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage, context);
        break;
    }
  }

  // Add to queue for remote logging
  if (config.enableRemote) {
    logQueue.push(logEntry);
    
    // Send logs if we've reached the batch size
    if (logQueue.length >= (config.batchSize || 10)) {
      sendLogsToRemote();
    }
  }
}

/**
 * Get the numeric value of a log level for comparison
 * @param level The log level
 * @returns The numeric value of the log level
 */
function getLevelValue(level: LogLevel): number {
  switch (level) {
    case LogLevel.DEBUG:
      return 0;
    case LogLevel.INFO:
      return 1;
    case LogLevel.WARN:
      return 2;
    case LogLevel.ERROR:
      return 3;
    default:
      return 0;
  }
}

/**
 * Log a debug message
 * @param message The log message
 * @param context Additional context for the log message
 */
export function debug(message: string, context?: any): void {
  log(LogLevel.DEBUG, message, context);
}

/**
 * Log an info message
 * @param message The log message
 * @param context Additional context for the log message
 */
export function info(message: string, context?: any): void {
  log(LogLevel.INFO, message, context);
}

/**
 * Log a warning message
 * @param message The log message
 * @param context Additional context for the log message
 */
export function warn(message: string, context?: any): void {
  log(LogLevel.WARN, message, context);
}

/**
 * Log an error message
 * @param message The log message
 * @param context Additional context for the log message
 */
export function error(message: string, context?: any): void {
  log(LogLevel.ERROR, message, context);
}

/**
 * Flush any pending logs to the remote server
 * This should be called before the application unloads
 */
export function flushLogs(): void {
  if (config.enableRemote && logQueue.length > 0) {
    sendLogsToRemote();
  }
}

// Set up event listener to flush logs before the page unloads
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', flushLogs);
}

export default {
  LogLevel,
  configureLogger,
  debug,
  info,
  warn,
  error,
  flushLogs
};
