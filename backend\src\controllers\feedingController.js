// Note: Using mock data for now - database integration to be added later
// const logger = require('../utils/logger');

const feedingController = {
  // Get feeding overview (for /api/feeding route)
  getFeeding: async (req, res) => {
    try {
      // Mock feeding overview data
      const feedingOverview = {
        summary: {
          totalFeedings: 156,
          todayFeedings: 12,
          scheduledFeedings: 8,
          feedConsumption: 2500, // kg
          averageFeedPerAnimal: 15.6 // kg
        },
        recentFeedings: [
          {
            id: '1',
            animalId: 'A001',
            animalTag: 'COW001',
            feedType: 'Hay',
            quantity: 25,
            unit: 'kg',
            date: '2024-12-21',
            time: '08:00',
            location: 'Pasture A'
          },
          {
            id: '2',
            animalId: 'A002',
            animalTag: 'COW002',
            feedType: 'Concentrate',
            quantity: 5,
            unit: 'kg',
            date: '2024-12-21',
            time: '08:30',
            location: 'Barn 1'
          }
        ],
        upcomingSchedules: [
          {
            id: '3',
            scheduledTime: '12:00',
            feedType: 'Silage',
            location: 'Pasture B',
            animalsCount: 15
          }
        ]
      };

      res.json({
        success: true,
        data: feedingOverview
      });
    } catch (error) {
      console.error('Error fetching feeding overview:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch feeding overview'
      });
    }
  },

  // Feeding Management
  getFeedingRecords: async (req, res) => {
    try {
      const { feedType, location, fromDate, toDate } = req.query;

      // Mock feeding records data
      let feedingRecords = [
        {
          id: '1',
          animalId: 'A001',
          animalTag: 'COW001',
          feedType: 'Hay',
          quantity: 25,
          unit: 'kg',
          date: '2024-12-21',
          time: '08:00',
          location: 'Pasture A',
          notes: 'Regular morning feeding'
        },
        {
          id: '2',
          animalId: 'A002',
          animalTag: 'COW002',
          feedType: 'Concentrate',
          quantity: 5,
          unit: 'kg',
          date: '2024-12-21',
          time: '08:30',
          location: 'Barn 1',
          notes: 'Supplemental feeding'
        },
        {
          id: '3',
          animalId: 'A003',
          animalTag: 'COW003',
          feedType: 'Silage',
          quantity: 20,
          unit: 'kg',
          date: '2024-12-20',
          time: '17:00',
          location: 'Pasture B',
          notes: 'Evening feeding'
        }
      ];

      // Filter by parameters
      if (feedType) {
        feedingRecords = feedingRecords.filter(record => record.feedType.toLowerCase().includes(feedType.toLowerCase()));
      }
      if (location) {
        feedingRecords = feedingRecords.filter(record => record.location.toLowerCase().includes(location.toLowerCase()));
      }
      if (fromDate) {
        feedingRecords = feedingRecords.filter(record => record.date >= fromDate);
      }
      if (toDate) {
        feedingRecords = feedingRecords.filter(record => record.date <= toDate);
      }

      res.json({
        success: true,
        data: feedingRecords,
        total: feedingRecords.length
      });
    } catch (error) {
      console.error('Error fetching feeding records:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch feeding records'
      });
    }
  },

  getFeedInventory: async (req, res) => {
    try {
      const { feedType, location } = req.query;

      // Mock feed inventory data
      let feedInventory = [
        {
          id: '1',
          feedType: 'Hay',
          quantity: 500,
          unit: 'kg',
          location: 'Barn 1',
          cost: 2500,
          supplier: 'AgriSupply Pro',
          purchaseDate: '2024-12-01',
          expiryDate: '2025-06-01',
          notes: 'High-quality hay for cattle'
        },
        {
          id: '2',
          feedType: 'Concentrate',
          quantity: 200,
          unit: 'kg',
          location: 'Feed Storage',
          cost: 1800,
          supplier: 'Feed Masters',
          purchaseDate: '2024-12-10',
          expiryDate: '2025-12-10',
          notes: 'Protein-rich concentrate feed'
        },
        {
          id: '3',
          feedType: 'Silage',
          quantity: 1000,
          unit: 'kg',
          location: 'Silo 1',
          cost: 3000,
          supplier: 'Local Farm Co-op',
          purchaseDate: '2024-11-15',
          expiryDate: '2025-05-15',
          notes: 'Fermented corn silage'
        }
      ];

      // Filter by parameters
      if (feedType) {
        feedInventory = feedInventory.filter(item => item.feedType.toLowerCase().includes(feedType.toLowerCase()));
      }
      if (location) {
        feedInventory = feedInventory.filter(item => item.location.toLowerCase().includes(location.toLowerCase()));
      }

      res.json({
        success: true,
        data: feedInventory,
        total: feedInventory.length
      });
    } catch (error) {
      console.error('Error fetching feed inventory:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to fetch feed inventory'
      });
    }
  },

  createFeedingRecord: async (req, res) => {
    try {
      const { date, feedType, quantity, location, animalIds, notes } = req.body;

      // Mock creation - generate new feeding record
      const newRecord = {
        id: Date.now().toString(),
        date,
        feedType,
        quantity,
        location,
        animalIds,
        notes,
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        data: newRecord,
        message: 'Feeding record created successfully'
      });
    } catch (error) {
      console.error('Error creating feeding record:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to create feeding record'
      });
    }
  },

  updateFeedInventory: async (req, res) => {
    try {
      const { id } = req.params;
      const { feedType, quantity, location, cost, supplier, purchaseDate, expiryDate, notes } = req.body;

      // Mock update - return updated inventory item
      const updatedItem = {
        id,
        feedType,
        quantity,
        location,
        cost,
        supplier,
        purchaseDate,
        expiryDate,
        notes,
        updatedAt: new Date().toISOString()
      };

      res.json({
        success: true,
        data: updatedItem,
        message: 'Feed inventory updated successfully'
      });
    } catch (error) {
      console.error('Error updating feed inventory:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to update feed inventory'
      });
    }
  },

  addFeedInventory: async (req, res) => {
    try {
      const { feedType, quantity, location, cost, supplier, purchaseDate, expiryDate, notes } = req.body;

      // Mock creation - generate new inventory item
      const newItem = {
        id: Date.now().toString(),
        feedType,
        quantity,
        location,
        cost,
        supplier,
        purchaseDate,
        expiryDate,
        notes,
        createdAt: new Date().toISOString()
      };

      res.status(201).json({
        success: true,
        data: newItem,
        message: 'Feed inventory item added successfully'
      });
    } catch (error) {
      console.error('Error adding feed inventory:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Failed to add feed inventory'
      });
    }
  }
};

module.exports = feedingController;
