import React, { ReactNode } from 'react';
import { Box, Typography, Breadcrumbs, Link, useTheme, alpha, IconButton } from '@mui/material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { Home, NavigateNext, ArrowBack } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import UnifiedModuleContainer from '../components/common/UnifiedModuleContainer';

interface UnifiedModuleLayoutProps {
  title: string;
  subtitle?: string;
  module: string;
  icon?: ReactNode;
  showBackButton?: boolean;
  onBack?: () => void;
  breadcrumbs?: Array<{ label: string; path: string }>;
  children: ReactNode;
}

/**
 * UnifiedModuleLayout - A consistent layout for all module pages
 * This component provides a unified layout and styling for all module pages
 */
const UnifiedModuleLayout: React.FC<UnifiedModuleLayoutProps> = ({
  title,
  subtitle,
  module,
  icon,
  showBackButton = false,
  onBack,
  breadcrumbs,
  children
}) => {
  const theme = useTheme();
  const location = useLocation();
  const { translate } = useLanguage();
  
  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      case 'analytics':
        return theme.palette.primary.dark;
      default:
        return theme.palette.primary.main;
    }
  };
  
  const moduleColor = getModuleColor();
  
  // Generate breadcrumbs from location if not provided
  const generateBreadcrumbs = () => {
    if (breadcrumbs) return breadcrumbs;
    
    const paths = location.pathname.split('/').filter(Boolean);
    return paths.map((path, index) => {
      const url = `/${paths.slice(0, index + 1).join('/')}`;
      return {
        label: path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' '),
        path: url
      };
    });
  };
  
  const pathBreadcrumbs = generateBreadcrumbs();
  
  return (
    <UnifiedModuleContainer module={module}>
      <Box sx={{ mb: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs
          separator={<NavigateNext fontSize="small" />}
          aria-label="breadcrumb"
          sx={{ mb: 2 }}
        >
          <Link
            component={RouterLink}
            to="/dashboard"
            color="inherit"
            sx={{
              display: 'flex',
              alignItems: 'center',
              textDecoration: 'none',
              '&:hover': { textDecoration: 'underline' }
            }}
          >
            <Home fontSize="small" sx={{ mr: 0.5 }} />
            {translate('nav.dashboard')}
          </Link>
          
          {pathBreadcrumbs.map((crumb, index) => (
            <Link
              key={index}
              component={RouterLink}
              to={crumb.path}
              color={index === pathBreadcrumbs.length - 1 ? 'textPrimary' : 'inherit'}
              sx={{
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
              aria-current={index === pathBreadcrumbs.length - 1 ? 'page' : undefined}
            >
              {crumb.label}
            </Link>
          ))}
        </Breadcrumbs>
        
        {/* Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 3,
            p: 3,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(moduleColor, 0.15)}, ${alpha(moduleColor, 0.05)})`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(moduleColor, 0.1)}`,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          {showBackButton && (
            <IconButton
              onClick={onBack}
              sx={{ mr: 2, color: moduleColor }}
            >
              <ArrowBack />
            </IconButton>
          )}
          
          {icon && (
            <Box
              sx={{
                mr: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 48,
                height: 48,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${moduleColor}, ${alpha(moduleColor, 0.8)})`,
                color: 'white',
                boxShadow: `0 4px 10px ${alpha(moduleColor, 0.3)}`,
              }}
            >
              {icon}
            </Box>
          )}
          
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="subtitle1" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        </Box>
      </Box>
      
      {/* Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {children}
      </motion.div>
    </UnifiedModuleContainer>
  );
};

export default UnifiedModuleLayout;
