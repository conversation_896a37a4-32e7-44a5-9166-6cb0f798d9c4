# API Routes Analysis

- **Total defined routes:** 113
- **Total used routes:** 31
- **Missing routes (used but not defined):** 31
- **Unused routes (defined but not used):** 113

## Missing Routes

- `POST /api/:param/api/animals`
- `GET /api/:param`
- `GET /api/:param/api/financial/transactions`
- `GET /api/:param/api/financial/sales`
- `GET /api/:param/api/financial/budgets`
- `GET /api/:param/api/notifications`
- `PUT /api/:param/api/notifications/:param/read`
- `POST /api/:param/api/notifications`
- `GET /api/:param/animals`
- `GET /api/:param/animals/:param`
- `GET /api/:param/business-analysis/dashboard`
- `GET /api/:param/business-analysis/strategy`
- `POST /api/:param/business-analysis/reports/predictive`
- `GET /api/:param:param`
- `POST /api/:param:param`
- `PUT /api/:param:param`
- `DELETE /api/:param:param`
- `GET /api/:param:param?animalId=:param`
- `GET /api/:param/suppliers`
- `GET /api/:param/suppliers/:param`
- `GET /api/:param/suppliers/:param/products`
- `GET /api/:param/users`
- `GET /api/:param/users/:param`
- `GET /api/:param/users/username/:param`
- `POST /api/:param/users`
- `PUT /api/:param/users/:param`
- `DELETE /api/:param/users/:param`
- `POST /api/:param/auth/login`
- `GET /api/:param/auth/me`
- `GET /api/${API_BASE_URL.replace(/\/api$/, `
- `GET /api/:param/db-status`

## Unused Routes

- `GET /api/analytics/metrics/:type`
- `GET /api/animalRoutes/`
- `GET /api/animalRoutes/:id`
- `POST /api/animalRoutes/`
- `PUT /api/animalRoutes/:id`
- `DELETE /api/animalRoutes/:id`
- `GET /api/animals/`
- `GET /api/animals/statistics`
- `GET /api/animals/:id`
- `GET /api/animals/tag/:tagNumber`
- `POST /api/animals/`
- `PUT /api/animals/:id`
- `DELETE /api/animals/:id`
- `GET /api/assetRoutes/`
- `GET /api/assetRoutes/animals`
- `GET /api/assetRoutes/animals/:id`
- `POST /api/assetRoutes/animals`
- `PUT /api/assetRoutes/animals/:id`
- `POST /api/assetRoutes/animals/:id/retire`
- `GET /api/assetRoutes/retirement/candidates`
- `GET /api/assetRoutes/retirement/stats`
- `POST /api/auth/login`
- `POST /api/auth/forgot-password`
- `POST /api/auth/reset-password`
- `GET /api/auth/me`
- `GET /api/breeding/`
- `POST /api/breeding/`
- `GET /api/breedingRoutes/`
- `GET /api/breedingRoutes/records`
- `GET /api/breedingRoutes/calving`
- `POST /api/breedingRoutes/records`
- `PUT /api/breedingRoutes/records/:id`
- `DELETE /api/breedingRoutes/records/:id`
- `POST /api/breedingRoutes/calving`
- `GET /api/business/`
- `GET /api/business/:id`
- `POST /api/business/`
- `PUT /api/business/:id`
- `DELETE /api/business/:id`
- `GET /api/businessAnalysisRoutes/`
- `GET /api/businessAnalysisRoutes/dashboard`
- `GET /api/businessAnalysisRoutes/strategy`
- `GET /api/businessAnalysisRoutes/kpis`
- `POST /api/businessAnalysisRoutes/kpis`
- `PUT /api/businessAnalysisRoutes/kpis/:id`
- `GET /api/businessAnalysisRoutes/predictions`
- `GET /api/businessAnalysisRoutes/insights`
- `POST /api/businessAnalysisRoutes/insights`
- `GET /api/businessAnalysisRoutes/risks`
- `GET /api/businessAnalysisRoutes/opportunities`
- `GET /api/businessAnalysisRoutes/goals`
- `GET /api/businessAnalysisRoutes/initiatives`
- `GET /api/businessAnalysisRoutes/swot`
- `PUT /api/businessAnalysisRoutes/swot`
- `GET /api/businessAnalysisRoutes/scenarios`
- `GET /api/feeding/`
- `GET /api/feedingRoutes/`
- `GET /api/feedingRoutes/records`
- `GET /api/feedingRoutes/inventory`
- `POST /api/feedingRoutes/records`
- `POST /api/feedingRoutes/inventory`
- `PUT /api/feedingRoutes/inventory/:id`
- `GET /api/financial/`
- `GET /api/financial/:id`
- `POST /api/financial/`
- `PUT /api/financial/:id`
- `DELETE /api/financial/:id`
- `GET /api/health/`
- `POST /api/health/`
- `GET /api/healthRoutes/`
- `GET /api/healthRoutes/records`
- `GET /api/healthRoutes/vaccinations`
- `POST /api/healthRoutes/records`
- `PUT /api/healthRoutes/records/:id`
- `DELETE /api/healthRoutes/records/:id`
- `GET /api/inventory/`
- `GET /api/inventory/:id`
- `POST /api/inventory/`
- `PUT /api/inventory/:id`
- `DELETE /api/inventory/:id`
- `GET /api/inventoryRoutes/`
- `GET /api/inventoryRoutes/items`
- `GET /api/inventoryRoutes/items/:id`
- `POST /api/inventoryRoutes/items`
- `PUT /api/inventoryRoutes/items/:id`
- `DELETE /api/inventoryRoutes/items/:id`
- `GET /api/notifications/`
- `GET /api/notifications/:id`
- `POST /api/notifications/`
- `PUT /api/notifications/:id/read`
- `GET /api/reports/`
- `GET /api/reports/:id`
- `POST /api/reports/`
- `PUT /api/reports/:id`
- `DELETE /api/reports/:id`
- `GET /api/reports/:id/download`
- `GET /api/reports/stats/overview`
- `GET /api/settings/`
- `GET /api/settings/:id`
- `POST /api/settings/`
- `PUT /api/settings/:id`
- `DELETE /api/settings/:id`
- `GET /api/supplierRoutes/`
- `GET /api/supplierRoutes/:id`
- `GET /api/supplierRoutes/:id/products`
- `POST /api/supplierRoutes/update`
- `GET /api/users/profile`
- `POST /api/users/`
- `GET /api/users/`
- `GET /api/users/username/:username`
- `GET /api/users/:id`
- `PUT /api/users/:id`
- `DELETE /api/users/:id`

## All Defined Routes

- `GET /api/analytics/metrics/:type`
- `GET /api/animalRoutes/`
- `GET /api/animalRoutes/:id`
- `POST /api/animalRoutes/`
- `PUT /api/animalRoutes/:id`
- `DELETE /api/animalRoutes/:id`
- `GET /api/animals/`
- `GET /api/animals/statistics`
- `GET /api/animals/:id`
- `GET /api/animals/tag/:tagNumber`
- `POST /api/animals/`
- `PUT /api/animals/:id`
- `DELETE /api/animals/:id`
- `GET /api/assetRoutes/`
- `GET /api/assetRoutes/animals`
- `GET /api/assetRoutes/animals/:id`
- `POST /api/assetRoutes/animals`
- `PUT /api/assetRoutes/animals/:id`
- `POST /api/assetRoutes/animals/:id/retire`
- `GET /api/assetRoutes/retirement/candidates`
- `GET /api/assetRoutes/retirement/stats`
- `POST /api/auth/login`
- `POST /api/auth/forgot-password`
- `POST /api/auth/reset-password`
- `GET /api/auth/me`
- `GET /api/breeding/`
- `POST /api/breeding/`
- `GET /api/breedingRoutes/`
- `GET /api/breedingRoutes/records`
- `GET /api/breedingRoutes/calving`
- `POST /api/breedingRoutes/records`
- `PUT /api/breedingRoutes/records/:id`
- `DELETE /api/breedingRoutes/records/:id`
- `POST /api/breedingRoutes/calving`
- `GET /api/business/`
- `GET /api/business/:id`
- `POST /api/business/`
- `PUT /api/business/:id`
- `DELETE /api/business/:id`
- `GET /api/businessAnalysisRoutes/`
- `GET /api/businessAnalysisRoutes/dashboard`
- `GET /api/businessAnalysisRoutes/strategy`
- `GET /api/businessAnalysisRoutes/kpis`
- `POST /api/businessAnalysisRoutes/kpis`
- `PUT /api/businessAnalysisRoutes/kpis/:id`
- `GET /api/businessAnalysisRoutes/predictions`
- `GET /api/businessAnalysisRoutes/insights`
- `POST /api/businessAnalysisRoutes/insights`
- `GET /api/businessAnalysisRoutes/risks`
- `GET /api/businessAnalysisRoutes/opportunities`
- `GET /api/businessAnalysisRoutes/goals`
- `GET /api/businessAnalysisRoutes/initiatives`
- `GET /api/businessAnalysisRoutes/swot`
- `PUT /api/businessAnalysisRoutes/swot`
- `GET /api/businessAnalysisRoutes/scenarios`
- `GET /api/feeding/`
- `GET /api/feedingRoutes/`
- `GET /api/feedingRoutes/records`
- `GET /api/feedingRoutes/inventory`
- `POST /api/feedingRoutes/records`
- `POST /api/feedingRoutes/inventory`
- `PUT /api/feedingRoutes/inventory/:id`
- `GET /api/financial/`
- `GET /api/financial/:id`
- `POST /api/financial/`
- `PUT /api/financial/:id`
- `DELETE /api/financial/:id`
- `GET /api/health/`
- `POST /api/health/`
- `GET /api/healthRoutes/`
- `GET /api/healthRoutes/records`
- `GET /api/healthRoutes/vaccinations`
- `POST /api/healthRoutes/records`
- `PUT /api/healthRoutes/records/:id`
- `DELETE /api/healthRoutes/records/:id`
- `GET /api/inventory/`
- `GET /api/inventory/:id`
- `POST /api/inventory/`
- `PUT /api/inventory/:id`
- `DELETE /api/inventory/:id`
- `GET /api/inventoryRoutes/`
- `GET /api/inventoryRoutes/items`
- `GET /api/inventoryRoutes/items/:id`
- `POST /api/inventoryRoutes/items`
- `PUT /api/inventoryRoutes/items/:id`
- `DELETE /api/inventoryRoutes/items/:id`
- `GET /api/notifications/`
- `GET /api/notifications/:id`
- `POST /api/notifications/`
- `PUT /api/notifications/:id/read`
- `GET /api/reports/`
- `GET /api/reports/:id`
- `POST /api/reports/`
- `PUT /api/reports/:id`
- `DELETE /api/reports/:id`
- `GET /api/reports/:id/download`
- `GET /api/reports/stats/overview`
- `GET /api/settings/`
- `GET /api/settings/:id`
- `POST /api/settings/`
- `PUT /api/settings/:id`
- `DELETE /api/settings/:id`
- `GET /api/supplierRoutes/`
- `GET /api/supplierRoutes/:id`
- `GET /api/supplierRoutes/:id/products`
- `POST /api/supplierRoutes/update`
- `GET /api/users/profile`
- `POST /api/users/`
- `GET /api/users/`
- `GET /api/users/username/:username`
- `GET /api/users/:id`
- `PUT /api/users/:id`
- `DELETE /api/users/:id`
