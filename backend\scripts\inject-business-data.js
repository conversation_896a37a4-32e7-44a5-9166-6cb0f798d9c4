/**
 * Inject Business Analysis Data Script
 * 
 * This script injects realistic business analysis data into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Business KPIs to inject
const businessKpis = [
  {
    _id: new ObjectId(),
    name: 'Average Daily Milk Production',
    value: 18.5,
    target: 20,
    unit: 'liters/cow',
    status: 'warning',
    trend: 5, // 5% increase from previous period
    category: 'operational',
    description: 'Average daily milk production per cow',
    period: 'monthly',
    date: new Date('2024-04-30'),
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Feed Cost per Animal',
    value: 45,
    target: 40,
    unit: 'ZAR/day',
    status: 'danger',
    trend: 8, // 8% increase from previous period
    category: 'financial',
    description: 'Average daily feed cost per animal',
    period: 'monthly',
    date: new Date('2024-04-30'),
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Calving Rate',
    value: 85,
    target: 80,
    unit: '%',
    status: 'success',
    trend: 3, // 3% increase from previous period
    category: 'breeding',
    description: 'Percentage of successful calvings from confirmed pregnancies',
    period: 'quarterly',
    date: new Date('2024-03-31'),
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Mortality Rate',
    value: 2.5,
    target: 3,
    unit: '%',
    status: 'success',
    trend: -10, // 10% decrease from previous period
    category: 'health',
    description: 'Percentage of animal deaths in the herd',
    period: 'quarterly',
    date: new Date('2024-03-31'),
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Profit Margin',
    value: 22,
    target: 25,
    unit: '%',
    status: 'warning',
    trend: -2, // 2% decrease from previous period
    category: 'financial',
    description: 'Overall profit margin for livestock operations',
    period: 'quarterly',
    date: new Date('2024-03-31'),
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Business predictions to inject
const businessPredictions = [
  {
    _id: new ObjectId(),
    title: 'Milk Production Forecast',
    description: 'Predicted milk production for the next quarter',
    category: 'operational',
    currentValue: 18.5,
    predictedValue: 19.8,
    unit: 'liters/cow/day',
    confidenceLevel: 85,
    trend: 'increasing',
    factors: [
      'Improved feed quality',
      'Seasonal temperature changes',
      'New milking equipment'
    ],
    timeframe: {
      start: new Date('2024-05-01'),
      end: new Date('2024-07-31')
    },
    methodology: 'Time series analysis with seasonal adjustment',
    recommendations: [
      'Maintain current feed quality',
      'Monitor temperature in barns',
      'Continue staff training on new equipment'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    title: 'Feed Cost Projection',
    description: 'Projected feed costs for the next quarter',
    category: 'financial',
    currentValue: 45,
    predictedValue: 42,
    unit: 'ZAR/animal/day',
    confidenceLevel: 75,
    trend: 'decreasing',
    factors: [
      'New supplier contracts',
      'Bulk purchasing discounts',
      'Improved feed efficiency'
    ],
    timeframe: {
      start: new Date('2024-05-01'),
      end: new Date('2024-07-31')
    },
    methodology: 'Regression analysis with market trend factors',
    recommendations: [
      'Finalize new supplier contracts',
      'Implement feed efficiency monitoring',
      'Consider additional bulk purchases'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    title: 'Market Price Forecast',
    description: 'Predicted livestock market prices for the next quarter',
    category: 'financial',
    currentValue: 38,
    predictedValue: 42,
    unit: 'ZAR/kg',
    confidenceLevel: 70,
    trend: 'increasing',
    factors: [
      'Seasonal demand patterns',
      'Reduced supply in market',
      'Export opportunities'
    ],
    timeframe: {
      start: new Date('2024-05-01'),
      end: new Date('2024-07-31')
    },
    methodology: 'Market analysis with historical comparison',
    recommendations: [
      'Consider delaying some sales to later in the quarter',
      'Explore export market opportunities',
      'Monitor market reports weekly'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Business insights to inject
const businessInsights = [
  {
    _id: new ObjectId(),
    title: 'Feed Efficiency Opportunity',
    description: 'Analysis shows that switching to a mixed ration with higher protein content could improve feed efficiency by 12% for dairy cattle.',
    impact: 'high',
    actionable: true,
    suggestedAction: 'Implement new feed formulation for dairy herd and monitor results over 30 days.',
    category: 'feeding',
    dataPoints: [
      'Current feed conversion ratio: 1.8',
      'Benchmark feed conversion ratio: 1.6',
      'Potential cost savings: R35,000 per year'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    title: 'Breeding Timing Optimization',
    description: 'Data analysis reveals that breeding during the second week of detected heat results in 15% higher conception rates compared to first week breeding.',
    impact: 'medium',
    actionable: true,
    suggestedAction: 'Adjust breeding schedule to target second week of heat cycle.',
    category: 'breeding',
    dataPoints: [
      'First week conception rate: 65%',
      'Second week conception rate: 80%',
      'Potential increase in successful pregnancies: 10-15 per year'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    title: 'Preventative Health ROI',
    description: 'Investment in preventative health measures shows a 3:1 return on investment through reduced treatment costs and improved productivity.',
    impact: 'high',
    actionable: true,
    suggestedAction: 'Increase preventative health budget by 20% and implement comprehensive vaccination program.',
    category: 'health',
    dataPoints: [
      'Current preventative health spending: R50,000 per year',
      'Current treatment costs: R180,000 per year',
      'Projected treatment costs after increased prevention: R120,000 per year'
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Business strategies to inject
const businessStrategies = [
  {
    _id: new ObjectId(),
    name: 'Dairy Production Expansion',
    description: 'Strategic plan to increase dairy production capacity and efficiency',
    objectives: [
      'Increase herd size by 20% over 18 months',
      'Improve milk yield per cow by 15%',
      'Reduce feed costs by 10% through improved efficiency'
    ],
    timeframe: {
      start: new Date('2024-06-01'),
      end: new Date('2025-12-31')
    },
    budget: 500000,
    currency: 'ZAR',
    roi: {
      expected: 25,
      timeframe: 24 // months
    },
    status: 'planning',
    milestones: [
      {
        name: 'Facility expansion',
        dueDate: new Date('2024-09-30'),
        status: 'pending'
      },
      {
        name: 'New equipment installation',
        dueDate: new Date('2024-11-30'),
        status: 'pending'
      },
      {
        name: 'Herd expansion phase 1',
        dueDate: new Date('2025-03-31'),
        status: 'pending'
      }
    ],
    risks: [
      {
        name: 'Market price volatility',
        probability: 'medium',
        impact: 'high',
        mitigation: 'Secure long-term contracts with processors'
      },
      {
        name: 'Disease outbreak',
        probability: 'low',
        impact: 'high',
        mitigation: 'Enhance biosecurity and vaccination protocols'
      }
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Value-Added Product Development',
    description: 'Strategy to develop and market value-added dairy products',
    objectives: [
      'Develop 3 new value-added dairy products',
      'Establish processing facility on-farm',
      'Achieve 20% premium over raw milk prices'
    ],
    timeframe: {
      start: new Date('2024-07-01'),
      end: new Date('2025-06-30')
    },
    budget: 350000,
    currency: 'ZAR',
    roi: {
      expected: 30,
      timeframe: 36 // months
    },
    status: 'planning',
    milestones: [
      {
        name: 'Market research completion',
        dueDate: new Date('2024-08-31'),
        status: 'pending'
      },
      {
        name: 'Product development',
        dueDate: new Date('2024-12-31'),
        status: 'pending'
      },
      {
        name: 'Processing facility setup',
        dueDate: new Date('2025-03-31'),
        status: 'pending'
      }
    ],
    risks: [
      {
        name: 'Regulatory compliance',
        probability: 'medium',
        impact: 'high',
        mitigation: 'Engage food safety consultant early in process'
      },
      {
        name: 'Market acceptance',
        probability: 'medium',
        impact: 'high',
        mitigation: 'Conduct thorough market testing before full launch'
      }
    ],
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject business analysis data
async function injectBusinessData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const businessKpisCollection = db.collection('businesskpis');
    const businessPredictionsCollection = db.collection('businesspredictions');
    const businessInsightsCollection = db.collection('businessinsights');
    const businessStrategiesCollection = db.collection('businessstrategies');
    const usersCollection = db.collection('users');

    // Get a user ID for the creator field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const creatorId = adminUser ? adminUser._id : new ObjectId();

    // Insert or update business KPIs
    for (const kpi of businessKpis) {
      // Set the creator to an actual user
      kpi.createdBy = creatorId;
      
      const existingKpi = await businessKpisCollection.findOne({ 
        name: kpi.name,
        period: kpi.period,
        date: kpi.date
      });

      if (existingKpi) {
        logger.info(`Updating business KPI: ${kpi.name}`);
        await businessKpisCollection.updateOne(
          { _id: existingKpi._id },
          { $set: { ...kpi, _id: existingKpi._id } }
        );
      } else {
        logger.info(`Inserting new business KPI: ${kpi.name}`);
        await businessKpisCollection.insertOne(kpi);
      }
    }

    // Insert or update business predictions
    for (const prediction of businessPredictions) {
      // Set the creator to an actual user
      prediction.createdBy = creatorId;
      
      const existingPrediction = await businessPredictionsCollection.findOne({ 
        title: prediction.title,
        'timeframe.start': prediction.timeframe.start
      });

      if (existingPrediction) {
        logger.info(`Updating business prediction: ${prediction.title}`);
        await businessPredictionsCollection.updateOne(
          { _id: existingPrediction._id },
          { $set: { ...prediction, _id: existingPrediction._id } }
        );
      } else {
        logger.info(`Inserting new business prediction: ${prediction.title}`);
        await businessPredictionsCollection.insertOne(prediction);
      }
    }

    // Insert or update business insights
    for (const insight of businessInsights) {
      // Set the creator to an actual user
      insight.createdBy = creatorId;
      
      const existingInsight = await businessInsightsCollection.findOne({ 
        title: insight.title
      });

      if (existingInsight) {
        logger.info(`Updating business insight: ${insight.title}`);
        await businessInsightsCollection.updateOne(
          { _id: existingInsight._id },
          { $set: { ...insight, _id: existingInsight._id } }
        );
      } else {
        logger.info(`Inserting new business insight: ${insight.title}`);
        await businessInsightsCollection.insertOne(insight);
      }
    }

    // Insert or update business strategies
    for (const strategy of businessStrategies) {
      // Set the creator to an actual user
      strategy.createdBy = creatorId;
      
      const existingStrategy = await businessStrategiesCollection.findOne({ 
        name: strategy.name
      });

      if (existingStrategy) {
        logger.info(`Updating business strategy: ${strategy.name}`);
        await businessStrategiesCollection.updateOne(
          { _id: existingStrategy._id },
          { $set: { ...strategy, _id: existingStrategy._id } }
        );
      } else {
        logger.info(`Inserting new business strategy: ${strategy.name}`);
        await businessStrategiesCollection.insertOne(strategy);
      }
    }

    logger.info('Business analysis data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting business analysis data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectBusinessData()
  .then(() => {
    logger.info('Business analysis data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in business analysis data injection script:', error);
  });

module.exports = { businessKpis, businessPredictions, businessInsights, businessStrategies };
