/**
 * Realistic Data Service
 * 
 * This service provides realistic South African livestock data
 * with proper MongoDB integration and fallback to enhanced mock data
 */

import api from './apiService';

// South African livestock breeds and names
const SOUTH_AFRICAN_BREEDS = {
  cattle: ['Nguni', 'Afrikaner', 'Bonsmara', 'Brahman', 'Simmentaler', 'Charolais', 'Limousin', 'Hereford'],
  sheep: ['<PERSON><PERSON><PERSON>', 'Merino', 'Damara', 'Blackhead Persian', 'Van Rooy', 'Dohne Merino'],
  goats: ['Boer', 'Angora', 'Savanna', 'Kalahari Red', 'Indigenous']
};

const SOUTH_AFRICAN_NAMES = {
  male: ['Thabo', 'Sipho', 'Mandla', 'Bongani', 'Tshepo', 'Kagiso', 'Lerato', 'Mpho', 'Tebogo', 'Kgosi'],
  female: ['Nomsa', 'Thandi', '<PERSON>lesa', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Mmabatho', 'Dineo']
};

const LOCATIONS = [
  'North Paddock', 'South Paddock', 'East Paddock', 'West Paddock', 'Main Kraal',
  'Breeding Camp', 'Quarantine Area', 'Grazing Field A', 'Grazing Field B', 'River Camp'
];

// Generate realistic animal data
export const generateRealisticAnimals = (count: number = 100) => {
  const animals = [];
  const currentYear = new Date().getFullYear();
  
  for (let i = 1; i <= count; i++) {
    const species = ['cattle', 'sheep', 'goats'][Math.floor(Math.random() * 3)];
    const gender = Math.random() > 0.5 ? 'male' : 'female';
    const breed = SOUTH_AFRICAN_BREEDS[species as keyof typeof SOUTH_AFRICAN_BREEDS][
      Math.floor(Math.random() * SOUTH_AFRICAN_BREEDS[species as keyof typeof SOUTH_AFRICAN_BREEDS].length)
    ];
    const name = SOUTH_AFRICAN_NAMES[gender as keyof typeof SOUTH_AFRICAN_NAMES][
      Math.floor(Math.random() * SOUTH_AFRICAN_NAMES[gender as keyof typeof SOUTH_AFRICAN_NAMES].length)
    ];
    
    // Generate realistic weights based on species
    let weight;
    let purchasePrice;
    switch (species) {
      case 'cattle':
        weight = Math.floor(Math.random() * 300) + 200; // 200-500kg
        purchasePrice = Math.floor(Math.random() * 20000) + 10000; // R10,000-30,000
        break;
      case 'sheep':
        weight = Math.floor(Math.random() * 40) + 30; // 30-70kg
        purchasePrice = Math.floor(Math.random() * 3000) + 2000; // R2,000-5,000
        break;
      case 'goats':
        weight = Math.floor(Math.random() * 30) + 20; // 20-50kg
        purchasePrice = Math.floor(Math.random() * 2500) + 1500; // R1,500-4,000
        break;
      default:
        weight = 100;
        purchasePrice = 5000;
    }
    
    const birthYear = currentYear - Math.floor(Math.random() * 8) - 1; // 1-8 years old
    const birthDate = new Date(birthYear, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
    
    animals.push({
      id: i,
      tagNumber: `${species.toUpperCase().substring(0, 3)}-${currentYear}-${i.toString().padStart(3, '0')}`,
      name,
      species: species.charAt(0).toUpperCase() + species.slice(1),
      breed,
      gender,
      birthDate: birthDate.toISOString().split('T')[0],
      weight,
      status: Math.random() > 0.1 ? 'Active' : ['Sold', 'Breeding', 'Quarantined'][Math.floor(Math.random() * 3)],
      healthStatus: Math.random() > 0.15 ? 'Healthy' : ['Sick', 'Injured', 'Recovering'][Math.floor(Math.random() * 3)],
      location: LOCATIONS[Math.floor(Math.random() * LOCATIONS.length)],
      purchaseDate: new Date(birthYear, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
      purchasePrice,
      estimatedValue: Math.floor(purchasePrice * (1.1 + Math.random() * 0.5)), // 10-60% appreciation
      rfidTag: `RF${i.toString().padStart(8, '0')}`,
      notes: `${gender.charAt(0).toUpperCase() + gender.slice(1)} ${breed} ${species} in good condition`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  
  return animals;
};

// Generate realistic health records
export const generateRealisticHealthRecords = (animalCount: number = 100) => {
  const healthRecords = [];
  const treatments = ['Vaccination', 'Deworming', 'Antibiotic Treatment', 'Vitamin Injection', 'Hoof Trimming'];
  const vaccines = ['FMD', 'Anthrax', 'Blackleg', 'Botulism', 'Rabies'];
  
  for (let i = 1; i <= animalCount * 3; i++) { // 3 records per animal on average
    const animalId = Math.floor(Math.random() * animalCount) + 1;
    const treatmentType = treatments[Math.floor(Math.random() * treatments.length)];
    const recordDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000); // Within last year
    
    healthRecords.push({
      id: i,
      animalId,
      animalTag: `CTL-2024-${animalId.toString().padStart(3, '0')}`,
      date: recordDate.toISOString().split('T')[0],
      type: treatmentType,
      description: treatmentType === 'Vaccination' ? 
        `${vaccines[Math.floor(Math.random() * vaccines.length)]} vaccination` : 
        `Routine ${treatmentType.toLowerCase()}`,
      veterinarian: ['Dr. Smith', 'Dr. Patel', 'Dr. Van Der Merwe', 'Dr. Mthembu'][Math.floor(Math.random() * 4)],
      cost: Math.floor(Math.random() * 500) + 50, // R50-550
      notes: `Treatment completed successfully. Animal responded well.`,
      nextDueDate: treatmentType === 'Vaccination' ? 
        new Date(recordDate.getTime() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
      createdAt: recordDate.toISOString(),
      updatedAt: recordDate.toISOString()
    });
  }
  
  return healthRecords;
};

// Generate realistic breeding records
export const generateRealisticBreedingRecords = (animalCount: number = 100) => {
  const breedingRecords = [];
  
  for (let i = 1; i <= Math.floor(animalCount * 0.3); i++) { // 30% breeding rate
    const femaleId = Math.floor(Math.random() * animalCount) + 1;
    const maleId = Math.floor(Math.random() * animalCount) + 1;
    const breedingDate = new Date(Date.now() - Math.random() * 300 * 24 * 60 * 60 * 1000); // Within last 300 days
    const expectedCalvingDate = new Date(breedingDate.getTime() + 280 * 24 * 60 * 60 * 1000); // 280 days gestation
    
    breedingRecords.push({
      id: i,
      femaleId,
      maleId,
      femaleTag: `CTL-2024-${femaleId.toString().padStart(3, '0')}`,
      maleTag: `CTL-2024-${maleId.toString().padStart(3, '0')}`,
      breedingDate: breedingDate.toISOString().split('T')[0],
      method: Math.random() > 0.3 ? 'Natural' : 'Artificial Insemination',
      expectedCalvingDate: expectedCalvingDate.toISOString().split('T')[0],
      status: Date.now() > expectedCalvingDate.getTime() ? 
        (Math.random() > 0.1 ? 'Calved' : 'Pregnant') : 'Pregnant',
      notes: 'Breeding program - selected for genetic improvement',
      createdAt: breedingDate.toISOString(),
      updatedAt: breedingDate.toISOString()
    });
  }
  
  return breedingRecords;
};

// Generate realistic financial data
export const generateRealisticFinancialData = () => {
  const transactions = [];
  const categories = ['Feed Purchase', 'Veterinary', 'Equipment', 'Labor', 'Utilities', 'Animal Sales', 'Milk Sales'];
  
  for (let i = 1; i <= 200; i++) {
    const category = categories[Math.floor(Math.random() * categories.length)];
    const isIncome = category.includes('Sales');
    const amount = isIncome ? 
      Math.floor(Math.random() * 50000) + 5000 : // Income: R5,000-55,000
      -(Math.floor(Math.random() * 10000) + 500); // Expense: R500-10,500
    
    const transactionDate = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
    
    transactions.push({
      id: i,
      date: transactionDate.toISOString().split('T')[0],
      description: `${category} - ${transactionDate.toLocaleDateString()}`,
      category,
      amount,
      type: isIncome ? 'Income' : 'Expense',
      reference: `TXN${i.toString().padStart(6, '0')}`,
      createdAt: transactionDate.toISOString(),
      updatedAt: transactionDate.toISOString()
    });
  }
  
  return transactions;
};

// Main service class
export class RealisticDataService {
  private static instance: RealisticDataService;
  private cache: Map<string, any> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  
  static getInstance(): RealisticDataService {
    if (!RealisticDataService.instance) {
      RealisticDataService.instance = new RealisticDataService();
    }
    return RealisticDataService.instance;
  }
  
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key);
    return expiry ? Date.now() < expiry : false;
  }
  
  private setCache(key: string, data: any, ttlMinutes: number = 30): void {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + ttlMinutes * 60 * 1000);
  }
  
  async getAnimals(): Promise<any[]> {
    const cacheKey = 'animals';
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      // Try to get from MongoDB first
      const response = await api.get('/api/animals');
      if (response.data && response.data.length > 0) {
        this.setCache(cacheKey, response.data);
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch from MongoDB, using realistic mock data:', error);
    }
    
    // Fallback to realistic mock data
    const mockData = generateRealisticAnimals(150);
    this.setCache(cacheKey, mockData);
    return mockData;
  }
  
  async getHealthRecords(): Promise<any[]> {
    const cacheKey = 'healthRecords';
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const response = await api.get('/api/health-records');
      if (response.data && response.data.length > 0) {
        this.setCache(cacheKey, response.data);
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch health records from MongoDB, using realistic mock data:', error);
    }
    
    const mockData = generateRealisticHealthRecords(150);
    this.setCache(cacheKey, mockData);
    return mockData;
  }
  
  async getBreedingRecords(): Promise<any[]> {
    const cacheKey = 'breedingRecords';
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const response = await api.get('/api/breeding-records');
      if (response.data && response.data.length > 0) {
        this.setCache(cacheKey, response.data);
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch breeding records from MongoDB, using realistic mock data:', error);
    }
    
    const mockData = generateRealisticBreedingRecords(150);
    this.setCache(cacheKey, mockData);
    return mockData;
  }
  
  async getFinancialData(): Promise<any[]> {
    const cacheKey = 'financialData';
    
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    try {
      const response = await api.get('/api/financial-records');
      if (response.data && response.data.length > 0) {
        this.setCache(cacheKey, response.data);
        return response.data;
      }
    } catch (error) {
      console.warn('Failed to fetch financial data from MongoDB, using realistic mock data:', error);
    }
    
    const mockData = generateRealisticFinancialData();
    this.setCache(cacheKey, mockData);
    return mockData;
  }
  
  clearCache(): void {
    this.cache.clear();
    this.cacheExpiry.clear();
  }
}

export const realisticDataService = RealisticDataService.getInstance();
