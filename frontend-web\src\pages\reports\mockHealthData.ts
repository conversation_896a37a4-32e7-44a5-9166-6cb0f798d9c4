// Health Record Type
export interface HealthRecord {
  id: string;
  animalId: string;
  animalName: string;
  animalType: string;
  date: string;
  type: 'checkup' | 'treatment' | 'vaccination' | 'surgery' | 'quarantine' | 'dipping';
  diagnosis: string;
  treatment: string;
  medication: string;
  dosage: string;
  performedBy: string;
  notes: string;
  followUpDate?: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  cost: number;
}

// Vaccination Record Type
export interface VaccinationRecord {
  id: string;
  animalId: string;
  animalName: string;
  animalType: string;
  date: string;
  vaccineName: string;
  disease: string;
  dosage: string;
  administeredBy: string;
  batchNumber: string;
  expiryDate: string;
  boosterRequired: boolean;
  boosterDate?: string;
  notes: string;
  status: 'completed' | 'scheduled' | 'cancelled';
  cost: number;
}

// Mock Health Records
export const mockHealthRecords: HealthRecord[] = [
  {
    id: 'HR001',
    animalId: '1',
    animalName: 'Tshepiso',
    animalType: 'Cattle',
    date: '2024-04-01',
    type: 'checkup',
    diagnosis: 'Healthy',
    treatment: 'None required',
    medication: '',
    dosage: '',
    performedBy: 'Dr. <PERSON>',
    notes: 'Regular health checkup, animal in good condition',
    status: 'completed',
    cost: 750
  },
  {
    id: 'HR002',
    animalId: '2',
    animalName: 'Tumelo',
    animalType: 'Cattle',
    date: '2024-04-03',
    type: 'treatment',
    diagnosis: 'Tick-borne disease',
    treatment: 'Antibiotics',
    medication: 'Oxytetracycline',
    dosage: '20mg/kg',
    performedBy: 'Dr. Botha',
    notes: 'Moderate infestation, follow-up required',
    followUpDate: '2024-04-10',
    status: 'completed',
    cost: 1250
  },
  {
    id: 'HR003',
    animalId: '5',
    animalName: 'Kgosi',
    animalType: 'Cattle',
    date: '2024-04-05',
    type: 'dipping',
    diagnosis: 'Preventative',
    treatment: 'Dipping',
    medication: 'Amitraz',
    dosage: 'Standard',
    performedBy: 'Farm Staff',
    notes: 'Regular dipping schedule',
    status: 'completed',
    cost: 350
  },
  {
    id: 'HR004',
    animalId: '8',
    animalName: 'Naledi',
    animalType: 'Sheep',
    date: '2024-04-07',
    type: 'treatment',
    diagnosis: 'Foot rot',
    treatment: 'Hoof trimming and antibiotics',
    medication: 'Penicillin',
    dosage: '10mg/kg',
    performedBy: 'Dr. Viljoen',
    notes: 'Moderate case, isolated from flock',
    followUpDate: '2024-04-14',
    status: 'completed',
    cost: 850
  },
  {
    id: 'HR005',
    animalId: '12',
    animalName: 'Tau',
    animalType: 'Goat',
    date: '2024-04-10',
    type: 'checkup',
    diagnosis: 'Healthy',
    treatment: 'None required',
    medication: '',
    dosage: '',
    performedBy: 'Dr. Mkhize',
    notes: 'Regular health checkup, animal in good condition',
    status: 'completed',
    cost: 750
  },
  {
    id: 'HR006',
    animalId: '3',
    animalName: 'Lesedi',
    animalType: 'Cattle',
    date: '2024-04-15',
    type: 'surgery',
    diagnosis: 'Foreign body obstruction',
    treatment: 'Surgical removal',
    medication: 'Flunixin',
    dosage: '2.2mg/kg',
    performedBy: 'Dr. van Niekerk',
    notes: 'Successful procedure, monitoring recovery',
    followUpDate: '2024-04-22',
    status: 'completed',
    cost: 3500
  },
  {
    id: 'HR007',
    animalId: '15',
    animalName: 'Thabo',
    animalType: 'Sheep',
    date: '2024-04-20',
    type: 'quarantine',
    diagnosis: 'Suspected Blue tongue',
    treatment: 'Isolation and supportive care',
    medication: 'Anti-inflammatory',
    dosage: 'As directed',
    performedBy: 'Dr. Naidoo',
    notes: 'Isolated in quarantine pen, monitoring closely',
    followUpDate: '2024-04-27',
    status: 'scheduled',
    cost: 1200
  },
  {
    id: 'HR008',
    animalId: '7',
    animalName: 'Pule',
    animalType: 'Cattle',
    date: '2024-04-25',
    type: 'treatment',
    diagnosis: 'Parasites',
    treatment: 'Deworming',
    medication: 'Ivermectin',
    dosage: '0.2mg/kg',
    performedBy: 'Farm Staff',
    notes: 'Routine deworming treatment',
    status: 'scheduled',
    cost: 650
  }
];

// Mock Vaccination Records
export const mockVaccinations: VaccinationRecord[] = [
  {
    id: 'VAC001',
    animalId: '1',
    animalName: 'Tshepiso',
    animalType: 'Cattle',
    date: '2024-03-15',
    vaccineName: 'Bovishield Gold FP5',
    disease: 'BVD, IBR, PI3, BRSV',
    dosage: '2ml',
    administeredBy: 'Dr. van Niekerk',
    batchNumber: 'BV2024-123',
    expiryDate: '2025-03-15',
    boosterRequired: true,
    boosterDate: '2024-09-15',
    notes: 'Annual vaccination',
    status: 'completed',
    cost: 450
  },
  {
    id: 'VAC002',
    animalId: '2',
    animalName: 'Tumelo',
    animalType: 'Cattle',
    date: '2024-03-15',
    vaccineName: 'Bovishield Gold FP5',
    disease: 'BVD, IBR, PI3, BRSV',
    dosage: '2ml',
    administeredBy: 'Dr. van Niekerk',
    batchNumber: 'BV2024-123',
    expiryDate: '2025-03-15',
    boosterRequired: true,
    boosterDate: '2024-09-15',
    notes: 'Annual vaccination',
    status: 'completed',
    cost: 450
  },
  {
    id: 'VAC003',
    animalId: '8',
    animalName: 'Naledi',
    animalType: 'Sheep',
    date: '2024-03-20',
    vaccineName: 'Ovivac P',
    disease: 'Pulpy kidney, tetanus, black quarter',
    dosage: '1ml',
    administeredBy: 'Dr. Botha',
    batchNumber: 'OV2024-456',
    expiryDate: '2025-03-20',
    boosterRequired: true,
    boosterDate: '2024-06-20',
    notes: 'First vaccination for young sheep',
    status: 'completed',
    cost: 350
  },
  {
    id: 'VAC004',
    animalId: '12',
    animalName: 'Tau',
    animalType: 'Goat',
    date: '2024-03-25',
    vaccineName: 'Coglavax',
    disease: 'Clostridial diseases',
    dosage: '1ml',
    administeredBy: 'Dr. Mkhize',
    batchNumber: 'CG2024-789',
    expiryDate: '2025-03-25',
    boosterRequired: true,
    boosterDate: '2024-06-25',
    notes: 'Annual vaccination',
    status: 'completed',
    cost: 350
  },
  {
    id: 'VAC005',
    animalId: '3',
    animalName: 'Lesedi',
    animalType: 'Cattle',
    date: '2024-04-05',
    vaccineName: 'Botuthrax',
    disease: 'Anthrax, botulism',
    dosage: '2ml',
    administeredBy: 'Dr. Viljoen',
    batchNumber: 'BT2024-321',
    expiryDate: '2025-04-05',
    boosterRequired: false,
    notes: 'Annual vaccination',
    status: 'completed',
    cost: 500
  },
  {
    id: 'VAC006',
    animalId: '15',
    animalName: 'Thabo',
    animalType: 'Sheep',
    date: '2024-04-10',
    vaccineName: 'Bluetongue Vaccine',
    disease: 'Bluetongue',
    dosage: '1ml',
    administeredBy: 'Dr. Naidoo',
    batchNumber: 'BT2024-654',
    expiryDate: '2025-04-10',
    boosterRequired: false,
    notes: 'Annual vaccination',
    status: 'completed',
    cost: 400
  },
  {
    id: 'VAC007',
    animalId: '5',
    animalName: 'Kgosi',
    animalType: 'Cattle',
    date: '2024-05-01',
    vaccineName: 'Lumpy Skin Disease Vaccine',
    disease: 'Lumpy skin disease',
    dosage: '2ml',
    administeredBy: 'Dr. van Niekerk',
    batchNumber: 'LS2024-987',
    expiryDate: '2025-05-01',
    boosterRequired: false,
    notes: 'Annual vaccination',
    status: 'scheduled',
    cost: 550
  },
  {
    id: 'VAC008',
    animalId: '7',
    animalName: 'Pule',
    animalType: 'Cattle',
    date: '2024-05-05',
    vaccineName: 'Bovishield Gold FP5',
    disease: 'BVD, IBR, PI3, BRSV',
    dosage: '2ml',
    administeredBy: 'Dr. Botha',
    batchNumber: 'BV2024-123',
    expiryDate: '2025-03-15',
    boosterRequired: true,
    boosterDate: '2024-11-05',
    notes: 'Annual vaccination',
    status: 'scheduled',
    cost: 450
  }
];

// Health Statistics
export const mockHealthStats = {
  totalRecords: mockHealthRecords.length,
  totalVaccinations: mockVaccinations.length,
  byType: {
    checkup: mockHealthRecords.filter(r => r.type === 'checkup').length,
    treatment: mockHealthRecords.filter(r => r.type === 'treatment').length,
    vaccination: mockVaccinations.length,
    surgery: mockHealthRecords.filter(r => r.type === 'surgery').length,
    quarantine: mockHealthRecords.filter(r => r.type === 'quarantine').length,
    dipping: mockHealthRecords.filter(r => r.type === 'dipping').length
  },
  byStatus: {
    completed: mockHealthRecords.filter(r => r.status === 'completed').length,
    scheduled: mockHealthRecords.filter(r => r.status === 'scheduled').length,
    cancelled: mockHealthRecords.filter(r => r.status === 'cancelled').length
  },
  byAnimalType: {
    Cattle: mockHealthRecords.filter(r => r.animalType === 'Cattle').length,
    Sheep: mockHealthRecords.filter(r => r.animalType === 'Sheep').length,
    Goat: mockHealthRecords.filter(r => r.animalType === 'Goat').length
  },
  totalCost: mockHealthRecords.reduce((sum, r) => sum + r.cost, 0) + 
    mockVaccinations.reduce((sum, v) => sum + v.cost, 0),
  averageCost: (mockHealthRecords.reduce((sum, r) => sum + r.cost, 0) + 
    mockVaccinations.reduce((sum, v) => sum + v.cost, 0)) / 
    (mockHealthRecords.length + mockVaccinations.length),
  pendingFollowUps: mockHealthRecords.filter(r => r.followUpDate).length,
  pendingVaccinations: mockVaccinations.filter(v => v.status === 'scheduled').length
};
