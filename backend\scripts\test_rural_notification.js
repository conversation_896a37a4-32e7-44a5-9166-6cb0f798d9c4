/**
 * Test Rural Notification System
 * 
 * This script tests the rural notification system for birth alerts
 */

const ruralNotificationService = require('../services/ruralNotificationService');

/**
 * Test the notification system
 * @param {string} phoneNumber - Phone number to send notifications to
 */
async function testNotification(phoneNumber) {
  console.log('=== AMPD Livestock Rural Notification System Test ===');
  console.log(`Testing with phone number: ${phoneNumber}`);
  console.log(`Current time: ${new Date().toLocaleString()}`);
  console.log('');
  
  // Test SMS
  console.log('=== Testing SMS to', phoneNumber, '===');
  const smsMessage = `AMPD Livestock SMS Test: This is a test message sent at ${new Date().toLocaleTimeString()}`;
  const smsResult = await ruralNotificationService.sendSMS(phoneNumber, smsMessage, 'medium');
  console.log('SMS Result:', smsResult);
  console.log('');
  
  // Test Voice Call
  console.log('=== Testing Voice Call to', phoneNumber, '===');
  const voiceMessage = `This is a test voice call from AMPD Livestock. The current time is ${new Date().toLocaleTimeString()}. This is only a test.`;
  const voiceResult = await ruralNotificationService.makeVoiceCall(phoneNumber, voiceMessage, 'high');
  console.log('Voice Call Result:', voiceResult);
  console.log('');
  
  // Test WhatsApp
  console.log('=== Testing WhatsApp to', phoneNumber, '===');
  const whatsappMessage = `AMPD Livestock WhatsApp Test: This is a test message sent at ${new Date().toLocaleTimeString()}`;
  const whatsappResult = await ruralNotificationService.sendWhatsApp(phoneNumber, whatsappMessage, 'medium');
  console.log('WhatsApp Result:', whatsappResult);
  console.log('');
  
  // Test Birth Alert
  console.log('=== Testing Birth Alert to', phoneNumber, '===');
  const birthAlertResults = await ruralNotificationService.sendBirthPredictionAlert({
    animalId: 'CTL001',
    animalName: 'Bella',
    daysRemaining: 1,
    expectedDueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
    riskLevel: 'high',
    specialCare: [
      'Isolate in calving pen',
      'Ensure clean water supply',
      'Monitor every 2 hours',
      'Prepare colostrum supplements'
    ],
    recipient: {
      phone: phoneNumber
    },
    notificationTypes: ['sms', 'voice', 'whatsapp']
  });
  console.log('Birth Alert Results:', birthAlertResults);
  console.log('');
  
  console.log('=== All tests completed ===');
}

// Get phone number from command line arguments
const phoneNumber = process.argv[2] || '0794484159';

// Run the test
testNotification(phoneNumber).catch(console.error);
