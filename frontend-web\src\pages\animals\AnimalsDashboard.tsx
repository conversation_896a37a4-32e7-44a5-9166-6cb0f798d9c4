import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Grid, Card, CardContent, Typography, Box, IconButton, LinearProgress, useTheme, CircularProgress, Alert, Snackbar, alpha, Divider,  } from '@mui/material';
import {
  Add,
  Pets,
  TrendingUp,
  LocalHospital,
  LocationOn,
  MoreVert,
  Assessment,
  CalendarToday,
  MonetizationOn,
  Refresh,
 } from '../../utils/iconImports';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, AreaChart, Area, CartesianGrid, Legend } from 'recharts';
import { EnhancedPieLabelRenderProps } from '../../types/recharts';
// import AnimalList from '../../components/animals/AnimalList';
import { useAnimalData } from '../../hooks/useAnimalData';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModuleContainer,
  ModernDashboard,
  ModernChart,
  ModernDataTable,
  ModernCard,
  withSubModuleTranslation,
  AnimatedBackgroundCard,
  CustomButton,
  StandardDashboard,
  ModuleContentCard
} from '../../components/common';
import AnimalMarketplaceGrid from '../../components/modules/animals/AnimalMarketplaceGrid';
// Import MongoDB data
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { tryCatch, getErrorMessage } from '../../utils/errorHandling';
// Import card styling utilities
import { getCardStyle } from '../../utils/cardStyles';
import AddRecordButton from '../../components/common/AddRecordButton';
import { animalFields } from '../../config/formFields';

interface AnimalsDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const AnimalsDashboard: React.FC<AnimalsDashboardProps> = ({
  translate: propTranslate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  // Use MongoDB if connected, otherwise use mock data
  const { isConnected, mongoStats: dbStats, refreshStats, isLoading: isDbLoading } = useMongoDb();
  const { stats: mockStats, animals: mockAnimals } = useAnimalData();
  const { stats: mongoStats, animals: mongoAnimals } = useMongoAnimalData();
  const { translate: contextTranslate } = useLanguage();

  // Use the translate prop if provided, otherwise use the context translate
  const translate = propTranslate || contextTranslate;

  // Loading state
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Use MongoDB data if connected, otherwise use mock data
  const stats = isConnected && dbStats ? dbStats :
                isConnected && mongoStats ? mongoStats :
                mockStats || {
    totalAnimals: 0,
    healthPercentage: 0,
    activeAnimals: 0,
    inactiveAnimals: 0,
    healthyAnimals: 0,
    sickAnimals: 0,
    injuredAnimals: 0,
    pregnantAnimals: 0,
    averageGrowthRate: 0,
    bySpecies: {},
    byStatus: {},
    byLocation: {},
    byBreed: {},
    byHealth: {
      healthy: 0,
      sick: 0,
      injured: 0,
      pregnant: 0
    },
    // Asset Management stats
    retiredAnimals: 5,
    nearingRetirement: 3,
    retirementByReason: {
      age: 2,
      breeding: 2,
      health: 1,
      other: 0
    },
    valueOfActiveAssets: 850000,
    valueOfRetiredAssets: 150000,
    totalAssetValue: 1000000,
    averageRoi: 15
  };
  const animals = isConnected && mongoAnimals ? mongoAnimals : mockAnimals || [];

  // State for chart interactions
  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // Prepare data for species chart
  const speciesData = Object.entries(stats.bySpecies || {
    'Cattle': 25,
    'Sheep': 18,
    'Goats': 12,
    'Pigs': 8
  }).map(([name, value]) => ({
    name,
    value,
  }));

  // Prepare data for health status chart
  const healthData = Object.entries(stats.byHealth || {
    healthy: 45,
    sick: 8,
    injured: 5,
    pregnant: 12
  }).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  }));

  // Prepare data for location chart
  const locationData = Object.entries(stats.byLocation || {
    'Pasture A': 15,
    'Pasture B': 12,
    'Barn': 8,
    'Quarantine': 3
  }).map(([name, value]) => ({
    name,
    value,
  }));

  // Prepare data for growth chart (mock data for demonstration)
  const growthData = [
    { month: 'Jan', weight: 120, height: 60 },
    { month: 'Feb', weight: 132, height: 63 },
    { month: 'Mar', weight: 145, height: 65 },
    { month: 'Apr', weight: 160, height: 68 },
    { month: 'May', weight: 178, height: 70 },
    { month: 'Jun', weight: 190, height: 72 },
  ];

  // Alert state for notifications
  const [alert, setAlert] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Refresh data
  const handleRefreshData = async () => {
    setIsLoading(true);

    await tryCatch(
      async () => {
        await refreshStats();
        setAlert({
          open: true,
          message: translate('common.success'),
          severity: 'success'
        });
      },
      (error) => {
        setAlert({
          open: true,
          message: getErrorMessage(error),
          severity: 'error'
        });
      }
    );

    setIsLoading(false);
  };

  // No need for separate loading indicator since we're using LoadingOverlay

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translate('animals.total'),
      value: stats.totalAnimals || 63,
      icon: <Pets />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('animals.healthy'),
      value: `${stats.healthPercentage || 85}%`,
      icon: <LocalHospital />,
      color: theme.palette.success.main,
      trend: {
        value: 2,
        isPositive: true,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('dashboard.growth_rate'),
      value: `${stats.averageGrowthRate || 12}%`,
      icon: <TrendingUp />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: true,
        label: translate('dashboard.since_last_month')
      }
    },
    {
      label: translate('animals.active'),
      value: stats.activeAnimals || 58,
      icon: <LocationOn />,
      color: theme.palette.info.main,
      trend: {
        value: 1,
        isPositive: false,
        label: translate('dashboard.since_last_month')
      }
    }
  ];

  // Refresh data function for add record success
  const handleAddSuccess = () => {
    handleRefreshData();
    setAlert({
      open: true,
      message: 'Animal added successfully!',
      severity: 'success'
    });
  };

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translate('animals.add'),
      icon: <Add />,
      onClick: () => navigate('/animals/new'),
      color: 'primary'
    }
  ];

  // Prepare dashboard tabs
  const dashboardTabs = [
    {
      label: translate('dashboard.overview'),
      icon: <Assessment />,
      content: (
        <Box>
          {/* Add Record Button */}
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
            <AddRecordButton
              module="animals"
              title="Animal"
              fields={animalFields}
              onSuccess={handleAddSuccess}
              variant="contained"
              size="large"
            />
          </Box>

          {/* Charts Section */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={4}>
              <ModernChart
                title={translate('animals.species_distribution')}
                subtitle={translate('animals.species_distribution_desc')}
                data={speciesData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="animals"
                tooltip={translate('animals.species_distribution_help')}
                formatValue={(value) => `${value} ${translate('animals.animals')}`}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <ModernChart
                title={translate('animals.health_distribution')}
                subtitle={translate('animals.health_distribution_desc')}
                data={healthData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.success.main}
                allowChartTypeChange={true}
                module="health"
                tooltip={translate('animals.health_distribution_help')}
                formatValue={(value) => `${value} ${translate('animals.animals')}`}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <ModernChart
                title={translate('animals.location_distribution')}
                subtitle={translate('animals.location_distribution_desc')}
                data={locationData}
                type="bar"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.info.main}
                allowChartTypeChange={true}
                module="animals"
                tooltip={translate('animals.location_distribution_help')}
                formatValue={(value) => `${value} ${translate('animals.animals')}`}
              />
            </Grid>
          </Grid>

          {/* Growth Chart */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <ModernChart
                title={translate('animals.growth_chart')}
                subtitle={translate('animals.growth_chart_desc')}
                data={growthData}
                type="line"
                dataKeys={['weight', 'height']}
                xAxisDataKey="month"
                height={350}
                accentColor={theme.palette.warning.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="animals"
                tooltip={translate('animals.growth_chart_help')}
                formatValue={(value) => `${value} ${value > 100 ? 'kg' : 'cm'}`}
              />
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('animals.asset_management'),
      icon: <MonetizationOn />,
      content: (
        <Box>
          {/* Asset Management Stats */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={6}>
              <ModuleContentCard
                title={translate('animals.asset_overview')}
                subtitle={translate('animals.asset_overview_desc')}
                icon={<MonetizationOn />}
                module="animals"
                height="100%"
                delay={0.1}
              >
                <Box sx={{ p: 2 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">{translate('animals.total_asset_value')}</Typography>
                        <Typography variant="h5" fontWeight="bold">R {stats.totalAssetValue?.toLocaleString() || '1,000,000'}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">{translate('animals.average_roi')}</Typography>
                        <Typography variant="h5" fontWeight="bold">{stats.averageRoi || 15}%</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">{translate('animals.active_assets')}</Typography>
                        <Typography variant="h5" fontWeight="bold">R {stats.valueOfActiveAssets?.toLocaleString() || '850,000'}</Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">{translate('animals.retired_assets')}</Typography>
                        <Typography variant="h5" fontWeight="bold">R {stats.valueOfRetiredAssets?.toLocaleString() || '150,000'}</Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>{translate('animals.asset_status')}</Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.primary.main, mr: 1 }} />
                          <Typography variant="body2">{translate('animals.active')}: {stats.activeAnimals || 58}</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.error.main, mr: 1 }} />
                          <Typography variant="body2">{translate('animals.retired')}: {stats.retiredAnimals || 5}</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.warning.main, mr: 1 }} />
                          <Typography variant="body2">{translate('animals.near_retirement')}: {stats.nearingRetirement || 3}</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Box>
              </ModuleContentCard>
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translate('animals.retirement_reasons')}
                subtitle={translate('animals.retirement_reasons_desc')}
                data={[
                  { name: 'Age', value: stats.retirementByReason?.age || 2 },
                  { name: 'Breeding', value: stats.retirementByReason?.breeding || 2 },
                  { name: 'Health', value: stats.retirementByReason?.health || 1 },
                  { name: 'Other', value: stats.retirementByReason?.other || 0 },
                ]}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.error.main}
                module="animals"
                tooltip={translate('animals.retirement_reasons_help')}
                formatValue={(value) => `${value} ${translate('animals.animals')}`}
              />
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('animals.registry'),
      icon: <Pets />,
      content: (
        <Box>
          {/* Animal Registry */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <ModuleContentCard
                title={translate('animals.registry')}
                subtitle={translate('animals.registry_desc')}
                icon={<Pets />}
                module="animals"
                height="100%"
                delay={0.1}
                actionLabel={translate('animals.view_all')}
                onAction={() => navigate('/animals/list')}
              >
                <Box sx={{ p: 2 }}>
                  <AnimalMarketplaceGrid
                    animals={animals}
                    loading={isLoading}
                    title={translate('animals.featured')}
                    maxItems={8}
                  />
                </Box>
              </ModuleContentCard>
            </Grid>
          </Grid>
        </Box>
      )
    },
    {
      label: translate('animals.growth_trends'),
      icon: <TrendingUp />,
      content: (
        <Box>
          {/* Growth Chart */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <AnimatedBackgroundCard
                title={translateModuleField ? translateModuleField('animal_growth_trends', "Animal Growth Trends") : "Animal Growth Trends"}
                module="animals"
                backgroundImage="/images/modules/animals/cattle-1.jpeg"
                icon={<TrendingUp />}
                accentColor={theme.palette.warning.main}
                secondaryColor={theme.palette.warning.dark}
              >
                <Box sx={{ height: { xs: 300, sm: 350, md: 400 }, p: 2 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={growthData}
                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      onClick={(data) => {
                        if (data && data.activePayload && data.activePayload.length > 0) {
                          const payload = data.activePayload[0].payload;
                          setSelectedChart('growth');
                          setChartTooltip({
                            show: true,
                            content: `${payload.month}: Weight ${payload.weight}kg, Height ${payload.height}cm`
                          });
                        }
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area type="monotone" dataKey="weight" stroke={theme.palette.primary.main} fill={theme.palette.primary.light} fillOpacity={0.3} />
                      <Area type="monotone" dataKey="height" stroke={theme.palette.secondary.main} fill={theme.palette.secondary.light} fillOpacity={0.3} />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </Grid>
          </Grid>
        </Box>
      )
    }
  ];

  return (
    <StandardDashboard
      title={translate('animals.dashboard')}
      subtitle={translate('animals.manage')}
      icon={<Pets />}
      stats={dashboardStats.map(stat => ({
        label: stat.label,
        value: stat.value,
        trend: {
          value: stat.trend.value,
          isPositive: stat.trend.isPositive
        }
      }))}
      actions={dashboardActions}
      tabs={dashboardTabs}
      activeTab={0}
      isLoading={isLoading || isDbLoading}
      loadingMessage={translate('common.loading')}
      onRefresh={handleRefreshData}
      module="animals"
    >
        {/* Chart Interaction Feedback */}
        {chartTooltip.show && (
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12}>
              <Card sx={{
                ...getCardStyle('animals', theme),
                mb: 4,
                overflow: 'hidden'
              }}>
                <CardContent>
                  <Box display="flex" alignItems="center" mb={2}>
                    {selectedChart === 'species' ? <Pets sx={{ color: 'white', mr: 1 }} /> :
                     selectedChart === 'growth' ? <TrendingUp sx={{ color: 'white', mr: 1 }} /> :
                     <LocationOn sx={{ color: 'white', mr: 1 }} />}
                    <Typography variant="h6" fontWeight="bold" color="white">
                      {selectedChart === 'species' ?
                        (translateModuleField ? translateModuleField('species_details', 'Species Details') : 'Species Details') :
                       selectedChart === 'growth' ?
                        (translateModuleField ? translateModuleField('growth_details', 'Growth Details') : 'Growth Details') :
                        (translateModuleField ? translateModuleField('chart_details', 'Chart Details') : 'Chart Details')}
                    </Typography>
                  </Box>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="body1" color="white">
                      {chartTooltip.content}
                    </Typography>
                    <IconButton size="small" onClick={() => setChartTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>
                      <MoreVert />
                    </IconButton>
                  </Box>
                  {selectedChart === 'species' && selectedSpecies && (
                    <CustomButton
                      variant="contained"
                      color="secondary"
                      size="medium"
                      sx={{ mt: 2 }}
                      onClick={() => navigate(`/animals/list?species=${selectedSpecies}`)}
                    >
                      View {selectedSpecies} Animals
                    </CustomButton>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Alert Snackbar */}
        <Snackbar
          open={alert.open}
          autoHideDuration={6000}
          onClose={() => setAlert({ ...alert, open: false })}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert
            onClose={() => setAlert({ ...alert, open: false })}
            severity={alert.severity}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {alert.message}
          </Alert>
        </Snackbar>
    </StandardDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(AnimalsDashboard, 'animals', 'dashboard');
