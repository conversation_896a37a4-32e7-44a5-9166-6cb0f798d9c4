import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, FormControl, InputLabel, Select, MenuItem, TextField, IconButton, Divider } from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { Download, FilterList, AttachMoney, TrendingUp, Pie<PERSON>hart as PieChartIcon, Print, Share, MonetizationOn } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { formatCurrency } from '../../utils/formatters';
import { generateReport } from '../../utils/reportUtils';
import { mockFinancialTransactions, mockBudgets, mockFinancialSummary } from './mockFinancialData';
import { getCardStyle, getTableHeaderStyle, getTableRowStyle, getChipStyle } from '../../utils/cardStyles';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Monthly revenue data
const monthlyRevenueData = [
  { month: 'Jan', revenue: 150000, expenses: 90000, profit: 60000, change: '+5%' },
  { month: 'Feb', revenue: 165000, expenses: 95000, profit: 70000, change: '+8%' },
  { month: 'Mar', revenue: 180000, expenses: 100000, profit: 80000, change: '+12%' },
  { month: 'Apr', revenue: 175000, expenses: 105000, profit: 70000, change: '-2%' },
  { month: 'May', revenue: 190000, expenses: 110000, profit: 80000, change: '+10%' },
  { month: 'Jun', revenue: 210000, expenses: 115000, profit: 95000, change: '+15%' }
];

// Revenue sources data
const revenueSourcesData = [
  { name: 'Livestock Sales', value: 450000 },
  { name: 'Wool Production', value: 180000 },
  { name: 'Dairy Production', value: 150000 },
  { name: 'Other', value: 70000 }
];

// Expense categories data
const expenseCategoriesData = [
  { name: 'Feed', value: 320000 },
  { name: 'Veterinary', value: 85000 },
  { name: 'Equipment', value: 150000 },
  { name: 'Labor', value: 220000 },
  { name: 'Utilities', value: 75000 }
];

// Profit margin data
const profitMarginData = [
  { month: 'Jan', margin: 40 },
  { month: 'Feb', margin: 42 },
  { month: 'Mar', margin: 44 },
  { month: 'Apr', margin: 40 },
  { month: 'May', margin: 42 },
  { month: 'Jun', margin: 45 }
];

const COLORS = ['#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0', '#607d8b'];

const FinancialReport: React.FC = () => {
  const theme = useTheme();
  const [reportType, setReportType] = useState<string>('overview');
  const [reportFormat, setReportFormat] = useState<string>('pdf');
  const [dateRange, setDateRange] = useState<string>('month');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  const handleGenerateReport = (reportName: string) => {
    setIsGenerating(true);

    try {
      // Determine which data to use based on the report type
      let reportData: any[] = [];

      switch (reportType) {
        case 'overview':
          reportData = monthlyRevenueData;
          break;
        case 'revenue':
          reportData = revenueSourcesData;
          break;
        case 'expenses':
          reportData = expenseCategoriesData;
          break;
        case 'profitability':
          reportData = profitMarginData;
          break;
        default:
          reportData = monthlyRevenueData;
      }

      // Generate the report
      generateReport(
        reportData,
        `financial_${reportType}_report`,
        reportFormat as any,
        reportName
      );

      // Show success message or notification here if you have a notification system
      console.log(`${reportName} generated successfully`);
    } catch (error) {
      console.error('Error generating report:', error);
      // Show error message or notification here
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Box>
      {/* Module Header */}
      <Card sx={{ ...getCardStyle('financial', theme), mb: 3 }}>
        <CardContent sx={{ py: 2 }}>
          <Box display="flex" alignItems="center">
            <Box sx={{ mr: 2, width: 60, height: 60, borderRadius: '50%', overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'rgba(255,255,255,0.2)' }}>
              <AttachMoney sx={{ color: 'white', fontSize: '2.5rem' }} />
            </Box>
            <Box>
              <Typography variant="h4" fontWeight="bold" color="white">
                Financial Reports
              </Typography>
              <Typography variant="subtitle1" color="white" sx={{ opacity: 0.8 }}>
                Comprehensive financial analytics and performance metrics for your livestock operation
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      <Box sx={{ p: { xs: 2, sm: 3, md: 4 }, mt: -2 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Report Generation Controls */}
          <Card sx={{ ...getCardStyle('financial', theme), mb: 4 }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                Generate Financial Report
              </Typography>
              <Grid container spacing={3} mt={1}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth variant="outlined" size="small">
                    <InputLabel sx={{ color: 'white' }}>Report Type</InputLabel>
                    <Select
                      value={reportType}
                      onChange={(e) => setReportType(e.target.value)}
                      label="Report Type"
                      sx={{
                        color: 'white',
                        '.MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.3)',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.5)',
                        },
                        '.MuiSvgIcon-root': {
                          color: 'white',
                        }
                      }}
                    >
                      <MenuItem value="overview">Financial Overview</MenuItem>
                      <MenuItem value="revenue">Revenue Analysis</MenuItem>
                      <MenuItem value="expenses">Expense Breakdown</MenuItem>
                      <MenuItem value="profitability">Profitability Metrics</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth variant="outlined" size="small">
                    <InputLabel sx={{ color: 'white' }}>Format</InputLabel>
                    <Select
                      value={reportFormat}
                      onChange={(e) => setReportFormat(e.target.value)}
                      label="Format"
                      sx={{
                        color: 'white',
                        '.MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.3)',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.5)',
                        },
                        '.MuiSvgIcon-root': {
                          color: 'white',
                        }
                      }}
                    >
                      <MenuItem value="pdf">PDF</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="csv">CSV</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth variant="outlined" size="small">
                    <InputLabel sx={{ color: 'white' }}>Date Range</InputLabel>
                    <Select
                      value={dateRange}
                      onChange={(e) => setDateRange(e.target.value)}
                      label="Date Range"
                      sx={{
                        color: 'white',
                        '.MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.3)',
                        },
                        '&:hover .MuiOutlinedInput-notchedOutline': {
                          borderColor: 'rgba(255,255,255,0.5)',
                        },
                        '.MuiSvgIcon-root': {
                          color: 'white',
                        }
                      }}
                    >
                      <MenuItem value="month">This Month</MenuItem>
                      <MenuItem value="quarter">This Quarter</MenuItem>
                      <MenuItem value="year">This Year</MenuItem>
                      <MenuItem value="custom">Custom Range</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              <Box display="flex" justifyContent="flex-end" mt={3}>
                <CustomButton
                  variant="contained"
                  startIcon={<Download />}
                  onClick={() => handleGenerateReport('Financial Report')}
                  disabled={isGenerating}
                  sx={{
                    bgcolor: 'white',
                    color: theme.palette.primary.main,
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.8)' }
                  }}
                >
                  {isGenerating ? 'Generating...' : 'Generate Report'}
                </CustomButton>
              </Box>
            </CardContent>
          </Card>

          {/* Key Metrics */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ ...getCardStyle('financial', theme) }}>
                <CardContent>
                  <Typography variant="body2" color="white" sx={{ opacity: 0.8 }} gutterBottom>
                    Total Income
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="white">
                    {formatCurrency(mockFinancialSummary.totalIncome)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color="white"
                      fontWeight="medium"
                    >
                      +12% vs. last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ ...getCardStyle('financial', theme) }}>
                <CardContent>
                  <Typography variant="body2" color="white" sx={{ opacity: 0.8 }} gutterBottom>
                    Total Expenses
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="white">
                    {formatCurrency(mockFinancialSummary.totalExpenses)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color="white"
                      fontWeight="medium"
                    >
                      +8% vs. last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ ...getCardStyle('financial', theme) }}>
                <CardContent>
                  <Typography variant="body2" color="white" sx={{ opacity: 0.8 }} gutterBottom>
                    Net Profit
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="white">
                    {formatCurrency(mockFinancialSummary.netProfit)}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color="white"
                      fontWeight="medium"
                    >
                      +18% vs. last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ ...getCardStyle('financial', theme) }}>
                <CardContent>
                  <Typography variant="body2" color="white" sx={{ opacity: 0.8 }} gutterBottom>
                    Budget Utilization
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="white">
                    {mockFinancialSummary.budgetUtilization.toFixed(1)}%
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color="white"
                      fontWeight="medium"
                    >
                      -2% vs. last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Charts */}
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={8}>
              <Card sx={{ ...getCardStyle('financial', theme), height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                    Revenue, Expenses & Profit
                  </Typography>
                  <Box mt={2} height={300}>
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={monthlyRevenueData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                        <XAxis dataKey="month" stroke="white" />
                        <YAxis tickFormatter={(value) => `R${value / 1000}k`} stroke="white" />
                        <Tooltip
                          formatter={(value) => `R${value.toLocaleString()}`}
                          contentStyle={{ backgroundColor: 'rgba(0,0,0,0.8)', color: 'white', border: 'none' }}
                        />
                        <Legend wrapperStyle={{ color: 'white' }} />
                        <Bar dataKey="revenue" name="Revenue" fill="#4caf50" />
                        <Bar dataKey="expenses" name="Expenses" fill="#f44336" />
                        <Bar dataKey="profit" name="Profit" fill="#2196f3" />
                      </BarChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card sx={{ ...getCardStyle('financial', theme), height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                    Income by Category
                  </Typography>
                  <Box mt={2} height={300} display="flex" justifyContent="center">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={revenueSourcesData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {revenueSourcesData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                      </PieChart>
                    </ResponsiveContainer>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Recent Transactions */}
          <Card sx={{ ...getCardStyle('financial', theme), mb: 4 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="bold" color="white">
                  Recent Financial Transactions
                </Typography>
                <Box>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <Print fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    sx={{ color: 'white' }}
                    onClick={() => handleGenerateReport('Financial Transactions')}
                  >
                    <Download fontSize="small" />
                  </IconButton>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <Share fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
              <TableContainer component={Paper} sx={{ boxShadow: 'none', bgcolor: 'transparent' }}>
                <Table>
                  <TableHead sx={{ ...getTableHeaderStyle('financial', theme) }}>
                    <TableRow>
                      <TableCell sx={{ color: 'white' }}>ID</TableCell>
                      <TableCell sx={{ color: 'white' }}>Date</TableCell>
                      <TableCell sx={{ color: 'white' }}>Description</TableCell>
                      <TableCell sx={{ color: 'white' }}>Category</TableCell>
                      <TableCell sx={{ color: 'white' }} align="right">Amount</TableCell>
                      <TableCell sx={{ color: 'white' }}>Status</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockFinancialTransactions.slice(0, 5).map((transaction) => (
                      <TableRow key={transaction.id} hover sx={{ ...getTableRowStyle('financial', theme) }}>
                        <TableCell sx={{ color: 'white' }}>{transaction.id}</TableCell>
                        <TableCell sx={{ color: 'white' }}>{transaction.date}</TableCell>
                        <TableCell sx={{ color: 'white' }}>{transaction.description}</TableCell>
                        <TableCell sx={{ color: 'white' }}>{transaction.category}</TableCell>
                        <TableCell sx={{ color: 'white', fontWeight: 'bold' }} align="right">
                          {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={transaction.status}
                            size="small"
                            sx={{
                              bgcolor: transaction.status === 'completed' ? 'success.main' : 'warning.main',
                              color: 'white',
                              fontWeight: 'medium',
                              fontSize: '0.75rem'
                            }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>

          {/* Budget Overview */}
          <Card sx={{ ...getCardStyle('financial', theme) }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6" fontWeight="bold" color="white">
                  Budget Overview
                </Typography>
                <Box>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <Print fontSize="small" />
                  </IconButton>
                  <IconButton
                    size="small"
                    sx={{ color: 'white' }}
                    onClick={() => handleGenerateReport('Budget Overview')}
                  >
                    <Download fontSize="small" />
                  </IconButton>
                  <IconButton size="small" sx={{ color: 'white' }}>
                    <Share fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
              <TableContainer component={Paper} sx={{ boxShadow: 'none', bgcolor: 'transparent' }}>
                <Table>
                  <TableHead sx={{ ...getTableHeaderStyle('financial', theme) }}>
                    <TableRow>
                      <TableCell sx={{ color: 'white' }}>Budget Name</TableCell>
                      <TableCell sx={{ color: 'white' }}>Category</TableCell>
                      <TableCell sx={{ color: 'white' }} align="right">Allocated</TableCell>
                      <TableCell sx={{ color: 'white' }} align="right">Spent</TableCell>
                      <TableCell sx={{ color: 'white' }} align="right">Remaining</TableCell>
                      <TableCell sx={{ color: 'white' }} align="right">Utilization</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {mockBudgets.map((budget) => (
                      <TableRow key={budget.id} hover sx={{ ...getTableRowStyle('financial', theme) }}>
                        <TableCell sx={{ color: 'white' }}>{budget.name}</TableCell>
                        <TableCell sx={{ color: 'white' }}>{budget.category}</TableCell>
                        <TableCell sx={{ color: 'white' }} align="right">{formatCurrency(budget.amount)}</TableCell>
                        <TableCell sx={{ color: 'white' }} align="right">{formatCurrency(budget.spent)}</TableCell>
                        <TableCell sx={{ color: 'white' }} align="right">{formatCurrency(budget.amount - budget.spent)}</TableCell>
                        <TableCell sx={{ color: 'white' }} align="right">
                          {((budget.spent / budget.amount) * 100).toFixed(1)}%
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </motion.div>
      </Box>
    </Box>
  );
};

export default FinancialReport;
