/**
 * Offline Synchronization Utility
 * Provides functionality for handling offline mode and data synchronization
 */

import { openDB, IDBPDatabase } from 'idb';

// Database name and version
const DB_NAME = 'ampd-livestock-offline';
const DB_VERSION = 1;

// Store names for different data types
const STORES = {
  PENDING_REQUESTS: 'pendingRequests',
  CACHED_DATA: 'cachedData',
  SYNC_STATUS: 'syncStatus'
};

// Interface for pending requests
interface PendingRequest {
  id: string;
  url: string;
  method: string;
  body?: any;
  headers?: HeadersInit;
  timestamp: number;
  retryCount: number;
}

// Interface for cached data
interface CachedData {
  key: string;
  data: any;
  timestamp: number;
  expiresAt: number;
}

// Interface for sync status
interface SyncStatus {
  id: string;
  lastSyncTime: number;
  syncInProgress: boolean;
  pendingChanges: number;
}

/**
 * Initialize the IndexedDB database
 * @returns Promise resolving to the database instance
 */
const initDatabase = async (): Promise<IDBPDatabase> => {
  return openDB(DB_NAME, DB_VERSION, {
    upgrade(db) {
      // Create stores if they don't exist
      if (!db.objectStoreNames.contains(STORES.PENDING_REQUESTS)) {
        db.createObjectStore(STORES.PENDING_REQUESTS, { keyPath: 'id' });
      }
      
      if (!db.objectStoreNames.contains(STORES.CACHED_DATA)) {
        db.createObjectStore(STORES.CACHED_DATA, { keyPath: 'key' });
      }
      
      if (!db.objectStoreNames.contains(STORES.SYNC_STATUS)) {
        db.createObjectStore(STORES.SYNC_STATUS, { keyPath: 'id' });
      }
    }
  });
};

/**
 * Check if the device is online
 * @returns Boolean indicating online status
 */
export const isOnline = (): boolean => {
  return navigator.onLine;
};

/**
 * Add a request to the pending queue for later synchronization
 * @param request The request to queue
 */
export const queueRequest = async (request: Omit<PendingRequest, 'id' | 'timestamp' | 'retryCount'>): Promise<string> => {
  const db = await initDatabase();
  const id = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const pendingRequest: PendingRequest = {
    ...request,
    id,
    timestamp: Date.now(),
    retryCount: 0
  };
  
  await db.add(STORES.PENDING_REQUESTS, pendingRequest);
  await updateSyncStatus({ pendingChanges: 1 });
  
  return id;
};

/**
 * Get all pending requests
 * @returns Array of pending requests
 */
export const getPendingRequests = async (): Promise<PendingRequest[]> => {
  const db = await initDatabase();
  return db.getAll(STORES.PENDING_REQUESTS);
};

/**
 * Remove a request from the pending queue
 * @param id The ID of the request to remove
 */
export const removePendingRequest = async (id: string): Promise<void> => {
  const db = await initDatabase();
  await db.delete(STORES.PENDING_REQUESTS, id);
  await updateSyncStatus({ pendingChanges: -1 });
};

/**
 * Cache data for offline use
 * @param key The cache key
 * @param data The data to cache
 * @param ttl Time to live in milliseconds (default: 1 hour)
 */
export const cacheData = async (key: string, data: any, ttl: number = 3600000): Promise<void> => {
  const db = await initDatabase();
  const cachedData: CachedData = {
    key,
    data,
    timestamp: Date.now(),
    expiresAt: Date.now() + ttl
  };
  
  await db.put(STORES.CACHED_DATA, cachedData);
};

/**
 * Get cached data
 * @param key The cache key
 * @returns The cached data or null if not found or expired
 */
export const getCachedData = async (key: string): Promise<any | null> => {
  const db = await initDatabase();
  const cachedData = await db.get(STORES.CACHED_DATA, key);
  
  if (!cachedData) {
    return null;
  }
  
  // Check if the data has expired
  if (cachedData.expiresAt < Date.now()) {
    await db.delete(STORES.CACHED_DATA, key);
    return null;
  }
  
  return cachedData.data;
};

/**
 * Clear all cached data
 */
export const clearCache = async (): Promise<void> => {
  const db = await initDatabase();
  await db.clear(STORES.CACHED_DATA);
};

/**
 * Update the sync status
 * @param update Partial sync status update
 */
export const updateSyncStatus = async (update: Partial<Omit<SyncStatus, 'id'>>): Promise<void> => {
  const db = await initDatabase();
  const currentStatus = await db.get(STORES.SYNC_STATUS, 'main') || {
    id: 'main',
    lastSyncTime: 0,
    syncInProgress: false,
    pendingChanges: 0
  };
  
  const newStatus: SyncStatus = {
    ...currentStatus,
    ...update
  };
  
  // If we're updating pendingChanges, increment/decrement instead of replacing
  if ('pendingChanges' in update && typeof update.pendingChanges === 'number') {
    newStatus.pendingChanges = Math.max(0, currentStatus.pendingChanges + update.pendingChanges);
  }
  
  await db.put(STORES.SYNC_STATUS, newStatus);
};

/**
 * Get the current sync status
 * @returns The current sync status
 */
export const getSyncStatus = async (): Promise<SyncStatus> => {
  const db = await initDatabase();
  const status = await db.get(STORES.SYNC_STATUS, 'main');
  
  if (!status) {
    const defaultStatus: SyncStatus = {
      id: 'main',
      lastSyncTime: 0,
      syncInProgress: false,
      pendingChanges: 0
    };
    
    await db.put(STORES.SYNC_STATUS, defaultStatus);
    return defaultStatus;
  }
  
  return status;
};

/**
 * Synchronize pending requests with the server
 * @returns Promise resolving to the number of successfully synced requests
 */
export const synchronize = async (): Promise<number> => {
  if (!isOnline()) {
    throw new Error('Cannot synchronize while offline');
  }
  
  const db = await initDatabase();
  const pendingRequests = await getPendingRequests();
  
  if (pendingRequests.length === 0) {
    return 0;
  }
  
  // Update sync status to indicate sync in progress
  await updateSyncStatus({
    syncInProgress: true,
    lastSyncTime: Date.now()
  });
  
  let successCount = 0;
  
  try {
    for (const request of pendingRequests) {
      try {
        const response = await fetch(request.url, {
          method: request.method,
          headers: request.headers,
          body: request.body ? JSON.stringify(request.body) : undefined
        });
        
        if (response.ok) {
          // Request succeeded, remove from queue
          await removePendingRequest(request.id);
          successCount++;
        } else {
          // Request failed, increment retry count
          const updatedRequest: PendingRequest = {
            ...request,
            retryCount: request.retryCount + 1
          };
          
          await db.put(STORES.PENDING_REQUESTS, updatedRequest);
        }
      } catch (error) {
        console.error('Error syncing request:', error);
        // Increment retry count
        const updatedRequest: PendingRequest = {
          ...request,
          retryCount: request.retryCount + 1
        };
        
        await db.put(STORES.PENDING_REQUESTS, updatedRequest);
      }
    }
  } finally {
    // Update sync status to indicate sync complete
    await updateSyncStatus({
      syncInProgress: false,
      lastSyncTime: Date.now()
    });
  }
  
  return successCount;
};

/**
 * Register event listeners for online/offline events
 * @param onOnline Callback for when the device goes online
 * @param onOffline Callback for when the device goes offline
 */
export const registerConnectivityListeners = (
  onOnline?: () => void,
  onOffline?: () => void
): () => void => {
  const handleOnline = () => {
    console.log('Device is online');
    if (onOnline) {
      onOnline();
    }
  };
  
  const handleOffline = () => {
    console.log('Device is offline');
    if (onOffline) {
      onOffline();
    }
  };
  
  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);
  
  // Return a cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};

export default {
  isOnline,
  queueRequest,
  getPendingRequests,
  removePendingRequest,
  cacheData,
  getCachedData,
  clearCache,
  updateSyncStatus,
  getSyncStatus,
  synchronize,
  registerConnectivityListeners
};
