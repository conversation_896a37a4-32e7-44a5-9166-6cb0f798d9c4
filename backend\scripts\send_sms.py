#!/usr/bin/env python3
"""
SMS Sending Script for AMPD Livestock
This script sends SMS messages using Gammu
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'send_sms.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('send_sms')

def send_sms_with_gammu(phone_number, message, config_file=None):
    """Send an SMS using Gammu"""
    try:
        import gammu
        
        logger.info(f"Sending SMS to {phone_number}: {message}")
        
        # Initialize Gammu state machine
        sm = gammu.StateMachine()
        
        # Read configuration from file if provided
        if config_file and os.path.exists(config_file):
            logger.info(f"Using Gammu config file: {config_file}")
            sm.ReadConfig(Filename=config_file)
        else:
            logger.info("Using default Gammu configuration")
            sm.ReadConfig()
        
        # Connect to the device
        sm.Init()
        
        # Prepare message data
        sms = {
            'Text': message,
            'SMSC': {'Location': 1},
            'Number': phone_number
        }
        
        # Send the message
        result = sm.SendSMS(sms)
        
        logger.info(f"SMS sent successfully: {result}")
        return True, result
    except ImportError:
        logger.error("Gammu not installed. Install with: pip install python-gammu")
        return False, "Gammu not installed"
    except Exception as e:
        logger.error(f"Error sending SMS: {str(e)}")
        return False, str(e)

def send_sms_simulated(phone_number, message):
    """Simulate sending an SMS (when Gammu is not available)"""
    logger.info(f"SIMULATING SMS to {phone_number}: {message}")
    logger.info("This is a simulation only - no actual SMS was sent")
    
    # Simulate a successful response
    result = {
        'MessageReference': 123,
        'Status': 'Sent',
        'Timestamp': datetime.now().isoformat()
    }
    
    return True, result

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Send an SMS using Gammu')
    parser.add_argument('--phone', '-p', required=True, help='Phone number to send to')
    parser.add_argument('--message', '-m', required=True, help='Message to send')
    parser.add_argument('--config', '-c', help='Path to Gammu config file')
    parser.add_argument('--simulate', '-s', action='store_true', help='Simulate sending (don\'t actually send)')
    
    args = parser.parse_args()
    
    if args.simulate:
        success, result = send_sms_simulated(args.phone, args.message)
    else:
        try:
            success, result = send_sms_with_gammu(args.phone, args.message, args.config)
        except ImportError:
            logger.warning("Gammu not available, falling back to simulation")
            success, result = send_sms_simulated(args.phone, args.message)
    
    if success:
        logger.info("SMS process completed successfully")
        return 0
    else:
        logger.error(f"SMS process failed: {result}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
