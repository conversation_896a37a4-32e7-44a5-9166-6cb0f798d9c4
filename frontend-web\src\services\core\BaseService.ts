/**
 * Base Service Class
 * Provides common functionality for all services including error handling and logging
 */

export interface ServiceResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  message?: string;
}

export abstract class BaseService {
  protected serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * Execute a service operation with proper error handling
   * @param operation The async operation to execute
   * @param fallback Optional fallback value if the operation fails
   * @param errorMessage Custom error message
   * @returns ServiceResult with the operation result
   */
  protected async executeOperation<T>(
    operation: () => Promise<T>,
    fallback?: T,
    errorMessage?: string
  ): Promise<ServiceResult<T>> {
    try {
      const data = await operation();
      return {
        success: true,
        data
      };
    } catch (error) {
      console.error(
        `[${this.serviceName}] Error:`,
        errorMessage || (error instanceof Error ? error.message : 'Unknown error'),
        error
      );

      // If a fallback is provided, return it as successful but with a message
      if (fallback !== undefined) {
        return {
          success: true,
          data: fallback,
          message: `Using fallback data. Original error: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        };
      }

      // Otherwise return as an error
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error'),
        message: errorMessage || (error instanceof Error ? error.message : 'An error occurred')
      };
    }
  }

  /**
   * Log an informational message
   * @param message The message to log
   * @param data Optional data to include
   */
  protected logInfo(message: string, data?: any): void {
    console.info(`[${this.serviceName}] ${message}`, data || '');
  }

  /**
   * Log a warning message
   * @param message The warning message
   * @param data Optional data to include
   */
  protected logWarning(message: string, data?: any): void {
    console.warn(`[${this.serviceName}] WARNING: ${message}`, data || '');
  }

  /**
   * Log an error message
   * @param message The error message
   * @param error The error object
   */
  protected logError(message: string, error?: any): void {
    console.error(`[${this.serviceName}] ERROR: ${message}`, error || '');
  }
}
