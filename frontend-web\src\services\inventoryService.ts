interface InventoryItem {
  id: string;
  category: 'feed' | 'medicine' | 'equipment' | 'supplies';
  name: string;
  quantity: number;
  unit: string;
  minimumLevel: number;
  location: string;
  lastRestocked: string;
  expiryDate?: string;
}

interface StockMovement {
  id: string;
  itemId: string;
  type: 'in' | 'out';
  quantity: number;
  date: string;
  reason: string;
}

const inventoryService = {
  getInventory: async (): Promise<InventoryItem[]> => {
    return [];
  },

  getStockMovements: async (itemId: string): Promise<StockMovement[]> => {
    return [];
  },

  updateStock: async (movement: Omit<StockMovement, 'id'>): Promise<void> => {
    // Implementation
  },

  getLowStockAlerts: async (): Promise<InventoryItem[]> => {
    return [];
  }
};

export default inventoryService;
