import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, useTheme, alpha, Grid, Divider, Tooltip } from '@mui/material';
import { motion } from 'framer-motion';
// Removed useThemeContext import as it was deleted
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  WbSunny, 
  Cloud, 
  Opacity, 
  Air, 
  Thermostat, 
  WaterDrop, 
  Grain, 
  Warning
} from '@mui/icons-material';

interface WeatherData {
  location: string;
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  precipitation: number;
  forecast: {
    day: string;
    condition: string;
    high: number;
    low: number;
    precipitation: number;
  }[];
  alerts: {
    type: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }[];
}

interface WeatherWidgetProps {
  location?: string;
  refreshInterval?: number;
}

/**
 * Weather Widget Component for the Dashboard
 * Displays current weather and forecast for the farm location
 */
const WeatherWidget: React.FC<WeatherWidgetProps> = ({ 
  location = 'Pretoria, South Africa',
  refreshInterval = 1800000 // 30 minutes
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();
  
  const [weather, setWeather] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock weather data - in a real app, this would come from a weather API
  useEffect(() => {
    const fetchWeather = () => {
      setLoading(true);
      
      // Simulate API call with timeout
      setTimeout(() => {
        // Mock data - would be replaced with actual API call
        const mockWeatherData: WeatherData = {
          location: location,
          temperature: 24,
          condition: 'Partly Cloudy',
          humidity: 65,
          windSpeed: 12,
          precipitation: 20,
          forecast: [
            { day: 'Today', condition: 'Partly Cloudy', high: 26, low: 18, precipitation: 20 },
            { day: 'Tomorrow', condition: 'Sunny', high: 28, low: 17, precipitation: 10 },
            { day: 'Wed', condition: 'Rainy', high: 22, low: 16, precipitation: 80 },
            { day: 'Thu', condition: 'Cloudy', high: 23, low: 15, precipitation: 30 },
            { day: 'Fri', condition: 'Sunny', high: 27, low: 16, precipitation: 0 }
          ],
          alerts: [
            {
              type: 'Rain',
              description: 'Heavy rainfall expected on Wednesday',
              severity: 'medium'
            }
          ]
        };
        
        setWeather(mockWeatherData);
        setLoading(false);
      }, 1000);
    };
    
    fetchWeather();
    
    // Set up refresh interval
    const interval = setInterval(fetchWeather, refreshInterval);
    
    return () => clearInterval(interval);
  }, [location, refreshInterval]);
  
  // Get weather icon based on condition
  const getWeatherIcon = (condition: string) => {
    const conditionLower = condition.toLowerCase();
    if (conditionLower.includes('sun') || conditionLower.includes('clear')) {
      return <WbSunny fontSize="large" sx={{ color: '#FFB300' }} />;
    } else if (conditionLower.includes('cloud')) {
      return <Cloud fontSize="large" sx={{ color: '#78909C' }} />;
    } else if (conditionLower.includes('rain') || conditionLower.includes('shower')) {
      return <Opacity fontSize="large" sx={{ color: '#29B6F6' }} />;
    } else {
      return <Cloud fontSize="large" />;
    }
  };
  
  // Get color for precipitation chance
  const getPrecipitationColor = (chance: number) => {
    if (chance < 30) return theme.palette.success.main;
    if (chance < 70) return theme.palette.warning.main;
    return theme.palette.error.main;
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
        <CircularProgress size={40} />
      </Box>
    );
  }
  
  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }
  
  if (!weather) {
    return null;
  }
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box
        sx={{
          p: 2,
          borderRadius: 2,
          bgcolor: alpha(theme.palette.background.paper, 0.7),
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          overflow: 'hidden'
        }}
      >
        {/* Header with location and current conditions */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              {translate ? translate('dashboard.weather.title', { fallback: 'Farm Weather' }) : 'Farm Weather'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {weather.location}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {getWeatherIcon(weather.condition)}
            <Typography variant="h4" fontWeight="bold" sx={{ ml: 1 }}>
              {weather.temperature}°C
            </Typography>
          </Box>
        </Box>
        
        {/* Current conditions */}
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Opacity sx={{ color: theme.palette.info.main, mr: 1 }} />
              <Box>
                <Typography variant="caption" color="text.secondary">
                  {translate ? translate('dashboard.weather.humidity', { fallback: 'Humidity' }) : 'Humidity'}
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {weather.humidity}%
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Air sx={{ color: theme.palette.info.main, mr: 1 }} />
              <Box>
                <Typography variant="caption" color="text.secondary">
                  {translate ? translate('dashboard.weather.wind', { fallback: 'Wind' }) : 'Wind'}
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {weather.windSpeed} km/h
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <WaterDrop sx={{ color: getPrecipitationColor(weather.precipitation), mr: 1 }} />
              <Box>
                <Typography variant="caption" color="text.secondary">
                  {translate ? translate('dashboard.weather.precipitation', { fallback: 'Rain Chance' }) : 'Rain Chance'}
                </Typography>
                <Typography variant="body2" fontWeight="medium">
                  {weather.precipitation}%
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
        
        <Divider sx={{ my: 2 }} />
        
        {/* 5-day forecast */}
        <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
          {translate ? translate('dashboard.weather.forecast', { fallback: '5-Day Forecast' }) : '5-Day Forecast'}
        </Typography>
        
        <Grid container spacing={1}>
          {weather.forecast.map((day, index) => (
            <Grid item xs={12/5} key={index}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  borderRadius: 1,
                  bgcolor: index === 0 ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                <Typography variant="caption" fontWeight="bold">
                  {day.day}
                </Typography>
                <Tooltip title={day.condition}>
                  <Box sx={{ my: 0.5 }}>
                    {getWeatherIcon(day.condition)}
                  </Box>
                </Tooltip>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Typography variant="body2" fontWeight="bold">
                    {day.high}°
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {day.low}°
                  </Typography>
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: 4,
                    borderRadius: 2,
                    bgcolor: getPrecipitationColor(day.precipitation),
                    opacity: day.precipitation / 100,
                    mt: 0.5
                  }}
                />
              </Box>
            </Grid>
          ))}
        </Grid>
        
        {/* Weather alerts */}
        {weather.alerts.length > 0 && (
          <Box
            sx={{
              mt: 2,
              p: 1.5,
              borderRadius: 1,
              bgcolor: alpha(theme.palette.warning.main, 0.1),
              border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`,
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <Warning sx={{ color: theme.palette.warning.main, mr: 1 }} />
            <Box>
              <Typography variant="caption" fontWeight="bold" color="warning.main">
                {weather.alerts[0].type}
              </Typography>
              <Typography variant="body2">
                {weather.alerts[0].description}
              </Typography>
            </Box>
          </Box>
        )}
      </Box>
    </motion.div>
  );
};

export default WeatherWidget;
