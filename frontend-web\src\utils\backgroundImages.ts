/**
 * Background Images Utility
 *
 * This file provides a centralized place to manage background images used across the application.
 * Using this utility ensures consistency in image usage and makes it easier to update images.
 */

// Module background images
export const moduleBackgrounds = {
  // Animals module
  animals: {
    main: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    rfid: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    profiles: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    records: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    tracking: 'https://www.farmersweekly.co.za/wp-content/uploads/2020/07/jj-gouin-3y-HX8jxvKQ-unsplash.jpg',
    genealogy: 'https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg',
  },

  // Commercial module
  commercial: {
    main: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    marketplace: 'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    orders: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    suppliers: 'https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg',
    pricing: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    auctions: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
  },

  // Breeding module
  breeding: {
    main: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    heatCalendar: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    pregnancy: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    predictions: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
  },

  // Health module
  health: {
    main: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    vaccinations: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    diseases: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    treatments: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    appointments: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    records: 'https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg',
  },

  // Reports module
  reports: {
    main: 'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    custom: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    market: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    health: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    financial: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    performance: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
  },

  // Financial module
  financial: {
    main: 'https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg',
    roi: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    forecast: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    invoices: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    budgets: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
  },

  // Feeding module
  feeding: {
    main: 'https://images.unsplash.com/photo-1605493725784-56651e4c5f1a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    inventory: 'https://images.unsplash.com/photo-1560009571-bbf933d6ce8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80',
    schedules: 'https://images.unsplash.com/photo-1560009571-bbf933d6ce8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80',
    suppliers: 'https://images.unsplash.com/photo-1560009571-bbf933d6ce8d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80',
  },

  // Compliance module
  compliance: {
    main: 'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    certifications: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    inspections: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    documents: 'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
  },

  // Resources module
  resources: {
    main: 'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    guidelines: 'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    training: 'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    support: 'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
  },
};

// Card background images by category
export const cardBackgrounds = {
  animals: [
    'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/07/jj-gouin-3y-HX8jxvKQ-unsplash.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2022/05/Bonsmara-cattle-herd.jpg',
    'https://wallpaperaccess.com/full/4396596.png',
    'https://www.wallpaperflare.com/static/181/697/179/curiosity-herd-black-and-white-cattle-wallpaper.jpg',
  ],

  finance: [
    'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    'https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg',
    'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/06/money-1.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/09/calculator-money.jpg',
    'https://images.pexels.com/photos/2050428/pexels-photo-2050428.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  ],

  health: [
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
  ],

  feed: [
    'https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg',
    'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    'https://img.freepik.com/premium-photo/twilight-transforms-grazing-cows-into-enchanting-shadows-against-sunset_795881-8815.jpg',
  ],

  breeding: [
    'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
  ],

  commercial: [
    'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    'https://th.bing.com/th/id/OIP.IFIFu9bN4YW3BxGapztuSAHaEo?w=566&h=354&rs=1&pid=ImgDetMain',
  ],

  compliance: [
    'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
    'https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png',
  ],

  resources: [
    'https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg',
    'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    'https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/05/farmer-with-tablet.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2019/10/farmer-with-smartphone.jpg',
  ],

  healthImages: [
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/03/vet-examining-cow.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2019/08/vaccination-cattle.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2021/02/veterinarian-with-stethoscope.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2018/06/cattle-health-check.jpg',
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
  ],

  breedingImages: [
    'https://www.farmersweekly.co.za/wp-content/uploads/2019/05/cattle-breeding.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/11/cow-with-calf.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2018/09/nguni-cattle-breeding.jpg',
    'https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg',
    'https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg',
  ],

  commercialImages: [
    'https://www.farmersweekly.co.za/wp-content/uploads/2020/04/cattle-auction.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2019/12/livestock-market.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2021/03/cattle-sale.jpg',
    'https://www.farmersweekly.co.za/wp-content/uploads/2018/11/livestock-trading.jpg',
    'https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png',
  ],
};

// New requested background images
export const requestedBackgrounds = [
  'https://wallpaperaccess.com/full/4396596.png',
  'https://www.wallpaperflare.com/static/181/697/179/curiosity-herd-black-and-white-cattle-wallpaper.jpg',
  'https://images.pexels.com/photos/2050428/pexels-photo-2050428.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
  'https://img.freepik.com/premium-photo/twilight-transforms-grazing-cows-into-enchanting-shadows-against-sunset_795881-8815.jpg',
  'https://th.bing.com/th/id/OIP.IFIFu9bN4YW3BxGapztuSAHaEo?w=566&h=354&rs=1&pid=ImgDetMain'
];

/**
 * Get a random background image from a specific category
 * @param category The category to get a background image from
 * @returns A URL to a background image
 */
export const getRandomBackground = (category: keyof typeof cardBackgrounds): string => {
  const backgrounds = cardBackgrounds[category];
  const randomIndex = Math.floor(Math.random() * backgrounds.length);
  return backgrounds[randomIndex];
};

/**
 * Get a module background image
 * @param module The main module (e.g., 'animals', 'commercial')
 * @param submodule The submodule (e.g., 'rfid', 'marketplace')
 * @returns A URL to a background image
 */
export const getModuleBackground = (
  module: keyof typeof moduleBackgrounds,
  submodule?: string
): string => {
  const moduleData = moduleBackgrounds[module];

  if (submodule && submodule in moduleData) {
    return moduleData[submodule as keyof typeof moduleData];
  }

  return moduleData.main;
};

/**
 * Get a random background from the requested backgrounds
 * @returns A URL to a background image from the requested list
 */
export const getRandomRequestedBackground = (): string => {
  const randomIndex = Math.floor(Math.random() * requestedBackgrounds.length);
  return requestedBackgrounds[randomIndex];
};

/**
 * Create a blended background style with an image and gradient overlay
 * @param backgroundImage The background image URL
 * @param primaryColor The primary color for the gradient
 * @param secondaryColor The secondary color for the gradient
 * @param opacity The opacity of the gradient overlay (0-1)
 * @returns A CSS style object for the blended background
 */
export const createBlendedBackground = (
  backgroundImage: string,
  primaryColor: string,
  secondaryColor: string,
  opacity: number = 0.8
): React.CSSProperties => {
  return {
    backgroundImage: `linear-gradient(135deg, ${primaryColor}${Math.round(opacity * 99)}, ${secondaryColor}${Math.round(opacity * 85)}), url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
  };
};
