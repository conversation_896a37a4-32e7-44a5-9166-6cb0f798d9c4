import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  useTheme,
  alpha,
  Tab,
  Tabs
} from '@mui/material';
import {
  Add,
  Schedule,
  Group,
  Restaurant,
  CalendarToday,
  Notifications,
  Assessment
} from '../../utils/iconImports';
import {
  SubModuleDashboard,
  SubModuleDataTable,
  SubModuleChart,
  CustomButton
} from '../../components/common';
import { useLanguage } from '../../contexts/LanguageContext';

// Mock feeding schedules
const mockFeedingSchedules = [
  {
    id: 'FS001',
    name: 'Morning Feed - Dairy Cows',
    time: '06:00',
    feedType: 'Concentrate Mix',
    quantity: '250kg',
    location: 'Barn A',
    animalGroups: ['Dairy Cows', 'Heifers'],
    status: 'active'
  },
  {
    id: 'FS002',
    name: 'Afternoon Feed - All Cattle',
    time: '14:00',
    feedType: 'Hay and Silage',
    quantity: '500kg',
    location: 'All Pastures',
    animalGroups: ['All Cattle'],
    status: 'active'
  },
  {
    id: 'FS003',
    name: 'Evening Feed - Calves',
    time: '18:00',
    feedType: 'Calf Starter',
    quantity: '50kg',
    location: 'Calf Pen',
    animalGroups: ['Calves'],
    status: 'active'
  },
  {
    id: 'FS004',
    name: 'Special Diet - Pregnant Cows',
    time: '10:00',
    feedType: 'Prenatal Mix',
    quantity: '100kg',
    location: 'Maternity Pen',
    animalGroups: ['Pregnant Cows'],
    status: 'scheduled'
  },
  {
    id: 'FS005',
    name: 'Weekend Feed - All Animals',
    time: '12:00',
    feedType: 'Mixed Feed',
    quantity: '350kg',
    location: 'All Locations',
    animalGroups: ['All Animals'],
    status: 'inactive'
  }
];

const FeedingSchedules: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const [timeRange, setTimeRange] = useState('today');
  const [tabValue, setTabValue] = useState(0);

  // Define metrics for the dashboard
  const metrics = [
    {
      label: "Active Schedules",
      value: mockFeedingSchedules.filter(s => s.status === 'active').length,
      icon: <Schedule />,
      color: theme.palette.success.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: "Total Daily Feed",
      value: "800 kg",
      icon: <Restaurant />,
      color: theme.palette.primary.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last week"
      }
    },
    {
      label: "Animal Groups",
      value: "5",
      icon: <Group />,
      color: theme.palette.info.main
    },
    {
      label: "Scheduled Feeds",
      value: mockFeedingSchedules.filter(s => s.status === 'scheduled').length,
      icon: <CalendarToday />,
      color: theme.palette.warning.main
    }
  ];

  // Define actions for the dashboard
  const actions = [
    {
      label: "Create Schedule",
      icon: <Add />,
      onClick: () => console.log("Create new feeding schedule"),
      color: theme.palette.success.main,
      description: "Add new feeding routine",
      component: (
        <CustomButton
          variant="contained"
          color="success"
          startIcon={<Add />}
          onClick={() => console.log("Create new feeding schedule")}
        >
          Create Schedule
        </CustomButton>
      )
    },
    {
      label: "Generate Report",
      icon: <Assessment />,
      onClick: () => console.log("Generate feeding report"),
      color: theme.palette.info.main,
      description: "Export feeding data",
      component: (
        <CustomButton
          variant="contained"
          color="info"
          startIcon={<Assessment />}
          onClick={() => console.log("Generate feeding report")}
        >
          Generate Report
        </CustomButton>
      )
    },
    {
      label: "Set Notifications",
      icon: <Notifications />,
      onClick: () => console.log("Set feeding notifications"),
      color: theme.palette.warning.main,
      description: "Feeding reminders",
      component: (
        <CustomButton
          variant="contained"
          color="warning"
          startIcon={<Notifications />}
          onClick={() => console.log("Set feeding notifications")}
        >
          Set Notifications
        </CustomButton>
      )
    }
  ];

  // Define feeding schedule table columns
  const feedingScheduleColumns = [
    { id: 'name', label: 'Schedule Name', minWidth: 200 },
    { id: 'time', label: 'Time', minWidth: 100 },
    { id: 'feedType', label: 'Feed Type', minWidth: 150 },
    { id: 'quantity', label: 'Quantity', minWidth: 100 },
    { id: 'location', label: 'Location', minWidth: 150 },
    {
      id: 'animalGroups',
      label: 'Animal Groups',
      minWidth: 200,
      format: (value: string[]) => (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {value.map(group => (
            <Chip
              key={group}
              label={group}
              size="small"
              icon={<Group fontSize="small" />}
              sx={{ mr: 0.5, mb: 0.5 }}
            />
          ))}
        </Box>
      )
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value: string) => (
        <Chip
          label={value.toUpperCase()}
          color={
            value === 'active' ? 'success' :
            value === 'scheduled' ? 'info' :
            'default'
          }
          size="small"
          sx={{ fontWeight: 'medium' }}
        />
      )
    }
  ];

  // Define feed distribution chart data
  const feedDistributionData = {
    labels: ['Dairy Cows', 'Heifers', 'Calves', 'Pregnant Cows', 'Bulls'],
    datasets: [
      {
        label: 'Feed Distribution (kg)',
        data: [250, 150, 50, 100, 50],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.info.main
        ]
      }
    ]
  };

  // Define feeding schedule by time chart data
  const feedingScheduleByTimeData = {
    labels: ['06:00', '10:00', '14:00', '18:00'],
    datasets: [
      {
        label: 'Feed Quantity (kg)',
        data: [250, 100, 500, 50],
        backgroundColor: theme.palette.primary.main,
        borderColor: theme.palette.primary.main
      }
    ]
  };

  // Define tabs for the dashboard
  const dashboardTabs = [
    {
      label: "All Schedules",
      icon: <Schedule />,
      content: <Box>{translate('feeding.all_schedules')}</Box>
    },
    {
      label: "Active",
      icon: <Schedule />,
      content: <Box>{translate('feeding.active_schedules')}</Box>
    },
    {
      label: "Scheduled",
      icon: <CalendarToday />,
      content: <Box>{translate('feeding.scheduled_feeds')}</Box>
    },
    {
      label: "Inactive",
      icon: <Schedule />,
      content: <Box>{translate('feeding.inactive_schedules')}</Box>
    }
  ];

  // Filter schedules based on active tab
  const getFilteredSchedules = () => {
    if (tabValue === 0) {
      return mockFeedingSchedules;
    } else if (tabValue === 1) {
      return mockFeedingSchedules.filter(s => s.status === 'active');
    } else if (tabValue === 2) {
      return mockFeedingSchedules.filter(s => s.status === 'scheduled');
    } else {
      return mockFeedingSchedules.filter(s => s.status === 'inactive');
    }
  };

  return (
    <SubModuleDashboard
      title="Feeding Schedules"
      subtitle="Manage and monitor all feeding schedules and routines"
      parentModule="feeding"
      subModule="schedules"
      icon={<Schedule />}
      metrics={metrics}
      actions={actions}
      tabs={dashboardTabs}
      activeTab={tabValue}
      onTabChange={(index) => setTabValue(index)}
      isLoading={false}
      onRefresh={() => console.log("Refreshing feeding schedules")}
      onBack={() => navigate('/dashboard/feeding')}
    >
      {/* Time Range Selector */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <CustomButton
            variant="contained"
            startIcon={<Add />}
            onClick={() => console.log("Create new feeding schedule")}
          >
            Add New Schedule
          </CustomButton>
        </Box>
        <Paper
          elevation={0}
          sx={{
            p: 0.5,
            borderRadius: 2,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}>
          <Tabs
            value={timeRange}
            onChange={(_, newValue) => setTimeRange(newValue)}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              minHeight: 'unset',
              '& .MuiTab-root': {
                minHeight: 'unset',
                py: 0.5,
                px: 2,
                minWidth: 'unset',
                borderRadius: 1,
                fontSize: '0.875rem',
                fontWeight: 'medium',
                textTransform: 'none',
                '&.Mui-selected': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                }
              }
            }}
          >
            <Tab label="Today" value="today" />
            <Tab label="This Week" value="week" />
            <Tab label="This Month" value="month" />
            <Tab label="All Time" value="all" />
          </Tabs>
        </Paper>
      </Box>

      {/* Feeding Schedules Table */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Box sx={{
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
            borderRadius: 2,
            p: 0.5
          }}>
            <SubModuleDataTable
              title="Feeding Schedules"
              columns={feedingScheduleColumns}
              data={getFilteredSchedules()}
              onView={(row) => console.log("View schedule", row)}
              onEdit={(row) => console.log("Edit schedule", row)}
              onDelete={(row) => console.log("Delete schedule", row)}
              onExport={() => console.log("Export schedules")}
              onPrint={() => console.log("Print schedules")}
              onShare={() => console.log("Share schedules")}
              moduleColor={theme.palette.primary.main}
              emptyStateMessage="No feeding schedules found"
            />
          </Box>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Box sx={{
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
            borderRadius: 2,
            p: 0.5,
            height: '100%'
          }}>
            <SubModuleChart
              title="Feed Distribution by Animal Group"
              description="Distribution of feed quantities across different animal groups"
              type="pie"
              data={feedDistributionData}
              height={350}
              moduleColor={theme.palette.primary.main}
              onExport={() => console.log("Exporting chart")}
              onPrint={() => console.log("Printing chart")}
            />
          </Box>
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
            backdropFilter: 'blur(10px)',
            border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
            borderRadius: 2,
            p: 0.5,
            height: '100%'
          }}>
            <SubModuleChart
              title="Feeding Schedule by Time"
              description="Feed quantities distributed throughout the day"
              type="bar"
              data={feedingScheduleByTimeData}
              height={350}
              moduleColor={theme.palette.success.main}
              onExport={() => console.log("Exporting chart")}
              onPrint={() => console.log("Printing chart")}
            />
          </Box>
        </Grid>
      </Grid>

      {/* Today's Schedule */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.main, 0.05)})`,
              backdropFilter: 'blur(10px)',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
              boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
              '&:hover': {
                boxShadow: `0 8px 30px ${alpha(theme.palette.common.black, 0.1)}`,
                borderColor: alpha(theme.palette.primary.main, 0.25)
              },
              transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
            }}
          >
            <Typography variant="h6" fontWeight="bold" color={theme.palette.primary.main} gutterBottom>
              Today's Feeding Schedule
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <List sx={{ p: 0 }}>
              {mockFeedingSchedules
                .filter(s => s.status === 'active')
                .map((schedule) => (
                  <React.Fragment key={schedule.id}>
                    <ListItem
                      sx={{
                        py: 2,
                        bgcolor: alpha(theme.palette.success.main, 0.1),
                        borderRadius: 2,
                        mb: 2,
                        border: `1px solid ${alpha(theme.palette.success.main, 0.15)}`,
                        boxShadow: `0 4px 10px ${alpha(theme.palette.common.black, 0.03)}`,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: `0 6px 15px ${alpha(theme.palette.common.black, 0.05)}`,
                          borderColor: alpha(theme.palette.success.main, 0.25)
                        }
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.8) }}>
                          <Schedule />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box display="flex"
                            flexDirection={{ xs: 'column', sm: 'row' }}
                            alignItems={{ xs: 'flex-start', sm: 'center' }}
                            gap={1}
                          >
                            <Typography variant="subtitle1" fontWeight="bold">{schedule.time}</Typography>
                            <Typography variant="body1" sx={{ ml: { xs: 0, sm: 2 } }}>
                              {schedule.name}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography variant="body2" component="span">
                              {schedule.feedType} - {schedule.quantity} - {schedule.location}
                            </Typography>
                            <Box mt={1} sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {schedule.animalGroups.map(group => (
                                <Chip
                                  key={group}
                                  label={group}
                                  size="small"
                                  icon={<Group fontSize="small" />}
                                  sx={{ mr: 1, mb: 1 }}
                                />
                              ))}
                            </Box>
                          </>
                        }
                      />
                    </ListItem>
                  </React.Fragment>
                ))}
            </List>
          </Paper>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <CustomButton
              variant="outlined"
              startIcon={<Assessment />}
              onClick={() => console.log("Generate full report")}
            >
              Generate Full Report
            </CustomButton>
            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={() => console.log("Create new feeding schedule")}
            >
              Add New Schedule
            </CustomButton>
          </Box>
        </Grid>
      </Grid>
    </SubModuleDashboard>
  );
};

export default FeedingSchedules;
