/**
 * Inject Inventory Data Script
 * 
 * This script injects realistic inventory data into the MongoDB database
 */

require('dotenv').config();
const { MongoClient, ObjectId } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection string
const uri = process.env.MONGODB_URI || process.env.MONGODB_ATLAS_URI;
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Inventory items to inject
const inventoryItems = [
  {
    _id: new ObjectId(),
    name: 'Alfalfa Hay',
    category: 'feed',
    subcategory: 'hay',
    description: 'High-quality alfalfa hay for cattle and horses',
    sku: 'FEED-HAY-001',
    barcode: '6001234567890',
    quantity: 2500,
    unit: 'kg',
    unitCost: 2.5,
    totalValue: 6250,
    location: 'Feed Storage Barn',
    minimumLevel: 500,
    reorderLevel: 1000,
    reorderQuantity: 2000,
    supplier: {
      name: 'Farm Feed Suppliers',
      contactPerson: '<PERSON>',
      phone: '+27 82 123 4567',
      email: '<EMAIL>',
      website: 'www.farmfeedsuppliers.co.za'
    },
    expirationDate: new Date('2024-12-31'),
    images: [],
    notes: 'Main feed for dairy cattle',
    status: 'active',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Dairy Concentrate',
    category: 'feed',
    subcategory: 'concentrate',
    description: 'High-protein dairy concentrate for lactating cows',
    sku: 'FEED-CONC-001',
    barcode: '6001234567891',
    quantity: 1000,
    unit: 'kg',
    unitCost: 5.8,
    totalValue: 5800,
    location: 'Feed Storage Barn',
    minimumLevel: 200,
    reorderLevel: 400,
    reorderQuantity: 1000,
    supplier: {
      name: 'Livestock Nutrition Ltd',
      contactPerson: 'Sarah Nkosi',
      phone: '+27 83 987 6543',
      email: '<EMAIL>',
      website: 'www.livestocknutrition.co.za'
    },
    expirationDate: new Date('2024-10-15'),
    images: [],
    notes: 'Premium concentrate for dairy cattle',
    status: 'active',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Oxytetracycline',
    category: 'medication',
    subcategory: 'antibiotic',
    description: 'Broad-spectrum antibiotic for livestock',
    sku: 'MED-ANTI-001',
    barcode: '6001234567892',
    quantity: 20,
    unit: 'bottle',
    unitCost: 350,
    totalValue: 7000,
    location: 'Medicine Cabinet',
    minimumLevel: 5,
    reorderLevel: 8,
    reorderQuantity: 15,
    supplier: {
      name: 'Veterinary Supplies SA',
      contactPerson: 'Dr. Patel',
      phone: '+27 11 555 1234',
      email: '<EMAIL>',
      website: 'www.vetsupplies.co.za'
    },
    expirationDate: new Date('2025-06-30'),
    images: [],
    notes: 'For treatment of bacterial infections',
    status: 'active',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Ear Tags',
    category: 'supplies',
    subcategory: 'identification',
    description: 'Plastic ear tags for livestock identification',
    sku: 'SUP-TAG-001',
    barcode: '6001234567893',
    quantity: 500,
    unit: 'piece',
    unitCost: 8,
    totalValue: 4000,
    location: 'Supply Room',
    minimumLevel: 100,
    reorderLevel: 150,
    reorderQuantity: 300,
    supplier: {
      name: 'Farm Equipment Distributors',
      contactPerson: 'Michael Botha',
      phone: '+27 12 345 6789',
      email: '<EMAIL>',
      website: 'www.farmeqdist.co.za'
    },
    expirationDate: null,
    images: [],
    notes: 'Yellow plastic ear tags with sequential numbering',
    status: 'active',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Hoof Trimming Kit',
    category: 'equipment',
    subcategory: 'tools',
    description: 'Professional hoof trimming kit for cattle',
    sku: 'EQUIP-TOOL-001',
    barcode: '6001234567894',
    quantity: 2,
    unit: 'set',
    unitCost: 1500,
    totalValue: 3000,
    location: 'Tool Shed',
    minimumLevel: 1,
    reorderLevel: 1,
    reorderQuantity: 1,
    supplier: {
      name: 'Farm Equipment Distributors',
      contactPerson: 'Michael Botha',
      phone: '+27 12 345 6789',
      email: '<EMAIL>',
      website: 'www.farmeqdist.co.za'
    },
    expirationDate: null,
    images: [],
    notes: 'Complete kit with electric grinder and manual tools',
    status: 'active',
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Inventory transactions to inject
const inventoryTransactions = [
  {
    _id: new ObjectId(),
    item: inventoryItems[0]._id, // Alfalfa Hay
    date: new Date('2024-01-15'),
    type: 'purchase',
    quantity: 2000,
    unitCost: 2.5,
    totalCost: 5000,
    source: 'Farm Feed Suppliers',
    reference: 'PO-2024-001',
    notes: 'Bulk purchase of alfalfa hay',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    item: inventoryItems[0]._id, // Alfalfa Hay
    date: new Date('2024-02-10'),
    type: 'use',
    quantity: -500,
    unitCost: 2.5,
    totalCost: -1250,
    destination: 'Cattle Feeding',
    reference: 'USE-2024-015',
    notes: 'Monthly usage for cattle feeding',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    item: inventoryItems[1]._id, // Dairy Concentrate
    date: new Date('2024-01-20'),
    type: 'purchase',
    quantity: 1000,
    unitCost: 5.8,
    totalCost: 5800,
    source: 'Livestock Nutrition Ltd',
    reference: 'PO-2024-003',
    notes: 'Quarterly purchase of dairy concentrate',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    item: inventoryItems[1]._id, // Dairy Concentrate
    date: new Date('2024-02-15'),
    type: 'use',
    quantity: -200,
    unitCost: 5.8,
    totalCost: -1160,
    destination: 'Dairy Cattle Feeding',
    reference: 'USE-2024-022',
    notes: 'Monthly usage for dairy cattle',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    item: inventoryItems[2]._id, // Oxytetracycline
    date: new Date('2024-03-05'),
    type: 'purchase',
    quantity: 20,
    unitCost: 350,
    totalCost: 7000,
    source: 'Veterinary Supplies SA',
    reference: 'PO-2024-008',
    notes: 'Restocking of antibiotics',
    performer: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Suppliers to inject
const suppliers = [
  {
    _id: new ObjectId(),
    name: 'Farm Feed Suppliers',
    type: 'feed',
    contactPerson: 'John Venter',
    phone: '+27 82 123 4567',
    email: '<EMAIL>',
    website: 'www.farmfeedsuppliers.co.za',
    address: {
      street: '123 Farm Road',
      city: 'Pretoria',
      province: 'Gauteng',
      postalCode: '0001',
      country: 'South Africa'
    },
    paymentTerms: 'Net 30',
    taxId: 'VAT123456789',
    notes: 'Primary supplier for hay and silage',
    status: 'active',
    items: [inventoryItems[0]._id], // Alfalfa Hay
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Livestock Nutrition Ltd',
    type: 'feed',
    contactPerson: 'Sarah Nkosi',
    phone: '+27 83 987 6543',
    email: '<EMAIL>',
    website: 'www.livestocknutrition.co.za',
    address: {
      street: '45 Industrial Avenue',
      city: 'Johannesburg',
      province: 'Gauteng',
      postalCode: '2000',
      country: 'South Africa'
    },
    paymentTerms: 'Net 15',
    taxId: 'VAT987654321',
    notes: 'Supplier for concentrates and supplements',
    status: 'active',
    items: [inventoryItems[1]._id], // Dairy Concentrate
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    _id: new ObjectId(),
    name: 'Veterinary Supplies SA',
    type: 'medication',
    contactPerson: 'Dr. Patel',
    phone: '+27 11 555 1234',
    email: '<EMAIL>',
    website: 'www.vetsupplies.co.za',
    address: {
      street: '78 Health Street',
      city: 'Cape Town',
      province: 'Western Cape',
      postalCode: '8001',
      country: 'South Africa'
    },
    paymentTerms: 'Net 30',
    taxId: 'VAT456789123',
    notes: 'Supplier for all veterinary medications and supplies',
    status: 'active',
    items: [inventoryItems[2]._id], // Oxytetracycline
    createdBy: new ObjectId(), // Will be updated with actual user ID
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// Connect to MongoDB and inject inventory data
async function injectInventoryData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000
    });

    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);
    const inventoryItemsCollection = db.collection('inventoryitems');
    const inventoryTransactionsCollection = db.collection('inventorytransactions');
    const suppliersCollection = db.collection('suppliers');
    const usersCollection = db.collection('users');

    // Get a user ID for the creator field
    const adminUser = await usersCollection.findOne({ role: 'admin' });
    const creatorId = adminUser ? adminUser._id : new ObjectId();

    // Insert or update inventory items
    for (const item of inventoryItems) {
      // Set the creator to an actual user
      item.createdBy = creatorId;
      
      const existingItem = await inventoryItemsCollection.findOne({ 
        name: item.name,
        category: item.category
      });

      if (existingItem) {
        logger.info(`Updating inventory item: ${item.name}`);
        await inventoryItemsCollection.updateOne(
          { _id: existingItem._id },
          { $set: { ...item, _id: existingItem._id } }
        );
      } else {
        logger.info(`Inserting new inventory item: ${item.name}`);
        await inventoryItemsCollection.insertOne(item);
      }
    }

    // Insert or update inventory transactions
    for (const transaction of inventoryTransactions) {
      // Set the performer to an actual user
      transaction.performer = creatorId;
      
      const existingTransaction = await inventoryTransactionsCollection.findOne({ 
        item: transaction.item,
        date: transaction.date,
        type: transaction.type
      });

      if (existingTransaction) {
        logger.info(`Updating inventory transaction: ${transaction._id}`);
        await inventoryTransactionsCollection.updateOne(
          { _id: existingTransaction._id },
          { $set: { ...transaction, _id: existingTransaction._id } }
        );
      } else {
        logger.info(`Inserting new inventory transaction: ${transaction._id}`);
        await inventoryTransactionsCollection.insertOne(transaction);
      }
    }

    // Insert or update suppliers
    for (const supplier of suppliers) {
      // Set the creator to an actual user
      supplier.createdBy = creatorId;
      
      const existingSupplier = await suppliersCollection.findOne({ 
        name: supplier.name
      });

      if (existingSupplier) {
        logger.info(`Updating supplier: ${supplier.name}`);
        await suppliersCollection.updateOne(
          { _id: existingSupplier._id },
          { $set: { ...supplier, _id: existingSupplier._id } }
        );
      } else {
        logger.info(`Inserting new supplier: ${supplier.name}`);
        await suppliersCollection.insertOne(supplier);
      }
    }

    logger.info('Inventory data injection completed successfully');
  } catch (error) {
    logger.error('Error injecting inventory data:', error);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the injection function
injectInventoryData()
  .then(() => {
    logger.info('Inventory data injection script completed');
  })
  .catch((error) => {
    logger.error('Error in inventory data injection script:', error);
  });

module.exports = { inventoryItems, inventoryTransactions, suppliers };
