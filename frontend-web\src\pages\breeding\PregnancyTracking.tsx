import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { format, parseISO, addMonths } from 'date-fns';
import { useBreedingRecords } from '../../hooks/useBreedingRecords';
import { mockAnimals } from '../../mocks/animalData';
import { Box, Typography, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, CircularProgress, Alert, useTheme, alpha } from '@mui/material';
import { Add, Edit, CalendarToday, CheckCircle, Warning, Info } from '../../utils/iconImports';
import {  Mo<PERSON>leHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';

interface PregnancyRecord {
  id: string;
  animalId: string;
  animalName: string;
  breedingDate: Date;
  confirmedDate: Date;
  dueDate: Date;
  status: 'confirmed' | 'suspected' | 'at-risk';
  trimester: 1 | 2 | 3;
  vetName: string;
  lastCheckup: Date;
  nextCheckup: Date;
  notes: string;
  vitals: {
    temperature: number;
    heartRate: number;
    weight: number;
  };
}

const PregnancyTracking: React.FC = () => {
  const theme = useTheme();
  const { breedingRecords, loading, error } = useBreedingRecords();
  const [pregnancies, setPregnancies] = useState<PregnancyRecord[]>([]);

  useEffect(() => {
    if (breedingRecords && breedingRecords.length > 0) {
      // Convert breeding records to pregnancy records
      const confirmedPregnancies = breedingRecords
        .filter(record => record.status === 'confirmed')
        .map(record => {
          const femaleAnimal = mockAnimals.find(a => a.id === record.femaleId) || { name: `Animal ${record.femaleId}` };
          const breedingDate = parseISO(record.date);
          const confirmedDate = record.expectedDueDate ? parseISO(record.expectedDueDate) : addMonths(breedingDate, 1);
          const dueDate = addMonths(breedingDate, 9); // Approximate gestation period

          // Calculate trimester based on current date and due date
          const today = new Date();
          const totalDays = Math.floor((dueDate.getTime() - breedingDate.getTime()) / (1000 * 60 * 60 * 24));
          const daysPassed = Math.floor((today.getTime() - breedingDate.getTime()) / (1000 * 60 * 60 * 24));
          const progressPercentage = daysPassed / totalDays;

          let trimester: 1 | 2 | 3 = 1;
          if (progressPercentage > 0.66) {
            trimester = 3;
          } else if (progressPercentage > 0.33) {
            trimester = 2;
          }

          // Determine status based on follow-up dates and notes
          let status: 'confirmed' | 'suspected' | 'at-risk' = 'confirmed';
          if (record.notes && record.notes.toLowerCase().includes('risk')) {
            status = 'at-risk';
          }

          // Get the last follow-up date
          const followUpDates = record.followUpDates || [];
          const lastCheckup = followUpDates.length > 0
            ? parseISO(followUpDates[followUpDates.length - 1].date)
            : breedingDate;

          // Set next checkup date (2 weeks after last checkup)
          const nextCheckup = new Date(lastCheckup);
          nextCheckup.setDate(nextCheckup.getDate() + 14);

          return {
            id: `PREG-${record.id}`,
            animalId: record.femaleId,
            animalName: femaleAnimal.name,
            breedingDate,
            confirmedDate,
            dueDate,
            status,
            trimester,
            vetName: record.technician || 'Unknown',
            lastCheckup,
            nextCheckup,
            notes: record.notes || 'No notes available',
            vitals: {
              temperature: 38.5 + Math.random() * 0.5, // Simulate vitals
              heartRate: 70 + Math.floor(Math.random() * 15),
              weight: 600 + Math.floor(Math.random() * 100)
            }
          };
        });

      setPregnancies(confirmedPregnancies);
    }
  }, [breedingRecords]);

  const getStatusColor = (status: string) => {
    const colors = {
      'confirmed': 'bg-green-100 text-green-800',
      'suspected': 'bg-yellow-100 text-yellow-800',
      'at-risk': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors];
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">Error loading pregnancy data. Please try again later.</Alert>
      </Box>
    );
  }


  return (
    <Box>
      <ModuleHeader
        title="Pregnancy Tracking"
        subtitle="Monitor and manage pregnancy records for your breeding livestock"
        module="breeding"
        submodule="pregnancy"
        actionLabel="Add New Record"
        actionIcon={<Add />}
        onAction={() => console.log('Add new pregnancy record')}
      />

      <Box sx={{ px: 3, pb: 5 }}>

        {/* Summary Cards */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Pregnancy Overview"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<Info />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {pregnancies.length}
                      </Typography>
                      <Typography variant="body2">Total Pregnancies</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="error.main">
                        {pregnancies.filter(p => p.status === 'at-risk').length}
                      </Typography>
                      <Typography variant="body2">At Risk</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Due This Month
                  </Typography>
                  <Typography variant="h4" fontWeight="bold" color="success.main">
                    {pregnancies.filter(p => p.dueDate.getMonth() === new Date().getMonth()).length}
                  </Typography>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Upcoming Checkups"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<CalendarToday />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Today's Checkups
                </Typography>
                <Typography variant="h4" fontWeight="bold" color="primary.main" mb={2}>
                  {pregnancies.filter(p => format(p.nextCheckup, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')).length}
                </Typography>

                {pregnancies
                  .filter(p => p.nextCheckup >= new Date())
                  .sort((a, b) => a.nextCheckup.getTime() - b.nextCheckup.getTime())
                  .slice(0, 3)
                  .map(pregnancy => (
                    <Box key={pregnancy.id} mb={2} p={1.5} bgcolor="rgba(0,0,0,0.03)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {pregnancy.animalName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          {format(pregnancy.nextCheckup, 'MMM dd, yyyy')}
                        </Typography>
                        <Chip
                          label={pregnancy.trimester === 3 ? "3rd Trimester" :
                                 pregnancy.trimester === 2 ? "2nd Trimester" : "1st Trimester"}
                          size="small"
                          color={pregnancy.trimester === 3 ? "error" :
                                 pregnancy.trimester === 2 ? "warning" : "success"}
                        />
                      </Box>
                    </Box>
                  ))
                }
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="At-Risk Pregnancies"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<Warning />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2}>
                {pregnancies
                  .filter(p => p.status === 'at-risk')
                  .map(pregnancy => (
                    <Box key={pregnancy.id} mb={2} p={1.5} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        {pregnancy.animalName}
                      </Typography>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="body2" color="text.secondary">
                          Due: {format(pregnancy.dueDate, 'MMM dd, yyyy')}
                        </Typography>
                        <CustomButton
                          size="small"
                          variant="outlined"
                          color="error"
                        >
                          View Details
                        </CustomButton>
                      </Box>
                    </Box>
                  ))
                }
                {pregnancies.filter(p => p.status === 'at-risk').length === 0 && (
                  <Box display="flex" alignItems="center" justifyContent="center" height={100}>
                    <Typography variant="body2" color="text.secondary">No at-risk pregnancies</Typography>
                  </Box>
                )}
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Pregnancy Records Table */}
        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent sx={{ p: 0 }}>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white' }}>Animal</TableCell>
                    <TableCell sx={{ color: 'white' }}>Status</TableCell>
                    <TableCell sx={{ color: 'white' }}>Trimester</TableCell>
                    <TableCell sx={{ color: 'white' }}>Due Date</TableCell>
                    <TableCell sx={{ color: 'white' }}>Next Checkup</TableCell>
                    <TableCell sx={{ color: 'white' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
            <TableBody>
              {pregnancies.length > 0 ? (
                pregnancies.map(pregnancy => (
                  <TableRow key={pregnancy.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body1">{pregnancy.animalName}</Typography>
                        <Typography variant="body2" color="text.secondary">ID: {pregnancy.animalId}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={pregnancy.status}
                        color={
                          pregnancy.status === 'confirmed' ? 'success' :
                          pregnancy.status === 'at-risk' ? 'error' : 'warning'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{pregnancy.trimester}</TableCell>
                    <TableCell>{format(pregnancy.dueDate, 'MMM dd, yyyy')}</TableCell>
                    <TableCell>{format(pregnancy.nextCheckup, 'MMM dd, yyyy')}</TableCell>
                    <TableCell>
                      <Box display="flex">
                        <CustomButton size="small" variant="outlined" color="primary" sx={{ mr: 1 }}>View</CustomButton>
                        <CustomButton size="small" variant="outlined" color="secondary">Edit</CustomButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body1" py={3}>No pregnancy records found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default PregnancyTracking;

