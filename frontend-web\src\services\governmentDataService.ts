/**
 * Government Data Integration Service
 * 
 * Provides access to South African government livestock data APIs
 */

interface AuctionPrice {
  date: string;
  location: string;
  animalType: 'cattle' | 'sheep' | 'goats' | 'pigs';
  grade: string;
  pricePerKg: number;
  averageWeight: number;
  totalPrice: number;
  volume: number;
}

interface VaccinationSchedule {
  id: string;
  province: string;
  animalType: string;
  vaccineName: string;
  scheduledDate: string;
  location: string;
  veterinarian: string;
  contactNumber: string;
  cost: number;
  mandatory: boolean;
}

interface VeterinaryService {
  id: string;
  name: string;
  province: string;
  district: string;
  address: string;
  contactNumber: string;
  email: string;
  services: string[];
  emergencyAvailable: boolean;
  rating: number;
  distance?: number;
}

interface WeatherData {
  location: string;
  province: string;
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    conditions: string;
    uvIndex: number;
  };
  forecast: Array<{
    date: string;
    high: number;
    low: number;
    conditions: string;
    precipitation: number;
    windSpeed: number;
  }>;
  livestockAdvisory: {
    heatStress: 'low' | 'moderate' | 'high';
    grazingConditions: 'excellent' | 'good' | 'fair' | 'poor';
    waterRequirements: 'normal' | 'increased' | 'critical';
    recommendations: string[];
  };
}

class GovernmentDataService {
  private static instance: GovernmentDataService;
  private baseUrl = process.env.REACT_APP_GOV_API_URL || 'https://api.gov.za/livestock';
  private apiKey = process.env.REACT_APP_GOV_API_KEY || 'demo_key';

  static getInstance(): GovernmentDataService {
    if (!GovernmentDataService.instance) {
      GovernmentDataService.instance = new GovernmentDataService();
    }
    return GovernmentDataService.instance;
  }

  /**
   * Get real-time livestock auction prices
   */
  async getAuctionPrices(province?: string, animalType?: string): Promise<AuctionPrice[]> {
    try {
      // Mock data for demo - replace with actual API call
      const mockData: AuctionPrice[] = [
        {
          date: new Date().toISOString().split('T')[0],
          location: 'Johannesburg Livestock Market',
          animalType: 'cattle',
          grade: 'A',
          pricePerKg: 45.50,
          averageWeight: 450,
          totalPrice: 20475,
          volume: 150
        },
        {
          date: new Date().toISOString().split('T')[0],
          location: 'Cape Town Auction',
          animalType: 'sheep',
          grade: 'A',
          pricePerKg: 52.00,
          averageWeight: 45,
          totalPrice: 2340,
          volume: 200
        },
        {
          date: new Date().toISOString().split('T')[0],
          location: 'Durban Livestock Exchange',
          animalType: 'goats',
          grade: 'B',
          pricePerKg: 48.75,
          averageWeight: 35,
          totalPrice: 1706,
          volume: 80
        }
      ];

      // Filter by province and animal type if specified
      let filteredData = mockData;
      if (animalType) {
        filteredData = filteredData.filter(item => item.animalType === animalType);
      }

      return filteredData;
    } catch (error) {
      console.error('Error fetching auction prices:', error);
      return [];
    }
  }

  /**
   * Get vaccination schedules for specific province
   */
  async getVaccinationSchedule(province: string): Promise<VaccinationSchedule[]> {
    try {
      // Mock data for demo - replace with actual API call
      const mockSchedules: VaccinationSchedule[] = [
        {
          id: '1',
          province,
          animalType: 'cattle',
          vaccineName: 'Foot and Mouth Disease',
          scheduledDate: '2024-02-15',
          location: 'Provincial Veterinary Clinic',
          veterinarian: 'Dr. Sarah Johnson',
          contactNumber: '+27 11 123 4567',
          cost: 45.00,
          mandatory: true
        },
        {
          id: '2',
          province,
          animalType: 'sheep',
          vaccineName: 'Blue Tongue',
          scheduledDate: '2024-02-20',
          location: 'Mobile Vaccination Unit',
          veterinarian: 'Dr. Michael Smith',
          contactNumber: '+27 11 234 5678',
          cost: 25.00,
          mandatory: false
        },
        {
          id: '3',
          province,
          animalType: 'cattle',
          vaccineName: 'Anthrax',
          scheduledDate: '2024-03-01',
          location: 'District Veterinary Office',
          veterinarian: 'Dr. Lisa Brown',
          contactNumber: '+27 11 345 6789',
          cost: 35.00,
          mandatory: true
        }
      ];

      return mockSchedules;
    } catch (error) {
      console.error('Error fetching vaccination schedule:', error);
      return [];
    }
  }

  /**
   * Get veterinary services directory
   */
  async getVeterinaryServices(province: string, district?: string): Promise<VeterinaryService[]> {
    try {
      // Mock data for demo - replace with actual API call
      const mockServices: VeterinaryService[] = [
        {
          id: '1',
          name: 'Provincial Veterinary Services',
          province,
          district: 'Central',
          address: '123 Main Street, City Center',
          contactNumber: '+27 11 123 4567',
          email: '<EMAIL>',
          services: ['Vaccinations', 'Emergency Care', 'Disease Testing', 'Artificial Insemination'],
          emergencyAvailable: true,
          rating: 4.5
        },
        {
          id: '2',
          name: 'Rural Livestock Clinic',
          province,
          district: 'Rural North',
          address: '456 Farm Road, Rural Area',
          contactNumber: '+27 11 234 5678',
          email: '<EMAIL>',
          services: ['Mobile Services', 'Vaccinations', 'Health Certificates'],
          emergencyAvailable: false,
          rating: 4.2
        },
        {
          id: '3',
          name: 'Emergency Veterinary Response',
          province,
          district: 'All Districts',
          address: '24/7 Mobile Service',
          contactNumber: '+27 11 911 VETS',
          email: '<EMAIL>',
          services: ['Emergency Response', 'Disease Outbreak Control', 'Quarantine Services'],
          emergencyAvailable: true,
          rating: 4.8
        }
      ];

      let filteredServices = mockServices;
      if (district) {
        filteredServices = filteredServices.filter(service => 
          service.district === district || service.district === 'All Districts'
        );
      }

      return filteredServices;
    } catch (error) {
      console.error('Error fetching veterinary services:', error);
      return [];
    }
  }

  /**
   * Get weather data with livestock advisory
   */
  async getWeatherData(province: string, location?: string): Promise<WeatherData> {
    try {
      // Mock data for demo - replace with actual API call
      const mockWeatherData: WeatherData = {
        location: location || `${province} Province`,
        province,
        current: {
          temperature: 28,
          humidity: 65,
          windSpeed: 12,
          conditions: 'Partly Cloudy',
          uvIndex: 7
        },
        forecast: [
          {
            date: new Date().toISOString().split('T')[0],
            high: 30,
            low: 18,
            conditions: 'Sunny',
            precipitation: 0,
            windSpeed: 10
          },
          {
            date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
            high: 32,
            low: 20,
            conditions: 'Partly Cloudy',
            precipitation: 10,
            windSpeed: 15
          },
          {
            date: new Date(Date.now() + 172800000).toISOString().split('T')[0],
            high: 29,
            low: 17,
            conditions: 'Thunderstorms',
            precipitation: 80,
            windSpeed: 20
          }
        ],
        livestockAdvisory: {
          heatStress: 'moderate',
          grazingConditions: 'good',
          waterRequirements: 'normal',
          recommendations: [
            'Provide adequate shade during peak hours (11 AM - 3 PM)',
            'Ensure fresh water is available at all times',
            'Monitor animals for signs of heat stress',
            'Consider adjusting feeding times to cooler periods'
          ]
        }
      };

      return mockWeatherData;
    } catch (error) {
      console.error('Error fetching weather data:', error);
      throw error;
    }
  }

  /**
   * Get agricultural extension services
   */
  async getExtensionServices(province: string) {
    try {
      // Mock data for demo
      const mockExtensionServices = [
        {
          id: '1',
          name: 'Provincial Agricultural Development',
          province,
          services: ['Training Programs', 'Technical Support', 'Funding Assistance'],
          contactNumber: '+27 11 456 7890',
          email: '<EMAIL>',
          programs: [
            'Livestock Management Training',
            'Pasture Management Workshop',
            'Financial Planning for Farmers',
            'Disease Prevention Seminar'
          ]
        },
        {
          id: '2',
          name: 'Rural Development Agency',
          province,
          services: ['Infrastructure Development', 'Market Access', 'Cooperative Formation'],
          contactNumber: '+27 11 567 8901',
          email: '<EMAIL>',
          programs: [
            'Water Infrastructure Projects',
            'Market Linkage Programs',
            'Farmer Cooperative Support',
            'Technology Adoption Assistance'
          ]
        }
      ];

      return mockExtensionServices;
    } catch (error) {
      console.error('Error fetching extension services:', error);
      return [];
    }
  }

  /**
   * Get market trends and analysis
   */
  async getMarketTrends(animalType: string, timeframe: '1month' | '3months' | '6months' | '1year' = '3months') {
    try {
      // Mock market trend data
      const mockTrends = {
        animalType,
        timeframe,
        currentPrice: 45.50,
        priceChange: 2.3,
        priceChangePercent: 5.3,
        trend: 'upward' as const,
        forecast: {
          nextMonth: 47.20,
          confidence: 0.75
        },
        factors: [
          'Increased export demand',
          'Seasonal breeding patterns',
          'Feed cost fluctuations',
          'Weather conditions impact'
        ],
        recommendations: [
          'Consider selling in next 2-3 weeks for optimal prices',
          'Monitor feed costs for profitability analysis',
          'Prepare for seasonal demand increase'
        ]
      };

      return mockTrends;
    } catch (error) {
      console.error('Error fetching market trends:', error);
      throw error;
    }
  }
}

export default GovernmentDataService.getInstance();
