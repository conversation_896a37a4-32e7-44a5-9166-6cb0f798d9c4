/**
 * Test Configuration
 * Provides secure test credentials and configuration
 */

module.exports = {
  testUsers: {
    admin: {
      username: 'test_admin',
      password: process.env.TEST_ADMIN_PASSWORD || 'TestAdmin@123',
      role: 'admin'
    },
    demo: {
      username: 'test_demo',
      password: process.env.TEST_DEMO_PASSWORD || 'TestDemo@123',
      role: 'demo'
    },
    beta: {
      username: 'test_beta',
      password: process.env.TEST_BETA_PASSWORD || 'TestBeta@123',
      role: 'beta'
    }
  },
  
  testDatabase: {
    uri: process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/ampd_livestock_test',
    name: process.env.TEST_DB_NAME || 'ampd_livestock_test'
  },
  
  testJWT: {
    secret: process.env.TEST_JWT_SECRET || 'd608cab403bd97a67d665be7ff6ee765132aed67aeb58b66685beb97538866a4',
    expiresIn: '1h'
  }
};