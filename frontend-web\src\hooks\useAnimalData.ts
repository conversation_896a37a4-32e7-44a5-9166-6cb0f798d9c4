import { useState, useEffect } from 'react';
import { useDataFetching } from './useDataFetching';
import { mockAnimals, mockAnimalStats } from '../mocks';
import { useMockData } from '../utils/config';
import { fetchData } from '../services/apiService';

import { Animal as TypesAnimal } from '../types/animal';

export interface Animal extends TypesAnimal {
  rfidTag?: string;
  imageUrl?: string;
}

export interface AnimalStats {
  totalAnimals: number;
  healthPercentage: number;
  pendingCheckups: number;
  activeAnimals: number;
  recentAdditions: number;
  averageGrowthRate?: number;
  bySpecies: Record<string, number>;
  byStatus: Record<string, number>;
  byLocation: Record<string, number>;
  byHealth: {
    healthy: number;
    sick: number;
    injured: number;
    pregnant: number;
  };
  // Asset Management stats
  retiredAnimals?: number;
  nearingRetirement?: number;
  retirementByReason?: {
    age: number;
    breeding: number;
    health: number;
    other: number;
  };
  valueOfActiveAssets?: number;
  valueOfRetiredAssets?: number;
  totalAssetValue?: number;
  averageRoi?: number;
}

// API functions
const fetchAnimals = async (): Promise<Animal[]> => {
  try {
    return await fetchData('/animals');
  } catch (error) {
    console.error('Error fetching animals:', error);
    // Return mock data as fallback
    return mockAnimals;
  }
};

const fetchAnimalStats = async (): Promise<AnimalStats> => {
  try {
    return await fetchData('/animals/stats');
  } catch (error) {
    console.error('Error fetching animal stats:', error);
    // Return mock data as fallback
    return mockAnimalStats;
  }
};

export const useAnimalData = () => {
  // Use state to ensure animals is always an array
  const [animalsData, setAnimalsData] = useState<Animal[]>([]);

  const {
    data: fetchedAnimals,
    loading: animalsLoading,
    error: animalsError,
    refetch: refetchAnimals
  } = useDataFetching<Animal[]>(fetchAnimals, []);

  // Update animalsData whenever fetchedAnimals changes
  useEffect(() => {
    // Ensure animals is always an array
    if (Array.isArray(fetchedAnimals)) {
      setAnimalsData(fetchedAnimals);
    } else {
      console.warn('Animals data is not an array, using empty array instead');
      setAnimalsData([]);
    }
  }, [fetchedAnimals]);

  const {
    data: stats,
    loading: statsLoading,
    error: statsError,
    refetch: refetchStats
  } = useDataFetching<AnimalStats>(fetchAnimalStats, {
    totalAnimals: 0,
    healthPercentage: 0,
    pendingCheckups: 0,
    activeAnimals: 0,
    recentAdditions: 0,
    bySpecies: {},
    byStatus: {},
    byLocation: {},
    byHealth: {
      healthy: 0,
      sick: 0,
      injured: 0,
      pregnant: 0
    },
    // Asset Management stats
    retiredAnimals: 5,
    nearingRetirement: 3,
    retirementByReason: {
      age: 2,
      breeding: 2,
      health: 1,
      other: 0
    },
    valueOfActiveAssets: 850000,
    valueOfRetiredAssets: 150000,
    totalAssetValue: 1000000,
    averageRoi: 15
  });

  const loading = animalsLoading || statsLoading;
  const error = animalsError || statsError;

  const refetch = async () => {
    await Promise.all([refetchAnimals(), refetchStats()]);
  };

  return { animals: animalsData, stats, loading, error, refetch };
};


