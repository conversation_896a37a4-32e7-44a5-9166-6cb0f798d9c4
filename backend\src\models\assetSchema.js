// Asset Management Schema
const assetSchema = {
  // Animal Asset Schema
  animalAsset: {
    animalId: { type: String, required: true }, // Reference to animal ID
    tagNumber: { type: String, required: true }, // Animal tag number for easy reference
    name: { type: String, required: true }, // Animal name
    type: { type: String, required: true }, // Animal type (Cattle, Sheep, etc.)
    status: { type: String, required: true, default: 'Active' }, // Active, Retired, Sold, Deceased
    acquisitionDate: { type: String }, // ISO date format - when the animal was acquired
    acquisitionCost: { type: Number }, // Cost in ZAR
    currentValue: { type: Number }, // Current estimated value in ZAR
    depreciationRate: { type: Number }, // Annual depreciation rate (%)
    retirementDate: { type: String }, // ISO date format - when the animal was retired
    retirementReason: { type: String }, // Age, Breeding Limit, Health Issues, Performance, Other
    retirementNotes: { type: String }, // Additional notes about retirement
    breedingCount: { type: Number, default: 0 }, // Number of times the animal has been bred
    lastBreedingDate: { type: String }, // ISO date format - last breeding date
    productivityMetrics: { type: Object }, // Custom metrics based on animal type
    maintenanceCosts: { type: Number, default: 0 }, // Total maintenance costs in ZAR
    revenueGenerated: { type: Number, default: 0 }, // Total revenue generated in ZAR
    roi: { type: Number }, // Return on investment (%)
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  },
  
  // Equipment Asset Schema
  equipmentAsset: {
    name: { type: String, required: true }, // Equipment name
    category: { type: String, required: true }, // Equipment category
    manufacturer: { type: String }, // Manufacturer name
    model: { type: String }, // Model number
    serialNumber: { type: String }, // Serial number
    status: { type: String, required: true, default: 'Operational' }, // Operational, Maintenance, Repair, Retired
    purchaseDate: { type: String }, // ISO date format
    purchasePrice: { type: Number }, // Cost in ZAR
    currentValue: { type: Number }, // Current estimated value in ZAR
    depreciationRate: { type: Number }, // Annual depreciation rate (%)
    location: { type: String }, // Current location
    assignedTo: { type: String }, // Person responsible for the equipment
    lastMaintenanceDate: { type: String }, // ISO date format
    nextMaintenanceDate: { type: String }, // ISO date format
    maintenanceHistory: { type: Array }, // Array of maintenance records
    retirementDate: { type: String }, // ISO date format - when the equipment was retired
    retirementReason: { type: String }, // Age, Obsolete, Damaged, Other
    retirementNotes: { type: String }, // Additional notes about retirement
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  },
  
  // Asset Depreciation Record Schema
  depreciationRecord: {
    assetId: { type: String, required: true }, // Reference to asset ID
    assetType: { type: String, required: true }, // Animal or Equipment
    year: { type: Number, required: true }, // Year of depreciation
    startValue: { type: Number, required: true }, // Value at start of year
    depreciationAmount: { type: Number, required: true }, // Amount depreciated
    endValue: { type: Number, required: true }, // Value at end of year
    notes: { type: String }, // Additional notes
    createdAt: { type: Date, default: Date.now }
  },
  
  // Asset Transfer Record Schema
  transferRecord: {
    assetId: { type: String, required: true }, // Reference to asset ID
    assetType: { type: String, required: true }, // Animal or Equipment
    transferDate: { type: String, required: true }, // ISO date format
    fromLocation: { type: String, required: true }, // Previous location
    toLocation: { type: String, required: true }, // New location
    transferReason: { type: String }, // Reason for transfer
    transferredBy: { type: String }, // Person who performed the transfer
    notes: { type: String }, // Additional notes
    createdAt: { type: Date, default: Date.now }
  }
};

module.exports = assetSchema;
