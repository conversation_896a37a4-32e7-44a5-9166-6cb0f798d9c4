import React from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Card, CardContent, useTheme, alpha, Chip } from '@mui/material';
import { motion } from 'framer-motion';

interface ModuleHeaderCardProps {
  title: string;
  subtitle?: string;
  module: string;
  actionLabel?: string;
  actionIcon?: React.ReactNode;
  onAction?: () => void;
  stats?: Array<{ label: string; value: string | number; color?: string }>;
  icon?: React.ReactNode;
  tags?: Array<{ label: string; color?: string }>;
  children?: React.ReactNode;
}

/**
 * A high-quality header card component for module dashboards
 * Uses a single background image with consistent styling
 */
const ModuleHeaderCard: React.FC<ModuleHeaderCardProps> = ({
  title,
  subtitle,
  module,
  actionLabel,
  actionIcon,
  onAction,
  stats,
  icon,
  tags,
  children
}) => {
  const theme = useTheme();

  // Get the appropriate background image based on module
  const getModuleImage = (moduleName: string): string => {
    const moduleImageMap: Record<string, string> = {
      animals: '/images/modules/animals/cattle-1.jpeg',
      health: '/images/modules/health/health-main.png',
      breeding: '/images/modules/breeding/breeding-main.png',
      feeding: '/images/modules/feeding/feed-main.jpeg',
      feed: '/images/modules/feeding/feed-main.jpeg',
      commercial: '/images/modules/commercial/commercial-main.webp',
      financial: '/images/modules/commercial/commercial-1.jpeg',
      compliance: '/images/modules/commercial/commercial-2.jpeg',
      rfid: '/images/modules/rfid/rfid-1.webp',
      reports: '/images/modules/commercial/commercial-1.jpeg',
      settings: '/images/dashboard/main-dashboard.jpg',
      resources: '/images/modules/health/veterinary-1.jpg',
    };

    return moduleImageMap[moduleName.toLowerCase()] || '/images/dashboard/main-dashboard.jpg';
  };

  const backgroundImage = getModuleImage(module);

  // Check if this is an excluded module (dashboard, login, commercial)
  const isExcludedModule = ['dashboard', 'login', 'commercial'].includes(module.toLowerCase());

  // Use metallic blue for all modules except excluded ones
  const primaryColor = isExcludedModule ? theme.palette.primary.main : '#4A6FA5';
  const secondaryColor = isExcludedModule ? theme.palette.primary.dark : '#3A5A8C';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card
        sx={{
          borderRadius: '16px',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          mb: 4,
          position: 'relative',
          height: stats ? 300 : 200
        }}
      >
        {/* Background Image with Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `url(${backgroundImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `linear-gradient(135deg, ${alpha(primaryColor, 0.85)}, ${alpha(secondaryColor, 0.9)})`,
              backdropFilter: 'blur(2px)',
              zIndex: 1
            }
          }}
        />

        {/* Content */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            p: 4,
            color: 'white'
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="flex-start">
            <Box>
              <Box display="flex" alignItems="center" mb={1}>
                {icon && (
                  <Box
                    sx={{
                      mr: 2,
                      p: 1.5,
                      borderRadius: '12px',
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    {icon}
                  </Box>
                )}
                <Box>
                  <Typography
                    variant="h4"
                    fontWeight="bold"
                    sx={{
                      fontSize: { xs: '1.75rem', sm: '2rem', md: '2.25rem' },
                      letterSpacing: '-0.5px',
                      textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                    }}
                  >
                    {title}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="h6"
                      sx={{
                        opacity: 0.9,
                        fontWeight: 400,
                        fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                        mt: 0.5,
                        maxWidth: '80%',
                        textShadow: '0 1px 2px rgba(0,0,0,0.1)'
                      }}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </Box>
              </Box>

              {/* Tags */}
              {tags && tags.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
                  {tags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag.label}
                      size="small"
                      sx={{
                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                        color: 'white',
                        backdropFilter: 'blur(4px)',
                        fontWeight: 500,
                        '& .MuiChip-label': { px: 1.5 }
                      }}
                    />
                  ))}
                </Box>
              )}
            </Box>

            {/* Action Button */}
            {actionLabel && onAction && (
              <CustomButton
                variant="contained"
                startIcon={actionIcon}
                onClick={onAction}
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  backdropFilter: 'blur(4px)',
                  color: 'white',
                  borderRadius: '8px',
                  px: 2,
                  py: 1,
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  }
                }}
              >
                {actionLabel}
              </CustomButton>
            )}
          </Box>

          {/* Stats Grid */}
          {stats && stats.length > 0 && (
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: `repeat(${Math.min(stats.length, 4)}, 1fr)` },
                gap: 3,
                mt: 'auto',
                pt: 4
              }}
            >
              {stats.map((stat, index) => (
                <Box
                  key={index}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.15)',
                    backdropFilter: 'blur(4px)',
                    borderRadius: '12px',
                    p: 2,
                    transition: 'transform 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    }
                  }}
                >
                  <Typography variant="body2" sx={{ opacity: 0.9, fontWeight: 500 }}>
                    {stat.label}
                  </Typography>
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 'bold',
                      mt: 1,
                      color: stat.color ? stat.color : 'white'
                    }}
                  >
                    {stat.value}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}

          {/* Additional Content */}
          {children && (
            <Box sx={{ mt: 3 }}>
              {children}
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ModuleHeaderCard;
