/**
 * Utility to fix MUI Button component issues
 * This fixes the "Cannot read properties of undefined (reading 'dark')" error
 */

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    __MUI_BUTTON_FIX_APPLIED__?: boolean;
    __MUI_BUTTON_FIX_INTERVAL__?: number;
  }
}

/**
 * Apply fixes to MUI Button components
 * This function patches the MUI Button component to prevent the "Cannot read properties of undefined (reading 'dark')" error
 */
export const applyMuiButtonFix = (): void => {
  try {
    // Find all MUI Button components in the DOM
    const buttonElements = document.querySelectorAll('.MuiButton-root');

    // Apply fixes to each button
    buttonElements.forEach(button => {
      // Add inline styles to override the problematic styles
      (button as HTMLElement).style.setProperty('color', '#3AA99F', 'important');
      (button as HTMLElement).style.setProperty('background-color', 'transparent', 'important');

      // Add classes to override the problematic styles
      button.classList.add('mui-button-fixed');

      // For contained buttons
      if (button.classList.contains('MuiButton-contained')) {
        (button as HTMLElement).style.setProperty('background-color', '#3AA99F', 'important');
        (button as HTMLElement).style.setProperty('color', '#ffffff', 'important');
      }

      // For outlined buttons
      if (button.classList.contains('MuiButton-outlined')) {
        (button as HTMLElement).style.setProperty('border', '1px solid #3AA99F', 'important');
      }

      // For primary buttons
      if (button.classList.contains('MuiButton-containedPrimary')) {
        (button as HTMLElement).style.setProperty('background-color', '#3AA99F', 'important');
        (button as HTMLElement).style.setProperty('color', '#ffffff', 'important');
      }

      // For secondary buttons
      if (button.classList.contains('MuiButton-containedSecondary')) {
        (button as HTMLElement).style.setProperty('background-color', '#38B2AC', 'important');
        (button as HTMLElement).style.setProperty('color', '#ffffff', 'important');
      }

      // For text buttons
      if (button.classList.contains('MuiButton-text')) {
        (button as HTMLElement).style.setProperty('background-color', 'transparent', 'important');
      }

      // For disabled buttons
      if (button.classList.contains('Mui-disabled')) {
        (button as HTMLElement).style.setProperty('opacity', '0.5', 'important');
        (button as HTMLElement).style.setProperty('pointer-events', 'none', 'important');
      }
    });
  } catch (error) {
    console.error('Error applying MUI Button fix:', error);
  }
};

/**
 * Apply fixes to MUI Button components periodically
 * This function applies the fixes every 500ms to ensure all buttons are fixed
 */
export const applyMuiButtonFixPeriodically = (): void => {
  // Check if already applied
  if (window.__MUI_BUTTON_FIX_APPLIED__) {
    console.log('MUI Button fix already applied periodically');
    return;
  }

  // Set flag to prevent multiple applications
  window.__MUI_BUTTON_FIX_APPLIED__ = true;

  // Apply fixes immediately
  applyMuiButtonFix();

  // Clear any existing interval
  if (window.__MUI_BUTTON_FIX_INTERVAL__) {
    clearInterval(window.__MUI_BUTTON_FIX_INTERVAL__);
  }

  // Apply fixes every 500ms
  window.__MUI_BUTTON_FIX_INTERVAL__ = window.setInterval(() => {
    applyMuiButtonFix();
  }, 500) as unknown as number;
};

export default {
  applyMuiButtonFix,
  applyMuiButtonFixPeriodically
};
