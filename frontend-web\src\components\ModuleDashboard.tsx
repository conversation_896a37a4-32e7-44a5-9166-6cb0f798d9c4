import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useThemeContext } from '../contexts/ThemeContext';

export interface ModuleMetric {
  label: string;
  value: string | number;
  trend?: string;
  status: 'positive' | 'negative' | 'neutral' | 'warning';
}

export interface QuickAction {
  name: string;
  icon: string;
  path: string;
}

export interface ModuleDashboardProps {
  title: string;
  description: string;
  metrics: ModuleMetric[];
  quickActions: QuickAction[];
  subModules?: any[]; // Add this if you need submodules
  stats?: Record<string, any>; // Add this if you need stats
}

const ModuleDashboard: React.FC<ModuleDashboardProps> = ({
  title,
  description,
  metrics,
  quickActions,
  subModules,
  stats
}) => {
  const navigate = useNavigate();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold">{title}</h1>
        <p className="text-gray-600">{description}</p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <div key={metric.label} className="bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-600">{metric.label}</div>
            <div className="text-xl font-bold mt-1">{metric.value}</div>
            {metric.trend && (
              <div className="text-sm mt-1" style={{
                color: metric.status === 'positive' ? themeColor.primary :
                       metric.status === 'negative' ? '#ef4444' :
                       '#6b7280'
              }}>
                {metric.trend}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
        {quickActions.map((action) => (
          <button
            key={action.name}
            onClick={() => navigate(action.path)}
            className="p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow"
            style={{
              // Apply hover styles via className instead
            }}
          >
            <span className="text-2xl" style={{ color: themeColor.primary }}>{action.icon}</span>
            <div className="text-sm mt-2">{action.name}</div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModuleDashboard;
