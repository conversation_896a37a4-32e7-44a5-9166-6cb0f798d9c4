import React, { ReactNode } from 'react';
import { Box, Grid, Typography, Paper, useTheme, alpha, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import { Refresh, ArrowForward } from '../../utils/iconImports';
import { useLanguage } from '../../contexts/LanguageContext';
import { LoadingOverlay, ErrorBoundary } from '.';
import { getSafeElevation, getSafeAnimation } from '../../utils/themeDefaults';
import ModernDashboard, { DashboardStat, DashboardAction, DashboardTab } from './ModernDashboard';
import UnifiedModuleContainer from './UnifiedModuleContainer';

export interface SubModuleMetric {
  label: string;
  value: string | number;
  icon?: ReactNode;
  color?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    label: string;
  };
}

export interface SubModuleAction {
  label: string;
  icon: ReactNode;
  onClick: () => void;
  color?: string;
  disabled?: boolean;
}

export interface SubModuleChart {
  title: string;
  type: 'bar' | 'line' | 'pie' | 'area';
  data: any;
  height?: number;
}

export interface SubModuleDashboardProps {
  title: string;
  subtitle?: string;
  parentModule: string;
  subModule: string;
  icon?: ReactNode;
  metrics?: SubModuleMetric[];
  actions?: SubModuleAction[];
  charts?: SubModuleChart[];
  tables?: ReactNode;
  tabs?: DashboardTab[];
  activeTab?: number;
  onTabChange?: (index: number) => void;
  isLoading?: boolean;
  loadingMessage?: string;
  onRefresh?: () => void;
  children?: ReactNode;
  onBack?: () => void;
}

/**
 * A standardized dashboard component for submodules that provides consistent styling and functionality
 */
const SubModuleDashboard: React.FC<SubModuleDashboardProps> = ({
  title,
  subtitle,
  parentModule,
  subModule,
  icon,
  metrics,
  actions,
  charts,
  tables,
  tabs,
  activeTab = 0,
  onTabChange,
  isLoading = false,
  loadingMessage,
  onRefresh,
  children,
  onBack
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Convert metrics to DashboardStat format
  const dashboardStats: DashboardStat[] = metrics?.map(metric => ({
    label: metric.label,
    value: metric.value,
    icon: metric.icon,
    color: metric.color,
    trend: metric.trend
  })) || [];

  // Convert actions to DashboardAction format
  const dashboardActions: DashboardAction[] = actions?.map(action => ({
    label: action.label,
    icon: action.icon,
    onClick: action.onClick,
    color: action.color,
    disabled: action.disabled
  })) || [];

  // Add back action if provided
  if (onBack) {
    dashboardActions.unshift({
      label: translate('common.back'),
      icon: <ArrowForward style={{ transform: 'rotate(180deg)' }} />,
      onClick: onBack,
      color: 'secondary'
    });
  }

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  // Get module color based on parent module
  const getModuleColor = () => {
    switch (parentModule) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feed':
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      case 'commercial':
        return theme.palette.secondary.dark;
      case 'compliance':
        return theme.palette.error.dark;
      case 'resources':
        return theme.palette.success.dark;
      default:
        return theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  return (
    <UnifiedModuleContainer module={parentModule}>
      <ModernDashboard
        title={title}
        subtitle={subtitle}
        icon={icon}
        stats={dashboardStats}
        actions={dashboardActions}
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={onTabChange}
        isLoading={isLoading}
        loadingMessage={loadingMessage}
        onRefresh={onRefresh}
        module={parentModule}
      >
        {/* Charts Section */}
        {charts && charts.length > 0 && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {charts.map((chart, index) => (
                <Grid item xs={12} md={chart.type === 'pie' ? 4 : 6} lg={chart.type === 'pie' ? 4 : 6} key={index}>
                  <motion.div variants={itemVariants}>
                    <Paper
                      elevation={getSafeElevation(theme, 1)}
                      sx={{
                        p: 3,
                        height: chart.height || 300,
                        borderRadius: 2,
                        overflow: 'hidden',
                      background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${alpha(moduleColor, 0.1)}`,
                      '&:hover': {
                        boxShadow: `0 8px 25px ${alpha(moduleColor, 0.15)}`,
                        transform: 'translateY(-4px)',
                      },
                      transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
                    }}
                  >
                    <Typography variant="h6" gutterBottom fontWeight="bold" color={moduleColor}>
                      {chart.title}
                    </Typography>
                    <Box sx={{ height: 'calc(100% - 30px)', width: '100%' }}>
                      {/* Chart content would be rendered here based on chart.type and chart.data */}
                      {/* This is a placeholder for actual chart implementation */}
                      <Box
                        sx={{
                          height: '100%',
                          width: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: `1px dashed ${alpha(moduleColor, 0.3)}`,
                          borderRadius: 1
                        }}
                      >
                        <Typography variant="body2" color="text.secondary">
                          {chart.type.toUpperCase()} Chart: {chart.title}
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </motion.div>
      )}

      {/* Tables Section */}
      {tables && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          style={{ marginBottom: theme.spacing(4) }}
        >
          <motion.div variants={itemVariants}>
            <Paper
              elevation={getSafeElevation(theme, 1)}
              sx={{
                p: 3,
                borderRadius: 2,
                overflow: 'hidden',
                background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(moduleColor, 0.1)}`,
                '&:hover': {
                  boxShadow: `0 8px 25px ${alpha(moduleColor, 0.15)}`,
                },
                transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
              }}
            >
              {tables}
            </Paper>
          </motion.div>
        </motion.div>
      )}

      {/* Additional Content */}
      {children && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {children}
        </motion.div>
      )}
      </ModernDashboard>
    </UnifiedModuleContainer>
  );
};

export default SubModuleDashboard;
