/**
 * reports Controller
 * 
 * Controller for reports module
 */

const { getCollection, getModel } = require('../config/mongodb');
const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * Get all reports
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getAll = async (req, res) => {
  try {
    // Try to use the Reports model
    try {
      const Reports = getModel('Reports');
      const items = await Reports.find({});
      return res.status(200).json({ success: true, data: items });
    } catch (modelError) {
      logger.warn(`Reports model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('reports');
      const items = await collection.find({}).toArray();
      return res.status(200).json({ success: true, data: items });
    }
  } catch (error) {
    logger.error(`Error in getAll reports: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Get reports by ID
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const getById = async (req, res) => {
  try {
    const id = req.params.id;
    
    // Try to use the Reports model
    try {
      const Reports = getModel('Reports');
      const item = await Reports.findById(id);
      
      if (!item) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      return res.status(200).json({ success: true, data: item });
    } catch (modelError) {
      logger.warn(`Reports model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('reports');
      const item = await collection.findOne({ _id: id });
      
      if (!item) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      return res.status(200).json({ success: true, data: item });
    }
  } catch (error) {
    logger.error(`Error in getById reports: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Create reports
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const create = async (req, res) => {
  try {
    const newItem = req.body;
    
    if (!newItem) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }
    
    // Try to use the Reports model
    try {
      const Reports = getModel('Reports');
      const item = new Reports(newItem);
      const savedItem = await item.save();
      
      return res.status(201).json({ success: true, data: savedItem });
    } catch (modelError) {
      logger.warn(`Reports model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('reports');
      const result = await collection.insertOne(newItem);
      
      return res.status(201).json({ success: true, data: { _id: result.insertedId, ...newItem } });
    }
  } catch (error) {
    logger.error(`Error in create reports: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Update reports
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const update = async (req, res) => {
  try {
    const id = req.params.id;
    const updates = req.body;
    
    if (!updates) {
      return res.status(400).json({ success: false, message: 'No data provided' });
    }
    
    // Try to use the Reports model
    try {
      const Reports = getModel('Reports');
      const updatedItem = await Reports.findByIdAndUpdate(id, updates, { new: true });
      
      if (!updatedItem) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      return res.status(200).json({ 
        success: true, 
        message: 'Reports updated successfully',
        data: updatedItem
      });
    } catch (modelError) {
      logger.warn(`Reports model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('reports');
      const result = await collection.updateOne({ _id: id }, { $set: updates });
      
      if (result.matchedCount === 0) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      // Get the updated item to return
      const updatedItem = await collection.findOne({ _id: id });
      
      return res.status(200).json({ 
        success: true, 
        message: 'Reports updated successfully',
        data: updatedItem
      });
    }
  } catch (error) {
    logger.error(`Error in update reports: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

/**
 * Delete reports
 * 
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
const deleteReports = async (req, res) => {
  try {
    const id = req.params.id;
    
    // Try to use the Reports model
    try {
      const Reports = getModel('Reports');
      const deletedItem = await Reports.findByIdAndDelete(id);
      
      if (!deletedItem) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      return res.status(200).json({ success: true, message: 'Reports deleted successfully' });
    } catch (modelError) {
      logger.warn(`Reports model not available, falling back to collection: ${modelError.message}`);
      
      // Fallback to collection if model is not available
      const collection = await getCollection('reports');
      const result = await collection.deleteOne({ _id: id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ success: false, message: 'Reports not found' });
      }
      
      return res.status(200).json({ success: true, message: 'Reports deleted successfully' });
    }
  } catch (error) {
    logger.error(`Error in delete reports: ${error.message}`);
    return res.status(500).json({ success: false, message: 'Internal server error', error: error.message });
  }
};

module.exports = {
  getAll,
  getById,
  create,
  update,
  delete: deleteReports
};
