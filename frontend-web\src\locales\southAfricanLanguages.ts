/**
 * South African Official Languages Configuration
 * Complete support for all 11 official languages plus additional languages
 */

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
  enabled: boolean;
  completeness: number; // Percentage of translations completed
}

// South African Official Languages (11 + 2 additional)
export const southAfricanLanguages: Language[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 100
  },
  {
    code: 'af',
    name: 'Afrikaans',
    nativeName: 'Afrikaans',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 95
  },
  {
    code: 'zu',
    name: '<PERSON><PERSON>',
    nativeName: 'isiZulu',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 80
  },
  {
    code: 'xh',
    name: 'Xhosa',
    nativeName: 'isiXhosa',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 80
  },
  {
    code: 'st',
    name: 'Soth<PERSON>',
    nativeName: 'Sesotho',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 75
  },
  {
    code: 'tn',
    name: 'Tswana',
    nativeName: 'Setswana',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 75
  },
  {
    code: 've',
    name: 'Venda',
    nativeName: 'Tshivenḓa',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 70
  },
  {
    code: 'ts',
    name: 'Tsonga',
    nativeName: 'Xitsonga',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 70
  },
  {
    code: 'ss',
    name: 'Swati',
    nativeName: 'siSwati',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 70
  },
  {
    code: 'nr',
    name: 'Ndebele',
    nativeName: 'isiNdebele',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 70
  },
  {
    code: 'nso',
    name: 'Northern Sotho',
    nativeName: 'Sepedi',
    flag: '🇿🇦',
    rtl: false,
    enabled: true,
    completeness: 75
  },
  // Additional languages for broader accessibility
  {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇵🇹',
    rtl: false,
    enabled: true,
    completeness: 85
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
    enabled: true,
    completeness: 85
  }
];

// Language groups for better organization
export const languageGroups = {
  official: southAfricanLanguages.slice(0, 11),
  additional: southAfricanLanguages.slice(11),
  nguni: southAfricanLanguages.filter(lang => ['zu', 'xh', 'ss', 'nr'].includes(lang.code)),
  sotho: southAfricanLanguages.filter(lang => ['st', 'tn', 'nso'].includes(lang.code)),
  other: southAfricanLanguages.filter(lang => ['ve', 'ts'].includes(lang.code))
};

// Get language by code
export const getLanguageByCode = (code: string): Language | undefined => {
  return southAfricanLanguages.find(lang => lang.code === code);
};

// Get enabled languages
export const getEnabledLanguages = (): Language[] => {
  return southAfricanLanguages.filter(lang => lang.enabled);
};

// Get languages with high completeness (>= 80%)
export const getCompleteLanguages = (): Language[] => {
  return southAfricanLanguages.filter(lang => lang.completeness >= 80);
};

// Language detection utilities
export const detectBrowserLanguage = (): string => {
  const browserLang = navigator.language.split('-')[0];
  const supportedLang = southAfricanLanguages.find(lang => lang.code === browserLang);
  return supportedLang ? browserLang : 'en';
};

// Language fallback chain
export const getLanguageFallback = (code: string): string[] => {
  const fallbackChains: Record<string, string[]> = {
    'zu': ['zu', 'en', 'af'],
    'xh': ['xh', 'en', 'af'],
    'st': ['st', 'en', 'af'],
    'tn': ['tn', 'en', 'af'],
    've': ['ve', 'en', 'af'],
    'ts': ['ts', 'en', 'af'],
    'ss': ['ss', 'en', 'af'],
    'nr': ['nr', 'en', 'af'],
    'nso': ['nso', 'en', 'af'],
    'af': ['af', 'en'],
    'pt': ['pt', 'en'],
    'fr': ['fr', 'en']
  };
  
  return fallbackChains[code] || [code, 'en'];
};

// Common livestock and farming terms in different languages
export const commonTerms = {
  en: {
    cattle: 'Cattle',
    sheep: 'Sheep',
    goat: 'Goat',
    pig: 'Pig',
    chicken: 'Chicken',
    farm: 'Farm',
    pasture: 'Pasture',
    feed: 'Feed',
    health: 'Health',
    breeding: 'Breeding'
  },
  af: {
    cattle: 'Beeste',
    sheep: 'Skape',
    goat: 'Bokke',
    pig: 'Varke',
    chicken: 'Hoenders',
    farm: 'Plaas',
    pasture: 'Weiding',
    feed: 'Voer',
    health: 'Gesondheid',
    breeding: 'Teling'
  },
  zu: {
    cattle: 'Inkomo',
    sheep: 'Izimvu',
    goat: 'Imbuzi',
    pig: 'Ingulube',
    chicken: 'Inkukhu',
    farm: 'Ipulazi',
    pasture: 'Idlelo',
    feed: 'Ukudla',
    health: 'Impilo',
    breeding: 'Ukuzala'
  },
  xh: {
    cattle: 'Iinkomo',
    sheep: 'Iigusha',
    goat: 'Iibhokhwe',
    pig: 'Iihagu',
    chicken: 'Iinkukhu',
    farm: 'Ifama',
    pasture: 'Idlelo',
    feed: 'Ukutya',
    health: 'Impilo',
    breeding: 'Ukuzala'
  },
  st: {
    cattle: 'Dikgomo',
    sheep: 'Dinku',
    goat: 'Dipudi',
    pig: 'Dikolobe',
    chicken: 'Dikgogo',
    farm: 'Polase',
    pasture: 'Mobu',
    feed: 'Dijo',
    health: 'Bophelo',
    breeding: 'Pelehi'
  }
};

// Language-specific formatting
export const languageFormatting = {
  en: {
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'HH:mm',
    numberFormat: '1,234.56',
    currencySymbol: 'R',
    currencyPosition: 'before'
  },
  af: {
    dateFormat: 'DD/MM/YYYY',
    timeFormat: 'HH:mm',
    numberFormat: '1 234,56',
    currencySymbol: 'R',
    currencyPosition: 'before'
  },
  // Other languages follow similar patterns
};

// Language validation
export const isValidLanguageCode = (code: string): boolean => {
  return southAfricanLanguages.some(lang => lang.code === code);
};

// Language statistics
export const getLanguageStats = () => {
  const total = southAfricanLanguages.length;
  const enabled = southAfricanLanguages.filter(lang => lang.enabled).length;
  const complete = southAfricanLanguages.filter(lang => lang.completeness >= 90).length;
  const avgCompleteness = southAfricanLanguages.reduce((sum, lang) => sum + lang.completeness, 0) / total;
  
  return {
    total,
    enabled,
    complete,
    avgCompleteness: Math.round(avgCompleteness)
  };
};

export default southAfricanLanguages;
