import React from 'react';
import { alpha, Theme } from '@mui/material';
import { getModuleTheme } from './backgroundThemes';

/**
 * Comprehensive styling utility for consistent module styling
 * This provides a centralized way to style all modules consistently
 */

// Background image URLs for different modules
const backgroundImages = {
  dashboard: 'https://images.unsplash.com/photo-**********-04071c5f467b?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  animals: 'https://images.unsplash.com/photo-1564085352725-08da0272627d?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  breeding: 'https://images.unsplash.com/photo-1596733430284-f7437764b1a9?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  health: 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  feeding: 'https://images.unsplash.com/photo-**********-1a0145e9d1c5?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  commercial: 'https://images.unsplash.com/photo-1607082349566-187342175e2f?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  financial: 'https://images.unsplash.com/photo-1565514020179-026b92b2d70b?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  compliance: 'https://images.unsplash.com/photo-1589829545856-d10d557cf95f?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  resources: 'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
  reports: 'https://images.unsplash.com/photo-**********-bebda4e38f71?q=80&w=2000&auto=format&fit=crop&ixlib=rb-4.0.3&blur=10',
};

// Module color schemes - using metallic blue as the base color (from the provided image)
const moduleColors = {
  // Keep original colors for these specific modules
  dashboard: { primary: '#3AA99F', secondary: '#2A8A82' },
  login: { primary: '#3AA99F', secondary: '#2A8A82' },
  commercial: { primary: '#3AA99F', secondary: '#2A8A82' },

  // Use metallic blue for all other modules
  animals: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  breeding: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  health: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  feeding: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  financial: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  compliance: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  resources: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  reports: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  rfid: { primary: '#4A6FA5', secondary: '#3A5A8C' },
  settings: { primary: '#4A6FA5', secondary: '#3A5A8C' },
};

/**
 * Get the background style for a module header
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for the module header background
 */
export const getModuleHeaderStyle = (module: string, theme: Theme): React.CSSProperties => {
  const moduleName = module.toLowerCase();
  const colors = moduleColors[moduleName as keyof typeof moduleColors] || moduleColors.dashboard;

  return {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '200px',
    zIndex: 0,
    background: `linear-gradient(135deg, ${alpha(colors.primary, 0.85)}, ${alpha(colors.secondary, 0.75)})`,
    backgroundImage: `url(${backgroundImages[moduleName as keyof typeof backgroundImages] || backgroundImages.dashboard})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundBlendMode: 'overlay',
    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
  };
};

/**
 * Get the content container style for a module
 * @param theme The Material UI theme
 * @returns CSS properties for the module content container
 */
export const getContentContainerStyle = (theme: Theme): React.CSSProperties => {
  return {
    position: 'relative',
    zIndex: 1,
    padding: '0 24px',
    marginTop: '24px',
  };
};

/**
 * Get the card style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for module cards
 */
export const getModuleCardStyle = (module: string, theme: Theme): React.CSSProperties => {
  const moduleName = module.toLowerCase();
  const colors = moduleColors[moduleName as keyof typeof moduleColors] || moduleColors.dashboard;

  return {
    borderRadius: '12px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    overflow: 'hidden',
    border: `1px solid ${alpha(colors.primary, 0.1)}`,
    transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  };
};

/**
 * Get the card header style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for module card headers
 */
export const getCardHeaderStyle = (module: string, theme: Theme): React.CSSProperties => {
  const moduleName = module.toLowerCase();
  const colors = moduleColors[moduleName as keyof typeof moduleColors] || moduleColors.dashboard;

  return {
    background: `linear-gradient(135deg, ${alpha(colors.primary, 0.9)}, ${alpha(colors.secondary, 0.8)})`,
    color: 'white',
    padding: '16px',
    position: 'relative',
    overflow: 'hidden',
  };
};

/**
 * Get the background pattern style
 * @param opacity The opacity of the pattern
 * @returns CSS properties for the background pattern
 */
export const getBackgroundPatternStyle = (opacity: number = 0.05): React.CSSProperties => {
  return {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity,
    backgroundImage: 'url(https://www.transparenttextures.com/patterns/cubes.png)',
    zIndex: 0,
  };
};

/**
 * Get the table style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns CSS properties for module tables
 */
export const getTableStyle = (module: string, theme: Theme) => {
  const moduleName = module.toLowerCase();
  const colors = moduleColors[moduleName as keyof typeof moduleColors] || moduleColors.dashboard;

  return {
    table: {
      borderCollapse: 'separate',
      borderSpacing: 0,
      width: '100%',
    },
    tableHead: {
      backgroundColor: alpha(colors.primary, 0.05),
    },
    tableHeadCell: {
      fontWeight: 600,
      color: theme.palette.text.primary,
      borderBottom: `2px solid ${alpha(colors.primary, 0.2)}`,
      padding: '12px 16px',
    },
    tableCell: {
      padding: '12px 16px',
      borderBottom: `1px solid ${alpha(theme.palette.divider, 0.7)}`,
    },
    tableRow: {
      '&:hover': {
        backgroundColor: alpha(colors.primary, 0.05),
      },
    },
  };
};

/**
 * Get the button style for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @param variant The button variant (contained, outlined, text)
 * @returns CSS properties for module buttons
 */
export const getButtonStyle = (module: string, theme: Theme, variant: 'contained' | 'outlined' | 'text' = 'contained') => {
  const moduleName = module.toLowerCase();
  const colors = moduleColors[moduleName as keyof typeof moduleColors] || moduleColors.dashboard;

  if (variant === 'contained') {
    return {
      background: `linear-gradient(135deg, ${colors.primary}, ${colors.secondary})`,
      color: 'white',
      boxShadow: `0 4px 10px ${alpha(colors.primary, 0.3)}`,
      '&:hover': {
        boxShadow: `0 6px 15px ${alpha(colors.primary, 0.4)}`,
      },
    };
  } else if (variant === 'outlined') {
    return {
      border: `1px solid ${colors.primary}`,
      color: colors.primary,
      '&:hover': {
        backgroundColor: alpha(colors.primary, 0.05),
      },
    };
  } else {
    return {
      color: colors.primary,
      '&:hover': {
        backgroundColor: alpha(colors.primary, 0.05),
      },
    };
  }
};

/**
 * Get all styles for a module
 * @param module The module name
 * @param theme The Material UI theme
 * @returns All CSS properties for the module
 */
export const getModuleStyles = (module: string, theme: Theme) => {
  return {
    headerStyle: getModuleHeaderStyle(module, theme),
    contentStyle: getContentContainerStyle(theme),
    cardStyle: getModuleCardStyle(module, theme),
    cardHeaderStyle: getCardHeaderStyle(module, theme),
    backgroundPattern: getBackgroundPatternStyle(),
    tableStyles: getTableStyle(module, theme),
    buttonStyles: {
      contained: getButtonStyle(module, theme, 'contained'),
      outlined: getButtonStyle(module, theme, 'outlined'),
      text: getButtonStyle(module, theme, 'text'),
    },
  };
};

export default getModuleStyles;
