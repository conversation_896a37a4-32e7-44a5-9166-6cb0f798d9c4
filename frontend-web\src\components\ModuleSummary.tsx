import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { alpha } from '@mui/material';

interface SubModule {
  name: string;
  path: string;
  icon?: string;
  count?: number | string;
  status?: string;
  color?: string;
}

interface ModuleSummaryProps {
  title: string;
  description: string;
  subModules: SubModule[];
  stats?: Record<string, number | string>;
}

const ModuleSummary: React.FC<ModuleSummaryProps> = ({ title, description, subModules, stats }) => {
  const { currentColor, availableColors } = useThemeContext();
  const { translate } = useLanguage();
  const themeColor = availableColors[currentColor];
  return (
    <div className="p-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-4xl font-bold text-gray-800">{title}</h1>
        <p className="text-gray-600 mt-2">{description}</p>
      </motion.div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {Object.entries(stats).map(([key, value]) => (
            <motion.div
              key={key}
              whileHover={{ scale: 1.02 }}
              className="rounded-xl p-6"
              style={{
                background: `linear-gradient(135deg, ${alpha(themeColor.primary, 0.15)}, ${alpha(themeColor.secondary, 0.1)})`,
                backdropFilter: 'blur(10px)',
                border: 'none',
                boxShadow: `0 8px 32px ${alpha(themeColor.primary, 0.1)}`,
                transition: 'all 0.3s ease'
              }}
            >
              <h3 className="text-gray-500 text-sm font-medium capitalize">
                {translate(`dashboard.stat.${key}`, { fallback: key.replace(/_/g, ' ') })}
              </h3>
              <p className="text-3xl font-bold" style={{ color: themeColor.primary }}>{value}</p>
            </motion.div>
          ))}
        </div>
      )}

      {/* SubModules Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {subModules.map((module) => (
          <motion.div
            key={module.path}
            whileHover={{ scale: 1.02 }}
            className="group"
          >
            <Link
              to={module.path}
              className="block rounded-xl p-6 transition-all duration-300"
              style={{
                background: `linear-gradient(135deg, ${alpha(themeColor.primary, 0.15)}, ${alpha(themeColor.secondary, 0.1)})`,
                backdropFilter: 'blur(10px)',
                border: 'none',
                boxShadow: `0 8px 32px ${alpha(themeColor.primary, 0.1)}`,
                transition: 'all 0.3s ease'
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  {module.icon && (
                    <span className="text-2xl mr-3">{module.icon}</span>
                  )}
                  <h3 className="text-xl font-semibold text-gray-800 hover:text-primary-600">
                    {module.name}
                  </h3>
                </div>
                {module.count && (
                  <span className="px-3 py-1 rounded-full text-sm font-medium"
                        style={{
                          backgroundColor: alpha(themeColor.primary, 0.1),
                          color: themeColor.secondary
                        }}>
                    {module.count}
                  </span>
                )}
              </div>
              {module.status && (
                <div className="mt-2">
                  <span className="text-sm text-gray-500">{module.status}</span>
                </div>
              )}
              <div className="mt-4 flex justify-end">
                <span className="group-hover:translate-x-2 transition-transform duration-200"
                      style={{ color: themeColor.primary }}>
                  →
                </span>
              </div>
            </Link>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default ModuleSummary;
