/**
 * MongoDB Animal Data Verification Script
 *
 * This script verifies the animal data in MongoDB by checking relationships
 * between animals and related records.
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object
const client = new MongoClient(uri, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
});

async function verifyAnimalData() {
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);

    // Get collection counts
    const collections = [
      'animals',
      'health_records',
      'breeding_records',
      'birth_records',
      'feeding_records',
      'feed_inventory',
      'financial_records'
    ];

    console.log('\nCollection counts:');
    for (const collectionName of collections) {
      const count = await db.collection(collectionName).countDocuments();
      console.log(`${collectionName}: ${count} documents`);
    }

    // Verify animal relationships
    console.log('\nVerifying animal relationships:');

    // Get all animals
    const animals = await db.collection('animals').find().toArray();
    console.log(`Total animals: ${animals.length}`);

    // Check parent-child relationships
    const offspringCount = animals.filter(animal => animal.parentId).length;
    console.log(`Animals with parent relationships: ${offspringCount}`);

    // Verify breeding records
    const breedingRecords = await db.collection('breeding_records').find().toArray();
    console.log(`Breeding records: ${breedingRecords.length}`);

    // Verify each breeding record has valid animal references
    let validBreedingRecords = 0;
    for (const record of breedingRecords) {
      const female = animals.find(a => a._id.toString() === record.femaleId);
      const male = animals.find(a => a._id.toString() === record.maleId);

      if (female && male) {
        validBreedingRecords++;
      }
    }
    console.log(`Valid breeding records with existing animals: ${validBreedingRecords}`);

    // Verify birth records
    const birthRecords = await db.collection('birth_records').find().toArray();
    console.log(`Birth records: ${birthRecords.length}`);

    // Verify each birth record has valid animal references
    let validBirthRecords = 0;
    for (const record of birthRecords) {
      const mother = animals.find(a => a._id.toString() === record.motherId);
      const father = animals.find(a => a._id.toString() === record.fatherId);
      const offspring = animals.find(a => a._id.toString() === record.offspringId);

      if (mother && father && offspring) {
        validBirthRecords++;
      }
    }
    console.log(`Valid birth records with existing animals: ${validBirthRecords}`);

    // Verify health records
    const healthRecords = await db.collection('health_records').find().toArray();
    console.log(`Health records: ${healthRecords.length}`);

    // Verify each health record has valid animal reference
    let validHealthRecords = 0;
    for (const record of healthRecords) {
      const animal = animals.find(a => a._id.toString() === record.animalId);

      if (animal) {
        validHealthRecords++;
      }
    }
    console.log(`Valid health records with existing animals: ${validHealthRecords}`);

    // Verify feeding records
    const feedingRecords = await db.collection('feeding_records').find().toArray();
    console.log(`Feeding records: ${feedingRecords.length}`);

    // Verify each feeding record has valid animal reference
    let validFeedingRecords = 0;
    for (const record of feedingRecords) {
      const animal = animals.find(a => a._id.toString() === record.animalId);

      if (animal) {
        validFeedingRecords++;
      }
    }
    console.log(`Valid feeding records with existing animals: ${validFeedingRecords}`);

    // Verify financial records
    const financialRecords = await db.collection('financial_records').find().toArray();
    console.log(`Financial records: ${financialRecords.length}`);

    // Count financial records related to animals
    const animalFinancialRecords = financialRecords.filter(record =>
      record.relatedEntityType === 'Animal'
    ).length;
    console.log(`Financial records related to animals: ${animalFinancialRecords}`);

    // Print sample animal data
    console.log('\nSample animal data:');
    const sampleAnimal = animals[0];
    console.log(JSON.stringify(sampleAnimal, null, 2));

    console.log('\nVerification completed successfully!');

  } catch (error) {
    console.error('Error verifying animal data:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

// Run the script
console.log('Starting verification of animal data...');
verifyAnimalData()
  .then(() => {
    console.log('Verification script completed successfully!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Verification script failed with error:', error);
    process.exit(1);
  });
