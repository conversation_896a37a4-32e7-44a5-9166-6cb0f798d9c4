/**
 * <PERSON><PERSON><PERSON> to set the MongoDB password in .env files
 * 
 * This script replaces the <db_password> placeholder in .env files with the actual password
 * Run with: node set-db-password.js YOUR_PASSWORD
 */

const fs = require('fs');
const path = require('path');

// Get the password from command line arguments
const password = process.argv[2];

if (!password) {
  console.error('Error: No password provided');
  console.error('Usage: node set-db-password.js YOUR_PASSWORD');
  process.exit(1);
}

// Files to update
const files = [
  path.join(__dirname, 'backend', '.env'),
  path.join(__dirname, 'frontend-web', '.env')
];

// Update each file
files.forEach(filePath => {
  try {
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return;
    }

    // Read the file content
    let content = fs.readFileSync(filePath, 'utf8');

    // Replace <db_password> with the actual password
    const updatedContent = content.replace(/<db_password>/g, password);

    // Write the updated content back to the file
    fs.writeFileSync(filePath, updatedContent);

    console.log(`✅ Updated ${filePath}`);
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error);
  }
});

console.log('Done! MongoDB password has been set in all .env files.');
