# 🔐 AgriIntel Login Credentials & Access Guide

## 🚀 **Application URLs**

### **Main Application**
- **Landing Page:** [http://localhost:3003](http://localhost:3003)
- **Beta Access:** [http://localhost:3003/beta](http://localhost:3003/beta)
- **Login Page:** [http://localhost:3003/login](http://localhost:3003/login)
- **Registration:** [http://localhost:3003/register](http://localhost:3003/register)
- **Dashboard:** [http://localhost:3003/dashboard](http://localhost:3003/dashboard)

### **Backend API**
- **API Base:** [http://localhost:3002/api](http://localhost:3002/api)
- **Health Check:** [http://localhost:3002/api/health](http://localhost:3002/api/health)

---

## 👤 **Login Credentials**

### **🔑 Admin Access (Full System Access)**
```
Username: admin
Password: Admin@123
Role: Administrator
Access Level: Full system access, all modules, user management
Subscription: Enterprise
```

### **🧪 Demo/Beta User (Limited Access)**
```
Username: Demo
Password: 123
Role: Demo User
Access Level: Beta features, limited to 50 animals, basic modules
Subscription: Beta Access (30-day trial)
```

### **🔧 Development/Testing Users**
```
Username: manager
Password: CHANGE_ME
Role: Manager
Access Level: View and edit permissions

Username: staff
Password: CHANGE_ME
Role: Staff
Access Level: View-only permissions
```

---

## 🎯 **Access Levels & Features**

### **Beta Access (Demo User)**
- ✅ Up to 50 animals
- ✅ Basic health monitoring
- ✅ Simple reports
- ✅ Email support
- ✅ Mobile app access
- ❌ Advanced analytics
- ❌ Financial management
- ❌ API access

### **Professional Tier (R299/month)**
- ✅ Up to 500 animals
- ✅ Advanced health analytics
- ✅ Financial management
- ✅ Breeding optimization
- ✅ Priority support
- ✅ Custom reports
- ✅ API access

### **Enterprise Tier (R599/month)**
- ✅ Unlimited animals
- ✅ AI-powered insights
- ✅ Multi-farm management
- ✅ Advanced analytics
- ✅ Dedicated support
- ✅ Custom integrations
- ✅ White-label options
- ✅ Training & onboarding

### **Administrator Access**
- ✅ All features from all tiers
- ✅ User management
- ✅ System configuration
- ✅ Database management
- ✅ Security settings
- ✅ Backup & restore
- ✅ Analytics & reporting

---

## 🔄 **Navigation Flow**

### **New Users**
1. Visit [http://localhost:3003](http://localhost:3003) (Landing Page)
2. Click "Start Free Trial" → Redirects to Beta Access
3. Or click "Try Beta" in navigation
4. Register for full account or use Demo credentials

### **Existing Users**
1. Visit [http://localhost:3003/login](http://localhost:3003/login)
2. Use credentials above
3. Access dashboard at [http://localhost:3003/dashboard](http://localhost:3003/dashboard)

### **Beta Testing**
1. Visit [http://localhost:3003/beta](http://localhost:3003/beta)
2. Use Demo credentials (Demo/123)
3. Explore limited features
4. Upgrade prompts for premium features

---

## 🛠 **Module Access by Role**

### **Available to All Users**
- 📊 Dashboard Overview
- 🐄 Animals (Basic)
- 📱 Mobile App Access

### **Professional & Enterprise**
- 💰 Financial Management
- 📈 Advanced Analytics
- 🔬 Health Management
- 🧬 Breeding Management
- 🌾 Feeding Management
- 📦 Inventory Management
- 🏪 Commercial Management
- 📋 Reports & Analytics
- 🌍 Resources Management
- ⚙️ Settings (Limited)

### **Administrator Only**
- 👥 User Management
- 🔧 System Settings
- 🗄️ Database Management
- 🔒 Security Configuration
- 📊 System Analytics
- 🔄 Backup & Restore

---

## 🎨 **Premium Features**

### **Modern Design Elements**
- ✨ Glassmorphism effects
- 🌈 Gradient color schemes
- 🎭 Interactive animations
- 📱 Responsive design
- 🎯 Premium UI components

### **Subscription Management**
- 💳 Integrated payment processing
- 📊 Usage analytics
- 🔔 Upgrade notifications
- 📈 Feature comparison
- 💎 Premium feature locks

### **Multi-language Support**
- 🇿🇦 11 South African official languages
- 🌍 Dynamic content translation
- 🔄 Seamless language switching
- 📱 Mobile-optimized translations

---

## 🚨 **Troubleshooting**

### **Login Issues**
1. Ensure backend is running on port 3002
2. Check MongoDB connection
3. Verify credentials are correct (case-sensitive)
4. Clear browser cache if needed

### **Access Denied**
1. Check user role and permissions
2. Verify subscription status
3. Contact administrator for access

### **Feature Not Available**
1. Check subscription tier
2. Upgrade to access premium features
3. Contact support for assistance

---

## 📞 **Support**

- **Email:** <EMAIL>
- **Phone:** +27 (0) 11 123 4567
- **Hours:** 24/7 for Enterprise, Business hours for others
- **Documentation:** Available in dashboard help section

---

## 🔄 **Quick Start Commands**

```bash
# Start Backend
cd backend
npm start

# Start Frontend
cd frontend-web
npm start

# Access Application
# Frontend: http://localhost:3003
# Backend: http://localhost:3002
```

---

**Last Updated:** December 2024  
**Version:** 1.0.0  
**Status:** Production Ready (75% complete)
