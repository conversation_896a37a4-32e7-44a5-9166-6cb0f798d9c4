/**
 * Check MongoDB Script
 * 
 * This script checks the MongoDB database structure and data
 */

const { MongoClient } = require('mongodb');

// MongoDB connection string
const uri = 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = 'ampd_livestock';

// Function to check MongoDB
async function checkMongoDB() {
  let client;

  try {
    console.log('Connecting to MongoDB...');
    client = new MongoClient(uri);
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(dbName);
    
    // List all collections
    const collections = await db.listCollections().toArray();
    console.log(`Found ${collections.length} collections in database:`);
    
    for (const collection of collections) {
      console.log(`- ${collection.name}`);
      
      // Count documents in collection
      const count = await db.collection(collection.name).countDocuments();
      console.log(`  Collection ${collection.name} has ${count} documents`);
      
      // Show a sample document if collection has documents
      if (count > 0) {
        const sample = await db.collection(collection.name).findOne();
        console.log(`  Sample document from ${collection.name}:`);
        console.log(JSON.stringify(sample, null, 2).substring(0, 500) + '...');
      }
    }
    
    // Check specific collections needed for the application
    const requiredCollections = [
      'animals',
      'healthrecords',
      'breedingrecords',
      'birthrecords',
      'feedingrecords',
      'transactions',
      'inventoryitems',
      'businesskpis',
      'users'
    ];
    
    console.log('\nChecking required collections:');
    for (const collectionName of requiredCollections) {
      const exists = collections.some(c => c.name === collectionName);
      console.log(`- ${collectionName}: ${exists ? 'EXISTS' : 'MISSING'}`);
      
      if (exists) {
        const count = await db.collection(collectionName).countDocuments();
        console.log(`  Has ${count} documents`);
      }
    }
    
    console.log('\nMongoDB check completed');
  } catch (error) {
    console.error('Error checking MongoDB:', error);
  } finally {
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// Run the check function
checkMongoDB()
  .then(() => {
    console.log('MongoDB check process completed');
  })
  .catch((error) => {
    console.error('Error in MongoDB check process:', error);
  });
