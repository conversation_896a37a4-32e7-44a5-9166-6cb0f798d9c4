import React, { useEffect, useState, useRef } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { getModuleTheme } from '../../utils/backgroundThemes';

export interface AnimatedBackgroundProps {
  module?: string;
  backgroundImage?: string;
  overlay?: 'light' | 'dark' | 'gradient' | 'none';
  accentColor?: string;
  secondaryColor?: string;
  children?: React.ReactNode;
  animationVariant?: 'fade' | 'slide' | 'zoom' | 'particles' | 'livestock' | 'none';
  height?: string | number;
  borderRadius?: string | number;
  boxShadow?: string;
  zIndex?: number;
  position?: 'relative' | 'absolute' | 'fixed' | 'sticky';
  showFloatingElements?: boolean;
  particleCount?: number;
}

const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({
  module,
  backgroundImage: propBackgroundImage,
  overlay = 'gradient',
  accentColor,
  secondaryColor,
  children,
  animationVariant = 'fade',
  height = '100%',
  borderRadius = 0,
  boxShadow = 'none',
  zIndex = 0,
  position = 'relative',
  showFloatingElements = true,
  particleCount = 30
}) => {
  const theme = useTheme();
  const [isLoaded, setIsLoaded] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Get module theme if provided
  const moduleTheme = module ? getModuleTheme(module) : null;

  // Use theme colors or fallback to provided colors
  const primaryColor = accentColor || theme.palette.primary.main;
  const secondaryColorValue = secondaryColor || theme.palette.primary.dark;

  // Use the provided background image or get one based on module
  const backgroundImage = propBackgroundImage || moduleTheme?.backgroundImage || '';

  // Set loaded state after component mounts
  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Particle animation system
  useEffect(() => {
    if (animationVariant !== 'particles' && animationVariant !== 'livestock') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle system
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }> = [];

    // Enhanced color palette for livestock theme
    const colors = animationVariant === 'livestock' ? [
      '#4CAF50', // Green for grass/nature
      '#2196F3', // Blue for sky/water
      '#FF9800', // Orange for earth/wheat
      '#8BC34A', // Light green for crops
      '#795548', // Brown for earth
      '#FFC107', // Yellow for sun/grain
      theme.palette.primary.main,
      theme.palette.secondary.main,
    ] : [
      theme.palette.primary.main,
      theme.palette.secondary.main,
      theme.palette.success.main,
      alpha(theme.palette.primary.main, 0.6),
    ];

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1,
        opacity: Math.random() * 0.3 + 0.1,
        color: colors[Math.floor(Math.random() * colors.length)],
      });
    }

    let animationId: number;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      particles.forEach((particle) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = `${particle.color}${Math.floor(particle.opacity * 255).toString(16).padStart(2, '0')}`;
        ctx.fill();
      });

      // Draw connections for livestock variant
      if (animationVariant === 'livestock') {
        particles.forEach((particle, i) => {
          particles.slice(i + 1).forEach((otherParticle) => {
            const dx = particle.x - otherParticle.x;
            const dy = particle.y - otherParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(particle.x, particle.y);
              ctx.lineTo(otherParticle.x, otherParticle.y);
              ctx.strokeStyle = `${particle.color}${Math.floor((1 - distance / 100) * 0.2 * 255).toString(16).padStart(2, '0')}`;
              ctx.lineWidth = 0.5;
              ctx.stroke();
            }
          });
        });
      }

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [animationVariant, particleCount, theme]);

  // Get overlay styles
  const getOverlayStyles = () => {
    switch (overlay) {
      case 'light':
        return {
          background: alpha('#ffffff', 0.5),
        };
      case 'dark':
        return {
          background: alpha('#000000', 0.4),
        };
      case 'gradient':
        return {
          background: `linear-gradient(135deg, ${alpha(primaryColor, 0.85)}, ${alpha(secondaryColorValue, 0.75)})`,
        };
      case 'none':
      default:
        return {};
    }
  };

  // Get animation variants
  const getAnimationVariants = () => {
    switch (animationVariant) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          transition: { duration: 0.8 }
        };
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          transition: { duration: 0.5 }
        };
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          transition: { duration: 0.5 }
        };
      case 'none':
      default:
        return {
          initial: {},
          animate: {},
          transition: {}
        };
    }
  };

  const animationProps = getAnimationVariants();

  return (
    <Box
      sx={{
        position,
        height,
        width: '100%',
        overflow: 'hidden',
        borderRadius,
        boxShadow,
        zIndex
      }}
    >
      {/* Background Image with Overlay */}
      <motion.div
        initial={animationProps.initial}
        animate={isLoaded ? animationProps.animate : animationProps.initial}
        transition={animationProps.transition}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: 0
        }}
      >
        {/* Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            ...getOverlayStyles(),
            zIndex: 1
          }}
        />
      </motion.div>

      {/* Particle Canvas */}
      {(animationVariant === 'particles' || animationVariant === 'livestock') && (
        <Box
          component="canvas"
          ref={canvasRef}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 1,
          }}
        />
      )}

      {/* Floating Livestock Elements */}
      {showFloatingElements && animationVariant === 'livestock' && (
        <>
          <Box
            component={motion.div}
            animate={{
              y: [0, -20, 0],
              rotate: [0, 5, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut",
            }}
            sx={{
              position: 'absolute',
              top: '10%',
              left: '5%',
              fontSize: '60px',
              opacity: 0.1,
              color: theme.palette.primary.main,
              zIndex: 1,
            }}
          >
            🐄
          </Box>

          <Box
            component={motion.div}
            animate={{
              y: [0, 15, 0],
              rotate: [0, -3, 0],
            }}
            transition={{
              duration: 6,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1,
            }}
            sx={{
              position: 'absolute',
              top: '60%',
              right: '10%',
              fontSize: '50px',
              opacity: 0.1,
              color: theme.palette.secondary.main,
              zIndex: 1,
            }}
          >
            🐑
          </Box>

          <Box
            component={motion.div}
            animate={{
              y: [0, -10, 0],
              x: [0, 10, 0],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2,
            }}
            sx={{
              position: 'absolute',
              bottom: '20%',
              left: '15%',
              fontSize: '40px',
              opacity: 0.1,
              color: theme.palette.success.main,
              zIndex: 1,
            }}
          >
            🌾
          </Box>

          <Box
            component={motion.div}
            animate={{
              y: [0, 12, 0],
              rotate: [0, 2, 0],
            }}
            transition={{
              duration: 7,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 3,
            }}
            sx={{
              position: 'absolute',
              top: '30%',
              right: '25%',
              fontSize: '45px',
              opacity: 0.1,
              color: theme.palette.warning.main,
              zIndex: 1,
            }}
          >
            🚜
          </Box>

          <Box
            component={motion.div}
            animate={{
              scale: [1, 1.1, 1],
              opacity: [0.05, 0.15, 0.05],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4,
            }}
            sx={{
              position: 'absolute',
              top: '45%',
              left: '30%',
              fontSize: '55px',
              opacity: 0.1,
              color: theme.palette.info.main,
              zIndex: 1,
            }}
          >
            🐷
          </Box>

          <Box
            component={motion.div}
            animate={{
              y: [0, -8, 0],
              rotate: [0, -2, 0],
            }}
            transition={{
              duration: 9,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 5,
            }}
            sx={{
              position: 'absolute',
              bottom: '40%',
              right: '35%',
              fontSize: '35px',
              opacity: 0.1,
              color: theme.palette.error.main,
              zIndex: 1,
            }}
          >
            🐔
          </Box>
        </>
      )}

      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 3,
          height: '100%',
          width: '100%'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default AnimatedBackground;
