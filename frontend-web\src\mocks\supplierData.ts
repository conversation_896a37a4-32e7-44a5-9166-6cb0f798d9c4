export interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  category: 'feed' | 'medicine' | 'equipment' | 'livestock' | 'other';
  rating: number;
  activeContracts: number;
  paymentTerms: string;
  status: 'active' | 'inactive' | 'blacklisted';
  products: SupplierProduct[];
  lastOrderDate?: Date;
  notes: string;
}

export interface SupplierProduct {
  id: string;
  name: string;
  category: string;
  unitPrice: number;
  minimumOrder: number;
  leadTime: string;
  availability: boolean;
}

export const mockSuppliers: Supplier[] = [
  {
    id: 'SUP001',
    name: 'Farm Feed Co.',
    contactPerson: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0123',
    address: '456 Supply Road, Rural City, ST 12345',
    category: 'feed',
    rating: 4.5,
    activeContracts: 2,
    paymentTerms: 'Net 30',
    status: 'active',
    products: [
      {
        id: 'PROD001',
        name: 'Premium Cattle Feed',
        category: 'feed',
        unitPrice: 45.99,
        minimumOrder: 1000,
        leadTime: '5 days',
        availability: true
      }
    ],
    lastOrderDate: new Date('2024-02-01'),
    notes: 'Reliable supplier with competitive prices'
  }
];