/**
 * <PERSON><PERSON><PERSON> to fix MongoDB timers/promises issue
 * This script creates a package.json file in the mongodb_oidc directory
 * to resolve the timers/promises module
 */
const fs = require('fs');
const path = require('path');

// Path to the mongodb_oidc directory
const mongodbOidcPath = path.resolve(
  __dirname, 
  '../../../node_modules/mongodb/lib/cmap/auth/mongodb_oidc'
);

// Check if the directory exists
if (fs.existsSync(mongodbOidcPath)) {
  console.log(`Found MongoDB OIDC directory at: ${mongodbOidcPath}`);
  
  // Create a package.json file in the directory
  const packageJson = {
    "name": "mongodb-oidc-polyfill",
    "version": "1.0.0",
    "main": "index.js",
    "dependencies": {
      "timers-promises-polyfill": "file:../../../../../src/polyfills/timers-promises-polyfill.js"
    }
  };
  
  // Write the package.json file
  const packageJsonPath = path.join(mongodbOidcPath, 'package.json');
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log(`Created package.json at: ${packageJsonPath}`);
  
  // Create an index.js file that exports empty implementations
  const indexJs = `
/**
 * Empty implementations for MongoDB OIDC modules
 */
module.exports = {
  callback_workflow: {
    CallbackWorkflow: class CallbackWorkflow {
      constructor() {}
      start() { return Promise.resolve(null); }
    }
  },
  machine_workflow: {
    MachineWorkflow: class MachineWorkflow {
      constructor() {}
      start() { return Promise.resolve(null); }
    }
  }
};
`;
  
  const indexJsPath = path.join(mongodbOidcPath, 'index.js');
  fs.writeFileSync(indexJsPath, indexJs);
  console.log(`Created index.js at: ${indexJsPath}`);
  
  // Create empty implementations for the problematic files
  const callbackWorkflowJs = `
/**
 * Empty implementation for callback_workflow.js
 */
class CallbackWorkflow {
  constructor() {}
  start() { return Promise.resolve(null); }
}

module.exports = { CallbackWorkflow };
`;
  
  const callbackWorkflowJsPath = path.join(mongodbOidcPath, 'callback_workflow.js');
  fs.writeFileSync(callbackWorkflowJsPath, callbackWorkflowJs);
  console.log(`Created callback_workflow.js at: ${callbackWorkflowJsPath}`);
  
  const machineWorkflowJs = `
/**
 * Empty implementation for machine_workflow.js
 */
class MachineWorkflow {
  constructor() {}
  start() { return Promise.resolve(null); }
}

module.exports = { MachineWorkflow };
`;
  
  const machineWorkflowJsPath = path.join(mongodbOidcPath, 'machine_workflow.js');
  fs.writeFileSync(machineWorkflowJsPath, machineWorkflowJs);
  console.log(`Created machine_workflow.js at: ${machineWorkflowJsPath}`);
  
  console.log('MongoDB OIDC polyfill setup complete!');
} else {
  console.error(`MongoDB OIDC directory not found at: ${mongodbOidcPath}`);
}
