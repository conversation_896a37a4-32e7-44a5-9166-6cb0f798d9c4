import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useThemeContext } from '../contexts/ThemeContext';
import AgriIntelBrand from '../components/branding/AgriIntelBrand';
import EnhancedGradientBackground from '../components/common/EnhancedGradientBackground';

interface RegistrationData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  farmName: string;
  province: string;
  farmSize: string;
  primaryLivestock: string;
  phoneNumber: string;
  agreeToTerms: boolean;
  subscriptionTier: 'beta' | 'professional' | 'enterprise';
}

const Register: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { currentColor, availableColors } = useThemeContext();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState<RegistrationData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    farmName: '',
    province: '',
    farmSize: '',
    primaryLivestock: '',
    phoneNumber: '',
    agreeToTerms: false,
    subscriptionTier: 'beta'
  });

  const provinces = [
    'Eastern Cape', 'Free State', 'Gauteng', 'KwaZulu-Natal',
    'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West', 'Western Cape'
  ];

  const farmSizes = [
    'Small (1-50 animals)', 'Medium (51-200 animals)', 
    'Large (201-500 animals)', 'Commercial (500+ animals)'
  ];

  const livestockTypes = [
    'Cattle', 'Sheep', 'Goats', 'Pigs', 'Poultry', 'Mixed Livestock'
  ];

  const handleInputChange = (field: keyof RegistrationData) => (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | any
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const validateForm = (): boolean => {
    if (!formData.username.trim()) {
      setError('Username is required');
      return false;
    }
    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Valid email is required');
      return false;
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (!formData.firstName.trim() || !formData.lastName.trim()) {
      setError('First and last name are required');
      return false;
    }
    if (!formData.agreeToTerms) {
      setError('You must agree to the terms and conditions');
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setError(null);

    try {
      const registrationPayload = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        farmName: formData.farmName,
        province: formData.province,
        farmSize: formData.farmSize,
        primaryLivestock: formData.primaryLivestock,
        phoneNumber: formData.phoneNumber,
        role: 'user',
        subscriptionTier: formData.subscriptionTier,
        status: 'active',
        permissions: formData.subscriptionTier === 'beta' ? ['basic'] : ['all'],
        registrationDate: new Date().toISOString()
      };

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationPayload),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setSuccess('Registration successful! You can now log in to your account.');
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      } else {
        setError(data.message || 'Registration failed. Please try again.');
      }
    } catch (err) {
      setError('Network error. Please check your connection and try again.');
      console.error('Registration error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const themeColor = availableColors[currentColor];

  return (
    <EnhancedGradientBackground>
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2
        }}
      >
        <Card
          sx={{
            maxWidth: 800,
            width: '100%',
            backdropFilter: 'blur(10px)',
            backgroundColor: alpha(theme.palette.background.paper, 0.9),
            boxShadow: `0 8px 32px ${alpha(themeColor.primary, 0.3)}`
          }}
        >
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <AgriIntelBrand
                variant="full"
                size="large"
                showSlogan={true}
                color="primary"
                orientation="vertical"
                sx={{ mb: 2 }}
              />
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
                Join the Future of Farming
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Create your AgriIntel account and start managing your livestock intelligently
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            {success && (
              <Alert severity="success" sx={{ mb: 3 }}>
                {success}
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Username"
                    value={formData.username}
                    onChange={handleInputChange('username')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange('email')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={formData.firstName}
                    onChange={handleInputChange('firstName')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={formData.lastName}
                    onChange={handleInputChange('lastName')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Password"
                    type="password"
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Confirm Password"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    required
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Farm Name (Optional)"
                    value={formData.farmName}
                    onChange={handleInputChange('farmName')}
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Province</InputLabel>
                    <Select
                      value={formData.province}
                      onChange={handleInputChange('province')}
                      disabled={isLoading}
                    >
                      {provinces.map((province) => (
                        <MenuItem key={province} value={province}>
                          {province}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Phone Number (Optional)"
                    value={formData.phoneNumber}
                    onChange={handleInputChange('phoneNumber')}
                    disabled={isLoading}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Farm Size</InputLabel>
                    <Select
                      value={formData.farmSize}
                      onChange={handleInputChange('farmSize')}
                      disabled={isLoading}
                    >
                      {farmSizes.map((size) => (
                        <MenuItem key={size} value={size}>
                          {size}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Primary Livestock</InputLabel>
                    <Select
                      value={formData.primaryLivestock}
                      onChange={handleInputChange('primaryLivestock')}
                      disabled={isLoading}
                    >
                      {livestockTypes.map((type) => (
                        <MenuItem key={type} value={type}>
                          {type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Subscription Tier</InputLabel>
                    <Select
                      value={formData.subscriptionTier}
                      onChange={handleInputChange('subscriptionTier')}
                      disabled={isLoading}
                    >
                      <MenuItem value="beta">Beta (Free - 50 animals max)</MenuItem>
                      <MenuItem value="professional">Professional (R299/month)</MenuItem>
                      <MenuItem value="enterprise">Enterprise (R599/month)</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.agreeToTerms}
                        onChange={handleInputChange('agreeToTerms')}
                        disabled={isLoading}
                      />
                    }
                    label="I agree to the Terms and Conditions and Privacy Policy"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading}
                    sx={{
                      background: `linear-gradient(135deg, ${themeColor.primary}, ${themeColor.secondary})`,
                      py: 1.5,
                      fontSize: '1.1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {isLoading ? (
                      <>
                        <CircularProgress size={20} sx={{ mr: 1 }} />
                        Creating Account...
                      </>
                    ) : (
                      'Create Account'
                    )}
                  </Button>
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      Already have an account?{' '}
                      <Button
                        variant="text"
                        onClick={() => navigate('/login')}
                        disabled={isLoading}
                        sx={{ textTransform: 'none' }}
                      >
                        Sign In
                      </Button>
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </CardContent>
        </Card>
      </Box>
    </EnhancedGradientBackground>
  );
};

export default Register;
