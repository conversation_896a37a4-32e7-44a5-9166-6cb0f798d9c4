import React, { ReactNode } from 'react';
import { Box, Grid, Paper, Typography, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks';

interface FormSection {
  title: string;
  description?: string;
  children: ReactNode;
  gridColumns?: 1 | 2 | 3 | 4;
}

interface ResponsiveFormLayoutProps {
  title: string;
  subtitle?: string;
  sections: FormSection[];
  actions?: ReactNode;
  backgroundImage?: string;
  maxWidth?: string | number;
}

/**
 * ResponsiveFormLayout component
 * Provides a consistent, responsive layout for forms
 */
const ResponsiveFormLayout: React.FC<ResponsiveFormLayoutProps> = ({
  title,
  subtitle,
  sections,
  actions,
  backgroundImage,
  maxWidth = '1200px'
}) => {
  const theme = useTheme();
  const { isMobile, isTablet } = useResponsive();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box
      sx={{
        maxWidth,
        mx: 'auto',
        px: { xs: 2, sm: 3, md: 4 },
        py: { xs: 2, sm: 3 },
        position: 'relative'
      }}
    >
      {/* Form Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h4" 
            component="h1" 
            gutterBottom
            sx={{
              fontWeight: 700,
              fontSize: { xs: '1.75rem', sm: '2rem', md: '2.25rem' },
              color: theme.palette.text.primary
            }}
          >
            {title}
          </Typography>
          {subtitle && (
            <Typography 
              variant="subtitle1" 
              color="text.secondary"
              sx={{
                fontSize: { xs: '1rem', sm: '1.1rem' },
                maxWidth: '800px'
              }}
            >
              {subtitle}
            </Typography>
          )}
        </Box>
      </motion.div>

      {/* Form Sections */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {sections.map((section, index) => (
          <motion.div key={index} variants={itemVariants}>
            <Paper
              elevation={0}
              sx={{
                mb: 4,
                borderRadius: '12px',
                border: `1px solid ${theme.palette.divider}`,
                overflow: 'hidden',
                position: 'relative',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  background: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
                  zIndex: 1
                }
              }}
            >
              {/* Background Image with Overlay */}
              {backgroundImage && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundImage: `url(${backgroundImage})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                    opacity: 0.05,
                    zIndex: 0
                  }}
                />
              )}

              {/* Section Header */}
              <Box
                sx={{
                  p: { xs: 2, sm: 3 },
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  position: 'relative',
                  zIndex: 1
                }}
              >
                <Typography 
                  variant="h6" 
                  fontWeight="bold"
                  sx={{ 
                    color: theme.palette.text.primary,
                    fontSize: { xs: '1.1rem', sm: '1.2rem' }
                  }}
                >
                  {section.title}
                </Typography>
                {section.description && (
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ mt: 0.5 }}
                  >
                    {section.description}
                  </Typography>
                )}
              </Box>

              {/* Section Content */}
              <Box
                sx={{
                  p: { xs: 2, sm: 3 },
                  position: 'relative',
                  zIndex: 1,
                  bgcolor: alpha(theme.palette.background.paper, 0.7)
                }}
              >
                <Grid 
                  container 
                  spacing={3}
                  columns={12}
                >
                  {React.Children.map(section.children, (child, childIndex) => {
                    // Determine column width based on screen size and section configuration
                    const gridColumns = section.gridColumns || 2;
                    let columnWidth = 12 / gridColumns;
                    
                    // On mobile, always use full width
                    if (isMobile) {
                      columnWidth = 12;
                    } 
                    // On tablet, use at most 2 columns
                    else if (isTablet && gridColumns > 2) {
                      columnWidth = 6;
                    }

                    return (
                      <Grid item xs={12} sm={columnWidth === 6 ? 6 : 12} md={columnWidth}>
                        {child}
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Paper>
          </motion.div>
        ))}
      </motion.div>

      {/* Form Actions */}
      {actions && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-end',
              mt: 2,
              gap: 2,
              flexWrap: 'wrap',
              flexDirection: isMobile ? 'column' : 'row'
            }}
          >
            {actions}
          </Box>
        </motion.div>
      )}
    </Box>
  );
};

export default ResponsiveFormLayout;
