const express = require('express');
const router = express.Router();

module.exports = (useMockData = false) => {
  // Controller would be selected based on mock data flag
  const controller = useMockData
    ? require('../controllers/mockDataController')
    : require('../controllers/mongoDbController');

  // Define routes
  router.get('/', (req, res) => {
    res.status(200).json({ message: 'Breeding records endpoint' });
  });

  // Breeding records routes
  router.get('/records', controller.getBreedingRecords);
  router.get('/calving', controller.getCalvingRecords);

  // If not using mock data, add CRUD operations
  if (!useMockData) {
    router.post('/records', controller.createBreedingRecord);
    router.put('/records/:id', controller.updateBreedingRecord);
    router.delete('/records/:id', controller.deleteBreedingRecord);
    router.post('/calving', controller.createCalvingRecord);
  }

  return router;
};