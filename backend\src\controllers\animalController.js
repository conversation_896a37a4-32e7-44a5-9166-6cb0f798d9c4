const Animal = require('../models/animal');
const logger = require('../utils/logger');

/**
 * Animal controller for handling animal-related requests
 */
const animalController = {
  /**
   * Create a new animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createAnimal: async (req, res) => {
    try {
      const {
        tagNumber,
        animalType,
        breed,
        gender,
        birthDate,
        weight,
        status,
        location,
        parentTagNumber,
        purchaseDate,
        purchasePrice,
        notes
      } = req.body;

      // Check if animal with tag number already exists
      const existingAnimal = await Animal.findByTagNumber(tagNumber);
      if (existingAnimal) {
        return res.status(400).json({
          error: {
            message: 'Animal with this tag number already exists',
            status: 400
          }
        });
      }

      // Create animal
      const animal = await Animal.create({
        tagNumber,
        animalType,
        breed,
        gender,
        birthDate,
        weight,
        status,
        location,
        parentTagNumber,
        purchaseDate,
        purchasePrice,
        notes,
        createdBy: req.user.id
      });

      res.status(201).json({
        message: 'Animal created successfully',
        animal
      });
    } catch (error) {
      logger.error('Error creating animal:', error);
      res.status(500).json({
        error: {
          message: 'Error creating animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get all animals with pagination and filtering
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllAnimals: async (req, res) => {
    try {
      const {
        page,
        limit,
        animalType,
        status,
        location,
        gender,
        minWeight,
        maxWeight,
        search,
        sortBy,
        sortOrder
      } = req.query;

      const options = {
        page: parseInt(page) || 1,
        limit: parseInt(limit) || 10,
        animalType,
        status,
        location,
        gender,
        minWeight: minWeight ? parseFloat(minWeight) : undefined,
        maxWeight: maxWeight ? parseFloat(maxWeight) : undefined,
        search,
        sortBy,
        sortOrder
      };

      const result = await Animal.findAll(options);

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting all animals:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animals',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalById: async (req, res) => {
    try {
      const animal = await Animal.findById(req.params.id);
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      res.status(200).json({
        animal
      });
    } catch (error) {
      logger.error('Error getting animal by ID:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal by tag number
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalByTagNumber: async (req, res) => {
    try {
      const animal = await Animal.findByTagNumber(req.params.tagNumber);
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      res.status(200).json({
        animal
      });
    } catch (error) {
      logger.error('Error getting animal by tag number:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Update animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateAnimal: async (req, res) => {
    try {
      const {
        tagNumber,
        animalType,
        breed,
        gender,
        birthDate,
        weight,
        status,
        location,
        parentTagNumber,
        purchaseDate,
        purchasePrice,
        notes
      } = req.body;

      // Check if animal exists
      const animal = await Animal.findById(req.params.id);
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      // Check if tag number is taken by another animal
      if (tagNumber && tagNumber !== animal.tag_number) {
        const existingAnimal = await Animal.findByTagNumber(tagNumber);
        if (existingAnimal && existingAnimal.id !== parseInt(req.params.id)) {
          return res.status(400).json({
            error: {
              message: 'Tag number already exists',
              status: 400
            }
          });
        }
      }

      // Update animal
      const updatedAnimal = await Animal.update(req.params.id, {
        tagNumber,
        animalType,
        breed,
        gender,
        birthDate,
        weight,
        status,
        location,
        parentTagNumber,
        purchaseDate,
        purchasePrice,
        notes,
        updatedBy: req.user.id
      });

      res.status(200).json({
        message: 'Animal updated successfully',
        animal: updatedAnimal
      });
    } catch (error) {
      logger.error('Error updating animal:', error);
      res.status(500).json({
        error: {
          message: 'Error updating animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Delete animal
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteAnimal: async (req, res) => {
    try {
      // Check if animal exists
      const animal = await Animal.findById(req.params.id);
      if (!animal) {
        return res.status(404).json({
          error: {
            message: 'Animal not found',
            status: 404
          }
        });
      }

      // Delete animal
      const deleted = await Animal.delete(req.params.id);
      if (!deleted) {
        return res.status(500).json({
          error: {
            message: 'Failed to delete animal',
            status: 500
          }
        });
      }

      res.status(200).json({
        message: 'Animal deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting animal:', error);
      res.status(500).json({
        error: {
          message: 'Error deleting animal',
          details: error.message,
          status: 500
        }
      });
    }
  },

  /**
   * Get animal statistics
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAnimalStatistics: async (req, res) => {
    try {
      const statistics = await Animal.getStatistics();

      res.status(200).json({
        statistics
      });
    } catch (error) {
      logger.error('Error getting animal statistics:', error);
      res.status(500).json({
        error: {
          message: 'Error getting animal statistics',
          details: error.message,
          status: 500
        }
      });
    }
  }
};

module.exports = animalController;