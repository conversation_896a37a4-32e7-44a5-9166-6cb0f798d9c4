import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, SelectChangeEvent, Alert, Snackbar, Tabs, Tab, useTheme, alpha, Tooltip, CircularProgress, Checkbox as MuiCheckbox } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Person,
  Security,
  Refresh,
  Search,
  FilterList,
  MoreVert,
  Check,
  Close
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';
import { DashboardCard } from '../common';
import { User, UserFormData } from '../../types/user';

// Role descriptions
const roleDescriptions: Record<string, string> = {
  admin: 'Full access to all system features and settings',
  manager: 'Manage farm operations with limited administrative access',
  staff: 'Regular staff with access to daily operations',
  veterinarian: 'Specialized access to animal health records',
  viewer: 'Read-only access to view data without making changes'
};

// Status options
const statusOptions = [
  { value: 'active', label: 'Active', color: 'success' },
  { value: 'inactive', label: 'Inactive', color: 'default' },
  { value: 'suspended', label: 'Suspended', color: 'error' }
];

// Module permissions
const modulePermissions = [
  { name: 'dashboard', label: 'Dashboard', actions: ['view'] },
  { name: 'animals', label: 'Animals', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'health', label: 'Health', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'breeding', label: 'Breeding', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'feeding', label: 'Feeding', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'commercial', label: 'Commercial', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'financial', label: 'Financial', actions: ['view', 'create', 'edit', 'delete'] },
  { name: 'reports', label: 'Reports', actions: ['view', 'create', 'export'] },
  { name: 'settings', label: 'Settings', actions: ['view', 'edit'] },
  { name: 'users', label: 'Users', actions: ['view', 'create', 'edit', 'delete'] }
];

// Mock users data - will be replaced with API calls
const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    status: 'active',
    lastLogin: '2023-06-15T10:30:00'
  },
  {
    id: '2',
    username: 'manager',
    email: '<EMAIL>',
    firstName: 'Farm',
    lastName: 'Manager',
    role: 'manager',
    status: 'active',
    lastLogin: '2023-06-14T16:45:00'
  },
  {
    id: '3',
    username: 'staff',
    email: '<EMAIL>',
    firstName: 'Staff',
    lastName: 'Member',
    role: 'staff',
    status: 'active',
    lastLogin: '2023-06-13T09:15:00'
  },
  {
    id: '4',
    username: 'vet',
    email: '<EMAIL>',
    firstName: 'Veterinarian',
    lastName: 'Doctor',
    role: 'veterinarian',
    status: 'active',
    lastLogin: '2023-06-10T14:20:00'
  },
  {
    id: '5',
    username: 'viewer',
    email: '<EMAIL>',
    firstName: 'View',
    lastName: 'Only',
    role: 'viewer',
    status: 'inactive',
    lastLogin: '2023-06-08T11:10:00'
  }
];

/**
 * User Management Component
 * Allows administrators to manage users and their permissions
 */
const UserManagement: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [openPermissionsDialog, setOpenPermissionsDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<UserFormData>({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    role: 'viewer',
    status: 'active'
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // Filter users when search term changes
  useEffect(() => {
    if (searchTerm) {
      const filtered = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.role.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers(users);
    }
  }, [searchTerm, users]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle dialog open for adding new user
  const handleAddUser = () => {
    setSelectedUser(null);
    setFormData({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
      role: 'viewer',
      status: 'active'
    });
    setFormErrors({});
    setOpenDialog(true);
  };

  // Handle dialog open for editing user
  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      password: '',
      confirmPassword: '',
      role: user.role,
      status: user.status
    });
    setFormErrors({});
    setOpenDialog(true);
  };

  // Handle dialog open for managing permissions
  const handleManagePermissions = (user: User) => {
    setSelectedUser(user);
    setOpenPermissionsDialog(true);
  };

  // Handle dialog open for deleting user
  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setOpenDeleteDialog(true);
  };

  // Handle dialog close
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setOpenPermissionsDialog(false);
    setOpenDeleteDialog(false);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }> | SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name as string]: value
    }));

    // Clear error for this field
    if (formErrors[name as string]) {
      setFormErrors(prev => ({
        ...prev,
        [name as string]: ''
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.username) errors.username = 'Username is required';
    if (!formData.email) errors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) errors.email = 'Email is invalid';
    if (!formData.firstName) errors.firstName = 'First name is required';
    if (!formData.lastName) errors.lastName = 'Last name is required';

    // Password validation only for new users or when changing password
    if (!selectedUser || formData.password) {
      if (!selectedUser && !formData.password) errors.password = 'Password is required';
      else if (formData.password && formData.password.length < 6) errors.password = 'Password must be at least 6 characters';
      if (formData.password !== formData.confirmPassword) errors.confirmPassword = 'Passwords do not match';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submit
  const handleSubmit = () => {
    if (!validateForm()) return;

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      if (selectedUser) {
        // Update existing user
        const updatedUsers = users.map(u =>
          u.id === selectedUser.id ? {
            ...u,
            username: formData.username,
            email: formData.email,
            firstName: formData.firstName,
            lastName: formData.lastName,
            role: formData.role as User['role'],
            status: formData.status as User['status'],
            id: u.id
          } : u
        );
        setUsers(updatedUsers);
        setSnackbar({
          open: true,
          message: 'User updated successfully',
          severity: 'success'
        });
      } else {
        // Add new user
        const newId = (Math.max(...users.map(u => parseInt(u.id))) + 1).toString();
        const newUser: User = {
          id: newId,
          username: formData.username,
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          role: formData.role as User['role'],
          status: formData.status as User['status'],
          lastLogin: ''
        };
        setUsers([...users, newUser]);
        setSnackbar({
          open: true,
          message: 'User added successfully',
          severity: 'success'
        });
      }

      setLoading(false);
      handleCloseDialog();
    }, 1000);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (!selectedUser) return;

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      const updatedUsers = users.filter(u => u.id !== selectedUser.id);
      setUsers(updatedUsers);
      setSnackbar({
        open: true,
        message: 'User deleted successfully',
        severity: 'success'
      });

      setLoading(false);
      handleCloseDialog();
    }, 1000);
  };

  // Handle permission change
  const handlePermissionChange = (module: string, action: string, checked: boolean) => {
    if (!selectedUser) return;

    setSelectedUser(prev => {
      if (!prev) return null;

      const updatedPermissions = prev.permissions ? { ...prev.permissions } : {};

      if (!updatedPermissions[module]) {
        updatedPermissions[module] = {};
      }

      updatedPermissions[module][action] = checked;

      return {
        ...prev,
        permissions: updatedPermissions
      };
    });
  };

  // Handle save permissions
  const handleSavePermissions = () => {
    if (!selectedUser) return;

    setLoading(true);

    // Simulate API call
    setTimeout(() => {
      const updatedUsers = users.map(u =>
        u.id === selectedUser.id ? { ...u, permissions: selectedUser.permissions } : u
      );
      setUsers(updatedUsers);
      setSnackbar({
        open: true,
        message: 'Permissions updated successfully',
        severity: 'success'
      });

      setLoading(false);
      handleCloseDialog();
    }, 1000);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';

    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Check if user has permission
  const hasPermission = (module: string, action: string) => {
    if (!selectedUser || !selectedUser.permissions) return false;

    return selectedUser.permissions[module]?.[action] || false;
  };

  // Render role badge
  const renderRoleBadge = (role: string) => {
    let color: 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning' | 'default' = 'default';

    switch (role) {
      case 'admin':
        color = 'primary';
        break;
      case 'manager':
        color = 'secondary';
        break;
      case 'staff':
        color = 'info';
        break;
      case 'veterinarian':
        color = 'success';
        break;
      case 'viewer':
        color = 'default';
        break;
    }

    return (
      <Chip
        label={role.charAt(0).toUpperCase() + role.slice(1)}
        color={color}
        size="small"
        sx={{ fontWeight: 'medium' }}
      />
    );
  };

  // Render status badge
  const renderStatusBadge = (status: string) => {
    let color: 'success' | 'error' | 'default' = 'default';

    switch (status) {
      case 'active':
        color = 'success';
        break;
      case 'inactive':
        color = 'default';
        break;
      case 'suspended':
        color = 'error';
        break;
    }

    return (
      <Chip
        label={status.charAt(0).toUpperCase() + status.slice(1)}
        color={color}
        size="small"
        variant="outlined"
        sx={{ fontWeight: 'medium' }}
      />
    );
  };

  return (
    <Box sx={{ mb: 4 }}>
      <DashboardCard
        title="User Management"
        subtitle="Manage system users and their permissions"
        icon={<Person />}
        showSearch
        onSearch={() => {}}
        showFilter
        onFilter={() => {}}
      >
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <TextField
              placeholder="Search users..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ color: 'action.active', mr: 1 }} />,
              }}
              sx={{ width: 300 }}
            />

            <CustomButton
              variant="contained"
              startIcon={<Add />}
              onClick={handleAddUser}
              disabled={user?.role !== 'admin'}
            >
              Add User
            </CustomButton>
          </Box>

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
          >
            <Tab label="All Users" />
            <Tab label="Admins" />
            <Tab label="Managers" />
            <Tab label="Staff" />
            <Tab label="Inactive" />
          </Tabs>

          <TableContainer
            component={Paper}
            sx={{
              borderRadius: '12px',
              boxShadow: 'none',
              border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
              overflow: 'hidden'
            }}
          >
            <Table>
              <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Username</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Name</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Email</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Role</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Last Login</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>{user.username}</TableCell>
                    <TableCell>{`${user.firstName} ${user.lastName}`}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>{renderRoleBadge(user.role)}</TableCell>
                    <TableCell>{renderStatusBadge(user.status)}</TableCell>
                    <TableCell>{formatDate(user.lastLogin)}</TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex' }}>
                        <Tooltip title="Edit User">
                          <IconButton
                            size="small"
                            onClick={() => handleEditUser(user)}
                            disabled={user?.role !== 'admin'}
                          >
                            <Edit fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Manage Permissions">
                          <IconButton
                            size="small"
                            onClick={() => handleManagePermissions(user)}
                            disabled={user?.role !== 'admin'}
                          >
                            <Security fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title="Delete User">
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteUser(user)}
                            disabled={user?.role !== 'admin'}
                          >
                            <Delete fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </DashboardCard>

      {/* Add/Edit User Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
            <TextField
              label="Username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.username}
              helperText={formErrors.username}
              required
            />

            <TextField
              label="Email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.email}
              helperText={formErrors.email}
              required
            />

            <TextField
              label="First Name"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.firstName}
              helperText={formErrors.firstName}
              required
            />

            <TextField
              label="Last Name"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.lastName}
              helperText={formErrors.lastName}
              required
            />

            <TextField
              label="Password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.password}
              helperText={formErrors.password || (selectedUser ? 'Leave blank to keep current password' : '')}
              required={!selectedUser}
            />

            <TextField
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              fullWidth
              error={!!formErrors.confirmPassword}
              helperText={formErrors.confirmPassword}
              required={!selectedUser || !!formData.password}
            />

            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                label="Role"
              >
                <MenuItem value="admin">Admin</MenuItem>
                <MenuItem value="manager">Manager</MenuItem>
                <MenuItem value="staff">Staff</MenuItem>
                <MenuItem value="veterinarian">Veterinarian</MenuItem>
                <MenuItem value="viewer">Viewer</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                label="Status"
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="suspended">Suspended</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              {roleDescriptions[formData.role]}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSubmit}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Saving...' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Manage Permissions Dialog */}
      <Dialog open={openPermissionsDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          Manage Permissions for {selectedUser?.firstName} {selectedUser?.lastName}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Role: {renderRoleBadge(selectedUser?.role || 'viewer')}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {roleDescriptions[selectedUser?.role || 'viewer']}
            </Typography>

            <Alert severity="info" sx={{ my: 2 }}>
              Permissions are automatically set based on the user's role. You can customize them below.
            </Alert>

            <TableContainer component={Paper} sx={{ mt: 2, borderRadius: '12px', boxShadow: 'none', border: `1px solid ${alpha(theme.palette.divider, 0.5)}` }}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Module</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>View</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Create</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Edit</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Delete</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Export</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {modulePermissions.map((module) => (
                    <TableRow key={module.name} hover>
                      <TableCell sx={{ fontWeight: 'medium' }}>{module.label}</TableCell>
                      <TableCell>
                        {module.actions.includes('view') && (
                          <Checkbox
                            checked={hasPermission(module.name, 'view')}
                            onChange={(e) => handlePermissionChange(module.name, 'view', e.target.checked)}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        {module.actions.includes('create') && (
                          <Checkbox
                            checked={hasPermission(module.name, 'create')}
                            onChange={(e) => handlePermissionChange(module.name, 'create', e.target.checked)}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        {module.actions.includes('edit') && (
                          <Checkbox
                            checked={hasPermission(module.name, 'edit')}
                            onChange={(e) => handlePermissionChange(module.name, 'edit', e.target.checked)}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        {module.actions.includes('delete') && (
                          <Checkbox
                            checked={hasPermission(module.name, 'delete')}
                            onChange={(e) => handlePermissionChange(module.name, 'delete', e.target.checked)}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        {module.actions.includes('export') && (
                          <Checkbox
                            checked={hasPermission(module.name, 'export')}
                            onChange={(e) => handlePermissionChange(module.name, 'export', e.target.checked)}
                          />
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleSavePermissions}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Saving...' : 'Save Permissions'}
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={openDeleteDialog} onClose={handleCloseDialog}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the user <strong>{selectedUser?.username}</strong>?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton
            onClick={handleDeleteConfirm}
            variant="contained"
            color="error"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Deleting...' : 'Delete'}
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

// Custom Checkbox component
const Checkbox: React.FC<{ checked: boolean; onChange: (e: React.ChangeEvent<HTMLInputElement>) => void }> = ({ checked, onChange }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
    >
      <MuiCheckbox
        checked={checked}
        onChange={onChange}
        color="primary"
        icon={<Close fontSize="small" color="disabled" />}
        checkedIcon={<Check fontSize="small" color="success" />}
      />
    </Box>
  );
};

export default UserManagement;
