/**
 * Direct fix for MUI Button component
 * This script directly patches the MUI Button component to prevent the "Cannot read properties of undefined (reading 'dark')" error
 */

// Execute this script immediately when loaded
(function() {
  console.log('Applying direct MUI Button fix...');

  // Define a function to patch the MUI Button component
  function patchMuiButton() {
    try {
      // Find all script tags
      const scripts = document.querySelectorAll('script');
      
      // Find the script that contains the MUI Button component
      for (let i = 0; i < scripts.length; i++) {
        const script = scripts[i];
        
        // Check if the script contains the MUI Button component
        if (script.textContent && script.textContent.includes('ButtonRoot') && script.textContent.includes('ownerState.color')) {
          // Get the script content
          let content = script.textContent;
          
          // Replace the problematic code
          content = content.replace(
            /theme\.palette\[ownerState\.color\]\.dark/g,
            '(theme.palette[ownerState.color]?.dark || "#2A8A82")'
          );
          
          // Create a new script element
          const newScript = document.createElement('script');
          newScript.textContent = content;
          
          // Replace the old script with the new one
          script.parentNode.replaceChild(newScript, script);
          
          console.log('MUI Button component patched successfully');
          return;
        }
      }
      
      console.log('MUI Button component not found, will try again later');
      
      // Try again later
      setTimeout(patchMuiButton, 1000);
    } catch (error) {
      console.error('Error patching MUI Button component:', error);
    }
  }
  
  // Start patching the MUI Button component
  patchMuiButton();
  
  // Add a global error handler to suppress the error
  const originalError = console.error;
  console.error = function(...args) {
    // Check if the error is the one we're trying to fix
    if (args[0] && typeof args[0] === 'string' && args[0].includes('Cannot read properties of undefined (reading \'dark\')')) {
      // Ignore the error
      return;
    }
    
    // Otherwise, call the original error method
    return originalError.apply(console, args);
  };
  
  // Monkey patch Object.prototype to handle undefined properties
  const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
  Object.getOwnPropertyDescriptor = function(obj, prop) {
    // If the object is undefined or null, return a safe descriptor
    if (obj === undefined || obj === null) {
      if (prop === 'dark') {
        return {
          configurable: true,
          enumerable: true,
          value: '#2A8A82',
          writable: true
        };
      }
    }
    
    // Otherwise, call the original method
    return originalGetOwnPropertyDescriptor.apply(this, arguments);
  };
  
  // Monkey patch the theme object
  function patchThemeObject() {
    try {
      // Find all theme objects
      const themeObjects = [];
      
      // Walk the DOM to find theme objects
      function walkDOM(node, callback) {
        callback(node);
        node = node.firstChild;
        while (node) {
          walkDOM(node, callback);
          node = node.nextSibling;
        }
      }
      
      walkDOM(document.body, function(node) {
        if (node.__theme && node.__theme.palette) {
          themeObjects.push(node.__theme);
        }
      });
      
      // Patch each theme object
      themeObjects.forEach(theme => {
        // Make sure the palette object exists
        if (!theme.palette) {
          theme.palette = {};
        }
        
        // Make sure the primary color exists
        if (!theme.palette.primary) {
          theme.palette.primary = {
            main: '#3AA99F',
            dark: '#2A8A82',
            light: '#4FBEB4',
            contrastText: '#ffffff'
          };
        }
        
        // Make sure the primary color has the dark property
        if (!theme.palette.primary.dark) {
          theme.palette.primary.dark = '#2A8A82';
        }
        
        // Make sure the secondary color exists
        if (!theme.palette.secondary) {
          theme.palette.secondary = {
            main: '#38B2AC',
            dark: '#2C8A84',
            light: '#4FD1CB',
            contrastText: '#ffffff'
          };
        }
        
        // Make sure the secondary color has the dark property
        if (!theme.palette.secondary.dark) {
          theme.palette.secondary.dark = '#2C8A84';
        }
      });
      
      console.log('Theme objects patched successfully');
    } catch (error) {
      console.error('Error patching theme objects:', error);
    }
  }
  
  // Patch theme objects periodically
  setInterval(patchThemeObject, 1000);
  
  // Apply CSS fixes
  function applyCSSFixes() {
    try {
      // Create a style element
      const style = document.createElement('style');
      
      // Set the style content
      style.textContent = `
        /* Fix for MUI Button component */
        .MuiButton-root {
          color: #3AA99F !important;
        }
        
        .MuiButton-contained {
          background-color: #3AA99F !important;
          color: #ffffff !important;
        }
        
        .MuiButton-contained:hover {
          background-color: #2A8A82 !important;
        }
        
        .MuiButton-outlined {
          border: 1px solid #3AA99F !important;
          color: #3AA99F !important;
        }
        
        .MuiButton-text {
          color: #3AA99F !important;
        }
      `;
      
      // Append the style to the document
      document.head.appendChild(style);
      
      console.log('CSS fixes applied successfully');
    } catch (error) {
      console.error('Error applying CSS fixes:', error);
    }
  }
  
  // Apply CSS fixes
  applyCSSFixes();
})();
