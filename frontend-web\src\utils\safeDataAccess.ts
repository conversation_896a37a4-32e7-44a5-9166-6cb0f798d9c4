/**
 * Safely access nested properties in an object without throwing errors
 * @param obj The object to access
 * @param path The path to the property, using dot notation (e.g., 'user.address.city')
 * @param defaultValue The default value to return if the property doesn't exist
 * @returns The value at the specified path, or the default value if the path doesn't exist
 */
export function safeGet<T>(obj: any, path: string, defaultValue: T): T {
  if (!obj || !path) return defaultValue;
  
  const keys = path.split('.');
  let result = obj;
  
  for (const key of keys) {
    if (result === null || result === undefined || typeof result !== 'object') {
      return defaultValue;
    }
    result = result[key];
  }
  
  return (result === undefined || result === null) ? defaultValue : result as T;
}

/**
 * Safely access an array element without throwing errors
 * @param arr The array to access
 * @param index The index to access
 * @param defaultValue The default value to return if the index doesn't exist
 * @returns The value at the specified index, or the default value if the index doesn't exist
 */
export function safeArrayGet<T>(arr: T[] | null | undefined, index: number, defaultValue: T): T {
  if (!arr || !Array.isArray(arr) || index < 0 || index >= arr.length) {
    return defaultValue;
  }
  return arr[index] ?? defaultValue;
}

/**
 * Safely parse JSON without throwing errors
 * @param jsonString The JSON string to parse
 * @param defaultValue The default value to return if parsing fails
 * @returns The parsed JSON object, or the default value if parsing fails
 */
export function safeJsonParse<T>(jsonString: string | null | undefined, defaultValue: T): T {
  if (!jsonString) return defaultValue;
  
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.warn('Failed to parse JSON:', error);
    return defaultValue;
  }
}

/**
 * Safely convert a value to a number without throwing errors
 * @param value The value to convert
 * @param defaultValue The default value to return if conversion fails
 * @returns The converted number, or the default value if conversion fails
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  if (value === null || value === undefined || value === '') return defaultValue;
  
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * Safely convert a value to a string without throwing errors
 * @param value The value to convert
 * @param defaultValue The default value to return if conversion fails
 * @returns The converted string, or the default value if conversion fails
 */
export function safeString(value: any, defaultValue: string = ''): string {
  if (value === null || value === undefined) return defaultValue;
  
  try {
    return String(value);
  } catch (error) {
    return defaultValue;
  }
}

/**
 * Safely call a function without throwing errors
 * @param fn The function to call
 * @param args The arguments to pass to the function
 * @param defaultValue The default value to return if the function throws an error
 * @returns The result of the function call, or the default value if the function throws an error
 */
export function safeCall<T, Args extends any[]>(
  fn: ((...args: Args) => T) | undefined | null,
  args: Args,
  defaultValue: T
): T {
  if (!fn || typeof fn !== 'function') return defaultValue;
  
  try {
    return fn(...args);
  } catch (error) {
    console.warn('Function call failed:', error);
    return defaultValue;
  }
}

export default {
  safeGet,
  safeArrayGet,
  safeJsonParse,
  safeNumber,
  safeString,
  safeCall
};
