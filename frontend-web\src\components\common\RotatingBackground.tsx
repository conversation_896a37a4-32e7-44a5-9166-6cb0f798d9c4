import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { alpha } from '@mui/material/styles';

interface RotatingBackgroundProps {
  images: string[];
  interval?: number; // in milliseconds
  primaryColor?: string;
  secondaryColor?: string;
  opacity?: number;
  children?: React.ReactNode;
  sx?: React.CSSProperties;
}

/**
 * A component that rotates through background images at a specified interval
 */
const RotatingBackground: React.FC<RotatingBackgroundProps> = ({
  images,
  interval = 10000, // default to 10 seconds
  primaryColor = '#3AA99F',
  secondaryColor = '#2A8A82',
  opacity = 0.8,
  children,
  sx = {},
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    // Only set up rotation if there are multiple images
    if (images.length <= 1) return;

    const timer = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, interval);

    return () => clearInterval(timer);
  }, [images, interval]);

  const currentImage = images[currentImageIndex];

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
        backgroundImage: `linear-gradient(135deg, ${alpha(primaryColor, opacity)}, ${alpha(secondaryColor, opacity)}), url(${currentImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        transition: 'background-image 1s ease-in-out',
        ...sx,
      }}
    >
      {children}
    </Box>
  );
};

export default RotatingBackground;
