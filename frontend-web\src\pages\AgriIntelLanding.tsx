import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Typo<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Box,
  Chip,
  Rating,
  CircularProgress,
  Divider,
  Tabs,
  Tab
} from '@mui/material';
import {
  <PERSON><PERSON>rrow,
  ArrowForward,
  Pets,
  LocalHospital,
  TrendingUp,
  CheckCircle,
  Phone,
  Email
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import NeuralNetworkBackground from '../components/effects/NeuralNetworkBackground';
import HolographicCard from '../components/effects/HolographicCard';
import EnhancedLanguageSelector from '../components/common/EnhancedLanguageSelector';
import AgriIntelLogo from '../components/common/AgriIntelLogo';
import { useLanguage } from '../contexts/LanguageContext';

// Import new landing page sections
import TestimonialsSection from '../components/landing/TestimonialsSection';
import FAQSection from '../components/landing/FAQSection';
import TrustSignalsSection from '../components/landing/TrustSignalsSection';

// Import futuristic 2050 styles
import '../styles/clean-landing.css';
import '../styles/futuristic-landing.css';
import '../styles/tabbed-landing.css';

// TabPanel component for tab content
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`agriintel-tabpanel-${index}`}
      aria-labelledby={`agriintel-tab-${index}`}
      style={{
        width: '100%',
        minHeight: '80vh',
        position: 'relative',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'scroll'
      }}
      {...other}
    >
      {value === index && (
        <Box sx={{
          py: 6,
          px: 3,
          width: '100%',
          minHeight: '80vh',
          position: 'relative',
          zIndex: 2,
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.3)',
            zIndex: -1,
            pointerEvents: 'none'
          }
        }}>
          <Container maxWidth="xl">
            {children}
          </Container>
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `agriintel-tab-${index}`,
    'aria-controls': `agriintel-tabpanel-${index}`,
  };
}

const AgriIntelLanding: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const { translate } = useLanguage();



  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handlePlanSelect = async (plan: string) => {
    setIsLoading(true);
    setLoadingPlan(plan);

    try {
      await new Promise(resolve => setTimeout(resolve, 1500));

      if (plan === 'beta') {
        navigate('/login?tier=beta');
      } else if (plan === 'professional') {
        navigate('/login?tier=professional');
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsLoading(false);
      setLoadingPlan(null);
    }
  };

  return (
    <main className="agri-landing-main" role="main">
      {/* Neural Network Background */}
      <NeuralNetworkBackground
        nodeCount={60}
        connectionDistance={120}
        animationSpeed={0.3}
        opacity={0.4}
      />

      {/* Fixed Header with Logo and Language Selector */}
      <motion.header
        className="agri-header"
        role="banner"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Container maxWidth="xl">
          <div className="agri-header-container">
            <div className="agri-header-brand">
              <AgriIntelLogo
                variant="full"
                size="extra-large"
                theme="professional"
                showTagline={true}
                orientation="horizontal"
              />
            </div>

            <div className="agri-header-center">
              <Typography variant="h6" component="p" className="agri-tagline">
                Smart Farming, Smart Decisions
              </Typography>
            </div>

            <div className="agri-header-actions">
              <EnhancedLanguageSelector
                variant="futuristic"
                showLabel={false}
                showFlag={true}
                size="medium"
              />
              <Chip
                label="Production Ready"
                className="agri-status-badge"
                size="small"
                aria-label="Application status: Production ready"
                sx={{ ml: 2 }}
              />
            </div>
          </div>
        </Container>
      </motion.header>

      {/* FIXED Tabbed Navigation */}
      <Box sx={{
        borderBottom: 2,
        borderColor: '#4CAF50',
        bgcolor: 'rgba(255, 255, 255, 0.98)',
        backdropFilter: 'blur(15px)',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
      }}>
        <Container maxWidth="xl">
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="AgriIntel navigation tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minWidth: 140,
                fontWeight: 700,
                fontSize: '1.1rem',
                textTransform: 'none',
                color: '#2E7D32',
                padding: '16px 24px',
                '&.Mui-selected': {
                  color: '#1565C0',
                  backgroundColor: 'rgba(76, 175, 80, 0.1)',
                },
                '&:hover': {
                  backgroundColor: 'rgba(46, 125, 50, 0.05)',
                }
              },
              '& .MuiTabs-indicator': {
                background: 'linear-gradient(90deg, #1565C0, #4CAF50, #FF9800)',
                height: 4,
              },
            }}
          >
            <Tab label="🏠 Home" {...a11yProps(0)} />
            <Tab label="⚡ Features" {...a11yProps(1)} />
            <Tab label="💬 Testimonials" {...a11yProps(2)} />
            <Tab label="💰 Pricing" {...a11yProps(3)} />
            <Tab label="🔒 Trust & Security" {...a11yProps(4)} />
            <Tab label="❓ FAQ" {...a11yProps(5)} />
            <Tab label="📞 Contact" {...a11yProps(6)} />
          </Tabs>
        </Container>
      </Box>

      {/* FIXED Tab Content - Full Width */}
      <Box sx={{ width: '100%' }}>
        {/* Home Tab */}
        <TabPanel value={tabValue} index={0}>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="agri-hero-content"
          >
            <Typography
              variant="h1"
              component="h1"
              className="agri-hero-title"
              id="hero-title"
            >
              {translate('landing.hero.title', { fallback: 'AgriIntel' })}
              <br />
              <span className="agri-hero-accent">
                {translate('landing.hero.subtitle', { fallback: 'Smart Farming, Smart Decisions' })}
              </span>
            </Typography>

            <Typography
              variant="h5"
              component="p"
              className="agri-hero-subtitle"
            >
              {translate('landing.hero.description', {
                fallback: 'Professional livestock management platform for South African farmers. Track animals, monitor health, manage feeding, and optimize farm operations.'
              })}
            </Typography>

            {/* Professional CTA Buttons */}
            <div className="agri-hero-actions" role="group" aria-label="Plan selection">
              <Button
                variant="contained"
                size="large"
                className="agri-cta-primary"
                onClick={() => handlePlanSelect('beta')}
                disabled={isLoading}
                startIcon={loadingPlan === 'beta' ? <CircularProgress size={20} color="inherit" /> : <PlayArrow />}
                type="button"
                aria-describedby="beta-plan-description"
              >
                {loadingPlan === 'beta' ? translate('common.loading', { fallback: 'Loading...' }) : translate('landing.cta.free_trial', { fallback: 'Start Free Trial' })}
              </Button>
              <Button
                variant="outlined"
                size="large"
                className="agri-cta-secondary"
                onClick={() => handlePlanSelect('professional')}
                disabled={isLoading}
                endIcon={loadingPlan === 'professional' ? <CircularProgress size={20} color="inherit" /> : <ArrowForward />}
                type="button"
                aria-describedby="pro-plan-description"
              >
                {loadingPlan === 'professional' ? translate('common.loading', { fallback: 'Loading...' }) : translate('landing.cta.professional', { fallback: 'Go Professional - R699/month' })}
              </Button>
            </div>

            {/* Trust Indicators with Accessibility */}
            <div className="agri-hero-trust" role="region" aria-label="Customer testimonials">
              <Rating
                value={5}
                readOnly
                size="large"
                aria-label="5 out of 5 stars customer rating"
              />
              <Typography variant="h6" component="p" className="agri-trust-text">
                4.9/5 from 2,500+ verified South African farmers
              </Typography>
            </div>

            {/* Hidden descriptions for screen readers */}
            <div className="sr-only">
              <p id="beta-plan-description">
                Start with our free 30-day BETA trial including basic livestock management features
              </p>
              <p id="pro-plan-description">
                Upgrade to Professional plan for R699 per month with full AI features and marketplace access
              </p>
            </div>
          </motion.div>
        </TabPanel>

        {/* Features Tab */}
        <TabPanel value={tabValue} index={1}>
          <motion.div
            className="agri-section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography
              variant="h2"
              component="h2"
              className="agri-section-title"
              id="features-title"
            >
              Everything You Need for Smart Livestock Management
            </Typography>
            <Typography
              variant="h6"
              component="p"
              className="agri-section-subtitle"
            >
              Comprehensive tools designed specifically for South African farmers
            </Typography>
          </motion.div>

          <Grid container spacing={4} className="agri-features-grid">
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <HolographicCard
                  glowColor="#00D4FF"
                  intensity={0.6}
                  borderGradient="linear-gradient(135deg, #00D4FF, #7C3AED)"
                >
                  <div className="agri-feature-icon" aria-hidden="true">
                    <Pets style={{ fontSize: '3rem', color: '#00D4FF' }} />
                  </div>
                  <Typography
                    variant="h5"
                    component="h3"
                    className="agri-feature-title"
                    sx={{ color: 'white', fontWeight: 700, mb: 2 }}
                  >
                    Animal Management
                  </Typography>
                  <Typography
                    variant="body1"
                    component="p"
                    className="agri-feature-description"
                    sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
                  >
                    Track individual animals, monitor breeding cycles, and manage livestock records with RFID technology and digital profiles.
                  </Typography>
                </HolographicCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <HolographicCard
                  glowColor="#10B981"
                  intensity={0.6}
                  borderGradient="linear-gradient(135deg, #10B981, #00FFFF)"
                >
                  <div className="agri-feature-icon" aria-hidden="true">
                    <LocalHospital style={{ fontSize: '3rem', color: '#10B981' }} />
                  </div>
                  <Typography
                    variant="h5"
                    component="h3"
                    className="agri-feature-title"
                    sx={{ color: 'white', fontWeight: 700, mb: 2 }}
                  >
                    Health Monitoring
                  </Typography>
                  <Typography
                    variant="body1"
                    component="p"
                    className="agri-feature-description"
                    sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
                  >
                    Monitor animal health with vaccination tracking, disease alerts, and veterinary integration for proactive care management.
                  </Typography>
                </HolographicCard>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={4}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <HolographicCard
                  glowColor="#F59E0B"
                  intensity={0.6}
                  borderGradient="linear-gradient(135deg, #F59E0B, #FF00FF)"
                >
                  <div className="agri-feature-icon" aria-hidden="true">
                    <TrendingUp style={{ fontSize: '3rem', color: '#F59E0B' }} />
                  </div>
                  <Typography
                    variant="h5"
                    component="h3"
                    className="agri-feature-title"
                    sx={{ color: 'white', fontWeight: 700, mb: 2 }}
                  >
                    Analytics & Reports
                  </Typography>
                  <Typography
                    variant="body1"
                    component="p"
                    className="agri-feature-description"
                    sx={{ color: 'rgba(255, 255, 255, 0.8)' }}
                  >
                    Generate comprehensive reports, track farm performance, and make data-driven decisions with advanced analytics tools.
                  </Typography>
                </HolographicCard>
              </motion.div>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Testimonials Tab */}
        <TabPanel value={tabValue} index={2}>
          <TestimonialsSection />
        </TabPanel>

        {/* Pricing Tab */}
        <TabPanel value={tabValue} index={3}>
        <Container maxWidth="lg" sx={{ py: { xs: 6, md: 8 } }}>
          <motion.div
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Typography variant="h2" className="section-title">
              Choose Your Plan
            </Typography>
            <Typography variant="h6" className="section-subtitle">
              Start free, upgrade when you're ready
            </Typography>
          </motion.div>

          <Grid container spacing={4} justifyContent="center" sx={{ mt: 4 }}>
            {/* BETA Plan */}
            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <Card className="pricing-card beta-card">
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Chip
                      label="FREE TRIAL"
                      sx={{
                        background: 'var(--agri-beta-primary)',
                        color: 'white',
                        fontWeight: 700,
                        mb: 2
                      }}
                    />
                    <Typography variant="h4" sx={{ color: 'var(--agri-white)', fontWeight: 800, mb: 1 }}>
                      BETA V1
                    </Typography>
                    <Typography variant="h2" sx={{ color: 'var(--agri-beta-primary)', fontWeight: 900, mb: 1 }}>
                      FREE
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 3 }}>
                      30-day free trial • Perfect for small farms
                    </Typography>
                    <Box sx={{ textAlign: 'left', mb: 3 }}>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        Up to 50 animals
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        5 core modules
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-beta-primary)', mr: 1, fontSize: '1rem' }} />
                        Basic reporting
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePlanSelect('beta')}
                      sx={{
                        background: 'var(--agri-beta-primary)',
                        '&:hover': { background: 'var(--agri-beta-secondary)' },
                        fontWeight: 700,
                        py: 1.5
                      }}
                    >
                      Start Free Trial
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>

            {/* Professional Plan */}
            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="pricing-card pro-card">
                  <CardContent sx={{ p: 4, textAlign: 'center' }}>
                    <Chip
                      label="MOST POPULAR"
                      sx={{
                        background: 'var(--agri-pro-primary)',
                        color: 'white',
                        fontWeight: 700,
                        mb: 2
                      }}
                    />
                    <Typography variant="h4" sx={{ color: 'var(--agri-white)', fontWeight: 800, mb: 1 }}>
                      Professional V1
                    </Typography>
                    <Typography variant="h2" sx={{ color: 'var(--agri-pro-primary)', fontWeight: 900, mb: 1 }}>
                      R699
                    </Typography>
                    <Typography variant="body1" sx={{ color: 'var(--agri-white)', opacity: 0.8, mb: 3 }}>
                      per month • Full access to all features
                    </Typography>
                    <Box sx={{ textAlign: 'left', mb: 3 }}>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        Unlimited animals
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        All 12 modules
                      </Typography>
                      <Typography variant="body2" sx={{ color: 'var(--agri-white)', mb: 1, display: 'flex', alignItems: 'center' }}>
                        <CheckCircle sx={{ color: 'var(--agri-pro-primary)', mr: 1, fontSize: '1rem' }} />
                        AI automation
                      </Typography>
                    </Box>
                    <Button
                      variant="contained"
                      fullWidth
                      size="large"
                      onClick={() => handlePlanSelect('professional')}
                      sx={{
                        background: 'var(--agri-pro-primary)',
                        '&:hover': { background: 'var(--agri-pro-secondary)' },
                        fontWeight: 700,
                        py: 1.5
                      }}
                    >
                      Go Professional
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
        </TabPanel>

        {/* Trust & Security Tab */}
        <TabPanel value={tabValue} index={4}>
          <TrustSignalsSection />
        </TabPanel>

        {/* FAQ Tab */}
        <TabPanel value={tabValue} index={5}>
          <FAQSection />
        </TabPanel>

        {/* Contact Tab */}
        <TabPanel value={tabValue} index={6}>
          <Box sx={{ py: 4 }}>
            <Typography variant="h2" component="h2" sx={{ mb: 4, textAlign: 'center', color: '#2E7D32' }}>
              Get in Touch
            </Typography>
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3, height: '100%' }}>
                  <Typography variant="h5" sx={{ mb: 2, color: '#2E7D32' }}>
                    Contact Information
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Email sx={{ mr: 2, color: '#4CAF50' }} />
                    <Typography><EMAIL></Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Phone sx={{ mr: 2, color: '#4CAF50' }} />
                    <Typography>+27 (0) 79 448 4159</Typography>
                  </Box>
                  <Typography variant="h6" sx={{ mt: 3, mb: 1, color: '#2E7D32' }}>
                    Business Hours
                  </Typography>
                  <Typography>Monday - Friday: 8:00 AM - 5:00 PM</Typography>
                  <Typography>Saturday: 9:00 AM - 1:00 PM</Typography>
                  <Typography>Sunday: Closed</Typography>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ p: 3, height: '100%' }}>
                  <Typography variant="h5" sx={{ mb: 2, color: '#2E7D32' }}>
                    Enterprise Support
                  </Typography>
                  <Typography sx={{ mb: 2 }}>
                    For enterprise solutions and custom implementations, contact our WhatsApp support:
                  </Typography>
                  <Button
                    variant="contained"
                    sx={{
                      bgcolor: '#25D366',
                      '&:hover': { bgcolor: '#128C7E' },
                      mb: 2
                    }}
                    startIcon={<Phone />}
                  >
                    WhatsApp: ************
                  </Button>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Available for immediate assistance with enterprise inquiries, technical support, and custom integrations.
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>
      </Box>
      </Container>

      {/* Simple Footer */}
      <footer className="landing-footer">
        <Container maxWidth="xl">
          <Grid container spacing={6}>
            <Grid item xs={12} md={4}>
              <div className="footer-brand">
                <Typography variant="h5" className="footer-brand-title">
                  AgriIntel
                </Typography>
                <Typography variant="body1" className="footer-description">
                  Transforming South African agriculture through intelligent livestock management solutions.
                </Typography>
                <div className="footer-contact">
                  <div className="contact-item">
                    <Email fontSize="small" />
                    <Typography variant="body2"><EMAIL></Typography>
                  </div>
                  <div className="contact-item">
                    <Phone fontSize="small" />
                    <Typography variant="body2">+27 (0) 79 448 4159</Typography>
                  </div>
                  <div className="contact-item">
                    <Typography variant="body2" sx={{ fontSize: '0.9rem', fontWeight: 500 }}>
                      🌍 Serving farmers across South Africa since 2023
                    </Typography>
                  </div>
                </div>
              </div>
            </Grid>

            <Grid item xs={12} md={4}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Platform
                </Typography>
                <ul className="footer-links">
                  <li><a href="#features">Features</a></li>
                  <li><a href="#pricing">Pricing</a></li>
                  <li><a href="/help">Help Center</a></li>
                  <li><a href="/contact">Contact Us</a></li>
                </ul>
              </div>
            </Grid>

            <Grid item xs={12} md={4}>
              <div className="footer-section">
                <Typography variant="h6" className="footer-title">
                  Connect
                </Typography>
                <div className="footer-cta">
                  <Button
                    variant="contained"
                    size="small"
                    className="footer-cta-button"
                    onClick={() => handlePlanSelect('beta')}
                    startIcon={<PlayArrow />}
                    type="button"
                    aria-label="Start your free trial from footer"
                  >
                    Start Free Trial
                  </Button>
                </div>
              </div>
            </Grid>
          </Grid>

          <Divider className="footer-divider" />

          <div className="footer-bottom">
            <Typography variant="body2" className="footer-copyright">
              © 2025 AgriIntel Technologies (Pty) Ltd. All rights reserved. Proudly South African.
            </Typography>
            <div className="footer-badges">
              <Chip
                label="ISO 27001:2022 Certified"
                size="small"
                className="footer-badge"
                sx={{
                  background: 'linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%)',
                  color: 'white',
                  fontWeight: 600
                }}
              />
              <Chip
                label="GDPR & POPIA Compliant"
                size="small"
                className="footer-badge"
                sx={{
                  background: 'linear-gradient(135deg, #1565C0 0%, #0D47A1 100%)',
                  color: 'white',
                  fontWeight: 600
                }}
              />
              <Chip
                label="AI Ethics Certified"
                size="small"
                className="footer-badge"
                sx={{
                  background: 'linear-gradient(135deg, #F57C00 0%, #E65100 100%)',
                  color: 'white',
                  fontWeight: 600
                }}
              />
            </div>
          </div>
        </Container>
      </footer>
    </main>
  );
};

export default AgriIntelLanding;
