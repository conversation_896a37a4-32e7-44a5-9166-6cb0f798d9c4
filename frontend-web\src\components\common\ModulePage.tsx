/**
 * Standardized Module Page Component
 * 
 * This component provides a consistent page layout for all modules
 * with integrated CRUD functionality.
 */

import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, CircularProgress, Alert, Grid, useTheme, alpha, Divider, Tabs, Tab } from '@mui/material';
import { Add, Refresh } from '../../utils/iconImports';
import { useTranslation } from '../../hooks/useTranslation';
import { BaseEntity } from '../../services/moduleService';
import DataTable, { Column } from './DataTable';
import StandardForm, { FormField } from './StandardForm';
import { Mo<PERSON>leContainer, ModuleHeader } from './index';

interface ModulePageProps<T extends BaseEntity> {
  title: string;
  subtitle?: string;
  module: string;
  submodule?: string;
  data: T[];
  loading: boolean;
  error: string | null;
  columns: Column<T>[];
  formFields: FormField<T>[];
  onRefresh: () => void;
  onCreate: (data: Omit<T, 'id'>) => Promise<T>;
  onUpdate: (id: string, data: Partial<T>) => Promise<T>;
  onDelete: (id: string) => Promise<void>;
  validateForm?: (data: Partial<T>) => Record<string, string>;
  initialFormData: Partial<T>;
  itemName: string;
  tabs?: {
    label: string;
    content: React.ReactNode;
  }[];
  renderStats?: () => React.ReactNode;
  renderFilters?: () => React.ReactNode;
  renderCustomActions?: () => React.ReactNode;
  disableAdd?: boolean;
  disableEdit?: boolean;
  disableDelete?: boolean;
  searchFields?: (keyof T)[];
}

function ModulePage<T extends BaseEntity>({
  title,
  subtitle,
  module,
  submodule,
  data,
  loading,
  error,
  columns,
  formFields,
  onRefresh,
  onCreate,
  onUpdate,
  onDelete,
  validateForm,
  initialFormData,
  itemName,
  tabs,
  renderStats,
  renderFilters,
  renderCustomActions,
  disableAdd = false,
  disableEdit = false,
  disableDelete = false,
  searchFields
}: ModulePageProps<T>) {
  const theme = useTheme();
  const { translate } = useTranslation();
  const [formOpen, setFormOpen] = useState(false);
  const [formData, setFormData] = useState<Partial<T>>(initialFormData);
  const [editingItem, setEditingItem] = useState<T | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [tabValue, setTabValue] = useState(0);

  // Handle form field changes
  const handleChange = (field: keyof T, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field if it exists
    if (formErrors[field as string]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      });
    }
  };

  // Open form for creating new item
  const handleAddNew = () => {
    setFormData(initialFormData);
    setEditingItem(null);
    setFormErrors({});
    setFormOpen(true);
  };

  // Open form for editing existing item
  const handleEdit = (item: T) => {
    setFormData(item);
    setEditingItem(item);
    setFormErrors({});
    setFormOpen(true);
  };

  // Validate form and submit
  const handleSubmit = async () => {
    // Validate form if validation function is provided
    if (validateForm) {
      const errors = validateForm(formData);
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        return;
      }
    }

    try {
      if (editingItem) {
        // Update existing item
        await onUpdate(editingItem.id, formData);
      } else {
        // Create new item
        await onCreate(formData as Omit<T, 'id'>);
      }
      setFormOpen(false);
    } catch (err) {
      console.error(`Error saving ${itemName}:`, err);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <ModuleContainer>
      <ModuleHeader
        title={title}
        subtitle={subtitle}
        module={module}
        submodule={submodule}
        actionLabel={disableAdd ? undefined : `Add ${itemName}`}
        actionIcon={disableAdd ? undefined : <Add />}
        onAction={disableAdd ? undefined : handleAddNew}
      />

      {/* Stats section */}
      {renderStats && (
        <Box sx={{ mb: 4 }}>
          {renderStats()}
        </Box>
      )}

      {/* Filters section */}
      {renderFilters && (
        <Box sx={{ mb: 4 }}>
          {renderFilters()}
        </Box>
      )}

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      {tabs && tabs.length > 0 ? (
        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label={translate ? translate('common.list', { fallback: 'List' }) : 'List'} />
              {tabs.map((tab, index) => (
                <Tab key={index} label={tab.label} />
              ))}
            </Tabs>
          </Box>
          
          {/* List tab */}
          <Box role="tabpanel" hidden={tabValue !== 0}>
            {tabValue === 0 && (
              <Box sx={{ p: 2 }}>
                <DataTable
                  columns={columns}
                  rows={data}
                  loading={loading}
                  error={error}
                  onRefresh={onRefresh}
                  onEdit={disableEdit ? undefined : handleEdit}
                  onDelete={disableDelete ? undefined : onDelete}
                  searchFields={searchFields as string[]}
                  emptyMessage={`No ${itemName} records found`}
                />
              </Box>
            )}
          </Box>
          
          {/* Custom tabs */}
          {tabs.map((tab, index) => (
            <Box
              key={index}
              role="tabpanel"
              hidden={tabValue !== index + 1}
            >
              {tabValue === index + 1 && (
                <Box sx={{ p: 2 }}>
                  {tab.content}
                </Box>
              )}
            </Box>
          ))}
        </Box>
      ) : (
        // No tabs, just show the data table
        <Box sx={{ p: 2 }}>
          <DataTable
            columns={columns}
            rows={data}
            loading={loading}
            error={error}
            onRefresh={onRefresh}
            onEdit={disableEdit ? undefined : handleEdit}
            onDelete={disableDelete ? undefined : onDelete}
            searchFields={searchFields as string[]}
            emptyMessage={`No ${itemName} records found`}
          />
        </Box>
      )}

      {/* Form dialog */}
      {formOpen && (
        <Paper
          sx={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '90%',
            maxWidth: 800,
            maxHeight: '90vh',
            overflow: 'auto',
            p: 3,
            zIndex: 1300,
            boxShadow: theme.shadows[10],
            bgcolor: alpha(theme.palette.background.paper, 0.95),
            borderRadius: 2
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              {editingItem 
                ? `${translate ? translate('common.edit', { fallback: 'Edit' }) : 'Edit'} ${itemName}`
                : `${translate ? translate('common.add', { fallback: 'Add' }) : 'Add'} ${itemName}`
              }
            </Typography>
            <CustomButton onClick={() => setFormOpen(false)}>
              {translate ? translate('common.close', { fallback: 'Close' }) : 'Close'}
            </CustomButton>
          </Box>
          
          <Divider sx={{ mb: 3 }} />
          
          <StandardForm
            fields={formFields}
            values={formData}
            errors={formErrors}
            onChange={handleChange}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3, gap: 2 }}>
            <CustomButton 
              variant="outlined" 
              onClick={() => setFormOpen(false)}
            >
              {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
            </CustomButton>
            <CustomButton 
              variant="contained" 
              color="primary" 
              onClick={handleSubmit}
              disabled={loading}
            >
              {loading 
                ? (translate ? translate('common.saving', { fallback: 'Saving...' }) : 'Saving...') 
                : (translate ? translate('common.save', { fallback: 'Save' }) : 'Save')
              }
            </CustomButton>
          </Box>
        </Paper>
      )}
    </ModuleContainer>
  );
}

export default ModulePage;
