// Generate 12 months of data for analytics
const generateMonthlyData = (months = 12, baseValue: number, variance = 0.2) => {
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const currentMonth = new Date().getMonth();
  
  return {
    dates: Array.from({ length: months }, (_, i) => {
      const monthIndex = (currentMonth - months + i + 1 + 12) % 12;
      return monthNames[monthIndex];
    }),
    values: Array.from({ length: months }, () => {
      return Math.round(baseValue * (1 + (Math.random() * variance * 2 - variance)));
    })
  };
};

export const mockAnalyticsData = {
  productionMetrics: {
    dates: generateMonthlyData(12, 0).dates,
    milk: generateMonthlyData(12, 1300, 0.2).values,
    meat: generateMonthlyData(12, 550, 0.15).values,
    wool: generateMonthlyData(12, 320, 0.1).values,
  },
  financialMetrics: {
    dates: generateMonthlyData(12, 0).dates,
    revenue: generateMonthlyData(12, 55000, 0.15).values,
    expenses: generateMonthlyData(12, 32000, 0.1).values,
    profit: generateMonthlyData(12, 23000, 0.2).values,
  },
  healthMetrics: {
    cases: 150,
    recoveries: 140,
    vaccinations: 500,
    treatments: 180,
    monthlyTrends: {
      dates: generateMonthlyData(12, 0).dates,
      cases: generateMonthlyData(12, 15, 0.3).values,
      treatments: generateMonthlyData(12, 20, 0.25).values,
      vaccinations: generateMonthlyData(12, 45, 0.2).values,
    }
  },
  inventoryMetrics: {
    feed: 75,
    medicine: 45,
    equipment: 90,
    supplies: 60,
    monthlyUsage: {
      dates: generateMonthlyData(12, 0).dates,
      feed: generateMonthlyData(12, 500, 0.2).values,
      medicine: generateMonthlyData(12, 100, 0.3).values,
      equipment: generateMonthlyData(12, 50, 0.1).values,
    }
  },
  animalMetrics: {
    monthlyGrowth: {
      dates: generateMonthlyData(12, 0).dates,
      cattle: generateMonthlyData(12, 2.5, 0.1).values,
      sheep: generateMonthlyData(12, 1.8, 0.15).values,
      goats: generateMonthlyData(12, 1.5, 0.12).values,
    },
    monthlyMortality: {
      dates: generateMonthlyData(12, 0).dates,
      values: generateMonthlyData(12, 0.5, 0.5).values,
    }
  }
};

