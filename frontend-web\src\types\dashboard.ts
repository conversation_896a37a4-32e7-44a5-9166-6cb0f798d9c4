export interface DashboardModule {
  id: string;
  title: string;
  description: string;
  icon: string;
  path: string;
  subModules: SubModule[];
  stats: ModuleStats[];
}

export interface SubModule {
  id: string;
  title: string;
  description: string;
  icon: string;
  path: string;
  count?: number;
  status?: string;
}

export interface ModuleStats {
  label: string;
  value: number | string;
  icon: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}
