/**
 * This patch fixes the "Cannot read properties of undefined (reading 'dark')" error
 * by monkey-patching the MUI theme system to ensure required properties always exist.
 */

// Store the original createTheme function
const originalCreateTheme = require('@mui/material/styles').createTheme;

// Override the createTheme function to ensure all required properties exist
require('@mui/material/styles').createTheme = function(options = {}) {
  // Create a base theme with the original function
  const theme = originalCreateTheme(options);
  
  // Ensure palette exists
  if (!theme.palette) {
    theme.palette = {};
  }
  
  // Ensure primary palette exists
  if (!theme.palette.primary) {
    theme.palette.primary = {
      main: '#3AA99F',
      light: '#4FBEB4',
      dark: '#2A8A82',
      contrastText: '#ffffff'
    };
  } else {
    // Ensure primary.dark exists
    if (!theme.palette.primary.dark && theme.palette.primary.main) {
      theme.palette.primary.dark = theme.palette.primary.main;
    } else if (!theme.palette.primary.dark) {
      theme.palette.primary.dark = '#2A8A82';
    }
    
    // Ensure other primary properties exist
    if (!theme.palette.primary.light) {
      theme.palette.primary.light = theme.palette.primary.main || '#4FBEB4';
    }
    if (!theme.palette.primary.contrastText) {
      theme.palette.primary.contrastText = '#ffffff';
    }
  }
  
  // Ensure secondary palette exists
  if (!theme.palette.secondary) {
    theme.palette.secondary = {
      main: '#38B2AC',
      light: '#4FD1CB',
      dark: '#2C8A84',
      contrastText: '#ffffff'
    };
  } else {
    // Ensure secondary.dark exists
    if (!theme.palette.secondary.dark && theme.palette.secondary.main) {
      theme.palette.secondary.dark = theme.palette.secondary.main;
    } else if (!theme.palette.secondary.dark) {
      theme.palette.secondary.dark = '#2C8A84';
    }
    
    // Ensure other secondary properties exist
    if (!theme.palette.secondary.light) {
      theme.palette.secondary.light = theme.palette.secondary.main || '#4FD1CB';
    }
    if (!theme.palette.secondary.contrastText) {
      theme.palette.secondary.contrastText = '#ffffff';
    }
  }
  
  return theme;
};

console.log('MUI theme patch applied successfully');
