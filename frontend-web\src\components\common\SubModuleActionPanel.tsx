import React, { ReactNode } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Typography, useTheme, alpha, Grid } from '@mui/material';
import { motion } from 'framer-motion';
import { getSafeElevation } from '../../utils/themeDefaults';

export interface SubModuleAction {
  label: string;
  icon: ReactNode;
  onClick: () => void;
  color?: string;
  disabled?: boolean;
  description?: string;
}

export interface SubModuleActionPanelProps {
  title?: string;
  actions: SubModuleAction[];
  moduleColor?: string;
  columns?: 2 | 3 | 4;
}

/**
 * A standardized action panel component for submodules
 */
const SubModuleActionPanel: React.FC<SubModuleActionPanelProps> = ({
  title = 'Quick Actions',
  actions,
  moduleColor,
  columns = 3
}) => {
  const theme = useTheme();
  
  // Use provided module color or default to primary
  const panelColor = moduleColor || theme.palette.primary.main;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        elevation={getSafeElevation(theme, 1)}
        sx={{
          p: 3,
          borderRadius: 2,
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.85)})`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(panelColor, 0.1)}`,
          '&:hover': {
            boxShadow: `0 8px 25px ${alpha(panelColor, 0.15)}`,
          },
          transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
        }}
      >
        {title && (
          <Typography 
            variant="h6" 
            fontWeight="bold" 
            color={panelColor} 
            gutterBottom
            sx={{ mb: 2, borderBottom: `1px solid ${alpha(panelColor, 0.1)}`, pb: 1 }}
          >
            {title}
          </Typography>
        )}
        
        <Grid container spacing={2}>
          {actions.map((action, index) => {
            const actionColor = action.color || panelColor;
            
            return (
              <Grid item xs={12} sm={6} md={12 / columns} key={index}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <CustomButton
                    variant="outlined"
                    startIcon={action.icon}
                    onClick={action.onClick}
                    disabled={action.disabled}
                    fullWidth
                    sx={{
                      p: 1.5,
                      justifyContent: 'flex-start',
                      borderColor: alpha(actionColor, 0.3),
                      color: actionColor,
                      backgroundColor: alpha(actionColor, 0.05),
                      '&:hover': {
                        backgroundColor: alpha(actionColor, 0.1),
                        borderColor: alpha(actionColor, 0.5),
                      },
                      '&.Mui-disabled': {
                        borderColor: alpha(theme.palette.text.disabled, 0.2),
                        color: theme.palette.text.disabled,
                        backgroundColor: alpha(theme.palette.text.disabled, 0.05),
                      }
                    }}
                  >
                    <Box sx={{ textAlign: 'left' }}>
                      <Typography variant="body2" fontWeight="medium">
                        {action.label}
                      </Typography>
                      {action.description && (
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                          {action.description}
                        </Typography>
                      )}
                    </Box>
                  </CustomButton>
                </motion.div>
              </Grid>
            );
          })}
        </Grid>
      </Paper>
    </motion.div>
  );
};

export default SubModuleActionPanel;
