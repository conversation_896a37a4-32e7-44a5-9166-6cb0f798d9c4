const logger = require('../utils/logger');

/**
 * Middleware to check if user has required permissions
 * @param {string} module - Module name (e.g., 'animals', 'health')
 * @param {string} action - Action name (e.g., 'read', 'write')
 * @returns {Function} Express middleware
 */
const checkPermission = (module, action) => {
  return async (req, res, next) => {
    try {
      // Skip permission check for admin users
      if (req.user && req.user.role === 'admin') {
        return next();
      }

      // Check if user has 'all' permission
      if (req.user && req.user.permissions && req.user.permissions.includes('all')) {
        return next();
      }

      // Check if user has specific permission
      const permissionKey = `${module}:${action}`;
      if (req.user && req.user.permissions && req.user.permissions.includes(permissionKey)) {
        return next();
      }

      // Permission denied
      return res.status(403).json({
        error: 'You do not have permission to perform this action'
      });
    } catch (error) {
      logger.error('Error checking permissions:', error);
      return res.status(500).json({
        error: 'Error checking permissions'
      });
    }
  };
};

/**
 * Middleware to check if user is an admin
 * @returns {Function} Express middleware
 */
const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    return next();
  }

  return res.status(403).json({
    error: 'Admin access required'
  });
};

/**
 * Middleware to check if user is a manager or admin
 * @returns {Function} Express middleware
 */
const isManagerOrAdmin = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'manager' || req.user.role === 'super_user')) {
    return next();
  }

  return res.status(403).json({
    error: 'Manager, super user, or admin access required'
  });
};

module.exports = {
  checkPermission,
  isAdmin,
  isManagerOrAdmin
};
