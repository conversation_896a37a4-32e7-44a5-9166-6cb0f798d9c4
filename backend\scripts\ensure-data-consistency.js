/**
 * Ensure Data Consistency Script
 *
 * This script ensures that data is consistent across all collections in MongoDB.
 * It fixes any inconsistencies and ensures that all references are valid.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  tlsAllowInvalidCertificates: true, // For development only
  tlsAllowInvalidHostnames: true, // For development only
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000, // 30 seconds timeout
  socketTimeoutMS: 45000, // 45 seconds timeout
});

/**
 * Ensure data consistency across collections
 */
async function ensureDataConsistency() {
  try {
    // Connect to MongoDB
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    // Get database
    const db = client.db(dbName);

    // Get all collections
    const collections = await db.listCollections().toArray();
    logger.info(`Found ${collections.length} collections in database`);

    // Check if animals collection exists
    if (collections.some(c => c.name === 'animals')) {
      const animalsCollection = db.collection('animals');
      const animals = await animalsCollection.find({}).toArray();
      logger.info(`Found ${animals.length} animals`);

      // Ensure all animals have required fields
      let updatedCount = 0;
      for (const animal of animals) {
        let needsUpdate = false;
        const updates = {};

        // Ensure animal has a status
        if (!animal.status) {
          updates.status = 'Active';
          needsUpdate = true;
        }

        // Ensure animal has a health status
        if (!animal.healthStatus) {
          updates.healthStatus = 'healthy';
          needsUpdate = true;
        }

        // Ensure animal has a type
        if (!animal.type && animal.species) {
          updates.type = animal.species;
          needsUpdate = true;
        }

        // Update animal if needed
        if (needsUpdate) {
          await animalsCollection.updateOne(
            { _id: animal._id },
            { $set: updates }
          );
          updatedCount++;
        }
      }

      logger.info(`Updated ${updatedCount} animals with missing required fields`);

      // Check health records
      if (collections.some(c => c.name === 'health_records')) {
        const healthRecordsCollection = db.collection('health_records');
        const healthRecords = await healthRecordsCollection.find({}).toArray();
        logger.info(`Found ${healthRecords.length} health records`);

        // Fix health records with invalid animal references
        const animalIds = animals.map(animal => animal.id);
        let fixedHealthRecords = 0;

        for (const record of healthRecords) {
          if (!animalIds.includes(record.animalId)) {
            // Delete invalid health record
            await healthRecordsCollection.deleteOne({ _id: record._id });
            fixedHealthRecords++;
          }
        }

        logger.info(`Removed ${fixedHealthRecords} health records with invalid animal references`);
      }

      // Check breeding records
      if (collections.some(c => c.name === 'breeding_records')) {
        const breedingRecordsCollection = db.collection('breeding_records');
        const breedingRecords = await breedingRecordsCollection.find({}).toArray();
        logger.info(`Found ${breedingRecords.length} breeding records`);

        // Fix breeding records with invalid animal references
        const animalIds = animals.map(animal => animal.id);
        let fixedBreedingRecords = 0;

        for (const record of breedingRecords) {
          if (!animalIds.includes(record.femaleId) || !animalIds.includes(record.maleId)) {
            // Delete invalid breeding record
            await breedingRecordsCollection.deleteOne({ _id: record._id });
            fixedBreedingRecords++;
          }
        }

        logger.info(`Removed ${fixedBreedingRecords} breeding records with invalid animal references`);
      }
    }

    // Check business analysis collections
    if (collections.some(c => c.name === 'business_kpis')) {
      const kpisCollection = db.collection('business_kpis');
      const kpis = await kpisCollection.find({}).toArray();
      logger.info(`Found ${kpis.length} business KPIs`);

      // Fix KPIs with invalid categories
      const validCategories = ['financial', 'operational', 'animals', 'health', 'breeding', 'feeding'];
      let fixedKPIs = 0;

      for (const kpi of kpis) {
        if (!validCategories.includes(kpi.category)) {
          // Update KPI with default category
          await kpisCollection.updateOne(
            { _id: kpi._id },
            { $set: { category: 'financial' } }
          );
          fixedKPIs++;
        }
      }

      logger.info(`Fixed ${fixedKPIs} KPIs with invalid categories`);
    }

    // Check strategic goals and initiatives
    if (collections.some(c => c.name === 'strategic_goals') && collections.some(c => c.name === 'strategic_initiatives')) {
      const goalsCollection = db.collection('strategic_goals');
      const initiativesCollection = db.collection('strategic_initiatives');

      const goals = await goalsCollection.find({}).toArray();
      const initiatives = await initiativesCollection.find({}).toArray();

      logger.info(`Found ${goals.length} strategic goals and ${initiatives.length} strategic initiatives`);

      // Fix initiatives with invalid goal references
      const goalIds = goals.map(goal => goal.id);
      let fixedInitiatives = 0;

      for (const initiative of initiatives) {
        if (initiative.relatedGoalIds) {
          const validGoalIds = initiative.relatedGoalIds.filter(goalId => goalIds.includes(goalId));

          if (validGoalIds.length !== initiative.relatedGoalIds.length) {
            // Update initiative with valid goal IDs only
            await initiativesCollection.updateOne(
              { _id: initiative._id },
              { $set: { relatedGoalIds: validGoalIds } }
            );
            fixedInitiatives++;
          }
        }
      }

      logger.info(`Fixed ${fixedInitiatives} initiatives with invalid goal references`);
    }

    // Ensure all collections have timestamps
    for (const collection of collections) {
      const collectionObj = db.collection(collection.name);
      const documents = await collectionObj.find({ createdAt: { $exists: false } }).toArray();

      if (documents.length > 0) {
        logger.info(`Adding timestamps to ${documents.length} documents in ${collection.name}`);

        for (const doc of documents) {
          await collectionObj.updateOne(
            { _id: doc._id },
            {
              $set: {
                createdAt: new Date(),
                updatedAt: new Date()
              }
            }
          );
        }
      }
    }

    logger.info('Data consistency check and fixes completed');
  } catch (error) {
    logger.error('Error ensuring data consistency:', error);
  } finally {
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the script
ensureDataConsistency();
