/**
 * Create Default Users (Ad<PERSON> and <PERSON>mo)
 * 
 * This script creates default admin and demo users for the Beta version.
 * Run with: node scripts/create-default-users.js
 */

require('dotenv').config();
const mongodb = require('../src/config/mongodb');
const bcrypt = require('bcryptjs');
const logger = require('../src/utils/logger');

// Default users for Beta version
const defaultUsers = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'Admin@123',
    firstName: 'System',
    lastName: 'Administrator',
    role: 'admin',
    permissions: ['*'], // All permissions
    subscriptionTier: 'Enterprise',
    status: 'active',
    isActive: true
  },
  {
    username: 'Demo',
    email: '<EMAIL>',
    password: '123',
    firstName: 'Demo',
    lastName: 'User',
    role: 'beta',
    permissions: ['read', 'basic_features'],
    subscriptionTier: 'Beta Access',
    status: 'active',
    isActive: true
  }
];

// Hash password
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

// Create default users
async function createDefaultUsers() {
  try {
    logger.info('Creating default users for Beta version...');
    
    // Connect to MongoDB
    const { db } = await mongodb.connectDB();
    logger.info('Connected to MongoDB');
    
    // Check if users collection exists
    const collections = await db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      logger.info('Users collection does not exist, creating...');
      await db.createCollection('users');
      
      // Create indexes
      await db.collection('users').createIndex({ username: 1 }, { unique: true });
      await db.collection('users').createIndex({ email: 1 }, { unique: true });
    }
    
    // Process each default user
    for (const userData of defaultUsers) {
      try {
        // Check if user already exists
        const existingUser = await db.collection('users').findOne({ username: userData.username });
        
        if (existingUser) {
          logger.info(`User '${userData.username}' already exists, updating...`);
          
          // Hash the password
          const hashedPassword = await hashPassword(userData.password);
          
          // Update existing user
          await db.collection('users').updateOne(
            { username: userData.username },
            { 
              $set: { 
                password: hashedPassword,
                role: userData.role,
                permissions: userData.permissions,
                subscriptionTier: userData.subscriptionTier,
                status: userData.status,
                isActive: userData.isActive,
                updatedAt: new Date()
              }
            }
          );
          logger.info(`User '${userData.username}' updated successfully`);
        } else {
          // Hash the password
          const hashedPassword = await hashPassword(userData.password);
          
          // Create new user
          const newUser = {
            ...userData,
            password: hashedPassword,
            profile: {
              farmName: userData.username === 'Demo' ? 'Demo Farm' : 'AgriIntel Admin Farm',
              farmSize: userData.username === 'Demo' ? '50 hectares' : '500 hectares',
              province: 'Gauteng',
              animalTypes: ['Cattle', 'Sheep', 'Goats'],
              paymentMethod: ''
            },
            trialStartDate: new Date(),
            trialEndDate: userData.subscriptionTier === 'Beta Access' ? 
              new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : null,
            lastLogin: null,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          
          await db.collection('users').insertOne(newUser);
          logger.info(`User '${userData.username}' created successfully`);
        }
      } catch (userError) {
        logger.error(`Error processing user '${userData.username}':`, userError);
      }
    }
    
    // Verify users were created
    const userCount = await db.collection('users').countDocuments();
    logger.info(`Total users in database: ${userCount}`);
    
    // List all users (without passwords)
    const allUsers = await db.collection('users').find({}, { 
      projection: { password: 0 } 
    }).toArray();
    
    logger.info('Current users in database:');
    allUsers.forEach(user => {
      logger.info(`- ${user.username} (${user.role}) - ${user.subscriptionTier}`);
    });
    
    // Disconnect from MongoDB
    await mongodb.disconnectDB();
    logger.info('Disconnected from MongoDB');
    
    return true;
  } catch (error) {
    logger.error('Error creating default users:', error);
    return false;
  }
}

// Run the script
createDefaultUsers()
  .then(success => {
    if (success) {
      logger.info('Default users creation completed successfully');
      console.log('\n=== BETA VERSION LOGIN CREDENTIALS ===');
      console.log('Admin User: admin / Admin@123');
      console.log('Demo User:  Demo / 123');
      console.log('=====================================\n');
      process.exit(0);
    } else {
      logger.error('Default users creation failed');
      process.exit(1);
    }
  })
  .catch(error => {
    logger.error('Unexpected error during default users creation:', error);
    process.exit(1);
  });
