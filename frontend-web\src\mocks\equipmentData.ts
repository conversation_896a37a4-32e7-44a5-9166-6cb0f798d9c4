export interface Equipment {
  id: string;
  name: string;
  category: 'machinery' | 'tools' | 'vehicles' | 'infrastructure';
  manufacturer: string;
  model: string;
  serialNumber: string;
  purchaseInfo: {
    date: Date;
    cost: number;
    supplier: string;
    warranty: {
      startDate: Date;
      endDate: Date;
      terms: string;
    };
  };
  status: 'operational' | 'maintenance' | 'repair' | 'retired';
  location: string;
  maintenanceSchedule: MaintenanceSchedule[];
  documents: string[]; // URLs to manuals, certificates, etc.
}

export interface MaintenanceSchedule {
  id: string;
  equipmentId: string;
  type: 'routine' | 'repair' | 'inspection';
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  lastPerformed: Date;
  nextDue: Date;
  assignedTo: string;
  estimatedCost: number;
  status: 'pending' | 'in-progress' | 'completed';
}

export interface MaintenanceLog {
  id: string;
  equipmentId: string;
  date: Date;
  type: 'maintenance' | 'repair' | 'inspection';
  description: string;
  performedBy: string;
  cost: number;
  parts: {
    name: string;
    quantity: number;
    cost: number;
  }[];
  notes: string;
}

export const mockEquipment: Equipment[] = [
  {
    id: 'EQ001',
    name: 'Feed Mixer',
    category: 'machinery',
    manufacturer: 'AgriTech',
    model: 'FM2000',
    serialNumber: 'FM2024001',
    purchaseInfo: {
      date: new Date('2023-12-01'),
      cost: 15000,
      supplier: 'Farm Equipment Ltd',
      warranty: {
        startDate: new Date('2023-12-01'),
        endDate: new Date('2025-12-01'),
        terms: '2 years parts and labor'
      }
    },
    status: 'operational',
    location: 'Feed Processing Area',
    maintenanceSchedule: [
      {
        id: 'MS001',
        equipmentId: 'EQ001',
        type: 'routine',
        frequency: 'monthly',
        lastPerformed: new Date('2024-01-15'),
        nextDue: new Date('2024-02-15'),
        assignedTo: 'John Smith',
        estimatedCost: 200,
        status: 'pending'
      }
    ],
    documents: ['manual.pdf', 'warranty.pdf']
  }
];