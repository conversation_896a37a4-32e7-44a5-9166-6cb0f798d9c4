import React from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton } from '@mui/material';
import {  Add, Edit, Delete, Warning  } from '../../utils/iconImports';
import { mockFeedInventory } from '../../mocks/feedingData';

const FeedingInventory: React.FC = () => {
  return (
    <Box p={4}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">Feed Inventory</Typography>
        <CustomButton variant="contained" color="primary" startIcon={<Add />}>
          Add Feed Item
        </CustomButton>
      </Box>

      <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
        <TableContainer>
          <Table>
            <TableHead sx={{ bgcolor: 'primary.light' }}>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Quantity</TableCell>
                <TableCell>Unit</TableCell>
                <TableCell>Location</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mockFeedInventory.map((item) => (
                <TableRow key={item.id} hover>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.type}</TableCell>
                  <TableCell>{item.quantity}</TableCell>
                  <TableCell>{item.unit}</TableCell>
                  <TableCell>{item.location}</TableCell>
                  <TableCell>
                    <Chip 
                      label={item.status.toUpperCase()} 
                      color={
                        item.status === 'available' ? 'success' : 
                        item.status === 'low' ? 'warning' : 
                        item.status === 'reorder' ? 'error' : 
                        'default'
                      }
                      size="small"
                      icon={item.status === 'low' || item.status === 'reorder' ? <Warning /> : undefined}
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" color="primary">
                      <Edit fontSize="small" />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <Delete fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default FeedingInventory;
