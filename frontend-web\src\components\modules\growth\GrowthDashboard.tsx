import React from 'react';
import { ROUTES } from '../../../constants/routes';
import ModuleDashboard, { ModuleMetric, QuickAction } from '../../ModuleDashboard';

const GrowthDashboard: React.FC = () => {
  const metrics: ModuleMetric[] = [
    { label: 'Avg Daily Gain', value: '2.1 lbs', trend: '+0.2', status: 'positive' },
    { label: 'Target Weight', value: '92%', trend: '+3%', status: 'positive' },
    { label: 'Health Score', value: '4.5/5', trend: 'neutral', status: 'positive' },
    { label: 'Due Measurements', value: '12', trend: 'neutral', status: 'warning' }
  ];

  const quickActions: QuickAction[] = [
    { name: 'Record Weight', icon: '⚖️', path: `${ROUTES.GROWTH}/weight` },
    { name: 'Growth Charts', icon: '📊', path: `${ROUTES.GROWTH}/charts` },
    { name: 'Health Check', icon: '🏥', path: `${ROUTES.GROWTH}/health` },
    { name: 'Set Targets', icon: '🎯', path: `${ROUTES.GROWTH}/targets` }
  ];

  return (
    <ModuleDashboard
      title="Growth Monitoring"
      description="Track animal growth and development"
      metrics={metrics}
      quickActions={quickActions}
    />
  );
};

export default GrowthDashboard;