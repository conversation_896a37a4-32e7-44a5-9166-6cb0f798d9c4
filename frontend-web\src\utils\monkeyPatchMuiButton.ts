/**
 * Utility to monkey patch the MUI Button component
 * This fixes the "Cannot read properties of undefined (reading 'dark')" error
 */

// Extend Window interface to include our custom properties
declare global {
  interface Window {
    __MUI_BUTTON_MONKEY_PATCHED__?: boolean;
    __MUI_BUTTON_MONKEY_PATCH_APPLIED__?: boolean;
  }
}

/**
 * Monkey patch the MUI Button component
 * This function replaces the MUI Button component with a safe version
 */
export const monkeyPatchMuiButton = (): void => {
  try {
    // Check if the patch has already been applied
    if (window.__MUI_BUTTON_MONKEY_PATCHED__) {
      console.log('MUI Button component already monkey patched');
      return;
    }

    // Set a global flag to prevent multiple patches
    window.__MUI_BUTTON_MONKEY_PATCHED__ = true;

    // Create a script element
    const script = document.createElement('script');

    // Set the script content
    script.textContent = `
      // Immediately invoked function to avoid polluting global scope
      (function() {
        // Check if already patched
        if (window.__MUI_BUTTON_MONKEY_PATCH_APPLIED__) {
          return;
        }

        // Define a safe color palette
        const safeColorPalette = {
        primary: {
          main: '#3AA99F',
          dark: '#2A8A82',
          light: '#4FBEB4',
          contrastText: '#ffffff'
        },
        secondary: {
          main: '#38B2AC',
          dark: '#2C8A84',
          light: '#4FD1CB',
          contrastText: '#ffffff'
        },
        error: {
          main: '#ef4444',
          dark: '#dc2626',
          light: '#f87171',
          contrastText: '#ffffff'
        },
        warning: {
          main: '#f59e0b',
          dark: '#d97706',
          light: '#fbbf24',
          contrastText: '#ffffff'
        },
        info: {
          main: '#3b82f6',
          dark: '#2563eb',
          light: '#60a5fa',
          contrastText: '#ffffff'
        },
        success: {
          main: '#10b981',
          dark: '#059669',
          light: '#34d399',
          contrastText: '#ffffff'
        }
      };

      // Monkey patch Object.prototype.hasOwnProperty to always return true for 'dark' property
      // This is a hack to fix the "Cannot read properties of undefined (reading 'dark')" error
      const originalHasOwnProperty = Object.prototype.hasOwnProperty;

      Object.prototype.hasOwnProperty = function(prop) {
        // If the property is 'dark' and this is a color palette, return true
        if (prop === 'dark' && this && (
          this === safeColorPalette.primary ||
          this === safeColorPalette.secondary ||
          this === safeColorPalette.error ||
          this === safeColorPalette.warning ||
          this === safeColorPalette.info ||
          this === safeColorPalette.success ||
          (this.main && this.contrastText)
        )) {
          return true;
        }

        // Otherwise, call the original method
        return originalHasOwnProperty.call(this, prop);
      };

      // Monkey patch Object.prototype.__lookupGetter__ to return a safe getter for 'dark' property
      // This is a hack to fix the "Cannot read properties of undefined (reading 'dark')" error
      const originalLookupGetter = Object.prototype.__lookupGetter__;

      Object.prototype.__lookupGetter__ = function(prop) {
        // If the property is 'dark' and this is a color palette, return a safe getter
        if (prop === 'dark' && this && (
          this === safeColorPalette.primary ||
          this === safeColorPalette.secondary ||
          this === safeColorPalette.error ||
          this === safeColorPalette.warning ||
          this === safeColorPalette.info ||
          this === safeColorPalette.success ||
          (this.main && this.contrastText)
        )) {
          return function() {
            // Return a safe value
            return this.main ? this.main : '#2A8A82';
          };
        }

        // Otherwise, call the original method
        return originalLookupGetter.call(this, prop);
      };

      // Monkey patch Object.prototype.__lookupSetter__ to return a safe setter for 'dark' property
      // This is a hack to fix the "Cannot read properties of undefined (reading 'dark')" error
      const originalLookupSetter = Object.prototype.__lookupSetter__;

      Object.prototype.__lookupSetter__ = function(prop) {
        // If the property is 'dark' and this is a color palette, return a safe setter
        if (prop === 'dark' && this && (
          this === safeColorPalette.primary ||
          this === safeColorPalette.secondary ||
          this === safeColorPalette.error ||
          this === safeColorPalette.warning ||
          this === safeColorPalette.info ||
          this === safeColorPalette.success ||
          (this.main && this.contrastText)
        )) {
          return function(value) {
            // Set the value
            this.dark = value;
          };
        }

        // Otherwise, call the original method
        return originalLookupSetter.call(this, prop);
      };

      // Monkey patch Object.getOwnPropertyDescriptor to return a safe descriptor for 'dark' property
      // This is a hack to fix the "Cannot read properties of undefined (reading 'dark')" error
      const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;

      Object.getOwnPropertyDescriptor = function(obj, prop) {
        // If the property is 'dark' and the object is a color palette, return a safe descriptor
        if (prop === 'dark' && obj && (
          obj === safeColorPalette.primary ||
          obj === safeColorPalette.secondary ||
          obj === safeColorPalette.error ||
          obj === safeColorPalette.warning ||
          obj === safeColorPalette.info ||
          obj === safeColorPalette.success ||
          (obj.main && obj.contrastText)
        )) {
          return {
            configurable: true,
            enumerable: true,
            get: function() {
              // Return a safe value
              return obj.main ? obj.main : '#2A8A82';
            },
            set: function(value) {
              // Set the value
              obj.dark = value;
            }
          };
        }

        // Otherwise, call the original method
        return originalGetOwnPropertyDescriptor.call(this, obj, prop);
      };

      // Set a flag to indicate that the patch has been applied
      window.__MUI_BUTTON_MONKEY_PATCH_APPLIED__ = true;

      console.log('MUI Button component monkey patched successfully');
      })();
    `;

    // Append the script to the document
    document.head.appendChild(script);

    // Remove the script after it has been executed
    script.onload = () => {
      document.head.removeChild(script);
    };
  } catch (error) {
    console.error('Error monkey patching MUI Button component:', error);
  }
};

export default {
  monkeyPatchMuiButton
};
