import React, { useState, useEffect } from 'react';
import { Box, Skeleton, Typography } from '@mui/material';
import { alpha } from '@mui/material/styles';

interface OptimizedImageProps {
  src: string;
  alt: string;
  fallbackSrc?: string;
  width?: string | number;
  height?: string | number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  borderRadius?: string | number;
  opacity?: number;
  filter?: string;
  overlay?: string;
  showSkeleton?: boolean;
  onLoad?: () => void;
  onError?: () => void;
  sx?: any;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  fallbackSrc = '/images/modules/animals/cattle-1.jpeg', // Default fallback
  width = '100%',
  height = '200px',
  objectFit = 'cover',
  borderRadius = 0,
  opacity = 1,
  filter = 'none',
  overlay,
  showSkeleton = true,
  onLoad,
  onError,
  sx = {}
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);

  useEffect(() => {
    setCurrentSrc(src);
    setError(false);
    setLoading(true);
  }, [src]);

  const handleLoad = () => {
    setLoading(false);
    setError(false);
    onLoad?.();
  };

  const handleError = () => {
    setLoading(false);
    if (currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
      setError(false);
    } else {
      setError(true);
      onError?.();
    }
  };

  if (error && currentSrc === fallbackSrc) {
    return (
      <Box
        sx={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: alpha('#000', 0.1),
          borderRadius,
          ...sx
        }}
      >
        <Typography variant="body2" color="text.secondary">
          Image not available
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        position: 'relative',
        width,
        height,
        borderRadius,
        overflow: 'hidden',
        ...sx
      }}
    >
      {/* Loading skeleton */}
      {loading && showSkeleton && (
        <Skeleton
          variant="rectangular"
          width="100%"
          height="100%"
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 2
          }}
        />
      )}

      {/* Main image */}
      <Box
        component="img"
        src={currentSrc}
        alt={alt}
        onLoad={handleLoad}
        onError={handleError}
        sx={{
          width: '100%',
          height: '100%',
          objectFit,
          opacity: loading ? 0 : opacity,
          filter,
          transition: 'opacity 0.3s ease',
          display: 'block'
        }}
      />

      {/* Overlay */}
      {overlay && !loading && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: overlay,
            zIndex: 1
          }}
        />
      )}
    </Box>
  );
};

export default OptimizedImage;
