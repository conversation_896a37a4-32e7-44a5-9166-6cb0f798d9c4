import React, { useState } from 'react';
import { Box, Typography, Paper, Grid, Card, CardContent, FormControl, InputLabel, Select, MenuItem, Switch, List, ListItem, ListItemIcon, ListItemText, Divider, TextField, alpha, useTheme, CircularProgress, Snackbar, Alert } from '@mui/material';
import {
  Storage,
  CloudSync,
  Backup,
  RestoreFromTrash,
  Schedule,
  CloudDownload,
  CloudUpload
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader , CustomButton } from '../../components/common';
import { useLanguage } from '../../contexts/LanguageContext';

const BackupSettings: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const [autoBackup, setAutoBackup] = useState(true);
  const [syncEnabled, setSyncEnabled] = useState(true);
  const [backupFrequency, setBackupFrequency] = useState('daily');
  const [backupLocation, setBackupLocation] = useState('local');
  const [isBackingUp, setIsBackingUp] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const handleBackupNow = () => {
    setIsBackingUp(true);
    // Simulate backup process
    setTimeout(() => {
      setIsBackingUp(false);
      setSnackbarMessage('Backup completed successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    }, 2000);
  };

  const handleRestore = () => {
    setIsRestoring(true);
    // Simulate restore process
    setTimeout(() => {
      setIsRestoring(false);
      setSnackbarMessage('System restored successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);
    }, 2000);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box sx={{ width: '100%', p: { xs: 2, sm: 3 } }}>
      <ModuleHeader
        title={translate('settings.backup.title', { fallback: 'Backup & Restore' })}
        subtitle={translate('settings.backup.subtitle', { fallback: 'Manage system backups and restore points' })}
        module="settings"
        icon={<Backup />}
      />

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <Card sx={{
                borderRadius: '12px',
                overflow: 'hidden',
                background: `linear-gradient(135deg,
                  ${alpha(theme.palette.background.paper, 0.6)},
                  ${alpha(theme.palette.background.paper, 0.4)})`,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
                boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
              }}>
                <Box sx={{
                  p: 2,
                  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                  color: 'white'
                }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Storage sx={{ mr: 1 }} /> {translate('settings.backup.settings', { fallback: 'Backup Settings' })}
                  </Typography>
                </Box>
                <CardContent>
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <Storage />
                      </ListItemIcon>
                      <ListItemText
                        primary={translate('settings.backup.automatic', { fallback: 'Automatic Backups' })}
                        secondary={translate('settings.backup.automatic_desc', { fallback: 'Schedule regular system backups' })}
                      />
                      <Switch
                        checked={autoBackup}
                        onChange={() => setAutoBackup(!autoBackup)}
                        color="primary"
                      />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                    <ListItem>
                      <ListItemIcon>
                        <CloudSync />
                      </ListItemIcon>
                      <ListItemText
                        primary={translate('settings.backup.cloud_sync', { fallback: 'Cloud Synchronization' })}
                        secondary={translate('settings.backup.cloud_sync_desc', { fallback: 'Sync data with cloud storage' })}
                      />
                      <Switch
                        checked={syncEnabled}
                        onChange={() => setSyncEnabled(!syncEnabled)}
                        color="primary"
                      />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                    <ListItem>
                      <ListItemIcon>
                        <Schedule />
                      </ListItemIcon>
                      <FormControl fullWidth>
                        <InputLabel>{translate('settings.backup.frequency', { fallback: 'Backup Frequency' })}</InputLabel>
                        <Select
                          label={translate('settings.backup.frequency', { fallback: 'Backup Frequency' })}
                          value={backupFrequency}
                          onChange={(e) => setBackupFrequency(e.target.value)}
                          disabled={!autoBackup}
                        >
                          <MenuItem value="hourly">{translate('settings.backup.hourly', { fallback: 'Hourly' })}</MenuItem>
                          <MenuItem value="daily">{translate('settings.backup.daily', { fallback: 'Daily' })}</MenuItem>
                          <MenuItem value="weekly">{translate('settings.backup.weekly', { fallback: 'Weekly' })}</MenuItem>
                          <MenuItem value="monthly">{translate('settings.backup.monthly', { fallback: 'Monthly' })}</MenuItem>
                        </Select>
                      </FormControl>
                    </ListItem>
                    <Divider variant="inset" component="li" />
                    <ListItem>
                      <ListItemIcon>
                        <CloudUpload />
                      </ListItemIcon>
                      <FormControl fullWidth>
                        <InputLabel>{translate('settings.backup.location', { fallback: 'Backup Location' })}</InputLabel>
                        <Select
                          label={translate('settings.backup.location', { fallback: 'Backup Location' })}
                          value={backupLocation}
                          onChange={(e) => setBackupLocation(e.target.value)}
                        >
                          <MenuItem value="local">{translate('settings.backup.local', { fallback: 'Local Storage' })}</MenuItem>
                          <MenuItem value="cloud">{translate('settings.backup.cloud', { fallback: 'Cloud Storage' })}</MenuItem>
                          <MenuItem value="both">{translate('settings.backup.both', { fallback: 'Both Local & Cloud' })}</MenuItem>
                        </Select>
                      </FormControl>
                    </ListItem>
                  </List>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                    <CustomButton
                      variant="outlined"
                      color="primary"
                      startIcon={isBackingUp ? <CircularProgress size={20} /> : <Backup />}
                      onClick={handleBackupNow}
                      disabled={isBackingUp}
                    >
                      {isBackingUp
                        ? translate('settings.backup.backing_up', { fallback: 'Backing Up...' })
                        : translate('settings.backup.backup_now', { fallback: 'Backup Now' })}
                    </CustomButton>
                    <CustomButton
                      variant="contained"
                      color="primary"
                      sx={{
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                      }}
                    >
                      {translate('settings.backup.save_settings', { fallback: 'Save Settings' })}
                    </CustomButton>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <Card sx={{
                borderRadius: '12px',
                overflow: 'hidden',
                background: `linear-gradient(135deg,
                  ${alpha(theme.palette.background.paper, 0.6)},
                  ${alpha(theme.palette.background.paper, 0.4)})`,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
                boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
              }}>
                <Box sx={{
                  p: 2,
                  background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                  color: 'white'
                }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
                    <RestoreFromTrash sx={{ mr: 1 }} /> {translate('settings.backup.restore', { fallback: 'Restore System' })}
                  </Typography>
                </Box>
                <CardContent>
                  <Typography variant="body1" paragraph>
                    {translate('settings.backup.restore_desc', { fallback: 'Restore your system from a previous backup point. This will replace your current data with the selected backup.' })}
                  </Typography>

                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel>{translate('settings.backup.select_backup', { fallback: 'Select Backup' })}</InputLabel>
                    <Select
                      label={translate('settings.backup.select_backup', { fallback: 'Select Backup' })}
                      defaultValue="latest"
                    >
                      <MenuItem value="latest">{translate('settings.backup.latest', { fallback: 'Latest Backup (Today at 12:30 PM)' })}</MenuItem>
                      <MenuItem value="yesterday">{translate('settings.backup.yesterday', { fallback: 'Yesterday at 6:00 PM' })}</MenuItem>
                      <MenuItem value="lastweek">{translate('settings.backup.last_week', { fallback: 'Last Week (Monday at 9:00 AM)' })}</MenuItem>
                      <MenuItem value="custom">{translate('settings.backup.custom', { fallback: 'Custom Backup...' })}</MenuItem>
                    </Select>
                  </FormControl>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <CustomButton
                      variant="outlined"
                      color="secondary"
                      startIcon={<CloudDownload />}
                    >
                      {translate('settings.backup.download', { fallback: 'Download Backup' })}
                    </CustomButton>
                    <CustomButton
                      variant="contained"
                      color="secondary"
                      startIcon={isRestoring ? <CircularProgress size={20} color="inherit" /> : <RestoreFromTrash />}
                      onClick={handleRestore}
                      disabled={isRestoring}
                      sx={{
                        background: `linear-gradient(135deg, ${theme.palette.secondary.main}, ${theme.palette.secondary.dark})`,
                      }}
                    >
                      {isRestoring
                        ? translate('settings.backup.restoring', { fallback: 'Restoring...' })
                        : translate('settings.backup.restore_system', { fallback: 'Restore System' })}
                    </CustomButton>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card sx={{
                borderRadius: '12px',
                overflow: 'hidden',
                mt: 3,
                background: `linear-gradient(135deg,
                  ${alpha(theme.palette.background.paper, 0.6)},
                  ${alpha(theme.palette.background.paper, 0.4)})`,
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.15)}`,
                boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
              }}>
                <Box sx={{
                  p: 2,
                  background: `linear-gradient(135deg, ${theme.palette.info.main}, ${theme.palette.info.dark})`,
                  color: 'white'
                }}>
                  <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Storage sx={{ mr: 1 }} /> {translate('settings.backup.system_info', { fallback: 'System Information' })}
                  </Typography>
                </Box>
                <CardContent>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="textSecondary">
                        {translate('settings.backup.last_backup', { fallback: 'Last Backup' })}
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        Today at 12:30 PM
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="textSecondary">
                        {translate('settings.backup.next_backup', { fallback: 'Next Scheduled Backup' })}
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        Tomorrow at 12:30 PM
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="textSecondary">
                        {translate('settings.backup.storage_used', { fallback: 'Storage Used' })}
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        1.2 GB / 5 GB
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="subtitle2" color="textSecondary">
                        {translate('settings.backup.backup_count', { fallback: 'Total Backups' })}
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        24
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>
      </motion.div>

      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar}>
        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BackupSettings;
