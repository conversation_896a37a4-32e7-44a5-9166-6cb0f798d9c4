import React from 'react';
import { Box, Typography, Chip, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { ModuleContentCard } from '../common';
import { LocalHospital, CalendarToday, Pets, Person } from '../../utils/iconImports';
import { HealthRecord } from '../../types/health';
import { useLanguage } from '../../contexts/LanguageContext';

interface HealthCardProps {
  record: HealthRecord;
  onClick?: (record: HealthRecord) => void;
  delay?: number;
  selectable?: boolean;
  selected?: boolean;
  onSelect?: () => void;
}

/**
 * HealthCard - A specialized card component for displaying health record information
 * Based on the BusinessAnalyticsCard design pattern
 */
const HealthCard: React.FC<HealthCardProps> = ({
  record,
  onClick,
  delay = 0,
  selectable = false,
  selected = false,
  onSelect
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Get status color
  const getStatusColor = () => {
    switch (record.status?.toLowerCase()) {
      case 'completed':
        return theme.palette.success.main;
      case 'scheduled':
        return theme.palette.warning.main;
      case 'cancelled':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  // Get type color
  const getTypeColor = () => {
    switch (record.type?.toLowerCase()) {
      case 'vaccination':
        return theme.palette.primary.main;
      case 'treatment':
        return theme.palette.secondary.main;
      case 'checkup':
        return theme.palette.info.main;
      case 'surgery':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <ModuleContentCard
      title={record.description || 'Health Record'}
      subtitle={`${record.type || 'Unknown type'} - ${new Date(record.date).toLocaleDateString()}`}
      icon={<LocalHospital />}
      module="health"
      delay={delay}
      selectable={selectable}
      selected={selected}
      onSelect={onSelect}
      onAction={onClick ? () => onClick(record) : undefined}
      actionLabel={onClick ? translate('common.view_details') : undefined}
      hoverEffect="scale"
    >
      <Box sx={{ p: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ mr: 2 }}>
            <Chip 
              icon={<LocalHospital sx={{ fontSize: 16 }} />} 
              label={record.type || 'Unknown'} 
              size="small" 
              sx={{ 
                bgcolor: alpha(getTypeColor(), 0.1),
                color: getTypeColor(),
                fontWeight: 'medium'
              }} 
            />
          </Box>
          <Box>
            <Chip 
              icon={<CalendarToday sx={{ fontSize: 16 }} />} 
              label={record.status || 'Unknown'} 
              size="small" 
              sx={{ 
                bgcolor: alpha(getStatusColor(), 0.1),
                color: getStatusColor(),
                fontWeight: 'medium'
              }} 
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Pets sx={{ fontSize: 18, mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="text.secondary">
              {translate('health.animal_id')}: {record.animalId || 'Unknown'}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Person sx={{ fontSize: 18, mr: 1, color: theme.palette.text.secondary }} />
            <Typography variant="body2" color="text.secondary">
              {translate('health.performed_by')}: {record.performedBy || 'Unknown'}
            </Typography>
          </Box>

          {record.notes && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {record.notes.length > 100 ? `${record.notes.substring(0, 100)}...` : record.notes}
            </Typography>
          )}
        </Box>
      </Box>
    </ModuleContentCard>
  );
};

export default HealthCard;
