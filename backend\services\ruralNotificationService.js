/**
 * Rural Notification Service for AMPD Livestock
 * Designed for farms in rural or poor network areas
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');
const execPromise = util.promisify(exec);
// Import fetch for making HTTP requests
const fetch = require('node-fetch');

// Configuration
let config = {
  // Phone numbers
  primaryPhone: '**********', // Changed to first additional number
  additionalPhones: ['**********', '**********'],

  // SMS gateways configuration
  smsGateways: {
    // Using Twilio for reliable SMS delivery
    twilio: {
      enabled: true,
      accountSid: process.env.TWILIO_ACCOUNT_SID || '**********************************',
      authToken: process.env.TWILIO_AUTH_TOKEN || '00000000000000000000000000000000',
      fromNumber: process.env.TWILIO_FROM_NUMBER || '+***********',
      baseUrl: 'https://api.twilio.com/2010-04-01/Accounts/'
    },
    // South African SMS gateway (disabled until we have credentials)
    clickatell: {
      enabled: false,
      apiKey: process.env.CLICKATELL_API_KEY || 'your-api-key',
      baseUrl: 'https://platform.clickatell.com/messages'
    },
    // Another South African SMS gateway (disabled until we have credentials)
    bulksms: {
      enabled: false,
      username: process.env.BULKSMS_USERNAME || 'username',
      password: process.env.BULKSMS_PASSWORD || 'password',
      baseUrl: 'https://api.bulksms.com/v1/messages'
    }
  },

  // Voice call configuration
  voiceCall: {
    enabled: true,
    // Using Android phone connected via ADB for voice calls
    adb: {
      enabled: false,
      path: 'adb'  // Path to ADB executable
    },
    // Using text-to-speech for local voice alerts
    tts: {
      enabled: true,
      engine: 'pyttsx3',  // 'pyttsx3' or 'espeak'
      scriptPath: path.join(__dirname, '../scripts/voice_alert.py')
    }
  },

  // WhatsApp configuration (using WhatsApp Web)
  whatsapp: {
    enabled: false,
    scriptPath: path.join(__dirname, '../scripts/whatsapp_message.py')
  },

  // Default country code
  defaultCountryCode: '+27'
};

/**
 * Format phone number to E.164 format
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} - Formatted phone number
 */
function formatPhoneNumber(phoneNumber) {
  // Remove any non-digit characters
  let digits = phoneNumber.replace(/\\D/g, '');

  // If the number starts with 0, replace it with the default country code
  if (digits.startsWith('0')) {
    digits = config.defaultCountryCode + digits.substring(1);
  }

  // If the number doesn't have a country code, add the default country code
  if (!digits.startsWith('+')) {
    if (!digits.startsWith(config.defaultCountryCode.substring(1))) {
      digits = config.defaultCountryCode + digits;
    } else {
      digits = '+' + digits;
    }
  }

  return digits;
}

// No longer needed as we're using fetch API

/**
 * Rural Notification Service
 */
const ruralNotificationService = {
  /**
   * Initialize the notification service
   */
  init: async () => {
    try {
      console.log('Rural Notification Service initialized');
      console.log(`Primary phone: ${config.primaryPhone}`);
      console.log(`Additional phones: ${config.additionalPhones.join(', ')}`);

      // Check if text-to-speech is available
      if (config.voiceCall.enabled && config.voiceCall.tts.enabled) {
        try {
          if (config.voiceCall.tts.engine === 'pyttsx3') {
            await execPromise('python -c "import pyttsx3"');
            console.log('pyttsx3 detected for text-to-speech');
          } else if (config.voiceCall.tts.engine === 'espeak') {
            await execPromise('espeak --version');
            console.log('espeak detected for text-to-speech');
          }
        } catch (error) {
          console.warn('Text-to-speech engine not detected:', error.message);
          console.warn('Voice call functionality will be simulated');
          config.voiceCall.tts.enabled = false;
        }
      }

      // Check if ADB is available
      if (config.voiceCall.enabled && config.voiceCall.adb.enabled) {
        try {
          await execPromise(`${config.voiceCall.adb.path} devices`);
          console.log('ADB detected for Android phone control');
        } catch (error) {
          console.warn('ADB not detected:', error.message);
          console.warn('Android phone control will be disabled');
          config.voiceCall.adb.enabled = false;
        }
      }
    } catch (error) {
      console.error('Error initializing Rural Notification Service:', error);
    }
  },

  /**
   * Send an SMS message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  sendSMS: async (to, message, priority = 'medium') => {
    console.log(`Sending SMS to ${to} with priority ${priority}:`, message);

    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Try sending SMS using Twilio
      if (config.smsGateways.twilio.enabled) {
        try {
          console.log('Attempting to send SMS via Twilio...');

          // Format the phone number for Twilio (E.164 format)
          // Make sure the number starts with a plus sign
          const twilioFormattedTo = formattedTo.startsWith('+') ? formattedTo : `+${formattedTo.replace(/^0/, '27')}`;

          // Create the URL for the Twilio API
          const twilioUrl = `${config.smsGateways.twilio.baseUrl}${config.smsGateways.twilio.accountSid}/Messages.json`;

          // Create the form data for the Twilio API
          const twilioFormData = new URLSearchParams();
          twilioFormData.append('To', twilioFormattedTo);
          twilioFormData.append('From', config.smsGateways.twilio.fromNumber);
          twilioFormData.append('Body', message);

          // Create the authorization header
          const authHeader = 'Basic ' + Buffer.from(
            `${config.smsGateways.twilio.accountSid}:${config.smsGateways.twilio.authToken}`
          ).toString('base64');

          // Send the request
          console.log(`Sending SMS to ${twilioFormattedTo} via Twilio...`);
          const twilioResponse = await fetch(twilioUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'Authorization': authHeader
            },
            body: twilioFormData
          });

          const responseData = await twilioResponse.json();

          if (twilioResponse.ok && responseData.sid) {
            console.log('SMS sent successfully via Twilio:', responseData);
            return {
              success: true,
              messageId: responseData.sid,
              timestamp: new Date().toISOString(),
              gateway: 'twilio'
            };
          } else {
            console.warn('Failed to send SMS via Twilio:', responseData);
          }
        } catch (error) {
          console.warn('Error sending SMS via Twilio:', error.message);
        }
      }

      // Try sending SMS using Clickatell (if enabled)
      if (config.smsGateways.clickatell.enabled) {
        try {
          console.log('Attempting to send SMS via Clickatell...');

          // Format the message for Clickatell REST API
          const clickatellData = {
            content: message,
            to: [formattedTo],
            from: 'AMPD' // Optional sender ID
          };

          // Send the request
          console.log('Sending request to Clickatell:', config.smsGateways.clickatell.baseUrl);
          const clickatellResponse = await fetch(config.smsGateways.clickatell.baseUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': config.smsGateways.clickatell.apiKey
            },
            body: JSON.stringify(clickatellData)
          });

          const responseData = await clickatellResponse.json();
          console.log('Clickatell response:', JSON.stringify(responseData, null, 2));

          // Check if the message was sent successfully
          if (clickatellResponse.ok && responseData.data && responseData.data.message && responseData.data.message.length > 0) {
            const messageId = responseData.data.message[0].apiMessageId;
            console.log('SMS sent successfully via Clickatell. Message ID:', messageId);
            return {
              success: true,
              messageId: messageId,
              timestamp: new Date().toISOString(),
              gateway: 'clickatell'
            };
          } else {
            console.warn('Failed to send SMS via Clickatell:', responseData);
          }
        } catch (error) {
          console.warn('Error sending SMS via Clickatell:', error.message);
        }
      }

      // Try sending SMS using BulkSMS (if enabled)
      if (config.smsGateways.bulksms.enabled) {
        try {
          console.log('Attempting to send SMS via BulkSMS...');

          // Format the message for BulkSMS
          const bulkSmsData = {
            to: formattedTo,
            body: message,
            encoding: 'UNICODE',
            route: 'INTERNATIONAL',  // Use INTERNATIONAL for South African numbers
            sender_id: 'AMPD'  // Sender ID (optional)
          };

          // Create authorization header using username and password
          const authHeader = 'Basic ' + Buffer.from(
            config.smsGateways.bulksms.username + ':' + config.smsGateways.bulksms.password
          ).toString('base64');

          // Send the request
          const bulkSmsResponse = await fetch(config.smsGateways.bulksms.baseUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(bulkSmsData)
          });

          const responseData = await bulkSmsResponse.json();

          if (bulkSmsResponse.ok) {
            console.log('SMS sent successfully via BulkSMS:', responseData);
            return {
              success: true,
              messageId: responseData.id || `bulksms-${Date.now()}`,
              timestamp: new Date().toISOString(),
              gateway: 'bulksms'
            };
          } else {
            console.warn('Failed to send SMS via BulkSMS:', responseData);
          }
        } catch (error) {
          console.warn('Error sending SMS via BulkSMS:', error.message);
        }
      }

      // If all gateways fail, simulate sending SMS
      console.log('All SMS gateways failed or disabled, simulating SMS send');

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        messageId: `sms-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    } catch (error) {
      console.error('Error sending SMS:', error);
      return {
        success: false,
        error: error.message || 'Unknown error sending SMS'
      };
    }
  },

  /**
   * Make a voice call or alert
   * @param {string} to - Recipient phone number
   * @param {string} message - Message to speak
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  makeVoiceCall: async (to, message, priority = 'high') => {
    console.log(`Making voice call/alert to ${to} with priority ${priority}:`, message);

    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Try using text-to-speech for local voice alert
      if (config.voiceCall.enabled && config.voiceCall.tts.enabled) {
        try {
          console.log('Attempting to use text-to-speech for local voice alert...');

          const alertId = `alert-${Date.now()}`;
          const scriptPath = config.voiceCall.tts.scriptPath;

          // Execute the voice alert script
          const command = `python "${scriptPath}" --message "${message.replace(/"/g, '\\"')}" --output "${alertId}"`;

          const { stdout, stderr } = await execPromise(command);

          if (stderr) {
            console.warn('Error in voice alert script:', stderr);
          } else {
            console.log('Voice alert played successfully:', stdout);
            return {
              success: true,
              callId: alertId,
              timestamp: new Date().toISOString(),
              method: 'tts'
            };
          }
        } catch (error) {
          console.warn('Error using text-to-speech for voice alert:', error.message);
        }
      }

      // Try using ADB to make a call via Android phone
      if (config.voiceCall.enabled && config.voiceCall.adb.enabled) {
        try {
          console.log('Attempting to make a call via Android phone using ADB...');

          // Use ADB to make a call
          const command = `${config.voiceCall.adb.path} shell am start -a android.intent.action.CALL -d tel:${formattedTo}`;

          const { stdout, stderr } = await execPromise(command);

          if (stderr) {
            console.warn('Error making call via ADB:', stderr);
          } else {
            console.log('Call initiated successfully via ADB:', stdout);
            return {
              success: true,
              callId: `call-${Date.now()}`,
              timestamp: new Date().toISOString(),
              method: 'adb'
            };
          }
        } catch (error) {
          console.warn('Error making call via ADB:', error.message);
        }
      }

      // If all methods fail, simulate making a voice call
      console.log('All voice call methods failed or disabled, simulating voice call');

      // Simulate call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      return {
        success: true,
        callId: `call-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    } catch (error) {
      console.error('Error making voice call:', error);
      return {
        success: false,
        error: error.message || 'Unknown error making voice call'
      };
    }
  },

  /**
   * Send a WhatsApp message
   * @param {string} to - Recipient phone number
   * @param {string} message - Message content
   * @param {string} priority - Priority level (low, medium, high, critical)
   * @returns {Promise<object>} - Result of the operation
   */
  sendWhatsApp: async (to, message, priority = 'medium') => {
    console.log(`Sending WhatsApp message to ${to} with priority ${priority}:`, message);

    try {
      // Format the phone number
      const formattedTo = formatPhoneNumber(to);

      // Try using WhatsApp Web automation
      if (config.whatsapp.enabled) {
        try {
          console.log('Attempting to send WhatsApp message via automation...');

          const scriptPath = config.whatsapp.scriptPath;

          // Execute the WhatsApp message script
          const command = `python "${scriptPath}" --phone "${formattedTo}" --message "${message.replace(/"/g, '\\"')}"`;

          const { stdout, stderr } = await execPromise(command);

          if (stderr) {
            console.warn('Error in WhatsApp message script:', stderr);
          } else {
            console.log('WhatsApp message sent successfully:', stdout);
            return {
              success: true,
              messageId: `whatsapp-${Date.now()}`,
              timestamp: new Date().toISOString(),
              method: 'automation'
            };
          }
        } catch (error) {
          console.warn('Error sending WhatsApp message via automation:', error.message);
        }
      }

      // If all methods fail, simulate sending WhatsApp message
      console.log('WhatsApp automation failed or disabled, simulating WhatsApp message');

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        messageId: `whatsapp-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        timestamp: new Date().toISOString(),
        simulated: true
      };
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      return {
        success: false,
        error: error.message || 'Unknown error sending WhatsApp message'
      };
    }
  },

  /**
   * Send a birth prediction alert using multiple notification methods
   * @param {object} params - Alert parameters
   * @returns {Promise<Array>} - Results for each notification type
   */
  sendBirthPredictionAlert: async ({
    animalId,
    animalName,
    daysRemaining,
    expectedDueDate,
    riskLevel,
    specialCare,
    recipient,
    notificationTypes,
    customMessage,
    priority
  }) => {
    // Determine priority based on days remaining if not specified
    if (!priority) {
      if (daysRemaining <= 1) {
        priority = 'critical';
      } else if (daysRemaining <= 3) {
        priority = 'high';
      } else if (daysRemaining <= 7) {
        priority = 'medium';
      } else {
        priority = 'low';
      }
    }

    // Format the due date
    const dueDate = expectedDueDate ? new Date(expectedDueDate).toLocaleDateString('en-ZA') : 'unknown';

    // Create the base message
    let baseMessage = customMessage;

    if (!baseMessage) {
      baseMessage = `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${daysRemaining} days (${dueDate}).`;

      // Add risk level
      if (riskLevel) {
        baseMessage += ` Risk level: ${riskLevel.toUpperCase()}.`;
      }

      // Add special care instructions
      if (specialCare && specialCare.length > 0) {
        baseMessage += ` Special care: ${specialCare.join(', ')}.`;
      }
    }

    // Add urgency indicator based on priority
    let message = baseMessage;
    if (priority === 'critical') {
      message = `URGENT: ${baseMessage} IMMEDIATE ATTENTION REQUIRED.`;
    } else if (priority === 'high') {
      message = `IMPORTANT: ${baseMessage} Please prepare accordingly.`;
    }

    // Create message subject for logging purposes
    console.log(`Message subject: ${priority === 'critical' ? 'URGENT: ' : ''}Birth Alert for ${animalName} - ${daysRemaining} days remaining`);

    // Send notifications using each specified method
    const results = [];

    // If no recipient is specified, use the primary phone number
    const phoneNumber = recipient?.phone || config.primaryPhone;

    for (const type of notificationTypes) {
      let result;

      switch (type) {
        case 'sms':
          result = await ruralNotificationService.sendSMS(phoneNumber, message, priority);
          results.push({ type, result });
          break;
        case 'voice':
          result = await ruralNotificationService.makeVoiceCall(phoneNumber, message, priority);
          results.push({ type, result });
          break;
        case 'whatsapp':
          result = await ruralNotificationService.sendWhatsApp(phoneNumber, message, priority);
          results.push({ type, result });
          break;
      }
    }

    // If priority is critical or high, also send to additional phone numbers
    if ((priority === 'critical' || priority === 'high') && config.additionalPhones.length > 0) {
      for (const additionalPhone of config.additionalPhones) {
        // Skip if it's the same as the primary recipient
        if (additionalPhone === phoneNumber) {
          continue;
        }

        // Send SMS to additional phone
        const result = await ruralNotificationService.sendSMS(
          additionalPhone,
          `[CC] ${message}`,
          priority
        );

        results.push({ type: 'sms', recipient: additionalPhone, result });
      }
    }

    return results;
  }
};

// Initialize the notification service
ruralNotificationService.init();

// Export the service and config for testing
module.exports = ruralNotificationService;
module.exports.config = config;
