import React, { useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface GrowthRecord {
  id: string;
  animalId: string;
  animalName: string;
  date: string;
  weight: number;
  height: number;
  notes: string;
}

const mockGrowthData: GrowthRecord[] = [
  {
    id: 'GR001',
    animalId: 'ANM001',
    animalName: 'Bella',
    date: '2024-01-15',
    weight: 620,
    height: 145,
    notes: 'Normal growth rate'
  },
  {
    id: 'GR002',
    animalId: 'ANM001',
    animalName: 'Bella',
    date: '2024-02-15',
    weight: 650,
    height: 147,
    notes: 'Good weight gain'
  },
  {
    id: 'GR003',
    animalId: 'ANM002',
    animalName: 'Zeus',
    date: '2024-01-15',
    weight: 820,
    height: 152,
    notes: 'Above average growth'
  },
  {
    id: 'GR004',
    animalId: 'ANM002',
    animalName: 'Zeus',
    date: '2024-02-15',
    weight: 850,
    height: 154,
    notes: 'Consistent growth'
  }
];

const AnimalGrowth: React.FC = () => {
  const [selectedAnimal, setSelectedAnimal] = useState('ANM001');

  const filteredData = mockGrowthData.filter(record => record.animalId === selectedAnimal);
  
  const chartData = {
    labels: filteredData.map(record => record.date),
    datasets: [
      {
        label: 'Weight (kg)',
        data: filteredData.map(record => record.weight),
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1
      },
      {
        label: 'Height (cm)',
        data: filteredData.map(record => record.height),
        borderColor: 'rgb(153, 102, 255)',
        tension: 0.1
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Growth Trends'
      }
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Growth Tracking</h1>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg">
          Add Growth Record
        </button>
      </div>

      <div className="mb-6">
        <select
          className="border rounded-lg p-2"
          value={selectedAnimal}
          onChange={(e) => setSelectedAnimal(e.target.value)}
        >
          <option value="ANM001">Bella</option>
          <option value="ANM002">Zeus</option>
        </select>
      </div>

      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <Line data={chartData} options={chartOptions} />
      </div>

      <div className="bg-white rounded-lg shadow">
        <table className="min-w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Weight (kg)
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Height (cm)
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Notes
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredData.map((record) => (
              <tr key={record.id}>
                <td className="px-6 py-4 whitespace-nowrap">{record.date}</td>
                <td className="px-6 py-4 whitespace-nowrap">{record.weight}</td>
                <td className="px-6 py-4 whitespace-nowrap">{record.height}</td>
                <td className="px-6 py-4">{record.notes}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AnimalGrowth;