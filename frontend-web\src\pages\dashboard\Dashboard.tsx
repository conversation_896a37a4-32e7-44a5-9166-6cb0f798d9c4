import React, { useState } from 'react';
import ModernDashboard from './ModernDashboard';

// Import CSS for legacy support
import './Dashboard.css';

/**
 * Dashboard Component
 *
 * This component has been updated to use the ModernDashboard component
 * which implements the StandardDashboard pattern used across all modules.
 */
const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (index: number) => {
    console.log('Dashboard component: Tab changed to', index);
    setActiveTab(index);
  };

  return (
    <ModernDashboard
      activeTabIndex={activeTab}
      onTabChange={handleTabChange}
    />
  );
};

export default Dashboard;
