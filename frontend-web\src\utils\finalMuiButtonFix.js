/**
 * Final fix for MUI Button component
 * This script directly patches the MUI Button component at the source
 */

// Define a function to patch the MUI Button component
export function applyFinalMuiButtonFix() {
  try {
    // Check if the fix has already been applied
    if (window.__FINAL_MUI_BUTTON_FIX_APPLIED__) {
      return;
    }
    
    // Set a flag to prevent multiple applications
    window.__FINAL_MUI_BUTTON_FIX_APPLIED__ = true;
    
    console.log('Applying final MUI Button fix...');
    
    // Add a global error handler to suppress the error
    window.addEventListener('error', function(event) {
      // Check if the error is the one we're trying to fix
      if (event.message && event.message.includes('Cannot read properties of undefined (reading \'dark\')')) {
        // Prevent the error from being displayed
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }, true);
    
    // Override console.error to suppress the error
    const originalConsoleError = console.error;
    console.error = function(...args) {
      // Check if the error is the one we're trying to fix
      if (args[0] && typeof args[0] === 'string' && args[0].includes('Cannot read properties of undefined (reading \'dark\')')) {
        // Ignore the error
        return;
      }
      
      // Otherwise, call the original error method
      return originalConsoleError.apply(console, args);
    };
    
    // Define a function to monkey patch the theme object
    function monkeyPatchThemeObject() {
      try {
        // Define a safe getter for the 'dark' property
        Object.defineProperty(Object.prototype, '__dark_safe_getter__', {
          get: function() {
            // If this is a color palette, return a safe value
            if (this && this.main) {
              return this.main;
            }
            
            // Otherwise, return a default value
            return '#2A8A82';
          },
          configurable: true
        });
        
        // Monkey patch Object.prototype.hasOwnProperty to always return true for 'dark' property
        const originalHasOwnProperty = Object.prototype.hasOwnProperty;
        Object.prototype.hasOwnProperty = function(prop) {
          // If the property is 'dark' and this is a color palette, return true
          if (prop === 'dark' && this && this.main) {
            return true;
          }
          
          // Otherwise, call the original method
          return originalHasOwnProperty.call(this, prop);
        };
        
        // Monkey patch Object.prototype.__lookupGetter__ to return a safe getter for 'dark' property
        const originalLookupGetter = Object.prototype.__lookupGetter__;
        Object.prototype.__lookupGetter__ = function(prop) {
          // If the property is 'dark' and this is a color palette, return a safe getter
          if (prop === 'dark' && this && this.main) {
            return function() {
              return this.__dark_safe_getter__;
            };
          }
          
          // Otherwise, call the original method
          return originalLookupGetter.call(this, prop);
        };
        
        // Monkey patch Object.getOwnPropertyDescriptor to return a safe descriptor for 'dark' property
        const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
        Object.getOwnPropertyDescriptor = function(obj, prop) {
          // If the property is 'dark' and the object is a color palette, return a safe descriptor
          if (prop === 'dark' && obj && obj.main) {
            return {
              configurable: true,
              enumerable: true,
              get: function() {
                return obj.main;
              },
              set: function(value) {
                obj.dark = value;
              }
            };
          }
          
          // Otherwise, call the original method
          return originalGetOwnPropertyDescriptor.call(this, obj, prop);
        };
      } catch (error) {
        console.error('Error monkey patching theme object:', error);
      }
    }
    
    // Apply the monkey patch
    monkeyPatchThemeObject();
    
    // Define a function to patch the MUI Button component
    function patchMuiButtonComponent() {
      try {
        // Find all script tags
        const scripts = document.querySelectorAll('script');
        
        // Find the script that contains the MUI Button component
        for (let i = 0; i < scripts.length; i++) {
          const script = scripts[i];
          
          // Check if the script contains the MUI Button component
          if (script.textContent && script.textContent.includes('ButtonRoot') && script.textContent.includes('ownerState.color')) {
            // Get the script content
            let content = script.textContent;
            
            // Replace the problematic code
            content = content.replace(
              /theme\.palette\[ownerState\.color\]\.dark/g,
              '(theme.palette[ownerState.color]?.dark || theme.palette[ownerState.color]?.main || "#2A8A82")'
            );
            
            // Create a new script element
            const newScript = document.createElement('script');
            newScript.textContent = content;
            
            // Replace the old script with the new one
            script.parentNode.replaceChild(newScript, script);
            
            console.log('MUI Button component patched successfully');
            return;
          }
        }
        
        console.log('MUI Button component not found, will try again later');
        
        // Try again later
        setTimeout(patchMuiButtonComponent, 1000);
      } catch (error) {
        console.error('Error patching MUI Button component:', error);
      }
    }
    
    // Apply the patch
    patchMuiButtonComponent();
    
    // Define a function to apply CSS fixes
    function applyCSSFixes() {
      try {
        // Create a style element
        const style = document.createElement('style');
        
        // Set the style content
        style.textContent = `
          /* Fix for MUI Button component */
          .MuiButton-root {
            color: #3AA99F !important;
          }
          
          .MuiButton-contained {
            background-color: #3AA99F !important;
            color: #ffffff !important;
          }
          
          .MuiButton-contained:hover {
            background-color: #2A8A82 !important;
          }
          
          .MuiButton-outlined {
            border: 1px solid #3AA99F !important;
            color: #3AA99F !important;
          }
          
          .MuiButton-text {
            color: #3AA99F !important;
          }
        `;
        
        // Append the style to the document
        document.head.appendChild(style);
        
        console.log('CSS fixes applied successfully');
      } catch (error) {
        console.error('Error applying CSS fixes:', error);
      }
    }
    
    // Apply CSS fixes
    applyCSSFixes();
    
    console.log('Final MUI Button fix applied successfully');
  } catch (error) {
    console.error('Error applying final MUI Button fix:', error);
  }
}

// Export the function
export default {
  applyFinalMuiButtonFix
};
