/**
 * Unified Typography System for AgriIntel
 * 
 * Ensures consistent font usage, sizing, and spacing across all pages
 * based on the reference design pattern.
 */

/* Import Inter font family for consistent typography */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Base typography settings */
:root {
  /* Font families */
  --font-primary: 'Inter', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Font sizes - Mobile first approach */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
  --font-size-6xl: 3.75rem;   /* 60px */
  
  /* Line heights */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Letter spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

/* Global typography reset and base styles */
* {
  font-family: var(--font-primary);
}

html {
  font-size: 16px;
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  color: #1f2937;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: #374151;
  background-color: #f8fafc;
}

/* Heading styles - consistent with reference design */
.unified-h1,
h1.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  color: #111827;
  margin-bottom: 1rem;
}

.unified-h2,
h2.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  letter-spacing: var(--letter-spacing-tight);
  color: #1f2937;
  margin-bottom: 0.875rem;
}

.unified-h3,
h3.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.unified-h4,
h4.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-snug);
  color: #374151;
  margin-bottom: 0.625rem;
}

.unified-h5,
h5.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: #374151;
  margin-bottom: 0.5rem;
}

.unified-h6,
h6.unified {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-normal);
  color: #4b5563;
  margin-bottom: 0.5rem;
}

/* Body text styles */
.unified-body-large,
.body-large {
  font-family: var(--font-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: #374151;
}

.unified-body,
.body-text {
  font-family: var(--font-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-relaxed);
  color: #4b5563;
}

.unified-body-small,
.body-small {
  font-family: var(--font-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: #6b7280;
}

/* Caption and helper text */
.unified-caption,
.caption-text {
  font-family: var(--font-primary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: #9ca3af;
}

/* Responsive typography adjustments */
@media (min-width: 640px) {
  .unified-h1, h1.unified {
    font-size: var(--font-size-5xl);
  }
  
  .unified-h2, h2.unified {
    font-size: var(--font-size-4xl);
  }
  
  .unified-h3, h3.unified {
    font-size: var(--font-size-3xl);
  }
}

@media (min-width: 1024px) {
  .unified-h1, h1.unified {
    font-size: var(--font-size-6xl);
  }
  
  .unified-h2, h2.unified {
    font-size: var(--font-size-5xl);
  }
  
  .unified-body-large, .body-large {
    font-size: var(--font-size-xl);
  }
}

/* Material-UI typography overrides */
.MuiTypography-root {
  font-family: var(--font-primary) !important;
}

.MuiTypography-h1 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-bold) !important;
  letter-spacing: var(--letter-spacing-tight) !important;
}

.MuiTypography-h2 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  letter-spacing: var(--letter-spacing-tight) !important;
}

.MuiTypography-h3 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.MuiTypography-h4 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-medium) !important;
}

.MuiTypography-h5 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-medium) !important;
}

.MuiTypography-h6 {
  font-family: var(--font-primary) !important;
  font-weight: var(--font-weight-medium) !important;
}

.MuiTypography-body1 {
  font-family: var(--font-primary) !important;
  line-height: var(--line-height-relaxed) !important;
}

.MuiTypography-body2 {
  font-family: var(--font-primary) !important;
  line-height: var(--line-height-normal) !important;
}
