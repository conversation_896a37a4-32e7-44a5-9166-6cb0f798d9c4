/**
 * Supplier Context
 * 
 * Provides supplier data and operations to components.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Supplier, SupplierFilter, SupplierProduct } from '../types/commercial/suppliers';
import * as supplierService from '../services/supplierService';

interface SupplierContextType {
  suppliers: Supplier[];
  loading: boolean;
  error: string | null;
  filter: SupplierFilter;
  setFilter: (filter: SupplierFilter) => void;
  selectedSupplier: Supplier | null;
  setSelectedSupplier: (supplier: Supplier | null) => void;
  supplierProducts: SupplierProduct[];
  loadingProducts: boolean;
  getSupplierById: (id: string) => Promise<Supplier>;
  getSupplierProducts: (supplierId: string) => Promise<SupplierProduct[]>;
  refreshSuppliers: () => Promise<void>;
}

const SupplierContext = createContext<SupplierContextType | undefined>(undefined);

export const SupplierProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<SupplierFilter>({});
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [supplierProducts, setSupplierProducts] = useState<SupplierProduct[]>([]);
  const [loadingProducts, setLoadingProducts] = useState<boolean>(false);

  // Fetch suppliers on mount and when filter changes
  useEffect(() => {
    fetchSuppliers();
  }, [filter]);

  // Fetch suppliers from API
  const fetchSuppliers = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Try to fetch from API first
      const response = await supplierService.getSuppliers({
        category: filter.categories?.[0],
        search: filter.search,
        limit: 20,
        page: 1
      });
      
      setSuppliers(response.data);
    } catch (apiError) {
      console.error('Error fetching suppliers from API, falling back to mock data:', apiError);
      
      try {
        // Fall back to mock data if API fails
        const mockData = await supplierService.getMockSuppliers();
        
        // Apply filters to mock data
        let filteredData = [...mockData];
        
        if (filter.categories && filter.categories.length > 0) {
          filteredData = filteredData.filter(supplier => 
            supplier.categories.some(category => filter.categories?.includes(category))
          );
        }
        
        if (filter.search) {
          const searchLower = filter.search.toLowerCase();
          filteredData = filteredData.filter(supplier => 
            supplier.name.toLowerCase().includes(searchLower) ||
            supplier.description.toLowerCase().includes(searchLower) ||
            supplier.shortDescription.toLowerCase().includes(searchLower)
          );
        }
        
        setSuppliers(filteredData);
      } catch (mockError) {
        console.error('Error fetching mock suppliers:', mockError);
        setError('Failed to load supplier data. Please try again later.');
        setSuppliers([]);
      }
    } finally {
      setLoading(false);
    }
  };

  // Get supplier by ID
  const getSupplierById = async (id: string): Promise<Supplier> => {
    try {
      const supplier = await supplierService.getSupplierById(id);
      return supplier;
    } catch (error) {
      console.error(`Error fetching supplier ${id}:`, error);
      
      // Fall back to mock data
      const mockSuppliers = await supplierService.getMockSuppliers();
      const mockSupplier = mockSuppliers.find(s => s.id === id);
      
      if (mockSupplier) {
        return mockSupplier;
      }
      
      throw new Error(`Supplier with ID ${id} not found`);
    }
  };

  // Get supplier products
  const getSupplierProducts = async (supplierId: string): Promise<SupplierProduct[]> => {
    setLoadingProducts(true);
    
    try {
      const response = await supplierService.getSupplierProducts(supplierId);
      setSupplierProducts(response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching products for supplier ${supplierId}:`, error);
      setSupplierProducts([]);
      throw error;
    } finally {
      setLoadingProducts(false);
    }
  };

  // Refresh suppliers
  const refreshSuppliers = async (): Promise<void> => {
    await fetchSuppliers();
  };

  return (
    <SupplierContext.Provider
      value={{
        suppliers,
        loading,
        error,
        filter,
        setFilter,
        selectedSupplier,
        setSelectedSupplier,
        supplierProducts,
        loadingProducts,
        getSupplierById,
        getSupplierProducts,
        refreshSuppliers
      }}
    >
      {children}
    </SupplierContext.Provider>
  );
};

// Custom hook to use the supplier context
export const useSuppliers = () => {
  const context = useContext(SupplierContext);
  
  if (context === undefined) {
    throw new Error('useSuppliers must be used within a SupplierProvider');
  }
  
  return context;
};
