import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  Tabs,
  Tab,
  CircularProgress,
  Alert
} from '@mui/material';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { useFeedingNutrition } from '../../hooks/useFeedingNutrition';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const FeedingNutrition: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);
  const { nutritionData, loading, error } = useFeedingNutrition();

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">Error loading nutrition data. Please try again later.</Alert>
      </Box>
    );
  }

  return (
    <Box p={4}>
      <Typography variant="h4" fontWeight="bold" mb={3}>Nutrition Analysis</Typography>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="nutrition tabs">
          <Tab label="Current Diet" />
          <Tab label="Nutrient Trends" />
          <Tab label="Recommendations" />
        </Tabs>
      </Box>

      {tabValue === 0 && (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3}>Nutrient Analysis</Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Nutrient</TableCell>
                      <TableCell>Current</TableCell>
                      <TableCell>Recommended</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Progress</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {nutritionData.nutrients.map((nutrient) => {
                      const percentage = (nutrient.value / nutrient.recommended) * 100;
                      let color = 'success';
                      if (percentage < 80) color = 'error';
                      else if (percentage < 95 || percentage > 120) color = 'warning';

                      return (
                        <TableRow key={nutrient.name}>
                          <TableCell>{nutrient.name}</TableCell>
                          <TableCell>{nutrient.value} {nutrient.unit}</TableCell>
                          <TableCell>{nutrient.recommended} {nutrient.unit}</TableCell>
                          <TableCell>
                            {percentage < 80 ? 'Deficient' :
                             percentage < 95 ? 'Low' :
                             percentage > 120 ? 'Excess' :
                             'Optimal'}
                          </TableCell>
                          <TableCell width="30%">
                            <LinearProgress
                              variant="determinate"
                              value={Math.min(percentage, 150)}
                              color={color as any}
                              sx={{ height: 8, borderRadius: 4 }}
                            />
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
              <CardContent>
                <Typography variant="h6" fontWeight="bold" mb={2}>Feed Composition</Typography>
                <Box height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={nutritionData.feedComposition}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="percentage"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {nutritionData.feedComposition.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {tabValue === 1 && (
        <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', p: 3 }}>
          <Typography variant="h6" fontWeight="bold" mb={3}>Nutrient Trends Over Time</Typography>
          <Box height={400}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={nutritionData.trends}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="protein" name="Protein %" fill="#8884d8" />
                <Bar dataKey="fiber" name="Fiber %" fill="#82ca9d" />
                <Bar dataKey="fat" name="Fat %" fill="#ffc658" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </Paper>
      )}

      {tabValue === 2 && (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', p: 3 }}>
              <Typography variant="h6" fontWeight="bold" mb={3}>Nutritional Recommendations</Typography>
              <Typography variant="body1" paragraph>
                Based on the current analysis, the following adjustments are recommended:
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Increase fiber content</strong> by adding more hay to the diet. Current levels are 12% below recommendations.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Reduce protein slightly</strong> as current levels are 12.5% above recommendations, which may lead to unnecessary costs.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Supplement Vitamin D</strong> as current levels are 8.3% below recommendations.
                  </Typography>
                </Box>
                <Box component="li" mb={1}>
                  <Typography variant="body1">
                    <strong>Monitor calcium levels</strong> which are currently at the upper end of the acceptable range.
                  </Typography>
                </Box>
              </Box>
              <Typography variant="body1" mt={3} fontWeight="bold">
                Implementing these changes could improve feed efficiency by approximately 7% and potentially reduce feed costs by 5%.
              </Typography>
            </Paper>
          </Grid>
        </Grid>
      )}
    </Box>
  );
};

export default FeedingNutrition;
