export interface PregnancyRecord {
  id: string;
  animalId: string;
  breedingDate: Date;
  confirmedDate?: Date;
  expectedDueDate?: Date;
  actualBirthDate?: Date;
  status: 'suspected' | 'confirmed' | 'completed' | 'unsuccessful';
  notes?: string;
}

export interface PregnancyCheck {
  id: string;
  pregnancyRecordId: string;
  checkDate: Date;
  performedBy: string;
  method: 'visual' | 'ultrasound' | 'blood test';
  results: string;
  notes?: string;
}

// Enhance pregnancy records with more realistic data
export const mockPregnancyRecords: PregnancyRecord[] = [
  {
    id: 'PR001',
    animalId: 'ANM002',
    breedingDate: new Date('2024-01-15'),
    confirmedDate: new Date('2024-02-15'),
    expectedDueDate: new Date('2024-10-15'),
    status: 'confirmed',
    notes: 'Progressing normally'
  },
  {
    id: 'PR002',
    animalId: 'ANM005',
    breedingDate: new Date('2024-01-20'),
    confirmedDate: new Date('2024-02-20'),
    expectedDueDate: new Date('2024-10-20'),
    status: 'confirmed',
    notes: 'Healthy pregnancy, no concerns'
  },
  {
    id: 'PR003',
    animalId: 'ANM008',
    breedingDate: new Date('2024-02-05'),
    confirmedDate: new Date('2024-03-05'),
    expectedDueDate: new Date('2024-11-05'),
    status: 'confirmed',
    notes: 'First pregnancy for this animal, monitoring closely'
  },
  {
    id: 'PR004',
    animalId: 'ANM012',
    breedingDate: new Date('2024-02-10'),
    status: 'suspected',
    notes: 'Awaiting confirmation'
  },
  {
    id: 'PR005',
    animalId: 'ANM015',
    breedingDate: new Date('2023-12-10'),
    confirmedDate: new Date('2024-01-10'),
    expectedDueDate: new Date('2024-09-10'),
    status: 'confirmed',
    notes: 'Progressing well, second trimester'
  },
  {
    id: 'PR006',
    animalId: 'ANM018',
    breedingDate: new Date('2023-11-05'),
    confirmedDate: new Date('2023-12-05'),
    expectedDueDate: new Date('2024-08-05'),
    actualBirthDate: new Date('2024-08-03'),
    status: 'completed',
    notes: 'Successful birth, healthy calf'
  },
  {
    id: 'PR007',
    animalId: 'ANM022',
    breedingDate: new Date('2023-11-15'),
    confirmedDate: new Date('2023-12-15'),
    expectedDueDate: new Date('2024-08-15'),
    status: 'unsuccessful',
    notes: 'Pregnancy lost at 4 months, animal recovering well'
  }
];

// Enhance pregnancy checks with more realistic data
export const mockPregnancyChecks: PregnancyCheck[] = [
  {
    id: 'PC001',
    pregnancyRecordId: 'PR001',
    checkDate: new Date('2024-02-15'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Pregnancy confirmed, fetus developing normally',
    notes: 'Scheduled follow-up in 30 days'
  },
  {
    id: 'PC002',
    pregnancyRecordId: 'PR001',
    checkDate: new Date('2024-03-15'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Fetus developing normally, good heartbeat',
    notes: 'Next check in 60 days'
  },
  {
    id: 'PC003',
    pregnancyRecordId: 'PR002',
    checkDate: new Date('2024-02-20'),
    performedBy: 'VET002',
    method: 'ultrasound',
    results: 'Pregnancy confirmed',
    notes: 'No concerns'
  },
  {
    id: 'PC004',
    pregnancyRecordId: 'PR003',
    checkDate: new Date('2024-03-05'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Pregnancy confirmed, single fetus',
    notes: 'First-time mother, recommend additional monitoring'
  },
  {
    id: 'PC005',
    pregnancyRecordId: 'PR004',
    checkDate: new Date('2024-03-10'),
    performedBy: 'VET002',
    method: 'blood test',
    results: 'Inconclusive, recommend ultrasound',
    notes: 'Schedule ultrasound within 7 days'
  },
  {
    id: 'PC006',
    pregnancyRecordId: 'PR005',
    checkDate: new Date('2024-01-10'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Pregnancy confirmed, normal development',
    notes: 'No concerns'
  },
  {
    id: 'PC007',
    pregnancyRecordId: 'PR005',
    checkDate: new Date('2024-03-10'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Normal development, second trimester',
    notes: 'Fetus size appropriate for gestational age'
  },
  {
    id: 'PC008',
    pregnancyRecordId: 'PR006',
    checkDate: new Date('2023-12-05'),
    performedBy: 'VET002',
    method: 'ultrasound',
    results: 'Pregnancy confirmed',
    notes: 'No concerns'
  },
  {
    id: 'PC009',
    pregnancyRecordId: 'PR006',
    checkDate: new Date('2024-03-05'),
    performedBy: 'VET002',
    method: 'ultrasound',
    results: 'Healthy fetus, third trimester',
    notes: 'On track for expected due date'
  },
  {
    id: 'PC010',
    pregnancyRecordId: 'PR007',
    checkDate: new Date('2023-12-15'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'Pregnancy confirmed',
    notes: 'No concerns'
  },
  {
    id: 'PC011',
    pregnancyRecordId: 'PR007',
    checkDate: new Date('2024-03-15'),
    performedBy: 'VET001',
    method: 'ultrasound',
    results: 'No fetal heartbeat detected',
    notes: 'Pregnancy loss confirmed, monitor animal health'
  }
];
