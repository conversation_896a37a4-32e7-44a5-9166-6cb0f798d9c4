import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { motion } from 'framer-motion';
import { Box, Typography, Card, CardContent, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, IconButton, Tooltip, LinearProgress, Avatar, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Warning,
  CheckCircle,
  Info,
  Inventory as InventoryIcon,
  LocalShipping,
  CalendarToday,
  Search,
  FilterList,
  Grass,
  Grain,
  Science,
  Spa,
  AttachMoney
 } from '../../utils/iconImports';
import { useFeedingData } from '../../hooks/useFeedingData';
import { formatCurrency } from '../../utils/formatters';
import { FeedInventory as FeedInventoryType } from '../../types/feeding';
import { EnhancedSection } from '../../components/common/DesignSystem';
import { Mo<PERSON>leHeader, AnimatedBackgroundCard } from '../../components/common/';
import EnhancedDataTable from '../../components/common/EnhancedDataTable';
import AnimatedDashboardCard from '../../components/common/AnimatedDashboardCard';
import AnimatedChart from '../../components/common/AnimatedChart';

const Inventory: React.FC = () => {
  const theme = useTheme();
  const { feedInventory, stats, loading, error } = useFeedingData();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedItem, setSelectedItem] = useState<FeedInventoryType | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [feedTypeFilter, setFeedTypeFilter] = useState<string>('all');

  const handleAddNew = () => {
    setSelectedItem(null);
    setOpenDialog(true);
  };

  const handleEdit = (item: any) => {
    setSelectedItem(item);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const getStockLevelChip = (item: any) => {
    // Default capacity to reorderLevel * 5 if not defined
    const capacity = item.capacity || (item.reorderLevel * 5);
    const stockPercentage = (item.quantity / capacity) * 100;
    if (stockPercentage <= 20) {
      return <Chip label="Low Stock" color="error" size="small" />;
    } else if (stockPercentage <= 50) {
      return <Chip label="Medium Stock" color="warning" size="small" />;
    } else {
      return <Chip label="Good Stock" color="success" size="small" />;
    }
  };

  const getStockLevelColor = (item: any) => {
    // Default capacity to reorderLevel * 5 if not defined
    const capacity = item.capacity || (item.reorderLevel * 5);
    const stockPercentage = (item.quantity / capacity) * 100;
    if (stockPercentage <= 20) {
      return 'error';
    } else if (stockPercentage <= 50) {
      return 'warning';
    } else {
      return 'success';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getFilteredInventory = () => {
    let filtered = feedInventory;

    // Filter by feed type
    if (feedTypeFilter !== 'all') {
      filtered = filtered.filter(item => item.type === feedTypeFilter);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(term) ||
        item.type.toLowerCase().includes(term) ||
        item.supplier?.toLowerCase().includes(term) ||
        item.location?.toLowerCase().includes(term)
      );
    }

    return filtered;
  };

  // Helper functions for feed types and quantities
  const getFeedTypes = (): string[] => {
    const types = new Set<string>();
    feedInventory.forEach(item => {
      if (item.type) types.add(item.type);
    });
    return Array.from(types);
  };

  const getTotalQuantityByType = (type: string): number => {
    return feedInventory
      .filter(item => item.type === type)
      .reduce((sum, item) => sum + item.quantity, 0);
  };

  const getTotalValueByType = (type: string): number => {
    return feedInventory
      .filter(item => item.type === type)
      .reduce((sum, item) => sum + (item.quantity * item.costPerUnit), 0);
  };

  // Prepare data for charts
  const feedTypeData = getFeedTypes().map(type => ({
    name: type.charAt(0).toUpperCase() + type.slice(1),
    value: getTotalQuantityByType(type),
    cost: getTotalValueByType(type)
  }));

  const stockLevelData = [
    { name: 'Low Stock', value: feedInventory.filter(item => {
      const capacity = item.capacity || (item.reorderLevel * 5);
      return (item.quantity / capacity) * 100 <= 20;
    }).length },
    { name: 'Medium Stock', value: feedInventory.filter(item => {
      const capacity = item.capacity || (item.reorderLevel * 5);
      const percentage = (item.quantity / capacity) * 100;
      return percentage > 20 && percentage <= 50;
    }).length },
    { name: 'Good Stock', value: feedInventory.filter(item => {
      const capacity = item.capacity || (item.reorderLevel * 5);
      return (item.quantity / capacity) * 100 > 50;
    }).length }
  ];

  // Prepare data for the enhanced table
  const tableColumns = [
    { id: 'name', label: 'Feed Name', minWidth: 150 },
    { id: 'type', label: 'Type', minWidth: 100, type: 'status' },
    { id: 'quantity', label: 'Quantity', minWidth: 100, align: 'right' },
    { id: 'unit', label: 'Unit', minWidth: 80 },
    { id: 'location', label: 'Location', minWidth: 120 },
    { id: 'supplier', label: 'Supplier', minWidth: 150 },
    { id: 'costPerUnit', label: 'Cost per Unit', minWidth: 120, align: 'right',
      format: (value: number) => formatCurrency(value) },
    { id: 'status', label: 'Status', minWidth: 100, type: 'status' }
  ];

  const getIconForFeedType = (type: string) => {
    switch(type.toLowerCase()) {
      case 'forage':
        return <Grass />;
      case 'concentrate':
        return <Grain />;
      case 'supplement':
        return <Science />;
      case 'mineral':
        return <Spa />;
      default:
        return <InventoryIcon />;
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <ModuleHeader
        title="Feed Inventory Management"
        subtitle="Monitor feed stock levels, track inventory value, and manage feed supplies efficiently"
        module="feed"
        submodule="inventory"
        actionLabel="Add Inventory Item"
        actionIcon={<Add />}
        onAction={handleAddNew}
        icon={<InventoryIcon />}
      />


      <Box sx={{ px: 3, pb: 5 }}>
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Inventory Summary"
              backgroundImage="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2232&q=80"
              icon={<InventoryIcon />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(25, 118, 210, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="primary.main">
                        {feedInventory.length}
                      </Typography>
                      <Typography variant="body2">Total Items</Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6}>
                    <Box textAlign="center" p={1.5} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                      <Typography variant="h4" fontWeight="bold" color="success.main">
                        {formatCurrency(stats.feedInventoryValue)}
                      </Typography>
                      <Typography variant="body2">Total Value</Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Box mt={3}>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Stock Levels
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      <Box display="flex" justifyContent="space-between" p={1} bgcolor="rgba(211, 47, 47, 0.1)" borderRadius={1} mb={1}>
                        <Typography variant="body2">Low Stock</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {feedInventory.filter(item => {
                            const capacity = item.capacity || (item.reorderLevel * 5);
                            return (item.quantity / capacity) * 100 <= 20;
                          }).length}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12}>
                      <Box display="flex" justifyContent="space-between" p={1} bgcolor="rgba(255, 152, 0, 0.1)" borderRadius={1} mb={1}>
                        <Typography variant="body2">Medium Stock</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {feedInventory.filter(item => {
                            const capacity = item.capacity || (item.reorderLevel * 5);
                            const percentage = (item.quantity / capacity) * 100;
                            return percentage > 20 && percentage <= 50;
                          }).length}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12}>
                      <Box display="flex" justifyContent="space-between" p={1} bgcolor="rgba(46, 125, 50, 0.1)" borderRadius={1}>
                        <Typography variant="body2">Good Stock</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {feedInventory.filter(item => {
                            const capacity = item.capacity || (item.reorderLevel * 5);
                            return (item.quantity / capacity) * 100 > 50;
                          }).length}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

        <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Feed Types"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<Grain />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} flexDirection={{ xs: 'column', sm: 'row' }} gap={2}>
                <Box display="flex" alignItems="center">
                  <TextField
                    size="small"
                    placeholder="Search inventory..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    InputProps={{
                      startAdornment: <Search fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    }}
                    sx={{ mr: 2 }}
                  />
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="feed-type-filter-label">Feed Type</InputLabel>
                    <Select
                      labelId="feed-type-filter-label"
                      id="feed-type-filter"
                      value={feedTypeFilter}
                      label="Feed Type"
                      onChange={(e) => setFeedTypeFilter(e.target.value)}
                    >
                      <MenuItem value="all">All Types</MenuItem>
                      <MenuItem value="lucerne">Lucerne</MenuItem>
                      <MenuItem value="maize">Maize</MenuItem>
                      <MenuItem value="eragrostis">Eragrostis</MenuItem>
                      <MenuItem value="teff">Teff</MenuItem>
                      <MenuItem value="supplement">Supplement</MenuItem>
                      <MenuItem value="mineral">Mineral</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </Box>

              <Grid container spacing={2} justifyContent="center">
                {getFilteredInventory().map((item) => (
                  <Grid item xs={12} sm={6} md={6} lg={4} key={item.id}>
                    <Card variant="outlined" sx={{ borderRadius: 2 }}>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                          <Typography variant="subtitle1" fontWeight="bold" sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.15rem' } }}>
                            {item.name}
                          </Typography>
                          {getStockLevelChip(item)}
                        </Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontSize: { xs: '0.85rem', sm: '0.9rem', md: '0.95rem' } }}>
                          {item.type}
                        </Typography>

                        <Box mt={2}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Stock Level
                          </Typography>
                          <Box display="flex" alignItems="center">
                            <Box width="100%" mr={1}>
                              <LinearProgress
                                variant="determinate"
                                value={(item.quantity / (item.capacity || (item.reorderLevel * 5))) * 100}
                                color={getStockLevelColor(item) as "success" | "warning" | "error"}
                                sx={{ height: 8, borderRadius: 5 }}
                              />
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              {Math.round((item.quantity / (item.capacity || (item.reorderLevel * 5))) * 100)}%
                            </Typography>
                          </Box>
                        </Box>

                        <Box mt={2} display="flex" justifyContent="space-between">
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Quantity
                            </Typography>
                            <Typography variant="body1">
                              {item.quantity} {item.unit}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Value
                            </Typography>
                            <Typography variant="body1">
                              {formatCurrency(item.quantity * item.costPerUnit)}
                            </Typography>
                          </Box>
                        </Box>

                        <Box mt={2} display="flex" justifyContent="space-between">
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Location
                            </Typography>
                            <Typography variant="body1">
                              {item.location || 'Main Storage'}
                            </Typography>
                          </Box>
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Last Updated
                            </Typography>
                            <Typography variant="body1">
                              {formatDate(item.lastUpdated ? item.lastUpdated.toString() : item.purchaseDate.toString())}
                            </Typography>
                          </Box>
                        </Box>

                        <Box mt={2} display="flex" justifyContent="flex-end">
                          <IconButton size="small" color="primary" onClick={() => handleEdit(item)}>
                            <Edit fontSize="small" />
                          </IconButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
                {getFilteredInventory().length === 0 && (
                  <Grid item xs={12}>
                    <Box display="flex" alignItems="center" justifyContent="center" height={200}>
                      <Typography variant="body1" color="text.secondary">
                        No inventory items found matching your criteria
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </AnimatedBackgroundCard>
        </Grid>
      </Grid>

      <EnhancedSection
        backgroundImage="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2232&q=80"
        height="auto"
        parallax={true}
        overlayColor={alpha(theme.palette.background.paper, 0.9)}
      >
        <EnhancedDataTable
          title="Feed Inventory Items"
          subtitle="Manage your feed stock with detailed information"
          columns={[
            { id: 'name', label: 'Feed Name', minWidth: 150 },
            { id: 'type', label: 'Type', minWidth: 100, type: 'status' },
            { id: 'quantity', label: 'Quantity', minWidth: 100, align: 'right' },
            { id: 'costPerUnit', label: 'Unit Cost', minWidth: 120, align: 'right',
              format: (value: number) => formatCurrency(value) },
            {
              id: 'totalValue',
              label: 'Total Value',
              minWidth: 120,
              align: 'right',
              format: (value: number, row: any) => formatCurrency(row.quantity * row.costPerUnit)
            },
            { id: 'location', label: 'Location', minWidth: 120 },
            { id: 'supplier', label: 'Supplier', minWidth: 150 },
            {
              id: 'stockLevel',
              label: 'Stock Level',
              minWidth: 120,
              type: 'status',
              format: (value: any, row: any) => {
                const capacity = row.capacity || (row.reorderLevel * 5);
                const percentage = (row.quantity / capacity) * 100;
                if (percentage <= 20) return 'Low';
                if (percentage <= 50) return 'Medium';
                return 'Good';
              }
            }
          ]}
          data={getFilteredInventory()}
          backgroundImage="https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2232&q=80"
          searchPlaceholder="Search feed inventory..."
          accentColor={theme.palette.primary.main}
          loading={loading}
          emptyMessage="No feed inventory items found"
          onRowClick={handleEdit}
          actions={[
            {
              icon: <Edit />,
              label: 'Edit Item',
              onClick: handleEdit
            },
            {
              icon: <Delete />,
              label: 'Delete Item',
              onClick: (item) => console.log('Delete', item.id)
            }
          ]}
          expandableRows={true}
          renderExpandedRow={(row) => (
            <Box p={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Nutritional Information
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <Box p={1} bgcolor={alpha(theme.palette.primary.main, 0.1)} borderRadius={1}>
                    <Typography variant="body2" color="text.secondary">Protein</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {row.nutritionalInfo?.protein || 0}%
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box p={1} bgcolor={alpha(theme.palette.primary.main, 0.1)} borderRadius={1}>
                    <Typography variant="body2" color="text.secondary">Fiber</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {row.nutritionalInfo?.fiber || 0}%
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box p={1} bgcolor={alpha(theme.palette.primary.main, 0.1)} borderRadius={1}>
                    <Typography variant="body2" color="text.secondary">Energy</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {row.nutritionalInfo?.energy || 0} MJ/kg
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box p={1} bgcolor={alpha(theme.palette.primary.main, 0.1)} borderRadius={1}>
                    <Typography variant="body2" color="text.secondary">Moisture</Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {row.nutritionalInfo?.moisture || 0}%
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        />
      </EnhancedSection>


      {/* Add/Edit Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            background: `linear-gradient(to bottom right, ${alpha(theme.palette.background.paper, 0.95)}, ${alpha(theme.palette.background.paper, 0.98)})`,
            backdropFilter: 'blur(10px)',
            overflow: 'hidden'
          }
        }}
        TransitionProps={{
          style: {
            transition: 'all 0.3s ease-out'
          }
        }}
      >
        <Box sx={{ position: 'relative', overflow: 'hidden' }}>
          {/* Background Image */}
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: 'url(https://images.unsplash.com/photo-1500382017468-9049fed747ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2232&q=80)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.05,
              zIndex: 0
            }}
          />

          <DialogTitle sx={{
            background: theme.palette.primary.main,
            color: 'white',
            position: 'relative',
            zIndex: 1,
            boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
          }}>
            <Typography variant="h6" fontWeight="bold">
              {selectedItem ? 'Edit Inventory Item' : 'Add New Inventory Item'}
            </Typography>
          </DialogTitle>

          <DialogContent dividers sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="name"
                label="Feed Name"
                defaultValue={selectedItem?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="feed-type-label">Feed Type</InputLabel>
                <Select
                  labelId="feed-type-label"
                  id="feed-type"
                  label="Feed Type"
                  defaultValue={selectedItem?.type || 'lucerne'}
                >
                  <MenuItem value="lucerne">Lucerne</MenuItem>
                  <MenuItem value="maize">Maize</MenuItem>
                  <MenuItem value="eragrostis">Eragrostis</MenuItem>
                  <MenuItem value="teff">Teff</MenuItem>
                  <MenuItem value="supplement">Supplement</MenuItem>
                  <MenuItem value="mineral">Mineral</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                margin="normal"
                fullWidth
                id="quantity"
                label="Quantity"
                type="number"
                defaultValue={selectedItem?.quantity || 0}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                margin="normal"
                fullWidth
                id="unit"
                label="Unit"
                defaultValue={selectedItem?.unit || 'kg'}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                margin="normal"
                fullWidth
                id="capacity"
                label="Storage Capacity"
                type="number"
                defaultValue={selectedItem?.capacity || 0}
                InputProps={{ inputProps: { min: 0 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="costPerUnit"
                label="Cost per Unit (ZAR)"
                type="number"
                defaultValue={selectedItem?.costPerUnit || 0}
                InputProps={{ inputProps: { min: 0, step: 0.01 } }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="location"
                label="Storage Location"
                defaultValue={selectedItem?.location || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="supplier"
                label="Supplier"
                defaultValue={selectedItem?.supplier || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="normal"
                fullWidth
                id="purchaseDate"
                label="Purchase Date"
                type="date"
                defaultValue={selectedItem?.purchaseDate || new Date().toISOString().split('T')[0]}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="normal"
                fullWidth
                id="notes"
                label="Notes"
                multiline
                rows={4}
                defaultValue={selectedItem?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{
          position: 'relative',
          zIndex: 1,
          p: 3,
          background: alpha(theme.palette.background.paper, 0.8)
        }}>
          <CustomButton
            onClick={handleCloseDialog}
            variant="outlined"
            sx={{
              borderRadius: 2,
              px: 3,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }
            }}
          >
            Cancel
          </CustomButton>
          <CustomButton
            variant="contained"
            color="primary"
            onClick={handleCloseDialog}
            sx={{
              borderRadius: 2,
              px: 3,
              ml: 2,
              background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
              }
            }}
          >
            {selectedItem ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
        </Box>
      </Dialog>
      </Box>
    </Box>
  );
};

export default Inventory;
