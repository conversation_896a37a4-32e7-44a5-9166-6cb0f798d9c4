<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AMPD Livestock Report Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .report-header {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    .report-title {
      font-size: 24px;
      font-weight: bold;
      margin: 0;
      color: #2c3e50;
    }
    .report-subtitle {
      font-size: 18px;
      color: #7f8c8d;
      margin: 5px 0;
    }
    .report-date {
      font-size: 14px;
      color: #95a5a6;
    }
    .report-section {
      margin-bottom: 30px;
    }
    .section-title {
      font-size: 20px;
      color: #2980b9;
      border-bottom: 2px solid #3498db;
      padding-bottom: 5px;
      margin-bottom: 15px;
    }
    .summary-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    .stat-card {
      background-color: #f8f9fa;
      border-radius: 5px;
      padding: 15px;
      min-width: 200px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .stat-title {
      font-size: 14px;
      color: #7f8c8d;
      margin-bottom: 5px;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #2c3e50;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f2f2f2;
      font-weight: bold;
      color: #2c3e50;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .chart-container {
      margin-bottom: 30px;
      text-align: center;
    }
    .chart-placeholder {
      background-color: #f8f9fa;
      border: 1px dashed #ccc;
      border-radius: 5px;
      padding: 30px;
      text-align: center;
      color: #7f8c8d;
    }
    .footer {
      margin-top: 50px;
      text-align: center;
      font-size: 12px;
      color: #95a5a6;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }
    .button {
      display: inline-block;
      background-color: #3498db;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      margin: 10px;
      cursor: pointer;
    }
    .button:hover {
      background-color: #2980b9;
    }
  </style>
</head>
<body>
  <div class="report-header">
    <h1 class="report-title">AMPD Livestock Report Test</h1>
    <h2 class="report-subtitle">Report Generation Test Page</h2>
    <p class="report-date">Generated on: <span id="current-date"></span></p>
  </div>

  <div class="report-section">
    <h3 class="section-title">Report Generation Test</h3>
    <p>This page demonstrates the fixed report generation functionality. Click the button below to generate a report.</p>
    
    <div style="text-align: center; margin: 20px 0;">
      <button id="generate-report" class="button">Generate Financial Report</button>
      <button id="generate-health-report" class="button">Generate Health Report</button>
      <button id="generate-performance-report" class="button">Generate Performance Report</button>
    </div>
    
    <div id="report-status" style="text-align: center; margin: 20px 0; font-weight: bold;"></div>
  </div>

  <div class="report-section">
    <h3 class="section-title">Report Preview</h3>
    <div id="report-preview" style="border: 1px solid #ddd; padding: 20px; min-height: 300px;">
      <p style="text-align: center; color: #7f8c8d;">Report preview will appear here</p>
    </div>
  </div>

  <div class="footer">
    <p>AMPD Livestock Management System</p>
    <p>This is a test page for report generation functionality.</p>
  </div>

  <script>
    // Set current date
    document.getElementById('current-date').textContent = new Date().toLocaleString();

    // Generate report button click handler
    document.getElementById('generate-report').addEventListener('click', function() {
      generateReport('financial');
    });

    // Generate health report button click handler
    document.getElementById('generate-health-report').addEventListener('click', function() {
      generateReport('health');
    });

    // Generate performance report button click handler
    document.getElementById('generate-performance-report').addEventListener('click', function() {
      generateReport('performance');
    });

    // Function to generate a report
    function generateReport(type) {
      const statusElement = document.getElementById('report-status');
      const previewElement = document.getElementById('report-preview');
      
      statusElement.textContent = `Generating ${type} report...`;
      statusElement.style.color = '#3498db';
      
      // Simulate API call delay
      setTimeout(function() {
        try {
          // Generate mock report HTML
          const reportHtml = generateMockReport(type);
          
          // Display the report
          previewElement.innerHTML = reportHtml;
          
          statusElement.textContent = `${type.charAt(0).toUpperCase() + type.slice(1)} report generated successfully!`;
          statusElement.style.color = '#2ecc71';
        } catch (error) {
          statusElement.textContent = `Error generating report: ${error.message}`;
          statusElement.style.color = '#e74c3c';
        }
      }, 1500);
    }

    // Function to generate a mock report
    function generateMockReport(type) {
      const reportDate = new Date().toLocaleString();
      const reportTitle = type.charAt(0).toUpperCase() + type.slice(1) + ' Report';
      
      return `
        <div class="report-header">
          <h1 class="report-title">${reportTitle}</h1>
          <h2 class="report-subtitle">Monthly Report</h2>
          <p class="report-date">Generated on: ${reportDate}</p>
        </div>
        
        <div class="report-section">
          <h3 class="section-title">Summary</h3>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-title">Total Animals</div>
              <div class="stat-value">63</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Active Animals</div>
              <div class="stat-value">58</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Healthy Animals</div>
              <div class="stat-value">52</div>
            </div>
          </div>
        </div>
        
        ${type === 'financial' ? `
        <div class="report-section">
          <h3 class="section-title">Financial Summary</h3>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-title">Total Revenue</div>
              <div class="stat-value">R 125,000</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Total Expenses</div>
              <div class="stat-value">R 75,000</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Net Profit</div>
              <div class="stat-value">R 50,000</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">ROI</div>
              <div class="stat-value">40.00%</div>
            </div>
          </div>
        </div>
        ` : ''}
        
        ${type === 'health' ? `
        <div class="report-section">
          <h3 class="section-title">Health Summary</h3>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-title">Healthy Animals</div>
              <div class="stat-value">52</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Sick Animals</div>
              <div class="stat-value">3</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Vaccinations</div>
              <div class="stat-value">18</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Treatments</div>
              <div class="stat-value">7</div>
            </div>
          </div>
        </div>
        ` : ''}
        
        ${type === 'performance' ? `
        <div class="report-section">
          <h3 class="section-title">Performance Summary</h3>
          <div class="summary-stats">
            <div class="stat-card">
              <div class="stat-title">Average Weight</div>
              <div class="stat-value">450 kg</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Average Growth</div>
              <div class="stat-value">1.2 kg/day</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Feed Efficiency</div>
              <div class="stat-value">3.5:1</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Breeding Success</div>
              <div class="stat-value">85%</div>
            </div>
          </div>
        </div>
        ` : ''}
        
        <div class="report-section">
          <h3 class="section-title">Charts</h3>
          <div class="chart-container">
            <div class="chart-placeholder">
              <p>Species Distribution Chart</p>
              <p>This is where an interactive chart would appear in a dynamic report.</p>
              <p>Species counts: Cattle: 35, Sheep: 15, Goats: 10, Pigs: 3</p>
            </div>
          </div>
        </div>
        
        <div class="report-section">
          <h3 class="section-title">Animal Data</h3>
          <table>
            <thead>
              <tr>
                <th>Tag</th>
                <th>Name</th>
                <th>Species</th>
                <th>Breed</th>
                <th>Gender</th>
                <th>Status</th>
                <th>Health</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>A001</td>
                <td>Thabo</td>
                <td>Cattle</td>
                <td>Nguni</td>
                <td>Male</td>
                <td>Active</td>
                <td>Healthy</td>
              </tr>
              <tr>
                <td>A002</td>
                <td>Lesedi</td>
                <td>Cattle</td>
                <td>Bonsmara</td>
                <td>Female</td>
                <td>Active</td>
                <td>Healthy</td>
              </tr>
              <tr>
                <td>A003</td>
                <td>Tumelo</td>
                <td>Cattle</td>
                <td>Afrikaner</td>
                <td>Male</td>
                <td>Active</td>
                <td>Healthy</td>
              </tr>
              <tr>
                <td>S001</td>
                <td>Pule</td>
                <td>Sheep</td>
                <td>Dorper</td>
                <td>Male</td>
                <td>Active</td>
                <td>Healthy</td>
              </tr>
              <tr>
                <td>S002</td>
                <td>Naledi</td>
                <td>Sheep</td>
                <td>Merino</td>
                <td>Female</td>
                <td>Active</td>
                <td>Sick</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="footer">
          <p>AMPD Livestock Management System</p>
          <p>This report was generated automatically. For questions or support, please contact your system administrator.</p>
        </div>
      `;
    }
  </script>
</body>
</html>
