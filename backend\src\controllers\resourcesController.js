const logger = require('../utils/logger');

const resourcesController = {
  // Get all resources
  getResources: async (req, res) => {
    try {
      const { category, type } = req.query;
      
      let resources = [
        {
          id: '1',
          name: 'Cattle Breeding Guide',
          category: 'guides',
          type: 'document',
          description: 'Comprehensive guide to cattle breeding best practices',
          url: '/resources/documents/cattle_breeding_guide.pdf',
          downloadCount: 120,
          rating: 4.8,
          tags: ['breeding', 'cattle', 'genetics'],
          uploadDate: '2024-01-15',
          fileSize: '2.5 MB'
        },
        {
          id: '2',
          name: 'Animal Health Basics',
          category: 'training',
          type: 'video',
          description: 'Essential animal health knowledge for livestock farmers',
          url: '/resources/videos/animal_health_basics.mp4',
          downloadCount: 98,
          rating: 4.7,
          tags: ['health', 'veterinary', 'basics'],
          uploadDate: '2024-02-10',
          duration: '45 minutes'
        },
        {
          id: '3',
          name: 'Feed Management Calculator',
          category: 'tools',
          type: 'application',
          description: 'Calculate optimal feed requirements for your livestock',
          url: '/resources/tools/feed_calculator',
          downloadCount: 87,
          rating: 4.6,
          tags: ['feeding', 'nutrition', 'calculator'],
          uploadDate: '2024-03-05',
          features: ['Nutritional analysis', 'Cost calculation', 'Feed planning']
        },
        {
          id: '4',
          name: 'Emergency Contact Directory',
          category: 'support',
          type: 'directory',
          description: 'Important contacts for veterinary and emergency services',
          url: '/resources/support/emergency_contacts.pdf',
          downloadCount: 156,
          rating: 4.9,
          tags: ['emergency', 'contacts', 'veterinary'],
          uploadDate: '2024-01-20',
          fileSize: '1.2 MB'
        },
        {
          id: '5',
          name: 'Livestock Record Templates',
          category: 'templates',
          type: 'document',
          description: 'Printable templates for livestock record keeping',
          url: '/resources/templates/record_templates.zip',
          downloadCount: 203,
          rating: 4.8,
          tags: ['records', 'templates', 'documentation'],
          uploadDate: '2024-02-28',
          fileSize: '5.1 MB'
        }
      ];

      // Filter by category if provided
      if (category) {
        resources = resources.filter(resource => resource.category === category);
      }

      // Filter by type if provided
      if (type) {
        resources = resources.filter(resource => resource.type === type);
      }

      res.json({
        success: true,
        data: resources,
        total: resources.length
      });
    } catch (error) {
      logger.error('Error fetching resources:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch resources'
      });
    }
  },

  // Get resource by ID
  getResourceById: async (req, res) => {
    try {
      const { id } = req.params;
      
      // Mock data - in real implementation, fetch from database
      const resource = {
        id: id,
        name: 'Cattle Breeding Guide',
        category: 'guides',
        type: 'document',
        description: 'Comprehensive guide to cattle breeding best practices including genetic selection, breeding programs, and reproductive management.',
        url: '/resources/documents/cattle_breeding_guide.pdf',
        downloadCount: 120,
        rating: 4.8,
        tags: ['breeding', 'cattle', 'genetics'],
        uploadDate: '2024-01-15',
        fileSize: '2.5 MB',
        author: 'Dr. Michael Thompson',
        version: '2.1',
        lastUpdated: '2024-11-15',
        contents: [
          'Introduction to Cattle Breeding',
          'Genetic Principles',
          'Breeding Program Design',
          'Reproductive Management',
          'Record Keeping',
          'Performance Evaluation'
        ],
        relatedResources: ['2', '3', '5']
      };

      res.json({
        success: true,
        data: resource
      });
    } catch (error) {
      logger.error('Error fetching resource:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch resource'
      });
    }
  },

  // Get equipment resources
  getEquipment: async (req, res) => {
    try {
      const equipment = [
        {
          id: '1',
          name: 'Cattle Weighing Scale',
          category: 'weighing',
          type: 'equipment',
          description: 'Digital livestock weighing scale with data recording capability',
          manufacturer: 'AgriTech Solutions',
          model: 'ATS-2000',
          specifications: {
            capacity: '2000 kg',
            accuracy: '±0.5 kg',
            platform_size: '2m x 1.5m',
            connectivity: 'Bluetooth, USB'
          },
          price: 'R 25,000',
          availability: 'in_stock',
          supplier: 'Farm Equipment Co.',
          warranty: '2 years',
          manuals: ['/resources/manuals/ats2000_manual.pdf'],
          images: ['/resources/images/weighing_scale_1.jpg']
        },
        {
          id: '2',
          name: 'Automatic Feeding System',
          category: 'feeding',
          type: 'equipment',
          description: 'Automated feed distribution system for cattle',
          manufacturer: 'FeedMaster Pro',
          model: 'FMP-500',
          specifications: {
            capacity: '500 kg feed storage',
            distribution_rate: '10-50 kg/hour',
            power: '220V AC',
            control: 'Digital timer and sensors'
          },
          price: 'R 45,000',
          availability: 'pre_order',
          supplier: 'Agricultural Automation Ltd.',
          warranty: '3 years',
          manuals: ['/resources/manuals/fmp500_manual.pdf'],
          images: ['/resources/images/feeding_system_1.jpg']
        },
        {
          id: '3',
          name: 'Veterinary Examination Kit',
          category: 'health',
          type: 'equipment',
          description: 'Complete veterinary examination kit for livestock health checks',
          manufacturer: 'VetCare Professional',
          model: 'VCP-BASIC',
          specifications: {
            contents: 'Stethoscope, thermometer, syringes, gloves, antiseptic',
            case_material: 'Aluminum',
            weight: '3.5 kg',
            dimensions: '40cm x 30cm x 15cm'
          },
          price: 'R 3,500',
          availability: 'in_stock',
          supplier: 'Veterinary Supplies SA',
          warranty: '1 year',
          manuals: ['/resources/manuals/vet_kit_manual.pdf'],
          images: ['/resources/images/vet_kit_1.jpg']
        },
        {
          id: '4',
          name: 'Water Tank Monitoring System',
          category: 'monitoring',
          type: 'equipment',
          description: 'IoT-enabled water tank level monitoring with alerts',
          manufacturer: 'SmartFarm Tech',
          model: 'SFT-WM100',
          specifications: {
            sensor_type: 'Ultrasonic',
            range: '0-10 meters',
            connectivity: 'WiFi, GSM',
            battery_life: '2 years'
          },
          price: 'R 8,500',
          availability: 'in_stock',
          supplier: 'IoT Agriculture Solutions',
          warranty: '2 years',
          manuals: ['/resources/manuals/water_monitor_manual.pdf'],
          images: ['/resources/images/water_monitor_1.jpg']
        },
        {
          id: '5',
          name: 'Portable Cattle Crush',
          category: 'handling',
          type: 'equipment',
          description: 'Mobile cattle restraint system for safe handling and treatment',
          manufacturer: 'LivestockPro',
          model: 'LP-CRUSH-M',
          specifications: {
            material: 'Galvanized steel',
            weight: '450 kg',
            dimensions: '3m x 1.2m x 2m',
            capacity: 'Up to 800 kg cattle'
          },
          price: 'R 18,000',
          availability: 'in_stock',
          supplier: 'Livestock Handling Equipment',
          warranty: '5 years',
          manuals: ['/resources/manuals/cattle_crush_manual.pdf'],
          images: ['/resources/images/cattle_crush_1.jpg']
        }
      ];

      res.json({
        success: true,
        data: equipment,
        total: equipment.length
      });
    } catch (error) {
      logger.error('Error fetching equipment resources:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch equipment resources'
      });
    }
  },

  // Get staff resources
  getStaff: async (req, res) => {
    try {
      const staff = [
        {
          id: '1',
          name: 'Dr. Sarah Johnson',
          role: 'Veterinarian',
          specialization: 'Large Animal Medicine',
          contact: {
            phone: '+27 11 123 4567',
            email: '<EMAIL>',
            address: 'Johannesburg Veterinary Clinic'
          },
          availability: 'Mon-Fri 8AM-5PM, Emergency on-call',
          services: ['Health examinations', 'Vaccinations', 'Surgery', 'Emergency care'],
          experience: '15 years',
          qualifications: ['BVSc', 'MSc Animal Health'],
          rating: 4.9
        },
        {
          id: '2',
          name: 'John Smith',
          role: 'Animal Nutritionist',
          specialization: 'Cattle Nutrition',
          contact: {
            phone: '+27 11 234 5678',
            email: '<EMAIL>',
            address: 'Agricultural Nutrition Consultants'
          },
          availability: 'Mon-Fri 9AM-4PM',
          services: ['Feed formulation', 'Nutritional analysis', 'Diet planning', 'Consultation'],
          experience: '12 years',
          qualifications: ['BSc Animal Science', 'MSc Animal Nutrition'],
          rating: 4.7
        },
        {
          id: '3',
          name: 'Maria Rodriguez',
          role: 'Breeding Specialist',
          specialization: 'Cattle Genetics',
          contact: {
            phone: '+27 11 345 6789',
            email: '<EMAIL>',
            address: 'Genetics Plus Consulting'
          },
          availability: 'Tue-Sat 8AM-4PM',
          services: ['Breeding program design', 'Genetic evaluation', 'AI services', 'Record analysis'],
          experience: '18 years',
          qualifications: ['BSc Animal Science', 'PhD Animal Genetics'],
          rating: 4.8
        },
        {
          id: '4',
          name: 'David Wilson',
          role: 'Farm Manager',
          specialization: 'Livestock Operations',
          contact: {
            phone: '+27 11 456 7890',
            email: '<EMAIL>',
            address: 'Farm Operations Management'
          },
          availability: 'Mon-Sun 6AM-6PM',
          services: ['Farm planning', 'Operations management', 'Staff training', 'Efficiency optimization'],
          experience: '20 years',
          qualifications: ['Diploma Agriculture', 'Certificate Farm Management'],
          rating: 4.6
        },
        {
          id: '5',
          name: 'Lisa Thompson',
          role: 'Agricultural Engineer',
          specialization: 'Farm Infrastructure',
          contact: {
            phone: '+27 11 567 8901',
            email: '<EMAIL>',
            address: 'Agricultural Engineering Solutions'
          },
          availability: 'Mon-Fri 8AM-5PM',
          services: ['Infrastructure design', 'Equipment installation', 'Maintenance planning', 'Automation'],
          experience: '10 years',
          qualifications: ['BEng Agricultural Engineering', 'Professional Engineer'],
          rating: 4.5
        }
      ];

      res.json({
        success: true,
        data: staff,
        total: staff.length
      });
    } catch (error) {
      logger.error('Error fetching staff resources:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch staff resources'
      });
    }
  },

  // Get supplier resources
  getSuppliers: async (req, res) => {
    try {
      const suppliers = [
        {
          id: '1',
          name: 'AgriSupply Pro',
          category: 'feed_and_nutrition',
          contact: {
            phone: '+27 11 123 4567',
            email: '<EMAIL>',
            address: '123 Farm Road, Johannesburg',
            website: 'www.agrisupplypro.co.za'
          },
          products: ['Cattle feed', 'Supplements', 'Minerals', 'Hay'],
          services: ['Delivery', 'Nutritional consultation', 'Custom feed mixing'],
          rating: 4.7,
          paymentTerms: '30 days',
          deliveryArea: 'Gauteng, Mpumalanga',
          certifications: ['ISO 9001', 'HACCP']
        },
        {
          id: '2',
          name: 'VetMed Supplies',
          category: 'veterinary_supplies',
          contact: {
            phone: '+27 11 234 5678',
            email: '<EMAIL>',
            address: '456 Medical Street, Pretoria',
            website: 'www.vetmedsupplies.co.za'
          },
          products: ['Vaccines', 'Medications', 'Medical equipment', 'Surgical supplies'],
          services: ['Same-day delivery', 'Emergency supplies', 'Training'],
          rating: 4.8,
          paymentTerms: '15 days',
          deliveryArea: 'National',
          certifications: ['GMP', 'Veterinary approved']
        },
        {
          id: '3',
          name: 'Farm Equipment Solutions',
          category: 'equipment',
          contact: {
            phone: '+27 11 345 6789',
            email: '<EMAIL>',
            address: '789 Industrial Avenue, Durban',
            website: 'www.farmequipsolutions.co.za'
          },
          products: ['Weighing scales', 'Feeding systems', 'Water systems', 'Handling equipment'],
          services: ['Installation', 'Maintenance', 'Training', 'Warranty support'],
          rating: 4.6,
          paymentTerms: '60 days',
          deliveryArea: 'KwaZulu-Natal, Eastern Cape',
          certifications: ['ISO 14001', 'SABS approved']
        },
        {
          id: '4',
          name: 'Genetics International',
          category: 'breeding_services',
          contact: {
            phone: '+27 11 456 7890',
            email: '<EMAIL>',
            address: '321 Breeding Lane, Cape Town',
            website: 'www.geneticsintl.co.za'
          },
          products: ['Semen', 'Embryos', 'Genetic testing', 'Breeding records'],
          services: ['AI services', 'Genetic consultation', 'Breeding program design'],
          rating: 4.9,
          paymentTerms: '30 days',
          deliveryArea: 'National, International',
          certifications: ['International genetics standards', 'Quality assured']
        },
        {
          id: '5',
          name: 'Infrastructure Builders',
          category: 'construction',
          contact: {
            phone: '+27 11 567 8901',
            email: '<EMAIL>',
            address: '654 Construction Road, Bloemfontein',
            website: 'www.infrabuilders.co.za'
          },
          products: ['Barns', 'Fencing', 'Water systems', 'Feed storage'],
          services: ['Design', 'Construction', 'Project management', 'Maintenance'],
          rating: 4.4,
          paymentTerms: '90 days',
          deliveryArea: 'Free State, Northern Cape',
          certifications: ['CIDB registered', 'NHBRC approved']
        }
      ];

      res.json({
        success: true,
        data: suppliers,
        total: suppliers.length
      });
    } catch (error) {
      logger.error('Error fetching supplier resources:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch supplier resources'
      });
    }
  },

  // Get resource statistics
  getStats: async (req, res) => {
    try {
      const stats = {
        totalResources: 45,
        categories: {
          guides: 15,
          training: 20,
          support: 10
        },
        recentlyAdded: 5,
        mostViewed: [
          { id: '1', title: 'Cattle Breeding Guide', views: 120 },
          { id: '2', title: 'Animal Health Basics', views: 98 },
          { id: '3', title: 'Feed Management', views: 87 }
        ],
        equipment: {
          total: 25,
          available: 20,
          maintenance: 3,
          outOfStock: 2
        },
        staff: {
          total: 12,
          available: 10,
          onLeave: 2
        },
        suppliers: {
          total: 18,
          active: 15,
          preferred: 8
        }
      };

      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      logger.error('Error fetching resource statistics:', error);
      res.status(500).json({ 
        success: false, 
        error: 'Internal server error',
        message: 'Failed to fetch resource statistics'
      });
    }
  }
};

module.exports = resourcesController;
