import { AppError, ErrorType, parseError } from '../../utils/errorHandling';
import { ServiceResult } from './BaseService';

/**
 * API Client options
 */
interface ApiClientOptions {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

/**
 * API Client for making HTTP requests with proper error handling
 */
export class ApiClient {
  private baseUrl: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(options: ApiClientOptions) {
    this.baseUrl = options.baseUrl;
    this.timeout = options.timeout || 30000; // Default 30 seconds
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.headers
    };
  }

  /**
   * Make a GET request
   * @param endpoint API endpoint
   * @param params Query parameters
   * @param headers Additional headers
   * @returns Promise with the response data
   */
  async get<T>(
    endpoint: string,
    params?: Record<string, string>,
    headers?: Record<string, string>
  ): Promise<ServiceResult<T>> {
    const url = this.buildUrl(endpoint, params);
    return this.request<T>('GET', url, undefined, headers);
  }

  /**
   * Make a POST request
   * @param endpoint API endpoint
   * @param data Request body
   * @param headers Additional headers
   * @returns Promise with the response data
   */
  async post<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ServiceResult<T>> {
    const url = this.buildUrl(endpoint);
    return this.request<T>('POST', url, data, headers);
  }

  /**
   * Make a PUT request
   * @param endpoint API endpoint
   * @param data Request body
   * @param headers Additional headers
   * @returns Promise with the response data
   */
  async put<T>(
    endpoint: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ServiceResult<T>> {
    const url = this.buildUrl(endpoint);
    return this.request<T>('PUT', url, data, headers);
  }

  /**
   * Make a DELETE request
   * @param endpoint API endpoint
   * @param headers Additional headers
   * @returns Promise with the response data
   */
  async delete<T>(
    endpoint: string,
    headers?: Record<string, string>
  ): Promise<ServiceResult<T>> {
    const url = this.buildUrl(endpoint);
    return this.request<T>('DELETE', url, undefined, headers);
  }

  /**
   * Make a request with proper error handling
   * @param method HTTP method
   * @param url Request URL
   * @param data Request body
   * @param headers Additional headers
   * @returns Promise with the response data
   */
  private async request<T>(
    method: string,
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ServiceResult<T>> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        method,
        headers: {
          ...this.defaultHeaders,
          ...headers
        },
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
        } catch (e) {
          errorData = { message: response.statusText };
        }

        const errorType = this.getErrorTypeFromStatus(response.status);
        const errorMessage = errorData.message || `API error: ${response.status} ${response.statusText}`;
        
        throw new AppError(
          errorMessage,
          errorType,
          response.status,
          errorData
        );
      }

      // For successful responses, parse the JSON
      const responseData = await response.json() as T;
      return {
        success: true,
        data: responseData
      };
    } catch (error) {
      const appError = parseError(error);
      
      return {
        success: false,
        error: appError,
        message: appError.message
      };
    }
  }

  /**
   * Build a URL with query parameters
   * @param endpoint API endpoint
   * @param params Query parameters
   * @returns Full URL
   */
  private buildUrl(endpoint: string, params?: Record<string, string>): string {
    const url = new URL(endpoint, this.baseUrl);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, value);
        }
      });
    }
    
    return url.toString();
  }

  /**
   * Get error type from HTTP status code
   * @param status HTTP status code
   * @returns Error type
   */
  private getErrorTypeFromStatus(status: number): ErrorType {
    if (status === 401) {
      return ErrorType.AUTHENTICATION;
    } else if (status === 403) {
      return ErrorType.AUTHORIZATION;
    } else if (status === 404) {
      return ErrorType.NOT_FOUND;
    } else if (status >= 400 && status < 500) {
      return ErrorType.VALIDATION;
    } else if (status >= 500) {
      return ErrorType.DATABASE;
    } else {
      return ErrorType.UNKNOWN;
    }
  }
}
