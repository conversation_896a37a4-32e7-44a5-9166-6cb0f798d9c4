/**
 * API Service
 * 
 * This service provides functions for interacting with the MongoDB database
 * through the Mongoose models.
 */

const mongoose = require('mongoose');
const models = require('../models');
const logger = require('../utils/logger');

/**
 * Generic CRUD operations for any model
 */
class ApiService {
  /**
   * Create a new document
   * @param {string} modelName - The name of the model
   * @param {Object} data - The data to create
   * @returns {Promise<Object>} The created document
   */
  static async create(modelName, data) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Add timestamps
      const documentData = {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const document = new Model(documentData);
      await document.save();
      
      logger.info(`Created ${modelName} document with ID: ${document._id}`);
      return document;
    } catch (error) {
      logger.error(`Error creating ${modelName} document:`, error);
      throw error;
    }
  }
  
  /**
   * Find documents by query
   * @param {string} modelName - The name of the model
   * @param {Object} query - The query to find documents
   * @param {Object} options - Additional options (sort, limit, skip, populate)
   * @returns {Promise<Array>} The found documents
   */
  static async find(modelName, query = {}, options = {}) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Build the query
      let queryBuilder = Model.find(query);
      
      // Apply sorting
      if (options.sort) {
        queryBuilder = queryBuilder.sort(options.sort);
      }
      
      // Apply pagination
      if (options.limit) {
        queryBuilder = queryBuilder.limit(options.limit);
      }
      
      if (options.skip) {
        queryBuilder = queryBuilder.skip(options.skip);
      }
      
      // Apply population
      if (options.populate) {
        if (Array.isArray(options.populate)) {
          options.populate.forEach(field => {
            queryBuilder = queryBuilder.populate(field);
          });
        } else {
          queryBuilder = queryBuilder.populate(options.populate);
        }
      }
      
      // Execute the query
      const documents = await queryBuilder.exec();
      
      logger.info(`Found ${documents.length} ${modelName} documents`);
      return documents;
    } catch (error) {
      logger.error(`Error finding ${modelName} documents:`, error);
      throw error;
    }
  }
  
  /**
   * Find a document by ID
   * @param {string} modelName - The name of the model
   * @param {string} id - The ID of the document
   * @param {Object} options - Additional options (populate)
   * @returns {Promise<Object>} The found document
   */
  static async findById(modelName, id, options = {}) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Build the query
      let queryBuilder = Model.findById(id);
      
      // Apply population
      if (options.populate) {
        if (Array.isArray(options.populate)) {
          options.populate.forEach(field => {
            queryBuilder = queryBuilder.populate(field);
          });
        } else {
          queryBuilder = queryBuilder.populate(options.populate);
        }
      }
      
      // Execute the query
      const document = await queryBuilder.exec();
      
      if (!document) {
        logger.warn(`${modelName} document with ID ${id} not found`);
        return null;
      }
      
      logger.info(`Found ${modelName} document with ID: ${id}`);
      return document;
    } catch (error) {
      logger.error(`Error finding ${modelName} document with ID ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Update a document by ID
   * @param {string} modelName - The name of the model
   * @param {string} id - The ID of the document
   * @param {Object} data - The data to update
   * @param {Object} options - Additional options (new, runValidators)
   * @returns {Promise<Object>} The updated document
   */
  static async updateById(modelName, id, data, options = { new: true, runValidators: true }) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Add updatedAt timestamp
      const updateData = {
        ...data,
        updatedAt: new Date()
      };
      
      // Execute the update
      const document = await Model.findByIdAndUpdate(id, updateData, options);
      
      if (!document) {
        logger.warn(`${modelName} document with ID ${id} not found for update`);
        return null;
      }
      
      logger.info(`Updated ${modelName} document with ID: ${id}`);
      return document;
    } catch (error) {
      logger.error(`Error updating ${modelName} document with ID ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Delete a document by ID
   * @param {string} modelName - The name of the model
   * @param {string} id - The ID of the document
   * @returns {Promise<Object>} The deleted document
   */
  static async deleteById(modelName, id) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Execute the delete
      const document = await Model.findByIdAndDelete(id);
      
      if (!document) {
        logger.warn(`${modelName} document with ID ${id} not found for deletion`);
        return null;
      }
      
      logger.info(`Deleted ${modelName} document with ID: ${id}`);
      return document;
    } catch (error) {
      logger.error(`Error deleting ${modelName} document with ID ${id}:`, error);
      throw error;
    }
  }
  
  /**
   * Count documents by query
   * @param {string} modelName - The name of the model
   * @param {Object} query - The query to count documents
   * @returns {Promise<number>} The count of documents
   */
  static async count(modelName, query = {}) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Execute the count
      const count = await Model.countDocuments(query);
      
      logger.info(`Counted ${count} ${modelName} documents`);
      return count;
    } catch (error) {
      logger.error(`Error counting ${modelName} documents:`, error);
      throw error;
    }
  }
  
  /**
   * Aggregate documents
   * @param {string} modelName - The name of the model
   * @param {Array} pipeline - The aggregation pipeline
   * @returns {Promise<Array>} The aggregated documents
   */
  static async aggregate(modelName, pipeline) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Execute the aggregation
      const documents = await Model.aggregate(pipeline);
      
      logger.info(`Aggregated ${documents.length} ${modelName} documents`);
      return documents;
    } catch (error) {
      logger.error(`Error aggregating ${modelName} documents:`, error);
      throw error;
    }
  }
  
  /**
   * Perform a bulk write operation
   * @param {string} modelName - The name of the model
   * @param {Array} operations - The bulk write operations
   * @param {Object} options - Additional options (ordered)
   * @returns {Promise<Object>} The result of the bulk write
   */
  static async bulkWrite(modelName, operations, options = { ordered: true }) {
    try {
      const Model = models[modelName];
      if (!Model) {
        throw new Error(`Model ${modelName} not found`);
      }
      
      // Execute the bulk write
      const result = await Model.bulkWrite(operations, options);
      
      logger.info(`Performed bulk write on ${modelName} with ${operations.length} operations`);
      return result;
    } catch (error) {
      logger.error(`Error performing bulk write on ${modelName}:`, error);
      throw error;
    }
  }
}

module.exports = ApiService;
