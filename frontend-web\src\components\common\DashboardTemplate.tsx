import React, { ReactNode } from 'react';
import { Box, Grid, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { ModuleHeader } from './';
import { useThemeContext } from '../../contexts/ThemeContext';
import { getModuleTheme } from '../../utils/backgroundThemes';

interface DashboardTemplateProps {
  title: string;
  subtitle?: string;
  module: string;
  actionLabel?: string;
  actionIcon?: ReactNode;
  onAction?: () => void;
  statsCards?: ReactNode;
  charts?: ReactNode;
  tables?: ReactNode;
  additionalContent?: ReactNode;
  backgroundImage?: string;
}

/**
 * Standardized dashboard template component for consistent layout across modules
 */
const DashboardTemplate: React.FC<DashboardTemplateProps> = ({
  title,
  subtitle,
  module,
  actionLabel,
  actionIcon,
  onAction,
  statsCards,
  charts,
  tables,
  additionalContent,
  backgroundImage
}) => {
  const theme = useTheme();
  const { showBackgroundImages } = useThemeContext();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  // Get module theme - always using teal color scheme for consistency
  const moduleTheme = getModuleTheme(module, '#3AA99F', '#2A8A82');
  const finalBackgroundImage = backgroundImage || moduleTheme.backgroundImage;

  return (
    <Box sx={{ minHeight: '100%' }}>
      {/* Module Header */}
      <ModuleHeader
        title={title}
        subtitle={subtitle}
        module={module}
        actionLabel={actionLabel}
        actionIcon={actionIcon}
        onAction={onAction}
        backgroundImage={showBackgroundImages ? finalBackgroundImage : undefined}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Stats Cards */}
        {statsCards && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-6"
          >
            <Grid container spacing={3} sx={{ mb: 4 }}>
              {statsCards}
            </Grid>
          </motion.div>
        )}

        {/* Charts */}
        {charts && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-6"
          >
            <Grid container spacing={3} sx={{ mb: 4, '& .recharts-responsive-container': { minHeight: { xs: 200, sm: 250, md: 300 } } }}>
              {charts}
            </Grid>
          </motion.div>
        )}

        {/* Tables */}
        {tables && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-6"
          >
            {tables}
          </motion.div>
        )}

        {/* Additional Content */}
        {additionalContent && (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {additionalContent}
          </motion.div>
        )}
      </Box>
    </Box>
  );
};

export default DashboardTemplate;
