import { v4 as uuidv4 } from 'uuid';
import { animalMongoService } from '../services/animalMongoService';
import { breedingMongoService } from '../services/breedingMongoService';
import { healthMongoService } from '../services/healthMongoService';
import { mockAnimals } from '../mocks/animalData';
import { mockBreedingRecords } from '../mocks/breedingData';
import { mockHealthRecords } from '../mocks/healthData';

/**
 * Migrate mock animal data to MongoDB
 */
export const migrateAnimalData = async (): Promise<void> => {
  try {
    console.log('Starting animal data migration...');
    
    // Check if data already exists
    const existingAnimals = await animalMongoService.getAllAnimals();
    if (existingAnimals.length > 0) {
      console.log(`Found ${existingAnimals.length} existing animals in MongoDB. Skipping migration.`);
      return;
    }
    
    // Migrate each animal
    for (const animal of mockAnimals) {
      // Ensure each animal has an ID
      const animalWithId = {
        ...animal,
        id: animal.id || uuidv4()
      };
      
      await animalMongoService.createAnimal(animalWithId);
    }
    
    console.log(`Successfully migrated ${mockAnimals.length} animals to MongoDB`);
  } catch (error) {
    console.error('Error migrating animal data:', error);
    throw error;
  }
};

/**
 * Migrate mock breeding data to MongoDB
 */
export const migrateBreedingData = async (): Promise<void> => {
  try {
    console.log('Starting breeding data migration...');
    
    // Check if data already exists
    const existingRecords = await breedingMongoService.findAll();
    if (existingRecords.length > 0) {
      console.log(`Found ${existingRecords.length} existing breeding records in MongoDB. Skipping migration.`);
      return;
    }
    
    // Migrate each breeding record
    for (const record of mockBreedingRecords) {
      // Ensure each record has an ID
      const recordWithId = {
        ...record,
        id: record.id || uuidv4()
      };
      
      await breedingMongoService.create(recordWithId);
    }
    
    console.log(`Successfully migrated ${mockBreedingRecords.length} breeding records to MongoDB`);
  } catch (error) {
    console.error('Error migrating breeding data:', error);
    throw error;
  }
};

/**
 * Migrate mock health data to MongoDB
 */
export const migrateHealthData = async (): Promise<void> => {
  try {
    console.log('Starting health data migration...');
    
    // Check if data already exists
    const existingRecords = await healthMongoService.findAll();
    if (existingRecords.length > 0) {
      console.log(`Found ${existingRecords.length} existing health records in MongoDB. Skipping migration.`);
      return;
    }
    
    // Migrate each health record
    for (const record of mockHealthRecords) {
      // Ensure each record has an ID
      const recordWithId = {
        ...record,
        id: record.id || uuidv4()
      };
      
      await healthMongoService.create(recordWithId);
    }
    
    console.log(`Successfully migrated ${mockHealthRecords.length} health records to MongoDB`);
  } catch (error) {
    console.error('Error migrating health data:', error);
    throw error;
  }
};

/**
 * Migrate all mock data to MongoDB
 */
export const migrateAllData = async (): Promise<void> => {
  try {
    console.log('Starting data migration to MongoDB...');
    
    await migrateAnimalData();
    await migrateBreedingData();
    await migrateHealthData();
    
    console.log('Data migration completed successfully');
  } catch (error) {
    console.error('Error during data migration:', error);
    throw error;
  }
};
