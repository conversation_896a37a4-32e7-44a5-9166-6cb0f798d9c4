import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Box, Typography, Grid, Card, CardContent, TextField, FormControl, InputLabel, Select, MenuItem, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, useTheme, alpha, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, FormHelperText, Alert, Snackbar } from '@mui/material';
import {
  Save, Cancel, Edit, Delete, Visibility,
  TrendingUp, TrendingDown, Warning, CheckCircle,
  CalendarToday, Pets, Assessment
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  ModuleHeader, AnimatedBackgroundCard, BlendedBackgroundCard , CustomButton } from '../../components/common';
import { useAnimalData } from '../../hooks/useAnimalData';
import { useMongoDb } from '../../contexts/SimpleMongoDbContext';
import { ROUTES } from '../../constants/routes';
import { formatDate, calculateAge } from '../../utils/formatters';
// Removed useThemeContext import as it was deleted
import { useAlert } from '../../hooks/useAlert';

// Define retirement criteria
const RETIREMENT_AGE = {
  'Cattle': 10, // years
  'Sheep': 6,
  'Goat': 7,
  'Pig': 5
};

const BREEDING_LIMIT = {
  'Cattle': 8, // number of times
  'Sheep': 6,
  'Goat': 6,
  'Pig': 8
};

const RetirementTracking = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { showAlert } = useAlert();

  // Get animal ID from URL query params if available
  const queryParams = new URLSearchParams(location.search);
  const animalIdFromQuery = queryParams.get('id');

  // Use MongoDB if connected, otherwise use mock data
  const { isConnected } = useMongoDb();
  const { animals, loading, updateAnimal } = useAnimalData();

  // State for retirement form
  const [selectedAnimal, setSelectedAnimal] = useState(null);
  const [retirementReason, setRetirementReason] = useState('');
  const [retirementDate, setRetirementDate] = useState(new Date().toISOString().split('T')[0]);
  const [retirementNotes, setRetirementNotes] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // State for retirement history
  const [retirementHistory, setRetirementHistory] = useState([]);

  // Load animal data if ID is provided in URL
  useEffect(() => {
    if (animalIdFromQuery && animals && animals.length > 0) {
      const animal = animals.find(a => a.id === animalIdFromQuery);
      if (animal) {
        setSelectedAnimal(animal);

        // Set default retirement reason based on animal data
        const ageInYears = calculateAge(animal.birthDate);
        const breedingCount = animal.breedingCount || 0;
        const ageLimit = RETIREMENT_AGE[animal.type] || 10;
        const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

        if (ageInYears >= ageLimit) {
          setRetirementReason('Age');
        } else if (breedingCount >= breedingLimit) {
          setRetirementReason('Breeding Limit');
        } else {
          setRetirementReason('Health Issues');
        }
      }
    }
  }, [animalIdFromQuery, animals]);

  // Load retirement history
  useEffect(() => {
    if (animals && animals.length > 0) {
      // Filter retired animals
      const retired = animals.filter(animal => animal.status === 'Retired');

      // Create retirement history entries
      const history = retired.map(animal => {
        const ageInYears = calculateAge(animal.birthDate);
        const breedingCount = animal.breedingCount || 0;
        const ageLimit = RETIREMENT_AGE[animal.type] || 10;
        const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

        let reason = 'Other';
        if (ageInYears >= ageLimit) {
          reason = 'Age';
        } else if (breedingCount >= breedingLimit) {
          reason = 'Breeding Limit';
        } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {
          reason = 'Health Issues';
        }

        return {
          id: animal.id,
          tagNumber: animal.tagNumber,
          name: animal.name,
          type: animal.type,
          age: ageInYears,
          breedingCount: breedingCount,
          retirementDate: animal.retirementDate || new Date().toISOString().split('T')[0],
          reason: reason,
          notes: animal.retirementNotes || ''
        };
      });

      setRetirementHistory(history);
    }
  }, [animals]);

  // Handle animal selection
  const handleAnimalChange = (event) => {
    const animalId = event.target.value;
    const animal = animals.find(a => a.id === animalId);
    setSelectedAnimal(animal);

    // Reset form errors
    setFormErrors({});

    // Set default retirement reason based on animal data
    if (animal) {
      const ageInYears = calculateAge(animal.birthDate);
      const breedingCount = animal.breedingCount || 0;
      const ageLimit = RETIREMENT_AGE[animal.type] || 10;
      const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

      if (ageInYears >= ageLimit) {
        setRetirementReason('Age');
      } else if (breedingCount >= breedingLimit) {
        setRetirementReason('Breeding Limit');
      } else {
        setRetirementReason('Health Issues');
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault();

    // Validate form
    const errors = {};
    if (!selectedAnimal) {
      errors.animal = 'Please select an animal';
    }
    if (!retirementReason) {
      errors.reason = 'Please select a retirement reason';
    }
    if (!retirementDate) {
      errors.date = 'Please select a retirement date';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Open confirmation dialog
    setDialogOpen(true);
  };

  // Handle retirement confirmation
  const handleConfirmRetirement = async () => {
    try {
      // Update animal status to Retired
      const updatedAnimal = {
        ...selectedAnimal,
        status: 'Retired',
        retirementDate: retirementDate,
        retirementReason: retirementReason,
        retirementNotes: retirementNotes
      };

      // Call API to update animal
      await updateAnimal(updatedAnimal);

      // Show success message
      setSnackbarMessage(`${selectedAnimal.name} has been retired successfully`);
      setSnackbarOpen(true);

      // Reset form
      setSelectedAnimal(null);
      setRetirementReason('');
      setRetirementDate(new Date().toISOString().split('T')[0]);
      setRetirementNotes('');
      setFormErrors({});

      // Close dialog
      setDialogOpen(false);

      // Show alert
      showAlert({
        message: 'Animal retired successfully',
        severity: 'success'
      });

      // Navigate back to asset management dashboard after a delay
      setTimeout(() => {
        navigate(ROUTES.ASSET_MANAGEMENT);
      }, 2000);
    } catch (error) {
      console.error('Error retiring animal:', error);
      showAlert({
        message: 'Error retiring animal',
        severity: 'error'
      });
      setDialogOpen(false);
    }
  };

  // Filter animals that are candidates for retirement
  const retirementCandidates = animals ? animals.filter(animal => {
    const ageInYears = calculateAge(animal.birthDate);
    const breedingCount = animal.breedingCount || 0;
    const ageLimit = RETIREMENT_AGE[animal.type] || 10;
    const breedingLimit = BREEDING_LIMIT[animal.type] || 8;

    return (
      (ageInYears >= ageLimit - 1 || breedingCount >= breedingLimit - 1) &&
      animal.status !== 'Retired'
    );
  }) : [];

  return (
    <Box>
      <ModuleHeader
        title="Retirement Tracking"
        subtitle="Manage livestock retirement based on age, breeding history, and health status"
        module="animals"
        submodule="asset-management"
        actionLabel="Back to Dashboard"
        actionIcon={<Cancel />}
        onAction={() => navigate(ROUTES.ASSET_MANAGEMENT)}
        icon={<Pets />}
      />

      {/* Retirement Form */}
      <Grid container spacing={3} sx={{ mb: 4, mt: 1 }}>
        <Grid item xs={12} md={6}>
          <AnimatedBackgroundCard
            title="Retire Animal"
            icon={<CalendarToday />}
            accentColor={theme.palette.primary.main}
          >
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
              <FormControl fullWidth sx={{ mb: 3 }} error={!!formErrors.animal}>
                <InputLabel id="animal-select-label">Select Animal</InputLabel>
                <Select
                  labelId="animal-select-label"
                  id="animal-select"
                  value={selectedAnimal ? selectedAnimal.id : ''}
                  label="Select Animal"
                  onChange={handleAnimalChange}
                >
                  <MenuItem value="">
                    <em>Select an animal</em>
                  </MenuItem>
                  {retirementCandidates.map((animal) => (
                    <MenuItem key={animal.id} value={animal.id}>
                      {animal.tagNumber} - {animal.name} ({animal.type})
                    </MenuItem>
                  ))}
                </Select>
                {formErrors.animal && (
                  <FormHelperText>{formErrors.animal}</FormHelperText>
                )}
              </FormControl>

              {selectedAnimal && (
                <Box sx={{ mb: 3, p: 2, bgcolor: alpha(theme.palette.primary.main, 0.05), borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Animal Details:
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Tag:</strong> {selectedAnimal.tagNumber}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Name:</strong> {selectedAnimal.name}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Type:</strong> {selectedAnimal.type}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Age:</strong> {calculateAge(selectedAnimal.birthDate)} years
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Breeding Count:</strong> {selectedAnimal.breedingCount || 0}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2">
                        <strong>Health Status:</strong> {selectedAnimal.healthStatus}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              <FormControl fullWidth sx={{ mb: 3 }} error={!!formErrors.reason}>
                <InputLabel id="reason-select-label">Retirement Reason</InputLabel>
                <Select
                  labelId="reason-select-label"
                  id="reason-select"
                  value={retirementReason}
                  label="Retirement Reason"
                  onChange={(e) => setRetirementReason(e.target.value)}
                >
                  <MenuItem value="">
                    <em>Select a reason</em>
                  </MenuItem>
                  <MenuItem value="Age">Age</MenuItem>
                  <MenuItem value="Breeding Limit">Breeding Limit</MenuItem>
                  <MenuItem value="Health Issues">Health Issues</MenuItem>
                  <MenuItem value="Performance">Performance</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
                {formErrors.reason && (
                  <FormHelperText>{formErrors.reason}</FormHelperText>
                )}
              </FormControl>

              <TextField
                fullWidth
                label="Retirement Date"
                type="date"
                value={retirementDate}
                onChange={(e) => setRetirementDate(e.target.value)}
                sx={{ mb: 3 }}
                InputLabelProps={{
                  shrink: true,
                }}
                error={!!formErrors.date}
                helperText={formErrors.date}
              />

              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={4}
                value={retirementNotes}
                onChange={(e) => setRetirementNotes(e.target.value)}
                sx={{ mb: 3 }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <CustomButton
                  variant="outlined"
                  startIcon={<Cancel />}
                  onClick={() => navigate(ROUTES.ASSET_MANAGEMENT)}
                >
                  Cancel
                </CustomButton>
                <CustomButton
                  type="submit"
                  variant="contained"
                  startIcon={<Save />}
                  disabled={!selectedAnimal}
                >
                  Retire Animal
                </CustomButton>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>

        <Grid item xs={12} md={6}>
          <AnimatedBackgroundCard
            title="Retirement Guidelines"
            icon={<Assessment />}
            accentColor={theme.palette.primary.main}
          >
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Retirement Criteria by Animal Type:
              </Typography>

              <TableContainer component={Paper} sx={{ boxShadow: 'none', mb: 3 }}>
                <Table size="small">
                  <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                    <TableRow>
                      <TableCell>Animal Type</TableCell>
                      <TableCell>Age Limit (Years)</TableCell>
                      <TableCell>Breeding Limit</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {Object.entries(RETIREMENT_AGE).map(([type, age]) => (
                      <TableRow key={type}>
                        <TableCell>{type}</TableCell>
                        <TableCell>{age}</TableCell>
                        <TableCell>{BREEDING_LIMIT[type]}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <Typography variant="subtitle1" gutterBottom>
                Retirement Benefits:
              </Typography>

              <Box component="ul" sx={{ pl: 2 }}>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  Improved herd productivity by removing less productive animals
                </Typography>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  Reduced feed costs and resource allocation
                </Typography>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  Better genetic improvement through selective breeding
                </Typography>
                <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                  Improved animal welfare by preventing overbreeding
                </Typography>
                <Typography component="li" variant="body2">
                  Accurate asset valuation and financial planning
                </Typography>
              </Box>
            </Box>
          </AnimatedBackgroundCard>
        </Grid>
      </Grid>

      {/* Retirement History */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
          Retirement History
        </Typography>
        <TableContainer component={Paper} sx={{ boxShadow: theme.shadows[2], borderRadius: 2 }}>
          <Table>
            <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <TableRow>
                <TableCell>Tag Number</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Age at Retirement</TableCell>
                <TableCell>Breeding Count</TableCell>
                <TableCell>Retirement Date</TableCell>
                <TableCell>Reason</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {retirementHistory.length > 0 ? (
                retirementHistory.map((record) => (
                  <TableRow key={record.id} hover>
                    <TableCell>{record.tagNumber}</TableCell>
                    <TableCell>{record.name}</TableCell>
                    <TableCell>{record.type}</TableCell>
                    <TableCell>{record.age} years</TableCell>
                    <TableCell>{record.breedingCount}</TableCell>
                    <TableCell>{formatDate(record.retirementDate)}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.reason}
                        color={
                          record.reason === 'Age' ? 'primary' :
                          record.reason === 'Breeding Limit' ? 'secondary' :
                          record.reason === 'Health Issues' ? 'error' : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={7} align="center">
                    No retirement history found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Box>

      {/* Confirmation Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        aria-labelledby="retirement-dialog-title"
      >
        <DialogTitle id="retirement-dialog-title">
          Confirm Retirement
        </DialogTitle>
        <DialogContent>
          {selectedAnimal && (
            <Box>
              <Alert severity="warning" sx={{ mb: 2 }}>
                You are about to retire {selectedAnimal.name} ({selectedAnimal.tagNumber}).
                This will change the animal's status to "Retired".
              </Alert>
              <Typography variant="body2" gutterBottom>
                <strong>Retirement Reason:</strong> {retirementReason}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Retirement Date:</strong> {formatDate(retirementDate)}
              </Typography>
              {retirementNotes && (
                <Typography variant="body2" gutterBottom>
                  <strong>Notes:</strong> {retirementNotes}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={() => setDialogOpen(false)}>Cancel</CustomButton>
          <CustomButton onClick={handleConfirmRetirement} color="primary" variant="contained">
            Confirm Retirement
          </CustomButton>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </Box>
  );
};

export default RetirementTracking;
