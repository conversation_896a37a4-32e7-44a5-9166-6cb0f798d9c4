import React, { useState } from 'react';
import { 
  DocumentTextIcon, 
  ArrowDownTrayIcon, 
  EyeIcon, 
  PlusIcon, 
  TrashIcon, 
  PencilIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

interface Document {
  id: string;
  name: string;
  type: 'certificate' | 'permit' | 'license' | 'registration' | 'other';
  issueDate: Date;
  expiryDate?: Date;
  issuedBy: string;
  documentUrl: string;
  status: 'active' | 'expired' | 'pending';
  notes?: string;
}

const ComplianceDocuments: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([
    {
      id: 'doc-001',
      name: 'Livestock Identification and Traceability System (LITS) Certificate',
      type: 'certificate',
      issueDate: new Date('2023-05-15'),
      expiryDate: new Date('2024-05-15'),
      issuedBy: 'Department of Agriculture, Land Reform and Rural Development',
      documentUrl: 'https://example.com/lits-cert.pdf',
      status: 'active',
      notes: 'Required for all livestock operations'
    },
    {
      id: 'doc-002',
      name: 'Red Meat Abattoir Association Compliance Certificate',
      type: 'certificate',
      issueDate: new Date('2023-11-10'),
      expiryDate: new Date('2024-11-10'),
      issuedBy: 'Red Meat Abattoir Association',
      documentUrl: 'https://example.com/rmaa-cert.pdf',
      status: 'active',
      notes: 'Annual certification for meat production'
    },
    {
      id: 'doc-003',
      name: 'Livestock Movement Permit',
      type: 'permit',
      issueDate: new Date('2024-01-10'),
      expiryDate: new Date('2024-07-10'),
      issuedBy: 'Provincial Veterinary Services',
      documentUrl: 'https://example.com/movement-permit.pdf',
      status: 'active',
      notes: 'Valid for transport within the province'
    },
    {
      id: 'doc-004',
      name: 'Stock Theft Act Registration',
      type: 'registration',
      issueDate: new Date('2023-03-22'),
      expiryDate: undefined,
      issuedBy: 'South African Police Service',
      documentUrl: 'https://example.com/stock-theft-reg.pdf',
      status: 'active',
      notes: 'Permanent registration under Act 57 of 1959'
    },
    {
      id: 'doc-005',
      name: 'Animal Diseases Act Compliance Certificate',
      type: 'certificate',
      issueDate: new Date('2023-08-05'),
      expiryDate: new Date('2024-08-05'),
      issuedBy: 'State Veterinarian',
      documentUrl: 'https://example.com/disease-cert.pdf',
      status: 'active',
      notes: 'Compliance with Act 35 of 1984'
    },
    {
      id: 'doc-006',
      name: 'Animal Improvement Act Registration',
      type: 'registration',
      issueDate: new Date('2022-11-30'),
      expiryDate: undefined,
      issuedBy: 'Department of Agriculture, Land Reform and Rural Development',
      documentUrl: 'https://example.com/improvement-reg.pdf',
      status: 'active',
      notes: 'Registration under Act 62 of 1998'
    },
    {
      id: 'doc-007',
      name: 'Veterinary Fence Compliance Certificate',
      type: 'certificate',
      issueDate: new Date('2023-10-15'),
      expiryDate: new Date('2024-10-15'),
      issuedBy: 'Provincial Department of Agriculture',
      documentUrl: 'https://example.com/fence-cert.pdf',
      status: 'active',
      notes: 'Annual certification for disease control zones'
    }
  ]);

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);

  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setShowViewModal(true);
  };

  const handleDownloadDocument = (document: Document) => {
    // In a real implementation, this would trigger a download
    window.open(document.documentUrl, '_blank');
  };

  const handleDeleteDocument = (documentId: string) => {
    setDocuments(documents.filter(doc => doc.id !== documentId));
  };

  const getDocumentTypeIcon = (type: Document['type']) => {
    switch (type) {
      case 'certificate':
        return <DocumentTextIcon className="h-6 w-6 text-blue-500" />;
      case 'permit':
        return <DocumentTextIcon className="h-6 w-6 text-green-500" />;
      case 'license':
        return <DocumentTextIcon className="h-6 w-6 text-purple-500" />;
      case 'registration':
        return <DocumentTextIcon className="h-6 w-6 text-orange-500" />;
      default:
        return <DocumentTextIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'expired':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Compliance Documents</h2>
        <button
          onClick={() => setShowUploadModal(true)}
          className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Upload Document
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Document
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Issued By
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Issue Date
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expiry Date
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {documents.map((document) => (
              <tr 
                key={document.id}
                className="hover:bg-gray-50"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-gray-100 rounded-md text-xl">
                      {getDocumentTypeIcon(document.type)}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{document.name}</div>
                      {document.notes && (
                        <div className="text-sm text-gray-500">{document.notes}</div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 capitalize">{document.type}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{document.issuedBy}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{formatDate(document.issueDate)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {document.expiryDate ? formatDate(document.expiryDate) : 'No Expiry'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getStatusIcon(document.status)}
                    <span className="ml-1.5 text-sm font-medium capitalize text-gray-900">
                      {document.status}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleViewDocument(document)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDownloadDocument(document)}
                      className="text-green-600 hover:text-green-900"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDeleteDocument(document.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* View Document Modal */}
      {showViewModal && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">{selectedDocument.name}</h3>
              <button
                onClick={() => setShowViewModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            <div className="mb-4">
              <p><span className="font-medium">Type:</span> {selectedDocument.type}</p>
              <p><span className="font-medium">Issued By:</span> {selectedDocument.issuedBy}</p>
              <p><span className="font-medium">Issue Date:</span> {formatDate(selectedDocument.issueDate)}</p>
              {selectedDocument.expiryDate && (
                <p><span className="font-medium">Expiry Date:</span> {formatDate(selectedDocument.expiryDate)}</p>
              )}
              <p><span className="font-medium">Status:</span> {selectedDocument.status}</p>
              {selectedDocument.notes && (
                <p><span className="font-medium">Notes:</span> {selectedDocument.notes}</p>
              )}
            </div>
            <div className="border p-4 rounded-lg bg-gray-50 h-64 flex items-center justify-center">
              <p className="text-gray-500">Document preview would appear here</p>
            </div>
            <div className="mt-4 flex justify-end">
              <button
                onClick={() => setShowViewModal(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md mr-2"
              >
                Close
              </button>
              <button
                onClick={() => handleDownloadDocument(selectedDocument)}
                className="px-4 py-2 bg-primary-600 text-white rounded-md flex items-center"
              >
                <ArrowDownTrayIcon className="h-5 w-5 mr-1" />
                Download
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Upload Document Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold">Upload New Document</h3>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Document Name</label>
                <input
                  type="text"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  placeholder="Enter document name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Document Type</label>
                <select className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2">
                  <option value="certificate">Certificate</option>
                  <option value="permit">Permit</option>
                  <option value="license">License</option>
                  <option value="registration">Registration</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Issued By</label>
                <input
                  type="text"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  placeholder="Enter issuing authority"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Issue Date</label>
                  <input
                    type="date"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Expiry Date</label>
                  <input
                    type="date"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Notes</label>
                <textarea
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                  rows={3}
                  placeholder="Enter any additional notes"
                ></textarea>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Upload File</label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <svg
                      className="mx-auto h-12 w-12 text-gray-400"
                      stroke="currentColor"
                      fill="none"
                      viewBox="0 0 48 48"
                      aria-hidden="true"
                    >
                      <path
                        d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                        strokeWidth={2}
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                      >
                        <span>Upload a file</span>
                        <input id="file-upload" name="file-upload" type="file" className="sr-only" />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PDF, PNG, JPG, GIF up to 10MB</p>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => setShowUploadModal(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-primary-600 text-white rounded-md"
                >
                  Upload
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ComplianceDocuments;
