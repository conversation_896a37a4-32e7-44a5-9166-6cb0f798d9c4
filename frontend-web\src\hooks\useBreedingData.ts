import { useState, useEffect } from 'react';
import { BreedingRecord as TypeBreedingRecord, CalvingRecord as TypeCalvingRecord } from '../types/breeding';
import {
  mockBreedingRecords as mockBreedingRecordsData,
  mockCalvingRecords as mockCalvingRecordsData
} from '../mocks/breedingData';
import { exportToCSV } from '../utils/export';
import { fetchData } from '../services/apiService';
import { useMockData } from '../utils/config';

export const useBreedingData = () => {
  const [breedingRecords, setBreedingRecords] = useState<TypeBreedingRecord[]>([]);
  const [calvingRecords, setCalvingRecords] = useState<TypeCalvingRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBreedingData = async () => {
    try {
      setLoading(true);

      if (useMockData) {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Convert mock data to match the expected types
        const typedBreedingRecords = mockBreedingRecordsData.map(record => ({
          ...record,
          followUpDates: record.followUpDates?.map((date: any) => ({
            ...date,
            // Use type assertion before the comparison
            status: ((date.status as string) === 'pending' ? 'scheduled' : date.status) as any
          })) || []
        })) as TypeBreedingRecord[];

        setBreedingRecords(typedBreedingRecords);
        setCalvingRecords(mockCalvingRecordsData as TypeCalvingRecord[]);
      } else {
        // Fetch real data from API
        const [breedingData, calvingData] = await Promise.all([
          fetchData('/breeding/records'),
          fetchData('/breeding/calving')
        ]);

        setBreedingRecords(breedingData);
        setCalvingRecords(calvingData);
      }
    } catch (err) {
      console.error('Error fetching breeding data:', err);
      setError('Failed to fetch breeding records');
    } finally {
      setLoading(false);
    }
  };

  const exportBreedingRecords = () => {
    const csvData = breedingRecords.map(record => ({
      ID: record.id,
      'Female ID': record.femaleId,
      'Male ID': record.maleId,
      Date: record.date,
      Type: record.type,
      Status: record.status,
      'Expected Due Date': record.expectedDueDate,
      Technician: record.technician,
      Notes: record.notes || ''
    }));

    exportToCSV(csvData, `breeding_records_${new Date().toISOString().split('T')[0]}`);
  };

  useEffect(() => {
    fetchBreedingData();
  }, []);

  return {
    breedingRecords,
    calvingRecords,
    loading,
    error,
    fetchBreedingData,
    exportBreedingRecords
  };
};


