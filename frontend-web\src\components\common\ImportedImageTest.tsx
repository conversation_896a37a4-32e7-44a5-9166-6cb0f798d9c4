import React from 'react';
import { Box, Typography, Card, CardContent } from '@mui/material';
import { sponsorImages } from '../../assets/sponsors';

const ImportedImageTest: React.FC = () => {
  return (
    <Card>
      <CardContent>
        <Typography variant="h4" gutterBottom>Imported Image Test</Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          Testing images imported directly from assets folder
        </Typography>
        
        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>Land Bank</Typography>
          <img 
            src={sponsorImages.landBank} 
            alt="Land Bank" 
            style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block' }} 
          />
        </Box>

        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>Agricultural Research Council</Typography>
          <img 
            src={sponsorImages.arc} 
            alt="Agricultural Research Council" 
            style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block' }} 
          />
        </Box>

        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>Department of Agriculture, Land Reform and Rural Development</Typography>
          <img 
            src={sponsorImages.dalrrd} 
            alt="Department of Agriculture, Land Reform and Rural Development" 
            style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block' }} 
          />
        </Box>

        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>National Agricultural Marketing Council</Typography>
          <img 
            src={sponsorImages.namc} 
            alt="National Agricultural Marketing Council" 
            style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block' }} 
          />
        </Box>

        <Box sx={{ mb: 3, p: 2, bgcolor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="subtitle1" gutterBottom>Perishable Products Export Control Board</Typography>
          <img 
            src={sponsorImages.ppecb} 
            alt="Perishable Products Export Control Board" 
            style={{ maxWidth: '300px', border: '1px solid #ccc', display: 'block' }} 
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default ImportedImageTest;
