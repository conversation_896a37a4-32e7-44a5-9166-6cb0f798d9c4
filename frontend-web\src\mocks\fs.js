// Mock implementation of the fs module for the browser
module.exports = {
  readFileSync: () => '',
  writeFileSync: () => {},
  existsSync: () => false,
  mkdirSync: () => {},
  readdirSync: () => [],
  statSync: () => ({
    isDirectory: () => false,
    isFile: () => false
  }),
  promises: {
    readFile: () => Promise.resolve(''),
    writeFile: () => Promise.resolve(),
    mkdir: () => Promise.resolve(),
    readdir: () => Promise.resolve([]),
    stat: () => Promise.resolve({
      isDirectory: () => false,
      isFile: () => false
    })
  }
};
