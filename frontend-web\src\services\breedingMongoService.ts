import { MongoDbService } from './mongoDbService';
import { BreedingRecord } from '../types/breeding';

/**
 * Service for breeding-related operations
 */
class BreedingMongoService extends MongoDbService<BreedingRecord> {
  constructor() {
    super('breeding_records');
  }

  /**
   * Find breeding records by status
   * @param status Breeding status
   */
  async findByStatus(status: string) {
    return this.findAll({ status });
  }

  /**
   * Find breeding records by animal ID
   * @param animalId Animal ID
   */
  async findByAnimalId(animalId: string) {
    return this.findAll({
      $or: [{ femaleId: animalId }, { maleId: animalId }]
    });
  }

  /**
   * Find breeding records by date range
   * @param startDate Start date
   * @param endDate End date
   */
  async findByDateRange(startDate: Date, endDate: Date) {
    return this.findAll({
      date: { $gte: startDate, $lte: endDate }
    });
  }

  /**
   * Find upcoming due dates
   * @param daysAhead Number of days ahead to look
   */
  async findUpcomingDueDates(daysAhead: number = 30) {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + daysAhead);
    
    return this.findAll({
      expectedDueDate: { $gte: today, $lte: futureDate }
    });
  }
}

export const breedingMongoService = new BreedingMongoService();
