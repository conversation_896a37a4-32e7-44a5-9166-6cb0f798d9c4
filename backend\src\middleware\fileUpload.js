const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const baseDir = 'uploads';
    const entityType = req.params.entityType || 'misc';
    const uploadDir = path.join(baseDir, entityType);

    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
    cb(null, `${file.fieldname}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 5
  }
});

const handleFileUpload = (fieldName) => {
  return async (req, res, next) => {
    try {
      await upload.array(fieldName, 5)(req, res, (err) => {
        if (err instanceof multer.MulterError) {
          logger.error('File upload error:', err);
          return res.status(400).json({
            error: 'File upload error',
            details: err.message
          });
        } else if (err) {
          logger.error('Unknown file upload error:', err);
          return res.status(500).json({
            error: 'File upload failed',
            details: err.message
          });
        }
        
        if (req.files) {
          req.uploadedFiles = req.files.map(file => ({
            filename: file.filename,
            path: file.path,
            size: file.size,
            mimetype: file.mimetype
          }));
        }
        
        next();
      });
    } catch (error) {
      logger.error('File upload handler error:', error);
      res.status(500).json({
        error: 'File upload processing failed',
        details: error.message
      });
    }
  };
};

module.exports = {
  handleFileUpload
};