class BaseError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends BaseError {
  constructor(message) {
    super(message, 400);
  }
}

class AnimalNotFoundError extends BaseError {
  constructor(message) {
    super(message, 404);
  }
}

class FileProcessingError extends BaseError {
  constructor(message) {
    super(message, 400);
  }
}

class DatabaseError extends BaseError {
  constructor(message) {
    super(message, 500);
  }
}

module.exports = {
  BaseError,
  ValidationError,
  AnimalNotFoundError,
  FileProcessingError,
  DatabaseError
};