#!/usr/bin/env python3
"""
Voice Call Script for AMPD Livestock
This script generates audio from text and can initiate voice calls using a GSM modem
"""

import sys
import os
import argparse
import time
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(__file__), 'voice_call.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('voice_call')

def generate_audio(text, output_file):
    """Generate audio file from text using pyttsx3"""
    try:
        import pyttsx3

        logger.info(f"Generating audio for: {text}")
        engine = pyttsx3.init()

        # Configure voice properties
        engine.setProperty('rate', 150)  # Speed of speech

        # Get available voices
        voices = engine.getProperty('voices')
        if voices:
            # Try to find a female voice
            female_voices = [v for v in voices if 'female' in v.name.lower()]
            if female_voices:
                engine.setProperty('voice', female_voices[0].id)
                logger.info(f"Using female voice: {female_voices[0].name}")
            else:
                logger.info(f"Using default voice: {voices[0].name}")
                engine.setProperty('voice', voices[0].id)

        # Generate audio file - use raw string for Windows paths
        # Convert backslashes to forward slashes to avoid Python escape issues
        fixed_output_file = output_file.replace('\\', '/')
        engine.save_to_file(text, r"{}".format(fixed_output_file))
        engine.runAndWait()

        logger.info(f"Audio generated successfully: {output_file}")
        return True
    except ImportError:
        logger.error("pyttsx3 not installed. Install with: pip install pyttsx3")
        return False
    except Exception as e:
        logger.error(f"Error generating audio: {str(e)}")
        return False

def convert_to_mp3(wav_file, mp3_file):
    """Convert WAV to MP3 using ffmpeg"""
    try:
        import subprocess

        logger.info(f"Converting {wav_file} to {mp3_file}")
        result = subprocess.run(
            ['ffmpeg', '-y', '-i', wav_file, mp3_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        if result.returncode == 0:
            logger.info("Conversion successful")
            return True
        else:
            logger.error(f"Conversion failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Error converting to MP3: {str(e)}")
        return False

def make_voice_call(phone_number, audio_file):
    """Make a voice call using the GSM modem"""
    try:
        logger.info(f"Initiating voice call to {phone_number} with audio {audio_file}")

        # In a real implementation, this would use AT commands to initiate a call
        # For now, we'll simulate a call
        logger.info("Simulating voice call (not actually calling)")
        logger.info(f"Would play {audio_file} to {phone_number}")

        # Simulate call duration
        time.sleep(2)

        logger.info("Call completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error making voice call: {str(e)}")
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Make a voice call with text-to-speech')
    parser.add_argument('--phone', '-p', required=True, help='Phone number to call')
    parser.add_argument('--message', '-m', required=True, help='Message to speak')
    parser.add_argument('--output-dir', '-o', default='../audio', help='Output directory for audio files')

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Generate timestamp for filenames
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    wav_file = os.path.join(args.output_dir, f'call_{timestamp}.wav')
    mp3_file = os.path.join(args.output_dir, f'call_{timestamp}.mp3')

    # Generate audio
    if not generate_audio(args.message, wav_file):
        logger.error("Failed to generate audio")
        return 1

    # Convert to MP3
    if not convert_to_mp3(wav_file, mp3_file):
        logger.warning("Failed to convert to MP3, will use WAV file")
        audio_file = wav_file
    else:
        audio_file = mp3_file

    # Make the call
    if not make_voice_call(args.phone, audio_file):
        logger.error("Failed to make voice call")
        return 1

    logger.info("Voice call process completed successfully")
    return 0

if __name__ == '__main__':
    sys.exit(main())
