import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, CircularProgress } from '@mui/material';
import {  Add, Visibility, Edit  } from '../../../utils/iconImports';
import { ROUTES } from '../../../constants/routes';
import { mockBreedingRecords } from '../../../mocks/breedingData';
import { BreedingRecord } from '../../../mocks/breedingData';

interface ModuleMetric {
  label: string;
  value: string;
  trend: string;
  status: 'positive' | 'negative' | 'neutral';
}

interface QuickAction {
  name: string;
  icon: string;
  path: string;
}

const BreedingDashboard: React.FC = () => {
  const [selectedRecord, setSelectedRecord] = useState<BreedingRecord | null>(null);
  const [loading] = useState(false);

  const metrics: ModuleMetric[] = [
    { label: 'Active Breeding', value: '18', trend: '+5%', status: 'positive' },
    { label: 'Success Rate', value: '78%', trend: '+3%', status: 'positive' },
    { label: 'Upcoming Births', value: '7', trend: 'neutral', status: 'neutral' },
    { label: 'Heat Detection', value: '92%', trend: '+4%', status: 'positive' },
  ];

  const quickActions: QuickAction[] = [
    { name: 'New Breeding', icon: '🧬', path: `${ROUTES.BREEDING}/new` },
    { name: 'Heat Records', icon: '🔥', path: `${ROUTES.BREEDING}/heat-records` },
    { name: 'Birth Records', icon: '🐣', path: `${ROUTES.BREEDING}/birth-records` },
    { name: 'Genetic Analysis', icon: '🧪', path: `${ROUTES.BREEDING}/genetics` },
  ];

  const handleView = (record: BreedingRecord) => {
    setSelectedRecord(record);
  };

  if (loading) {
    return <CircularProgress />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Breeding Dashboard</h1>
          <p className="text-gray-600">Manage breeding programs and reproductive health</p>
        </div>
        <CustomButton 
          variant="contained" 
          startIcon={<Add />}
          className="bg-primary-600"
          component={Link}
          to={`${ROUTES.BREEDING}/new`}
        >
          New Breeding Record
        </CustomButton>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-md p-4"
          >
            <div className="flex justify-between items-start">
              <h3 className="text-gray-500 text-sm">{metric.label}</h3>
              <span className={`text-sm px-2 py-1 rounded ${
                metric.status === 'positive' ? 'bg-green-100 text-green-800' :
                metric.status === 'negative' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {metric.trend}
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-800 mt-2">{metric.value}</p>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <Link
              key={action.name}
              to={action.path}
              className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <span className="text-2xl mb-2">{action.icon}</span>
              <span className="text-sm text-gray-600">{action.name}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Breeding Records */}
      <div className="bg-white rounded-xl shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Recent Breeding Records</h2>
        <TableContainer component={Paper} className="shadow-md">
          <Table>
            <TableHead className="bg-gray-100">
              <TableRow>
                <TableCell><strong>ID</strong></TableCell>
                <TableCell><strong>Female ID</strong></TableCell>
                <TableCell><strong>Male ID</strong></TableCell>
                <TableCell><strong>Date</strong></TableCell>
                <TableCell><strong>Type</strong></TableCell>
                <TableCell><strong>Status</strong></TableCell>
                <TableCell><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mockBreedingRecords.slice(0, 5).map((record) => (
                <TableRow key={record.id} hover>
                  <TableCell>{record.id}</TableCell>
                  <TableCell>{record.femaleId}</TableCell>
                  <TableCell>{record.maleId}</TableCell>
                  <TableCell>{record.date}</TableCell>
                  <TableCell>{record.type}</TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      record.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                      record.status === 'unsuccessful' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {record.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" onClick={() => handleView(record)}>
                      <Visibility fontSize="small" />
                    </IconButton>
                    <IconButton size="small" component={Link} to={`${ROUTES.BREEDING}/edit/${record.id}`}>
                      <Edit fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <CustomButton 
          variant="outlined" 
          className="mt-4"
          component={Link}
          to={ROUTES.BREEDING}
        >
          View All Records
        </CustomButton>
      </div>
    </div>
  );
};

export default BreedingDashboard;

