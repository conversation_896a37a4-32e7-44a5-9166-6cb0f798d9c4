-- AMPD Livestock Management System Database Schema
-- SQL Server (SSMS) Schema

-- Drop tables if they exist (in reverse order of dependencies)
IF OBJECT_ID('dbo.BreedingRecords', 'U') IS NOT NULL DROP TABLE dbo.BreedingRecords;
IF OBJECT_ID('dbo.HeatCycles', 'U') IS NOT NULL DROP TABLE dbo.HeatCycles;
IF OBJECT_ID('dbo.HealthRecords', 'U') IS NOT NULL DROP TABLE dbo.HealthRecords;
IF OBJECT_ID('dbo.FeedingRecords', 'U') IS NOT NULL DROP TABLE dbo.FeedingRecords;
IF OBJECT_ID('dbo.FeedInventory', 'U') IS NOT NULL DROP TABLE dbo.FeedInventory;
IF OBJECT_ID('dbo.WeightRecords', 'U') IS NOT NULL DROP TABLE dbo.WeightRecords;
IF OBJECT_ID('dbo.Animals', 'U') IS NOT NULL DROP TABLE dbo.Animals;
IF OBJECT_ID('dbo.Locations', 'U') IS NOT NULL DROP TABLE dbo.Locations;
IF OBJECT_ID('dbo.Users', 'U') IS NOT NULL DROP TABLE dbo.Users;
IF OBJECT_ID('dbo.UserRoles', 'U') IS NOT NULL DROP TABLE dbo.UserRoles;
IF OBJECT_ID('dbo.AuditLogs', 'U') IS NOT NULL DROP TABLE dbo.AuditLogs;

-- Create UserRoles table
CREATE TABLE dbo.UserRoles (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(50) NOT NULL UNIQUE,
    description NVARCHAR(255),
    permissions NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create Users table
CREATE TABLE dbo.Users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) NOT NULL UNIQUE,
    email NVARCHAR(100) NOT NULL UNIQUE,
    password NVARCHAR(255) NOT NULL,
    first_name NVARCHAR(50) NOT NULL,
    last_name NVARCHAR(50) NOT NULL,
    role_id INT NOT NULL REFERENCES dbo.UserRoles(id),
    is_active BIT DEFAULT 1,
    last_login DATETIME2,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create Locations table
CREATE TABLE dbo.Locations (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    type NVARCHAR(50) NOT NULL, -- paddock, barn, pen, etc.
    capacity INT,
    current_count INT DEFAULT 0,
    description NVARCHAR(255),
    created_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create Animals table
CREATE TABLE dbo.Animals (
    id INT IDENTITY(1,1) PRIMARY KEY,
    tag_number NVARCHAR(50) NOT NULL UNIQUE,
    name NVARCHAR(100),
    animal_type NVARCHAR(50) NOT NULL, -- cattle, sheep, goat, etc.
    breed NVARCHAR(100),
    gender NVARCHAR(20) NOT NULL,
    birth_date DATE,
    weight DECIMAL(10, 2),
    status NVARCHAR(50) NOT NULL DEFAULT 'active', -- active, sold, deceased, etc.
    health_status NVARCHAR(50) DEFAULT 'healthy', -- healthy, sick, quarantine, etc.
    location_id INT REFERENCES dbo.Locations(id),
    parent_female_id INT REFERENCES dbo.Animals(id), -- mother
    parent_male_id INT REFERENCES dbo.Animals(id), -- father
    purchase_date DATE,
    purchase_price DECIMAL(10, 2),
    rfid_tag NVARCHAR(100),
    notes NVARCHAR(MAX),
    created_by INT REFERENCES dbo.Users(id),
    updated_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create WeightRecords table
CREATE TABLE dbo.WeightRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT NOT NULL REFERENCES dbo.Animals(id),
    weight DECIMAL(10, 2) NOT NULL,
    height DECIMAL(10, 2),
    date DATE NOT NULL,
    notes NVARCHAR(255),
    created_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE()
);

-- Create FeedInventory table
CREATE TABLE dbo.FeedInventory (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    type NVARCHAR(50) NOT NULL, -- hay, grain, supplement, etc.
    quantity DECIMAL(10, 2) NOT NULL,
    unit NVARCHAR(20) NOT NULL, -- kg, ton, bale, etc.
    cost_per_unit DECIMAL(10, 2),
    supplier NVARCHAR(100),
    purchase_date DATE,
    expiration_date DATE,
    storage_location NVARCHAR(100),
    notes NVARCHAR(255),
    created_by INT REFERENCES dbo.Users(id),
    updated_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create FeedingRecords table
CREATE TABLE dbo.FeedingRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT REFERENCES dbo.Animals(id),
    group_id NVARCHAR(50), -- For group feeding, can be NULL for individual
    feed_id INT REFERENCES dbo.FeedInventory(id),
    quantity DECIMAL(10, 2) NOT NULL,
    unit NVARCHAR(20) NOT NULL,
    feeding_date DATETIME2 NOT NULL,
    notes NVARCHAR(255),
    created_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE()
);

-- Create HealthRecords table
CREATE TABLE dbo.HealthRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT NOT NULL REFERENCES dbo.Animals(id),
    record_type NVARCHAR(50) NOT NULL, -- vaccination, treatment, check-up, etc.
    date DATETIME2 NOT NULL,
    diagnosis NVARCHAR(255),
    treatment NVARCHAR(255),
    medication NVARCHAR(100),
    dosage NVARCHAR(50),
    veterinarian NVARCHAR(100),
    cost DECIMAL(10, 2),
    follow_up_date DATE,
    status NVARCHAR(50) NOT NULL, -- pending, completed, etc.
    notes NVARCHAR(MAX),
    created_by INT REFERENCES dbo.Users(id),
    updated_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create HeatCycles table
CREATE TABLE dbo.HeatCycles (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT NOT NULL REFERENCES dbo.Animals(id),
    start_date DATE NOT NULL,
    end_date DATE,
    intensity NVARCHAR(20), -- low, medium, high
    symptoms NVARCHAR(255),
    action NVARCHAR(50), -- monitor, breed, skip
    notes NVARCHAR(255),
    created_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create BreedingRecords table
CREATE TABLE dbo.BreedingRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    female_id INT NOT NULL REFERENCES dbo.Animals(id),
    male_id INT REFERENCES dbo.Animals(id), -- Can be NULL for artificial insemination
    breeding_date DATE NOT NULL,
    breeding_method NVARCHAR(50) NOT NULL, -- natural, artificial insemination
    ai_batch_number NVARCHAR(50), -- For artificial insemination
    ai_technician NVARCHAR(100), -- For artificial insemination
    ai_cost DECIMAL(10, 2), -- For artificial insemination
    expected_due_date DATE,
    pregnancy_check_date DATE,
    pregnancy_result NVARCHAR(20), -- positive, negative, uncertain
    actual_birth_date DATE,
    birth_outcome NVARCHAR(50), -- successful, complications, unsuccessful
    offspring_count INT,
    notes NVARCHAR(MAX),
    status NVARCHAR(50) NOT NULL, -- planned, in-progress, successful, unsuccessful
    created_by INT REFERENCES dbo.Users(id),
    updated_by INT REFERENCES dbo.Users(id),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create AuditLogs table
CREATE TABLE dbo.AuditLogs (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT REFERENCES dbo.Users(id),
    action NVARCHAR(50) NOT NULL,
    entity_type NVARCHAR(50) NOT NULL,
    entity_id INT,
    details NVARCHAR(MAX),
    ip_address NVARCHAR(50),
    created_at DATETIME2 DEFAULT GETDATE()
);

-- Create RFID Readers table
CREATE TABLE dbo.RFIDReaders (
    id INT IDENTITY(1,1) PRIMARY KEY,
    reader_id VARCHAR(50) NOT NULL UNIQUE,
    location_id INT REFERENCES dbo.Locations(id),
    name NVARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    last_heartbeat DATETIME2,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create RFID Location History table
CREATE TABLE dbo.RFIDLocationHistory (
    id INT IDENTITY(1,1) PRIMARY KEY,
    tag_id VARCHAR(50) NOT NULL,
    reader_id INT REFERENCES dbo.RFIDReaders(id),
    animal_id INT REFERENCES dbo.Animals(id),
    timestamp DATETIME2 NOT NULL,
    signal_strength DECIMAL(5,2),
    battery_level INT
);

-- Add battery_level and last_location_update to Animals table
ALTER TABLE dbo.Animals ADD
    rfid_battery_level INT,
    last_location_update DATETIME2;

-- Create indexes for performance
CREATE INDEX IX_Animals_tag_number ON dbo.Animals(tag_number);
CREATE INDEX IX_Animals_animal_type ON dbo.Animals(animal_type);
CREATE INDEX IX_Animals_status ON dbo.Animals(status);
CREATE INDEX IX_Animals_location_id ON dbo.Animals(location_id);
CREATE INDEX IX_HealthRecords_animal_id ON dbo.HealthRecords(animal_id);
CREATE INDEX IX_HealthRecords_date ON dbo.HealthRecords(date);
CREATE INDEX IX_BreedingRecords_female_id ON dbo.BreedingRecords(female_id);
CREATE INDEX IX_BreedingRecords_breeding_date ON dbo.BreedingRecords(breeding_date);
CREATE INDEX IX_BreedingRecords_status ON dbo.BreedingRecords(status);
CREATE INDEX IX_WeightRecords_animal_id ON dbo.WeightRecords(animal_id);
CREATE INDEX IX_FeedingRecords_animal_id ON dbo.FeedingRecords(animal_id);
CREATE INDEX IX_FeedingRecords_feeding_date ON dbo.FeedingRecords(feeding_date);
CREATE INDEX IX_HeatCycles_animal_id ON dbo.HeatCycles(animal_id);
CREATE INDEX IX_HeatCycles_start_date ON dbo.HeatCycles(start_date);

-- Feed Management Tables
CREATE TABLE dbo.FeedTypes (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100) NOT NULL,
    category NVARCHAR(50) NOT NULL, -- concentrate, forage, supplement
    description NVARCHAR(MAX),
    nutritional_value JSON,
    unit_of_measure NVARCHAR(20),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

CREATE TABLE dbo.FeedInventory (
    id INT IDENTITY(1,1) PRIMARY KEY,
    feed_type_id INT REFERENCES dbo.FeedTypes(id),
    batch_number NVARCHAR(50),
    quantity DECIMAL(10,2),
    unit_cost DECIMAL(10,2),
    purchase_date DATE,
    expiry_date DATE,
    supplier_id INT,
    storage_location NVARCHAR(100),
    minimum_stock_level DECIMAL(10,2),
    status NVARCHAR(20), -- available, low, depleted
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

CREATE TABLE dbo.FeedingSchedules (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_group_id INT,
    feed_type_id INT REFERENCES dbo.FeedTypes(id),
    quantity_per_animal DECIMAL(10,2),
    feeding_time TIME,
    frequency NVARCHAR(20), -- daily, twice-daily, weekly
    notes NVARCHAR(MAX),
    status NVARCHAR(20),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Health Management Tables
CREATE TABLE dbo.HealthRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT,
    record_type NVARCHAR(50), -- checkup, treatment, vaccination
    date DATETIME2,
    diagnosis NVARCHAR(255),
    treatment NVARCHAR(MAX),
    medication NVARCHAR(255),
    dosage NVARCHAR(50),
    veterinarian_id INT,
    cost DECIMAL(10,2),
    follow_up_date DATE,
    status NVARCHAR(20),
    notes NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

CREATE TABLE dbo.VaccinationSchedule (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT,
    vaccine_name NVARCHAR(100),
    due_date DATE,
    administered_date DATE,
    administered_by INT,
    batch_number NVARCHAR(50),
    notes NVARCHAR(MAX),
    status NVARCHAR(20),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Pregnancy Monitoring Tables
CREATE TABLE dbo.PregnancyRecords (
    id INT IDENTITY(1,1) PRIMARY KEY,
    animal_id INT,
    breeding_date DATE,
    confirmed_date DATE,
    expected_due_date DATE,
    actual_birth_date DATE,
    status NVARCHAR(20), -- confirmed, suspected, completed
    notes NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

CREATE TABLE dbo.PregnancyChecks (
    id INT IDENTITY(1,1) PRIMARY KEY,
    pregnancy_record_id INT REFERENCES dbo.PregnancyRecords(id),
    check_date DATE,
    performed_by INT,
    method NVARCHAR(50),
    results NVARCHAR(MAX),
    notes NVARCHAR(MAX),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
