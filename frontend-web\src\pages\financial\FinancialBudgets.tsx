import React, { useState } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Paper, Grid, Card, CardContent, LinearProgress, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, IconButton, Tabs, Tab } from '@mui/material';
import {  Add, Edit, Delete, Warning, CheckCircle, TrendingUp, TrendingDown  } from '../../utils/iconImports';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

// Mock budget data
const mockBudgets = [
  { 
    id: 'BUD001', 
    name: 'Feed Budget', 
    category: 'Operational',
    allocated: 45000, 
    spent: 35000, 
    remaining: 10000,
    status: 'on-track'
  },
  { 
    id: 'BUD002', 
    name: 'Veterinary Services', 
    category: 'Operational',
    allocated: 20000, 
    spent: 18000, 
    remaining: 2000,
    status: 'warning'
  },
  { 
    id: 'BUD003', 
    name: 'Equipment Maintenance', 
    category: 'Maintenance',
    allocated: 15000, 
    spent: 8000, 
    remaining: 7000,
    status: 'on-track'
  },
  { 
    id: 'BUD004', 
    name: 'Labor Costs', 
    category: 'Operational',
    allocated: 60000, 
    spent: 30000, 
    remaining: 30000,
    status: 'on-track'
  },
  { 
    id: 'BUD005', 
    name: 'New Equipment Purchase', 
    category: 'Capital',
    allocated: 25000, 
    spent: 27500, 
    remaining: -2500,
    status: 'over-budget'
  },
  { 
    id: 'BUD006', 
    name: 'Utilities', 
    category: 'Operational',
    allocated: 12000, 
    spent: 6000, 
    remaining: 6000,
    status: 'on-track'
  },
  { 
    id: 'BUD007', 
    name: 'Marketing', 
    category: 'Administrative',
    allocated: 8000, 
    spent: 3000, 
    remaining: 5000,
    status: 'on-track'
  },
];

// Mock budget vs actual data for chart
const budgetVsActualData = [
  { month: 'Jan', budget: 25000, actual: 23500 },
  { month: 'Feb', budget: 25000, actual: 24200 },
  { month: 'Mar', budget: 25000, actual: 26500 },
  { month: 'Apr', budget: 25000, actual: 24800 },
  { month: 'May', budget: 25000, actual: 27200 },
  { month: 'Jun', budget: 25000, actual: 28000 },
];

const FinancialBudgets: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Filter budgets based on tab
  const filteredBudgets = mockBudgets.filter(budget => {
    if (tabValue === 0) return true; // All
    if (tabValue === 1) return budget.status === 'on-track';
    if (tabValue === 2) return budget.status === 'warning';
    if (tabValue === 3) return budget.status === 'over-budget';
    return true;
  });

  // Calculate totals
  const totalAllocated = mockBudgets.reduce((sum, budget) => sum + budget.allocated, 0);
  const totalSpent = mockBudgets.reduce((sum, budget) => sum + budget.spent, 0);
  const totalRemaining = totalAllocated - totalSpent;
  const spentPercentage = (totalSpent / totalAllocated) * 100;

  return (
    <Box p={4}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" fontWeight="bold">Budget Management</Typography>
        <CustomButton variant="contained" color="primary" startIcon={<Add />}>
          Create Budget
        </CustomButton>
      </Box>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'primary.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Total Budget</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                ${totalAllocated.toLocaleString()}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'error.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Total Spent</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                ${totalSpent.toLocaleString()} ({spentPercentage.toFixed(1)}%)
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={spentPercentage} 
                color="error"
                sx={{ height: 8, borderRadius: 4, mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', bgcolor: 'success.light' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold">Remaining Budget</Typography>
              <Typography variant="h4" fontWeight="bold" mt={1}>
                ${totalRemaining.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center" mt={1}>
                {totalRemaining >= 0 ? (
                  <TrendingUp color="success" sx={{ mr: 1 }} />
                ) : (
                  <TrendingDown color="error" sx={{ mr: 1 }} />
                )}
                <Typography variant="body2">
                  {totalRemaining >= 0 ? 'Under budget' : 'Over budget'}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3} mb={3}>
        <Grid item xs={12}>
          <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
            <CardContent>
              <Typography variant="h6" fontWeight="bold" mb={2}>Budget vs. Actual Expenses</Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={budgetVsActualData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="budget" 
                      stroke="#8884d8" 
                      activeDot={{ r: 8 }} 
                      name="Budget"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="actual" 
                      stroke="#82ca9d" 
                      name="Actual"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Paper sx={{ borderRadius: '12px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
        <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="All Budgets" />
            <Tab label="On Track" />
            <Tab label="Warning" />
            <Tab label="Over Budget" />
          </Tabs>
        </Box>

        <TableContainer>
          <Table>
            <TableHead sx={{ bgcolor: 'primary.light' }}>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Budget Name</TableCell>
                <TableCell>Category</TableCell>
                <TableCell>Allocated</TableCell>
                <TableCell>Spent</TableCell>
                <TableCell>Remaining</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Progress</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredBudgets.map((budget) => {
                const spentPercentage = (budget.spent / budget.allocated) * 100;
                let statusColor = 'success';
                if (budget.status === 'warning') statusColor = 'warning';
                if (budget.status === 'over-budget') statusColor = 'error';
                
                return (
                  <TableRow key={budget.id} hover>
                    <TableCell>{budget.id}</TableCell>
                    <TableCell>{budget.name}</TableCell>
                    <TableCell>
                      <Chip 
                        label={budget.category} 
                        size="small"
                        color="default"
                      />
                    </TableCell>
                    <TableCell>${budget.allocated.toLocaleString()}</TableCell>
                    <TableCell>${budget.spent.toLocaleString()}</TableCell>
                    <TableCell>${budget.remaining.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip 
                        label={
                          budget.status === 'on-track' ? 'On Track' : 
                          budget.status === 'warning' ? 'Warning' : 
                          'Over Budget'
                        } 
                        color={statusColor as any}
                        icon={
                          budget.status === 'on-track' ? <CheckCircle /> : 
                          <Warning />
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell width="15%">
                      <Box display="flex" alignItems="center">
                        <Box width="100%" mr={1}>
                          <LinearProgress 
                            variant="determinate" 
                            value={Math.min(spentPercentage, 100)} 
                            color={statusColor as any}
                            sx={{ height: 8, borderRadius: 4 }}
                          />
                        </Box>
                        <Box minWidth={35}>
                          <Typography variant="body2" color="text.secondary">
                            {spentPercentage.toFixed(0)}%
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" color="primary">
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <Delete fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

export default FinancialBudgets;
