/**
 * AuthContext Tests
 *
 * This file contains tests for the AuthContext component.
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '../AuthContext';
import { authAPI } from '../../services/api';

// Mock the API service
jest.mock('../../services/api', () => ({
  authAPI: {
    login: jest.fn(),
    logout: jest.fn(),
    getCurrentUser: jest.fn()
  }
}));

// Test component that uses the auth context
const TestComponent: React.FC = () => {
  const { user, login, logout, isLoading, error, hasPermission } = useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'Loading...' : 'Not loading'}</div>
      <div data-testid="error">{error || 'No error'}</div>
      <div data-testid="user">{user ? JSON.stringify(user) : 'No user'}</div>
      <div data-testid="has-permission">
        {hasPermission('view_animals') ? 'Has permission' : 'No permission'}
      </div>
      <button type="button" onClick={() => login('testuser', 'password')}>Login</button>
      <button type="button" onClick={() => logout()}>Logout</button>
    </div>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    // Clear mocks
    jest.clearAllMocks();

    // Clear localStorage
    localStorage.clear();
  });

  it('should provide initial auth state', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('loading')).toHaveTextContent('Loading...');
    expect(screen.getByTestId('error')).toHaveTextContent('No error');
    expect(screen.getByTestId('user')).toHaveTextContent('No user');
    expect(screen.getByTestId('has-permission')).toHaveTextContent('No permission');
  });

  it('should handle successful login', async () => {
    // Mock successful login
    const mockUser = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      permissions: ['view_animals'],
      status: 'active',
      lastLogin: '2023-01-01T00:00:00.000Z'
    };

    (authAPI.login as jest.Mock).mockResolvedValue({
      success: true,
      user: mockUser,
      token: 'test-token'
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Click login button
    await act(async () => {
      userEvent.click(screen.getByText('Login'));
    });

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Check that login was called with correct parameters
    expect(authAPI.login).toHaveBeenCalledWith('testuser', 'password');

    // Check that user is set
    expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockUser));

    // Check that token is stored in localStorage
    expect(localStorage.getItem('token')).toBe('test-token');

    // Check that user has permission
    expect(screen.getByTestId('has-permission')).toHaveTextContent('Has permission');
  });

  it('should handle login error', async () => {
    // Mock login error
    (authAPI.login as jest.Mock).mockRejectedValue(new Error('Invalid credentials'));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Click login button
    await act(async () => {
      userEvent.click(screen.getByText('Login'));
    });

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Check that error is set
    expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials');

    // Check that user is not set
    expect(screen.getByTestId('user')).toHaveTextContent('No user');

    // Check that token is not stored in localStorage
    expect(localStorage.getItem('token')).toBeNull();
  });

  it('should handle logout', async () => {
    // Mock successful login
    const mockUser = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      permissions: ['view_animals'],
      status: 'active',
      lastLogin: '2023-01-01T00:00:00.000Z'
    };

    (authAPI.login as jest.Mock).mockResolvedValue({
      success: true,
      user: mockUser,
      token: 'test-token'
    });

    // Mock successful logout
    (authAPI.logout as jest.Mock).mockResolvedValue({ success: true });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Login first
    await act(async () => {
      userEvent.click(screen.getByText('Login'));
    });

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockUser));
    });

    // Click logout button
    await act(async () => {
      userEvent.click(screen.getByText('Logout'));
    });

    // Wait for logout to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Check that logout was called
    expect(authAPI.logout).toHaveBeenCalled();

    // Check that user is cleared
    expect(screen.getByTestId('user')).toHaveTextContent('No user');

    // Check that token is removed from localStorage
    expect(localStorage.getItem('token')).toBeNull();
  });

  it('should initialize from localStorage', async () => {
    // Set up localStorage with user and token
    const mockUser = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      role: 'admin',
      permissions: ['view_animals'],
      status: 'active',
      lastLogin: '2023-01-01T00:00:00.000Z'
    };

    localStorage.setItem('token', 'test-token');
    localStorage.setItem('user', JSON.stringify(mockUser));

    // Mock successful getCurrentUser
    (authAPI.getCurrentUser as jest.Mock).mockResolvedValue({
      success: true,
      data: mockUser
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not loading');
    });

    // Check that user is set from localStorage
    expect(screen.getByTestId('user')).toHaveTextContent(JSON.stringify(mockUser));

    // Check that hasPermission works
    expect(screen.getByTestId('has-permission')).toHaveTextContent('Has permission');
  });
});
