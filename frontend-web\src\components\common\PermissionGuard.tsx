import React from 'react';
import { usePermissions, Permission } from '../../hooks/usePermissions';

interface PermissionGuardProps {
  permission: Permission | Permission[];
  fallback?: React.ReactNode;
  children: React.ReactNode;
  requireAll?: boolean;
}

/**
 * Component that conditionally renders children based on user permissions
 * @param permission Single permission or array of permissions required
 * @param fallback Optional fallback component to render if permission is denied
 * @param children Content to render if permission is granted
 * @param requireAll If true, user must have all permissions in the array (default: false)
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  fallback = null,
  children,
  requireAll = false
}) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = usePermissions();
  
  // Check if user has required permissions
  const hasAccess = () => {
    if (Array.isArray(permission)) {
      return requireAll ? hasAllPermissions(permission) : hasAnyPermission(permission);
    }
    return hasPermission(permission);
  };
  
  // Render children if user has permission, otherwise render fallback
  return hasAccess() ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGuard;
