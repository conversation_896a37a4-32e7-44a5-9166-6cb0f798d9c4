/**
 * MongoDB Schemas Index
 * 
 * This file exports all MongoDB schemas for the application
 */

// Import all schemas
const Animal = require('./animal.schema');
const User = require('./user.schema');
const { FeedingRecord, FeedingSchedule } = require('./feeding.schema');
const { HealthRecord, VaccinationSchedule } = require('./health.schema');
const { BreedingRecord, BirthRecord, HeatDetection } = require('./breeding.schema');
const { Transaction, Budget, Asset } = require('./financial.schema');
const { InventoryItem, InventoryTransaction, Supplier } = require('./inventory.schema');
const { Report, ReportResult } = require('./report.schema');
const { SystemSettings, UserSettings } = require('./settings.schema');
const { BusinessStrategy, MarketAnalysis, PredictiveAnalysis } = require('./business.schema');

// Export all schemas
module.exports = {
  // Animal Management
  Animal,
  
  // User Management
  User,
  
  // Feeding Management
  FeedingRecord,
  FeedingSchedule,
  
  // Health Management
  HealthRecord,
  VaccinationSchedule,
  
  // Breeding Management
  BreedingRecord,
  BirthRecord,
  HeatDetection,
  
  // Financial Management
  Transaction,
  Budget,
  Asset,
  
  // Inventory Management
  InventoryItem,
  InventoryTransaction,
  Supplier,
  
  // Reports
  Report,
  ReportResult,
  
  // Settings
  SystemSettings,
  UserSettings,
  
  // Business Analysis
  BusinessStrategy,
  MarketAnalysis,
  PredictiveAnalysis
};
