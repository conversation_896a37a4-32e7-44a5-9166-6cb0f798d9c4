const db = require('../config/database');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');
const logger = require('./logger');

class DataExporter {
  constructor(entityType) {
    this.entityType = entityType;
    this.tempDir = os.tmpdir();
  }

  async fetchData(filters = {}) {
    const query = db(this.entityType);
    
    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        query.whereIn(key, value);
      } else if (typeof value === 'object') {
        if (value.from && value.to) {
          query.whereBetween(key, [value.from, value.to]);
        } else if (value.like) {
          query.where(key, 'like', `%${value.like}%`);
        }
      } else {
        query.where(key, value);
      }
    });

    return await query.select('*');
  }

  async exportToCsv(data) {
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map(row => 
      Object.values(row)
        .map(value => this.formatCsvValue(value))
        .join(',')
    );
    
    const content = [headers, ...rows].join('\n');
    const filePath = path.join(this.tempDir, `export_${Date.now()}.csv`);
    
    await fs.writeFile(filePath, content, 'utf-8');
    return filePath;
  }

  async exportToExcel(data) {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(this.entityType);
    
    // Add headers
    const headers = Object.keys(data[0]);
    worksheet.addRow(headers);
    
    // Add data
    data.forEach(row => {
      worksheet.addRow(Object.values(row));
    });
    
    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    
    const filePath = path.join(this.tempDir, `export_${Date.now()}.xlsx`);
    await workbook.xlsx.writeFile(filePath);
    return filePath;
  }

  async exportToPdf(data) {
    const doc = new PDFDocument();
    const filePath = path.join(this.tempDir, `export_${Date.now()}.pdf`);
    const writeStream = fs.createWriteStream(filePath);
    
    return new Promise((resolve, reject) => {
      doc.pipe(writeStream);
      
      // Add title
      doc.fontSize(16)
         .text(`${this.entityType.toUpperCase()} REPORT`, { align: 'center' })
         .moveDown();
      
      // Add data table
      const headers = Object.keys(data[0]);
      const tableTop = 150;
      let currentTop = tableTop;
      
      // Draw headers
      headers.forEach((header, i) => {
        doc.fontSize(10)
           .text(header, 50 + (i * 100), currentTop, { width: 90 });
      });
      
      currentTop += 20;
      
      // Draw data rows
      data.forEach(row => {
        if (currentTop > 700) { // Start new page if near bottom
          doc.addPage();
          currentTop = 50;
        }
        
        headers.forEach((header, i) => {
          doc.fontSize(9)
             .text(String(row[header]), 50 + (i * 100), currentTop, { width: 90 });
        });
        
        currentTop += 20;
      });
      
      doc.end();
      
      writeStream.on('finish', () => resolve(filePath));
      writeStream.on('error', reject);
    });
  }

  formatCsvValue(value) {
    if (value === null || value === undefined) return '';
    if (typeof value === 'string') {
      // Escape quotes and wrap in quotes if contains comma or newline
      const escaped = value.replace(/"/g, '""');
      return /[,\n"]/.test(value) ? `"${escaped}"` : value;
    }
    if (value instanceof Date) {
      return value.toISOString();
    }
    return String(value);
  }
}

module.exports = DataExporter;