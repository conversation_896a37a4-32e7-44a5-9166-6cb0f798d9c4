import React, { useState } from 'react';
import { CustomButton } from '../common';
import { useBreedingData } from '../../hooks/useBreedingData';
import { BreedingRecord } from '../../types/breeding';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Typography, Box } from '@mui/material';

const BreedingList: React.FC = () => {
  const { breedingRecords, loading, error, exportBreedingRecords } = useBreedingData();
  const [selectedRecord, setSelectedRecord] = useState<BreedingRecord | null>(null);

  if (loading) {
    return <CircularProgress />;
  }

  if (error) {
    return <Typography color="error">{error}</Typography>;
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" mb={2}>
        <Typography variant="h5">Breeding Records</Typography>
        <CustomButton 
          variant="contained" 
          color="primary" 
          onClick={exportBreedingRecords}
        >
          Export Records
        </CustomButton>
      </Box>
      
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Female ID</TableCell>
              <TableCell>Male ID</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Expected Due Date</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {breedingRecords.map((record) => (
              <TableRow key={record.id}>
                <TableCell>{record.id}</TableCell>
                <TableCell>{record.femaleId}</TableCell>
                <TableCell>{record.maleId}</TableCell>
                <TableCell>{record.date}</TableCell>
                <TableCell>{record.type}</TableCell>
                <TableCell>{record.status}</TableCell>
                <TableCell>{record.expectedDueDate}</TableCell>
                <TableCell>
                  <CustomButton 
                    variant="outlined" 
                    size="small"
                    onClick={() => setSelectedRecord(record)}
                  >
                    View Details
                  </CustomButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      
      {breedingRecords.length === 0 && (
        <Typography align="center" mt={2}>
          No breeding records found
        </Typography>
      )}
    </Box>
  );
};

export default BreedingList;