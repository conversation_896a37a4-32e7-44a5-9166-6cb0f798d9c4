import { ObjectId } from 'mongodb';
import { Animal } from '../types/animal';

/**
 * MongoDB Animal Model
 * This model defines the structure of animal documents in MongoDB
 */
export interface AnimalModel {
  _id?: ObjectId | string;
  tagNumber: string; // Consistent tag format: [SPECIES_CODE]-[YEAR]-[SEQUENCE], e.g., CTL-2024-001
  name: string; // South African names (Sotho, Tswana, Afrikaans)
  type: string; // General type (e.g., Cattle, Sheep)
  breed: string; // Specific breed
  gender: string; // Male or Female
  birthDate?: string; // ISO date format
  weight?: number; // In kg
  status: string; // Active, Sold, Deceased, Quarantined, Breeding, Retired
  healthStatus: 'healthy' | 'sick' | 'injured' | 'pregnant';
  location: string; // Farm location
  purchaseDate?: string; // ISO date format
  purchasePrice?: number; // In ZAR (Rands)
  notes?: string; // Additional information
  parentId?: string; // Reference to parent animal ID (foreign key)
  parentTagNumber?: string; // Reference to parent animal tag (for easier querying)
  sireId?: string; // Father animal ID (foreign key)
  damId?: string; // Mother animal ID (foreign key)
  createdAt: Date;
  updatedAt: Date;
  species?: string; // Same as type, for backward compatibility
  imageUrl?: string; // Path to animal image
  rfidTag?: string; // RFID tag for tracking
  // Asset Management fields
  breedingCount?: number; // Number of times the animal has been bred
  retirementDate?: string; // ISO date format - when the animal was retired
  retirementReason?: string; // Age, Breeding Limit, Health Issues, Performance, Other
  retirementNotes?: string; // Additional notes about retirement
  currentValue?: number; // Current estimated value in ZAR
  depreciationRate?: number; // Annual depreciation rate (%)
  maintenanceCosts?: number; // Total maintenance costs in ZAR
  revenueGenerated?: number; // Total revenue generated in ZAR
  roi?: number; // Return on investment (%)
}

/**
 * Convert MongoDB document to Animal type
 * @param doc MongoDB document
 * @returns Animal object
 */
export const toAnimal = (doc: any): Animal => {
  return {
    id: doc._id?.toString() || '',
    tagNumber: doc.tagNumber || '',
    name: doc.name || '',
    type: doc.type || '',
    breed: doc.breed || '',
    gender: doc.gender || '',
    birthDate: doc.birthDate || '',
    weight: doc.weight || 0,
    status: doc.status || 'Active',
    healthStatus: doc.healthStatus || 'healthy',
    location: doc.location || '',
    purchaseDate: doc.purchaseDate || '',
    purchasePrice: doc.purchasePrice || 0,
    notes: doc.notes || '',
    parentId: doc.parentId || '',
    parentTagNumber: doc.parentTagNumber || '',
    sireId: doc.sireId || '',
    damId: doc.damId || '',
    createdAt: doc.createdAt ? doc.createdAt.toISOString() : new Date().toISOString(),
    updatedAt: doc.updatedAt ? doc.updatedAt.toISOString() : new Date().toISOString(),
    species: doc.species || doc.type || '',
    imageUrl: doc.imageUrl || '',
    rfidTag: doc.rfidTag || '',
    // Asset Management fields
    breedingCount: doc.breedingCount || 0,
    retirementDate: doc.retirementDate || '',
    retirementReason: doc.retirementReason || '',
    retirementNotes: doc.retirementNotes || '',
    currentValue: doc.currentValue || 0,
    depreciationRate: doc.depreciationRate || 0,
    maintenanceCosts: doc.maintenanceCosts || 0,
    revenueGenerated: doc.revenueGenerated || 0,
    roi: doc.roi || 0
  };
};

/**
 * Convert Animal to MongoDB document
 * @param animal Animal object
 * @returns MongoDB document
 */
export const toAnimalModel = (animal: Animal): AnimalModel => {
  const now = new Date();

  return {
    _id: animal.id ? animal.id : new ObjectId(),
    tagNumber: animal.tagNumber || '',
    name: animal.name || '',
    type: animal.type || '',
    breed: animal.breed || '',
    gender: animal.gender || '',
    birthDate: animal.birthDate ? (typeof animal.birthDate === 'string' ? animal.birthDate : animal.birthDate.toISOString()) : '',
    weight: animal.weight || 0,
    status: animal.status || 'Active',
    healthStatus: animal.healthStatus || 'healthy',
    location: animal.location || '',
    purchaseDate: animal.purchaseDate ? (typeof animal.purchaseDate === 'string' ? animal.purchaseDate : animal.purchaseDate.toISOString()) : '',
    purchasePrice: animal.purchasePrice || 0,
    notes: animal.notes || '',
    parentId: animal.parentId || '',
    parentTagNumber: animal.parentTagNumber || '',
    sireId: animal.sireId || '',
    damId: animal.damId || '',
    createdAt: now,
    updatedAt: now,
    species: animal.species || animal.type || '',
    imageUrl: animal.imageUrl || '',
    rfidTag: animal.rfidTag || '',
    // Asset Management fields
    breedingCount: animal.breedingCount || 0,
    retirementDate: animal.retirementDate ? (typeof animal.retirementDate === 'string' ? animal.retirementDate : animal.retirementDate.toISOString()) : '',
    retirementReason: animal.retirementReason || '',
    retirementNotes: animal.retirementNotes || '',
    currentValue: animal.currentValue || 0,
    depreciationRate: animal.depreciationRate || 0,
    maintenanceCosts: animal.maintenanceCosts || 0,
    revenueGenerated: animal.revenueGenerated || 0,
    roi: animal.roi || 0
  };
};
