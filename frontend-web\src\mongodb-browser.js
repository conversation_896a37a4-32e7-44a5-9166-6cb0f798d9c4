// This file provides browser-compatible versions of Node.js modules used by MongoDB
// It's used as a polyfill for webpack

// Mock crypto module
export const crypto = {
  randomBytes: (size) => {
    const array = new Uint8Array(size);
    window.crypto.getRandomValues(array);
    return array;
  },
  createHash: () => ({
    update: () => ({
      digest: () => 'mock-hash'
    })
  })
};

// Mock fs module
export const fs = {
  readFileSync: () => '',
  existsSync: () => false,
  readdirSync: () => []
};

// Mock http module
export const http = {
  request: () => ({
    on: () => {},
    end: () => {}
  })
};

// Mock stream module
export const stream = {
  Readable: class {
    pipe() { return this; }
    on() { return this; }
  },
  Writable: class {
    write() { return true; }
    end() {}
  }
};

// Mock timers module
export const timers = {
  setTimeout,
  clearTimeout,
  setInterval,
  clearInterval
};

// Mock zlib module
export const zlib = {
  createGzip: () => ({
    on: () => {},
    pipe: () => {}
  }),
  createGunzip: () => ({
    on: () => {},
    pipe: () => {}
  })
};

// Mock dns module
export const dns = {
  lookup: (hostname, options, callback) => {
    if (typeof options === 'function') {
      callback = options;
    }
    callback(null, '127.0.0.1', 4);
  },
  resolve: (hostname, callback) => {
    callback(null, ['127.0.0.1']);
  }
};

// Mock net module
export const net = {
  connect: () => ({
    on: () => {},
    end: () => {}
  }),
  Socket: class {
    connect() { return this; }
    on() { return this; }
    end() {}
  }
};

// Mock tls module
export const tls = {
  connect: () => ({
    on: () => {},
    end: () => {}
  }),
  TLSSocket: class {
    connect() { return this; }
    on() { return this; }
    end() {}
  }
};

// Mock child_process module
export const child_process = {
  spawn: () => ({
    on: () => {},
    stdout: { on: () => {} },
    stderr: { on: () => {} }
  }),
  exec: (cmd, callback) => {
    callback(null, '', '');
  }
};

// Export all modules
export default {
  crypto,
  fs,
  http,
  stream,
  timers,
  zlib,
  dns,
  net,
  tls,
  child_process
};
