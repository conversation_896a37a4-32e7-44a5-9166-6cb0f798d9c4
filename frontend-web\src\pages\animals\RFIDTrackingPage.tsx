import React from 'react';
import { Box, Typography, Paper, Tabs, Tab } from '@mui/material';
import { ModuleHeader, ModuleContainer } from '../../components/common';
import RFIDManagement from '../../components/animals/RFIDManagement';
import RFIDTracking from '../../components/animals/RFIDTracking';
import { Pets, LocationOn } from '../../utils/iconImports';

const RFIDTrackingPage: React.FC = () => {
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <ModuleContainer module="animals">
      <ModuleHeader
        title="RFID Management & Tracking"
        subtitle="Manage RFID tags and track animal locations in real-time"
        module="animals"
        submodule="rfid"
        actionLabel="Refresh Data"
        actionIcon={<LocationOn />}
        onAction={() => window.location.reload()}
        icon={<Pets />}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <Paper sx={{ mb: 3, borderRadius: 2, overflow: 'hidden' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2
              }
            }}
          >
            <Tab label="RFID Tracking" icon={<LocationOn />} iconPosition="start" />
            <Tab label="RFID Management" icon={<Pets />} iconPosition="start" />
          </Tabs>

          <Box sx={{ p: 3 }}>
            {tabValue === 0 && <RFIDTracking />}
            {tabValue === 1 && <RFIDManagement />}
          </Box>
        </Paper>
      </Box>
    </ModuleContainer>
  );
};

export default RFIDTrackingPage;
