
// Custom start script to fix webpack issues with Node.js v22
process.env.NODE_ENV = 'development';

// Load environment variables
require('../node_modules/react-scripts/config/env');

const fs = require('fs');
const webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const configFactory = require('../node_modules/react-scripts/config/webpack.config');
const createDevServerConfig = require('../node_modules/react-scripts/config/webpackDevServer.config');
const paths = require('../node_modules/react-scripts/config/paths');
const chalk = require('react-dev-utils/chalk');
const clearConsole = require('react-dev-utils/clearConsole');
const openBrowser = require('react-dev-utils/openBrowser');
const { checkBrowsers } = require('react-dev-utils/browsersHelper');
const { choosePort, createCompiler, prepareUrls } = require('react-dev-utils/WebpackDevServerUtils');

const isInteractive = process.stdout.isTTY;
const DEFAULT_PORT = parseInt(process.env.PORT, 10) || 3000;
const HOST = process.env.HOST || '0.0.0.0';

// Fix for the assets error in Node.js v22
const fixWebpackConfig = (config) => {
  // Add fallbacks for node modules
  config.resolve.fallback = {
    ...config.resolve.fallback,
    fs: false,
    path: false,
    os: false,
    net: false,
    tls: false,
    child_process: false,
    crypto: false,
    stream: false,
    http: false,
    https: false,
    zlib: false,
    dns: false,
    timers: false,
    'timers/promises': false,
    'fs/promises': false,
    querystring: false
  };

  // Add plugins for polyfills
  config.plugins.push(
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer']
    })
  );

  // Fix for the assets error in Node.js v22
  config.module.rules.push({
    test: /\.m?js$/,
    resolve: {
      fullySpecified: false
    }
  });

  // Disable performance hints
  config.performance = {
    hints: false
  };

  // Use a simpler source map for development
  config.devtool = 'eval-source-map';

  // Ensure webpack doesn't try to use Node.js core modules
  config.node = false;

  return config;
};

// Start the development server
checkBrowsers(paths.appPath, isInteractive)
  .then(() => {
    return choosePort(HOST, DEFAULT_PORT);
  })
  .then(port => {
    if (port == null) {
      return;
    }

    const config = fixWebpackConfig(configFactory('development'));
    const protocol = process.env.HTTPS === 'true' ? 'https' : 'http';
    const appName = require(paths.appPackageJson).name;
    const urls = prepareUrls(protocol, HOST, port);
    
    // Create a webpack compiler
    const compiler = createCompiler({
      appName,
      config,
      urls,
      useTypeScript: fs.existsSync(paths.appTsConfig),
      webpack
    });
    
    // Create a webpack dev server configuration
    const serverConfig = createDevServerConfig(urls.lanUrlForConfig);
    const devServer = new WebpackDevServer(serverConfig, compiler);
    
    // Launch the server
    devServer.startCallback(() => {
      if (isInteractive) {
        clearConsole();
      }
      
      console.log(chalk.cyan('Starting the development server...\n'));
      openBrowser(urls.localUrlForBrowser);
    });

    ['SIGINT', 'SIGTERM'].forEach(function(sig) {
      process.on(sig, function() {
        devServer.close();
        process.exit();
      });
    });

    if (process.env.CI !== 'true') {
      process.stdin.on('end', function() {
        devServer.close();
        process.exit();
      });
    }
  })
  .catch(err => {
    if (err && err.message) {
      console.log(err.message);
    }
    process.exit(1);
  });
