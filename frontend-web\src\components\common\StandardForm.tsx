/**
 * Standardized Form Component
 * 
 * This component provides a consistent form interface
 * that can be used across all modules in the application.
 */

import React from 'react';
import {
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Checkbox,
  FormControlLabel,
  Typography,
  Box,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs from 'dayjs';
import { useTranslation } from '../../hooks/useTranslation';

// Field types supported by the form
export type FieldType = 
  | 'text'
  | 'number'
  | 'select'
  | 'multiselect'
  | 'date'
  | 'checkbox'
  | 'textarea'
  | 'currency'
  | 'percentage'
  | 'email'
  | 'password'
  | 'tel'
  | 'url';

// Field definition
export interface FormField<T> {
  name: keyof T;
  label: string;
  type: FieldType;
  required?: boolean;
  options?: { value: string | number; label: string }[];
  min?: number;
  max?: number;
  rows?: number;
  disabled?: boolean;
  fullWidth?: boolean;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  helperText?: string;
  placeholder?: string;
  autoFocus?: boolean;
  hidden?: boolean;
  section?: string;
}

// Form props
interface StandardFormProps<T> {
  fields: FormField<T>[];
  values: Partial<T>;
  errors: Record<string, string>;
  onChange: (field: keyof T, value: any) => void;
  sections?: string[];
  dense?: boolean;
}

function StandardForm<T>({
  fields,
  values,
  errors,
  onChange,
  sections,
  dense = false
}: StandardFormProps<T>) {
  const { translate } = useTranslation();
  const theme = useTheme();

  // Group fields by section if sections are provided
  const fieldsBySection = React.useMemo(() => {
    if (!sections || sections.length === 0) {
      return { '': fields.filter(field => !field.hidden) };
    }

    const result: Record<string, FormField<T>[]> = {};
    sections.forEach(section => {
      result[section] = [];
    });

    // Add default section for fields without a section
    result[''] = [];

    fields.filter(field => !field.hidden).forEach(field => {
      const section = field.section || '';
      if (!result[section]) {
        result[section] = [];
      }
      result[section].push(field);
    });

    return result;
  }, [fields, sections]);

  // Handle text field change
  const handleTextChange = (field: keyof T) => (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(field, e.target.value);
  };

  // Handle number field change
  const handleNumberChange = (field: keyof T) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === '' ? '' : Number(e.target.value);
    onChange(field, value);
  };

  // Handle select field change
  const handleSelectChange = (field: keyof T) => (e: React.ChangeEvent<{ value: unknown }>) => {
    onChange(field, e.target.value);
  };

  // Handle date field change
  const handleDateChange = (field: keyof T) => (date: dayjs.Dayjs | null) => {
    onChange(field, date ? date.toISOString() : null);
  };

  // Handle checkbox field change
  const handleCheckboxChange = (field: keyof T) => (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(field, e.target.checked);
  };

  // Render a single form field
  const renderField = (field: FormField<T>) => {
    const {
      name,
      label,
      type,
      required = false,
      options = [],
      min,
      max,
      rows = 4,
      disabled = false,
      fullWidth = true,
      xs = 12,
      sm = 6,
      md = 6,
      lg = 4,
      xl = 3,
      helperText,
      placeholder,
      autoFocus = false
    } = field;

    const value = values[name] !== undefined ? values[name] : '';
    const error = errors[name as string] || '';
    const hasError = !!error;

    // Common props for all field types
    const commonProps = {
      id: `field-${String(name)}`,
      name: String(name),
      label,
      value,
      required,
      disabled,
      fullWidth,
      error: hasError,
      helperText: error || helperText,
      placeholder,
      autoFocus,
      size: dense ? 'small' : 'medium',
      margin: dense ? 'dense' : 'normal'
    };

    // Render different field types
    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'tel':
      case 'url':
        return (
          <TextField
            {...commonProps}
            type={type}
            onChange={handleTextChange(name)}
          />
        );

      case 'number':
        return (
          <TextField
            {...commonProps}
            type="number"
            inputProps={{ min, max }}
            onChange={handleNumberChange(name)}
          />
        );

      case 'currency':
        return (
          <TextField
            {...commonProps}
            type="number"
            inputProps={{ min, max, step: 0.01 }}
            onChange={handleNumberChange(name)}
            InputProps={{
              startAdornment: <Box component="span" sx={{ mr: 1 }}>$</Box>,
            }}
          />
        );

      case 'percentage':
        return (
          <TextField
            {...commonProps}
            type="number"
            inputProps={{ min: 0, max: 100, step: 0.01 }}
            onChange={handleNumberChange(name)}
            InputProps={{
              endAdornment: <Box component="span" sx={{ ml: 1 }}>%</Box>,
            }}
          />
        );

      case 'textarea':
        return (
          <TextField
            {...commonProps}
            multiline
            rows={rows}
            onChange={handleTextChange(name)}
          />
        );

      case 'select':
        return (
          <FormControl
            fullWidth={fullWidth}
            error={hasError}
            required={required}
            disabled={disabled}
            size={dense ? 'small' : 'medium'}
            margin={dense ? 'dense' : 'normal'}
          >
            <InputLabel id={`${String(name)}-label`}>{label}</InputLabel>
            <Select
              labelId={`${String(name)}-label`}
              id={`field-${String(name)}`}
              value={value || ''}
              onChange={handleSelectChange(name) as any}
              label={label}
            >
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(error || helperText) && (
              <FormHelperText>{error || helperText}</FormHelperText>
            )}
          </FormControl>
        );

      case 'multiselect':
        return (
          <FormControl
            fullWidth={fullWidth}
            error={hasError}
            required={required}
            disabled={disabled}
            size={dense ? 'small' : 'medium'}
            margin={dense ? 'dense' : 'normal'}
          >
            <InputLabel id={`${String(name)}-label`}>{label}</InputLabel>
            <Select
              labelId={`${String(name)}-label`}
              id={`field-${String(name)}`}
              value={value || []}
              onChange={handleSelectChange(name) as any}
              label={label}
              multiple
            >
              {options.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
            {(error || helperText) && (
              <FormHelperText>{error || helperText}</FormHelperText>
            )}
          </FormControl>
        );

      case 'date':
        return (
          <DatePicker
            label={label}
            value={value ? dayjs(value as string) : null}
            onChange={handleDateChange(name)}
            slotProps={{
              textField: {
                ...commonProps,
                fullWidth,
              },
            }}
          />
        );

      case 'checkbox':
        return (
          <FormControl
            fullWidth={fullWidth}
            error={hasError}
            required={required}
            disabled={disabled}
            margin={dense ? 'dense' : 'normal'}
          >
            <FormControlLabel
              control={
                <Checkbox
                  checked={!!value}
                  onChange={handleCheckboxChange(name)}
                  name={String(name)}
                  color="primary"
                  disabled={disabled}
                />
              }
              label={label}
            />
            {(error || helperText) && (
              <FormHelperText>{error || helperText}</FormHelperText>
            )}
          </FormControl>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      {Object.entries(fieldsBySection).map(([section, sectionFields]) => (
        <Box key={section || 'default'} sx={{ mb: 4 }}>
          {section && (
            <Box 
              sx={{ 
                mb: 2, 
                pb: 1, 
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}` 
              }}
            >
              <Typography variant="h6" color="primary">
                {translate ? translate(`form.section.${section}`, { fallback: section }) : section}
              </Typography>
            </Box>
          )}
          <Grid container spacing={2}>
            {sectionFields.map((field) => (
              <Grid
                item
                key={String(field.name)}
                xs={field.xs || 12}
                sm={field.sm || 6}
                md={field.md || 6}
                lg={field.lg || 4}
                xl={field.xl || 3}
              >
                {renderField(field)}
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}
    </Box>
  );
}

export default StandardForm;
