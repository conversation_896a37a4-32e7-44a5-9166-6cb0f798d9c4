import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, TextField, Typography, InputAdornment, IconButton, Alert, Link, CircularProgress, useTheme, alpha, Paper } from '@mui/material';
import { motion } from 'framer-motion';
import { Visibility, VisibilityOff, Lock, Person, ArrowForward } from '../../utils/iconImports';
import { useLanguage } from '../../contexts/LanguageContext';
import LanguageSelector from '../LanguageSelector';

interface EnhancedLoginFormProps {
  onSubmit: (username: string, password: string) => Promise<void>;
  isLoading: boolean;
  error: string;
}

/**
 * EnhancedLoginForm - A beautifully designed login form component
 * Features animated inputs, error handling, and responsive design
 */
const EnhancedLoginForm: React.FC<EnhancedLoginFormProps> = ({
  onSubmit,
  isLoading,
  error
}) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [formError, setFormError] = useState('');
  const [activeField, setActiveField] = useState<string | null>(null);

  const theme = useTheme();
  const { translate } = useLanguage();

  // Update form error when parent error changes
  useEffect(() => {
    // Filter out the "Failed to load health records" error
    if (error && !error.includes("health records")) {
      setFormError(error);
    }
  }, [error]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username.trim() || !password.trim()) {
      setFormError(translate('login.error.empty_fields'));
      return;
    }

    try {
      console.log('Attempting to login with:', username);
      await onSubmit(username, password);
      console.log('Login submission completed');
    } catch (err) {
      // Error is handled by parent component
      console.error('Login failed in form component:', err);
      // Make sure we show any error that might not be caught by the parent
      if (!formError) {
        setFormError(err instanceof Error ? err.message : 'Login failed. Please try again.');
      }
    }
  };

  // Toggle password visibility
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  // Handle forgot password
  const handleForgotPassword = () => {
    // Show a message about contacting the administrator
    alert(translate('login.contact_admin_for_password_reset'));
  };

  return (
    <Paper
      elevation={12}
      sx={{
        width: '100%',
        maxWidth: 450,
        borderRadius: 2,
        overflow: 'hidden',
        background: `linear-gradient(135deg,
          ${alpha(theme.palette.primary.dark, 0.95)},
          ${alpha(theme.palette.primary.main, 0.85)})`,
        backdropFilter: 'blur(10px)',
        boxShadow: `0 20px 50px ${alpha(theme.palette.common.black, 0.5)}`,
        border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
        p: 0,
        position: 'relative',
        zIndex: 100
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 3,
          background: 'rgba(0,0,0,0.4)',
          color: 'white',
          textAlign: 'center',
          position: 'relative',
          overflow: 'hidden',
          borderBottom: 'none',
          borderTopLeftRadius: 2,
          borderTopRightRadius: 2
        }}
      >
        {/* Logo */}
        <Box
          sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            mb: 2
          }}
        >
          <Box
            component="img"
            src="/logo.svg"
            alt="MayCaiphus Logo"
            sx={{
              height: '60px',
              filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'
            }}
          />
        </Box>

        <Typography variant="h5" component="h2" sx={{
          fontWeight: 600,
          mb: 1,
          textAlign: 'center',
          letterSpacing: '-0.5px',
          textShadow: '0 2px 4px rgba(0,0,0,0.2)',
          fontSize: '1.5rem'
        }}>
          {translate('login.signin_account')}
        </Typography>
      </Box>

      {/* Form Content */}
      <Box sx={{ p: 4 }}>
        {/* Language Selector */}
        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
          <LanguageSelector />
        </Box>

        {formError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {formError}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          {/* Username Field */}
          <TextField
            fullWidth
            label={translate('login.username')}
            variant="outlined"
            margin="normal"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            onFocus={() => setActiveField('username')}
            onBlur={() => setActiveField(null)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Person sx={{ color: 'rgba(255, 255, 255, 0.8)' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              mb: 2,
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                transition: 'all 0.3s ease',
                '& fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'white',
                },
                '&.Mui-focused': {
                  boxShadow: `0 0 0 2px rgba(255, 255, 255, 0.2)`
                }
              },
              '& .MuiInputLabel-root': {
                color: 'rgba(255, 255, 255, 0.7)',
              },
              '& .MuiInputBase-input': {
                color: 'white',
              }
            }}
          />

          {/* Password Field */}
          <TextField
            fullWidth
            label={translate('login.password')}
            variant="outlined"
            margin="normal"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onFocus={() => setActiveField('password')}
            onBlur={() => setActiveField(null)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Lock sx={{ color: 'rgba(255, 255, 255, 0.8)' }} />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={toggleShowPassword}
                    edge="end"
                    sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{
              mb: 1,
              '& .MuiOutlinedInput-root': {
                borderRadius: 1,
                transition: 'all 0.3s ease',
                '& fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                },
                '&:hover fieldset': {
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: 'white',
                },
                '&.Mui-focused': {
                  boxShadow: `0 0 0 2px rgba(255, 255, 255, 0.2)`
                }
              },
              '& .MuiInputLabel-root': {
                color: 'rgba(255, 255, 255, 0.7)',
              },
              '& .MuiInputBase-input': {
                color: 'white',
              }
            }}
          />

          {/* Forgot Password Link */}
          <Box sx={{ textAlign: 'right', mb: 3 }}>
            <Link
              component="button"
              type="button"
              variant="body2"
              onClick={handleForgotPassword}
              sx={{
                color: 'rgba(255, 255, 255, 0.9)',
                textDecoration: 'none',
                '&:hover': {
                  color: 'white',
                  textDecoration: 'underline'
                }
              }}
            >
              {translate('login.forgot_password')}
            </Link>
          </Box>

          {/* Submit Button */}
          <CustomButton
            type="submit"
            fullWidth
            variant="contained"
            disabled={isLoading}
            sx={{
              py: 1.5,
              textTransform: 'none',
              fontWeight: 'bold',
              fontSize: '1rem',
              borderRadius: 1,
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: theme.palette.primary.dark,
              position: 'relative',
              overflow: 'hidden',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: '-100%',
                width: '100%',
                height: '100%',
                background: 'linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent)',
                transition: 'all 0.5s ease',
              },
              '&:hover': {
                backgroundColor: 'white',
                boxShadow: '0 6px 15px rgba(0,0,0,0.2)',
                '&::before': {
                  left: '100%',
                }
              }
            }}
          >
            {isLoading ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                {translate('login.signin')}
                <ArrowForward sx={{ ml: 1, fontSize: 18 }} />
              </Box>
            )}
          </CustomButton>

          {/* Footer */}
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              {translate('login.footer_text')}
            </Typography>
          </Box>
        </form>
      </Box>
    </Paper>
  );
};

export default EnhancedLoginForm;
