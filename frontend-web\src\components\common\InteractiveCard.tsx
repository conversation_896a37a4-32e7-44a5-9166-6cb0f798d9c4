import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardActions,
  Typography,
  Box,
  IconButton,
  Button,
  useTheme,
  alpha,
  Chip,
  Avatar,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  MoreVert,
  ArrowForward,
  TrendingUp,
  TrendingDown,
  Star,
  StarBorder,
  Share,
  Bookmark,
  BookmarkBorder,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface InteractiveCardProps {
  title: string;
  subtitle?: string;
  description?: string;
  image?: string;
  icon?: React.ElementType;
  value?: string | number;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  progress?: {
    value: number;
    max: number;
    label?: string;
    color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  };
  tags?: string[];
  actions?: {
    primary?: {
      label: string;
      onClick: () => void;
      icon?: React.ElementType;
    };
    secondary?: {
      label: string;
      onClick: () => void;
      icon?: React.ElementType;
    };
  };
  gradient?: string;
  variant?: 'default' | 'livestock' | 'farm' | 'analytics' | 'minimal';
  size?: 'small' | 'medium' | 'large';
  interactive?: boolean;
  onClick?: () => void;
  onFavorite?: () => void;
  onBookmark?: () => void;
  onShare?: () => void;
  isFavorited?: boolean;
  isBookmarked?: boolean;
  delay?: number;
  className?: string;
}

const InteractiveCard: React.FC<InteractiveCardProps> = ({
  title,
  subtitle,
  description,
  image,
  icon: Icon,
  value,
  trend,
  progress,
  tags = [],
  actions,
  gradient,
  variant = 'default',
  size = 'medium',
  interactive = true,
  onClick,
  onFavorite,
  onBookmark,
  onShare,
  isFavorited = false,
  isBookmarked = false,
  delay = 0,
  className,
}) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const cardHeight = {
    small: 200,
    medium: 280,
    large: 360,
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'livestock':
        return {
          background: `
            linear-gradient(135deg, 
              ${alpha('#8D6E63', 0.1)} 0%, 
              ${alpha('#A1887F', 0.05)} 100%
            )
          `,
          borderColor: alpha('#8D6E63', 0.2),
        };
      case 'farm':
        return {
          background: `
            linear-gradient(135deg, 
              ${alpha('#4CAF50', 0.1)} 0%, 
              ${alpha('#66BB6A', 0.05)} 100%
            )
          `,
          borderColor: alpha('#4CAF50', 0.2),
        };
      case 'analytics':
        return {
          background: `
            linear-gradient(135deg, 
              ${alpha('#2196F3', 0.1)} 0%, 
              ${alpha('#64B5F6', 0.05)} 100%
            )
          `,
          borderColor: alpha('#2196F3', 0.2),
        };
      case 'minimal':
        return {
          background: theme.palette.background.paper,
          borderColor: alpha(theme.palette.divider, 0.1),
        };
      default:
        return {
          background: `
            linear-gradient(135deg, 
              ${alpha(theme.palette.primary.main, 0.05)} 0%, 
              ${alpha(theme.palette.background.paper, 0.95)} 100%
            )
          `,
          borderColor: alpha(theme.palette.primary.main, 0.1),
        };
    }
  };

  const variantStyles = getVariantStyles();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay,
        ease: [0.4, 0, 0.2, 1] 
      }}
      whileHover={interactive ? { 
        y: -8,
        transition: { duration: 0.3, ease: "easeOut" }
      } : {}}
      whileTap={interactive ? { scale: 0.98 } : {}}
      className={className}
    >
      <Card
        onClick={onClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        sx={{
          height: cardHeight[size],
          cursor: onClick ? 'pointer' : 'default',
          borderRadius: 3,
          border: `1px solid ${variantStyles.borderColor}`,
          background: variantStyles.background,
          backdropFilter: 'blur(20px)',
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          transform: isPressed ? 'scale(0.98)' : 'scale(1)',
          '&:hover': {
            boxShadow: `0 20px 60px ${alpha(theme.palette.primary.main, 0.15)}`,
            '& .card-image': {
              transform: 'scale(1.05)',
            },
            '& .card-icon': {
              transform: 'scale(1.1) rotate(5deg)',
            },
            '& .card-actions': {
              opacity: 1,
              transform: 'translateY(0)',
            },
          },
          ...(gradient && {
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '4px',
              background: gradient,
              borderRadius: '12px 12px 0 0',
            },
          }),
        }}
      >
        {/* Background Pattern */}
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 70%)`,
            opacity: isHovered ? 0.8 : 0.5,
            transition: 'opacity 0.3s ease',
          }}
        />

        {/* Header */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 2,
            p: 2,
            pb: image ? 0 : 2,
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
            <Box display="flex" alignItems="center" gap={1.5} flex={1}>
              {Icon && (
                <Box
                  className="card-icon"
                  sx={{
                    width: 48,
                    height: 48,
                    borderRadius: 2,
                    background: gradient || `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.2)}`,
                  }}
                >
                  <Icon sx={{ fontSize: 24, color: 'white' }} />
                </Box>
              )}
              
              <Box flex={1}>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: 600,
                    color: theme.palette.text.primary,
                    lineHeight: 1.3,
                    mb: 0.5,
                  }}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: theme.palette.text.secondary,
                      opacity: 0.8,
                    }}
                  >
                    {subtitle}
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box display="flex" gap={0.5}>
              {onFavorite && (
                <Tooltip title={isFavorited ? "Remove from favorites" : "Add to favorites"}>
                  <IconButton 
                    size="small" 
                    onClick={(e) => {
                      e.stopPropagation();
                      onFavorite();
                    }}
                    sx={{ 
                      color: isFavorited ? theme.palette.warning.main : theme.palette.text.secondary,
                      '&:hover': { 
                        backgroundColor: alpha(theme.palette.warning.main, 0.1),
                      }
                    }}
                  >
                    {isFavorited ? <Star fontSize="small" /> : <StarBorder fontSize="small" />}
                  </IconButton>
                </Tooltip>
              )}

              {onBookmark && (
                <Tooltip title={isBookmarked ? "Remove bookmark" : "Bookmark"}>
                  <IconButton 
                    size="small" 
                    onClick={(e) => {
                      e.stopPropagation();
                      onBookmark();
                    }}
                    sx={{ 
                      color: isBookmarked ? theme.palette.primary.main : theme.palette.text.secondary,
                      '&:hover': { 
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      }
                    }}
                  >
                    {isBookmarked ? <Bookmark fontSize="small" /> : <BookmarkBorder fontSize="small" />}
                  </IconButton>
                </Tooltip>
              )}

              {onShare && (
                <Tooltip title="Share">
                  <IconButton 
                    size="small" 
                    onClick={(e) => {
                      e.stopPropagation();
                      onShare();
                    }}
                    sx={{ 
                      color: theme.palette.text.secondary,
                      '&:hover': { 
                        backgroundColor: alpha(theme.palette.info.main, 0.1),
                      }
                    }}
                  >
                    <Share fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}

              <IconButton 
                size="small" 
                sx={{ 
                  color: theme.palette.text.secondary,
                  opacity: 0.7,
                  '&:hover': { opacity: 1 }
                }}
              >
                <MoreVert fontSize="small" />
              </IconButton>
            </Box>
          </Box>

          {/* Tags */}
          {tags.length > 0 && (
            <Box display="flex" gap={0.5} flexWrap="wrap" mb={1}>
              {tags.slice(0, 3).map((tag, index) => (
                <Chip
                  key={index}
                  label={tag}
                  size="small"
                  sx={{
                    height: 24,
                    fontSize: '0.7rem',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    color: theme.palette.primary.main,
                    border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                  }}
                />
              ))}
              {tags.length > 3 && (
                <Chip
                  label={`+${tags.length - 3}`}
                  size="small"
                  sx={{
                    height: 24,
                    fontSize: '0.7rem',
                    backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                    color: theme.palette.text.secondary,
                  }}
                />
              )}
            </Box>
          )}
        </Box>

        {/* Image */}
        {image && (
          <Box
            sx={{
              position: 'relative',
              height: 120,
              overflow: 'hidden',
              mx: 2,
              borderRadius: 2,
              mb: 2,
            }}
          >
            <Box
              className="card-image"
              sx={{
                width: '100%',
                height: '100%',
                backgroundImage: `url(${image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                transition: 'transform 0.3s ease',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(180deg, transparent 0%, ${alpha('#000', 0.3)} 100%)`,
              }}
            />
          </Box>
        )}

        {/* Content */}
        <CardContent sx={{ pt: image ? 0 : 1, pb: 1 }}>
          {/* Value and Trend */}
          {value && (
            <Box mb={2}>
              <Typography 
                variant="h4" 
                sx={{ 
                  fontWeight: 800,
                  color: theme.palette.text.primary,
                  lineHeight: 1.2,
                  mb: 0.5,
                }}
              >
                {value}
              </Typography>

              {trend && (
                <Box display="flex" alignItems="center" gap={0.5}>
                  <Chip
                    icon={trend.isPositive ? <TrendingUp /> : <TrendingDown />}
                    label={`${trend.isPositive ? '+' : ''}${trend.value}%`}
                    size="small"
                    sx={{
                      height: 24,
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      backgroundColor: trend.isPositive 
                        ? alpha(theme.palette.success.main, 0.1)
                        : alpha(theme.palette.error.main, 0.1),
                      color: trend.isPositive 
                        ? theme.palette.success.main
                        : theme.palette.error.main,
                      border: `1px solid ${trend.isPositive 
                        ? alpha(theme.palette.success.main, 0.2)
                        : alpha(theme.palette.error.main, 0.2)
                      }`,
                      '& .MuiChip-icon': {
                        fontSize: 16,
                      },
                    }}
                  />
                  {trend.label && (
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        color: theme.palette.text.secondary,
                        ml: 0.5,
                      }}
                    >
                      {trend.label}
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          )}

          {/* Description */}
          {description && (
            <Typography 
              variant="body2" 
              sx={{ 
                color: theme.palette.text.secondary,
                lineHeight: 1.5,
                mb: 2,
                display: '-webkit-box',
                WebkitLineClamp: 3,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
              }}
            >
              {description}
            </Typography>
          )}

          {/* Progress */}
          {progress && (
            <Box mb={2}>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={0.5}>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: theme.palette.text.secondary,
                    fontWeight: 500,
                  }}
                >
                  {progress.label || 'Progress'}
                </Typography>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: theme.palette.text.primary,
                    fontWeight: 600,
                  }}
                >
                  {progress.value}/{progress.max}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={(progress.value / progress.max) * 100}
                color={progress.color || 'primary'}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 3,
                  },
                }}
              />
            </Box>
          )}
        </CardContent>

        {/* Actions */}
        {actions && (
          <CardActions 
            className="card-actions"
            sx={{ 
              px: 2, 
              pb: 2,
              opacity: 0.8,
              transform: 'translateY(4px)',
              transition: 'all 0.3s ease',
            }}
          >
            <Box display="flex" gap={1} width="100%">
              {actions.primary && (
                <Button
                  variant="contained"
                  size="small"
                  startIcon={actions.primary.icon && <actions.primary.icon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    actions.primary!.onClick();
                  }}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 600,
                    background: gradient || `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                    '&:hover': {
                      background: gradient || `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                    },
                  }}
                >
                  {actions.primary.label}
                </Button>
              )}

              {actions.secondary && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={actions.secondary.icon && <actions.secondary.icon />}
                  onClick={(e) => {
                    e.stopPropagation();
                    actions.secondary!.onClick();
                  }}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 600,
                    borderWidth: 2,
                    '&:hover': {
                      borderWidth: 2,
                      backgroundColor: alpha(theme.palette.primary.main, 0.08),
                    },
                  }}
                >
                  {actions.secondary.label}
                </Button>
              )}

              <Box flex={1} />

              {onClick && (
                <IconButton
                  size="small"
                  sx={{
                    color: theme.palette.primary.main,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    },
                  }}
                >
                  <ArrowForward fontSize="small" />
                </IconButton>
              )}
            </Box>
          </CardActions>
        )}
      </Card>
    </motion.div>
  );
};

export default InteractiveCard;
