import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, Grid, Paper, TextField, InputAdornment, CircularProgress, Alert, Chip, List, ListItem, ListItemText, ListItemAvatar, Avatar, IconButton, Tooltip, Divider, useTheme, alpha, Snackbar, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent,  } from '@mui/material';
import {
  LocationOn,
  MyLocation,
  Search,
  Pets,
  History,
  Refresh,
  FilterList,
  Add,
  Timeline,
  Map as MapIcon,
  Visibility,
  Edit,
  Delete,
  GetApp,
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard, ModuleContainer , CustomButton } from '../../components/common';
import { useAnimalData } from '../../hooks/useAnimalData';
import { mockAnimals } from '../../mocks';
import { useMockData } from '../../utils/config';

// Google Maps component
const GoogleMap: React.FC<{
  apiKey: string;
  center: { lat: number; lng: number };
  zoom: number;
  markers: Array<{
    id: string;
    position: { lat: number; lng: number };
    title: string;
    icon?: string;
    info?: string;
  }>;
  onMarkerClick?: (markerId: string) => void;
}> = ({ apiKey, center, zoom, markers, onMarkerClick }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [googleMarkers, setGoogleMarkers] = useState<google.maps.Marker[]>([]);

  // Load Google Maps API
  useEffect(() => {
    if (!window.google && !document.getElementById('google-maps-script')) {
      const script = document.createElement('script');
      script.id = 'google-maps-script';
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
      script.async = true;
      script.defer = true;
      script.onload = () => setMapLoaded(true);
      document.head.appendChild(script);
    } else if (window.google) {
      setMapLoaded(true);
    }
  }, [apiKey]);

  // Initialize map
  useEffect(() => {
    if (mapLoaded && mapRef.current && !map) {
      const newMap = new window.google.maps.Map(mapRef.current, {
        center,
        zoom,
        mapTypeId: 'hybrid',
        mapTypeControl: true,
        streetViewControl: true,
        fullscreenControl: true,
        zoomControl: true,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }],
          },
        ],
      });
      setMap(newMap);
    }
  }, [mapLoaded, center, zoom, map]);

  // Update markers when they change
  useEffect(() => {
    if (map && markers.length > 0) {
      // Clear existing markers
      googleMarkers.forEach(marker => marker.setMap(null));

      // Create new markers
      const newMarkers = markers.map(marker => {
        const newMarker = new window.google.maps.Marker({
          position: marker.position,
          map,
          title: marker.title,
          icon: marker.icon,
          animation: window.google.maps.Animation.DROP,
        });

        // Add info window if info is provided
        if (marker.info) {
          const infoWindow = new window.google.maps.InfoWindow({
            content: `
              <div style="padding: 10px; max-width: 200px;">
                <h3 style="margin: 0 0 5px 0; font-size: 16px;">${marker.title}</h3>
                <p style="margin: 0; font-size: 14px;">${marker.info}</p>
              </div>
            `,
          });

          newMarker.addListener('click', () => {
            infoWindow.open(map, newMarker);
            if (onMarkerClick) {
              onMarkerClick(marker.id);
            }
          });
        }

        return newMarker;
      });

      setGoogleMarkers(newMarkers);

      // Fit bounds to show all markers
      if (newMarkers.length > 1) {
        const bounds = new window.google.maps.LatLngBounds();
        markers.forEach(marker => {
          bounds.extend(marker.position);
        });
        map.fitBounds(bounds);
      }
    }
  }, [map, markers, onMarkerClick]);

  return (
    <Box
      ref={mapRef}
      sx={{
        width: '100%',
        height: '100%',
        minHeight: '500px',
        borderRadius: '12px',
        overflow: 'hidden',
      }}
    />
  );
};

// Mock location data for all 10 animals
const mockLocations = [
  { id: '1', animalId: '1', lat: -25.7461, lng: 28.1881, timestamp: new Date().toISOString(), status: 'active' },
  { id: '2', animalId: '2', lat: -25.7491, lng: 28.1921, timestamp: new Date().toISOString(), status: 'active' },
  { id: '3', animalId: '3', lat: -25.7521, lng: 28.1851, timestamp: new Date().toISOString(), status: 'active' },
  { id: '4', animalId: '4', lat: -25.7551, lng: 28.1891, timestamp: new Date().toISOString(), status: 'active' },
  { id: '5', animalId: '5', lat: -25.7581, lng: 28.1861, timestamp: new Date().toISOString(), status: 'active' },
  { id: '6', animalId: '6', lat: -25.7511, lng: 28.1841, timestamp: new Date().toISOString(), status: 'active' },
  { id: '7', animalId: '7', lat: -25.7471, lng: 28.1901, timestamp: new Date().toISOString(), status: 'active' },
  { id: '8', animalId: '8', lat: -25.7531, lng: 28.1871, timestamp: new Date().toISOString(), status: 'inactive' },
  { id: '9', animalId: '9', lat: -25.7501, lng: 28.1911, timestamp: new Date().toISOString(), status: 'active' },
  { id: '10', animalId: '10', lat: -25.7541, lng: 28.1831, timestamp: new Date().toISOString(), status: 'active' },
];

// Mock RFID data for all 10 animals
const mockRfidData = [
  { id: '1', animalId: '1', tagId: 'RFID00001', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 85 },
  { id: '2', animalId: '2', tagId: 'RFID00002', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 92 },
  { id: '3', animalId: '3', tagId: 'RFID00003', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 78 },
  { id: '4', animalId: '4', tagId: 'RFID00004', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 65 },
  { id: '5', animalId: '5', tagId: 'RFID00005', lastScan: new Date().toISOString(), status: 'low-battery', batteryLevel: 12 },
  { id: '6', animalId: '6', tagId: 'RFID00006', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 88 },
  { id: '7', animalId: '7', tagId: 'RFID00007', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 75 },
  { id: '8', animalId: '8', tagId: 'RFID00008', lastScan: new Date().toISOString(), status: 'inactive', batteryLevel: 0 },
  { id: '9', animalId: '9', tagId: 'RFID00009', lastScan: new Date().toISOString(), status: 'active', batteryLevel: 95 },
  { id: '10', animalId: '10', tagId: 'RFID00010', lastScan: new Date().toISOString(), status: 'low-battery', batteryLevel: 18 },
];

interface AnimalLocation {
  id: string;
  animalId: string;
  lat: number;
  lng: number;
  timestamp: string;
  status: string;
}

interface RfidTag {
  id: string;
  animalId: string;
  tagId: string;
  lastScan: string;
  status: string;
  batteryLevel: number;
}

interface Animal {
  id: string;
  tagNumber: string;
  name: string;
  species: string;
  breed: string;
  status: string;
  location?: string;
}

const AnimalTracking: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { animals, loading, error } = useAnimalData();

  // State for tracking data
  const [locations, setLocations] = useState<AnimalLocation[]>([]);
  const [rfidTags, setRfidTags] = useState<RfidTag[]>([]);
  const [selectedAnimal, setSelectedAnimal] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [mapCenter, setMapCenter] = useState({ lat: -25.7461, lng: 28.1881 });
  const [mapZoom, setMapZoom] = useState(14);
  const [dataLoading, setDataLoading] = useState(false);
  const [alert, setAlert] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Google Maps API Key
  const googleMapsApiKey = 'AIzaSyA9JZ_PcJQ4H7mmXWnUXVsCbDEEBgGEPWs';

  // Load location and RFID data
  useEffect(() => {
    const fetchData = async () => {
      setDataLoading(true);
      try {
        // In a real app, this would be an API call
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
        setLocations(mockLocations);
        setRfidTags(mockRfidData);
      } catch (err) {
        console.error('Error fetching tracking data:', err);
        setAlert({
          open: true,
          message: 'Failed to load tracking data',
          severity: 'error',
        });
      } finally {
        setDataLoading(false);
      }
    };

    fetchData();
  }, []);

  // Handle filter change
  const handleFilterChange = (event: SelectChangeEvent) => {
    setFilterStatus(event.target.value);
  };

  // Handle marker click
  const handleMarkerClick = (markerId: string) => {
    const location = locations.find(loc => loc.id === markerId);
    if (location) {
      setSelectedAnimal(location.animalId);
    }
  };

  // Filter animals based on search and status
  const filteredLocations = locations.filter(location => {
    const animal = animals.find(a => a.id === location.animalId);
    if (!animal) return false;

    const matchesSearch = searchTerm === '' ||
      animal.tagNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      animal.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (animal.species ? animal.species.toLowerCase().includes(searchTerm.toLowerCase()) : false);

    const matchesStatus = filterStatus === 'all' || location.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Prepare markers for Google Maps
  const mapMarkers = filteredLocations.map(location => {
    const animal = animals.find(a => a.id === location.animalId);
    return {
      id: location.id,
      position: { lat: location.lat, lng: location.lng },
      title: animal ? `${animal.name} (${animal.tagNumber})` : `Animal #${location.animalId}`,
      info: animal ? `Species: ${animal.species}<br>Breed: ${animal.breed}<br>Status: ${animal.status}` : '',
      icon: selectedAnimal === location.animalId
        ? 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
        : 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
    };
  });

  // Export location data to CSV
  const exportToCSV = () => {
    const headers = ['Animal ID', 'Tag Number', 'Name', 'Species', 'Latitude', 'Longitude', 'Timestamp', 'Status'];
    const csvData = filteredLocations.map(location => {
      const animal = animals.find(a => a.id === location.animalId);
      return [
        location.animalId,
        animal?.tagNumber || '',
        animal?.name || '',
        animal?.species || '',
        location.lat,
        location.lng,
        new Date(location.timestamp).toLocaleString(),
        location.status
      ];
    });

    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.join(','))
    ].join('\\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'animal_locations.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Refresh tracking data
  const refreshData = async () => {
    setDataLoading(true);
    try {
      // In a real app, this would be an API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

      // Update with new random positions
      const updatedLocations = mockLocations.map(location => ({
        ...location,
        lat: location.lat + (Math.random() - 0.5) * 0.01,
        lng: location.lng + (Math.random() - 0.5) * 0.01,
        timestamp: new Date().toISOString(),
      }));

      setLocations(updatedLocations);
      setAlert({
        open: true,
        message: 'Tracking data updated successfully',
        severity: 'success',
      });
    } catch (err) {
      console.error('Error refreshing tracking data:', err);
      setAlert({
        open: true,
        message: 'Failed to refresh tracking data',
        severity: 'error',
      });
    } finally {
      setDataLoading(false);
    }
  };

  if (loading || dataLoading) {
    return (
      <ModuleContainer module="animals">
        <Box display="flex" justifyContent="center" alignItems="center" height="50vh">
          <CircularProgress size={60} thickness={4} />
        </Box>
      </ModuleContainer>
    );
  }

  if (error) {
    return (
      <ModuleContainer module="animals">
        <Box p={3}>
          <Alert severity="error" sx={{ fontSize: '1.1rem' }}>
            {error instanceof Error ? error.message : String(error)}
          </Alert>
        </Box>
      </ModuleContainer>
    );
  }

  return (
    <ModuleContainer module="animals">
      <ModuleHeader
        title="Animal Location Tracking"
        subtitle="Track and monitor your livestock with GPS and RFID technology"
        module="animals"
        actionLabel="Add Tracking Device"
        actionIcon={<Add />}
        onAction={() => {
          setAlert({
            open: true,
            message: 'Add tracking device functionality coming soon',
            severity: 'info',
          });
        }}
        icon={<LocationOn />}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Controls */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={8}>
            <Box display="flex" alignItems="center" gap={2}>
              <TextField
                placeholder="Search animals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                variant="outlined"
                size="small"
                fullWidth
                sx={{
                  maxWidth: 300,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '8px',
                    fontSize: '1.1rem',
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search color="action" />
                    </InputAdornment>
                  ),
                }}
              />

              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel id="status-filter-label">Status</InputLabel>
                <Select
                  labelId="status-filter-label"
                  value={filterStatus}
                  label="Status"
                  onChange={handleFilterChange}
                  sx={{ borderRadius: '8px', fontSize: '1.1rem' }}
                >
                  <MenuItem value="all">All Statuses</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="low-battery">Low Battery</MenuItem>
                </Select>
              </FormControl>

              <Tooltip title="Refresh Tracking Data">
                <IconButton
                  color="primary"
                  onClick={refreshData}
                  disabled={dataLoading}
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.2),
                    }
                  }}
                >
                  <Refresh />
                </IconButton>
              </Tooltip>

              <Tooltip title="Export to CSV">
                <IconButton
                  color="primary"
                  onClick={exportToCSV}
                  sx={{
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.2),
                    }
                  }}
                >
                  <GetApp />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>

          <Grid item xs={12} md={4}>
            <Box display="flex" justifyContent="flex-end" gap={2}>
              <CustomButton
                variant="outlined"
                color="primary"
                startIcon={<Timeline />}
                onClick={() => navigate('/animals/tracking/history')}
                sx={{ borderRadius: '8px', fontSize: '1.1rem' }}
              >
                Movement History
              </CustomButton>

              <CustomButton
                variant="contained"
                color="primary"
                startIcon={<MapIcon />}
                onClick={() => {
                  setMapZoom(14);
                  setMapCenter({ lat: -25.7461, lng: 28.1881 });
                }}
                sx={{ borderRadius: '8px', fontSize: '1.1rem' }}
              >
                Reset Map
              </CustomButton>
            </Box>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid container spacing={3}>
          {/* Map */}
          <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Live Location Tracking"
              module="animals"
              icon={<MyLocation />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
            >
              <Box sx={{ height: 600, p: 0 }}>
                <GoogleMap
                  apiKey={googleMapsApiKey}
                  center={mapCenter}
                  zoom={mapZoom}
                  markers={mapMarkers}
                  onMarkerClick={handleMarkerClick}
                />
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          {/* Animal List and RFID Status */}
          <Grid item xs={12} md={4}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <AnimatedBackgroundCard
                  title="Tracked Animals"
                  module="animals"
                  icon={<Pets />}
                  accentColor={theme.palette.primary.main}
                  secondaryColor={theme.palette.primary.dark}
                >
                  <List sx={{ p: 0 }}>
                    {filteredLocations.length > 0 ? (
                      filteredLocations.map((location) => {
                        const animal = animals.find(a => a.id === location.animalId);
                        const rfidTag = rfidTags.find(tag => tag.animalId === location.animalId);

                        if (!animal) return null;

                        return (
                          <React.Fragment key={location.id}>
                            <ListItem
                              button
                              selected={selectedAnimal === location.animalId}
                              onClick={() => {
                                setSelectedAnimal(location.animalId);
                                setMapCenter({ lat: location.lat, lng: location.lng });
                                setMapZoom(16);
                              }}
                              sx={{
                                borderRadius: '8px',
                                mb: 1,
                                '&.Mui-selected': {
                                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, 0.15),
                                  }
                                }
                              }}
                            >
                              <ListItemAvatar>
                                <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                                  <Pets />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={
                                  <Typography variant="subtitle1" fontWeight={600}>
                                    {animal.name} ({animal.tagNumber})
                                  </Typography>
                                }
                                secondary={
                                  <>
                                    <Typography variant="body2" color="text.secondary">
                                      {animal.species || 'Unknown'} • {animal.breed}
                                    </Typography>
                                    <Box display="flex" alignItems="center" mt={0.5}>
                                      <Chip
                                        label={location.status}
                                        size="small"
                                        color={
                                          location.status === 'active' ? 'success' :
                                          location.status === 'inactive' ? 'error' : 'warning'
                                        }
                                        sx={{ mr: 1, fontSize: '0.9rem' }}
                                      />
                                      {rfidTag && (
                                        <Chip
                                          label={`Battery: ${rfidTag.batteryLevel}%`}
                                          size="small"
                                          color={
                                            rfidTag.batteryLevel > 50 ? 'success' :
                                            rfidTag.batteryLevel > 20 ? 'warning' : 'error'
                                          }
                                          sx={{ fontSize: '0.9rem' }}
                                        />
                                      )}
                                    </Box>
                                  </>
                                }
                              />
                            </ListItem>
                            <Divider variant="inset" component="li" />
                          </React.Fragment>
                        );
                      })
                    ) : (
                      <Box p={3} textAlign="center">
                        <Typography variant="body1" color="text.secondary">
                          No animals match your search criteria
                        </Typography>
                      </Box>
                    )}
                  </List>
                </AnimatedBackgroundCard>
              </Grid>

              <Grid item xs={12}>
                <AnimatedBackgroundCard
                  title="RFID Tag Status"
                  module="animals"
                  icon={<History />}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                >
                  <List sx={{ p: 0 }}>
                    {rfidTags
                      .filter(tag => {
                        if (selectedAnimal) {
                          return tag.animalId === selectedAnimal;
                        }
                        return filteredLocations.some(loc => loc.animalId === tag.animalId);
                      })
                      .map((tag) => {
                        const animal = animals.find(a => a.id === tag.animalId);
                        if (!animal) return null;

                        return (
                          <React.Fragment key={tag.id}>
                            <ListItem
                              sx={{
                                borderRadius: '8px',
                                mb: 1,
                              }}
                            >
                              <ListItemAvatar>
                                <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                                  <MyLocation />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={
                                  <Typography variant="subtitle1" fontWeight={600}>
                                    {tag.tagId}
                                  </Typography>
                                }
                                secondary={
                                  <>
                                    <Typography variant="body2" color="text.secondary">
                                      {animal.name} • Last scan: {new Date(tag.lastScan).toLocaleTimeString()}
                                    </Typography>
                                    <Box display="flex" alignItems="center" mt={0.5}>
                                      <Chip
                                        label={`Battery: ${tag.batteryLevel}%`}
                                        size="small"
                                        color={
                                          tag.batteryLevel > 50 ? 'success' :
                                          tag.batteryLevel > 20 ? 'warning' : 'error'
                                        }
                                        sx={{ fontSize: '0.9rem' }}
                                      />
                                    </Box>
                                  </>
                                }
                              />
                              <Box>
                                <Tooltip title="View Details">
                                  <IconButton
                                    size="small"
                                    onClick={() => {
                                      setAlert({
                                        open: true,
                                        message: 'RFID details functionality coming soon',
                                        severity: 'info',
                                      });
                                    }}
                                  >
                                    <Visibility fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </ListItem>
                            <Divider variant="inset" component="li" />
                          </React.Fragment>
                        );
                      })}
                  </List>
                </AnimatedBackgroundCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Box>

      {/* Alert Snackbar */}
      <Snackbar
        open={alert.open}
        autoHideDuration={6000}
        onClose={() => setAlert({ ...alert, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={() => setAlert({ ...alert, open: false })} severity={alert.severity} sx={{ width: '100%' }}>
          {alert.message}
        </Alert>
      </Snackbar>
    </ModuleContainer>
  );
};

export default AnimalTracking;
