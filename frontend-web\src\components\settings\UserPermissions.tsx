import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Card, CardContent, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Checkbox, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, Chip, Tooltip, Alert, Paper, Divider, useTheme, alpha } from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Person,
  Security,
  Check,
  Close,
  Refresh,
  Search
} from '@mui/icons-material';
import { Role, Permission } from '../../hooks/usePermissions';
import { motion } from 'framer-motion';

// Mock user data
interface User {
  id: string;
  name: string;
  email: string;
  role: Role;
  lastLogin: string;
  status: 'active' | 'inactive';
}

const mockUsers: User[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    lastLogin: '2023-06-15T10:30:00',
    status: 'active'
  },
  {
    id: '2',
    name: 'Farm Manager',
    email: '<EMAIL>',
    role: 'manager',
    lastLogin: '2023-06-14T16:45:00',
    status: 'active'
  },
  {
    id: '3',
    name: 'Staff Member',
    email: '<EMAIL>',
    role: 'staff',
    lastLogin: '2023-06-13T09:15:00',
    status: 'active'
  },
  {
    id: '4',
    name: 'Veterinarian',
    email: '<EMAIL>',
    role: 'veterinarian',
    lastLogin: '2023-06-10T14:20:00',
    status: 'active'
  },
  {
    id: '5',
    name: 'Viewer Account',
    email: '<EMAIL>',
    role: 'viewer',
    lastLogin: '2023-06-08T11:10:00',
    status: 'inactive'
  }
];

// Role descriptions
const roleDescriptions: Record<Role, string> = {
  admin: 'Full access to all system features and settings',
  manager: 'Manage farm operations with limited administrative access',
  staff: 'Regular staff with access to daily operations',
  veterinarian: 'Specialized access to animal health records',
  viewer: 'Read-only access to view data without making changes'
};

const UserPermissions: React.FC = () => {
  const theme = useTheme();
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [filteredUsers, setFilteredUsers] = useState<User[]>(mockUsers);
  const [searchTerm, setSearchTerm] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<Partial<User>>({
    name: '',
    email: '',
    role: 'staff',
    status: 'active'
  });
  const [alert, setAlert] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Filter users based on search term
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(
        user =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.role.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    }
  }, [searchTerm, users]);

  const handleOpenDialog = (user: User | null = null) => {
    if (user) {
      setSelectedUser(user);
      setNewUser({
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status
      });
    } else {
      setSelectedUser(null);
      setNewUser({
        name: '',
        email: '',
        role: 'staff',
        status: 'active'
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedUser(null);
  };

  const handleSaveUser = () => {
    if (!newUser.name || !newUser.email || !newUser.role) {
      setAlert({
        open: true,
        message: 'Please fill in all required fields',
        severity: 'error'
      });
      return;
    }

    if (selectedUser) {
      // Update existing user
      const updatedUsers = users.map(user =>
        user.id === selectedUser.id
          ? { ...user, ...newUser }
          : user
      );
      setUsers(updatedUsers);
      setAlert({
        open: true,
        message: 'User updated successfully',
        severity: 'success'
      });
    } else {
      // Add new user
      const newId = (Math.max(...users.map(user => parseInt(user.id))) + 1).toString();
      const userToAdd = {
        id: newId,
        name: newUser.name!,
        email: newUser.email!,
        role: newUser.role as Role,
        lastLogin: '-',
        status: newUser.status as 'active' | 'inactive'
      };
      setUsers([...users, userToAdd]);
      setAlert({
        open: true,
        message: 'User added successfully',
        severity: 'success'
      });
    }
    handleCloseDialog();
  };

  const handleDeleteUser = (userId: string) => {
    const updatedUsers = users.filter(user => user.id !== userId);
    setUsers(updatedUsers);
    setAlert({
      open: true,
      message: 'User deleted successfully',
      severity: 'success'
    });
  };

  const handleAlertClose = () => {
    setAlert({ ...alert, open: false });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <Card sx={{ borderRadius: '12px', overflow: 'hidden', mb: 4 }}>
        <Box sx={{ 
          p: 2, 
          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Typography variant="h6" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center' }}>
            <Security sx={{ mr: 1 }} /> User Permissions
          </Typography>
          <CustomButton
            variant="contained"
            color="secondary"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
            sx={{ bgcolor: 'rgba(255, 255, 255, 0.2)', '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' } }}
          >
            Add User
          </CustomButton>
        </Box>
        
        <CardContent>
          <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', bgcolor: alpha(theme.palette.primary.main, 0.05), borderRadius: 2, px: 2, py: 0.5, width: 300 }}>
              <Search sx={{ color: 'text.secondary', mr: 1 }} />
              <TextField
                placeholder="Search users..."
                variant="standard"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{ disableUnderline: true }}
                fullWidth
              />
            </Box>
            <CustomButton
              startIcon={<Refresh />}
              onClick={() => setUsers(mockUsers)}
              color="primary"
            >
              Reset
            </CustomButton>
          </Box>
          
          <TableContainer component={Paper} sx={{ borderRadius: '12px', boxShadow: 'none', border: `1px solid ${alpha(theme.palette.divider, 0.5)}` }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: 32,
                            height: 32,
                            borderRadius: '50%',
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mr: 1.5
                          }}
                        >
                          <Person fontSize="small" />
                        </Box>
                        {user.name}
                      </Box>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        color={
                          user.role === 'admin'
                            ? 'primary'
                            : user.role === 'manager'
                            ? 'secondary'
                            : user.role === 'veterinarian'
                            ? 'info'
                            : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {user.lastLogin === '-'
                        ? '-'
                        : new Date(user.lastLogin).toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={user.status === 'active' ? 'Active' : 'Inactive'}
                        color={user.status === 'active' ? 'success' : 'error'}
                        size="small"
                        icon={user.status === 'active' ? <Check fontSize="small" /> : <Close fontSize="small" />}
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Edit User">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleOpenDialog(user)}
                        >
                          <Edit fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete User">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteUser(user.id)}
                          disabled={user.role === 'admin' && users.filter(u => u.role === 'admin').length === 1}
                        >
                          <Delete fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          {filteredUsers.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography color="text.secondary">No users found</Typography>
            </Box>
          )}
          
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom>
              Role Permissions
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <TableContainer component={Paper} sx={{ borderRadius: '12px', boxShadow: 'none', border: `1px solid ${alpha(theme.palette.divider, 0.5)}` }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Role</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Dashboard</TableCell>
                    <TableCell>Animals</TableCell>
                    <TableCell>Health</TableCell>
                    <TableCell>Breeding</TableCell>
                    <TableCell>Feed</TableCell>
                    <TableCell>Commercial</TableCell>
                    <TableCell>Reports</TableCell>
                    <TableCell>Settings</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {Object.entries(roleDescriptions).map(([role, description]) => (
                    <TableRow key={role} hover>
                      <TableCell>
                        <Chip
                          label={role.charAt(0).toUpperCase() + role.slice(1)}
                          color={
                            role === 'admin'
                              ? 'primary'
                              : role === 'manager'
                              ? 'secondary'
                              : role === 'veterinarian'
                              ? 'info'
                              : 'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{description}</TableCell>
                      <TableCell>
                        <Checkbox checked disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={role !== 'viewer'} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={['admin', 'manager', 'veterinarian'].includes(role)} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={['admin', 'manager'].includes(role)} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={['admin', 'manager'].includes(role)} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={role === 'admin'} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={['admin', 'manager', 'veterinarian'].includes(role)} disabled />
                      </TableCell>
                      <TableCell>
                        <Checkbox checked={role === 'admin'} disabled />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </CardContent>
      </Card>
      
      {/* User Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Full Name"
              fullWidth
              value={newUser.name || ''}
              onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
              required
            />
            <TextField
              label="Email"
              type="email"
              fullWidth
              value={newUser.email || ''}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              required
            />
            <FormControl fullWidth>
              <InputLabel>Role</InputLabel>
              <Select
                value={newUser.role || 'staff'}
                label="Role"
                onChange={(e) => setNewUser({ ...newUser, role: e.target.value as Role })}
              >
                <MenuItem value="admin">Administrator</MenuItem>
                <MenuItem value="manager">Manager</MenuItem>
                <MenuItem value="staff">Staff</MenuItem>
                <MenuItem value="veterinarian">Veterinarian</MenuItem>
                <MenuItem value="viewer">Viewer</MenuItem>
              </Select>
            </FormControl>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={newUser.status || 'active'}
                label="Status"
                onChange={(e) => setNewUser({ ...newUser, status: e.target.value as 'active' | 'inactive' })}
              >
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
            
            {newUser.role && (
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>{newUser.role.charAt(0).toUpperCase() + newUser.role.slice(1)}</strong>: {roleDescriptions[newUser.role as Role]}
                </Typography>
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton onClick={handleSaveUser} variant="contained" color="primary">
            {selectedUser ? 'Update' : 'Add'} User
          </CustomButton>
        </DialogActions>
      </Dialog>
      
      {/* Alert */}
      {alert.open && (
        <Alert
          severity={alert.severity}
          onClose={handleAlertClose}
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            zIndex: 9999,
            boxShadow: 3,
            borderRadius: 2
          }}
        >
          {alert.message}
        </Alert>
      )}
    </motion.div>
  );
};

export default UserPermissions;
