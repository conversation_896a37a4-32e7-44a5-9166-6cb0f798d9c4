/**
 * Custom Button Component
 *
 * A replacement for MUI Button component that fixes the 'Cannot read properties of undefined (reading 'dark')' error.
 * This component provides a consistent button styling across the application with animation support.
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import './CustomButton.css';

interface CustomButtonProps {
  variant?: 'contained' | 'outlined' | 'text' | 'gradient';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  children: React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: React.CSSProperties;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  target?: string;
  rel?: string;
  animate?: boolean;
  sx?: any; // Add support for sx prop to match MUI Button API
}

/**
 * A custom button component that doesn't rely on MUI
 * This helps prevent "Cannot read properties of undefined (reading 'dark')" errors
 */
const CustomButton: React.FC<CustomButtonProps> = ({
  variant = 'contained',
  color = 'primary',
  onClick,
  children,
  startIcon,
  endIcon,
  disabled = false,
  loading = false,
  fullWidth = false,
  size = 'medium',
  style,
  className,
  type = 'button',
  href,
  target,
  rel,
  animate = true,
  sx
}) => {
  // Use a default theme mode instead of relying on ThemeContext
  const themeMode = 'light' as const; // Default to light mode with type assertion
  const [isHovered, setIsHovered] = useState(false);

  // Define color mappings with dark mode support
  const colorMap: Record<string, { main: string, dark: string, light: string, contrastText: string }> = {
    primary: {
      main: themeMode === 'dark' ? '#4FBEB4' : '#3AA99F',
      dark: themeMode === 'dark' ? '#3AA99F' : '#2A8A82',
      light: themeMode === 'dark' ? '#60D0C6' : '#4FBEB4',
      contrastText: '#ffffff'
    },
    secondary: {
      main: themeMode === 'dark' ? '#4FD1CB' : '#38B2AC',
      dark: themeMode === 'dark' ? '#38B2AC' : '#2C8A84',
      light: themeMode === 'dark' ? '#60E2DC' : '#4FD1CB',
      contrastText: '#ffffff'
    },
    error: {
      main: themeMode === 'dark' ? '#f87171' : '#ef4444',
      dark: themeMode === 'dark' ? '#ef4444' : '#dc2626',
      light: themeMode === 'dark' ? '#fca5a5' : '#f87171',
      contrastText: '#ffffff'
    },
    warning: {
      main: themeMode === 'dark' ? '#fbbf24' : '#f59e0b',
      dark: themeMode === 'dark' ? '#f59e0b' : '#d97706',
      light: themeMode === 'dark' ? '#fcd34d' : '#fbbf24',
      contrastText: '#ffffff'
    },
    info: {
      main: themeMode === 'dark' ? '#60a5fa' : '#3b82f6',
      dark: themeMode === 'dark' ? '#3b82f6' : '#2563eb',
      light: themeMode === 'dark' ? '#93c5fd' : '#60a5fa',
      contrastText: '#ffffff'
    },
    success: {
      main: themeMode === 'dark' ? '#34d399' : '#10b981',
      dark: themeMode === 'dark' ? '#10b981' : '#059669',
      light: themeMode === 'dark' ? '#6ee7b7' : '#34d399',
      contrastText: '#ffffff'
    },
    default: {
      main: themeMode === 'dark' ? '#6b7280' : '#9ca3af',
      dark: themeMode === 'dark' ? '#4b5563' : '#6b7280',
      light: themeMode === 'dark' ? '#9ca3af' : '#d1d5db',
      contrastText: themeMode === 'dark' ? '#ffffff' : '#1f2937'
    }
  };

  // Get color values
  const colorValues = colorMap[color] || colorMap.primary;

  // Define base styles
  const baseStyles: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '8px',
    cursor: disabled || loading ? 'not-allowed' : 'pointer',
    fontWeight: 600,
    transition: 'all 0.2s ease-in-out',
    opacity: disabled ? 0.5 : 1,
    width: fullWidth ? '100%' : 'auto',
    textTransform: 'none',
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontSize: size === 'small' ? '0.8125rem' : size === 'large' ? '0.9375rem' : '0.875rem',
    padding: size === 'small' ? '4px 10px' : size === 'large' ? '12px 22px' : '8px 16px',
    minHeight: size === 'small' ? '32px' : size === 'large' ? '56px' : '48px',
    minWidth: size === 'small' ? '64px' : size === 'large' ? '112px' : '96px',
    boxSizing: 'border-box',
    border: 'none',
    outline: 'none',
    userSelect: 'none',
    textDecoration: 'none',
    position: 'relative',
    overflow: 'hidden'
  };

  // Apply variant-specific styles
  if (variant === 'contained') {
    baseStyles.backgroundColor = colorValues.main;
    baseStyles.color = colorValues.contrastText;
    baseStyles.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
  } else if (variant === 'outlined') {
    baseStyles.backgroundColor = 'transparent';
    baseStyles.color = colorValues.main;
    baseStyles.border = `1px solid ${colorValues.main}`;
  } else if (variant === 'text') {
    baseStyles.backgroundColor = 'transparent';
    baseStyles.color = colorValues.main;
    baseStyles.boxShadow = 'none';
  } else if (variant === 'gradient') {
    baseStyles.background = `linear-gradient(45deg, ${colorValues.main} 30%, ${colorValues.light} 90%)`;
    baseStyles.color = colorValues.contrastText;
    baseStyles.boxShadow = `0 3px 5px 2px rgba(${colorValues.main.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ') || '0, 0, 0'}, 0.3)`;
  }

  // Combine with custom styles
  const combinedStyles: React.CSSProperties = {
    ...baseStyles,
    ...style,
    ...(sx || {}) // Include sx prop if provided
  };

  // Apply hover styles
  if (isHovered && !disabled && !loading) {
    if (variant === 'contained') {
      combinedStyles.backgroundColor = colorValues.dark;
      combinedStyles.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
      combinedStyles.transform = 'translateY(-2px)';
    } else if (variant === 'outlined') {
      combinedStyles.backgroundColor = `${colorValues.main}20`; // 20% opacity
    } else if (variant === 'text') {
      combinedStyles.backgroundColor = `${colorValues.main}10`; // 10% opacity
    } else if (variant === 'gradient') {
      combinedStyles.boxShadow = `0 5px 8px 2px rgba(${colorValues.main.replace('#', '').match(/.{2}/g)?.map(hex => parseInt(hex, 16)).join(', ') || '0, 0, 0'}, 0.4)`;
      combinedStyles.transform = 'translateY(-2px)';
    }
  }

  // Loading spinner styles
  const spinnerStyles: React.CSSProperties = {
    width: '16px',
    height: '16px',
    borderRadius: '50%',
    border: `2px solid rgba(${colorValues.contrastText === '#ffffff' ? '255, 255, 255' : '0, 0, 0'}, 0.2)`,
    borderTopColor: colorValues.contrastText,
    animation: 'spin 1s linear infinite',
    marginRight: '8px'
  };

  // Animation variants
  const buttonVariants = {
    initial: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 }
  };

  // Render as anchor if href is provided
  if (href) {
    const ButtonComponent = animate ? motion.a : 'a';
    const animationProps = animate ? {
      variants: buttonVariants,
      initial: 'initial',
      whileHover: 'hover',
      whileTap: 'tap'
    } : {};

    return (
      <ButtonComponent
        href={href}
        target={target}
        rel={rel}
        style={combinedStyles}
        className={`custom-button custom-button-${variant} custom-button-${color} custom-button-${size} ${fullWidth ? 'custom-button-fullWidth' : ''} ${className || ''}`}
        onClick={disabled || loading ? undefined : onClick as any}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...animationProps}
      >
        {loading && (
          <div className="custom-button-spinner" />
        )}
        {startIcon && <span className="custom-button-icon custom-button-start-icon">{startIcon}</span>}
        <span className="custom-button-text">{children}</span>
        {endIcon && <span className="custom-button-icon custom-button-end-icon">{endIcon}</span>}
      </ButtonComponent>
    );
  }

  // Render as button
  const ButtonComponent = animate ? motion.button : 'button';
  const animationProps = animate ? {
    variants: buttonVariants,
    initial: 'initial',
    whileHover: 'hover',
    whileTap: 'tap'
  } : {};

  return (
    <ButtonComponent
      style={combinedStyles}
      onClick={disabled || loading ? undefined : onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      disabled={disabled || loading}
      className={`custom-button custom-button-${variant} custom-button-${color} custom-button-${size} ${fullWidth ? 'custom-button-fullWidth' : ''} ${className || ''}`}
      type={type}
      {...animationProps}
    >
      {loading && (
        <div className="custom-button-spinner" />
      )}
      {startIcon && <span className="custom-button-icon custom-button-start-icon">{startIcon}</span>}
      <span className="custom-button-text">{children}</span>
      {endIcon && <span className="custom-button-icon custom-button-end-icon">{endIcon}</span>}
    </ButtonComponent>
  );
};

export default CustomButton;
