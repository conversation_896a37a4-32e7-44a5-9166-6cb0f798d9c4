import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  Avatar,
  Chip,
  Divider,
  IconButton,
  useTheme,
  alpha,
  Collapse,
  Badge,
} from '@mui/material';
import {
  Dashboard,
  Pets,
  LocalHospital,
  Favorite,
  Restaurant,
  AccountBalance,
  Inventory,
  Store,
  Assessment,
  MenuBook,
  Settings,
  Gavel,
  ExpandLess,
  ExpandMore,
  ChevronLeft,
  Agriculture,
  TrendingUp,
  Notifications,
  WbSunny,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';

interface ModernSidebarProps {
  open: boolean;
  onClose: () => void;
  userRole: string;
  userName: string;
  userAvatar?: string;
}

interface MenuItem {
  id: string;
  title: string;
  icon: React.ElementType;
  path: string;
  badge?: number;
  color: string;
  gradient: string;
  children?: MenuItem[];
  premium?: boolean;
}

const ModernSidebar: React.FC<ModernSidebarProps> = ({
  open,
  onClose,
  userRole,
  userName,
  userAvatar,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>(['dashboard']);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: Dashboard,
      path: '/dashboard',
      color: theme.palette.primary.main,
      gradient: 'linear-gradient(135deg, #42AF9B 0%, #66BB6A 100%)',
      badge: 3,
    },
    {
      id: 'animals',
      title: 'Animals',
      icon: Pets,
      path: '/dashboard/animals',
      color: '#FF6B6B',
      gradient: 'linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%)',
      children: [
        { id: 'animals-list', title: 'Animal List', icon: Pets, path: '/dashboard/animals', color: '#FF6B6B', gradient: '' },
        { id: 'animals-add', title: 'Add Animal', icon: Pets, path: '/dashboard/animals/add', color: '#FF6B6B', gradient: '' },
        { id: 'animals-groups', title: 'Groups', icon: Pets, path: '/dashboard/animals/groups', color: '#FF6B6B', gradient: '' },
      ],
    },
    {
      id: 'health',
      title: 'Health',
      icon: LocalHospital,
      path: '/dashboard/health',
      color: '#4ECDC4',
      gradient: 'linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%)',
      badge: 2,
      children: [
        { id: 'health-records', title: 'Health Records', icon: LocalHospital, path: '/dashboard/health', color: '#4ECDC4', gradient: '' },
        { id: 'health-treatments', title: 'Treatments', icon: LocalHospital, path: '/dashboard/health/treatments', color: '#4ECDC4', gradient: '' },
        { id: 'health-vaccinations', title: 'Vaccinations', icon: LocalHospital, path: '/dashboard/health/vaccinations', color: '#4ECDC4', gradient: '' },
      ],
    },
    {
      id: 'breeding',
      title: 'Breeding',
      icon: Favorite,
      path: '/dashboard/breeding',
      color: '#FF8A80',
      gradient: 'linear-gradient(135deg, #FF8A80 0%, #FF5722 100%)',
      children: [
        { id: 'breeding-records', title: 'Breeding Records', icon: Favorite, path: '/dashboard/breeding', color: '#FF8A80', gradient: '' },
        { id: 'breeding-calendar', title: 'Calendar', icon: Favorite, path: '/dashboard/breeding/calendar', color: '#FF8A80', gradient: '' },
        { id: 'breeding-genetics', title: 'Genetics', icon: Favorite, path: '/dashboard/breeding/genetics', color: '#FF8A80', gradient: '', premium: true },
      ],
    },
    {
      id: 'feeding',
      title: 'Feeding',
      icon: Restaurant,
      path: '/dashboard/feeding',
      color: '#FFB74D',
      gradient: 'linear-gradient(135deg, #FFB74D 0%, #FF9800 100%)',
      children: [
        { id: 'feeding-schedule', title: 'Schedule', icon: Restaurant, path: '/dashboard/feeding', color: '#FFB74D', gradient: '' },
        { id: 'feeding-nutrition', title: 'Nutrition', icon: Restaurant, path: '/dashboard/feeding/nutrition', color: '#FFB74D', gradient: '' },
        { id: 'feeding-costs', title: 'Feed Costs', icon: Restaurant, path: '/dashboard/feeding/costs', color: '#FFB74D', gradient: '' },
      ],
    },
    {
      id: 'financial',
      title: 'Financial',
      icon: AccountBalance,
      path: '/dashboard/financial',
      color: '#81C784',
      gradient: 'linear-gradient(135deg, #81C784 0%, #4CAF50 100%)',
      children: [
        { id: 'financial-overview', title: 'Overview', icon: AccountBalance, path: '/dashboard/financial', color: '#81C784', gradient: '' },
        { id: 'financial-income', title: 'Income', icon: AccountBalance, path: '/dashboard/financial/income', color: '#81C784', gradient: '' },
        { id: 'financial-expenses', title: 'Expenses', icon: AccountBalance, path: '/dashboard/financial/expenses', color: '#81C784', gradient: '' },
        { id: 'financial-reports', title: 'Reports', icon: AccountBalance, path: '/dashboard/financial/reports', color: '#81C784', gradient: '', premium: true },
      ],
    },
    {
      id: 'inventory',
      title: 'Inventory',
      icon: Inventory,
      path: '/dashboard/inventory',
      color: '#9575CD',
      gradient: 'linear-gradient(135deg, #9575CD 0%, #673AB7 100%)',
      premium: true,
    },
    {
      id: 'commercial',
      title: 'Commercial',
      icon: Store,
      path: '/dashboard/commercial',
      color: '#F06292',
      gradient: 'linear-gradient(135deg, #F06292 0%, #E91E63 100%)',
      premium: true,
    },
    {
      id: 'reports',
      title: 'Reports',
      icon: Assessment,
      path: '/dashboard/reports',
      color: '#64B5F6',
      gradient: 'linear-gradient(135deg, #64B5F6 0%, #2196F3 100%)',
      children: [
        { id: 'reports-analytics', title: 'Analytics', icon: TrendingUp, path: '/dashboard/reports/analytics', color: '#64B5F6', gradient: '' },
        { id: 'reports-custom', title: 'Custom Reports', icon: Assessment, path: '/dashboard/reports/custom', color: '#64B5F6', gradient: '', premium: true },
      ],
    },
    {
      id: 'resources',
      title: 'Resources',
      icon: MenuBook,
      path: '/dashboard/resources',
      color: '#A1887F',
      gradient: 'linear-gradient(135deg, #A1887F 0%, #8D6E63 100%)',
      children: [
        { id: 'resources-weather', title: 'Weather', icon: WbSunny, path: '/dashboard/resources/weather', color: '#A1887F', gradient: '' },
        { id: 'resources-market', title: 'Market Prices', icon: TrendingUp, path: '/dashboard/resources/market', color: '#A1887F', gradient: '' },
        { id: 'resources-guides', title: 'Guides', icon: MenuBook, path: '/dashboard/resources/guides', color: '#A1887F', gradient: '' },
      ],
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      path: '/dashboard/settings',
      color: '#90A4AE',
      gradient: 'linear-gradient(135deg, #90A4AE 0%, #607D8B 100%)',
    },
    {
      id: 'compliance',
      title: 'Compliance',
      icon: Gavel,
      path: '/dashboard/compliance',
      color: '#FFAB91',
      gradient: 'linear-gradient(135deg, #FFAB91 0%, #FF7043 100%)',
      premium: true,
    },
  ];

  const handleItemClick = (item: MenuItem) => {
    if (item.children) {
      setExpandedItems(prev => 
        prev.includes(item.id) 
          ? prev.filter(id => id !== item.id)
          : [...prev, item.id]
      );
    } else {
      navigate(item.path);
      if (window.innerWidth < 768) {
        onClose();
      }
    }
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const isPremiumLocked = (item: MenuItem) => {
    return item.premium && userRole === 'beta';
  };

  const drawerWidth = 280;

  return (
    <Drawer
      variant="persistent"
      anchor="left"
      open={open}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: `linear-gradient(180deg, 
            ${alpha(theme.palette.background.paper, 0.95)} 0%, 
            ${alpha(theme.palette.background.default, 0.98)} 100%
          )`,
          backdropFilter: 'blur(20px)',
          borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',
        },
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 3,
          background: 'linear-gradient(135deg, #42AF9B 0%, #66BB6A 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: -50,
            right: -50,
            width: 100,
            height: 100,
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.1)',
          },
        }}
      >
        <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
          <Box display="flex" alignItems="center" gap={1}>
            <Agriculture sx={{ fontSize: 32 }} />
            <Typography variant="h5" fontWeight="bold">
              AgriIntel
            </Typography>
          </Box>
          <IconButton onClick={onClose} sx={{ color: 'white' }}>
            <ChevronLeft />
          </IconButton>
        </Box>

        <Box display="flex" alignItems="center" gap={2}>
          <Avatar
            src={userAvatar}
            sx={{
              width: 48,
              height: 48,
              border: '2px solid rgba(255, 255, 255, 0.3)',
            }}
          >
            {userName.charAt(0).toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" fontWeight="600">
              {userName}
            </Typography>
            <Chip
              label={userRole.toUpperCase()}
              size="small"
              sx={{
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                fontWeight: 600,
                fontSize: '0.7rem',
              }}
            />
          </Box>
        </Box>
      </Box>

      {/* Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 2 }}>
        <List sx={{ px: 2 }}>
          {menuItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
            >
              <ListItem disablePadding sx={{ mb: 1 }}>
                <ListItemButton
                  onClick={() => handleItemClick(item)}
                  disabled={isPremiumLocked(item)}
                  sx={{
                    borderRadius: 2,
                    mb: 0.5,
                    background: isActive(item.path) ? item.gradient : 'transparent',
                    color: isActive(item.path) ? 'white' : theme.palette.text.primary,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      background: isActive(item.path) 
                        ? item.gradient 
                        : alpha(item.color, 0.1),
                      transform: 'translateX(4px)',
                    },
                    '&.Mui-disabled': {
                      opacity: 0.5,
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: isActive(item.path) ? 'white' : item.color,
                      minWidth: 40,
                    }}
                  >
                    <Badge badgeContent={item.badge} color="error">
                      <item.icon />
                    </Badge>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={item.title}
                    primaryTypographyProps={{
                      fontWeight: isActive(item.path) ? 600 : 500,
                      fontSize: '0.9rem',
                    }}
                  />
                  
                  {isPremiumLocked(item) && (
                    <Chip
                      label="PRO"
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.6rem',
                        backgroundColor: theme.palette.warning.main,
                        color: 'white',
                        fontWeight: 600,
                      }}
                    />
                  )}
                  
                  {item.children && (
                    expandedItems.includes(item.id) ? <ExpandLess /> : <ExpandMore />
                  )}
                </ListItemButton>
              </ListItem>

              {/* Sub-items */}
              {item.children && (
                <AnimatePresence>
                  <Collapse in={expandedItems.includes(item.id)} timeout="auto" unmountOnExit>
                    <List component="div" disablePadding sx={{ ml: 2 }}>
                      {item.children.map((child) => (
                        <motion.div
                          key={child.id}
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <ListItem disablePadding>
                            <ListItemButton
                              onClick={() => navigate(child.path)}
                              disabled={isPremiumLocked(child)}
                              sx={{
                                borderRadius: 1.5,
                                py: 1,
                                background: isActive(child.path) 
                                  ? alpha(child.color, 0.2) 
                                  : 'transparent',
                                '&:hover': {
                                  background: alpha(child.color, 0.1),
                                  transform: 'translateX(2px)',
                                },
                                '&.Mui-disabled': {
                                  opacity: 0.5,
                                },
                              }}
                            >
                              <ListItemIcon
                                sx={{
                                  color: child.color,
                                  minWidth: 32,
                                  ml: 1,
                                }}
                              >
                                <child.icon sx={{ fontSize: 18 }} />
                              </ListItemIcon>
                              
                              <ListItemText
                                primary={child.title}
                                primaryTypographyProps={{
                                  fontSize: '0.8rem',
                                  fontWeight: isActive(child.path) ? 600 : 400,
                                }}
                              />
                              
                              {isPremiumLocked(child) && (
                                <Chip
                                  label="PRO"
                                  size="small"
                                  sx={{
                                    height: 16,
                                    fontSize: '0.5rem',
                                    backgroundColor: theme.palette.warning.main,
                                    color: 'white',
                                    fontWeight: 600,
                                  }}
                                />
                              )}
                            </ListItemButton>
                          </ListItem>
                        </motion.div>
                      ))}
                    </List>
                  </Collapse>
                </AnimatePresence>
              )}
            </motion.div>
          ))}
        </List>
      </Box>

      {/* Footer */}
      <Box
        sx={{
          p: 2,
          borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          background: alpha(theme.palette.background.paper, 0.5),
        }}
      >
        <Box
          sx={{
            p: 2,
            borderRadius: 2,
            background: 'linear-gradient(135deg, #42AF9B 0%, #66BB6A 100%)',
            color: 'white',
            textAlign: 'center',
          }}
        >
          <Typography variant="caption" display="block" fontWeight="600">
            🚀 Upgrade to Pro
          </Typography>
          <Typography variant="caption" sx={{ opacity: 0.9 }}>
            Unlock all features
          </Typography>
        </Box>
      </Box>
    </Drawer>
  );
};

export default ModernSidebar;
