import React from 'react';
import MetricsChart from '../components/modules/reports/MetricsChart';

const Analytics: React.FC = () => {
  const initialData = {
    labels: [],
    datasets: []
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
      <MetricsChart
        title="Livestock Status"
        description="Overview of animal health and breeding status"
        type="line"
        apiEndpoint="metrics/livestock"
        timeRange="week"
        refreshInterval={300000}
        initialData={initialData}
      />
      
      <MetricsChart
        title="Financial Performance"
        description="Revenue, expenses and profit trends"
        type="bar"
        apiEndpoint="metrics/financial"
        timeRange="month"
        initialData={initialData}
      />
    </div>
  );
};

export default Analytics;
