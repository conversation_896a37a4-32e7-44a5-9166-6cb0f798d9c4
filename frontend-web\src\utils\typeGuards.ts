import { Animal, AnimalStats } from '../types/animal';

export function isAnimal(obj: any): obj is Animal {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.tagNumber === 'string' &&
    typeof obj.type === 'string';
}

export function isAnimalStats(obj: any): obj is AnimalStats {
  return obj &&
    typeof obj.totalAnimals === 'number' &&
    typeof obj.healthPercentage === 'number' &&
    obj.byStatus && obj.byHealth;
}