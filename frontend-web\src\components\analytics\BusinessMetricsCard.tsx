import React from 'react';
import { Box, Typography, Grid, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, TrendingFlat } from '@mui/icons-material';
import BusinessAnalyticsCard from './BusinessAnalyticsCard';

interface MetricProps {
  label: string;
  value: string | number;
  change?: string;
  trend?: 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
}

interface BusinessMetricsCardProps {
  title: string;
  subtitle?: string;
  metrics: MetricProps[];
  icon?: React.ReactNode;
  loading?: boolean;
  delay?: number;
}

/**
 * A card component for displaying business metrics
 */
const BusinessMetricsCard: React.FC<BusinessMetricsCardProps> = ({
  title,
  subtitle,
  metrics,
  icon,
  loading = false,
  delay = 0
}) => {
  const theme = useTheme();

  const getTrendIcon = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp fontSize="small" />;
      case 'down':
        return <TrendingDown fontSize="small" />;
      default:
        return <TrendingFlat fontSize="small" />;
    }
  };

  const getTrendColor = (trend?: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return theme.palette.success.main;
      case 'down':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  return (
    <BusinessAnalyticsCard
      title={title}
      subtitle={subtitle}
      icon={icon}
      loading={loading}
      delay={delay}
    >
      <Grid container spacing={2}>
        {metrics.map((metric, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: delay + (index * 0.1) }}
            >
              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  background: alpha(theme.palette.background.paper, 0.7),
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                  boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.05)}`,
                  height: '100%'
                }}
              >
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {metric.label}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'baseline', mt: 1 }}>
                  <Typography variant="h5" fontWeight="bold">
                    {metric.value}
                  </Typography>
                  {metric.change && (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        ml: 1,
                        color: getTrendColor(metric.trend),
                        fontSize: '0.875rem'
                      }}
                    >
                      {getTrendIcon(metric.trend)}
                      <Typography variant="body2" sx={{ ml: 0.5 }}>
                        {metric.change}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </motion.div>
          </Grid>
        ))}
      </Grid>
    </BusinessAnalyticsCard>
  );
};

export default BusinessMetricsCard;
