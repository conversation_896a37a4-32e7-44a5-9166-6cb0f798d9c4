import React, { useState } from 'react';
import { 
  Box, 
  Grid, 
  Typography, 
  Card, 
  CardContent, 
  Chip,
  IconButton,
  Dialog,
  DialogContent,
  DialogTitle,
  useTheme,
  alpha
} from '@mui/material';
import { 
  ZoomIn, 
  Close, 
  Pets, 
  LocalHospital, 
  Restaurant, 
  Business,
  Memory,
  Visibility
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import OptimizedImage from '../common/OptimizedImage';

interface ImageItem {
  src: string;
  title: string;
  category: string;
  description: string;
  icon: React.ReactElement;
  color: string;
}

const ImageShowcase: React.FC = () => {
  const theme = useTheme();
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);

  const showcaseImages: ImageItem[] = [
    // Premium Cattle Images
    {
      src: '/images/modules/animals/cattle-1.jpeg',
      title: 'Premium Cattle Management',
      category: 'Animals',
      description: 'High-quality cattle breeding and management systems',
      icon: <Pets />,
      color: '#4CAF50'
    },
    {
      src: '/images/modules/animals/cattle-2.avif',
      title: 'Advanced Livestock Tracking',
      category: 'Animals',
      description: 'Modern livestock identification and monitoring',
      icon: <Pets />,
      color: '#4CAF50'
    },
    {
      src: '/images/modules/animals/cattle-3.jpeg',
      title: 'Pasture Management',
      category: 'Animals',
      description: 'Sustainable grazing and pasture optimization',
      icon: <Pets />,
      color: '#4CAF50'
    },
    {
      src: '/images/modules/animals/cattle-4.jpeg',
      title: 'Dairy Operations',
      category: 'Animals',
      description: 'Professional dairy farm management solutions',
      icon: <Pets />,
      color: '#4CAF50'
    },
    {
      src: '/images/modules/animals/cattle-5.avif',
      title: 'Beef Production',
      category: 'Animals',
      description: 'Optimized beef cattle production systems',
      icon: <Pets />,
      color: '#4CAF50'
    },

    // Health & Veterinary
    {
      src: '/images/modules/health/health-main.png',
      title: 'Health Management System',
      category: 'Health',
      description: 'Comprehensive animal health monitoring',
      icon: <LocalHospital />,
      color: '#F44336'
    },
    {
      src: '/images/modules/health/veterinary-1.jpg',
      title: 'Veterinary Care',
      category: 'Health',
      description: 'Professional veterinary services and care',
      icon: <LocalHospital />,
      color: '#F44336'
    },
    {
      src: '/images/modules/health/veterinary-2.jpg',
      title: 'Medical Procedures',
      category: 'Health',
      description: 'Advanced medical procedures and treatments',
      icon: <LocalHospital />,
      color: '#F44336'
    },
    {
      src: '/images/modules/health/veterinary-3.jpg',
      title: 'Health Monitoring',
      category: 'Health',
      description: 'Real-time health status monitoring',
      icon: <LocalHospital />,
      color: '#F44336'
    },

    // Feeding & Nutrition
    {
      src: '/images/modules/feeding/feed-main.jpeg',
      title: 'Feed Management',
      category: 'Feeding',
      description: 'Optimized nutrition and feeding programs',
      icon: <Restaurant />,
      color: '#FF9800'
    },
    {
      src: '/images/modules/feeding/feed-2.jpeg',
      title: 'Nutrition Planning',
      category: 'Feeding',
      description: 'Scientific nutrition planning and optimization',
      icon: <Restaurant />,
      color: '#FF9800'
    },
    {
      src: '/images/modules/feeding/feed-sheep.jpeg',
      title: 'Sheep Nutrition',
      category: 'Feeding',
      description: 'Specialized sheep feeding and nutrition',
      icon: <Restaurant />,
      color: '#FF9800'
    },

    // Commercial & Business
    {
      src: '/images/modules/commercial/commercial-1.jpeg',
      title: 'Commercial Operations',
      category: 'Commercial',
      description: 'Large-scale commercial livestock operations',
      icon: <Business />,
      color: '#2196F3'
    },
    {
      src: '/images/modules/commercial/commercial-2.jpeg',
      title: 'Market Analytics',
      category: 'Commercial',
      description: 'Market analysis and business intelligence',
      icon: <Business />,
      color: '#2196F3'
    },
    {
      src: '/images/modules/commercial/commercial-3.jpeg',
      title: 'Supply Chain',
      category: 'Commercial',
      description: 'Integrated supply chain management',
      icon: <Business />,
      color: '#2196F3'
    },

    // RFID & Technology
    {
      src: '/images/modules/rfid/rfid-1.webp',
      title: 'RFID Technology',
      category: 'Technology',
      description: 'Advanced RFID tracking and identification',
      icon: <Memory />,
      color: '#9C27B0'
    },
    {
      src: '/images/modules/rfid/rfid-2.jpg',
      title: 'Smart Monitoring',
      category: 'Technology',
      description: 'IoT-based smart monitoring systems',
      icon: <Memory />,
      color: '#9C27B0'
    },
    {
      src: '/images/modules/rfid/rfid-3.jpg',
      title: 'Data Analytics',
      category: 'Technology',
      description: 'Big data analytics for livestock management',
      icon: <Memory />,
      color: '#9C27B0'
    },
    {
      src: '/images/modules/rfid/rfid-4.jpg',
      title: 'Automation Systems',
      category: 'Technology',
      description: 'Automated livestock management systems',
      icon: <Memory />,
      color: '#9C27B0'
    }
  ];

  const categories = [...new Set(showcaseImages.map(img => img.category))];

  return (
    <Box sx={{ p: 4 }}>
      <Typography 
        variant="h3" 
        sx={{ 
          textAlign: 'center', 
          mb: 4, 
          fontWeight: 'bold',
          background: 'linear-gradient(45deg, #4CAF50, #2196F3)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent'
        }}
      >
        🌾 AgriIntel Professional Image Gallery
      </Typography>

      {categories.map(category => (
        <Box key={category} sx={{ mb: 6 }}>
          <Typography variant="h4" sx={{ mb: 3, fontWeight: 'bold', color: theme.palette.primary.main }}>
            {category} Management
          </Typography>
          
          <Grid container spacing={3}>
            {showcaseImages
              .filter(img => img.category === category)
              .map((image, index) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ y: -8, scale: 1.02 }}
                  >
                    <Card
                      sx={{
                        position: 'relative',
                        overflow: 'hidden',
                        cursor: 'pointer',
                        borderRadius: '16px',
                        background: alpha(image.color, 0.1),
                        border: `2px solid ${alpha(image.color, 0.3)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          boxShadow: `0 16px 48px ${alpha(image.color, 0.4)}`,
                          border: `2px solid ${image.color}`
                        }
                      }}
                      onClick={() => setSelectedImage(image)}
                    >
                      <Box sx={{ position: 'relative', height: 200 }}>
                        <OptimizedImage
                          src={image.src}
                          alt={image.title}
                          width="100%"
                          height="100%"
                          borderRadius="16px 16px 0 0"
                        />
                        
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            background: alpha('#000', 0.7),
                            borderRadius: '50%',
                            p: 1
                          }}
                        >
                          <IconButton size="small" sx={{ color: 'white' }}>
                            <ZoomIn />
                          </IconButton>
                        </Box>

                        <Chip
                          icon={image.icon}
                          label={image.category}
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 8,
                            left: 8,
                            background: image.color,
                            color: 'white',
                            fontWeight: 'bold'
                          }}
                        />
                      </Box>

                      <CardContent>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                          {image.title}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {image.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
          </Grid>
        </Box>
      ))}

      {/* Image Preview Dialog */}
      <Dialog
        open={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            background: alpha('#000', 0.9)
          }
        }}
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'white' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {selectedImage?.icon && React.cloneElement(selectedImage.icon, { sx: { color: selectedImage.color } })}
            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
              {selectedImage?.title}
            </Typography>
          </Box>
          <IconButton onClick={() => setSelectedImage(null)} sx={{ color: 'white' }}>
            <Close />
          </IconButton>
        </DialogTitle>
        
        <DialogContent sx={{ p: 0 }}>
          {selectedImage && (
            <OptimizedImage
              src={selectedImage.src}
              alt={selectedImage.title}
              width="100%"
              height="600px"
            />
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default ImageShowcase;
