import { useState, useCallback } from 'react';

/**
 * Custom hook for managing report selection
 * @returns Object with selection state and handlers
 */
export const useReportSelection = <T extends { id: string }>(initialSelected: T[] = []) => {
  const [selectedReports, setSelectedReports] = useState<T[]>(initialSelected);

  /**
   * Toggle selection of a report
   * @param report The report to toggle selection for
   */
  const toggleReportSelection = useCallback((report: T) => {
    setSelectedReports(prev => {
      const isSelected = prev.some(r => r.id === report.id);
      
      if (isSelected) {
        return prev.filter(r => r.id !== report.id);
      } else {
        return [...prev, report];
      }
    });
  }, []);

  /**
   * Select a single report (deselecting all others)
   * @param report The report to select
   */
  const selectSingleReport = useCallback((report: T) => {
    setSelectedReports([report]);
  }, []);

  /**
   * Check if a report is selected
   * @param reportId The ID of the report to check
   * @returns Boolean indicating if the report is selected
   */
  const isReportSelected = useCallback((reportId: string) => {
    return selectedReports.some(report => report.id === reportId);
  }, [selectedReports]);

  /**
   * Clear all selected reports
   */
  const clearSelectedReports = useCallback(() => {
    setSelectedReports([]);
  }, []);

  /**
   * Select all reports
   * @param reports The reports to select
   */
  const selectAllReports = useCallback((reports: T[]) => {
    setSelectedReports(reports);
  }, []);

  return {
    selectedReports,
    toggleReportSelection,
    selectSingleReport,
    isReportSelected,
    clearSelectedReports,
    selectAllReports
  };
};

export default useReportSelection;
