// This file provides a mock implementation of MongoDB for the frontend
// Import mock data from mocks directory
// Note: These imports are commented out to avoid errors if the mock files don't exist
// In a real implementation, you would import actual mock data

// Define MongoDB query operators interface for type safety
interface MongoQueryOperators {
  $gte?: any;
  $lte?: any;
  $eq?: any;
  $elemMatch?: {
    $eq?: any;
  };
}

// Mock data for collections
const mockAnimals: any[] = [];
const mockFeedingRecords: any[] = [];
const mockHealthRecords: any[] = [];
const mockBreedingRecords: any[] = [];
const mockInventory: any[] = [];
const mockUsers: any[] = [];
const mockFeedInventory: any[] = [];
const mockCalvingRecords: any[] = [];
const mockVaccinations: any[] = [];
const mockFinancialRecords: any[] = [];
const mockComplianceRecords: any[] = [];
const mockDocuments: any[] = [];
const mockMarketplaceListings: any[] = [];

// Mock ObjectId class
export class ObjectId {
  id: string;

  constructor(id?: string) {
    this.id = id || 'mock-id-' + Date.now();
  }

  toString() {
    return this.id;
  }

  toHexString() {
    return this.id;
  }
}

// Mock data store
const mockCollections: Record<string, any[]> = {
  'animals': mockAnimals,
  'feeding_records': mockFeedingRecords,
  'health_records': mockHealthRecords,
  'breeding_records': mockBreedingRecords,
  'inventory': mockInventory,
  'users': mockUsers,
  'feed_inventory': mockFeedInventory,
  'calving_records': mockCalvingRecords,
  'vaccinations': mockVaccinations,
  'financial_records': mockFinancialRecords,
  'compliance_records': mockComplianceRecords,
  'documents': mockDocuments,
  'marketplace_listings': mockMarketplaceListings
};

// Mock Collection class
export class MockCollection {
  name: string;
  data: any[];

  constructor(name: string) {
    this.name = name;
    this.data = mockCollections[name] || [];
  }

  find(query = {}) {
    console.log(`Mock find for ${this.name} with query:`, query);

    // Filter data based on query
    let filteredData = [...this.data];

    try {
      if (Object.keys(query).length > 0) {
        filteredData = filteredData.filter(item => {
          return Object.entries(query).every(([key, value]) => {
            // Handle special MongoDB operators
            if (key === '_id' && value instanceof ObjectId) {
              return item._id === value.toString() || item.id === value.toString();
            }

            // Handle $or operator
            if (key === '$or' && Array.isArray(value)) {
              return value.some(condition => {
                return Object.entries(condition).every(([condKey, condValue]) => {
                  if (condKey === '_id' && condValue instanceof ObjectId) {
                    return item._id === condValue.toString() || item.id === condValue.toString();
                  }
                  return item[condKey] === condValue;
                });
              });
            }

            if (typeof value === 'object' && value !== null) {
              // Handle $gte, $lte operators for dates
              const valueObj = value as { $gte?: string | Date, $lte?: string | Date };
              if (valueObj.$gte && valueObj.$lte) {
                const itemDate = new Date(item[key]);
                return itemDate >= new Date(valueObj.$gte) && itemDate <= new Date(valueObj.$lte);
              } else if (valueObj.$gte) {
                const itemDate = new Date(item[key]);
                return itemDate >= new Date(valueObj.$gte);
              } else if (valueObj.$lte) {
                const itemDate = new Date(item[key]);
                return itemDate <= new Date(valueObj.$lte);
              }

              // Handle $elemMatch operator
              const elemMatchObj = value as { $elemMatch?: { $eq?: any } };
              if (elemMatchObj.$elemMatch) {
                const array = item[key];
                if (!Array.isArray(array)) return false;

                if (elemMatchObj.$elemMatch.$eq) {
                  return array.includes(elemMatchObj.$elemMatch.$eq);
                }
                return false;
              }
            }

            return item[key] === value;
          });
        });
      }
    } catch (error) {
      console.error(`Error filtering data for ${this.name}:`, error);
      // Return empty array on error
      filteredData = [];
    }

    // Create a cursor object with toArray method
    const cursor = {
      toArray: async () => {
        try {
          return filteredData;
        } catch (error) {
          console.error(`Error in toArray for ${this.name}:`, error);
          return [];
        }
      },
      sort: (sortOptions: any) => {
        try {
          // Sort the data if sortOptions is provided
          if (sortOptions) {
            const sortKey = Object.keys(sortOptions)[0];
            const sortDirection = sortOptions[sortKey];

            filteredData.sort((a, b) => {
              if (a[sortKey] < b[sortKey]) return sortDirection === 1 ? -1 : 1;
              if (a[sortKey] > b[sortKey]) return sortDirection === 1 ? 1 : -1;
              return 0;
            });
          }

          return {
            limit: (n: number) => ({
              toArray: async () => filteredData.slice(0, n)
            }),
            skip: (n: number) => ({
              limit: (m: number) => ({
                toArray: async () => filteredData.slice(n, n + m)
              }),
              toArray: async () => filteredData.slice(n)
            }),
            toArray: async () => filteredData
          };
        } catch (error) {
          console.error(`Error in sort for ${this.name}:`, error);
          return {
            limit: () => ({ toArray: async () => [] }),
            skip: () => ({ toArray: async () => [], limit: () => ({ toArray: async () => [] }) }),
            toArray: async () => []
          };
        }
      },
      limit: (n: number) => {
        try {
          return {
            toArray: async () => filteredData.slice(0, n),
            skip: (m: number) => ({
              toArray: async () => filteredData.slice(m, m + n)
            }),
            sort: (sortOptions: any) => ({
              toArray: async () => filteredData.slice(0, n)
            })
          };
        } catch (error) {
          console.error(`Error in limit for ${this.name}:`, error);
          return {
            toArray: async () => [],
            skip: () => ({ toArray: async () => [] }),
            sort: () => ({ toArray: async () => [] })
          };
        }
      },
      skip: (n: number) => {
        try {
          return {
            toArray: async () => filteredData.slice(n),
            limit: (m: number) => ({
              toArray: async () => filteredData.slice(n, n + m)
            }),
            sort: (sortOptions: any) => ({
              toArray: async () => filteredData.slice(n)
            })
          };
        } catch (error) {
          console.error(`Error in skip for ${this.name}:`, error);
          return {
            toArray: async () => [],
            limit: () => ({ toArray: async () => [] }),
            sort: () => ({ toArray: async () => [] })
          };
        }
      }
    };

    return cursor;
  }

  findOne(query: any) {
    console.log(`Mock findOne for ${this.name} with query:`, query);

    // Find the first item that matches the query
    const item = this.data.find(item => {
      return Object.entries(query).every(([key, value]) => {
        if (key === '_id' && value instanceof ObjectId) {
          return item._id === value.toString() || item.id === value.toString();
        }
        return item[key] === value;
      });
    });

    return Promise.resolve(item || null);
  }

  insertOne(doc: any) {
    console.log(`Mock insertOne for ${this.name}:`, doc);

    // Generate a new ID if not provided
    const id = doc.id || this.data.length + 1;
    const newDoc = { ...doc, id };

    // Add the document to the collection
    this.data.push(newDoc);

    return Promise.resolve({
      insertedId: new ObjectId(id.toString()),
      acknowledged: true,
      insertedCount: 1
    });
  }

  updateOne(filter: any, update: any) {
    console.log(`Mock updateOne for ${this.name}:`, filter, update);

    // Find the index of the item to update
    const index = this.data.findIndex(item => {
      return Object.entries(filter).every(([key, value]) => {
        if (key === '_id' && value instanceof ObjectId) {
          return item._id === value.toString() || item.id === value.toString();
        }
        return item[key] === value;
      });
    });

    if (index === -1) {
      return Promise.resolve({ matchedCount: 0, modifiedCount: 0 });
    }

    // Update the item
    if (update.$set) {
      this.data[index] = { ...this.data[index], ...update.$set };
    }

    return Promise.resolve({ matchedCount: 1, modifiedCount: 1 });
  }

  deleteOne(filter: any) {
    console.log(`Mock deleteOne for ${this.name}:`, filter);

    // Find the index of the item to delete
    const index = this.data.findIndex(item => {
      return Object.entries(filter).every(([key, value]) => {
        if (key === '_id' && value instanceof ObjectId) {
          return item._id === value.toString() || item.id === value.toString();
        }
        return item[key] === value;
      });
    });

    if (index === -1) {
      return Promise.resolve({ deletedCount: 0 });
    }

    // Remove the item from the collection
    this.data.splice(index, 1);

    return Promise.resolve({ deletedCount: 1 });
  }

  deleteMany(filter: any) {
    console.log(`Mock deleteMany for ${this.name} with filter:`, filter);

    // Count documents that match the filter before deletion
    let count = 0;

    // Filter and remove matching documents
    this.data = this.data.filter(item => {
      const matches = Object.entries(filter).every(([key, value]) => {
        if (key === '_id' && value instanceof ObjectId) {
          return item._id === value.toString() || item.id === value.toString();
        }

        // Handle $or operator
        if (key === '$or' && Array.isArray(value)) {
          return value.some(condition => {
            return Object.entries(condition).every(([condKey, condValue]) => {
              if (condKey === '_id' && condValue instanceof ObjectId) {
                return item._id === condValue.toString() || item.id === condValue.toString();
              }
              return item[condKey] === condValue;
            });
          });
        }

        // Handle $elemMatch operator
        if (typeof value === 'object' && value !== null) {
          const elemMatchObj = value as { $elemMatch?: { $eq?: any } };
          if (elemMatchObj.$elemMatch) {
            const array = item[key];
            if (!Array.isArray(array)) return false;

            if (elemMatchObj.$elemMatch.$eq) {
              return array.includes(elemMatchObj.$elemMatch.$eq);
            }
            return false;
          }
        }

        return item[key] === value;
      });

      if (matches) {
        count++;
        return false; // Remove from array
      }
      return true; // Keep in array
    });

    return Promise.resolve({ deletedCount: count });
  }

  countDocuments(query = {}) {
    console.log(`Mock countDocuments for ${this.name} with query:`, query);

    // Count documents that match the query
    if (Object.keys(query).length === 0) {
      return Promise.resolve(this.data.length);
    }

    const count = this.data.filter(item => {
      return Object.entries(query).every(([key, value]) => {
        if (key === '_id' && value instanceof ObjectId) {
          return item._id === value.toString() || item.id === value.toString();
        }
        return item[key] === value;
      });
    }).length;

    return Promise.resolve(count);
  }
}

// Mock MongoDB service
export class MockMongoDbService {
  collection: MockCollection;

  constructor(collectionName: string) {
    this.collection = new MockCollection(collectionName);
  }

  // Add methods to match MongoDB API
  find(query = {}) {
    try {
      return this.collection.find(query);
    } catch (error) {
      console.error(`Error in MockMongoDbService.find for ${this.collection.name}:`, error);
      // Return a cursor with empty results
      return {
        toArray: async () => [],
        sort: () => ({
          limit: () => ({ toArray: async () => [] }),
          skip: () => ({ toArray: async () => [], limit: () => ({ toArray: async () => [] }) }),
          toArray: async () => []
        }),
        limit: () => ({
          toArray: async () => [],
          skip: () => ({ toArray: async () => [] }),
          sort: () => ({ toArray: async () => [] })
        }),
        skip: () => ({
          toArray: async () => [],
          limit: () => ({ toArray: async () => [] }),
          sort: () => ({ toArray: async () => [] })
        })
      };
    }
  }

  findOne(query: any) {
    return this.collection.findOne(query);
  }

  insertOne(doc: any) {
    return this.collection.insertOne(doc);
  }

  updateOne(filter: any, update: any) {
    return this.collection.updateOne(filter, update);
  }

  deleteOne(filter: any) {
    return this.collection.deleteOne(filter);
  }

  deleteMany(filter: any) {
    return this.collection.deleteMany(filter);
  }

  countDocuments(query = {}) {
    return this.collection.countDocuments(query);
  }
}

// For backward compatibility
export const MockMongoService = MockMongoDbService;

// Function to get a mock collection
export const getCollection = async (collectionName: string) => {
  try {
    return new MockCollection(collectionName);
  } catch (error) {
    console.error(`Error creating mock collection for ${collectionName}:`, error);
    // Return a minimal mock collection with empty data
    return {
      name: collectionName,
      data: [],
      find: () => ({
        toArray: async () => []
      }),
      findOne: async () => null,
      insertOne: async () => ({ insertedId: 'error' }),
      updateOne: async () => ({ modifiedCount: 0 }),
      deleteOne: async () => ({ deletedCount: 0 }),
      countDocuments: async () => 0
    };
  }
};

// Create a named export for the mock MongoDB service
export const mockMongoDb = {
  getCollection,
  ObjectId,
  MockMongoDbService,
  MockMongoService
};

// Export the mock MongoDB service as default
export default mockMongoDb;
