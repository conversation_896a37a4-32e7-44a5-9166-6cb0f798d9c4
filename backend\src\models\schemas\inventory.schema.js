/**
 * Inventory Schema
 *
 * Defines the MongoDB schema for the inventory collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Inventory Item Schema
const inventoryItemSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['feed', 'medication', 'equipment', 'supplies', 'other'],
    default: 'other'
  },
  subcategory: {
    type: String,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  sku: {
    type: String,
    trim: true
  },
  barcode: {
    type: String,
    trim: true
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  unit: {
    type: String,
    required: true,
    trim: true
  },
  unitCost: {
    type: Number,
    min: 0
  },
  totalValue: {
    type: Number,
    min: 0
  },
  location: {
    type: String,
    trim: true
  },
  minimumLevel: {
    type: Number,
    min: 0
  },
  reorderLevel: {
    type: Number,
    min: 0
  },
  reorderQuantity: {
    type: Number,
    min: 0
  },
  supplier: {
    name: {
      type: String,
      trim: true
    },
    contactPerson: {
      type: String,
      trim: true
    },
    phone: {
      type: String,
      trim: true
    },
    email: {
      type: String,
      trim: true
    },
    website: {
      type: String,
      trim: true
    }
  },
  expirationDate: {
    type: Date
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String
  },
  status: {
    type: String,
    enum: ['active', 'discontinued', 'out of stock'],
    default: 'active'
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Inventory Transaction Schema
const inventoryTransactionSchema = new Schema({
  item: {
    type: Schema.Types.ObjectId,
    ref: 'InventoryItem',
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  type: {
    type: String,
    enum: ['purchase', 'use', 'adjustment', 'transfer', 'return', 'disposal', 'other'],
    required: true
  },
  quantity: {
    type: Number,
    required: true
  },
  unitCost: {
    type: Number,
    min: 0
  },
  totalCost: {
    type: Number,
    min: 0
  },
  source: {
    type: String,
    trim: true
  },
  destination: {
    type: String,
    trim: true
  },
  relatedTo: {
    type: {
      type: String,
      enum: ['animal', 'breeding', 'health', 'feeding', 'equipment', 'facility', 'staff', 'other'],
      default: 'other'
    },
    id: {
      type: Schema.Types.ObjectId,
      refPath: 'relatedTo.type'
    },
    name: {
      type: String,
      trim: true
    }
  },
  reference: {
    type: String,
    trim: true
  },
  notes: {
    type: String
  },
  performedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Supplier Schema
const supplierSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['feed', 'medication', 'equipment', 'general', 'other'],
    default: 'general'
  },
  contactPerson: {
    type: String,
    trim: true
  },
  phone: {
    type: String,
    trim: true
  },
  email: {
    type: String,
    trim: true
  },
  website: {
    type: String,
    trim: true
  },
  address: {
    street: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      trim: true
    },
    state: {
      type: String,
      trim: true
    },
    postalCode: {
      type: String,
      trim: true
    },
    country: {
      type: String,
      trim: true
    }
  },
  paymentTerms: {
    type: String,
    trim: true
  },
  taxId: {
    type: String,
    trim: true
  },
  notes: {
    type: String
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  items: [{
    type: Schema.Types.ObjectId,
    ref: 'InventoryItem'
  }],
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
inventoryItemSchema.index({ name: 1 });
inventoryItemSchema.index({ category: 1 });
inventoryItemSchema.index({ status: 1 });
inventoryItemSchema.index({ quantity: 1 });
inventoryItemSchema.index({ expirationDate: 1 });

inventoryTransactionSchema.index({ item: 1 });
inventoryTransactionSchema.index({ date: -1 });
inventoryTransactionSchema.index({ type: 1 });
inventoryTransactionSchema.index({ 'relatedTo.id': 1 });

supplierSchema.index({ name: 1 });
supplierSchema.index({ type: 1 });
supplierSchema.index({ status: 1 });

// Pre-save hook to update timestamps
inventoryItemSchema.pre('save', function(next) {
  this.updatedAt = Date.now();

  // Calculate total value
  if (this.quantity && this.unitCost) {
    this.totalValue = this.quantity * this.unitCost;
  }

  next();
});

inventoryTransactionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();

  // Calculate total cost
  if (this.quantity && this.unitCost) {
    this.totalCost = Math.abs(this.quantity) * this.unitCost;
  }

  next();
});

supplierSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const InventoryItem = mongoose.model('InventoryItem', inventoryItemSchema);
const InventoryTransaction = mongoose.model('InventoryTransaction', inventoryTransactionSchema);
// Check if the model already exists to prevent "Cannot overwrite model once compiled" error
const Supplier = mongoose.models.Supplier || mongoose.model('Supplier', supplierSchema);

module.exports = {
  InventoryItem,
  InventoryTransaction,
  Supplier
};
