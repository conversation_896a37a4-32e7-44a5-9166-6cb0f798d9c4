import { DashboardModule } from '../types/dashboard';
import { ROUTES } from '../constants/routes';

export const animalManagementModule: DashboardModule = {
  id: 'animal-management',
  title: 'Animal Management',
  description: 'Comprehensive livestock tracking and management system',
  icon: '🐄',
  path: ROUTES.ANIMALS,
  subModules: [
    {
      id: 'inventory',
      title: 'Livestock Inventory',
      description: 'Track and manage all animals',
      icon: '📋',
      path: `${ROUTES.ANIMALS}/inventory`,
      count: 5,
      status: 'Active animals'
    },
    {
      id: 'health',
      title: 'Health Records',
      description: 'Medical history and treatments',
      icon: '🏥',
      path: `${ROUTES.ANIMALS}/health`,
      count: 2,
      status: 'Pending checkups'
    },
    {
      id: 'breeding',
      title: 'Breeding Program',
      description: 'Breeding cycles and genealogy',
      icon: '🧬',
      path: `${ROUTES.ANIMALS}/breeding`,
      count: 3,
      status: 'Active breeding pairs'
    },
    {
      id: 'feeding',
      title: 'Feeding Management',
      description: 'Feed schedules and nutrition',
      icon: '🌾',
      path: `${ROUTES.ANIMALS}/feeding`,
      count: 4,
      status: 'Active feed plans'
    }
  ],
  stats: [
    {
      label: 'Total Livestock',
      value: 5,
      icon: '🐎',
      trend: {
        value: 12,
        isPositive: true
      }
    },
    {
      label: 'Health Status',
      value: '98%',
      icon: '💚',
      trend: {
        value: 3,
        isPositive: true
      }
    },
    {
      label: 'Feed Stock',
      value: '85%',
      icon: '🌾',
      trend: {
        value: 5,
        isPositive: false
      }
    },
    {
      label: 'Breeding Success',
      value: '92%',
      icon: '🎯',
      trend: {
        value: 8,
        isPositive: true
      }
    }
  ]
};

// Add other modules following the same pattern
export const feedManagementModule: DashboardModule = {
  id: 'feed-management',
  title: 'Feed Management',
  description: 'Feed inventory and nutrition planning',
  icon: '🌾',
  path: ROUTES.FEED,
  subModules: [
    {
      id: 'inventory',
      title: 'Feed Inventory',
      description: 'Track feed stock levels',
      icon: '📦',
      path: `${ROUTES.FEED}/inventory`,
      count: 4,
      status: 'Stock items'
    },
    // Add more submodules...
  ],
  stats: [
    {
      label: 'Total Feed Stock',
      value: '12.5 tons',
      icon: '📦',
      trend: {
        value: 2,
        isPositive: true
      }
    },
    // Add more stats...
  ]
};

// Continue with other modules...