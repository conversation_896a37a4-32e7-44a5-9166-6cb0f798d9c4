// This file provides mock implementations for Node.js modules

// Mock implementations for Node.js modules
const mockImplementations = {
  // Mock crypto module
  crypto: {
    randomBytes: (size) => {
      const array = new Uint8Array(size);
      if (typeof window !== 'undefined' && window.crypto) {
        window.crypto.getRandomValues(array);
      }
      return array;
    },
    createHash: () => ({
      update: () => ({
        digest: () => 'mock-hash'
      })
    })
  },

  // Mock fs module
  fs: {
    readFileSync: () => '',
    existsSync: () => false,
    readdirSync: () => []
  },

  // Mock http module
  http: {
    request: () => ({
      on: () => {},
      end: () => {}
    })
  },

  // Mock stream module
  stream: {
    Readable: class {
      pipe() { return this; }
      on() { return this; }
    },
    Writable: class {
      write() { return true; }
      end() {}
    }
  },

  // Mock timers module
  timers: {
    setTimeout,
    clearTimeout,
    setInterval,
    clearInterval
  },

  // Mock zlib module
  zlib: {
    createGzip: () => ({
      on: () => {},
      pipe: () => {}
    }),
    createGunzip: () => ({
      on: () => {},
      pipe: () => {}
    })
  },

  // Mock dns module
  dns: {
    lookup: (hostname, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
      }
      callback(null, '127.0.0.1', 4);
    },
    resolve: (hostname, callback) => {
      callback(null, ['127.0.0.1']);
    }
  },

  // Mock net module
  net: {
    connect: () => ({
      on: () => {},
      end: () => {}
    }),
    Socket: class {
      connect() { return this; }
      on() { return this; }
      end() {}
    }
  },

  // Mock tls module
  tls: {
    connect: () => ({
      on: () => {},
      end: () => {}
    }),
    TLSSocket: class {
      connect() { return this; }
      on() { return this; }
      end() {}
    }
  },

  // Mock child_process module
  child_process: {
    spawn: () => ({
      on: () => {},
      stdout: { on: () => {} },
      stderr: { on: () => {} }
    }),
    exec: (cmd, callback) => {
      callback(null, '', '');
    }
  },

  // Mock fs/promises module
  'fs/promises': {
    readFile: () => Promise.resolve(''),
    writeFile: () => Promise.resolve()
  },

  // Mock timers/promises module
  'timers/promises': {
    setTimeout: (ms) => Promise.resolve()
  },

  // Mock querystring module
  querystring: {
    stringify: (obj) => {
      return Object.entries(obj)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
    },
    parse: (str) => {
      return str.split('&').reduce((acc, pair) => {
        const [key, value] = pair.split('=');
        acc[key] = value;
        return acc;
      }, {});
    }
  },

  // Mock os module
  os: {
    platform: () => 'browser',
    type: () => 'Browser',
    release: () => '1.0.0',
    hostname: () => 'browser-host'
  }
};

// Export the mock implementations
module.exports = mockImplementations;
