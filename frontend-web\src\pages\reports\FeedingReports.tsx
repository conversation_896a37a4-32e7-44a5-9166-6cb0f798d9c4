import React, { useState, useRef } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Card, CardContent, Typography, Grid, FormControl, InputLabel, Select, MenuItem, TextField, Divider, List, ListItem, ListItemIcon, ListItemText, ListItemSecondaryAction, IconButton, Paper, CircularProgress, Alert, Snackbar, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, LinearProgress } from '@mui/material';
import {
  Description,
  Download,
  PictureAsPdf,
  TableChart,
  Code,
  FilterList,
  DateRange,
  Bar<PERSON>hart as BarChartIcon,
  PieChart as PieChartIcon,
  Timeline,
  Inventory,
  LocalShipping,
  Grass,
  Restaurant,
  Schedule,
  Science,
  Print
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import { generateReport, ReportFormat, ReportParams, generateHtmlReport } from '../../services/reportService';
import HtmlReportViewer from '../../components/reports/HtmlReportViewer';
import { mockFeedSchedules, mockNutritionPlans } from './mockFeedingData';
import { useTheme } from '@mui/material/styles';
import { useResponsive } from '../../hooks/useResponsive';
import { getCardStyle, getTableHeaderStyle, getTableRowStyle, getChipStyle } from '../../utils/cardStyles';

const reportTypes = [
  {
    name: 'Feed Consumption Analysis',
    description: 'Detailed breakdown of feed consumption by animal group and feed type',
    lastGenerated: '2024-04-03',
    icon: <Grass />
  },
  {
    name: 'Feed Inventory Status',
    description: 'Current inventory levels, usage trends, and reorder recommendations',
    lastGenerated: '2024-04-02',
    icon: <Inventory />
  },
  {
    name: 'Feed Cost Analysis',
    description: 'Financial analysis of feed costs by type, group, and time period',
    lastGenerated: '2024-04-01',
    icon: <BarChartIcon />
  },
  {
    name: 'Feed Supplier Performance',
    description: 'Evaluation of suppliers based on cost, quality, and delivery',
    lastGenerated: '2024-03-28',
    icon: <LocalShipping />
  }
];

const FeedingReports: React.FC = () => {
  const [selectedReportType, setSelectedReportType] = useState<string>('consumption');
  const [reportFormat, setReportFormat] = useState<ReportFormat>('pdf');
  const [timePeriod, setTimePeriod] = useState<string>('month');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [showReportViewer, setShowReportViewer] = useState<boolean>(false);
  const theme = useTheme();
  const isMobile = useResponsive('down', 'sm');
  const [snackbar, setSnackbar] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  const handleGenerateReport = async () => {
    try {
      setLoading(true);

      const params: ReportParams = {
        type: 'analysis',
        format: reportFormat,
        timePeriod: timePeriod as any,
        startDate: startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
        endDate: endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
        filters: {
          reportType: selectedReportType
        }
      };

      // If HTML format is selected, show the preview
      if (reportFormat === 'html') {
        const html = await generateHtmlReport(params);
        setHtmlContent(html);
        setShowReportViewer(true);
      } else {
        // For other formats, generate and download
        await generateReport(params);
      }

      setSnackbar({
        open: true,
        message: 'Report generated successfully',
        severity: 'success'
      });
    } catch (error) {
      console.error('Error generating report:', error);
      setSnackbar({
        open: true,
        message: 'Failed to generate report',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseReportViewer = () => {
    setShowReportViewer(false);
  };

  const handleDownloadHtmlReport = () => {
    if (htmlContent) {
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `feeding_${selectedReportType}_report.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Feeding Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate comprehensive reports on feed consumption, inventory, and costs
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {/* Report Generator */}
          <Grid item xs={12} md={8}>
            <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Generate Report
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 3 }}>
                      <InputLabel>Report Type</InputLabel>
                      <Select
                        label="Report Type"
                        value={selectedReportType}
                        onChange={(e) => setSelectedReportType(e.target.value)}
                      >
                        <MenuItem value="consumption">Feed Consumption Analysis</MenuItem>
                        <MenuItem value="inventory">Feed Inventory Status</MenuItem>
                        <MenuItem value="cost">Feed Cost Analysis</MenuItem>
                        <MenuItem value="supplier">Feed Supplier Performance</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 3 }}>
                      <InputLabel>Report Format</InputLabel>
                      <Select
                        label="Report Format"
                        value={reportFormat}
                        onChange={(e) => setReportFormat(e.target.value as ReportFormat)}
                      >
                        <MenuItem value="pdf">PDF</MenuItem>
                        <MenuItem value="excel">Excel</MenuItem>
                        <MenuItem value="csv">CSV</MenuItem>
                        <MenuItem value="html">HTML</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth variant="outlined" size="small" sx={{ mb: 3 }}>
                      <InputLabel>Time Period</InputLabel>
                      <Select
                        label="Time Period"
                        value={timePeriod}
                        onChange={(e) => setTimePeriod(e.target.value)}
                      >
                        <MenuItem value="week">Last Week</MenuItem>
                        <MenuItem value="month">Last Month</MenuItem>
                        <MenuItem value="quarter">Last Quarter</MenuItem>
                        <MenuItem value="year">Last Year</MenuItem>
                        <MenuItem value="custom">Custom Range</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <DatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={(date) => setStartDate(date)}
                      disabled={timePeriod !== 'custom'}
                      slotProps={{ textField: { size: 'small', fullWidth: true, sx: { mb: 3 } } }}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <DatePicker
                      label="End Date"
                      value={endDate}
                      onChange={(date) => setEndDate(date)}
                      disabled={timePeriod !== 'custom'}
                      slotProps={{ textField: { size: 'small', fullWidth: true, sx: { mb: 3 } } }}
                    />
                  </Grid>
                </Grid>

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <CustomButton
                    variant="contained"
                    color="primary"
                    onClick={handleGenerateReport}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <Description />}
                  >
                    {loading ? 'Generating...' : 'Generate Report'}
                  </CustomButton>
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Report Preview
                </Typography>

                {selectedReportType === 'consumption' ? (
                  <Card sx={{ ...getCardStyle('feed', theme), height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" fontWeight="bold" color="white" gutterBottom>
                        Feed Consumption Preview
                      </Typography>
                      <Box sx={{ mt: 2, overflow: 'auto' }}>
                        <TableContainer component={Paper} sx={{
                          boxShadow: 'none',
                          borderRadius: 2,
                          overflow: 'auto',
                          maxWidth: '100%',
                          bgcolor: 'transparent'
                        }}>
                          <Table>
                            <TableHead sx={{ ...getTableHeaderStyle('feed', theme) }}>
                              <TableRow>
                                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Group</TableCell>
                                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Feed Type</TableCell>
                                <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Quantity</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {mockFeedSchedules.slice(0, 3).map((schedule) => (
                                <TableRow key={schedule.id} hover sx={{ ...getTableRowStyle('feed', theme) }}>
                                  <TableCell sx={{ color: 'white' }}>{schedule.groupName}</TableCell>
                                  <TableCell sx={{ color: 'white' }}>{schedule.feedType}</TableCell>
                                  <TableCell sx={{ color: 'white' }}>
                                    {schedule.quantity} {schedule.unit}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </Box>
                    </CardContent>
                  </Card>
                ) : (
                  <Paper
                    sx={{
                      height: 400,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#f5f5f5',
                      border: '1px dashed #ccc',
                      borderRadius: 2
                    }}
                  >
                    <Box sx={{ textAlign: 'center' }}>
                      <Description sx={{ fontSize: 60, color: '#bdbdbd', mb: 2 }} />
                      <Typography variant="body1" color="text.secondary">
                        Generate a report to see a preview here
                      </Typography>
                    </Box>
                  </Paper>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Report Types */}
          <Grid item xs={12} md={4}>
            <Card sx={{ borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Available Reports
                </Typography>

                <List>
                  {reportTypes.map((report, index) => (
                    <React.Fragment key={report.name}>
                      {index > 0 && <Divider />}
                      <ListItem
                        button
                        onClick={() => setSelectedReportType(report.name.toLowerCase().split(' ')[0])}
                        sx={{ py: 2 }}
                      >
                        <ListItemIcon>{report.icon}</ListItemIcon>
                        <ListItemText
                          primary={report.name}
                          secondary={
                            <>
                              {report.description}
                              <br />
                              <Typography variant="caption" color="text.secondary">
                                Last generated: {report.lastGenerated}
                              </Typography>
                            </>
                          }
                        />
                        <ListItemSecondaryAction>
                          <IconButton edge="end" aria-label="download">
                            <Download />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>

            <Card sx={{ mt: 4, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Export Options
                </Typography>

                <List>
                  <ListItem button onClick={() => setReportFormat('pdf')}>
                    <ListItemIcon>
                      <PictureAsPdf />
                    </ListItemIcon>
                    <ListItemText primary="PDF Document" secondary="High-quality printable document" />
                  </ListItem>

                  <Divider />

                  <ListItem button onClick={() => setReportFormat('excel')}>
                    <ListItemIcon>
                      <TableChart />
                    </ListItemIcon>
                    <ListItemText primary="Excel Spreadsheet" secondary="Editable spreadsheet with formulas" />
                  </ListItem>

                  <Divider />

                  <ListItem button onClick={() => setReportFormat('csv')}>
                    <ListItemIcon>
                      <Code />
                    </ListItemIcon>
                    <ListItemText primary="CSV File" secondary="Raw data for further processing" />
                  </ListItem>

                  <Divider />

                  <ListItem button onClick={() => setReportFormat('html')}>
                    <ListItemIcon>
                      <Description />
                    </ListItemIcon>
                    <ListItemText primary="HTML Report" secondary="Web-friendly interactive report" />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>

        {/* HTML Report Viewer */}
        {showReportViewer && (
          <HtmlReportViewer
            htmlContent={htmlContent}
            title={`${selectedReportType.charAt(0).toUpperCase() + selectedReportType.slice(1)} Report`}
            onClose={handleCloseReportViewer}
            onDownload={handleDownloadHtmlReport}
            open={showReportViewer}
          />
        )}
      </motion.div>
    </LocalizationProvider>
  );
};

export default FeedingReports;
