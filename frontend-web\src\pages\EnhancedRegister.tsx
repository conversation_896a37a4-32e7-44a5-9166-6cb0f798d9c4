import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Card,
  CardContent,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  Chip,
  Alert,
  Divider,
  useTheme,
  alpha,
  Paper,
  Avatar,
  IconButton
} from '@mui/material';
import {
  Agriculture,
  Person,
  Business,
  Payment,
  CheckCircle,
  ArrowBack,
  ArrowForward,
  Star,
  Security,
  Support,
  CloudSync
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LanguageSelector from '../components/LanguageSelector';

interface RegistrationData {
  // Personal Info
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  
  // Farm Info
  farmName: string;
  farmSize: string;
  province: string;
  animalTypes: string[];
  
  // Subscription
  selectedPlan: string;
  paymentMethod: string;
}

const EnhancedRegister: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { register } = useAuth();
  
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState<RegistrationData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    farmName: '',
    farmSize: '',
    province: '',
    animalTypes: [],
    selectedPlan: location.state?.selectedPlan || 'Professional',
    paymentMethod: ''
  });

  const steps = ['Personal Info', 'Farm Details', 'Subscription', 'Payment'];

  const subscriptionPlans = [
    {
      name: "Beta Access",
      price: "Free",
      duration: "30 Days",
      description: "Perfect for trying out our platform",
      features: [
        "Up to 50 animals",
        "Basic health tracking",
        "Simple reports",
        "Email support"
      ],
      color: "#4CAF50",
      popular: false
    },
    {
      name: "Professional",
      price: "R299",
      duration: "per month",
      description: "Ideal for medium-sized farms",
      features: [
        "Up to 500 animals",
        "Advanced analytics",
        "Custom reports",
        "Priority support",
        "Mobile app access",
        "Data export"
      ],
      color: "#2196F3",
      popular: true
    },
    {
      name: "Enterprise",
      price: "R599",
      duration: "per month",
      description: "For large commercial operations",
      features: [
        "Unlimited animals",
        "AI insights",
        "Custom integrations",
        "24/7 support",
        "Multi-farm management",
        "Advanced compliance"
      ],
      color: "#FF9800",
      popular: false
    }
  ];

  const provinces = [
    'Eastern Cape', 'Free State', 'Gauteng', 'KwaZulu-Natal',
    'Limpopo', 'Mpumalanga', 'Northern Cape', 'North West', 'Western Cape'
  ];

  const animalTypeOptions = [
    'Cattle', 'Sheep', 'Goats', 'Pigs', 'Poultry', 'Horses', 'Other'
  ];

  const farmSizeOptions = [
    'Small (1-50 animals)',
    'Medium (51-200 animals)',
    'Large (201-500 animals)',
    'Enterprise (500+ animals)'
  ];

  const handleNext = () => {
    if (validateStep()) {
      setActiveStep((prev) => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => prev - 1);
  };

  const validateStep = () => {
    switch (activeStep) {
      case 0:
        return formData.firstName && formData.lastName && formData.email && 
               formData.password && formData.password === formData.confirmPassword;
      case 1:
        return formData.farmName && formData.farmSize && formData.province && 
               formData.animalTypes.length > 0;
      case 2:
        return formData.selectedPlan;
      case 3:
        return formData.selectedPlan === 'Beta Access' || formData.paymentMethod;
      default:
        return true;
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    
    try {
      await register({
        username: formData.email,
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        farmName: formData.farmName,
        farmSize: formData.farmSize,
        province: formData.province,
        animalTypes: formData.animalTypes,
        subscriptionPlan: formData.selectedPlan,
        paymentMethod: formData.paymentMethod
      });
      
      // Redirect based on plan
      if (formData.selectedPlan === 'Beta Access') {
        navigate('/beta');
      } else {
        navigate('/dashboard');
      }
    } catch (err: any) {
      setError(err.message || 'Registration failed');
    } finally {
      setLoading(false);
    }
  };

  const renderPersonalInfo = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
    >
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="First Name"
            value={formData.firstName}
            onChange={(e) => setFormData({...formData, firstName: e.target.value})}
            required
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Last Name"
            value={formData.lastName}
            onChange={(e) => setFormData({...formData, lastName: e.target.value})}
            required
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({...formData, email: e.target.value})}
            required
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Password"
            type="password"
            value={formData.password}
            onChange={(e) => setFormData({...formData, password: e.target.value})}
            required
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Confirm Password"
            type="password"
            value={formData.confirmPassword}
            onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
            error={formData.confirmPassword !== '' && formData.password !== formData.confirmPassword}
            helperText={formData.confirmPassword !== '' && formData.password !== formData.confirmPassword ? 'Passwords do not match' : ''}
            required
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Phone Number"
            value={formData.phone}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
          />
        </Grid>
      </Grid>
    </motion.div>
  );

  const renderFarmDetails = () => (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
    >
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Farm Name"
            value={formData.farmName}
            onChange={(e) => setFormData({...formData, farmName: e.target.value})}
            required
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required>
            <InputLabel>Farm Size</InputLabel>
            <Select
              value={formData.farmSize}
              onChange={(e) => setFormData({...formData, farmSize: e.target.value})}
              label="Farm Size"
            >
              {farmSizeOptions.map((size) => (
                <MenuItem key={size} value={size}>{size}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth required>
            <InputLabel>Province</InputLabel>
            <Select
              value={formData.province}
              onChange={(e) => setFormData({...formData, province: e.target.value})}
              label="Province"
            >
              {provinces.map((province) => (
                <MenuItem key={province} value={province}>{province}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <Typography variant="subtitle1" sx={{ mb: 2 }}>
            Animal Types (Select all that apply)
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {animalTypeOptions.map((type) => (
              <Chip
                key={type}
                label={type}
                clickable
                color={formData.animalTypes.includes(type) ? "primary" : "default"}
                onClick={() => {
                  const newTypes = formData.animalTypes.includes(type)
                    ? formData.animalTypes.filter(t => t !== type)
                    : [...formData.animalTypes, type];
                  setFormData({...formData, animalTypes: newTypes});
                }}
                sx={{
                  '&:hover': {
                    transform: 'scale(1.05)'
                  }
                }}
              />
            ))}
          </Box>
        </Grid>
      </Grid>
    </motion.div>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        py: 4
      }}
    >
      <Container maxWidth="md">
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
          <IconButton onClick={() => navigate('/')} sx={{ color: 'white', mr: 2 }}>
            <ArrowBack />
          </IconButton>
          <Agriculture sx={{ color: 'white', mr: 1 }} />
          <Typography variant="h5" sx={{ color: 'white', fontWeight: 'bold', flexGrow: 1 }}>
            AgriIntel Registration
          </Typography>
          <LanguageSelector />
        </Box>

        {/* Main Registration Card */}
        <Paper
          elevation={24}
          sx={{
            borderRadius: 4,
            overflow: 'hidden',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)'
          }}
        >
          {/* Progress Stepper */}
          <Box sx={{ p: 4, pb: 2 }}>
            <Stepper activeStep={activeStep} alternativeLabel>
              {steps.map((label, index) => (
                <Step key={label}>
                  <StepLabel
                    StepIconProps={{
                      sx: {
                        '&.Mui-completed': {
                          color: theme.palette.success.main
                        },
                        '&.Mui-active': {
                          color: theme.palette.primary.main
                        }
                      }
                    }}
                  >
                    {label}
                  </StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>

          <Divider />

          {/* Form Content */}
          <Box sx={{ p: 4 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <AnimatePresence mode="wait">
              {activeStep === 0 && renderPersonalInfo()}
              {activeStep === 1 && renderFarmDetails()}
              {activeStep === 2 && (
                <motion.div
                  key="subscription"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                >
                  <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
                    Choose Your Subscription Plan
                  </Typography>
                  <Grid container spacing={3}>
                    {subscriptionPlans.map((plan) => (
                      <Grid item xs={12} md={4} key={plan.name}>
                        <Card
                          sx={{
                            cursor: 'pointer',
                            border: formData.selectedPlan === plan.name ? `3px solid ${plan.color}` : '1px solid',
                            borderColor: formData.selectedPlan === plan.name ? plan.color : 'divider',
                            transform: formData.selectedPlan === plan.name ? 'scale(1.02)' : 'scale(1)',
                            transition: 'all 0.3s ease',
                            position: 'relative'
                          }}
                          onClick={() => setFormData({...formData, selectedPlan: plan.name})}
                        >
                          {plan.popular && (
                            <Chip
                              label="Popular"
                              size="small"
                              sx={{
                                position: 'absolute',
                                top: -8,
                                right: 16,
                                background: plan.color,
                                color: 'white'
                              }}
                            />
                          )}
                          <CardContent sx={{ textAlign: 'center', p: 3 }}>
                            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
                              {plan.name}
                            </Typography>
                            <Typography variant="h4" sx={{ color: plan.color, fontWeight: 'bold', mb: 1 }}>
                              {plan.price}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                              {plan.duration}
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 2 }}>
                              {plan.description}
                            </Typography>
                            <Box sx={{ textAlign: 'left' }}>
                              {plan.features.slice(0, 3).map((feature, index) => (
                                <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                                  <CheckCircle sx={{ color: plan.color, fontSize: 16, mr: 1 }} />
                                  <Typography variant="body2">{feature}</Typography>
                                </Box>
                              ))}
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0}
                startIcon={<ArrowBack />}
              >
                Back
              </Button>
              
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={loading || !validateStep()}
                  sx={{
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                    px: 4
                  }}
                >
                  {loading ? 'Creating Account...' : 'Complete Registration'}
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={!validateStep()}
                  endIcon={<ArrowForward />}
                  sx={{
                    background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)'
                  }}
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>
        </Paper>

        {/* Login Link */}
        <Box sx={{ textAlign: 'center', mt: 3 }}>
          <Typography variant="body2" sx={{ color: 'white' }}>
            Already have an account?{' '}
            <Button
              onClick={() => navigate('/login')}
              sx={{ color: 'white', textDecoration: 'underline' }}
            >
              Sign In
            </Button>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default EnhancedRegister;
