import React, { useState, useEffect } from 'react';
import { CustomButton } from '../common';
import { Box, Grid, Typography, Card, CardContent, CardMedia, CardActionArea, Chip, TextField, InputAdornment, FormControl, InputLabel, Select, MenuItem, Divider, Rating, Avatar, useTheme, alpha, CircularProgress, Skeleton, Paper, IconButton, Tooltip, Link } from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';
import { motion } from 'framer-motion';
import {
  Search,
  FilterList,
  LocationOn,
  Phone,
  Email,
  Language,
  ArrowForward,
  Refresh,
  Category,
  Business
} from '@mui/icons-material';
import { useSuppliers } from '../../contexts/SupplierContext';
import { Supplier, SupplierProductCategory } from '../../types/commercial/suppliers';
import { getCategoryColor, getCategoryDisplayName } from '../../utils/supplierUtils';

interface SuppliersDirectoryProps {
  onSupplierSelect?: (supplierId: string) => void;
}

const SuppliersDirectory: React.FC<SuppliersDirectoryProps> = ({ onSupplierSelect }) => {
  const theme = useTheme();
  const {
    suppliers,
    loading,
    error,
    filter,
    setFilter,
    refreshSuppliers
  } = useSuppliers();

  const [searchQuery, setSearchQuery] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle category filter change
  const handleCategoryFilterChange = (event: SelectChangeEvent) => {
    setCategoryFilter(event.target.value);
  };

  // Apply filters when search or category changes
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilter({
        ...filter,
        search: searchQuery,
        categories: categoryFilter !== 'all' ? [categoryFilter as SupplierProductCategory] : undefined
      });
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery, categoryFilter]);

  // Handle manual refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshSuppliers();
    setIsRefreshing(false);
  };

  // Handle supplier selection
  const handleSupplierClick = (supplierId: string) => {
    if (onSupplierSelect) {
      onSupplierSelect(supplierId);
    }
  };

  // Get primary contact of a specific type
  const getPrimaryContact = (supplier: Supplier, type: 'phone' | 'email' | 'website') => {
    const contact = supplier.contacts?.find(c => c.type === type);
    return contact ? contact.value : '';
  };

  // Get primary location
  const getPrimaryLocation = (supplier: Supplier) => {
    return supplier.locations && supplier.locations.length > 0
      ? supplier.locations[0].address
      : '';
  };

  return (
    <Box>
      {/* Search and Filter */}
      <Box sx={{ mb: 3, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2 }}>
        <TextField
          placeholder="Search suppliers..."
          variant="outlined"
          fullWidth
          size="small"
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
          sx={{ flex: 1 }}
        />

        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel id="category-filter-label">Category</InputLabel>
          <Select
            labelId="category-filter-label"
            value={categoryFilter}
            label="Category"
            onChange={handleCategoryFilterChange}
          >
            <MenuItem value="all">All Categories</MenuItem>
            <MenuItem value="livestock-feed">Livestock Feed</MenuItem>
            <MenuItem value="equipment">Equipment</MenuItem>
            <MenuItem value="animal-health">Animal Health</MenuItem>
            <MenuItem value="fertilizer">Fertilizer</MenuItem>
            <MenuItem value="chemicals">Chemicals</MenuItem>
            <MenuItem value="irrigation">Irrigation</MenuItem>
            <MenuItem value="seeds">Seeds</MenuItem>
            <MenuItem value="financial">Financial</MenuItem>
            <MenuItem value="insurance">Insurance</MenuItem>
            <MenuItem value="retail">Retail</MenuItem>
            <MenuItem value="other">Other</MenuItem>
          </Select>
        </FormControl>

        <Tooltip title="Refresh supplier data">
          <CustomButton
            variant="outlined"
            color="primary"
            startIcon={isRefreshing ? <CircularProgress size={20} /> : <Refresh />}
            onClick={handleRefresh}
            disabled={isRefreshing}
            sx={{ minWidth: 120 }}
          >
            Refresh
          </CustomButton>
        </Tooltip>
      </Box>

      {/* Loading State */}
      {loading && (
        <Grid container spacing={3}>
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item}>
              <Card sx={{ height: '100%', borderRadius: 2, boxShadow: '0 4px 12px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
                    <Box>
                      <Skeleton variant="text" width={120} height={24} />
                      <Skeleton variant="text" width={80} height={20} />
                    </Box>
                  </Box>
                  <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 1, mb: 2 }} />
                  <Skeleton variant="text" width="90%" />
                  <Skeleton variant="text" width="70%" />
                  <Box sx={{ mt: 2 }}>
                    <Skeleton variant="text" width={100} height={24} />
                    <Skeleton variant="text" width="100%" height={20} />
                    <Skeleton variant="text" width="100%" height={20} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Error State */}
      {error && !loading && (
        <Paper
          sx={{
            p: 3,
            textAlign: 'center',
            borderRadius: 2,
            bgcolor: alpha(theme.palette.error.light, 0.1),
            border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
          }}
        >
          <Typography variant="h6" color="error" gutterBottom>
            Error Loading Suppliers
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            {error}
          </Typography>
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<Refresh />}
            onClick={handleRefresh}
          >
            Try Again
          </CustomButton>
        </Paper>
      )}

      {/* Supplier Cards */}
      {!loading && !error && (
        <Grid container spacing={3}>
          {suppliers.length > 0 ? (
            suppliers.map((supplier, index) => (
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
                key={supplier.id}
                component={motion.div}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card
                  sx={{
                    height: '100%',
                    borderRadius: 2,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 20px rgba(0,0,0,0.12)'
                    }
                  }}
                >
                  <CardActionArea onClick={() => handleSupplierClick(supplier.id)}>
                    <Box sx={{ position: 'relative' }}>
                      <CardMedia
                        component="img"
                        height="140"
                        image={supplier.coverImage || 'https://source.unsplash.com/random/?farm,agriculture'}
                        alt={supplier.name}
                        sx={{ objectFit: 'cover' }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          background: `linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.7))`,
                        }}
                      />
                      <Avatar
                        src={supplier.logo}
                        alt={supplier.name}
                        sx={{
                          position: 'absolute',
                          bottom: -24,
                          left: 16,
                          width: 48,
                          height: 48,
                          border: `2px solid ${theme.palette.background.paper}`,
                          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                          bgcolor: 'white'
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          bottom: 8,
                          right: 8,
                          display: 'flex',
                          gap: 0.5
                        }}
                      >
                        {supplier.categories?.slice(0, 3).map((category, i) => (
                          <Chip
                            key={i}
                            label={getCategoryDisplayName(category)}
                            size="small"
                            sx={{
                              bgcolor: alpha(getCategoryColor(category, theme), 0.9),
                              color: 'white',
                              fontWeight: 'medium',
                              fontSize: '0.7rem'
                            }}
                          />
                        ))}
                        {supplier.categories && supplier.categories.length > 3 && (
                          <Chip
                            label={`+${supplier.categories.length - 3}`}
                            size="small"
                            sx={{
                              bgcolor: alpha(theme.palette.grey[700], 0.9),
                              color: 'white',
                              fontWeight: 'medium',
                              fontSize: '0.7rem'
                            }}
                          />
                        )}
                      </Box>
                    </Box>

                    <CardContent sx={{ pt: 4 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                        <Typography variant="h6" fontWeight="bold" sx={{ ml: 7 }}>
                          {supplier.name}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Rating value={supplier.rating || 0} precision={0.5} size="small" readOnly />
                          <Typography variant="body2" sx={{ ml: 0.5 }}>
                            ({supplier.rating || 0})
                          </Typography>
                        </Box>
                      </Box>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          height: '40px'
                        }}
                      >
                        {supplier.shortDescription || supplier.description}
                      </Typography>

                      <Divider sx={{ my: 1.5 }} />

                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        {getPrimaryLocation(supplier) && (
                          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                            <LocationOn fontSize="small" color="action" sx={{ mt: 0.3 }} />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 1,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {getPrimaryLocation(supplier)}
                            </Typography>
                          </Box>
                        )}

                        {getPrimaryContact(supplier, 'phone') && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Phone fontSize="small" color="action" />
                            <Typography variant="body2" color="text.secondary">
                              {getPrimaryContact(supplier, 'phone')}
                            </Typography>
                          </Box>
                        )}

                        {getPrimaryContact(supplier, 'email') && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Email fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 1,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {getPrimaryContact(supplier, 'email')}
                            </Typography>
                          </Box>
                        )}

                        {supplier.website && (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Language fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 1,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden'
                              }}
                            >
                              {supplier.website.replace(/^https?:\/\//, '')}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                    </CardContent>
                  </CardActionArea>
                </Card>
              </Grid>
            ))
          ) : (
            <Grid item xs={12}>
              <Paper
                sx={{
                  p: 3,
                  textAlign: 'center',
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.info.light, 0.1),
                  border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                }}
              >
                <Business sx={{ fontSize: 48, color: alpha(theme.palette.info.main, 0.5), mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No Suppliers Found
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  No suppliers match your current search criteria. Try adjusting your filters or search query.
                </Typography>
                <CustomButton
                  variant="outlined"
                  color="primary"
                  startIcon={<FilterList />}
                  onClick={() => {
                    setSearchQuery('');
                    setCategoryFilter('all');
                  }}
                >
                  Clear Filters
                </CustomButton>
              </Paper>
            </Grid>
          )}
        </Grid>
      )}
    </Box>
  );
};

export default SuppliersDirectory;
