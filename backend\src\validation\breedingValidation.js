/**
 * Breeding Validation Schemas
 * 
 * This module provides validation schemas for breeding-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// Breeding record statuses
const breedingStatuses = [
  'pending', 
  'successful', 
  'unsuccessful', 
  'confirmed_pregnant', 
  'not_pregnant', 
  'aborted', 
  'completed', 
  'cancelled'
];

// Birth record statuses
const birthStatuses = [
  'live', 
  'stillborn', 
  'complications', 
  'assisted', 
  'caesarean', 
  'other'
];

// Create breeding record validation schema
const createBreedingRecordSchema = Joi.object({
  femaleId: schemas.id,
  maleId: schemas.id,
  breedingDate: Joi.date().iso().required().messages({
    'date.base': 'Breeding date must be a valid date',
    'date.format': 'Breeding date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Breeding date is required'
  }),
  method: Joi.string().trim().valid('natural', 'artificial_insemination', 'other').required().messages({
    'any.only': 'Method must be one of: natural, artificial_insemination, other',
    'any.required': 'Method is required'
  }),
  status: Joi.string().valid(...breedingStatuses).default('pending').messages({
    'any.only': `Status must be one of: ${breedingStatuses.join(', ')}`
  }),
  expectedDueDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Expected due date must be a valid date',
    'date.format': 'Expected due date must be in ISO format (YYYY-MM-DD)'
  }),
  pregnancyCheckDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Pregnancy check date must be a valid date',
    'date.format': 'Pregnancy check date must be in ISO format (YYYY-MM-DD)'
  }),
  pregnancyCheckResult: Joi.string().trim().valid('positive', 'negative', 'inconclusive', 'not_checked').allow('').default('not_checked').messages({
    'any.only': 'Pregnancy check result must be one of: positive, negative, inconclusive, not_checked'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  performedBy: Joi.string().trim().allow('').messages({
    'string.base': 'Performed by must be a string'
  }),
  cost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Cost must be a number',
    'number.min': 'Cost cannot be negative'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

// Update breeding record validation schema
const updateBreedingRecordSchema = Joi.object({
  femaleId: schemas.id,
  maleId: schemas.id,
  breedingDate: Joi.date().iso().messages({
    'date.base': 'Breeding date must be a valid date',
    'date.format': 'Breeding date must be in ISO format (YYYY-MM-DD)'
  }),
  method: Joi.string().trim().valid('natural', 'artificial_insemination', 'other').messages({
    'any.only': 'Method must be one of: natural, artificial_insemination, other'
  }),
  status: Joi.string().valid(...breedingStatuses).messages({
    'any.only': `Status must be one of: ${breedingStatuses.join(', ')}`
  }),
  expectedDueDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Expected due date must be a valid date',
    'date.format': 'Expected due date must be in ISO format (YYYY-MM-DD)'
  }),
  pregnancyCheckDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Pregnancy check date must be a valid date',
    'date.format': 'Pregnancy check date must be in ISO format (YYYY-MM-DD)'
  }),
  pregnancyCheckResult: Joi.string().trim().valid('positive', 'negative', 'inconclusive', 'not_checked').allow('').messages({
    'any.only': 'Pregnancy check result must be one of: positive, negative, inconclusive, not_checked'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  performedBy: Joi.string().trim().allow('').messages({
    'string.base': 'Performed by must be a string'
  }),
  cost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Cost must be a number',
    'number.min': 'Cost cannot be negative'
  }),
  metadata: Joi.object().messages({
    'object.base': 'Metadata must be an object'
  })
});

// Get breeding records query validation schema
const getBreedingRecordsQuerySchema = Joi.object({
  femaleId: Joi.string().trim().messages({
    'string.base': 'Female ID must be a string'
  }),
  maleId: Joi.string().trim().messages({
    'string.base': 'Male ID must be a string'
  }),
  status: Joi.string().valid(...breedingStatuses).messages({
    'any.only': `Status must be one of: ${breedingStatuses.join(', ')}`
  }),
  startDate: Joi.date().iso().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
  }),
  endDate: Joi.date().iso().messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format (YYYY-MM-DD)'
  }),
  method: Joi.string().trim().valid('natural', 'artificial_insemination', 'other').messages({
    'any.only': 'Method must be one of: natural, artificial_insemination, other'
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  })
}).concat(schemas.pagination);

// Breeding record ID parameter validation schema
const breedingRecordIdParamSchema = Joi.object({
  id: schemas.id
});

// Create birth record validation schema
const createBirthRecordSchema = Joi.object({
  breedingRecordId: schemas.id,
  birthDate: Joi.date().iso().required().messages({
    'date.base': 'Birth date must be a valid date',
    'date.format': 'Birth date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Birth date is required'
  }),
  numberOfOffspring: Joi.number().integer().min(1).required().messages({
    'number.base': 'Number of offspring must be a number',
    'number.integer': 'Number of offspring must be an integer',
    'number.min': 'Number of offspring must be at least 1',
    'any.required': 'Number of offspring is required'
  }),
  numberOfLive: Joi.number().integer().min(0).default(0).messages({
    'number.base': 'Number of live births must be a number',
    'number.integer': 'Number of live births must be an integer',
    'number.min': 'Number of live births cannot be negative'
  }),
  numberOfStillborn: Joi.number().integer().min(0).default(0).messages({
    'number.base': 'Number of stillborn must be a number',
    'number.integer': 'Number of stillborn must be an integer',
    'number.min': 'Number of stillborn cannot be negative'
  }),
  status: Joi.string().valid(...birthStatuses).default('live').messages({
    'any.only': `Status must be one of: ${birthStatuses.join(', ')}`
  }),
  assistedBy: Joi.string().trim().allow('').messages({
    'string.base': 'Assisted by must be a string'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  offspring: Joi.array().items(Joi.object({
    tagNumber: Joi.string().trim().required().messages({
      'string.empty': 'Tag number cannot be empty',
      'any.required': 'Tag number is required'
    }),
    gender: Joi.string().valid('male', 'female', 'unknown').required().messages({
      'any.only': 'Gender must be one of: male, female, unknown',
      'any.required': 'Gender is required'
    }),
    birthWeight: Joi.number().positive().allow(null).messages({
      'number.base': 'Birth weight must be a number',
      'number.positive': 'Birth weight must be positive'
    }),
    status: Joi.string().valid('live', 'stillborn', 'deceased').required().messages({
      'any.only': 'Status must be one of: live, stillborn, deceased',
      'any.required': 'Status is required'
    }),
    notes: Joi.string().trim().allow('').messages({
      'string.base': 'Notes must be a string'
    })
  })).default([]).messages({
    'array.base': 'Offspring must be an array'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

module.exports = {
  createBreedingRecordSchema,
  updateBreedingRecordSchema,
  getBreedingRecordsQuerySchema,
  breedingRecordIdParamSchema,
  createBirthRecordSchema,
  breedingStatuses,
  birthStatuses
};
