import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Box, Typography, IconButton, Paper, Grid, Card, CardContent, alpha, useTheme } from '@mui/material';
import { 
  Close, 
  Add, 
  Pets, 
  LocalHospital, 
  Agriculture, 
  Restaurant,
  Assessment,
  Store,
  TrendingUp,
  Inventory,
  Print,
  Download,
  Sync,
  Refresh,
  Settings,
  Search
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { ROUTES } from '../constants/routes';

interface QuickAction {
  id: string;
  title: string;
  icon: React.ReactNode;
  path: string;
  color: string;
}

interface QuickActionsProps {
  onClose: () => void;
}

const QuickActions: React.FC<QuickActionsProps> = ({ onClose }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Define quick actions
  const quickActions: QuickAction[] = [
    {
      id: 'add-animal',
      title: translate('animals.add'),
      icon: <Add />,
      path: `${ROUTES.ANIMALS}/new`,
      color: theme.palette.primary.main
    },
    {
      id: 'health-check',
      title: translate('health.add_record'),
      icon: <LocalHospital />,
      path: `${ROUTES.HEALTH}/records/new`,
      color: theme.palette.error.main
    },
    {
      id: 'add-feeding',
      title: translate('feeding.add'),
      icon: <Restaurant />,
      path: `${ROUTES.FEED}/records/new`,
      color: theme.palette.success.main
    },
    {
      id: 'add-breeding',
      title: translate('breeding.add'),
      icon: <Agriculture />,
      path: `${ROUTES.BREEDING}/new`,
      color: theme.palette.warning.main
    },
    {
      id: 'add-transaction',
      title: translate('financial.add_transaction'),
      icon: <TrendingUp />,
      path: `${ROUTES.FINANCIAL}/transactions/new`,
      color: theme.palette.info.main
    },
    {
      id: 'add-inventory',
      title: translate('inventory.add'),
      icon: <Inventory />,
      path: `${ROUTES.INVENTORY}/new`,
      color: theme.palette.secondary.main
    },
    {
      id: 'generate-report',
      title: translate('reports.generate'),
      icon: <Assessment />,
      path: `${ROUTES.REPORTS}/new`,
      color: '#9c27b0' // purple
    },
    {
      id: 'marketplace',
      title: translate('commercial.marketplace'),
      icon: <Store />,
      path: `${ROUTES.COMMERCIAL}/marketplace`,
      color: '#ff9800' // orange
    },
    {
      id: 'print-reports',
      title: translate('reports.print'),
      icon: <Print />,
      path: `${ROUTES.REPORTS}/print`,
      color: '#607d8b' // blue grey
    },
    {
      id: 'export-data',
      title: translate('common.export'),
      icon: <Download />,
      path: `${ROUTES.SETTINGS}/export`,
      color: '#795548' // brown
    },
    {
      id: 'sync-data',
      title: translate('common.sync'),
      icon: <Sync />,
      path: `${ROUTES.SETTINGS}/sync`,
      color: '#009688' // teal
    },
    {
      id: 'advanced-search',
      title: translate('common.advanced_search'),
      icon: <Search />,
      path: `${ROUTES.ANIMALS}/search`,
      color: '#673ab7' // deep purple
    }
  ];
  
  // Handle action click
  const handleActionClick = (path: string) => {
    navigate(path);
    onClose();
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        maxWidth: '400px',
        zIndex: theme.zIndex.drawer + 2,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Paper
        elevation={4}
        sx={{
          height: '100%',
          borderRadius: 0,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: alpha(theme.palette.background.paper, 0.95),
          backdropFilter: 'blur(10px)',
          borderLeft: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Refresh color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              {translate('common.quick_actions')}
            </Typography>
          </Box>
          
          <IconButton edge="end" onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
        
        {/* Actions Grid */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          <Grid container spacing={2}>
            {quickActions.map((action) => (
              <Grid item xs={6} key={action.id}>
                <Card
                  component={motion.div}
                  whileHover={{ 
                    y: -5,
                    boxShadow: `0 8px 20px ${alpha(action.color, 0.3)}`
                  }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleActionClick(action.path)}
                  sx={{
                    cursor: 'pointer',
                    height: '100%',
                    borderRadius: 2,
                    border: `1px solid ${alpha(action.color, 0.2)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      borderColor: alpha(action.color, 0.5)
                    }
                  }}
                >
                  <CardContent sx={{ textAlign: 'center', p: 2 }}>
                    <Box
                      sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        bgcolor: alpha(action.color, 0.1),
                        color: action.color,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        margin: '0 auto 12px auto'
                      }}
                    >
                      {action.icon}
                    </Box>
                    
                    <Typography variant="body2" fontWeight="medium">
                      {action.title}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
        
        {/* Footer */}
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            textAlign: 'center'
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {translate('common.customize_quick_actions')}
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default QuickActions;
