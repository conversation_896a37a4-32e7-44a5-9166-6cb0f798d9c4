import React, { useState, ReactNode } from 'react';
import { Box, Paper, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, IconButton, Tooltip, useTheme, alpha, Chip, TextField, InputAdornment } from '@mui/material';
import {
  Edit,
  Delete,
  Visibility,
  FilterList,
  Search,
  GetApp,
  Print,
  Share
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { getSafeElevation } from '../../utils/themeDefaults';
import { useLanguage } from '../../contexts/LanguageContext';

export interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'right' | 'left' | 'center';
  format?: (value: any) => ReactNode;
}

export interface SubModuleDataTableProps {
  title: string;
  columns: Column[];
  data: any[];
  onView?: (row: any) => void;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  onExport?: () => void;
  onPrint?: () => void;
  onShare?: () => void;
  moduleColor?: string;
  searchEnabled?: boolean;
  filterEnabled?: boolean;
  actionsEnabled?: boolean;
  paginationEnabled?: boolean;
  rowsPerPageOptions?: number[];
  defaultRowsPerPage?: number;
  emptyStateMessage?: string;
}

/**
 * A standardized data table component for submodules
 */
const SubModuleDataTable: React.FC<SubModuleDataTableProps> = ({
  title,
  columns,
  data,
  onView,
  onEdit,
  onDelete,
  onExport,
  onPrint,
  onShare,
  moduleColor,
  searchEnabled = true,
  filterEnabled = true,
  actionsEnabled = true,
  paginationEnabled = true,
  rowsPerPageOptions = [5, 10, 25],
  defaultRowsPerPage = 10,
  emptyStateMessage
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Use provided module color or default to primary
  const tableColor = moduleColor || theme.palette.primary.main;

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');

  // Handle pagination changes
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  // Filter data based on search query
  const filteredData = searchQuery
    ? data.filter(row =>
        Object.values(row).some(
          value =>
            value !== null &&
            value !== undefined &&
            value.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
      )
    : data;

  // Get current page data
  const currentPageData = paginationEnabled
    ? filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : filteredData;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.6)}, ${alpha(theme.palette.background.paper, 0.4)})`,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(tableColor, 0.15)}`,
          boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          '&:hover': {
            boxShadow: `0 8px 25px ${alpha(theme.palette.common.black, 0.1)}`,
            borderColor: alpha(tableColor, 0.25)
          },
          transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)'
        }}
      >
        {/* Table Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            borderBottom: `1px solid ${alpha(tableColor, 0.1)}`,
            backgroundColor: alpha(tableColor, 0.05)
          }}
        >
          <Typography variant="h6" fontWeight="bold" color={tableColor}>
            {title}
          </Typography>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Search Field */}
            {searchEnabled && (
              <TextField
                size="small"
                placeholder={translate('common.search')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: 200,
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: theme.palette.background.paper,
                    '& fieldset': {
                      borderColor: alpha(tableColor, 0.2),
                    },
                    '&:hover fieldset': {
                      borderColor: alpha(tableColor, 0.5),
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: tableColor,
                    },
                  },
                }}
              />
            )}

            {/* Filter Button */}
            {filterEnabled && (
              <Tooltip title={translate('common.filter')}>
                <IconButton
                  size="small"
                  sx={{
                    backgroundColor: alpha(tableColor, 0.1),
                    color: tableColor,
                    '&:hover': {
                      backgroundColor: alpha(tableColor, 0.2),
                    },
                  }}
                >
                  <FilterList fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Export Button */}
            {onExport && (
              <Tooltip title={translate('common.export')}>
                <IconButton
                  size="small"
                  onClick={onExport}
                  sx={{
                    backgroundColor: alpha(tableColor, 0.1),
                    color: tableColor,
                    '&:hover': {
                      backgroundColor: alpha(tableColor, 0.2),
                    },
                  }}
                >
                  <GetApp fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Print Button */}
            {onPrint && (
              <Tooltip title={translate('common.print')}>
                <IconButton
                  size="small"
                  onClick={onPrint}
                  sx={{
                    backgroundColor: alpha(tableColor, 0.1),
                    color: tableColor,
                    '&:hover': {
                      backgroundColor: alpha(tableColor, 0.2),
                    },
                  }}
                >
                  <Print fontSize="small" />
                </IconButton>
              </Tooltip>
            )}

            {/* Share Button */}
            {onShare && (
              <Tooltip title={translate('common.share')}>
                <IconButton
                  size="small"
                  onClick={onShare}
                  sx={{
                    backgroundColor: alpha(tableColor, 0.1),
                    color: tableColor,
                    '&:hover': {
                      backgroundColor: alpha(tableColor, 0.2),
                    },
                  }}
                >
                  <Share fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {/* Table Content */}
        <TableContainer sx={{ maxHeight: 440 }}>
          <Table stickyHeader aria-label={title}>
            <TableHead>
              <TableRow>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align}
                    style={{ minWidth: column.minWidth }}
                    sx={{
                      backgroundColor: alpha(tableColor, 0.15),
                      color: theme.palette.text.primary,
                      fontWeight: 'bold',
                      borderBottom: `2px solid ${alpha(tableColor, 0.2)}`,
                    }}
                  >
                    {column.label}
                  </TableCell>
                ))}
                {actionsEnabled && (onView || onEdit || onDelete) && (
                  <TableCell
                    align="center"
                    sx={{
                      backgroundColor: alpha(tableColor, 0.15),
                      color: theme.palette.text.primary,
                      fontWeight: 'bold',
                      minWidth: 120,
                      borderBottom: `2px solid ${alpha(tableColor, 0.2)}`,
                    }}
                  >
                    {translate('common.actions')}
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {currentPageData.length > 0 ? (
                currentPageData.map((row, rowIndex) => {
                  return (
                    <TableRow
                      hover
                      role="checkbox"
                      tabIndex={-1}
                      key={rowIndex}
                      sx={{
                        '&:nth-of-type(odd)': {
                          backgroundColor: alpha(theme.palette.background.default, 0.3),
                        },
                        '&:hover': {
                          backgroundColor: alpha(tableColor, 0.1),
                          transform: 'translateY(-2px)',
                          boxShadow: `0 4px 8px ${alpha(theme.palette.common.black, 0.05)}`,
                        },
                        transition: 'all 0.2s ease',
                      }}
                    >
                      {columns.map((column) => {
                        const value = row[column.id];
                        return (
                          <TableCell key={column.id} align={column.align}>
                            {column.format ? column.format(value) : value}
                          </TableCell>
                        );
                      })}

                      {actionsEnabled && (onView || onEdit || onDelete) && (
                        <TableCell align="center">
                          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                            {onView && (
                              <Tooltip title={translate('common.view')}>
                                <IconButton
                                  size="small"
                                  onClick={() => onView(row)}
                                  sx={{
                                    color: theme.palette.info.main,
                                    '&:hover': {
                                      backgroundColor: alpha(theme.palette.info.main, 0.1),
                                    },
                                  }}
                                >
                                  <Visibility fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}

                            {onEdit && (
                              <Tooltip title={translate('common.edit')}>
                                <IconButton
                                  size="small"
                                  onClick={() => onEdit(row)}
                                  sx={{
                                    color: theme.palette.warning.main,
                                    '&:hover': {
                                      backgroundColor: alpha(theme.palette.warning.main, 0.1),
                                    },
                                  }}
                                >
                                  <Edit fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}

                            {onDelete && (
                              <Tooltip title={translate('common.delete')}>
                                <IconButton
                                  size="small"
                                  onClick={() => onDelete(row)}
                                  sx={{
                                    color: theme.palette.error.main,
                                    '&:hover': {
                                      backgroundColor: alpha(theme.palette.error.main, 0.1),
                                    },
                                  }}
                                >
                                  <Delete fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Box>
                        </TableCell>
                      )}
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length + (actionsEnabled ? 1 : 0)}
                    align="center"
                    sx={{ py: 3 }}
                  >
                    <Typography variant="body1" color="text.secondary">
                      {emptyStateMessage || translate('common.no_data')}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        {paginationEnabled && (
          <TablePagination
            rowsPerPageOptions={rowsPerPageOptions}
            component="div"
            count={filteredData.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderTop: `1px solid ${alpha(tableColor, 0.1)}`,
              '.MuiTablePagination-selectIcon': {
                color: tableColor,
              },
            }}
          />
        )}
      </Paper>
    </motion.div>
  );
};

export default SubModuleDataTable;
