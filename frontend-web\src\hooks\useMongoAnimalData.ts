import { useState, useEffect, useCallback } from 'react';
import { Animal, AnimalStats } from '../types/animal';
import { animalMongoService } from '../services/animalMongoService';
import { useMongoDb } from '../contexts/SimpleMongoDbContext';

/**
 * Hook to fetch and manage animal data from MongoDB
 */
export const useMongoAnimalData = () => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [stats, setStats] = useState<AnimalStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { isConnected } = useMongoDb();

  // Calculate stats from animals
  const calculateStats = useCallback((animalList: Animal[]) => {
    const totalAnimals = animalList.length;
    const activeAnimals = animalList.filter(a => a.status === 'Active').length;
    const inactiveAnimals = animalList.filter(a => a.status === 'Inactive').length;
    const healthyAnimals = animalList.filter(a => a.healthStatus === 'healthy').length;
    const sickAnimals = animalList.filter(a => a.healthStatus === 'sick').length;
    const injuredAnimals = animalList.filter(a => a.healthStatus === 'injured').length;
    const pregnantAnimals = animalList.filter(a => a.healthStatus === 'pregnant').length;

    const speciesCount: Record<string, number> = {};
    animalList.forEach(animal => {
      const species = animal.species || 'Unknown';
      speciesCount[species] = (speciesCount[species] || 0) + 1;
    });

    const breedCount: Record<string, number> = {};
    animalList.forEach(animal => {
      const breed = animal.breed || 'Unknown';
      breedCount[breed] = (breedCount[breed] || 0) + 1;
    });

    return {
      totalAnimals,
      activeAnimals,
      inactiveAnimals,
      healthyAnimals,
      sickAnimals,
      injuredAnimals,
      pregnantAnimals,
      healthPercentage: totalAnimals > 0 ? Math.round((healthyAnimals / totalAnimals) * 100) : 0,
      bySpecies: speciesCount,
      byBreed: breedCount,
      byStatus: {
        active: activeAnimals,
        inactive: inactiveAnimals
      },
      byHealth: {
        healthy: healthyAnimals,
        sick: sickAnimals,
        injured: injuredAnimals,
        pregnant: pregnantAnimals
      }
    } as AnimalStats;
  }, []);

  // Fetch all animals
  const fetchAnimals = useCallback(async () => {
    try {
      setLoading(true);

      // Always try to get data from MongoDB
      console.log('Fetching animals from MongoDB...');
      const data = await animalMongoService.getAllAnimals();

      console.log(`Retrieved ${data.length} animals from MongoDB`);
      setAnimals(data);
      setStats(calculateStats(data));
      setError(null);
    } catch (err) {
      console.error('Error in fetchAnimals:', err);
      setError(err as Error);

      // Keep previous data if there was an error
      if (animals.length === 0) {
        setAnimals([]);
        setStats(calculateStats([]));
      }
    } finally {
      setLoading(false);
    }
  }, [isConnected, calculateStats, animals]);

  // Create a new animal
  const createAnimal = useCallback(async (animal: Animal) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      const newAnimal = await animalMongoService.createAnimal(animal);
      setAnimals(prev => {
        const newList = [...prev, newAnimal];
        setStats(calculateStats(newList));
        return newList;
      });
      return newAnimal;
    } catch (err) {
      console.error('Error creating animal in MongoDB:', err);
      throw err;
    }
  }, [isConnected, calculateStats]);

  // Update an animal
  const updateAnimal = useCallback(async (id: string, animal: Partial<Animal>) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      const success = await animalMongoService.updateAnimal(id, animal);
      if (success) {
        setAnimals(prev => {
          const newList = prev.map(a => a.id === id ? { ...a, ...animal } : a);
          setStats(calculateStats(newList));
          return newList;
        });
      }
      return success;
    } catch (err) {
      console.error('Error updating animal in MongoDB:', err);
      throw err;
    }
  }, [isConnected, calculateStats]);

  // Delete an animal
  const deleteAnimal = useCallback(async (id: string) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      const success = await animalMongoService.deleteAnimal(id);
      if (success) {
        setAnimals(prev => {
          const newList = prev.filter(a => a.id !== id);
          setStats(calculateStats(newList));
          return newList;
        });
      }
      return success;
    } catch (err) {
      console.error('Error deleting animal in MongoDB:', err);
      throw err;
    }
  }, [isConnected, calculateStats]);

  // Fetch animals by species
  const fetchAnimalsBySpecies = useCallback(async (species: string) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      return await animalMongoService.findBySpecies(species);
    } catch (err) {
      console.error('Error fetching animals by species from MongoDB:', err);
      throw err;
    }
  }, [isConnected]);

  // Fetch animals by breed
  const fetchAnimalsByBreed = useCallback(async (breed: string) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      return await animalMongoService.findByBreed(breed);
    } catch (err) {
      console.error('Error fetching animals by breed from MongoDB:', err);
      throw err;
    }
  }, [isConnected]);

  // Fetch animals by status
  const fetchAnimalsByStatus = useCallback(async (status: string) => {
    // Always consider MongoDB as connected
    // if (!isConnected) {
    //   throw new Error('MongoDB is not connected');
    // }

    try {
      return await animalMongoService.findByStatus(status);
    } catch (err) {
      console.error('Error fetching animals by status from MongoDB:', err);
      throw err;
    }
  }, [isConnected]);

  // Get animal by ID
  const getAnimalById = useCallback(async (id: string) => {
    try {
      console.log(`Fetching animal with ID ${id} from MongoDB...`);
      const animal = await animalMongoService.getAnimalById(id);

      if (animal) {
        console.log(`Found animal with ID ${id} in MongoDB`);
        return animal;
      } else {
        console.log(`Animal with ID ${id} not found in MongoDB`);
        return null;
      }
    } catch (err) {
      console.error(`Error in getAnimalById for ID ${id}:`, err);
      return null;
    }
  }, []);

  // Load animals on mount and when connection status changes
  useEffect(() => {
    // Always fetch animals, regardless of connection status
    fetchAnimals();
  }, [fetchAnimals]);

  return {
    animals,
    stats,
    loading,
    error,
    fetchAnimals,
    createAnimal,
    updateAnimal,
    deleteAnimal,
    fetchAnimalsBySpecies,
    fetchAnimalsByBreed,
    fetchAnimalsByStatus,
    getAnimalById
  };
};
