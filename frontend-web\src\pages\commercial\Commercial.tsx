import React, { lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { BkbAuctionProvider } from '../../contexts/commercial/BkbAuctionContext';

// Lazy load components to avoid circular dependencies
const CommercialDashboard = lazy(() => import('./CommercialDashboard'));
const Marketplace = lazy(() => import('./Marketplace'));
const Orders = lazy(() => import('./Orders'));
const Suppliers = lazy(() => import('./Suppliers'));
const SupplierDetail = lazy(() => import('./SupplierDetail'));
const Pricing = lazy(() => import('./Pricing'));
const Auctions = lazy(() => import('./Auctions'));
const PriceUpdates = lazy(() => import('./PriceUpdates'));

/**
 * Commercial Module
 * This component serves as the router for the Commercial module
 */
const Commercial: React.FC = () => {
  return (
    <Box sx={{ p: 0 }}>
      <Suspense fallback={<Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}><CircularProgress /></Box>}>
        <Routes>
          <Route index element={<Navigate to="dashboard" replace />} />
          <Route path="dashboard" element={<CommercialDashboard />} />
          <Route path="/" element={<CommercialDashboard />} />
          <Route path="marketplace" element={<Marketplace />} />
          <Route path="orders" element={<Orders />} />
          <Route path="suppliers" element={<Suppliers />} />
          <Route path="suppliers/:id" element={<SupplierDetail />} />
          <Route path="pricing" element={<Pricing />} />
          <Route
            path="auctions"
            element={
              <BkbAuctionProvider>
                <Auctions />
              </BkbAuctionProvider>
            }
          />
          <Route path="price-updates" element={<PriceUpdates />} />
        </Routes>
      </Suspense>
    </Box>
  );
};

export default Commercial;
