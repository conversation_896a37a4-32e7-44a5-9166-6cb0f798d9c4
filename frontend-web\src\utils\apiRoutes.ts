/**
 * API Routes configuration
 * Centralized management of API endpoints
 */

// Module-specific API routes
// These routes are relative to the baseURL defined in the axios instance (src/utils/axios.ts),
// which is /api (from REACT_APP_API_URL in frontend-web/.env).
const API_ROUTES = {
  // Authentication
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    refreshToken: '/auth/refresh-token',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    verifyEmail: '/auth/verify-email',
  },

  // Dashboard
  dashboard: {
    stats: '/dashboard/stats',
    recentActivity: '/dashboard/recent-activity',
    alerts: '/dashboard/alerts',
  },

  // Animals
  animals: {
    base: '/animals',
    byId: (id: string) => `/animals/${id}`,
    profiles: '/animals/profiles',
    records: '/animals/records',
    tracking: '/animals/tracking',
    genealogy: '/animals/genealogy',
    stats: '/animals/stats',
  },

  // Breeding
  breeding: {
    base: '/breeding',
    byId: (id: string) => `/breeding/${id}`,
    heat: '/breeding/heat', // Corrected from heatRecords and path updated
    pregnancies: '/breeding/pregnancies',
    births: '/breeding/births',
    stats: '/breeding/stats',
  },

  // Health
  health: {
    base: '/health',
    byId: (id: string) => `/health/${id}`,
    records: '/health/records',
    vaccinations: '/health/vaccinations',
    treatments: '/health/treatments',
    appointments: '/health/appointments',
    stats: '/health/stats',
  },

  // Feeding
  feeding: {
    base: '/feeding',
    byId: (id: string) => `/feeding/${id}`,
    records: '/feeding/records',
    inventory: '/feeding/inventory',
    schedules: '/feeding/schedules',
    stats: '/feeding/stats',
  },

  // Commercial
  commercial: {
    base: '/commercial',
    byId: (id: string) => `/commercial/${id}`,
    orders: '/commercial/orders',
    auctions: '/commercial/auctions',
    sales: '/commercial/sales',
    pricing: '/commercial/pricing',
    stats: '/commercial/stats',
  },

  // Financial
  financial: {
    base: '/financial',
    byId: (id: string) => `/financial/${id}`,
    transactions: '/financial/transactions',
    expenses: '/financial/expenses',
    income: '/financial/income',
    budgets: '/financial/budgets',
    forecasts: '/financial/forecasts',
    stats: '/financial/stats',
  },

  // Compliance
  compliance: {
    base: '/compliance',
    byId: (id: string) => `/compliance/${id}`,
    documents: '/compliance/documents',
    inspections: '/compliance/inspections',
    certifications: '/compliance/certifications',
    stats: '/compliance/stats',
  },

  // Reports
  reports: {
    base: '/reports',
    byId: (id: string) => `/reports/${id}`,
    generate: '/reports/generate',
    download: (id: string) => `/reports/download/${id}`,
    stats: '/reports/stats',
  },

  // Resources
  resources: {
    base: '/resources',
    byId: (id: string) => `/resources/${id}`,
    equipment: '/resources/equipment',
    staff: '/resources/staff',
    suppliers: '/resources/suppliers',
    stats: '/resources/stats',
  },

  // Users
  users: {
    base: '/users',
    byId: (id: string) => `/users/${id}`,
    profile: '/users/profile',
    permissions: '/users/permissions',
  },

  // Settings
  settings: {
    base: '/settings',
    system: '/settings/system',
    notifications: '/settings/notifications',
    backup: '/settings/backup',
  },
};

export default API_ROUTES;
