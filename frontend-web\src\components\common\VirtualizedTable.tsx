import React, { useState, useEffect, useRef } from 'react';
import { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, CircularProgress, useTheme, alpha, TablePagination, Checkbox, IconButton, Tooltip, Chip } from '@mui/material';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import InfiniteLoader from 'react-window-infinite-loader';
import { ArrowUpward, ArrowDownward, FilterList } from '@mui/icons-material';

export interface Column<T> {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  format?: (value: any, row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
}

export interface VirtualizedTableProps<T> {
  columns: Column<T>[];
  data: T[];
  loading?: boolean;
  error?: string;
  rowHeight?: number;
  headerHeight?: number;
  maxHeight?: number | string;
  selectable?: boolean;
  onRowClick?: (row: T) => void;
  onSelectionChange?: (selectedRows: T[]) => void;
  uniqueKey?: keyof T;
  sortable?: boolean;
  defaultSortBy?: string;
  defaultSortDirection?: 'asc' | 'desc';
  infiniteScroll?: boolean;
  loadMoreItems?: () => Promise<void>;
  hasNextPage?: boolean;
  emptyMessage?: string;
  stickyHeader?: boolean;
  dense?: boolean;
  showPagination?: boolean;
  rowsPerPageOptions?: number[];
  defaultRowsPerPage?: number;
  getRowClassName?: (row: T) => string;
  getRowStyle?: (row: T) => React.CSSProperties;
  virtualizeRows?: boolean;
}

function VirtualizedTable<T extends Record<string, any>>({
  columns,
  data,
  loading = false,
  error,
  rowHeight = 53,
  headerHeight = 56,
  maxHeight = 600,
  selectable = false,
  onRowClick,
  onSelectionChange,
  uniqueKey = 'id' as keyof T,
  sortable = true,
  defaultSortBy,
  defaultSortDirection = 'asc',
  infiniteScroll = false,
  loadMoreItems,
  hasNextPage = false,
  emptyMessage = 'No data available',
  stickyHeader = true,
  dense = false,
  showPagination = false,
  rowsPerPageOptions = [10, 25, 50, 100],
  defaultRowsPerPage = 25,
  getRowClassName,
  getRowStyle,
  virtualizeRows = true
}: VirtualizedTableProps<T>) {
  const theme = useTheme();
  const [selectedRows, setSelectedRows] = useState<T[]>([]);
  const [sortBy, setSortBy] = useState<string | undefined>(defaultSortBy);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(defaultSortDirection);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);
  const listRef = useRef<FixedSizeList | null>(null);

  // Adjust row height based on dense mode
  const actualRowHeight = dense ? rowHeight * 0.75 : rowHeight;

  // Handle selection changes
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(selectedRows);
    }
  }, [selectedRows, onSelectionChange]);

  // Sort data
  const sortedData = React.useMemo(() => {
    if (!sortBy) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];

      if (aValue === bValue) return 0;

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? -1 : 1;
      if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? 1 : -1;

      return sortDirection === 'asc'
        ? (aValue < bValue ? -1 : 1)
        : (bValue < aValue ? -1 : 1);
    });
  }, [data, sortBy, sortDirection]);

  // Get paginated data
  const paginatedData = React.useMemo(() => {
    if (!showPagination) return sortedData;
    return sortedData.slice(page * rowsPerPage, (page + 1) * rowsPerPage);
  }, [sortedData, showPagination, page, rowsPerPage]);

  // Handle sort change
  const handleSort = (columnId: string) => {
    const isAsc = sortBy === columnId && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortBy(columnId);

    // Reset to first page when sorting changes
    if (showPagination) {
      setPage(0);
    }

    // Scroll back to top when sorting changes
    if (listRef.current) {
      listRef.current.scrollTo(0);
    }
  };

  // Handle row selection
  const handleSelectRow = (row: T) => {
    setSelectedRows(prev => {
      const key = row[uniqueKey];
      const isSelected = prev.some(r => r[uniqueKey] === key);

      if (isSelected) {
        return prev.filter(r => r[uniqueKey] !== key);
      } else {
        return [...prev, row];
      }
    });
  };

  // Handle select all rows
  const handleSelectAllRows = () => {
    if (selectedRows.length === paginatedData.length) {
      setSelectedRows([]);
    } else {
      setSelectedRows([...paginatedData]);
    }
  };

  // Check if a row is selected
  const isSelected = (row: T) => {
    return selectedRows.some(r => r[uniqueKey] === row[uniqueKey]);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Infinite loader item status
  const isItemLoaded = (index: number) => {
    return !hasNextPage || index < data.length;
  };

  // Row renderer for virtualized list
  const RowRenderer = ({ index, style }: ListChildComponentProps) => {
    if (index >= paginatedData.length) {
      if (loading && infiniteScroll) {
        return (
          <TableRow style={style}>
            <TableCell
              colSpan={columns.length + (selectable ? 1 : 0)}
              align="center"
              sx={{ height: actualRowHeight }}
            >
              <CircularProgress size={24} />
            </TableCell>
          </TableRow>
        );
      }
      return null;
    }

    const row = paginatedData[index];
    const isRowSelected = isSelected(row);
    const rowClassName = getRowClassName ? getRowClassName(row) : '';
    const rowStyle = getRowStyle ? getRowStyle(row) : {};

    return (
      <TableRow
        hover
        role="checkbox"
        tabIndex={-1}
        key={String(row[uniqueKey])}
        selected={isRowSelected}
        style={{ ...style, ...rowStyle }}
        className={rowClassName}
        onClick={() => onRowClick && onRowClick(row)}
        sx={{
          cursor: onRowClick ? 'pointer' : 'default',
          '&.Mui-selected, &.Mui-selected:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.12),
          }
        }}
      >
        {selectable && (
          <TableCell padding="checkbox">
            <Checkbox
              color="primary"
              checked={isRowSelected}
              onClick={(e) => {
                e.stopPropagation();
                handleSelectRow(row);
              }}
            />
          </TableCell>
        )}
        {columns.map((column) => {
          const value = row[column.id];
          return (
            <TableCell
              key={column.id}
              align={column.align || 'left'}
              style={{ minWidth: column.minWidth }}
              sx={{ height: actualRowHeight, py: dense ? 1 : 2 }}
            >
              {column.format ? column.format(value, row) : value}
            </TableCell>
          );
        })}
      </TableRow>
    );
  };

  // Render loading state
  if (loading && data.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={200}>
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={200}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  // Render empty state
  if (data.length === 0 && !loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height={200}>
        <Typography color="textSecondary">{emptyMessage}</Typography>
      </Box>
    );
  }

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight }}>
        <Table stickyHeader={stickyHeader} size={dense ? 'small' : 'medium'}>
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    color="primary"
                    indeterminate={selectedRows.length > 0 && selectedRows.length < paginatedData.length}
                    checked={paginatedData.length > 0 && selectedRows.length === paginatedData.length}
                    onChange={handleSelectAllRows}
                  />
                </TableCell>
              )}
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                  sx={{
                    fontWeight: 'bold',
                    backgroundColor: theme.palette.mode === 'dark'
                      ? alpha(theme.palette.background.paper, 0.9)
                      : alpha(theme.palette.background.paper, 0.9),
                    cursor: column.sortable && sortable ? 'pointer' : 'default',
                    userSelect: 'none',
                    height: headerHeight,
                  }}
                  onClick={() => {
                    if (column.sortable && sortable) {
                      handleSort(column.id);
                    }
                  }}
                >
                  <Box display="flex" alignItems="center" justifyContent={column.align === 'right' ? 'flex-end' : 'flex-start'}>
                    {column.label}
                    {column.sortable && sortable && sortBy === column.id && (
                      <Box component="span" ml={0.5}>
                        {sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />}
                      </Box>
                    )}
                    {column.filterable && (
                      <Tooltip title="Filter">
                        <IconButton size="small" sx={{ ml: 0.5 }}>
                          <FilterList fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>

          {!virtualizeRows ? (
            <TableBody>
              {paginatedData.map((row, index) => {
                const isRowSelected = isSelected(row);
                const rowClassName = getRowClassName ? getRowClassName(row) : '';
                const rowStyle = getRowStyle ? getRowStyle(row) : {};

                return (
                  <TableRow
                    hover
                    role="checkbox"
                    tabIndex={-1}
                    key={String(row[uniqueKey])}
                    selected={isRowSelected}
                    className={rowClassName}
                    style={rowStyle}
                    onClick={() => onRowClick && onRowClick(row)}
                    sx={{
                      cursor: onRowClick ? 'pointer' : 'default',
                      '&.Mui-selected, &.Mui-selected:hover': {
                        backgroundColor: alpha(theme.palette.primary.main, 0.12),
                      }
                    }}
                  >
                    {selectable && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          color="primary"
                          checked={isRowSelected}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSelectRow(row);
                          }}
                        />
                      </TableCell>
                    )}
                    {columns.map((column) => {
                      const value = row[column.id];
                      return (
                        <TableCell
                          key={column.id}
                          align={column.align || 'left'}
                          style={{ minWidth: column.minWidth }}
                          sx={{ height: actualRowHeight, py: dense ? 1 : 2 }}
                        >
                          {column.format ? column.format(value, row) : value}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })}
            </TableBody>
          ) : (
            <TableBody>
              <TableRow>
                <TableCell
                  style={{ padding: 0 }}
                  colSpan={columns.length + (selectable ? 1 : 0)}
                >
                  <div style={{ height: paginatedData.length * actualRowHeight }}>
                    <AutoSizer disableHeight>
                      {({ width }: { width: number }) => (
                        infiniteScroll ? (
                          <InfiniteLoader
                            isItemLoaded={isItemLoaded}
                            itemCount={hasNextPage ? data.length + 1 : data.length}
                            loadMoreItems={loadMoreItems || (() => Promise.resolve())}
                          >
                            {({ onItemsRendered, ref }: { onItemsRendered: any, ref: any }) => (
                              <FixedSizeList
                                ref={(list) => {
                                  ref(list);
                                  if (list) {
                                    listRef.current = list;
                                  }
                                }}
                                height={Math.min(
                                  paginatedData.length * actualRowHeight,
                                  typeof maxHeight === 'number' ? maxHeight - headerHeight : 600
                                )}
                                width={width}
                                itemCount={paginatedData.length + (hasNextPage ? 1 : 0)}
                                itemSize={actualRowHeight}
                                onItemsRendered={onItemsRendered}
                              >
                                {RowRenderer}
                              </FixedSizeList>
                            )}
                          </InfiniteLoader>
                        ) : (
                          <FixedSizeList
                            ref={listRef}
                            height={Math.min(
                              paginatedData.length * actualRowHeight,
                              typeof maxHeight === 'number' ? maxHeight - headerHeight : 600
                            )}
                            width={width}
                            itemCount={paginatedData.length}
                            itemSize={actualRowHeight}
                          >
                            {RowRenderer}
                          </FixedSizeList>
                        )
                      )}
                    </AutoSizer>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          )}
        </Table>
      </TableContainer>

      {showPagination && (
        <TablePagination
          rowsPerPageOptions={rowsPerPageOptions}
          component="div"
          count={data.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      )}
    </Paper>
  );
}

export default VirtualizedTable;
