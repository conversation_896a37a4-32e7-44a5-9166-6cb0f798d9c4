const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * Security Cleanup Script for AgriIntel
 * 
 * This script automatically fixes critical security issues:
 * - Removes hardcoded credentials from test files
 * - Generates secure environment variables
 * - Sets proper file permissions
 * - Creates secure configuration templates
 */

class SecurityCleanup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '../..');
    this.fixedFiles = [];
    this.errors = [];
  }

  /**
   * Run complete security cleanup
   */
  async runCleanup() {
    console.log('🔒 Starting Security Cleanup for AgriIntel...\n');
    
    try {
      await this.removeHardcodedCredentials();
      await this.generateSecureEnvironmentVariables();
      await this.setProperFilePermissions();
      await this.createSecureConfigTemplates();
      await this.updateTestFiles();
      
      this.generateReport();
    } catch (error) {
      console.error('❌ Security cleanup failed:', error);
      process.exit(1);
    }
  }

  /**
   * Remove hardcoded credentials from files
   */
  async removeHardcodedCredentials() {
    console.log('🧹 Removing hardcoded credentials...');
    
    const filesToClean = [
      'frontend-web/src/services/api.ts',
      'frontend-web/src/components/IntegratedApp.tsx',
      'backend/src/server-simple.js',
      'backend/src/simple-server.js'
    ];
    
    for (const filePath of filesToClean) {
      const fullPath = path.join(this.projectRoot, filePath);
      if (fs.existsSync(fullPath)) {
        try {
          let content = fs.readFileSync(fullPath, 'utf8');
          
          // Replace hardcoded passwords with environment variables or secure defaults
          content = content.replace(/password:\s*['"][^'"]*['"]/g, "password: process.env.DEFAULT_PASSWORD || 'CHANGE_ME'");
          content = content.replace(/password\s*=\s*['"][^'"]*['"]/g, "password = process.env.DEFAULT_PASSWORD || 'CHANGE_ME'");
          
          // Replace hardcoded API keys
          content = content.replace(/api[_-]?key:\s*['"][^'"]*['"]/gi, "apiKey: process.env.API_KEY || 'CHANGE_ME'");
          
          // Replace hardcoded secrets
          content = content.replace(/secret:\s*['"][^'"]*['"]/gi, "secret: process.env.JWT_SECRET || 'CHANGE_ME'");
          
          fs.writeFileSync(fullPath, content);
          this.fixedFiles.push(filePath);
        } catch (error) {
          this.errors.push(`Failed to clean ${filePath}: ${error.message}`);
        }
      }
    }
    
    console.log(`✅ Cleaned ${this.fixedFiles.length} files\n`);
  }

  /**
   * Generate secure environment variables
   */
  async generateSecureEnvironmentVariables() {
    console.log('🔐 Generating secure environment variables...');
    
    const secureEnvPath = path.join(this.projectRoot, 'backend/.env.secure');
    
    const secureConfig = `# Secure Environment Configuration - Generated ${new Date().toISOString()}
# IMPORTANT: Change all values before deploying to production

# Server Configuration
NODE_ENV=production
PORT=3001
HOST=0.0.0.0

# MongoDB Configuration - REPLACE WITH YOUR VALUES
MONGODB_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority
MONGODB_DB_NAME=ampd_livestock_production

# JWT Configuration - SECURE SECRETS GENERATED
JWT_SECRET=${this.generateSecureSecret(64)}
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=${this.generateSecureSecret(64)}
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=${this.generateSecureSecret(64)}
CORS_ORIGIN=https://your-production-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Default Admin Credentials - CHANGE IMMEDIATELY
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=${this.generateSecurePassword()}
DEFAULT_ADMIN_EMAIL=<EMAIL>

# Email Configuration
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
SMTP_FROM=<EMAIL>

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=logs/production.log

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# API Keys - REPLACE WITH YOUR VALUES
WEATHER_API_KEY=your_weather_api_key
GOVERNMENT_API_KEY=your_government_api_key
SMS_API_KEY=your_sms_api_key

# Monitoring
SENTRY_DSN=your_sentry_dsn
ANALYTICS_API_KEY=your_analytics_key

# Feature Flags
FEATURE_ANALYTICS=true
FEATURE_NOTIFICATIONS=true
FEATURE_BACKUP=true
`;

    fs.writeFileSync(secureEnvPath, secureConfig);
    console.log(`✅ Generated secure environment file: ${secureEnvPath}\n`);
  }

  /**
   * Set proper file permissions
   */
  async setProperFilePermissions() {
    console.log('📁 Setting proper file permissions...');
    
    const sensitiveFiles = [
      'backend/.env',
      'backend/.env.production',
      'backend/.env.secure',
      'backend/src/config/database.js'
    ];
    
    for (const filePath of sensitiveFiles) {
      const fullPath = path.join(this.projectRoot, filePath);
      if (fs.existsSync(fullPath)) {
        try {
          // Set file permissions to 600 (read/write for owner only)
          if (process.platform !== 'win32') {
            fs.chmodSync(fullPath, 0o600);
          }
          console.log(`✅ Set permissions for ${filePath}`);
        } catch (error) {
          this.errors.push(`Failed to set permissions for ${filePath}: ${error.message}`);
        }
      }
    }
    
    console.log('✅ File permissions updated\n');
  }

  /**
   * Create secure configuration templates
   */
  async createSecureConfigTemplates() {
    console.log('📋 Creating secure configuration templates...');
    
    // Create secure database seeding script
    const secureSeederPath = path.join(this.projectRoot, 'backend/scripts/seed-secure-users.js');
    const secureSeederContent = `const { connectDB } = require('../src/config/database');
const { hashPassword } = require('../src/services/authService');
const logger = require('../src/utils/logger');

/**
 * Secure User Seeding Script
 * Creates default users with properly hashed passwords
 */
async function seedSecureUsers() {
  try {
    const { db } = await connectDB();
    const usersCollection = db.collection('users');
    
    // Check if admin user already exists
    const existingAdmin = await usersCollection.findOne({ username: 'admin' });
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create secure admin user
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'Admin@${Date.now()}';
    const hashedPassword = await hashPassword(adminPassword);
    
    const adminUser = {
      username: 'admin',
      email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'admin',
      permissions: ['all'],
      status: 'active',
      isActive: true,
      subscriptionTier: 'enterprise',
      profile: {
        farmName: 'AgriIntel System',
        province: 'gauteng',
        farmSize: 'enterprise'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await usersCollection.insertOne(adminUser);
    
    console.log('✅ Secure admin user created');
    console.log('Username: admin');
    console.log('Password:', adminPassword);
    console.log('⚠️ IMPORTANT: Change the password immediately after first login!');
    
  } catch (error) {
    logger.error('Error seeding secure users:', error);
    throw error;
  }
}

if (require.main === module) {
  seedSecureUsers()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedSecureUsers };`;

    fs.writeFileSync(secureSeederPath, secureSeederContent);
    console.log(`✅ Created secure user seeder: ${secureSeederPath}\n`);
  }

  /**
   * Update test files to use environment variables
   */
  async updateTestFiles() {
    console.log('🧪 Updating test files...');
    
    // Create a secure test configuration
    const testConfigPath = path.join(this.projectRoot, 'backend/test-config.js');
    const testConfigContent = `/**
 * Test Configuration
 * Provides secure test credentials and configuration
 */

module.exports = {
  testUsers: {
    admin: {
      username: 'test_admin',
      password: process.env.TEST_ADMIN_PASSWORD || 'TestAdmin@123',
      role: 'admin'
    },
    demo: {
      username: 'test_demo',
      password: process.env.TEST_DEMO_PASSWORD || 'TestDemo@123',
      role: 'demo'
    },
    beta: {
      username: 'test_beta',
      password: process.env.TEST_BETA_PASSWORD || 'TestBeta@123',
      role: 'beta'
    }
  },
  
  testDatabase: {
    uri: process.env.TEST_MONGODB_URI || 'mongodb://localhost:27017/ampd_livestock_test',
    name: process.env.TEST_DB_NAME || 'ampd_livestock_test'
  },
  
  testJWT: {
    secret: process.env.TEST_JWT_SECRET || '${this.generateSecureSecret(32)}',
    expiresIn: '1h'
  }
};`;

    fs.writeFileSync(testConfigPath, testConfigContent);
    console.log(`✅ Created secure test configuration: ${testConfigPath}\n`);
  }

  /**
   * Generate a secure random secret
   */
  generateSecureSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure random password
   */
  generateSecurePassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 16; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Generate cleanup report
   */
  generateReport() {
    console.log('📊 SECURITY CLEANUP REPORT');
    console.log('==========================\n');
    
    console.log(`✅ Files Fixed: ${this.fixedFiles.length}`);
    if (this.fixedFiles.length > 0) {
      this.fixedFiles.forEach(file => console.log(`  - ${file}`));
    }
    
    console.log(`\n❌ Errors: ${this.errors.length}`);
    if (this.errors.length > 0) {
      this.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('\n🔒 SECURITY IMPROVEMENTS COMPLETED:');
    console.log('  ✅ Removed hardcoded credentials');
    console.log('  ✅ Generated secure environment variables');
    console.log('  ✅ Set proper file permissions');
    console.log('  ✅ Created secure configuration templates');
    console.log('  ✅ Updated test files');
    
    console.log('\n⚠️ NEXT STEPS:');
    console.log('  1. Review and update backend/.env.secure');
    console.log('  2. Run: node scripts/seed-secure-users.js');
    console.log('  3. Update production environment variables');
    console.log('  4. Test authentication with new credentials');
    console.log('  5. Run security audit again: node scripts/security-audit.js');
    
    console.log('\n==========================');
    console.log('Security cleanup completed!');
  }
}

// Run the cleanup
if (require.main === module) {
  const cleanup = new SecurityCleanup();
  cleanup.runCleanup().catch(console.error);
}

module.exports = SecurityCleanup;
