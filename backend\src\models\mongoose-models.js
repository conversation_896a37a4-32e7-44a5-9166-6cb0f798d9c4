/**
 * Mongoose Models
 *
 * This file defines Mongoose models for all MongoDB collections
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const schemas = require('./schemas');

// Create Mongoose schemas from the schema definitions
const createMongooseSchema = (schemaDefinition) => {
  const mongooseSchema = new Schema({});

  // Add each field from the schema definition to the Mongoose schema
  Object.entries(schemaDefinition).forEach(([key, value]) => {
    mongooseSchema.add({ [key]: value });
  });

  // Add timestamps if they don't exist
  if (!schemaDefinition.createdAt) {
    mongooseSchema.add({ createdAt: { type: Date, default: Date.now } });
  }
  if (!schemaDefinition.updatedAt) {
    mongooseSchema.add({ updatedAt: { type: Date, default: Date.now } });
  }

  // Add pre-save middleware to update the updatedAt field
  mongooseSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
  });

  return mongooseSchema;
};

// Create Mongoose models
const models = {
  // Animal Management
  Animal: mongoose.model('Animal', createMongooseSchema(schemas.animalSchema)),

  // User Management
  User: mongoose.model('User', createMongooseSchema(schemas.userSchema)),

  // Health Management
  HealthRecord: mongoose.model('HealthRecord', createMongooseSchema(schemas.healthRecordSchema)),

  // Breeding Management
  BreedingRecord: mongoose.model('BreedingRecord', createMongooseSchema(schemas.breedingRecordSchema)),
  BirthRecord: mongoose.model('BirthRecord', createMongooseSchema(schemas.birthRecordSchema)),

  // Feeding Management
  FeedingRecord: mongoose.model('FeedingRecord', createMongooseSchema(schemas.feedingRecordSchema)),
  FeedInventory: mongoose.model('FeedInventory', createMongooseSchema(schemas.feedInventorySchema)),

  // Financial Management
  FinancialRecord: mongoose.model('FinancialRecord', createMongooseSchema(schemas.financialRecordSchema)),

  // Inventory Management
  Inventory: mongoose.model('Inventory', createMongooseSchema(schemas.inventorySchema))
};

// Add additional models that might be referenced in index.js but not defined in schemas.js
const additionalModels = {
  FeedingSchedule: mongoose.model('FeedingSchedule', new Schema({
    animalId: { type: String },
    animalTagNumber: { type: String },
    animalName: { type: String },
    groupId: { type: String },
    groupName: { type: String },
    feedType: { type: String, required: true },
    quantity: { type: Number, required: true },
    unit: { type: String, required: true },
    schedule: {
      frequency: { type: String, required: true }, // daily, weekly, monthly
      times: [{ type: String }], // Array of times (e.g., "08:00", "16:00")
      daysOfWeek: [{ type: Number }], // 0-6 (Sunday-Saturday)
      daysOfMonth: [{ type: Number }] // 1-31
    },
    startDate: { type: String, required: true },
    endDate: { type: String },
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  VaccinationSchedule: mongoose.model('VaccinationSchedule', new Schema({
    animalId: { type: String },
    animalTagNumber: { type: String },
    animalName: { type: String },
    groupId: { type: String },
    groupName: { type: String },
    vaccinationType: { type: String, required: true },
    dueDate: { type: String, required: true },
    status: { type: String, default: 'scheduled' }, // scheduled, completed, overdue
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  HeatDetection: mongoose.model('HeatDetection', new Schema({
    animalId: { type: String, required: true },
    animalTagNumber: { type: String, required: true },
    animalName: { type: String, required: true },
    detectionDate: { type: String, required: true },
    signs: [{ type: String }],
    intensity: { type: String }, // mild, moderate, strong
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  Transaction: mongoose.model('Transaction', new Schema({
    transactionType: { type: String, required: true }, // income, expense
    category: { type: String, required: true },
    subcategory: { type: String },
    amount: { type: Number, required: true },
    date: { type: String, required: true },
    description: { type: String },
    paymentMethod: { type: String },
    reference: { type: String },
    relatedTo: { type: Object },
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  Budget: mongoose.model('Budget', new Schema({
    name: { type: String, required: true },
    period: { type: String, required: true }, // monthly, quarterly, yearly
    startDate: { type: String, required: true },
    endDate: { type: String, required: true },
    categories: [{
      name: { type: String, required: true },
      type: { type: String, required: true }, // income, expense
      budgetedAmount: { type: Number, required: true },
      actualAmount: { type: Number, default: 0 }
    }],
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  Asset: mongoose.model('Asset', new Schema({
    name: { type: String, required: true },
    category: { type: String, required: true },
    purchaseDate: { type: String },
    purchasePrice: { type: Number },
    currentValue: { type: Number },
    depreciationRate: { type: Number },
    location: { type: String },
    status: { type: String, default: 'active' }, // active, sold, retired, damaged
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  InventoryItem: mongoose.model('InventoryItem', createMongooseSchema(schemas.inventorySchema)),

  InventoryTransaction: mongoose.model('InventoryTransaction', new Schema({
    itemId: { type: String, required: true },
    itemName: { type: String, required: true },
    transactionType: { type: String, required: true }, // purchase, use, adjustment
    quantity: { type: Number, required: true },
    date: { type: String, required: true },
    performedBy: { type: String },
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  // Check if the model already exists to prevent "Cannot overwrite model once compiled" error
  Supplier: mongoose.models.Supplier || mongoose.model('Supplier', new Schema({
    name: { type: String, required: true },
    contactPerson: { type: String },
    phone: { type: String },
    email: { type: String },
    address: { type: String },
    category: { type: String }, // feed, equipment, veterinary, etc.
    notes: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  Report: mongoose.model('Report', new Schema({
    name: { type: String, required: true },
    type: { type: String, required: true }, // animal, health, breeding, financial, etc.
    parameters: { type: Object },
    createdBy: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  ReportResult: mongoose.model('ReportResult', new Schema({
    reportId: { type: String, required: true },
    data: { type: Object, required: true },
    generatedAt: { type: Date, default: Date.now },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  SystemSettings: mongoose.model('SystemSettings', new Schema({
    setting: { type: String, required: true },
    value: { type: Schema.Types.Mixed, required: true },
    category: { type: String, required: true },
    description: { type: String },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  UserSettings: mongoose.model('UserSettings', new Schema({
    userId: { type: String, required: true },
    settings: { type: Object, required: true },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  BusinessStrategy: mongoose.model('BusinessStrategy', new Schema({
    name: { type: String, required: true },
    description: { type: String },
    objectives: [{ type: String }],
    startDate: { type: String },
    endDate: { type: String },
    status: { type: String, default: 'active' }, // active, completed, cancelled
    kpis: [{
      name: { type: String },
      target: { type: Number },
      current: { type: Number },
      unit: { type: String }
    }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  MarketAnalysis: mongoose.model('MarketAnalysis', new Schema({
    date: { type: String, required: true },
    marketType: { type: String, required: true }, // livestock, feed, equipment, etc.
    data: { type: Object, required: true },
    insights: [{ type: String }],
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  })),

  PredictiveAnalysis: mongoose.model('PredictiveAnalysis', new Schema({
    analysisType: { type: String, required: true }, // price, production, health, etc.
    date: { type: String, required: true },
    parameters: { type: Object },
    results: { type: Object },
    accuracy: { type: Number },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now }
  }))
};

// Merge models and additionalModels
Object.assign(models, additionalModels);

module.exports = models;
