import React, { useState, useEffect } from 'react';
import CustomButton from './common/CustomButton';
import { Box, Typography, Grid, Paper, Divider, Card, CardContent, Alert, Tabs, Tab, CircularProgress } from '@mui/material';
import { useLanguage, LANGUAGES } from '../contexts/LanguageContext';
import { useTheme } from '@mui/material/styles';

const LanguageTest: React.FC = () => {
  const { language, setLanguage, translate, languageNames, isLoading } = useLanguage();
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [loadingTest, setLoadingTest] = useState(false);

  // Simulate loading for testing
  useEffect(() => {
    if (loadingTest) {
      const timer = setTimeout(() => {
        setLoadingTest(false);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [loadingTest]);

  const commonKeys = [
    'common.loading',
    'common.success',
    'common.error',
    'common.save',
    'common.cancel',
    'common.delete',
    'common.edit',
    'common.add',
    'common.search',
    'common.filter'
  ];

  const uiKeys = [
    'login.welcome',
    'login.subtitle',
    'login.username',
    'login.password',
    'dashboard.welcome',
    'dashboard.title',
    'dashboard.subtitle',
    'dashboard.modules'
  ];

  const moduleKeys = [
    'sidebar.dashboard',
    'sidebar.animals',
    'sidebar.breeding',
    'sidebar.health',
    'sidebar.feeding',
    'sidebar.financial',
    'sidebar.reports',
    'sidebar.settings',
    'sidebar.logout'
  ];

  const animalKeys = [
    'animals.dashboard',
    'animals.manage',
    'animals.add',
    'animals.total',
    'animals.active',
    'animals.healthy',
    'animals.animals',
    'animals.registry',
    'animals.featured',
    'animals.view_all'
  ];

  const chartKeys = [
    'chart.no_data',
    'chart.change_type',
    'chart.type.bar',
    'chart.type.line',
    'chart.type.area',
    'chart.type.pie',
    'chart.time_range.day',
    'chart.time_range.week',
    'chart.time_range.month'
  ];

  const allTabs = [
    { name: 'Common', keys: commonKeys },
    { name: 'UI', keys: uiKeys },
    { name: 'Modules', keys: moduleKeys },
    { name: 'Animals', keys: animalKeys },
    { name: 'Charts', keys: chartKeys }
  ];

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: theme.shadows[3] }}>
        <CardContent>
          <Typography variant="h4" gutterBottom color="primary">
            Language Translation Test
          </Typography>
          <Typography variant="body1" paragraph>
            This page tests the translation functionality for different languages. Select a language below to see how the UI elements are translated.
          </Typography>

          <Divider sx={{ my: 2 }} />

          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h6" gutterBottom>
                Current Language: <strong>{languageNames[language]}</strong> ({language})
              </Typography>

              <Typography variant="body2" gutterBottom>
                Status: {isLoading ?
                  <Alert severity="info" sx={{ display: 'inline-flex', py: 0 }}>Loading translations...</Alert> :
                  <Alert severity="success" sx={{ display: 'inline-flex', py: 0 }}>Translations loaded</Alert>
                }
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>
                Select Language:
              </Typography>
              <Grid container spacing={1}>
                {Object.entries(LANGUAGES).map(([name, code]) => (
                  <Grid item key={code}>
                    <CustomButton
                      variant={language === code ? 'contained' : 'outlined'}
                      onClick={() => setLanguage(code)}
                      size="small"
                      sx={{
                        minWidth: 100,
                        bgcolor: language === code ? theme.palette.primary.main : 'transparent',
                        '&:hover': {
                          bgcolor: language === code ? theme.palette.primary.dark : 'rgba(0,0,0,0.04)'
                        }
                      }}
                    >
                      {languageNames[code]}
                    </CustomButton>
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      <Card sx={{ mb: 4, borderRadius: 2, boxShadow: theme.shadows[3] }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            Translation Categories
          </Typography>

          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
            >
              {allTabs.map((tab, index) => (
                <Tab label={tab.name} key={index} />
              ))}
            </Tabs>
          </Box>

          <Grid container spacing={2}>
            {allTabs[activeTab].keys.map(key => (
              <Grid item xs={12} sm={6} md={4} key={key}>
                <Paper
                  sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      boxShadow: theme.shadows[4],
                      transform: 'translateY(-2px)'
                    }
                  }}
                >
                  <Typography variant="caption" display="block" color="text.secondary" gutterBottom>
                    {key}
                  </Typography>
                  <Typography variant="body1" sx={{ fontWeight: 500, flexGrow: 1 }}>
                    {translate(key)}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      <Card sx={{ borderRadius: 2, boxShadow: theme.shadows[3] }}>
        <CardContent>
          <Typography variant="h5" gutterBottom color="primary">
            Live Translation Demo
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, mb: 2, bgcolor: theme.palette.background.default }}>
                <Typography variant="subtitle1" gutterBottom>
                  {translate('login.welcome')}
                </Typography>
                <Typography variant="body2" paragraph>
                  {translate('login.subtitle')}
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <CustomButton variant="contained" color="primary" fullWidth>
                    {translate('login.signin')}
                  </CustomButton>
                  <CustomButton variant="outlined" color="primary" fullWidth>
                    {translate('common.cancel')}
                  </CustomButton>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper sx={{ p: 2, bgcolor: theme.palette.background.default }}>
                <Typography variant="subtitle1" gutterBottom>
                  {translate('animals.dashboard')}
                </Typography>
                <Typography variant="body2" paragraph>
                  {translate('animals.manage')}
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                  <CustomButton
                    variant="contained"
                    color="primary"
                    size="small"
                    onClick={() => setLoadingTest(true)}
                  >
                    {loadingTest ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      translate('common.refresh')
                    )}
                  </CustomButton>
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    size="small"
                  >
                    {translate('animals.add')}
                  </CustomButton>
                </Box>

                <Alert severity="info" sx={{ mb: 2 }}>
                  {translate('chart.no_data')}
                </Alert>
              </Paper>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LanguageTest;
