import React from 'react';
import { CustomButton } from '../common';
import { ThemeProvider, createTheme } from '@mui/material';

/**
 * A button component that ensures a safe theme is always provided
 * This helps prevent "Cannot read properties of undefined (reading 'dark')" errors
 */
const ThemeSafeButton: React.FC<CustomButtonProps> = (props) => {
  // Create a fallback theme to use if the main theme context is not available
  const fallbackTheme = createTheme({
    palette: {
      mode: 'dark',
      primary: {
        main: '#3AA99F',
        light: '#4FBEB4',
        dark: '#2A8A82',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#38B2AC',
        light: '#4FD1CB',
        dark: '#2C8A84',
        contrastText: '#ffffff',
      },
    },
  });

  return (
    <ThemeProvider theme={fallbackTheme}>
      <CustomButton {...props} />
    </ThemeProvider>
  );
};

export default ThemeSafeButton;
