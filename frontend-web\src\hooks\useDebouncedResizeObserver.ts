import { useRef, useEffect, useState } from 'react';
import { createDebouncedResizeObserver } from '../utils/resizeObserverFix';

/**
 * Custom hook to use a debounced ResizeObserver
 * This helps prevent "ResizeObserver loop completed with undelivered notifications" errors
 * 
 * @param delay The debounce delay in milliseconds
 * @returns An object with the ref to observe and the size
 */
export const useDebouncedResizeObserver = (delay: number = 100) => {
  const ref = useRef<HTMLElement | null>(null);
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 0,
    height: 0
  });

  useEffect(() => {
    if (!ref.current) return;

    // Create a debounced resize observer
    const observer = createDebouncedResizeObserver((entries) => {
      // Get the first entry
      const entry = entries[entries.length - 1];
      if (!entry) return;

      // Get the content rect
      const { width, height } = entry.contentRect;

      // Update the size
      setSize({ width, height });
    }, delay);

    // Observe the element
    observer.observe(ref.current);

    // Clean up
    return () => {
      observer.disconnect();
    };
  }, [delay]);

  return { ref, size };
};

export default useDebouncedResizeObserver;
