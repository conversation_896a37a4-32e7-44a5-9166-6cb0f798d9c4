/**
 * Supplier Controller
 * 
 * Handles API requests related to suppliers.
 */

const Supplier = require('../models/Supplier');
const SupplierProduct = require('../models/SupplierProduct');
const supplierIntegrationService = require('../services/supplierIntegrationService');
const logger = require('../utils/logger');

/**
 * Get all suppliers
 */
exports.getAllSuppliers = async (req, res) => {
  try {
    const { category, search, limit = 20, page = 1 } = req.query;
    
    // Build query
    const query = {};
    
    // Filter by category if provided
    if (category) {
      query.categories = category;
    }
    
    // Search by text if provided
    if (search) {
      query.$text = { $search: search };
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Execute query
    const suppliers = await Supplier.find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await Supplier.countDocuments(query);
    
    res.status(200).json({
      success: true,
      count: suppliers.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      data: suppliers
    });
  } catch (error) {
    logger.error('Error fetching suppliers:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching suppliers',
      error: error.message
    });
  }
};

/**
 * Get supplier by ID
 */
exports.getSupplierById = async (req, res) => {
  try {
    const supplier = await Supplier.findOne({ id: req.params.id });
    
    if (!supplier) {
      return res.status(404).json({
        success: false,
        message: 'Supplier not found'
      });
    }
    
    // Get featured products
    const products = await SupplierProduct.find({ supplierId: supplier.id }).limit(5);
    
    res.status(200).json({
      success: true,
      data: {
        ...supplier.toObject(),
        featuredProducts: products
      }
    });
  } catch (error) {
    logger.error(`Error fetching supplier ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error fetching supplier',
      error: error.message
    });
  }
};

/**
 * Get supplier products
 */
exports.getSupplierProducts = async (req, res) => {
  try {
    const { category, search, limit = 20, page = 1 } = req.query;
    
    // Build query
    const query = { supplierId: req.params.id };
    
    // Filter by category if provided
    if (category) {
      query.category = category;
    }
    
    // Search by text if provided
    if (search) {
      query.$text = { $search: search };
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Execute query
    const products = await SupplierProduct.find(query)
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));
    
    // Get total count for pagination
    const total = await SupplierProduct.countDocuments(query);
    
    res.status(200).json({
      success: true,
      count: products.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      data: products
    });
  } catch (error) {
    logger.error(`Error fetching products for supplier ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      message: 'Error fetching supplier products',
      error: error.message
    });
  }
};

/**
 * Manually trigger supplier data update
 * (Admin only)
 */
exports.updateSupplierData = async (req, res) => {
  try {
    // Check if specific supplier ID is provided
    const { id } = req.query;
    
    let result;
    
    if (id) {
      // Update specific supplier
      switch (id) {
        case 'afgri-001':
          result = await supplierIntegrationService.processAfgriData();
          break;
        case 'senwes-001':
          result = await supplierIntegrationService.processSenwesData();
          break;
        case 'kaap-agri-001':
          result = await supplierIntegrationService.processKaapAgriData();
          break;
        case 'obaro-001':
          result = await supplierIntegrationService.processObaroData();
          break;
        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid supplier ID'
          });
      }
      
      return res.status(200).json({
        success: true,
        message: `Supplier ${id} data updated successfully`
      });
    } else {
      // Update all suppliers
      result = await supplierIntegrationService.updateSupplierData();
      
      return res.status(200).json(result);
    }
  } catch (error) {
    logger.error('Error updating supplier data:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating supplier data',
      error: error.message
    });
  }
};
