import React, { useState } from 'react';
import { Box, Typography, Grid, Card, CardContent, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, FormControl, InputLabel, Select, MenuItem, TextField, IconButton, Divider, Alert, AlertTitle } from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  Download,
  FilterList,
  Assessment,
  TrendingUp,
  Timeline,
  Print,
  Share,
  Visibility,
  BarChart as BarChartIcon,
  PieChart as PieChartIcon,
  ShowChart,
  Pets,
  LocalHospital,
  AttachMoney,
  Restaurant,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import {  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AnimatedBackgroundCard, BlendedBackgroundCard , CustomButton } from '../../components/common';
import { useAnimalData } from '../../hooks/useAnimalData';
import { useFinancialData } from '../../hooks/useFinancialData';
import { formatCurrency, formatDate, calculateAge } from '../../utils/formatters';
import { generateReport } from '../../utils/reportUtils';
import { useThemeContext } from '../../contexts/ThemeContext';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

const PredictiveAnalysis: React.FC = () => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];
  const { animals, stats, loading: animalsLoading } = useAnimalData();
  const { transactions, stats: financialStats, loading: financialLoading } = useFinancialData();
  
  // State for report generation
  const [reportFormat, setReportFormat] = useState('pdf');
  const [timeRange, setTimeRange] = useState('month');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedModule, setSelectedModule] = useState('all');
  
  // Handle report generation
  const handleGenerateReport = async () => {
    try {
      setIsGenerating(true);
      
      // Generate the appropriate report based on selected module
      let reportData = [];
      let reportTitle = '';
      
      switch (selectedModule) {
        case 'livestock':
          reportData = predictedGrowthData;
          reportTitle = 'Livestock Growth Prediction Report';
          break;
        case 'health':
          reportData = predictedHealthData;
          reportTitle = 'Health Risk Prediction Report';
          break;
        case 'financial':
          reportData = predictedFinancialData;
          reportTitle = 'Financial Forecast Report';
          break;
        case 'feed':
          reportData = predictedFeedData;
          reportTitle = 'Feed Optimization Report';
          break;
        default:
          reportData = [...predictedGrowthData, ...predictedHealthData, ...predictedFinancialData, ...predictedFeedData];
          reportTitle = 'Comprehensive Predictive Analysis Report';
      }
      
      // Generate the report
      await generateReport(
        reportData,
        `predictive_analysis_${selectedModule}`,
        reportFormat as 'csv' | 'excel' | 'pdf',
        reportTitle
      );
      
      // Show success message
      console.log(`${reportTitle} generated successfully`);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  // Mock data for predictive analysis
  const predictedGrowthData = [
    { month: 'Aug', actual: 0, predicted: 2.5 },
    { month: 'Sep', actual: 0, predicted: 2.8 },
    { month: 'Oct', actual: 0, predicted: 3.2 },
    { month: 'Nov', actual: 0, predicted: 3.5 },
    { month: 'Dec', actual: 0, predicted: 3.7 },
    { month: 'Jan', actual: 0, predicted: 3.9 }
  ];
  
  const predictedHealthData = [
    { month: 'Aug', actual: 0, predicted: 5 },
    { month: 'Sep', actual: 0, predicted: 7 },
    { month: 'Oct', actual: 0, predicted: 12 },
    { month: 'Nov', actual: 0, predicted: 8 },
    { month: 'Dec', actual: 0, predicted: 6 },
    { month: 'Jan', actual: 0, predicted: 4 }
  ];
  
  const predictedFinancialData = [
    { month: 'Aug', revenue: 240000, expenses: 160000, profit: 80000 },
    { month: 'Sep', revenue: 255000, expenses: 165000, profit: 90000 },
    { month: 'Oct', revenue: 270000, expenses: 170000, profit: 100000 },
    { month: 'Nov', revenue: 290000, expenses: 175000, profit: 115000 },
    { month: 'Dec', revenue: 320000, expenses: 180000, profit: 140000 },
    { month: 'Jan', revenue: 350000, expenses: 190000, profit: 160000 }
  ];
  
  const predictedFeedData = [
    { month: 'Aug', current: 2600, optimized: 2400, savings: 200 },
    { month: 'Sep', current: 2650, optimized: 2420, savings: 230 },
    { month: 'Oct', current: 2700, optimized: 2450, savings: 250 },
    { month: 'Nov', current: 2750, optimized: 2480, savings: 270 },
    { month: 'Dec', current: 2800, optimized: 2500, savings: 300 },
    { month: 'Jan', current: 2850, optimized: 2520, savings: 330 }
  ];
  
  // Livestock performance radar data
  const performanceRadarData = [
    { subject: 'Growth Rate', A: 120, B: 110, fullMark: 150 },
    { subject: 'Feed Efficiency', A: 98, B: 130, fullMark: 150 },
    { subject: 'Health Score', A: 86, B: 130, fullMark: 150 },
    { subject: 'Reproduction', A: 99, B: 100, fullMark: 150 },
    { subject: 'Meat Quality', A: 85, B: 90, fullMark: 150 },
    { subject: 'Adaptability', A: 65, B: 85, fullMark: 150 }
  ];
  
  // Risk assessment data
  const riskAssessmentData = [
    { name: 'Disease Outbreak', value: 25, color: theme.palette.error.main },
    { name: 'Market Volatility', value: 35, color: theme.palette.warning.main },
    { name: 'Weather Events', value: 20, color: theme.palette.info.main },
    { name: 'Feed Shortage', value: 15, color: theme.palette.success.main },
    { name: 'Operational', value: 5, color: theme.palette.primary.main }
  ];
  
  // Business strategy recommendations
  const businessStrategies = [
    {
      id: 1,
      title: 'Optimize Feed Composition',
      description: 'Adjust feed composition to increase protein content by 5% for improved growth rates.',
      impact: 'High',
      timeframe: 'Short-term',
      roi: '15%',
      category: 'Feed Management'
    },
    {
      id: 2,
      title: 'Implement Selective Breeding',
      description: 'Focus on breeding animals with superior feed conversion efficiency traits.',
      impact: 'High',
      timeframe: 'Long-term',
      roi: '25%',
      category: 'Breeding'
    },
    {
      id: 3,
      title: 'Preventative Health Program',
      description: 'Implement comprehensive vaccination schedule to reduce disease incidence by 30%.',
      impact: 'Medium',
      timeframe: 'Medium-term',
      roi: '18%',
      category: 'Health'
    },
    {
      id: 4,
      title: 'Market Timing Optimization',
      description: 'Adjust sales timing to align with seasonal price peaks in October-November.',
      impact: 'High',
      timeframe: 'Short-term',
      roi: '12%',
      category: 'Financial'
    },
    {
      id: 5,
      title: 'Asset Retirement Planning',
      description: 'Implement structured retirement of low-performing animals to improve overall herd productivity.',
      impact: 'Medium',
      timeframe: 'Medium-term',
      roi: '10%',
      category: 'Asset Management'
    }
  ];

  return (
    <Box>
      <ModuleHeader
        title="Predictive Business Analysis"
        subtitle="AI-powered predictive analytics and business strategy recommendations"
        module="reports"
        submodule="analysis"
        actionLabel={isGenerating ? "Generating..." : "Generate Predictive Report"}
        actionIcon={<Download />}
        onAction={handleGenerateReport}
        disabled={isGenerating}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Report Filters */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            <Grid item xs={12} md={9}>
              <Paper sx={{ p: 2, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <Box display="flex" alignItems="center" flexWrap="wrap" gap={2}>
                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Module</InputLabel>
                    <Select
                      value={selectedModule}
                      label="Module"
                      onChange={(e) => setSelectedModule(e.target.value)}
                    >
                      <MenuItem value="all">All Modules</MenuItem>
                      <MenuItem value="livestock">Livestock</MenuItem>
                      <MenuItem value="health">Health</MenuItem>
                      <MenuItem value="financial">Financial</MenuItem>
                      <MenuItem value="feed">Feed Management</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Time Range</InputLabel>
                    <Select
                      value={timeRange}
                      label="Time Range"
                      onChange={(e) => setTimeRange(e.target.value)}
                    >
                      <MenuItem value="month">1 Month</MenuItem>
                      <MenuItem value="quarter">3 Months</MenuItem>
                      <MenuItem value="halfyear">6 Months</MenuItem>
                      <MenuItem value="year">1 Year</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl size="small" sx={{ minWidth: 150 }}>
                    <InputLabel>Report Format</InputLabel>
                    <Select
                      value={reportFormat}
                      label="Report Format"
                      onChange={(e) => setReportFormat(e.target.value)}
                    >
                      <MenuItem value="pdf">PDF</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="csv">CSV</MenuItem>
                    </Select>
                  </FormControl>

                  <CustomButton
                    variant="contained"
                    startIcon={<FilterList />}
                    sx={{ bgcolor: theme.palette.primary.main }}
                  >
                    Apply Filters
                  </CustomButton>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} md={3}>
              <Paper sx={{ p: 2, borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Box display="flex" gap={2}>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Print />}
                    sx={{ borderColor: theme.palette.primary.main, color: theme.palette.primary.main }}
                  >
                    Print
                  </CustomButton>
                  <CustomButton
                    variant="outlined"
                    startIcon={<Share />}
                    sx={{ borderColor: theme.palette.primary.main, color: theme.palette.primary.main }}
                  >
                    Share
                  </CustomButton>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </motion.div>

        {/* Executive Summary */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Paper
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  mr: 2
                }}
              >
                <Assessment />
              </Box>
              <Typography variant="h5" fontWeight="bold">
                Executive Summary
              </Typography>
            </Box>

            <Typography variant="body1" paragraph>
              Based on comprehensive analysis of your farm data, our AI-powered predictive models have identified several key opportunities for optimization and growth. The following insights are tailored to your specific operation and market conditions.
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
              <AlertTitle>Key Prediction</AlertTitle>
              Your farm is projected to achieve a <strong>15% increase in overall productivity</strong> and a <strong>22% increase in profitability</strong> over the next 6 months by implementing the recommended strategies.
            </Alert>

            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <Pets fontSize="small" color="primary" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" fontWeight="bold" color="primary.main">
                      Livestock Growth
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Predicted <strong>12% increase</strong> in growth rates with optimized feed composition.
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <LocalHospital fontSize="small" color="error" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" fontWeight="bold" color="error.main">
                      Health Management
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Potential to <strong>reduce disease incidence by 30%</strong> with preventative measures.
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <AttachMoney fontSize="small" color="success" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" fontWeight="bold" color="success.main">
                      Financial Forecast
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Projected <strong>R160,000 profit</strong> in January with optimized market timing.
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={12} md={3}>
                <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, height: '100%' }}>
                  <Box display="flex" alignItems="center" mb={1}>
                    <Restaurant fontSize="small" color="warning" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" fontWeight="bold" color="warning.main">
                      Feed Optimization
                    </Typography>
                  </Box>
                  <Typography variant="body2">
                    Potential <strong>12% feed cost reduction</strong> with optimized feed composition.
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </motion.div>

        {/* Predictive Charts */}
        <Typography variant="h5" fontWeight="bold" mb={3}>
          Predictive Analysis
        </Typography>

        <Grid container spacing={3} mb={4}>
          {/* Financial Forecast */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Financial Forecast (6 Months)"
                icon={<AttachMoney />}
                accentColor={themeColor.primary}
                height="100%"
              >
                <Box mt={2} height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={predictedFinancialData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `R${value / 1000}k`} />
                      <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                      <Legend />
                      <Bar dataKey="revenue" name="Revenue" fill={theme.palette.primary.main} />
                      <Bar dataKey="expenses" name="Expenses" fill={theme.palette.error.main} />
                      <Bar dataKey="profit" name="Profit" fill={theme.palette.success.main} />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          {/* Feed Optimization */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Feed Cost Optimization"
                icon={<Restaurant />}
                accentColor={themeColor.primary}
                height="100%"
              >
                <Box mt={2} height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={predictedFeedData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis tickFormatter={(value) => `R${value}`} />
                      <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                      <Legend />
                      <Area type="monotone" dataKey="current" name="Current Feed Cost" stroke={theme.palette.error.main} fill={alpha(theme.palette.error.main, 0.5)} />
                      <Area type="monotone" dataKey="optimized" name="Optimized Feed Cost" stroke={theme.palette.success.main} fill={alpha(theme.palette.success.main, 0.5)} />
                      <Area type="monotone" dataKey="savings" name="Potential Savings" stroke={theme.palette.info.main} fill={alpha(theme.palette.info.main, 0.5)} />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          {/* Livestock Performance Radar */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Livestock Performance Comparison"
                icon={<Pets />}
                accentColor={themeColor.primary}
                height="100%"
              >
                <Box mt={2} height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart outerRadius={90} data={performanceRadarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={30} domain={[0, 150]} />
                      <Radar name="Current Performance" dataKey="A" stroke={theme.palette.primary.main} fill={alpha(theme.palette.primary.main, 0.5)} fillOpacity={0.6} />
                      <Radar name="Predicted Optimal" dataKey="B" stroke={theme.palette.success.main} fill={alpha(theme.palette.success.main, 0.5)} fillOpacity={0.6} />
                      <Legend />
                      <Tooltip />
                    </RadarChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>

          {/* Risk Assessment */}
          <Grid item xs={12} md={6}>
            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Risk Assessment"
                icon={<Warning />}
                accentColor={themeColor.primary}
                height="100%"
              >
                <Box mt={2} height={300}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={riskAssessmentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {riskAssessmentData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, 'Risk Level']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </AnimatedBackgroundCard>
            </motion.div>
          </Grid>
        </Grid>

        {/* Business Strategy Recommendations */}
        <Typography variant="h5" fontWeight="bold" mb={3}>
          Recommended Business Strategies
        </Typography>

        <TableContainer component={Paper} sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
          <Table>
            <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
              <TableRow>
                <TableCell>Strategy</TableCell>
                <TableCell>Description</TableCell>
                <TableCell>Impact</TableCell>
                <TableCell>Timeframe</TableCell>
                <TableCell>Expected ROI</TableCell>
                <TableCell>Category</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {businessStrategies.map((strategy) => (
                <TableRow key={strategy.id} hover>
                  <TableCell>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {strategy.title}
                    </Typography>
                  </TableCell>
                  <TableCell>{strategy.description}</TableCell>
                  <TableCell>
                    <Chip
                      label={strategy.impact}
                      color={
                        strategy.impact === 'High' ? 'success' :
                        strategy.impact === 'Medium' ? 'warning' : 'default'
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{strategy.timeframe}</TableCell>
                  <TableCell>{strategy.roi}</TableCell>
                  <TableCell>
                    <Chip
                      label={strategy.category}
                      variant="outlined"
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Implementation Plan */}
        <Typography variant="h5" fontWeight="bold" mb={3}>
          Implementation Plan
        </Typography>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <Typography variant="h6" gutterBottom>
                Phased Approach to Optimization
              </Typography>
              
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Phase 1: Immediate Actions (0-30 days)
                </Typography>
                <Typography variant="body2" paragraph>
                  • Implement feed composition adjustments to increase protein content<br />
                  • Begin monitoring market prices daily to identify optimal selling windows<br />
                  • Schedule comprehensive health assessments for all animals<br />
                  • Identify low-performing animals for potential retirement
                </Typography>
                
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Phase 2: Medium-Term Implementation (30-90 days)
                </Typography>
                <Typography variant="body2" paragraph>
                  • Roll out comprehensive vaccination program<br />
                  • Begin selective breeding program focusing on feed efficiency traits<br />
                  • Implement asset retirement plan for identified low-performing animals<br />
                  • Establish performance benchmarking system
                </Typography>
                
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Phase 3: Long-Term Strategy (90+ days)
                </Typography>
                <Typography variant="body2" paragraph>
                  • Evaluate results of initial feed optimization and refine as needed<br />
                  • Expand selective breeding program based on initial results<br />
                  • Implement advanced health monitoring technologies<br />
                  • Develop comprehensive risk management strategies
                </Typography>
              </Box>
              
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <CustomButton
                  variant="contained"
                  startIcon={<Download />}
                  onClick={() => handleGenerateReport()}
                >
                  Download Implementation Plan
                </CustomButton>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default PredictiveAnalysis;
