import React, { useState } from 'react';
import { useTheme, alpha } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { TextField, Select, MenuItem, FormControl, InputLabel, Paper, Grid, Alert } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import {  withSubModuleTranslation , CustomButton } from '../common';

interface AnimalFormData {
  tagNumber: string;
  species: string;
  breed: string;
  dateOfBirth: Date | null;
  gender: string;
  source: string;
  purchaseDate?: Date | null;
  purchasePrice?: number;
  currentLocation: string;
  notes?: string;
}

const initialFormData: AnimalFormData = {
  tagNumber: '',
  species: '',
  breed: '',
  dateOfBirth: null,
  gender: '',
  source: '',
  purchaseDate: null,
  purchasePrice: 0,
  currentLocation: '',
  notes: ''
};

interface AnimalFormProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const AnimalForm: React.FC<AnimalFormProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const [formData, setFormData] = useState<AnimalFormData>(initialFormData);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const theme = useTheme();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${process.env.REACT_APP_API_URL}/api/animals`, formData);
      navigate('/animals');
    } catch (err) {
      setError(translate ? translate('animals.form.error_create', { fallback: 'Failed to create animal record' }) : 'Failed to create animal record');
      console.error('Error creating animal:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (field: keyof AnimalFormData) => (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  return (
    <div className="p-4">
      <Paper sx={{
        p: 3,
        borderRadius: 2,
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        backgroundColor: alpha(theme.palette.primary.main, 0.03),
        borderLeft: `4px solid ${theme.palette.primary.main}`
      }}>
        <h1 className="text-2xl font-bold mb-6">
          {translateSubModule ? translateSubModule('title', 'Add New Animal') : 'Add New Animal'}
        </h1>

        {error && (
          <Alert severity="error" className="mb-4">
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label={translateModuleField ? translateModuleField('tag_number', 'Tag Number') : 'Tag Number'}
                value={formData.tagNumber}
                onChange={handleChange('tagNumber')}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>{translateModuleField ? translateModuleField('species', 'Species') : 'Species'}</InputLabel>
                <Select
                  value={formData.species}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    species: e.target.value
                  }))}
                >
                  <MenuItem value="cattle">{translate ? translate('animals.species.cattle', { fallback: 'Cattle' }) : 'Cattle'}</MenuItem>
                  <MenuItem value="sheep">{translate ? translate('animals.species.sheep', { fallback: 'Sheep' }) : 'Sheep'}</MenuItem>
                  <MenuItem value="goat">{translate ? translate('animals.species.goat', { fallback: 'Goat' }) : 'Goat'}</MenuItem>
                  <MenuItem value="pig">{translate ? translate('animals.species.pig', { fallback: 'Pig' }) : 'Pig'}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label={translateModuleField ? translateModuleField('breed', 'Breed') : 'Breed'}
                value={formData.breed}
                onChange={handleChange('breed')}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <DatePicker
                label={translateModuleField ? translateModuleField('date_of_birth', 'Date of Birth') : 'Date of Birth'}
                value={formData.dateOfBirth}
                onChange={(date) => setFormData(prev => ({
                  ...prev,
                  dateOfBirth: date
                }))}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>{translateModuleField ? translateModuleField('gender', 'Gender') : 'Gender'}</InputLabel>
                <Select
                  value={formData.gender}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    gender: e.target.value
                  }))}
                >
                  <MenuItem value="male">{translate ? translate('animals.gender.male', { fallback: 'Male' }) : 'Male'}</MenuItem>
                  <MenuItem value="female">{translate ? translate('animals.gender.female', { fallback: 'Female' }) : 'Female'}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={translateModuleField ? translateModuleField('current_location', 'Current Location') : 'Current Location'}
                value={formData.currentLocation}
                onChange={handleChange('currentLocation')}
                required
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label={translateModuleField ? translateModuleField('notes', 'Notes') : 'Notes'}
                value={formData.notes}
                onChange={handleChange('notes')}
              />
            </Grid>

            <Grid item xs={12} className="flex justify-end gap-4">
              <CustomButton
                variant="outlined"
                onClick={() => navigate('/animals')}
                disabled={loading}
              >
                {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}
              </CustomButton>
              <CustomButton
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
              >
                {loading
                  ? (translate ? translate('common.saving', { fallback: 'Saving...' }) : 'Saving...')
                  : (translateSubModule ? translateSubModule('save_button', 'Save Animal') : 'Save Animal')}
              </CustomButton>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </div>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(AnimalForm, 'animals', 'form');