const { MongoClient } = require('mongodb');
const { v4: uuidv4 } = require('uuid');

// Simple logger implementation
const logger = {
  info: (message) => console.log(`${new Date().toISOString()} info: ${message}`),
  error: (message, error) => console.error(`${new Date().toISOString()} error: ${message}`, error)
};

const uri = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Define categories globally so they can be accessed in other functions
const categories = [
  { name: 'feed', plannedExpenses: 45000 },
  { name: 'veterinary', plannedExpenses: 25000 },
  { name: 'labor', plannedExpenses: 100000 },
  { name: 'equipment', plannedExpenses: 30000 },
  { name: 'infrastructure', plannedExpenses: 40000 }
];

// Financial data with balanced records
const generateFinancialRecords = () => {
  const currentYear = new Date().getFullYear();
  const records = [];

  // Generate monthly records for the current year
  for (let month = 0; month < 12; month++) {
    const date = new Date(currentYear, month, 15);

    // Income records
    records.push({
      _id: uuidv4(),
      type: 'income',
      category: 'sales',
      subcategory: 'livestock',
      amount: 12500 + Math.floor(Math.random() * 5000),
      date: date,
      description: `Livestock sales for ${date.toLocaleString('default', { month: 'long' })}`,
      paymentMethod: 'bank transfer',
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    records.push({
      _id: uuidv4(),
      type: 'income',
      category: 'sales',
      subcategory: 'products',
      amount: 5000 + Math.floor(Math.random() * 2000),
      date: date,
      description: `Product sales for ${date.toLocaleString('default', { month: 'long' })}`,
      paymentMethod: 'cash',
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Expense records
    records.push({
      _id: uuidv4(),
      type: 'expense',
      category: 'feed',
      subcategory: 'hay',
      amount: 3500 + Math.floor(Math.random() * 1000),
      date: date,
      description: `Feed expenses for ${date.toLocaleString('default', { month: 'long' })}`,
      paymentMethod: 'bank transfer',
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    records.push({
      _id: uuidv4(),
      type: 'expense',
      category: 'veterinary',
      subcategory: 'routine care',
      amount: 2000 + Math.floor(Math.random() * 1000),
      date: date,
      description: `Veterinary expenses for ${date.toLocaleString('default', { month: 'long' })}`,
      paymentMethod: 'credit card',
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    records.push({
      _id: uuidv4(),
      type: 'expense',
      category: 'labor',
      subcategory: 'salaries',
      amount: 8000 + Math.floor(Math.random() * 1000),
      date: date,
      description: `Labor expenses for ${date.toLocaleString('default', { month: 'long' })}`,
      paymentMethod: 'bank transfer',
      status: 'completed',
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  // Add some capital expenses
  records.push({
    _id: uuidv4(),
    type: 'expense',
    category: 'equipment',
    subcategory: 'machinery',
    amount: 25000,
    date: new Date(currentYear, 2, 10),
    description: 'Purchase of new tractor',
    paymentMethod: 'bank loan',
    status: 'completed',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  records.push({
    _id: uuidv4(),
    type: 'expense',
    category: 'infrastructure',
    subcategory: 'buildings',
    amount: 35000,
    date: new Date(currentYear, 5, 20),
    description: 'Barn renovation',
    paymentMethod: 'bank transfer',
    status: 'completed',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  // Add some investment income
  records.push({
    _id: uuidv4(),
    type: 'income',
    category: 'investment',
    subcategory: 'dividends',
    amount: 15000,
    date: new Date(currentYear, 6, 15),
    description: 'Annual dividend payment',
    paymentMethod: 'bank transfer',
    status: 'completed',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  return records;
};

// Generate financial metrics
const generateFinancialMetrics = (financialRecords) => {
  const currentYear = new Date().getFullYear();
  const metrics = [];

  // Calculate total income and expenses
  let totalIncome = 0;
  let totalExpenses = 0;

  financialRecords.forEach(record => {
    if (record.type === 'income') {
      totalIncome += record.amount;
    } else if (record.type === 'expense') {
      totalExpenses += record.amount;
    }
  });

  const netProfit = totalIncome - totalExpenses;
  const profitMargin = (netProfit / totalIncome) * 100;

  // Annual metrics
  metrics.push({
    _id: uuidv4(),
    year: currentYear,
    period: 'annual',
    totalIncome: totalIncome,
    totalExpenses: totalExpenses,
    netProfit: netProfit,
    profitMargin: profitMargin.toFixed(2),
    roi: ((netProfit / totalExpenses) * 100).toFixed(2),
    createdAt: new Date(),
    updatedAt: new Date()
  });

  // Monthly metrics
  for (let month = 0; month < 12; month++) {
    const monthlyRecords = financialRecords.filter(record => {
      return record.date.getMonth() === month;
    });

    let monthlyIncome = 0;
    let monthlyExpenses = 0;

    monthlyRecords.forEach(record => {
      if (record.type === 'income') {
        monthlyIncome += record.amount;
      } else if (record.type === 'expense') {
        monthlyExpenses += record.amount;
      }
    });

    const monthlyNetProfit = monthlyIncome - monthlyExpenses;
    const monthlyProfitMargin = monthlyIncome > 0 ? (monthlyNetProfit / monthlyIncome) * 100 : 0;

    metrics.push({
      _id: uuidv4(),
      year: currentYear,
      month: month,
      period: 'monthly',
      totalIncome: monthlyIncome,
      totalExpenses: monthlyExpenses,
      netProfit: monthlyNetProfit,
      profitMargin: monthlyProfitMargin.toFixed(2),
      roi: monthlyExpenses > 0 ? ((monthlyNetProfit / monthlyExpenses) * 100).toFixed(2) : 0,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }

  return metrics;
};

// Generate budget data
const generateBudgetData = () => {
  const currentYear = new Date().getFullYear();
  const budgets = [];

  // Annual budget
  budgets.push({
    _id: uuidv4(),
    year: currentYear,
    period: 'annual',
    category: 'overall',
    plannedIncome: 250000,
    plannedExpenses: 180000,
    plannedProfit: 70000,
    actualIncome: 0, // Will be updated later
    actualExpenses: 0, // Will be updated later
    actualProfit: 0, // Will be updated later
    variance: 0, // Will be updated later
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date()
  });

  // Category budgets
  categories.forEach(category => {
    budgets.push({
      _id: uuidv4(),
      year: currentYear,
      period: 'annual',
      category: category.name,
      plannedExpenses: category.plannedExpenses,
      actualExpenses: 0, // Will be updated later
      variance: 0, // Will be updated later
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });
  });

  return budgets;
};

// Main function to populate financial data
async function populateFinancialData() {
  let client;

  try {
    logger.info('Connecting to MongoDB...');
    client = new MongoClient(uri);
    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);

    // Generate financial records
    const financialRecords = generateFinancialRecords();

    // Drop existing financial_records collection
    logger.info('Dropping existing financial_records collection');
    await db.collection('financial_records').drop().catch(() => {
      logger.info('No existing financial_records collection to drop');
    });

    // Insert financial records
    logger.info(`Inserting ${financialRecords.length} financial records`);
    await db.collection('financial_records').insertMany(financialRecords);

    // Generate and insert financial metrics
    const financialMetrics = generateFinancialMetrics(financialRecords);

    // Drop existing financial_metrics collection
    logger.info('Dropping existing financial_metrics collection');
    await db.collection('financial_metrics').drop().catch(() => {
      logger.info('No existing financial_metrics collection to drop');
    });

    // Insert financial metrics
    logger.info(`Inserting ${financialMetrics.length} financial metrics`);
    await db.collection('financial_metrics').insertMany(financialMetrics);

    // Generate and insert budget data
    const budgetData = generateBudgetData();

    // Drop existing budget collection
    logger.info('Dropping existing budget collection');
    await db.collection('budget').drop().catch(() => {
      logger.info('No existing budget collection to drop');
    });

    // Insert budget data
    logger.info(`Inserting ${budgetData.length} budget records`);
    await db.collection('budget').insertMany(budgetData);

    // Update budget with actual values
    logger.info('Updating budget with actual values');

    // Calculate total income and expenses
    let totalIncome = 0;
    let totalExpenses = 0;

    financialRecords.forEach(record => {
      if (record.type === 'income') {
        totalIncome += record.amount;
      } else if (record.type === 'expense') {
        totalExpenses += record.amount;
      }
    });

    const netProfit = totalIncome - totalExpenses;

    // Update overall budget
    await db.collection('budget').updateOne(
      { period: 'annual', category: 'overall' },
      {
        $set: {
          actualIncome: totalIncome,
          actualExpenses: totalExpenses,
          actualProfit: netProfit,
          variance: netProfit - 70000,
          updatedAt: new Date()
        }
      }
    );

    // Update category budgets
    for (const category of categories) {
      const categoryExpenses = financialRecords
        .filter(record => record.type === 'expense' && record.category === category.name)
        .reduce((sum, record) => sum + record.amount, 0);

      await db.collection('budget').updateOne(
        { period: 'annual', category: category.name },
        {
          $set: {
            actualExpenses: categoryExpenses,
            variance: category.plannedExpenses - categoryExpenses,
            updatedAt: new Date()
          }
        }
      );
    }

    logger.info('Financial data population completed successfully');
  } catch (err) {
    logger.error('Error populating financial data:', err);
  } finally {
    if (client) {
      await client.close();
      logger.info('MongoDB connection closed');
    }
  }
}

// Run the population script
populateFinancialData();
