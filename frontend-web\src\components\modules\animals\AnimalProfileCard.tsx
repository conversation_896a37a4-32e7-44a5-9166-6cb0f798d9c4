import React from 'react';
import { Card, CardContent, Typography, Box, Chip, Grid, IconButton, Tooltip, useTheme, alpha } from '@mui/material';
import { CommercialBackgroundCard } from '../../components/common';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  LocalHospital as HealthIcon,
  Restaurant as FeedIcon,
  Pets as BreedingIcon
} from '@mui/icons-material';
import { Animal } from '../../../types/animal';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../../../constants/routes';

// Default placeholder image - small size for better performance
const DEFAULT_IMAGE = '/images/modules/animals/cattle-1.jpeg';

// Helper function to get animal image based on species and breed
const getAnimalImage = (animal: Animal) => {
  // If animal has an imageUrl, use it
  if (animal.imageUrl) {
    return animal.imageUrl;
  }

  // Otherwise, use a default image based on species
  const species = animal.species?.toLowerCase() || animal.type?.toLowerCase() || 'cattle';

  switch (species) {
    case 'cattle':
      return '/images/modules/animals/cattle-1.jpeg';
    case 'sheep':
      return '/images/modules/animals/cattle-2.avif';
    case 'goat':
      return '/images/modules/animals/cattle-3.jpeg';
    case 'game':
      return '/images/modules/animals/cattle-4.jpeg';
    case 'horse':
      return '/images/modules/animals/cattle-5.avif';
    default:
      return DEFAULT_IMAGE;
  }
};

// Health status colors
const HEALTH_STATUS_COLORS = {
  healthy: 'success',
  sick: 'error',
  injured: 'warning',
  pregnant: 'info'
};

interface AnimalProfileCardProps {
  animal: Animal;
  onEdit?: (animal: Animal) => void;
  onDelete?: (animal: Animal) => void;
  onView?: (animal: Animal) => void;
  showActions?: boolean;
  showRelatedActions?: boolean;
  compact?: boolean;
}

const AnimalProfileCard: React.FC<AnimalProfileCardProps> = ({
  animal,
  onEdit,
  onDelete,
  onView,
  showActions = true,
  showRelatedActions = false,
  compact = false
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Handle health record navigation
  const handleHealthClick = () => {
    navigate(`${ROUTES.ANIMALS}/health?animalId=${animal.id}`);
  };

  // Handle feeding record navigation
  const handleFeedingClick = () => {
    navigate(`${ROUTES.ANIMALS}/feeding?animalId=${animal.id}`);
  };

  // Handle breeding record navigation
  const handleBreedingClick = () => {
    navigate(`${ROUTES.ANIMALS}/breeding?animalId=${animal.id}`);
  };

  // Determine card height based on compact mode - using smaller images for better performance
  const cardHeight = compact ? 300 : 380;
  const imageHeight = compact ? 100 : 150;

  return (
    <CommercialBackgroundCard
      images={[
        '/images/modules/animals/cattle-1.jpeg',
        '/images/modules/animals/cattle-2.avif',
        '/images/modules/animals/cattle-3.jpeg',
        '/images/modules/animals/cattle-4.jpeg',
        '/images/modules/animals/cattle-5.avif'
      ]}
      interval={10000}
      primaryColor={theme.palette.primary.main}
      secondaryColor={theme.palette.primary.dark}
      opacity={0.7}
      height={cardHeight}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
        }
      }}
    >
      <Box sx={{ position: 'relative', mb: 2 }}>

        {/* Tag number overlay */}
        <Box
          sx={{
            display: 'inline-block',
            bgcolor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            borderRadius: 1,
            px: 1.5,
            py: 0.5,
            backdropFilter: 'blur(4px)',
            mr: 1,
            fontWeight: 'bold'
          }}
        >
          <Typography variant="body2" fontWeight="bold">
            {animal.tagNumber}
          </Typography>
        </Box>

        {/* Health status chip */}
        <Chip
          label={animal.healthStatus}
          color={HEALTH_STATUS_COLORS[animal.healthStatus] as any}
          size="small"
          sx={{
            fontWeight: 'bold',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(4px)',
            color: 'white'
          }}
        />
      </Box>

      <Box sx={{ flexGrow: 1, pb: 1, px: 2 }}>
        <Typography
          variant="h6"
          gutterBottom
          noWrap
          sx={{
            fontWeight: 600,
            color: 'white',
            borderBottom: `1px solid rgba(255, 255, 255, 0.2)`,
            pb: 1
          }}
        >
          {animal.name}
        </Typography>

        <Grid container spacing={1}>
          <Grid item xs={6}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Type:
            </Typography>
            <Typography variant="body2" fontWeight="medium" noWrap sx={{ color: 'white' }}>
              {animal.species || animal.type}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Breed:
            </Typography>
            <Typography variant="body2" fontWeight="medium" noWrap sx={{ color: 'white' }}>
              {animal.breed}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Gender:
            </Typography>
            <Typography variant="body2" fontWeight="medium" sx={{ color: 'white' }}>
              {animal.gender}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              Weight:
            </Typography>
            <Typography variant="body2" fontWeight="medium" sx={{ color: 'white' }}>
              {animal.weight} kg
            </Typography>
          </Grid>

          {!compact && (
            <>
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Status:
                </Typography>
                <Typography variant="body2" fontWeight="medium" sx={{ color: 'white' }}>
                  {animal.status}
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
                  Location:
                </Typography>
                <Typography variant="body2" fontWeight="medium" noWrap sx={{ color: 'white' }}>
                  {animal.location}
                </Typography>
              </Grid>
            </>
          )}
        </Grid>
      </Box>

      {/* Action buttons */}
      {showActions && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            p: 1,
            borderTop: `1px solid rgba(255, 255, 255, 0.2)`,
            mt: 2
          }}
        >
          <Box>
            {onView && (
              <Tooltip title="View Details">
                <IconButton size="small" onClick={() => onView(animal)} sx={{ color: 'white' }}>
                  <ViewIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {onEdit && (
              <Tooltip title="Edit">
                <IconButton size="small" onClick={() => onEdit(animal)} sx={{ color: 'white' }}>
                  <EditIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
            {onDelete && (
              <Tooltip title="Delete">
                <IconButton size="small" onClick={() => onDelete(animal)} sx={{ color: 'white' }}>
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          {showRelatedActions && (
            <Box>
              <Tooltip title="Health Records">
                <IconButton size="small" onClick={handleHealthClick} sx={{ color: 'white' }}>
                  <HealthIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Feeding Records">
                <IconButton size="small" onClick={handleFeedingClick} sx={{ color: 'white' }}>
                  <FeedIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              <Tooltip title="Breeding Records">
                <IconButton size="small" onClick={handleBreedingClick} sx={{ color: 'white' }}>
                  <BreedingIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>
      )}
    </CommercialBackgroundCard>
  );
};

export default AnimalProfileCard;
