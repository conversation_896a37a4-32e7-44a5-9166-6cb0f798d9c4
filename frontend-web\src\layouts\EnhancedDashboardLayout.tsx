import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Box, Drawer, AppBar, Toolbar, IconButton, Typography, Badge, Avatar, useMediaQuery, Menu, MenuItem, ListItemIcon, ListItemText, Divider, Tooltip, alpha, useTheme as useMuiTheme } from '@mui/material';
import { 
  Menu as MenuIcon, 
  Notifications, 
  Settings, 
  Search, 
  DarkMode, 
  LightMode,
  AccountCircle,
  Logout,
  Dashboard,
  Help,
  Translate,
  Brightness4,
  Brightness7,
  NotificationsActive,
  NotificationsOff,
  MoreVert
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import Sidebar from '../components/Sidebar';
import LanguageSelector from '../components/LanguageSelector';
import { GlassCard } from '../design/DesignSystem';

// Import new components
import GlobalSearch from '../components/GlobalSearch';
import NotificationsPanel from '../components/NotificationsPanel';
import WeatherWidget from '../components/WeatherWidget';
import QuickActions from '../components/QuickActions';

const EnhancedDashboardLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [weatherOpen, setWeatherOpen] = useState(false);
  const [quickActionsOpen, setQuickActionsOpen] = useState(false);
  
  // Menu anchors
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);
  const [mobileMenuAnchor, setMobileMenuAnchor] = useState<null | HTMLElement>(null);
  
  const location = useLocation();
  const navigate = useNavigate();
  const muiTheme = useMuiTheme();
  const { theme, toggleMode, currentColor } = useThemeContext();
  const { translate, language, changeLanguage } = useLanguage();
  const { user, logout } = useAuth();
  
  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'));
  const isTablet = useMediaQuery(muiTheme.breakpoints.down('lg'));
  
  // Close sidebar when route changes on mobile
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [location.pathname, isMobile]);
  
  // Handle profile menu
  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setProfileMenuAnchor(event.currentTarget);
  };
  
  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };
  
  // Handle mobile menu
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMobileMenuAnchor(event.currentTarget);
  };
  
  const handleMobileMenuClose = () => {
    setMobileMenuAnchor(null);
  };
  
  // Handle logout
  const handleLogout = () => {
    handleProfileMenuClose();
    logout();
    navigate('/login');
  };
  
  // Toggle sidebar
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  
  // Toggle search
  const toggleSearch = () => {
    setSearchOpen(!searchOpen);
  };
  
  // Toggle notifications
  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
  };
  
  // Toggle weather widget
  const toggleWeather = () => {
    setWeatherOpen(!weatherOpen);
  };
  
  // Toggle quick actions
  const toggleQuickActions = () => {
    setQuickActionsOpen(!quickActionsOpen);
  };
  
  // Profile menu
  const renderProfileMenu = (
    <Menu
      anchorEl={profileMenuAnchor}
      id="profile-menu"
      keepMounted
      open={Boolean(profileMenuAnchor)}
      onClose={handleProfileMenuClose}
      PaperProps={{
        elevation: 0,
        sx: {
          overflow: 'visible',
          filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.15))',
          mt: 1.5,
          borderRadius: 2,
          minWidth: 220,
          '&:before': {
            content: '""',
            display: 'block',
            position: 'absolute',
            top: 0,
            right: 14,
            width: 10,
            height: 10,
            bgcolor: 'background.paper',
            transform: 'translateY(-50%) rotate(45deg)',
            zIndex: 0,
          },
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <Box sx={{ px: 2, py: 1.5 }}>
        <Typography variant="subtitle1" fontWeight="bold">
          {user?.firstName} {user?.lastName}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {user?.email}
        </Typography>
      </Box>
      
      <Divider />
      
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/settings/profile'); }}>
        <ListItemIcon>
          <AccountCircle fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('nav.profile')}</ListItemText>
      </MenuItem>
      
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/dashboard'); }}>
        <ListItemIcon>
          <Dashboard fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('nav.dashboard')}</ListItemText>
      </MenuItem>
      
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/settings'); }}>
        <ListItemIcon>
          <Settings fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('nav.settings')}</ListItemText>
      </MenuItem>
      
      <Divider />
      
      <MenuItem onClick={toggleMode}>
        <ListItemIcon>
          {theme.palette.mode === 'dark' ? (
            <Brightness7 fontSize="small" />
          ) : (
            <Brightness4 fontSize="small" />
          )}
        </ListItemIcon>
        <ListItemText>
          {theme.palette.mode === 'dark' 
            ? translate('settings.theme.light_mode') 
            : translate('settings.theme.dark_mode')}
        </ListItemText>
      </MenuItem>
      
      <MenuItem onClick={handleLogout}>
        <ListItemIcon>
          <Logout fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('nav.logout')}</ListItemText>
      </MenuItem>
    </Menu>
  );
  
  // Mobile menu
  const renderMobileMenu = (
    <Menu
      anchorEl={mobileMenuAnchor}
      id="mobile-menu"
      keepMounted
      open={Boolean(mobileMenuAnchor)}
      onClose={handleMobileMenuClose}
      PaperProps={{
        elevation: 0,
        sx: {
          overflow: 'visible',
          filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.15))',
          mt: 1.5,
          borderRadius: 2,
          minWidth: 200,
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <MenuItem onClick={toggleSearch}>
        <ListItemIcon>
          <Search fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('common.search')}</ListItemText>
      </MenuItem>
      
      <MenuItem onClick={toggleNotifications}>
        <ListItemIcon>
          <Notifications fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('common.notifications')}</ListItemText>
      </MenuItem>
      
      <MenuItem onClick={toggleWeather}>
        <ListItemIcon>
          <Brightness4 fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('common.weather')}</ListItemText>
      </MenuItem>
      
      <MenuItem onClick={toggleQuickActions}>
        <ListItemIcon>
          <Dashboard fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('common.quick_actions')}</ListItemText>
      </MenuItem>
      
      <Divider />
      
      <MenuItem onClick={handleProfileMenuOpen}>
        <ListItemIcon>
          <AccountCircle fontSize="small" />
        </ListItemIcon>
        <ListItemText>{translate('nav.profile')}</ListItemText>
      </MenuItem>
    </Menu>
  );
  
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* App Bar */}
      <AppBar 
        position="fixed" 
        color="inherit"
        elevation={0}
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backdropFilter: 'blur(10px)',
          backgroundColor: alpha(muiTheme.palette.background.default, 0.8),
          borderBottom: `1px solid ${alpha(muiTheme.palette.divider, 0.1)}`,
          transition: 'all 0.3s ease'
        }}
      >
        <Toolbar>
          {/* Menu Icon */}
          <IconButton
            edge="start"
            color="inherit"
            aria-label="open drawer"
            onClick={toggleSidebar}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
          
          {/* Logo */}
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              display: { xs: 'none', sm: 'block' },
              fontWeight: 'bold',
              background: `linear-gradient(135deg, ${muiTheme.palette.primary.main}, ${muiTheme.palette.primary.dark})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}
          >
            AgriIntel
          </Typography>
          
          {/* Spacer */}
          <Box sx={{ flexGrow: 1 }} />
          
          {/* Desktop Actions */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 1 }}>
            {/* Search */}
            <Tooltip title={translate('common.search')}>
              <IconButton color="inherit" onClick={toggleSearch}>
                <Search />
              </IconButton>
            </Tooltip>
            
            {/* Notifications */}
            <Tooltip title={translate('common.notifications')}>
              <IconButton color="inherit" onClick={toggleNotifications}>
                <Badge badgeContent={4} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>
            
            {/* Weather */}
            <Tooltip title={translate('common.weather')}>
              <IconButton color="inherit" onClick={toggleWeather}>
                <Brightness4 />
              </IconButton>
            </Tooltip>
            
            {/* Quick Actions */}
            <Tooltip title={translate('common.quick_actions')}>
              <IconButton color="inherit" onClick={toggleQuickActions}>
                <Dashboard />
              </IconButton>
            </Tooltip>
            
            {/* Theme Toggle */}
            <Tooltip title={
              theme.palette.mode === 'dark' 
                ? translate('settings.theme.light_mode') 
                : translate('settings.theme.dark_mode')
            }>
              <IconButton color="inherit" onClick={toggleMode}>
                {theme.palette.mode === 'dark' ? <LightMode /> : <DarkMode />}
              </IconButton>
            </Tooltip>
            
            {/* Language Selector */}
            <Box sx={{ mx: 1 }}>
              <LanguageSelector size="small" showLabel={false} />
            </Box>
            
            {/* Profile */}
            <Tooltip title={translate('nav.profile')}>
              <IconButton
                edge="end"
                aria-label="account of current user"
                aria-controls="profile-menu"
                aria-haspopup="true"
                onClick={handleProfileMenuOpen}
                color="inherit"
              >
                <Avatar 
                  sx={{ 
                    width: 32, 
                    height: 32,
                    bgcolor: muiTheme.palette.primary.main,
                    border: `2px solid ${alpha(muiTheme.palette.primary.main, 0.5)}`
                  }}
                >
                  {user?.firstName?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
          
          {/* Mobile Actions */}
          <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
            <IconButton
              aria-label="show more"
              aria-controls="mobile-menu"
              aria-haspopup="true"
              onClick={handleMobileMenuOpen}
              color="inherit"
            >
              <MoreVert />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>
      
      {/* Render menus */}
      {renderProfileMenu}
      {renderMobileMenu}
      
      {/* Sidebar */}
      <Sidebar 
        isOpen={sidebarOpen} 
        onToggle={toggleSidebar} 
        isMobile={isMobile} 
      />
      
      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${sidebarOpen ? 240 : 64}px)` },
          ml: { sm: `${sidebarOpen ? 240 : 64}px` },
          transition: 'all 0.3s ease',
          mt: '64px',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background gradient */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: -1,
            opacity: 0.05,
            background: `radial-gradient(circle at 10% 10%, ${muiTheme.palette.primary.light}, transparent 40%), 
                        radial-gradient(circle at 90% 90%, ${muiTheme.palette.secondary.light}, transparent 40%)`
          }}
        />
        
        {/* Content */}
        <Outlet />
      </Box>
      
      {/* Global Search Panel */}
      <AnimatePresence>
        {searchOpen && (
          <GlobalSearch onClose={toggleSearch} />
        )}
      </AnimatePresence>
      
      {/* Notifications Panel */}
      <AnimatePresence>
        {notificationsOpen && (
          <NotificationsPanel onClose={toggleNotifications} />
        )}
      </AnimatePresence>
      
      {/* Weather Widget */}
      <AnimatePresence>
        {weatherOpen && (
          <WeatherWidget onClose={toggleWeather} />
        )}
      </AnimatePresence>
      
      {/* Quick Actions */}
      <AnimatePresence>
        {quickActionsOpen && (
          <QuickActions onClose={toggleQuickActions} />
        )}
      </AnimatePresence>
    </Box>
  );
};

export default EnhancedDashboardLayout;
