/**
 * Health Validation Schemas
 * 
 * This module provides validation schemas for health-related API endpoints.
 */

const Joi = require('joi');
const { schemas } = require('../middleware/validationMiddleware');

// Health record types
const recordTypes = [
  'examination', 
  'vaccination', 
  'treatment', 
  'medication', 
  'surgery', 
  'injury', 
  'disease', 
  'parasite', 
  'other'
];

// Health record statuses
const statuses = ['active', 'resolved', 'ongoing', 'scheduled', 'cancelled'];

// Create health record validation schema
const createHealthRecordSchema = Joi.object({
  animalId: schemas.id,
  recordType: Joi.string().valid(...recordTypes).required().messages({
    'any.only': `Record type must be one of: ${recordTypes.join(', ')}`,
    'any.required': 'Record type is required'
  }),
  date: Joi.date().iso().required().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),
  description: Joi.string().trim().required().messages({
    'string.empty': 'Description cannot be empty',
    'any.required': 'Description is required'
  }),
  diagnosis: Joi.string().trim().allow('').messages({
    'string.base': 'Diagnosis must be a string'
  }),
  treatment: Joi.string().trim().allow('').messages({
    'string.base': 'Treatment must be a string'
  }),
  medication: Joi.string().trim().allow('').messages({
    'string.base': 'Medication must be a string'
  }),
  dosage: Joi.string().trim().allow('').messages({
    'string.base': 'Dosage must be a string'
  }),
  administeredBy: Joi.string().trim().allow('').messages({
    'string.base': 'Administered by must be a string'
  }),
  veterinarian: Joi.string().trim().allow('').messages({
    'string.base': 'Veterinarian must be a string'
  }),
  cost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Cost must be a number',
    'number.min': 'Cost cannot be negative'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  followUpDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Follow-up date must be a valid date',
    'date.format': 'Follow-up date must be in ISO format (YYYY-MM-DD)'
  }),
  status: Joi.string().valid(...statuses).default('active').messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  attachments: Joi.array().items(Joi.string()).default([]).messages({
    'array.base': 'Attachments must be an array'
  }),
  metadata: Joi.object().default({}).messages({
    'object.base': 'Metadata must be an object'
  })
});

// Update health record validation schema
const updateHealthRecordSchema = Joi.object({
  animalId: schemas.id,
  recordType: Joi.string().valid(...recordTypes).messages({
    'any.only': `Record type must be one of: ${recordTypes.join(', ')}`
  }),
  date: Joi.date().iso().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)'
  }),
  description: Joi.string().trim().messages({
    'string.empty': 'Description cannot be empty'
  }),
  diagnosis: Joi.string().trim().allow('').messages({
    'string.base': 'Diagnosis must be a string'
  }),
  treatment: Joi.string().trim().allow('').messages({
    'string.base': 'Treatment must be a string'
  }),
  medication: Joi.string().trim().allow('').messages({
    'string.base': 'Medication must be a string'
  }),
  dosage: Joi.string().trim().allow('').messages({
    'string.base': 'Dosage must be a string'
  }),
  administeredBy: Joi.string().trim().allow('').messages({
    'string.base': 'Administered by must be a string'
  }),
  veterinarian: Joi.string().trim().allow('').messages({
    'string.base': 'Veterinarian must be a string'
  }),
  cost: Joi.number().min(0).allow(null).messages({
    'number.base': 'Cost must be a number',
    'number.min': 'Cost cannot be negative'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  }),
  followUpDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Follow-up date must be a valid date',
    'date.format': 'Follow-up date must be in ISO format (YYYY-MM-DD)'
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  attachments: Joi.array().items(Joi.string()).messages({
    'array.base': 'Attachments must be an array'
  }),
  metadata: Joi.object().messages({
    'object.base': 'Metadata must be an object'
  })
});

// Get health records query validation schema
const getHealthRecordsQuerySchema = Joi.object({
  animalId: Joi.string().trim().messages({
    'string.base': 'Animal ID must be a string'
  }),
  recordType: Joi.string().valid(...recordTypes).messages({
    'any.only': `Record type must be one of: ${recordTypes.join(', ')}`
  }),
  status: Joi.string().valid(...statuses).messages({
    'any.only': `Status must be one of: ${statuses.join(', ')}`
  }),
  startDate: Joi.date().iso().messages({
    'date.base': 'Start date must be a valid date',
    'date.format': 'Start date must be in ISO format (YYYY-MM-DD)'
  }),
  endDate: Joi.date().iso().messages({
    'date.base': 'End date must be a valid date',
    'date.format': 'End date must be in ISO format (YYYY-MM-DD)'
  }),
  veterinarian: Joi.string().trim().messages({
    'string.base': 'Veterinarian must be a string'
  }),
  search: Joi.string().trim().allow('').messages({
    'string.base': 'Search must be a string'
  })
}).concat(schemas.pagination);

// Health record ID parameter validation schema
const healthRecordIdParamSchema = Joi.object({
  id: schemas.id
});

// Create vaccination validation schema
const createVaccinationSchema = Joi.object({
  animalId: schemas.id,
  vaccineName: Joi.string().trim().required().messages({
    'string.empty': 'Vaccine name cannot be empty',
    'any.required': 'Vaccine name is required'
  }),
  date: Joi.date().iso().required().messages({
    'date.base': 'Date must be a valid date',
    'date.format': 'Date must be in ISO format (YYYY-MM-DD)',
    'any.required': 'Date is required'
  }),
  batchNumber: Joi.string().trim().allow('').messages({
    'string.base': 'Batch number must be a string'
  }),
  administeredBy: Joi.string().trim().allow('').messages({
    'string.base': 'Administered by must be a string'
  }),
  nextDueDate: Joi.date().iso().allow(null).messages({
    'date.base': 'Next due date must be a valid date',
    'date.format': 'Next due date must be in ISO format (YYYY-MM-DD)'
  }),
  notes: Joi.string().trim().allow('').messages({
    'string.base': 'Notes must be a string'
  })
});

module.exports = {
  createHealthRecordSchema,
  updateHealthRecordSchema,
  getHealthRecordsQuerySchema,
  healthRecordIdParamSchema,
  createVaccinationSchema,
  recordTypes,
  statuses
};
