import React, { useState } from 'react';
import { Box, Typography, Card, CardContent, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Chip, useTheme, alpha, FormControl, InputLabel, Select, MenuItem, TextField, IconButton, Divider } from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area
} from 'recharts';
import { Download, FilterList, AttachMoney, TrendingUp, PieChart as PieChartIcon, Print, Share, MonetizationOn } from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { formatCurrency } from '../../utils/formatters';
import { generateReport } from '../../utils/reportUtils';

// Mock data for financial reports
const monthlyRevenueData = [
  { month: 'Jan', revenue: 125000, expenses: 85000, profit: 40000 },
  { month: 'Feb', revenue: 145000, expenses: 90000, profit: 55000 },
  { month: 'Mar', revenue: 135000, expenses: 88000, profit: 47000 },
  { month: 'Apr', revenue: 150000, expenses: 95000, profit: 55000 },
  { month: 'May', revenue: 160000, expenses: 100000, profit: 60000 },
  { month: 'Jun', revenue: 175000, expenses: 105000, profit: 70000 },
  { month: 'Jul', revenue: 190000, expenses: 110000, profit: 80000 },
  { month: 'Aug', revenue: 180000, expenses: 108000, profit: 72000 },
  { month: 'Sep', revenue: 165000, expenses: 102000, profit: 63000 },
  { month: 'Oct', revenue: 155000, expenses: 98000, profit: 57000 },
  { month: 'Nov', revenue: 170000, expenses: 105000, profit: 65000 },
  { month: 'Dec', revenue: 185000, expenses: 115000, profit: 70000 }
];

const revenueSourcesData = [
  { name: 'Livestock Sales', value: 65 },
  { name: 'Dairy Products', value: 20 },
  { name: 'Breeding Services', value: 10 },
  { name: 'Other', value: 5 }
];

const expenseCategoriesData = [
  { name: 'Feed', value: 40 },
  { name: 'Labor', value: 25 },
  { name: 'Veterinary', value: 15 },
  { name: 'Equipment', value: 10 },
  { name: 'Utilities', value: 5 },
  { name: 'Other', value: 5 }
];

const profitMarginData = [
  { month: 'Jan', margin: 32 },
  { month: 'Feb', margin: 38 },
  { month: 'Mar', margin: 35 },
  { month: 'Apr', margin: 37 },
  { month: 'May', margin: 38 },
  { month: 'Jun', margin: 40 },
  { month: 'Jul', margin: 42 },
  { month: 'Aug', margin: 40 },
  { month: 'Sep', margin: 38 },
  { month: 'Oct', margin: 37 },
  { month: 'Nov', margin: 38 },
  { month: 'Dec', margin: 38 }
];

const COLORS = ['#4caf50', '#2196f3', '#ff9800', '#f44336', '#9c27b0', '#607d8b'];

const financialMetrics = [
  { metric: 'Total Revenue', value: 'R1,935,000', change: '+12%', period: 'vs. last year' },
  { metric: 'Total Expenses', value: 'R1,201,000', change: '+8%', period: 'vs. last year' },
  { metric: 'Net Profit', value: 'R734,000', change: '+18%', period: 'vs. last year' },
  { metric: 'Profit Margin', value: '38%', change: '+2%', period: 'vs. last year' }
];

const Financial: React.FC = () => {
  const theme = useTheme();
  const [timeRange, setTimeRange] = useState('year');
  const [reportType, setReportType] = useState('overview');

  const [isGenerating, setIsGenerating] = useState(false);
  const [reportFormat, setReportFormat] = useState<'csv' | 'excel' | 'pdf'>('pdf');

  const handleTimeRangeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setTimeRange(event.target.value as string);
  };

  const handleReportTypeChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setReportType(event.target.value as string);
  };

  const handleGenerateReport = (reportName: string) => {
    setIsGenerating(true);

    try {
      // Determine which data to use based on the report type
      let reportData: any[] = [];

      switch (reportType) {
        case 'overview':
          reportData = monthlyRevenueData;
          break;
        case 'revenue':
          reportData = revenueSourcesData;
          break;
        case 'expenses':
          reportData = expenseCategoriesData;
          break;
        case 'profitability':
          reportData = profitMarginData;
          break;
        default:
          reportData = monthlyRevenueData;
      }

      // Generate the report
      generateReport(
        reportData,
        `financial_${reportType}_report`,
        reportFormat,
        reportName
      );

      // Show success message or notification here if you have a notification system
      console.log(`${reportName} generated successfully`);
    } catch (error) {
      console.error('Error generating report:', error);
      // Show error message or notification here
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Box>
      <ModuleHeader
        title="Financial Reports"
        subtitle="Comprehensive financial analytics and performance metrics for your livestock operation"
        module="reports"
        submodule="financial"
        actionLabel="Generate & Download Report"
        actionIcon={<Download />}
        onAction={() => handleGenerateReport('Comprehensive Financial Report')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Filters */}
        <Card
          sx={{
            p: 2,
            mb: 3,
            borderRadius: '12px',
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <FilterList color="primary" />
            </Grid>
            <Grid item xs={12} sm={3} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="time-range-label">Time Range</InputLabel>
                <Select
                  labelId="time-range-label"
                  id="time-range"
                  value={timeRange}
                  label="Time Range"
                  onChange={handleTimeRangeChange as any}
                >
                  <MenuItem value="month">This Month</MenuItem>
                  <MenuItem value="quarter">This Quarter</MenuItem>
                  <MenuItem value="year">This Year</MenuItem>
                  <MenuItem value="custom">Custom Range</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={3} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel id="report-type-label">Report Type</InputLabel>
                <Select
                  labelId="report-type-label"
                  id="report-type"
                  value={reportType}
                  label="Report Type"
                  onChange={handleReportTypeChange as any}
                >
                  <MenuItem value="overview">Overview</MenuItem>
                  <MenuItem value="revenue">Revenue Analysis</MenuItem>
                  <MenuItem value="expenses">Expense Analysis</MenuItem>
                  <MenuItem value="profitability">Profitability</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {timeRange === 'custom' && (
              <>
                <Grid item xs={12} sm={3} md={2}>
                  <TextField
                    fullWidth
                    id="start-date"
                    label="Start Date"
                    type="date"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    defaultValue="2024-01-01"
                  />
                </Grid>
                <Grid item xs={12} sm={3} md={2}>
                  <TextField
                    fullWidth
                    id="end-date"
                    label="End Date"
                    type="date"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    defaultValue="2024-12-31"
                  />
                </Grid>
              </>
            )}
            <Grid item>
              <FormControl fullWidth size="small">
                <InputLabel id="report-format-label">Format</InputLabel>
                <Select
                  labelId="report-format-label"
                  id="report-format"
                  value={reportFormat}
                  label="Format"
                  onChange={(e) => setReportFormat(e.target.value as 'csv' | 'excel' | 'pdf')}
                >
                  <MenuItem value="pdf">PDF Document</MenuItem>
                  <MenuItem value="excel">Excel Spreadsheet</MenuItem>
                  <MenuItem value="csv">CSV File</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item>
              <CustomButton
                variant="contained"
                color="primary"
                size="small"
                startIcon={isGenerating ? null : <Download />}
                disabled={isGenerating}
                onClick={() => handleGenerateReport(`Financial ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`)}
              >
                {isGenerating ? 'Generating...' : 'Generate Report'}
              </CustomButton>
            </Grid>
          </Grid>
        </Card>

        {/* Key Metrics */}
        <Grid container spacing={3} mb={4}>
          {financialMetrics.map((item, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
                <CardContent>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {item.metric}
                  </Typography>
                  <Typography variant="h4" fontWeight="bold">
                    {item.value}
                  </Typography>
                  <Box display="flex" alignItems="center" mt={1}>
                    <Typography
                      variant="body2"
                      color={item.change.startsWith('+') ? 'success.main' : 'error.main'}
                      fontWeight="medium"
                    >
                      {item.change}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ ml: 0.5 }}>
                      {item.period}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Charts */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={8}>
            <AnimatedBackgroundCard
              title="Revenue, Expenses & Profit"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<TrendingUp />}
              accentColor={theme.palette.primary.main}
              secondaryColor={theme.palette.primary.dark}
              delay={0.1}
              height="100%"
            >
              <Box mt={2} height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={monthlyRevenueData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `R${value / 1000}k`} />
                    <Tooltip formatter={(value) => `R${value.toLocaleString()}`} />
                    <Legend />
                    <Bar dataKey="revenue" name="Revenue" fill={theme.palette.success.main} />
                    <Bar dataKey="expenses" name="Expenses" fill={theme.palette.error.main} />
                    <Bar dataKey="profit" name="Profit" fill={theme.palette.primary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={4}>
            <AnimatedBackgroundCard
              title="Profit Margin Trend"
              backgroundImage="https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg"
              icon={<AttachMoney />}
              accentColor={theme.palette.secondary.main}
              secondaryColor={theme.palette.secondary.dark}
              delay={0.2}
              height="100%"
            >
              <Box mt={2} height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={profitMarginData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis tickFormatter={(value) => `${value}%`} />
                    <Tooltip formatter={(value) => `${value}%`} />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="margin"
                      name="Profit Margin"
                      stroke={theme.palette.success.main}
                      activeDot={{ r: 8 }}
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={6}>
            <AnimatedBackgroundCard
              title="Revenue Sources"
              backgroundImage="https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png"
              icon={<PieChartIcon />}
              accentColor={theme.palette.success.main}
              secondaryColor={theme.palette.success.dark}
              delay={0.3}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2} height={300} display="flex" flexDirection="column">
                <Box height="70%" width="100%">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={revenueSourcesData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {revenueSourcesData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Key Insights:
                  </Typography>
                  <Typography variant="body2">
                    Livestock sales remain the primary revenue driver at 65%, with dairy products contributing 20%.
                    Consider diversifying revenue streams to reduce dependency on livestock sales.
                  </Typography>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>

          <Grid item xs={12} md={6}>
            <AnimatedBackgroundCard
              title="Expense Categories"
              backgroundImage="https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg"
              icon={<PieChartIcon />}
              accentColor={theme.palette.error.main}
              secondaryColor={theme.palette.error.dark}
              delay={0.4}
              height="100%"
              overlay="gradient"
            >
              <Box mt={2} height={300} display="flex" flexDirection="column">
                <Box height="70%" width="100%">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={expenseCategoriesData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {expenseCategoriesData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
                <Divider sx={{ my: 2 }} />
                <Box>
                  <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
                    Key Insights:
                  </Typography>
                  <Typography variant="body2">
                    Feed costs (40%) and labor (25%) represent the largest expense categories.
                    Consider bulk purchasing strategies and efficiency improvements to reduce these costs.
                  </Typography>
                </Box>
              </Box>
            </AnimatedBackgroundCard>
          </Grid>
        </Grid>

        {/* Detailed Financial Table */}
        <Card
          sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden',
            mb: 4,
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
          }}
        >
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6" fontWeight="bold">
                Monthly Financial Summary
              </Typography>
              <Box>
                <IconButton size="small" color="primary">
                  <Print fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  color="primary"
                  onClick={() => handleGenerateReport('Monthly Financial Summary')}
                >
                  <Download fontSize="small" />
                </IconButton>
                <IconButton size="small" color="primary">
                  <Share fontSize="small" />
                </IconButton>
              </Box>
            </Box>
            <TableContainer component={Paper} sx={{ boxShadow: 'none' }}>
              <Table>
                <TableHead sx={{ bgcolor: 'primary.main' }}>
                  <TableRow>
                    <TableCell sx={{ color: 'white' }}>Month</TableCell>
                    <TableCell sx={{ color: 'white' }}>Revenue</TableCell>
                    <TableCell sx={{ color: 'white' }}>Expenses</TableCell>
                    <TableCell sx={{ color: 'white' }}>Profit</TableCell>
                    <TableCell sx={{ color: 'white' }}>Margin</TableCell>
                    <TableCell sx={{ color: 'white' }}>YoY Change</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {monthlyRevenueData.map((item) => (
                    <TableRow key={item.month} hover>
                      <TableCell>{item.month}</TableCell>
                      <TableCell sx={{ fontWeight: 'medium' }}>{formatCurrency(item.revenue)}</TableCell>
                      <TableCell sx={{ fontWeight: 'medium', color: 'error.main' }}>{formatCurrency(item.expenses)}</TableCell>
                      <TableCell sx={{ fontWeight: 'medium', color: 'success.main' }}>{formatCurrency(item.profit)}</TableCell>
                      <TableCell>{((item.profit / item.revenue) * 100).toFixed(1)}%</TableCell>
                      <TableCell>
                        <Chip
                          label={`+${Math.floor(Math.random() * 20) + 5}%`}
                          color="success"
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
};

export default Financial;
