const { validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * Middleware to validate request data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn(`Validation error: ${JSON.stringify(errors.array())}`);
    return res.status(400).json({
      error: {
        message: 'Validation failed',
        details: errors.array(),
        status: 400
      }
    });
  }
  
  next();
};

module.exports = {
  validateRequest
};