/**
 * settings Routes
 * 
 * API routes for settings module
 */

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const settingsController = require('../controllers/settingsController');

// Apply authentication middleware to all routes
// router.use(authenticateToken);

/**
 * GET /api/settings
 * 
 * @route GET /
 * @group settings - settings operations
 * @returns {object} 200 - Success response
 */
router.get('/', settingsController.getAll);

/**
 * GET /api/settings/:id
 * 
 * @route GET /:id
 * @group settings - settings operations
 * @returns {object} 200 - Success response
 */
router.get('/:id', settingsController.getById);

/**
 * POST /api/settings
 * 
 * @route POST /
 * @group settings - settings operations
 * @returns {object} 201 - Created response
 */
router.post('/', settingsController.create);

/**
 * PUT /api/settings/:id
 * 
 * @route PUT /:id
 * @group settings - settings operations
 * @returns {object} 200 - Success response
 */
router.put('/:id', settingsController.update);

/**
 * DELETE /api/settings/:id
 * 
 * @route DELETE /:id
 * @group settings - settings operations
 * @returns {object} 200 - Success response
 */
router.delete('/:id', settingsController.delete);

module.exports = router;
