import React from 'react';
import { Box, Typography, Paper, alpha, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import BusinessAnalyticsButton from './BusinessAnalyticsButton';

interface BusinessAnalyticsCardProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  height?: string | number;
  accentColor?: string;
  secondaryColor?: string;
  actionLabel?: string;
  onAction?: () => void;
  loading?: boolean;
  delay?: number;
}

/**
 * Custom card component for business analytics module
 * This component doesn't rely on MUI components that might cause the dark property error
 */
const BusinessAnalyticsCard: React.FC<BusinessAnalyticsCardProps> = ({
  title,
  subtitle,
  icon,
  children,
  height = 'auto',
  accentColor,
  secondaryColor,
  actionLabel,
  onAction,
  loading = false,
  delay = 0
}) => {
  const theme = useTheme();

  // Use provided colors or fallback to theme colors
  const primaryColor = accentColor || theme.palette.primary.main;
  const secondaryColorValue = secondaryColor || (theme.palette.primary.main || '#2A8A82');

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      style={{ height: height }}
    >
      <Paper
        elevation={0}
        sx={{
          height: '100%',
          borderRadius: 2,
          overflow: 'hidden',
          position: 'relative',
          background: `linear-gradient(135deg, ${alpha(primaryColor, 0.05)} 0%, ${alpha(secondaryColorValue, 0.1)} 100%)`,
          border: `1px solid ${alpha(primaryColor, 0.1)}`,
          boxShadow: `0 4px 20px ${alpha(primaryColor, 0.1)}`,
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            boxShadow: `0 8px 30px ${alpha(primaryColor, 0.15)}`,
            transform: 'translateY(-5px)'
          }
        }}
      >
        {/* Card Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            p: 2,
            borderBottom: `1px solid ${alpha(primaryColor, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && (
              <Box
                sx={{
                  mr: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  background: `linear-gradient(135deg, ${primaryColor} 0%, ${secondaryColorValue} 100%)`,
                  color: '#fff'
                }}
              >
                {icon}
              </Box>
            )}
            <Box>
              <Typography variant="h6" fontWeight="bold">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>

          {actionLabel && onAction && (
            <BusinessAnalyticsButton
              label={actionLabel}
              onClick={onAction}
              variant="text"
              color="primary"
              size="small"
            >
              {actionLabel}
            </BusinessAnalyticsButton>
          )}
        </Box>

        {/* Card Content */}
        <Box
          sx={{
            p: 2,
            position: 'relative',
            height: 'calc(100% - 72px)',
            overflow: 'auto'
          }}
        >
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                width: '100%'
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  border: `3px solid ${alpha(primaryColor, 0.2)}`,
                  borderTop: `3px solid ${primaryColor}`,
                  animation: 'spin 1s linear infinite',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }}
              />
            </Box>
          ) : (
            children
          )}
        </Box>
      </Paper>
    </motion.div>
  );
};

export default BusinessAnalyticsCard;
