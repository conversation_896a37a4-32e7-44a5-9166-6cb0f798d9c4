export interface FeedType {
  id: string;
  name: string;
  category: 'concentrate' | 'forage' | 'supplement';
  description: string;
  nutritionalValue: {
    protein: number;
    fiber: number;
    energy: number;
    minerals: {
      calcium: number;
      phosphorus: number;
      magnesium: number;
    };
  };
  unitOfMeasure: string;
}

export interface FeedInventoryItem {
  id: string;
  feedTypeId: string;
  batchNumber: string;
  quantity: number;
  unitCost: number;
  purchaseDate: Date;
  expiryDate: Date;
  supplierId: string;
  storageLocation: string;
  minimumStockLevel: number;
  status: 'available' | 'low' | 'depleted';
}

export const mockFeedTypes: FeedType[] = [
  {
    id: 'FT001',
    name: 'Premium Dairy Feed',
    category: 'concentrate',
    description: 'High-energy feed for lactating dairy cows',
    nutritionalValue: {
      protein: 18,
      fiber: 12,
      energy: 75,
      minerals: {
        calcium: 0.8,
        phosphorus: 0.6,
        magnesium: 0.2
      }
    },
    unitOfMeasure: 'kg'
  },
  // Add more feed types...
];

export const mockFeedInventory: FeedInventoryItem[] = [
  {
    id: 'FI001',
    feedTypeId: 'FT001',
    batchNumber: 'BATCH2024001',
    quantity: 2000,
    unitCost: 0.75,
    purchaseDate: new Date('2024-01-01'),
    expiryDate: new Date('2024-06-01'),
    supplierId: 'SUP001',
    storageLocation: 'Warehouse A',
    minimumStockLevel: 500,
    status: 'available'
  },
  // Add more inventory items...
];