/**
 * This file contains a direct monkey patch for the react-big-calendar package
 * to fix the "Cannot read properties of null (reading 'useRef')" error
 */

// Create a mock CalOverlay component that doesn't use useRef
const mockCalOverlay = (props) => {
  // Just return null to prevent rendering anything
  return null;
};

// Apply the patch
try {
  // Find the module in the webpack module cache
  const moduleId = Object.keys(window).find(key => 
    key.startsWith('__WEBPACK_DEFAULT_EXPORT__') && 
    window[key] && 
    window[key].Calendar
  );

  if (moduleId) {
    const reactBigCalendar = window[moduleId];
    
    // Replace the CalOverlay component with our mock
    if (reactBigCalendar.__patched) {
      console.log('react-big-calendar already patched');
    } else {
      // Find the PopOverlay module
      const popOverlayModule = Object.values(window).find(module => 
        module && 
        typeof module === 'object' && 
        module.CalOverlay
      );

      if (popOverlayModule) {
        // Replace the CalOverlay function with our mock
        popOverlayModule.CalOverlay = mockCalOverlay;
        reactBigCalendar.__patched = true;
        console.log('Successfully patched react-big-calendar');
      } else {
        console.warn('Could not find CalOverlay in react-big-calendar');
      }
    }
  } else {
    console.warn('Could not find react-big-calendar in webpack modules');
  }
} catch (error) {
  console.error('Error patching react-big-calendar:', error);
}

export default {};
