/**
 * <PERSON><PERSON>t to kill any Node.js processes running on a specific port
 */
const { exec } = require('child_process');
const os = require('os');

// Get the port from command line arguments or use default
const port = process.argv[2] || 3001;

console.log(`Attempting to kill any processes running on port ${port}...`);

// Command depends on the operating system
const command = os.platform() === 'win32'
  ? `FOR /F "tokens=5" %a in ('netstat -ano ^| findstr :${port} ^| findstr LISTENING') do taskkill /F /PID %a`
  : `lsof -i :${port} | grep LISTEN | awk '{print $2}' | xargs kill -9`;

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.log(`No processes found running on port ${port} or error killing processes.`);
    return;
  }
  
  if (stdout) {
    console.log(`Successfully killed processes on port ${port}:`);
    console.log(stdout);
  }
  
  if (stderr) {
    console.error(`Error output:`);
    console.error(stderr);
  }
});
