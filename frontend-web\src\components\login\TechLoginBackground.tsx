import React from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';

interface TechLoginBackgroundProps {
  children?: React.ReactNode;
}

/**
 * TechLoginBackground - A tech-themed background component for the login page
 * Features a green tech background with the May<PERSON>aiphus blinking logo
 */
const TechLoginBackground: React.FC<TechLoginBackgroundProps> = ({ children }) => {
  const theme = useTheme();
  
  return (
    <Box sx={{
      position: 'relative',
      width: '100%',
      height: '100%',
      overflow: 'hidden',
      borderRadius: { xs: 0, md: 2 }
    }}>
      {/* Tech Background Image */}
      <Box sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: 'url(/images/backgrounds/tech-background.svg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        zIndex: 0
      }} />
      
      {/* Overlay with Blend Mode */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `linear-gradient(135deg, 
            ${alpha(theme.palette.primary.dark, 0.2)}, 
            ${alpha(theme.palette.primary.main, 0.2)})`,
          zIndex: 1,
          mixBlendMode: 'overlay'
        }}
      />
      
      {/* Animated Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 2,
          overflow: 'hidden'
        }}
      >
        {/* Animated Floating Hexagons */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2 }}
          style={{
            position: 'absolute',
            top: '10%',
            left: '10%',
            width: '100px',
            height: '100px',
          }}
        >
          <svg width="100" height="100" viewBox="0 0 100 100">
            <polygon 
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25" 
              fill="white" 
              stroke={theme.palette.primary.light}
              strokeWidth="2"
            />
          </svg>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.15, scale: 1 }}
          transition={{ duration: 2, delay: 0.5 }}
          style={{
            position: 'absolute',
            bottom: '15%',
            right: '20%',
            width: '80px',
            height: '80px',
          }}
        >
          <svg width="80" height="80" viewBox="0 0 100 100">
            <polygon 
              points="50,0 93.3,25 93.3,75 50,100 6.7,75 6.7,25" 
              fill="white" 
              stroke={theme.palette.primary.light}
              strokeWidth="2"
            />
          </svg>
        </motion.div>
        
        {/* Blinking Logo */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5 }}
          style={{
            position: 'absolute',
            top: '5%',
            right: '5%',
            width: '150px',
            height: '150px',
            backgroundImage: 'url(/images/backgrounds/blinking-ampd.gif)',
            backgroundSize: 'contain',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        />
        
        {/* Animated Circles */}
        <motion.div
          initial={{ scale: 0.8, x: '10%', y: '10%' }}
          animate={{
            scale: [0.8, 1.2, 0.8],
            x: ['10%', '15%', '10%'],
            y: ['10%', '5%', '10%']
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
          style={{
            position: 'absolute',
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            border: '1px solid rgba(255,255,255,0.1)',
            background: `radial-gradient(circle, ${alpha(theme.palette.primary.light, 0.1)} 0%, transparent 70%)`,
          }}
        />
      </Box>
      
      {/* Content */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 3,
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 4,
          backdropFilter: 'blur(2px)'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default TechLoginBackground;
