/**
 * Navigation Fixes Utility
 * Comprehensive fixes for routing and navigation issues
 */

import { useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// Route validation and correction
export const validateAndCorrectRoute = (pathname: string): string => {
  // Common route corrections
  const routeCorrections: Record<string, string> = {
    '/animals/new': '/dashboard/animals/new',
    '/animals/edit': '/dashboard/animals/edit',
    '/health/new': '/dashboard/health/new',
    '/health/edit': '/dashboard/health/edit',
    '/breeding/new': '/dashboard/breeding/new',
    '/breeding/edit': '/dashboard/breeding/edit',
    '/financial/new': '/dashboard/financial/new',
    '/financial/edit': '/dashboard/financial/edit',
    '/inventory/new': '/dashboard/inventory/new',
    '/inventory/edit': '/dashboard/inventory/edit',
    '/commercial/new': '/dashboard/commercial/new',
    '/commercial/edit': '/dashboard/commercial/edit',
    '/reports/new': '/dashboard/reports/new',
    '/reports/edit': '/dashboard/reports/edit',
    '/resources/new': '/dashboard/resources/new',
    '/resources/edit': '/dashboard/resources/edit',
    '/compliance/new': '/dashboard/compliance/new',
    '/compliance/edit': '/dashboard/compliance/edit',
    '/settings/new': '/dashboard/settings/new',
    '/settings/edit': '/dashboard/settings/edit'
  };

  // Check for direct corrections
  if (routeCorrections[pathname]) {
    return routeCorrections[pathname];
  }

  // Ensure dashboard routes are properly prefixed
  const moduleRoutes = [
    'animals', 'health', 'breeding', 'financial', 'inventory',
    'commercial', 'reports', 'resources', 'compliance', 'settings', 'analytics'
  ];

  for (const module of moduleRoutes) {
    if (pathname.startsWith(`/${module}`) && !pathname.startsWith(`/dashboard/${module}`)) {
      return pathname.replace(`/${module}`, `/dashboard/${module}`);
    }
  }

  return pathname;
};

// Tab navigation fixes
export const fixTabNavigation = () => {
  // Remove any persistent overlays that might block tab clicks
  const removeBlockingOverlays = () => {
    // Remove MUI backdrop overlays that aren't supposed to be there
    const staleBackdrops = document.querySelectorAll('.MuiBackdrop-root:not(.MuiBackdrop-open)');
    staleBackdrops.forEach(backdrop => {
      (backdrop as HTMLElement).style.display = 'none';
      (backdrop as HTMLElement).style.pointerEvents = 'none';
    });

    // Remove any white overlays
    const whiteOverlays = document.querySelectorAll('[style*="rgba(255, 255, 255"]');
    whiteOverlays.forEach(overlay => {
      const element = overlay as HTMLElement;
      if (element.style.position === 'fixed' && element.style.zIndex === '1300') {
        element.style.display = 'none';
      }
    });

    // Ensure body doesn't have blur effects
    document.body.style.filter = 'none';
    document.body.style.backdropFilter = 'none';
  };

  // Fix tab click handlers
  const fixTabClickHandlers = () => {
    const tabs = document.querySelectorAll('.MuiTab-root');
    tabs.forEach(tab => {
      const element = tab as HTMLElement;
      
      // Ensure tab is clickable
      element.style.setProperty('pointer-events', 'auto', 'important');
      element.style.setProperty('cursor', 'pointer', 'important');
      element.style.setProperty('z-index', '10', 'important');
      element.style.setProperty('position', 'relative', 'important');

      // Add click event listener if not already present
      if (!element.dataset.clickFixed) {
        element.addEventListener('click', (e) => {
          e.stopPropagation();
          // Trigger the original MUI tab click handler
          const muiTabButton = element.querySelector('button');
          if (muiTabButton) {
            muiTabButton.click();
          }
        });
        element.dataset.clickFixed = 'true';
      }
    });
  };

  // Fix tab panels
  const fixTabPanels = () => {
    const tabPanels = document.querySelectorAll('[role="tabpanel"]');
    tabPanels.forEach(panel => {
      const element = panel as HTMLElement;
      element.style.setProperty('pointer-events', 'auto', 'important');
      element.style.setProperty('z-index', '5', 'important');
      element.style.setProperty('position', 'relative', 'important');
    });
  };

  removeBlockingOverlays();
  fixTabClickHandlers();
  fixTabPanels();
};

// Module navigation helper
export const navigateToModule = (navigate: any, moduleId: string, subPath?: string) => {
  const basePath = `/dashboard/${moduleId}`;
  const fullPath = subPath ? `${basePath}/${subPath}` : basePath;
  navigate(fullPath);
};

// Breadcrumb generation helper
export const generateBreadcrumbsFromPath = (pathname: string) => {
  const segments = pathname.split('/').filter(segment => segment);
  const breadcrumbs = [];

  let currentPath = '';
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Skip the first 'dashboard' segment for cleaner breadcrumbs
    if (segment === 'dashboard' && index === 0) {
      return;
    }

    breadcrumbs.push({
      label: segment.charAt(0).toUpperCase() + segment.slice(1),
      path: currentPath,
      isLast: index === segments.length - 1
    });
  });

  return breadcrumbs;
};

// Hook for automatic route validation and tab fixes
export const useNavigationFixes = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const applyFixes = useCallback(() => {
    // Apply tab navigation fixes
    fixTabNavigation();

    // Validate and correct current route if needed
    const correctedPath = validateAndCorrectRoute(location.pathname);
    if (correctedPath !== location.pathname) {
      navigate(correctedPath, { replace: true });
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    // Apply fixes on mount and route change
    applyFixes();

    // Set up interval to periodically fix tab issues
    const interval = setInterval(fixTabNavigation, 2000);

    // Apply fixes when DOM changes (for dynamic content)
    const observer = new MutationObserver(() => {
      setTimeout(fixTabNavigation, 100);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => {
      clearInterval(interval);
      observer.disconnect();
    };
  }, [applyFixes]);

  return {
    navigateToModule: (moduleId: string, subPath?: string) => 
      navigateToModule(navigate, moduleId, subPath),
    generateBreadcrumbs: () => generateBreadcrumbsFromPath(location.pathname),
    currentPath: location.pathname,
    applyFixes
  };
};

// Module access helper with navigation
export const createModuleNavigator = (navigate: any) => ({
  goToDashboard: () => navigate('/dashboard'),
  goToAnimals: (subPath?: string) => navigateToModule(navigate, 'animals', subPath),
  goToHealth: (subPath?: string) => navigateToModule(navigate, 'health', subPath),
  goToBreeding: (subPath?: string) => navigateToModule(navigate, 'breeding', subPath),
  goToFinancial: (subPath?: string) => navigateToModule(navigate, 'financial', subPath),
  goToInventory: (subPath?: string) => navigateToModule(navigate, 'inventory', subPath),
  goToCommercial: (subPath?: string) => navigateToModule(navigate, 'commercial', subPath),
  goToReports: (subPath?: string) => navigateToModule(navigate, 'reports', subPath),
  goToResources: (subPath?: string) => navigateToModule(navigate, 'resources', subPath),
  goToCompliance: (subPath?: string) => navigateToModule(navigate, 'compliance', subPath),
  goToSettings: (subPath?: string) => navigateToModule(navigate, 'settings', subPath),
  goToAnalytics: (subPath?: string) => navigateToModule(navigate, 'analytics', subPath)
});

// Tab state management helper
export const useTabState = (defaultTab: number = 0) => {
  const [activeTab, setActiveTab] = React.useState(defaultTab);

  const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    
    // Apply tab fixes after state change
    setTimeout(fixTabNavigation, 100);
  }, []);

  return {
    activeTab,
    setActiveTab,
    handleTabChange
  };
};

// Export all utilities
export default {
  validateAndCorrectRoute,
  fixTabNavigation,
  navigateToModule,
  generateBreadcrumbsFromPath,
  useNavigationFixes,
  createModuleNavigator,
  useTabState
};
