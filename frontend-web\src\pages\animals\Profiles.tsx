import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Paper
} from '@mui/material';
import { Add } from '@mui/icons-material';
import { useTheme, alpha } from '@mui/material/styles';
import { <PERSON><PERSON><PERSON>Container, ModuleHeader, withSubModuleTranslation } from '../../components/common';

interface AnimalProfile {
  id: string;
  tag: string;
  name: string;
  species: string;
  breed: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female';
  status: 'Active' | 'Inactive' | 'Sold' | 'Deceased' | 'Breeding' | 'Quarantined';
  weight: number;
  location: string;
  image: string;
}

const mockProfiles: AnimalProfile[] = [
  {
    id: 'ANM001',
    tag: 'CTL-2024-001',
    name: '<PERSON>she<PERSON><PERSON>',
    species: 'Cattle',
    breed: 'Nguni',
    dateOfBirth: '2022-03-15',
    gender: 'Female',
    status: 'Active',
    weight: 450,
    location: 'Veld A',
    image: '/images/animals/cattle-small-1.jpg'
  },
  {
    id: 'ANM002',
    tag: 'CTL-2024-002',
    name: '<PERSON><PERSON><PERSON>',
    species: 'Cattle',
    breed: 'Bonsmara',
    dateOfBirth: '2021-06-20',
    gender: 'Male',
    status: 'Active',
    weight: 650,
    location: 'Kraal 1',
    image: '/images/animals/cattle-small-2.jpg'
  },
  {
    id: 'ANM003',
    tag: 'CTL-2024-003',
    name: 'Lerato',
    species: 'Cattle',
    breed: 'Afrikaner',
    dateOfBirth: '2020-08-12',
    gender: 'Female',
    status: 'Breeding',
    weight: 520,
    location: 'Kraal 2',
    image: '/images/animals/cattle-small-3.jpg'
  },
  {
    id: 'ANM004',
    tag: 'CTL-2024-004',
    name: 'Botha',
    species: 'Cattle',
    breed: 'Drakensberger',
    dateOfBirth: '2020-11-05',
    gender: 'Male',
    status: 'Active',
    weight: 650,
    location: 'Veld A',
    image: '/images/animals/cattle-small-4.jpg'
  },
  {
    id: 'ANM005',
    tag: 'CTL-2024-005',
    name: 'Pule',
    species: 'Cattle',
    breed: 'Nguni',
    dateOfBirth: '2022-02-14',
    gender: 'Female',
    status: 'Active',
    weight: 320,
    location: 'Kraal 2',
    image: '/images/animals/cattle-small-5.jpg'
  }
];

interface AnimalProfilesProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const AnimalProfiles: React.FC<AnimalProfilesProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const theme = useTheme();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return { main: theme.palette.success.main, dark: theme.palette.success.dark };
      case 'Inactive':
        return { main: theme.palette.grey[500], dark: theme.palette.grey[700] };
      case 'Sold':
        return { main: theme.palette.info.main, dark: theme.palette.info.dark };
      case 'Deceased':
        return { main: theme.palette.error.main, dark: theme.palette.error.dark };
      case 'Breeding':
        return { main: theme.palette.warning.main, dark: theme.palette.warning.dark };
      case 'Quarantined':
        return { main: theme.palette.secondary.main, dark: theme.palette.secondary.dark };
      default:
        return { main: theme.palette.primary.main, dark: theme.palette.primary.dark };
    }
  };

  return (
    <ModuleContainer module="animals">
      <ModuleHeader
        title={translateSubModule ? translateSubModule('title', "Animal Profiles") : "Animal Profiles"}
        subtitle={translateSubModule ? translateSubModule('subtitle', "Manage and view detailed profiles of all livestock") : "Manage and view detailed profiles of all livestock"}
        module="animals"
        submodule="profiles"
        actionLabel={translateSubModule ? translateSubModule('action', "Add New Animal") : "Add New Animal"}
        actionIcon={<Add />}
        onAction={() => console.log('Add new animal')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        <Paper sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          backgroundColor: alpha(theme.palette.primary.main, 0.03)
        }}>
          <TextField
            fullWidth
            placeholder={translateModuleField ? translateModuleField('search_placeholder', "Search animals...") : "Search animals..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ mb: 3 }}
          />

          <Grid container spacing={3}>
            {mockProfiles.map((profile) => (
              <Grid item xs={12} sm={6} md={4} key={profile.id}>
                <Card sx={{
                  borderRadius: 2,
                  overflow: 'hidden',
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  transition: 'transform 0.2s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.12)'
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <CardMedia
                    component="img"
                    height="200"
                    image={profile.image}
                    alt={profile.name}
                  />
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography variant="h5" component="h2" gutterBottom fontWeight="bold">
                      {profile.name}
                    </Typography>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Chip
                        label={profile.status}
                        size="small"
                        sx={{
                          bgcolor: alpha(getStatusColor(profile.status).main, 0.1),
                          color: getStatusColor(profile.status).dark,
                          fontWeight: 'bold'
                        }}
                      />
                      <Chip
                        label={profile.species}
                        size="small"
                        sx={{
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.dark
                        }}
                      />
                    </Box>

                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" sx={{ mb: 0.5 }}>
                        <strong>{translateModuleField ? translateModuleField('tag', "Tag") : "Tag"}:</strong> {profile.tag}
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 0.5 }}>
                        <strong>{translateModuleField ? translateModuleField('breed', "Breed") : "Breed"}:</strong> {profile.breed}
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 0.5 }}>
                        <strong>{translateModuleField ? translateModuleField('born', "Born") : "Born"}:</strong> {new Date(profile.dateOfBirth).toLocaleDateString()}
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 0.5 }}>
                        <strong>{translateModuleField ? translateModuleField('weight', "Weight") : "Weight"}:</strong> {profile.weight}kg
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 0.5 }}>
                        <strong>{translateModuleField ? translateModuleField('location', "Location") : "Location"}:</strong> {profile.location}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Paper>
      </Box>
    </ModuleContainer>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(AnimalProfiles, 'animals', 'profiles');