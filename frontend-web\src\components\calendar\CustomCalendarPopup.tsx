import React, { useRef } from 'react';
import { Box, Paper, Typography, List, ListItem, ListItemText, Divider } from '@mui/material';
import { format } from 'date-fns';

interface CustomCalendarPopupProps {
  events: any[];
  date: Date;
  onSelectEvent?: (event: any) => void;
}

/**
 * A custom popup component for the calendar
 * This component is used to replace the default popup in react-big-calendar
 * to avoid the "Cannot read properties of null (reading 'useRef')" error
 */
const CustomCalendarPopup: React.FC<CustomCalendarPopupProps> = ({
  events,
  date,
  onSelectEvent
}) => {
  if (!events || events.length === 0) return null;

  return (
    <Paper
      elevation={3}
      sx={{
        position: 'absolute',
        zIndex: 1000,
        maxWidth: 300,
        maxHeight: 400,
        overflow: 'auto',
        p: 2,
        borderRadius: 2,
        boxShadow: '0 4px 20px rgba(0,0,0,0.15)'
      }}
    >
      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
        {format(date, 'MMMM d, yyyy')}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {events.length} event{events.length !== 1 ? 's' : ''}
      </Typography>
      <Divider sx={{ my: 1 }} />
      <List sx={{ p: 0 }}>
        {events.map((event, index) => (
          <React.Fragment key={event.id || index}>
            <ListItem
              button
              onClick={() => onSelectEvent && onSelectEvent(event)}
              sx={{ px: 1, py: 0.5 }}
            >
              <ListItemText
                primary={event.title}
                secondary={
                  <Box component="span" sx={{ display: 'flex', flexDirection: 'column', mt: 0.5 }}>
                    {event.start && (
                      <Typography variant="caption" color="text.secondary">
                        {format(new Date(event.start), 'h:mm a')}
                        {event.end && ` - ${format(new Date(event.end), 'h:mm a')}`}
                      </Typography>
                    )}
                    {event.description && (
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                        {event.description}
                      </Typography>
                    )}
                  </Box>
                }
              />
            </ListItem>
            {index < events.length - 1 && <Divider component="li" />}
          </React.Fragment>
        ))}
      </List>
    </Paper>
  );
};

export default CustomCalendarPopup;
