import React from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Paper, Grid, FormControl, InputLabel, Select, MenuItem, Chip, useTheme, SelectChangeEvent, OutlinedInput } from '@mui/material';
import { FilterList, Clear } from '../../utils/iconImports';
import { BkbAuctionType, BkbAuctionCategory } from '../../types/commercial/bkbAuction';
import { useBkbAuctions } from '../../contexts/commercial/BkbAuctionContext';
import { useLanguage } from '../../contexts/LanguageContext';

const BkbAuctionFilters: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();
  const { filter, setFilter } = useBkbAuctions();

  const handleTypeChange = (event: SelectChangeEvent<BkbAuctionType[]>) => {
    const value = event.target.value as BkbAuctionType[];
    setFilter({ ...filter, types: value.length > 0 ? value : undefined });
  };

  const handleCategoryChange = (event: SelectChangeEvent<BkbAuctionCategory[]>) => {
    const value = event.target.value as BkbAuctionCategory[];
    setFilter({ ...filter, categories: value.length > 0 ? value : undefined });
  };

  const handleProvinceChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    setFilter({ ...filter, provinces: value.length > 0 ? value : undefined });
  };

  const handleClearFilters = () => {
    setFilter({});
  };

  const auctionTypes: { value: BkbAuctionType; label: string }[] = [
    { value: 'weekly', label: translate ? translate('commercial.bkb.type.weekly', { fallback: 'Weekly' }) : 'Weekly' },
    { value: 'monthly', label: translate ? translate('commercial.bkb.type.monthly', { fallback: 'Monthly' }) : 'Monthly' },
    { value: 'fortnightly', label: translate ? translate('commercial.bkb.type.fortnightly', { fallback: 'Fortnightly' }) : 'Fortnightly' },
    { value: 'special', label: translate ? translate('commercial.bkb.type.special', { fallback: 'Special' }) : 'Special' },
    { value: 'prime', label: translate ? translate('commercial.bkb.type.prime', { fallback: 'Prime' }) : 'Prime' },
    { value: 'other', label: translate ? translate('commercial.bkb.type.other', { fallback: 'Other' }) : 'Other' }
  ];

  const auctionCategories: { value: BkbAuctionCategory; label: string }[] = [
    { value: 'livestock', label: translate ? translate('commercial.bkb.category.livestock', { fallback: 'Livestock' }) : 'Livestock' },
    { value: 'fibre', label: translate ? translate('commercial.bkb.category.fibre', { fallback: 'Fibre' }) : 'Fibre' },
    { value: 'irrigation', label: translate ? translate('commercial.bkb.category.irrigation', { fallback: 'Irrigation' }) : 'Irrigation' },
    { value: 'grazing', label: translate ? translate('commercial.bkb.category.grazing', { fallback: 'Grazing' }) : 'Grazing' },
    { value: 'other', label: translate ? translate('commercial.bkb.category.other', { fallback: 'Other' }) : 'Other' }
  ];

  const provinces = [
    'Eastern Cape',
    'Free State',
    'Gauteng',
    'KwaZulu-Natal',
    'Limpopo',
    'Mpumalanga',
    'North West',
    'Northern Cape',
    'Western Cape'
  ];

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        mb: 3
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <FilterList sx={{ mr: 1, color: theme.palette.primary.main }} />
        <Typography variant="subtitle1" fontWeight="bold">
          {translate ? translate('commercial.bkb.filter_auctions', { fallback: 'Filter Auctions' }) : 'Filter Auctions'}
        </Typography>
      </Box>

      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="auction-type-label">
              {translate ? translate('commercial.bkb.auction_type', { fallback: 'Auction Type' }) : 'Auction Type'}
            </InputLabel>
            <Select
              labelId="auction-type-label"
              multiple
              value={filter.types || []}
              onChange={handleTypeChange}
              input={<OutlinedInput label={translate ? translate('commercial.bkb.auction_type', { fallback: 'Auction Type' }) : 'Auction Type'} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip
                      key={value}
                      label={auctionTypes.find(type => type.value === value)?.label || value}
                      size="small"
                    />
                  ))}
                </Box>
              )}
            >
              {auctionTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="auction-category-label">
              {translate ? translate('commercial.bkb.category', { fallback: 'Category' }) : 'Category'}
            </InputLabel>
            <Select
              labelId="auction-category-label"
              multiple
              value={filter.categories || []}
              onChange={handleCategoryChange}
              input={<OutlinedInput label={translate ? translate('commercial.bkb.category', { fallback: 'Category' }) : 'Category'} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip
                      key={value}
                      label={auctionCategories.find(cat => cat.value === value)?.label || value}
                      size="small"
                    />
                  ))}
                </Box>
              )}
            >
              {auctionCategories.map((category) => (
                <MenuItem key={category.value} value={category.value}>
                  {category.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth size="small">
            <InputLabel id="auction-province-label">
              {translate ? translate('commercial.bkb.province', { fallback: 'Province' }) : 'Province'}
            </InputLabel>
            <Select
              labelId="auction-province-label"
              multiple
              value={filter.provinces || []}
              onChange={handleProvinceChange}
              input={<OutlinedInput label={translate ? translate('commercial.bkb.province', { fallback: 'Province' }) : 'Province'} />}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </Box>
              )}
            >
              {provinces.map((province) => (
                <MenuItem key={province} value={province}>
                  {province}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
        <CustomButton
          variant="outlined"
          startIcon={<Clear />}
          onClick={handleClearFilters}
          size="small"
          sx={{ mr: 1 }}
        >
          {translate ? translate('common.clear', { fallback: 'Clear' }) : 'Clear'}
        </CustomButton>
      </Box>
    </Paper>
  );
};

export default BkbAuctionFilters;
