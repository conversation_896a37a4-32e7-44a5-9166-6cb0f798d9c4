import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

// Import jsPDF with autoTable
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

// Add this to fix the TypeScript error
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export const exportToCSV = (data: any[], filename: string) => {
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row =>
      headers.map(header =>
        JSON.stringify(row[header])
      ).join(',')
    )
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, `${filename}.csv`);
};

export const exportToExcel = (data: any[], filename: string) => {
  const worksheet = XLSX.utils.json_to_sheet(data);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Data');

  // Generate Excel file
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
  const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  saveAs(blob, `${filename}.xlsx`);
};

export const exportToPDF = (data: any[], filename: string) => {
  const doc = new jsPDF();
  const headers = Object.keys(data[0]);

  doc.autoTable({
    head: [headers],
    body: data.map(row => headers.map(header => row[header])),
    margin: { top: 20 },
  });

  doc.save(`${filename}.pdf`);
};

