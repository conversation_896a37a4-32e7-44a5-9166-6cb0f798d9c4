import React from 'react';
import CustomButton from '../components/common/CustomButton';
import { motion } from 'framer-motion';

// Animation variants for common components
export const fadeIn = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.5 } }
};

export const slideUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

export const slideDown = {
  hidden: { opacity: 0, y: -20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

export const slideLeft = {
  hidden: { opacity: 0, x: 20 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.5 } }
};

export const slideRight = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.5 } }
};

export const scale = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.5 } }
};

export const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Card animation with hover effect
export const cardAnimation = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  hover: {
    y: -10,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: { duration: 0.3 }
  }
};

// Button animation with hover effect
export const buttonAnimation = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } },
  hover: {
    scale: 1.05,
    transition: { duration: 0.2 }
  },
  tap: {
    scale: 0.95,
    transition: { duration: 0.1 }
  }
};

// Table row animation
export const tableRowAnimation = {
  hidden: { opacity: 0, x: -10 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  hover: {
    backgroundColor: "rgba(255, 255, 255, 0.05)",
    x: 5,
    transition: { duration: 0.2 }
  }
};

// Icon animation with pulse effect
export const iconAnimation = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1, transition: { duration: 0.3 } },
  pulse: {
    scale: [1, 1.1, 1],
    transition: { duration: 1, repeat: Infinity, repeatType: "mirror" }
  }
};

// Gradient animation for backgrounds
export const gradientAnimation = {
  animate: {
    background: [
      "linear-gradient(135deg, rgba(10, 25, 50, 0.98), rgba(29, 55, 90, 0.95))",
      "linear-gradient(135deg, rgba(15, 30, 60, 0.98), rgba(35, 65, 100, 0.95))",
      "linear-gradient(135deg, rgba(10, 25, 50, 0.98), rgba(29, 55, 90, 0.95))"
    ],
    transition: { duration: 5, repeat: Infinity, repeatType: "reverse" }
  }
};

// Animated Card Component
export const AnimatedCard: React.FC<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent) => void;
  [key: string]: any; // Allow any other props to be passed through
}> = ({ children, className = "", style = {}, onClick, ...rest }) => {
  return (
    <motion.div
      variants={cardAnimation}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className={className}
      style={style}
      onClick={onClick}
      {...rest}
    >
      {children}
    </motion.div>
  );
};

// Animated Button Component
export const AnimatedButton: React.FC<{
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  style?: React.CSSProperties;
  variant?: 'text' | 'outlined' | 'contained';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default';
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  [key: string]: any; // Allow any other props to be passed through
}> = ({
  children,
  onClick,
  className = "",
  style = {},
  variant = 'contained',
  color = 'primary',
  startIcon,
  endIcon,
  ...rest
}) => {
  // Filter out props that should go to the Button vs the motion.div
  const {
    disabled,
    fullWidth,
    href,
    size,
    type,
    ...motionProps
  } = rest;

  const buttonProps = { disabled, fullWidth, href, size, type };

  return (
    <motion.div
      variants={buttonAnimation}
      initial="hidden"
      animate="visible"
      whileHover={!disabled ? "hover" : undefined}
      whileTap={!disabled ? "tap" : undefined}
      style={{ display: 'inline-block' }}
      {...motionProps}
    >
      <CustomButton
        variant={variant}
        color={color}
        onClick={onClick}
        className={className}
        style={style}
        startIcon={startIcon}
        endIcon={endIcon}
        {...buttonProps}
      >
        {children}
      </CustomButton>
    </motion.div>
  );
};

// Animated Icon Component
export const AnimatedIcon: React.FC<{
  children: React.ReactNode;
  pulse?: boolean;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent) => void;
  [key: string]: any; // Allow any other props to be passed through
}> = ({ children, pulse = false, className = "", style = {}, onClick, ...rest }) => {
  return (
    <motion.div
      variants={iconAnimation}
      initial="hidden"
      animate={pulse ? "pulse" : "visible"}
      className={className}
      style={style}
      onClick={onClick}
      {...rest}
    >
      {children}
    </motion.div>
  );
};

// Staggered Container for lists
export const StaggerContainer: React.FC<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent) => void;
  [key: string]: any; // Allow any other props to be passed through
}> = ({ children, className = "", style = {}, onClick, ...rest }) => {
  return (
    <motion.div
      variants={staggerContainer}
      initial="hidden"
      animate="visible"
      className={className}
      style={style}
      onClick={onClick}
      {...rest}
    >
      {children}
    </motion.div>
  );
};

// Staggered Item for use within StaggerContainer
export const StaggerItem: React.FC<{
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  onClick?: (e: React.MouseEvent) => void;
  [key: string]: any; // Allow any other props to be passed through
}> = ({ children, className = "", style = {}, onClick, ...rest }) => {
  return (
    <motion.div
      variants={slideUp}
      className={className}
      style={style}
      onClick={onClick}
      {...rest}
    >
      {children}
    </motion.div>
  );
};
