import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Typography, Box, Chip, IconButton, useTheme, List, ListItem, ListItemText, ListItemAvatar, Avatar, Card, CardContent } from '@mui/material';
// Import card styling utilities
import { getCardStyle } from '../../utils/cardStyles';
import {
  Add,
  LocalHospital,
  Search,
  FilterList,
  MoreVert,
  ArrowForward,
  CalendarToday,
  CheckCircle,
  Cancel,
  Schedule,
  HealthAndSafety,
  TrendingUp
 } from '../../utils/iconImports';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  LineChart,
  Line,
  CartesianGrid,
  XAxis,
  YAxis,
  Legend
} from 'recharts';
import { EnhancedPieLabelRenderProps } from '../../types/recharts';
import { useHealthRecords } from '../../hooks/useHealthRecords';
import HealthRecords from './HealthRecords';
import {  AnimatedBackgroundCard, ModuleContainer, ModuleHeaderCard, withSubModuleTranslation, StandardDashboard, ModernChart , CustomButton } from '../../components/common';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Removed unused interface

// Removed unused TabPanel component

interface HealthDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const HealthDashboard: React.FC<HealthDashboardProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { healthRecords, stats } = useHealthRecords();

  // State for chart interactions
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedDataType, setSelectedDataType] = useState<string | null>(null);
  const [lineChartTooltip, setLineChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });

  // We're keeping only the necessary state variables

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // We've removed unused event handlers

  // Prepare data for type chart
  const typeData = stats?.byType ? Object.entries(stats.byType).map(([name, value]) => ({
    name: name.charAt(0).toUpperCase() + name.slice(1),
    value,
  })) : [];

  // Commented out unused data
  // const statusData = stats?.byStatus ? Object.entries(stats.byStatus).map(([name, value]) => ({
  //   name: name.charAt(0).toUpperCase() + name.slice(1),
  //   value,
  // })) : [];

  // Get upcoming health events
  const upcomingEvents = healthRecords
    .filter(record => record.status === 'scheduled')
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    .slice(0, 5);

  // Commented out unused data
  // const recentRecords = [...healthRecords]
  //   .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  //   .slice(0, 10);

  // Prepare data for health trends chart (mock data for demonstration)
  const healthTrendsData = [
    { month: 'Jan', vaccinations: 12, treatments: 8, checkups: 15, issues: 3 },
    { month: 'Feb', vaccinations: 18, treatments: 6, checkups: 12, issues: 2 },
    { month: 'Mar', vaccinations: 15, treatments: 10, checkups: 18, issues: 4 },
    { month: 'Apr', vaccinations: 20, treatments: 12, checkups: 22, issues: 5 },
    { month: 'May', vaccinations: 25, treatments: 8, checkups: 20, issues: 3 },
    { month: 'Jun', vaccinations: 22, treatments: 5, checkups: 18, issues: 2 },
  ];

  // Commented out unused data
  // const vaccinationComplianceData = [
  //   { name: 'On Schedule', value: 85 },
  //   { name: 'Delayed', value: 10 },
  //   { name: 'Missed', value: 5 }
  // ];

  // Commented out unused data
  // const healthIssuesByAnimalType = [
  //   { animal: 'Cattle', respiratory: 8, digestive: 5, reproductive: 3, injury: 7, parasitic: 4 },
  //   { animal: 'Sheep', respiratory: 4, digestive: 7, reproductive: 2, injury: 3, parasitic: 6 },
  //   { animal: 'Goats', respiratory: 3, digestive: 6, reproductive: 1, injury: 2, parasitic: 5 },
  //   { animal: 'Pigs', respiratory: 6, digestive: 8, reproductive: 4, injury: 5, parasitic: 3 }
  // ];

  // Commented out unused data
  // const treatmentSuccessData = [
  //   { name: 'Successful', value: 78 },
  //   { name: 'Partial', value: 15 },
  //   { name: 'Unsuccessful', value: 7 }
  // ];

  // Commented out unused data
  // const healthCostData = [
  //   { month: 'Jan', preventive: 3500, treatment: 1200, emergency: 800 },
  //   { month: 'Feb', preventive: 2800, treatment: 1500, emergency: 600 },
  //   { month: 'Mar', preventive: 3200, treatment: 1800, emergency: 1200 },
  //   { month: 'Apr', preventive: 4000, treatment: 1300, emergency: 500 },
  //   { month: 'May', preventive: 3800, treatment: 1100, emergency: 700 },
  //   { month: 'Jun', preventive: 4200, treatment: 1400, emergency: 900 }
  // ];

  // We'll just use the healthCostData directly in the UI

  // Commented out unused data
  // const costPerAnimal = stats?.totalAnimals ? (totalHealthCost / stats.totalAnimals).toFixed(2) : 0;
  // const preventiveToTreatmentRatio = totalTreatmentCost ? (totalPreventiveCost / totalTreatmentCost).toFixed(2) : 0;

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translateModuleField ? translateModuleField('total_records', "Total Records") : "Total Records",
      value: stats?.totalRecords || 0,
      icon: <LocalHospital />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('active_treatments', "Active Treatments") : "Active Treatments",
      value: stats?.activeTreatments || 0,
      icon: <HealthAndSafety />,
      color: theme.palette.success.main,
      trend: {
        value: 2,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('vaccinations_due', "Vaccinations Due") : "Vaccinations Due",
      value: stats?.vaccinationsDue || 0,
      icon: <Schedule />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: false,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('health_alerts', "Health Alerts") : "Health Alerts",
      value: stats?.healthAlerts || 0,
      icon: <Cancel />,
      color: theme.palette.error.main,
      trend: {
        value: 1,
        isPositive: false,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translateModuleField ? translateModuleField('add_health_record', "Add Health Record") : "Add Health Record",
      icon: <Add />,
      onClick: () => navigate('/health/new'),
      color: 'primary'
    }
  ];

  // Prepare dashboard tabs
  const dashboardTabs = [
    {
      label: translateModuleField ? translateModuleField('overview', "Overview") : "Overview",
      icon: <Search />,
      content: (
        <Box>
          {/* Stats Cards */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-4"
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
              {translateModuleField ? translateModuleField('health_statistics', "Health Statistics") : "Health Statistics"}
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={translateModuleField ? translateModuleField('total_records', "Total Records") : "Total Records"}
                    subtitle={`${stats?.totalRecords || 0} ${translateModuleField ? translateModuleField('health_records', "health records") : "health records"}`}
                    module="health"
                    uniqueId="health-total"
                    icon={<LocalHospital />}
                    height={160}
                    accentColor={theme.palette.primary.main}
                    backgroundImage="/images/modules/health/veterinary-1.jpg"
                  />
                </motion.div>
              </Grid>

              <Grid item xs={12} md={3}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={translateModuleField ? translateModuleField('completed', "Completed") : "Completed"}
                    subtitle={`${stats?.byStatus?.completed || 0} records (${Math.round((stats?.byStatus?.completed / stats?.totalRecords) * 100 || 0)}%)`}
                    module="health"
                    uniqueId="health-completed"
                    icon={<CheckCircle />}
                    height={160}
                    accentColor={theme.palette.success.main}
                    secondaryColor={theme.palette.success.dark}
                    backgroundImage="/images/modules/health/veterinary-2.jpg"
                  />
                </motion.div>
              </Grid>

              <Grid item xs={12} md={3}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={translateModuleField ? translateModuleField('scheduled', "Scheduled") : "Scheduled"}
                    subtitle={`${stats?.byStatus?.scheduled || 0} records (${Math.round((stats?.byStatus?.scheduled / stats?.totalRecords) * 100 || 0)}%)`}
                    module="health"
                    uniqueId="health-scheduled"
                    icon={<Schedule />}
                    height={160}
                    accentColor={theme.palette.warning.main}
                    secondaryColor={theme.palette.warning.dark}
                    backgroundImage="/images/modules/health/veterinary-3.jpg"
                  />
                </motion.div>
              </Grid>

              <Grid item xs={12} md={3}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={translateModuleField ? translateModuleField('cancelled', "Cancelled") : "Cancelled"}
                    subtitle={`${stats?.byStatus?.cancelled || 0} records (${Math.round((stats?.byStatus?.cancelled / stats?.totalRecords) * 100 || 0)}%)`}
                    module="health"
                    uniqueId="health-cancelled"
                    icon={<Cancel />}
                    height={160}
                    accentColor={theme.palette.error.main}
                    secondaryColor={theme.palette.error.dark}
                    backgroundImage="/images/modules/health/veterinary-main.jpg"
                  />
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>

          {/* Charts and Lists Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-4"
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
              {translateModuleField ? translateModuleField('health_analytics', "Health Analytics") : "Health Analytics"}
            </Typography>

            <Grid container spacing={3} sx={{ '& .recharts-responsive-container': { minHeight: { xs: 200, sm: 250, md: 300 } } }}>
              <Grid item xs={12} md={6}>
                <ModernChart
                  title={translateModuleField ? translateModuleField('records_by_type', "Records by Type") : "Records by Type"}
                  subtitle={translateModuleField ? translateModuleField('records_by_type_desc', "Distribution of health records by type") : "Distribution of health records by type"}
                  data={typeData}
                  type="pie"
                  dataKeys={['value']}
                  height={350}
                  accentColor={theme.palette.primary.main}
                  allowChartTypeChange={true}
                  module="health"
                  tooltip={translateModuleField ? translateModuleField('records_by_type_help', "Shows the distribution of different types of health records") : "Shows the distribution of different types of health records"}
                  formatValue={(value) => `${value} records`}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={translateModuleField ? translateModuleField('upcoming_health_events', "Upcoming Health Events") : "Upcoming Health Events"}
                    module="health"
                    uniqueId="health-events"
                    height={400}
                    icon={<CalendarToday />}
                    accentColor={theme.palette.info.main}
                    backgroundImage="/images/modules/health/veterinary-1.jpg"
                    content={
                      <List>
                        {upcomingEvents.length > 0 ? (
                          upcomingEvents.map((event) => (
                            <ListItem key={event.id} divider>
                              <ListItemAvatar>
                                <Avatar sx={{ bgcolor: 'primary.light' }}>
                                  <CalendarToday />
                                </Avatar>
                              </ListItemAvatar>
                              <ListItemText
                                primary={event.description}
                                secondary={`${new Date(event.date).toLocaleDateString()} - Animal ID: ${event.animalId}`}
                              />
                              <Chip
                                label={event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                                color="primary"
                                size="small"
                                sx={{ mr: 1 }}
                              />
                              <IconButton size="small" onClick={() => navigate(`/health/records/${event.id}`)}>
                                <ArrowForward fontSize="small" />
                              </IconButton>
                            </ListItem>
                          ))
                        ) : (
                          <ListItem>
                            <ListItemText primary="No upcoming events" />
                          </ListItem>
                        )}
                      </List>
                    }
                  />
                </motion.div>
              </Grid>
            </Grid>
          </motion.div>
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('trends', "Trends") : "Trends",
      icon: <TrendingUp />,
      content: (
        <Box>
          {/* Health Trends Chart */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-4"
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
              {translateModuleField ? translateModuleField('health_trends', "Health Trends Over Time") : "Health Trends Over Time"}
            </Typography>

            <ModernChart
              title={translateModuleField ? translateModuleField('monthly_health_activities', "Monthly Health Activities") : "Monthly Health Activities"}
              subtitle={translateModuleField ? translateModuleField('monthly_health_activities_desc', "Track vaccinations, treatments, and checkups over time") : "Track vaccinations, treatments, and checkups over time"}
              data={healthTrendsData}
              type="line"
              dataKeys={['vaccinations', 'treatments', 'checkups', 'issues']}
              xAxisDataKey="month"
              height={400}
              accentColor={theme.palette.primary.main}
              allowChartTypeChange={true}
              allowTimeRangeChange={true}
              module="health"
              tooltip={translateModuleField ? translateModuleField('monthly_health_activities_help', "Shows health activities by month") : "Shows health activities by month"}
              formatValue={(value) => `${value} records`}
              onRefresh={() => {
                // Refresh data logic would go here
                console.log('Refreshing health trends data');
              }}
            />
          </motion.div>

          {/* Line Chart Interaction Feedback */}
          {lineChartTooltip.show && (
            <motion.div
              variants={itemVariants}
              initial="hidden"
              animate="visible"
            >
              <Card sx={{
                ...getCardStyle('health', theme),
                mb: 4,
                overflow: 'hidden'
              }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Box display="flex" alignItems="center">
                      <HealthAndSafety sx={{ color: 'white', mr: 1 }} />
                      <Typography variant="h6" fontWeight="bold" color="white">
                        Health Trends Analysis
                      </Typography>
                    </Box>
                    <IconButton size="small" onClick={() => setLineChartTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>
                      <MoreVert />
                    </IconButton>
                  </Box>
                  <Typography variant="body1" mt={1} color="white">
                    {lineChartTooltip.content}
                  </Typography>
                </CardContent>
              </Card>
              {selectedMonth && selectedDataType && (
                <Box mt={2}>
                  <CustomButton
                    variant="outlined"
                    color="primary"
                    size="small"
                    onClick={() => navigate(`/health/records?month=${selectedMonth}&type=${selectedDataType}`)}
                  >
                    View {selectedMonth} {selectedDataType}
                  </CustomButton>
                  <Typography variant="body2" color="text.secondary" mt={1}>
                    {selectedDataType === 'vaccinations' && 'Vaccination trends indicate seasonal patterns. Consider scheduling preventive care accordingly.'}
                    {selectedDataType === 'treatments' && 'Treatment frequency analysis can help identify recurring health issues in your herd.'}
                    {selectedDataType === 'checkups' && 'Regular checkups contribute to early detection and better overall herd health.'}
                  </Typography>
                </Box>
              )}
            </motion.div>
          )}
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('records', "Records") : "Records",
      icon: <LocalHospital />,
      content: (
        <Box>
          {/* Recent Records */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="mb-4"
          >
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
              Recent Health Records
            </Typography>

            <motion.div variants={itemVariants}>
              <AnimatedBackgroundCard
                title="Health Records"
                subtitle="View and manage recent health records"
                module="health"
                uniqueId="health-records"
                height={500}
                accentColor={theme.palette.primary.main}
                backgroundImage="/images/modules/health/veterinary-main.jpg"
                action={(
                  <Box>
                    <IconButton size="small">
                      <Search />
                    </IconButton>
                    <IconButton size="small">
                      <FilterList />
                    </IconButton>
                  </Box>
                )}
              >
                <HealthRecords limit={5} showViewAll={true} />
              </AnimatedBackgroundCard>
            </motion.div>
          </motion.div>
        </Box>
      )
    }
  ];

  return (
    <StandardDashboard
      title={translateSubModule ? translateSubModule('title', "Health Management") : "Health Management"}
      subtitle={translateSubModule ? translateSubModule('subtitle', "Monitor and manage animal health records, treatments, vaccinations, and medical history") : "Monitor and manage animal health records, treatments, vaccinations, and medical history"}
      icon={<LocalHospital />}
      stats={dashboardStats}
      actions={dashboardActions}
      tabs={dashboardTabs}
      activeTab={0}
      isLoading={false}
      loadingMessage={translateModuleField ? translateModuleField('loading', "Loading health data...") : "Loading health data..."}
      module="health"
    >
    </StandardDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(HealthDashboard, 'health', 'dashboard');
