import { Animal } from '../types/animal';
import { mockAnimals } from '../mocks/animalData';
import { mockBreedingRecords } from '../mocks/breedingData';
import { mockHealthRecords } from '../mocks/healthData';
import { mockFeedingRecords } from '../mocks/feedingData';

/**
 * Mock MongoDB service that uses local data instead of connecting to MongoDB
 * This is used when MongoDB connection fails or is not available
 */
export class MockMongoService {
  private collection: string;

  constructor(collection: string) {
    this.collection = collection;
  }

  // MongoDB Collection methods
  find(query = {}, options = {}) {
    console.log(`Mock find for ${this.collection} with query:`, query);
    return {
      toArray: async () => this.getMockData(query)
    };
  }

  async findOne(query: any) {
    console.log(`Mock findOne for ${this.collection} with query:`, query);
    const data = await this.getMockData(query);
    return data.length > 0 ? data[0] : null;
  }

  async insertOne(data: any) {
    console.log(`Mock insertOne for ${this.collection} with data:`, data);
    return { insertedId: 'mock-id-' + Date.now() };
  }

  async updateOne(filter: any, update: any) {
    console.log(`Mock updateOne for ${this.collection} with filter:`, filter, 'and update:', update);
    return { modifiedCount: 1 };
  }

  async deleteOne(filter: any) {
    console.log(`Mock deleteOne for ${this.collection} with filter:`, filter);
    return { deletedCount: 1 };
  }

  async countDocuments(query: any) {
    console.log(`Mock countDocuments for ${this.collection} with query:`, query);
    const data = await this.getMockData(query);
    return data.length;
  }

  // Helper methods
  private async getMockData(query = {}) {
    // Return mock data based on collection
    switch (this.collection) {
      case 'animals':
        return mockAnimals.map(animal => ({
          ...animal,
          _id: animal.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      case 'breeding':
        return mockBreedingRecords.map(record => ({
          ...record,
          _id: record.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      case 'health':
        return mockHealthRecords.map(record => ({
          ...record,
          _id: record.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      case 'feeding':
        return mockFeedingRecords.map(record => ({
          ...record,
          _id: record.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }));
      default:
        return [];
    }
  }

  // Legacy methods for backward compatibility
  async findAll(query = {}) {
    console.log(`Mock findAll for ${this.collection} with query:`, query);
    return this.getMockData(query);
  }

  async findById(id: string) {
    console.log(`Mock findById for ${this.collection} with id:`, id);
    const data = await this.getMockData({ id });
    return data.length > 0 ? data[0] : null;
  }
}

/**
 * Mock function to get a collection from MongoDB
 * @param collectionName Name of the collection
 * @returns Mock collection
 */
export const getMockCollection = (collectionName: string) => {
  return new MockMongoService(collectionName);
};
