import React from 'react';
import { Box, Typography, Paper, alpha, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { CustomButton } from '../common';

interface FinancialAnalyticsCardProps {
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  height?: string | number;
  accentColor?: string;
  secondaryColor?: string;
  actionLabel?: string;
  onAction?: () => void;
  loading?: boolean;
  delay?: number;
}

/**
 * Custom card component for financial module
 * Based on the BusinessAnalyticsCard design pattern
 */
const FinancialAnalyticsCard: React.FC<FinancialAnalyticsCardProps> = ({
  title,
  subtitle,
  icon,
  children,
  height = 'auto',
  accentColor,
  secondaryColor,
  actionLabel,
  onAction,
  loading = false,
  delay = 0
}) => {
  const theme = useTheme();
  
  // Use provided colors or fallback to theme colors
  const primaryColor = accentColor || '#2196f3'; // Financial module blue
  const secondaryColorValue = secondaryColor || '#1976d2'; // Darker blue

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        delay: delay,
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      style={{ height: '100%' }}
    >
      <Paper
        elevation={3}
        sx={{
          height: height,
          borderRadius: '12px',
          overflow: 'hidden',
          position: 'relative',
          transition: 'all 0.3s ease',
          background: `linear-gradient(135deg, 
            ${alpha(theme.palette.background.paper, 0.8)}, 
            ${alpha(theme.palette.background.paper, 0.6)})`,
          backdropFilter: 'blur(10px)',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 30px rgba(0,0,0,0.12)'
          }
        }}
      >
        {/* Top accent bar */}
        <Box
          sx={{
            height: '4px',
            width: '100%',
            background: `linear-gradient(to right, ${primaryColor}, ${secondaryColorValue})`,
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        />
        
        {/* Card Header */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 2,
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {icon && (
              <Box
                sx={{
                  mr: 1.5,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: primaryColor
                }}
              >
                {icon}
              </Box>
            )}
            <Box>
              <Typography variant="h6" fontWeight="medium">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
          
          {actionLabel && onAction && (
            <CustomButton
              variant="contained"
              color="primary"
              onClick={onAction}
              size="small"
            >
              {actionLabel}
            </CustomButton>
          )}
        </Box>

        {/* Card Content */}
        <Box
          sx={{
            p: 2,
            position: 'relative',
            height: 'calc(100% - 72px)',
            overflow: 'auto'
          }}
        >
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                width: '100%'
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  border: `3px solid ${alpha(primaryColor, 0.2)}`,
                  borderTop: `3px solid ${primaryColor}`,
                  animation: 'spin 1s linear infinite',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' }
                  }
                }}
              />
            </Box>
          ) : (
            children
          )}
        </Box>
      </Paper>
    </motion.div>
  );
};

export default FinancialAnalyticsCard;
