import React from 'react';
import { CustomButton } from '../common';

// Define the CustomButtonProps interface
interface CustomButtonProps {
  variant?: 'contained' | 'outlined' | 'text' | 'gradient';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'default';
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  children: React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  size?: 'small' | 'medium' | 'large';
  style?: React.CSSProperties;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  href?: string;
  target?: string;
  rel?: string;
  animate?: boolean;
  sx?: any; // Add support for sx prop to match MUI Button API
}

/**
 * A safe Button component that doesn't rely on the theme
 * This helps prevent "Cannot read properties of undefined (reading 'dark')" errors
 */
const GlobalSafeButton: React.FC<CustomButtonProps> = (props) => {
  // Define color mappings
  const colorMap: Record<string, { main: string, dark: string, light: string, contrastText: string }> = {
    primary: {
      main: '#3AA99F',
      dark: '#2A8A82',
      light: '#4FBEB4',
      contrastText: '#ffffff'
    },
    secondary: {
      main: '#38B2AC',
      dark: '#2C8A84',
      light: '#4FD1CB',
      contrastText: '#ffffff'
    },
    error: {
      main: '#ef4444',
      dark: '#dc2626',
      light: '#f87171',
      contrastText: '#ffffff'
    },
    warning: {
      main: '#f59e0b',
      dark: '#d97706',
      light: '#fbbf24',
      contrastText: '#ffffff'
    },
    info: {
      main: '#3b82f6',
      dark: '#2563eb',
      light: '#60a5fa',
      contrastText: '#ffffff'
    },
    success: {
      main: '#10b981',
      dark: '#059669',
      light: '#34d399',
      contrastText: '#ffffff'
    }
  };

  // Get the color values
  const colorValues = colorMap[props.color as string] || colorMap.primary;

  // Define the styles based on the variant and color
  const getStyles = () => {
    const styles: React.CSSProperties = {};

    // Apply variant-specific styles
    if (props.variant === 'contained') {
      styles.backgroundColor = colorValues.main;
      styles.color = colorValues.contrastText;
    } else if (props.variant === 'outlined') {
      styles.backgroundColor = 'transparent';
      styles.color = colorValues.main;
      styles.border = `1px solid ${colorValues.main}`;
    } else if (props.variant === 'text') {
      styles.backgroundColor = 'transparent';
      styles.color = colorValues.main;
    }

    // Apply disabled styles
    if (props.disabled) {
      styles.opacity = 0.5;
      styles.pointerEvents = 'none';
    }

    return styles;
  };

  // Get the hover styles
  const getHoverStyles = () => {
    const styles: React.CSSProperties = {};

    // Apply variant-specific hover styles
    if (props.variant === 'contained') {
      styles.backgroundColor = colorValues.dark;
    } else if (props.variant === 'outlined') {
      styles.backgroundColor = `${colorValues.main}20`; // 20% opacity
    } else if (props.variant === 'text') {
      styles.backgroundColor = `${colorValues.main}10`; // 10% opacity
    }

    return styles;
  };

  // Get the styles
  const styles = getStyles();

  // Get the hover styles
  const hoverStyles = getHoverStyles();

  // Create a new props object without the color and variant props
  const { color, variant, ...otherProps } = props;

  return (
    <CustomButton
      {...otherProps}
      className={`mui-button-fixed ${props.className || ''}`}
      sx={{
        ...styles,
        '&:hover': {
          ...hoverStyles
        },
        ...props.sx
      }}
    />
  );
};

export default GlobalSafeButton;
