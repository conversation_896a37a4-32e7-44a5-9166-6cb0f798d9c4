/**
 * Global Theme Wrapper
 * 
 * This component applies comprehensive theming across all pages
 * Features:
 * - Dynamic gradient backgrounds for all pages
 * - Module-specific theme variations
 * - Responsive design
 * - Animation support
 * - Theme persistence
 */

import React, { useEffect, useState } from 'react';
import { Box, useTheme, alpha } from '@mui/material';
import { useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useThemeContext } from '../../contexts/ThemeContext';
import EnhancedGradientBackground from './EnhancedGradientBackground';

interface GlobalThemeWrapperProps {
  children: React.ReactNode;
}

const GlobalThemeWrapper: React.FC<GlobalThemeWrapperProps> = ({ children }) => {
  const theme = useTheme();
  const location = useLocation();
  const { currentColor, availableColors, showThemeAnimations, blendMode } = useThemeContext();
  const [currentModule, setCurrentModule] = useState<string>('dashboard');
  const [backgroundVariant, setBackgroundVariant] = useState<'subtle' | 'vibrant' | 'mesh' | 'animated' | 'particles'>('mesh');

  // Determine current module from path
  useEffect(() => {
    const path = location.pathname;
    
    if (path.includes('/animals')) setCurrentModule('animals');
    else if (path.includes('/health')) setCurrentModule('health');
    else if (path.includes('/breeding')) setCurrentModule('breeding');
    else if (path.includes('/feeding')) setCurrentModule('feeding');
    else if (path.includes('/financial')) setCurrentModule('financial');
    else if (path.includes('/inventory')) setCurrentModule('inventory');
    else if (path.includes('/commercial')) setCurrentModule('commercial');
    else if (path.includes('/reports')) setCurrentModule('reports');
    else if (path.includes('/analytics')) setCurrentModule('analytics');
    else if (path.includes('/resources')) setCurrentModule('resources');
    else if (path.includes('/compliance')) setCurrentModule('compliance');
    else if (path.includes('/settings')) setCurrentModule('settings');
    else if (path.includes('/login')) setCurrentModule('login');
    else setCurrentModule('dashboard');
  }, [location.pathname]);

  // Set background variant based on module
  useEffect(() => {
    const moduleVariants: Record<string, typeof backgroundVariant> = {
      dashboard: 'mesh',
      animals: 'animated',
      health: 'subtle',
      breeding: 'vibrant',
      feeding: 'mesh',
      financial: 'animated',
      inventory: 'subtle',
      commercial: 'vibrant',
      reports: 'mesh',
      analytics: 'animated',
      resources: 'subtle',
      compliance: 'vibrant',
      settings: 'mesh',
      login: 'animated'
    };

    setBackgroundVariant(moduleVariants[currentModule] || 'mesh');
  }, [currentModule]);

  // Get module-specific theme colors
  const getModuleThemeColors = () => {
    const baseColors = availableColors[currentColor];
    
    // Module-specific color variations
    const moduleColorAdjustments: Record<string, { primary?: string; secondary?: string }> = {
      animals: { primary: baseColors.primary, secondary: baseColors.secondary },
      health: { primary: '#10b981', secondary: '#34d399' }, // Green for health
      breeding: { primary: '#8b5cf6', secondary: '#a78bfa' }, // Purple for breeding
      feeding: { primary: '#f59e0b', secondary: '#fbbf24' }, // Orange for feeding
      financial: { primary: '#059669', secondary: '#10b981' }, // Green for financial
      inventory: { primary: '#6366f1', secondary: '#818cf8' }, // Indigo for inventory
      commercial: { primary: '#dc2626', secondary: '#ef4444' }, // Red for commercial
      reports: { primary: '#0891b2', secondary: '#06b6d4' }, // Cyan for reports
      analytics: { primary: '#7c3aed', secondary: '#8b5cf6' }, // Violet for analytics
      resources: { primary: '#059669', secondary: '#10b981' }, // Green for resources
      compliance: { primary: '#dc2626', secondary: '#ef4444' }, // Red for compliance
      settings: { primary: '#6b7280', secondary: '#9ca3af' }, // Gray for settings
    };

    return moduleColorAdjustments[currentModule] || baseColors;
  };

  const moduleColors = getModuleThemeColors();

  // Page transition variants
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.4
  };

  // Get background based on blend mode
  const getBackground = () => {
    if (blendMode === 'lightBlended' || blendMode === 'darkBlended') {
      return `linear-gradient(135deg, ${alpha(moduleColors.primary || theme.palette.primary.main, 0.1)}, ${alpha(moduleColors.secondary || theme.palette.secondary.main, 0.05)})`;
    }
    return theme.palette.background.default;
  };

  // Don't apply enhanced background to login page unless it's blended mode
  if (location.pathname.includes('/login')) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: getBackground(),
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            style={{ minHeight: '100vh' }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden',
        background: blendMode === 'lightBlended' || blendMode === 'darkBlended' ? 'transparent' : getBackground()
      }}
    >
      {/* Enhanced Gradient Background - only for blended modes */}
      {(blendMode === 'lightBlended' || blendMode === 'darkBlended') && (
        <EnhancedGradientBackground
          module={currentModule}
          variant={backgroundVariant}
          enableAnimation={showThemeAnimations}
          enableParticles={currentModule === 'dashboard' || currentModule === 'analytics'}
          opacity={0.8}
          height="100vh"
          className="global-theme-background"
        >
        {/* Content Container */}
        <Box
          sx={{
            position: 'relative',
            zIndex: 2,
            minHeight: '100vh',
            background: 'transparent',
            pointerEvents: 'auto'
          }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={location.pathname}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
              style={{ minHeight: '100vh' }}
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </Box>

        {/* Module-specific decorative elements */}
        {currentModule === 'dashboard' && (
          <Box
            sx={{
              position: 'absolute',
              top: '10%',
              right: '5%',
              width: 200,
              height: 200,
              borderRadius: '50%',
              background: `radial-gradient(circle, ${alpha(moduleColors.primary || theme.palette.primary.main, 0.1)}, transparent)`,
              zIndex: 1,
              animation: showThemeAnimations ? 'float 6s ease-in-out infinite' : 'none',
              '@keyframes float': {
                '0%, 100%': { transform: 'translateY(0px)' },
                '50%': { transform: 'translateY(-20px)' }
              }
            }}
          />
        )}

        {currentModule === 'animals' && (
          <Box
            sx={{
              position: 'absolute',
              bottom: '10%',
              left: '5%',
              width: 150,
              height: 150,
              borderRadius: '50%',
              background: `radial-gradient(circle, ${alpha(moduleColors.secondary || theme.palette.secondary.main, 0.15)}, transparent)`,
              zIndex: 1,
              animation: showThemeAnimations ? 'pulse 4s ease-in-out infinite' : 'none',
              '@keyframes pulse': {
                '0%, 100%': { transform: 'scale(1)', opacity: 0.7 },
                '50%': { transform: 'scale(1.1)', opacity: 1 }
              }
            }}
          />
        )}

        {(currentModule === 'reports' || currentModule === 'analytics') && (
          <>
            <Box
              sx={{
                position: 'absolute',
                top: '20%',
                left: '10%',
                width: 100,
                height: 100,
                borderRadius: '50%',
                background: `linear-gradient(45deg, ${alpha(moduleColors.primary || theme.palette.primary.main, 0.2)}, transparent)`,
                zIndex: 1,
                animation: showThemeAnimations ? 'rotate 10s linear infinite' : 'none',
                '@keyframes rotate': {
                  '0%': { transform: 'rotate(0deg)' },
                  '100%': { transform: 'rotate(360deg)' }
                }
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                bottom: '30%',
                right: '15%',
                width: 80,
                height: 80,
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${alpha(moduleColors.secondary || theme.palette.secondary.main, 0.2)}, transparent)`,
                zIndex: 1,
                animation: showThemeAnimations ? 'rotate 8s linear infinite reverse' : 'none'
              }}
            />
          </>
        )}
        </EnhancedGradientBackground>
      )}

      {/* Content for non-blended modes */}
      {!(blendMode === 'lightBlended' || blendMode === 'darkBlended') && (
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            style={{ minHeight: '100vh' }}
          >
            {children}
          </motion.div>
        </AnimatePresence>
      )}

      {/* Global CSS for enhanced theming */}
      <style jsx global>{`
        .global-theme-background {
          transition: all 0.3s ease-in-out;
        }
        
        /* Scrollbar theming */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: ${alpha(theme.palette.background.paper, 0.1)};
          border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
          background: ${alpha(moduleColors.primary || theme.palette.primary.main, 0.3)};
          border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: ${alpha(moduleColors.primary || theme.palette.primary.main, 0.5)};
        }
        
        /* Selection theming */
        ::selection {
          background: ${alpha(moduleColors.primary || theme.palette.primary.main, 0.3)};
          color: ${theme.palette.text.primary};
        }
        
        /* Focus outline theming */
        *:focus-visible {
          outline: 2px solid ${moduleColors.primary || theme.palette.primary.main};
          outline-offset: 2px;
        }
      `}</style>
    </Box>
  );
};

export default GlobalThemeWrapper;
