# Dependencies
node_modules/
/.pnp
.pnp.js
.pnpm-store/

# Testing
/coverage

# Production
/build
/dist
/frontend-web/build
/frontend-web/dist

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cache
.npm
.eslintcache
.cache
.parcel-cache

# Local backup files
*-backup-*.zip

# Temporary files
*.tmp
*.temp
.temp/
.tmp/

# MongoDB
/data/db

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS specific
.AppleDouble
.LSOverride
Icon
._*
.Spotlight-V100
.Trashes

# Linux specific
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*
