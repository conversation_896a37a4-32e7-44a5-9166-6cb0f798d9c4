const { connectDB } = require('../src/config/database');
const { hashPassword } = require('../src/services/authService');
const logger = require('../src/utils/logger');

/**
 * Secure User Seeding Script
 * Creates default users with properly hashed passwords
 */
async function seedSecureUsers() {
  try {
    const { db } = await connectDB();
    const usersCollection = db.collection('users');
    
    // Check if admin user already exists
    const existingAdmin = await usersCollection.findOne({ username: 'admin' });
    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }
    
    // Create secure admin user
    const adminPassword = process.env.DEFAULT_ADMIN_PASSWORD || 'Admin@1749007646083';
    const hashedPassword = await hashPassword(adminPassword);
    
    const adminUser = {
      username: 'admin',
      email: process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'admin',
      permissions: ['all'],
      status: 'active',
      isActive: true,
      subscriptionTier: 'enterprise',
      profile: {
        farmName: 'AgriIntel System',
        province: 'gauteng',
        farmSize: 'enterprise'
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await usersCollection.insertOne(adminUser);
    
    console.log('✅ Secure admin user created');
    console.log('Username: admin');
    console.log('Password:', adminPassword);
    console.log('⚠️ IMPORTANT: Change the password immediately after first login!');
    
  } catch (error) {
    logger.error('Error seeding secure users:', error);
    throw error;
  }
}

if (require.main === module) {
  seedSecureUsers()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedSecureUsers };