# AgriIntel Design System

This document outlines the design system for the AgriIntel application, focusing on consistent design with background images across all modules.

## Design Principles

1. **Consistency**: All modules follow the same design patterns and visual language
2. **Visual Hierarchy**: Important information is visually emphasized
3. **Responsive Design**: All components adapt to different screen sizes
4. **Modern Aesthetics**: Clean, modern design with subtle animations and transitions
5. **Branding**: Consistent use of colors, typography, and imagery that reflects South African farming

## Components

### ModuleHeader

The `ModuleHeader` component provides a consistent header for all module pages with:
- Title and subtitle
- Background image specific to the module
- Optional action button
- Gradient overlay that blends with the theme

```jsx
<ModuleHeader
  title="Module Title"
  subtitle="Module description text"
  module="moduleName"
  submodule="submoduleName"
  actionLabel="Action Button"
  actionIcon={<IconComponent />}
  onAction={() => handleAction()}
/>
```

### AnimatedBackgroundCard

The `AnimatedBackgroundCard` component provides cards with background images that:
- Have a consistent design across the application
- Include a gradient overlay that blends with the theme
- Support icons, titles, and subtitles
- Include smooth animations and transitions
- Can contain any content

```jsx
<AnimatedBackgroundCard
  title="Card Title"
  subtitle="Card description"
  backgroundImage="https://example.com/image.jpg"
  icon={<IconComponent />}
  accentColor={theme.palette.primary.main}
  secondaryColor={theme.palette.primary.dark}
  actionLabel="Action"
  delay={0.1}
  height={180}
>
  {/* Card content goes here */}
</AnimatedBackgroundCard>
```

### Background Images

Background images are managed through the `backgroundImages.ts` utility, which provides:
- Module-specific background images
- Category-specific background images
- Functions to get random backgrounds or specific module backgrounds

```jsx
// Get a module background
const background = getModuleBackground('animals', 'profiles');

// Get a random background from a category
const randomBackground = getRandomBackground('health');
```

## Implementation Guidelines

### Module Pages

Each module page should follow this structure:

1. **ModuleHeader**: At the top of the page with module-specific background
2. **Content Container**: Wrapped in a Box with padding
3. **Card Grid**: Grid of AnimatedBackgroundCard components for key metrics or features
4. **Main Content**: Tables, forms, or other content specific to the module

### Color Usage

- Primary color for main actions and headers
- Secondary color for supporting elements
- Error, warning, and success colors for status indicators
- Background colors should be subtle and not distract from content
- Card backgrounds should use gradients that blend with the theme

### Typography

- Use MUI Typography components consistently
- Headings should use variant="h4" to variant="h6" with appropriate fontWeight
- Body text should use variant="body1" or variant="body2"
- Use color="text.secondary" for less important text

### Animations

- Use motion.div from framer-motion for page transitions
- Use AnimatedBackgroundCard for card animations
- Keep animations subtle and professional
- Use consistent animation durations (0.3-0.5s)
- Use staggered animations with delays for lists of items

## Module-Specific Design

Each module has its own color scheme and background images:

- **Animals**: Green theme with livestock imagery
- **Commercial**: Blue theme with marketplace imagery
- **Breeding**: Purple theme with breeding stock imagery
- **Health**: Red theme with veterinary imagery
- **Feeding**: Orange theme with feed and pasture imagery
- **Financial**: Teal theme with financial imagery
- **Compliance**: Brown theme with certification imagery
- **Resources**: Gray theme with training and support imagery

## Image Resources

The following image resources are used throughout the application:

- https://i.pinimg.com/originals/61/b3/48/61b348b34f30f060538f4731192b81e9.jpg
- https://tribuneonlineng.com/wp-content/uploads/2019/11/livestock.png
- https://www.bolnews.com/wp-content/uploads/2022/01/Untitled-1-copy-33.jpg
- https://extension.purdue.edu/county/dekalb/_media/animals-for-qlc.png
- https://agric4profits.com/wp-content/uploads/2023/11/images-2023-11-12T173712.114.jpeg
- https://st.depositphotos.com/1765488/1267/i/950/depositphotos_12672600-stock-photo-agriculture-collage.jpg
- https://caain.ca/wp-content/uploads/2022/10/Livestock-collage.jpg
