import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, IconButton, Chip, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, FormControl, InputLabel, Select, useTheme, Tabs, Tab, Divider, alpha } from '@mui/material';
import { 
  Add,
  Edit,
  Delete,
  CheckCircle,
  Warning,
  CalendarToday,
  FilterList,
  Search,
  ArrowForward,
  MoreVert,
  TrendingUp,
  TrendingDown,
  BarChart,
  PieChart,
  Timeline,
  AttachMoney,
  ShowChart
} from '../../utils/iconImports';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { format } from 'date-fns';
import { <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { formatCurrency } from '../../utils/formatters';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Mock data for ROI analysis
const mockROIProjects = [
  { 
    id: 'ROI001', 
    name: 'New Cattle Breed Introduction', 
    initialInvestment: 250000, 
    returns: 425000, 
    timeframe: 24, 
    roi: 70, 
    status: 'completed',
    category: 'Breeding',
    startDate: '2022-01-15',
    endDate: '2024-01-15'
  },
  { 
    id: 'ROI002', 
    name: 'Automated Feeding System', 
    initialInvestment: 180000, 
    returns: 270000, 
    timeframe: 18, 
    roi: 50, 
    status: 'active',
    category: 'Equipment',
    startDate: '2023-06-01',
    endDate: '2024-12-01'
  },
  { 
    id: 'ROI003', 
    name: 'Pasture Improvement Program', 
    initialInvestment: 120000, 
    returns: 192000, 
    timeframe: 12, 
    roi: 60, 
    status: 'active',
    category: 'Land',
    startDate: '2023-09-15',
    endDate: '2024-09-15'
  },
  { 
    id: 'ROI004', 
    name: 'Health Management Protocol', 
    initialInvestment: 85000, 
    returns: 153000, 
    timeframe: 12, 
    roi: 80, 
    status: 'completed',
    category: 'Health',
    startDate: '2023-01-01',
    endDate: '2024-01-01'
  },
  { 
    id: 'ROI005', 
    name: 'Export Market Development', 
    initialInvestment: 320000, 
    returns: 480000, 
    timeframe: 24, 
    roi: 50, 
    status: 'planned',
    category: 'Marketing',
    startDate: '2024-07-01',
    endDate: '2026-07-01'
  }
];

// Mock data for ROI by category
const mockROIByCategory = [
  { name: 'Breeding', value: 70 },
  { name: 'Equipment', value: 50 },
  { name: 'Land', value: 60 },
  { name: 'Health', value: 80 },
  { name: 'Marketing', value: 50 }
];

// Mock data for ROI over time
const mockROIOverTime = [
  { year: '2020', roi: 35 },
  { year: '2021', roi: 42 },
  { year: '2022', roi: 55 },
  { year: '2023', roi: 62 },
  { year: '2024', roi: 68 }
];

const ROI: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  
  // Calculate ROI statistics
  const totalInvestment = mockROIProjects.reduce((sum, project) => sum + project.initialInvestment, 0);
  const totalReturns = mockROIProjects.reduce((sum, project) => sum + project.returns, 0);
  const averageROI = mockROIProjects.reduce((sum, project) => sum + project.roi, 0) / mockROIProjects.length;
  const activeProjects = mockROIProjects.filter(project => project.status === 'active').length;
  
  // Filter projects based on status
  const filteredProjects = filterStatus === 'all' 
    ? mockROIProjects 
    : mockROIProjects.filter(project => project.status === filterStatus);

  const handleOpenDialog = (project?: any) => {
    setSelectedProject(project || null);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedProject(null);
  };

  const handleSave = () => {
    // Save logic would go here
    handleCloseDialog();
  };

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main
  ];

  return (
    <Box>
      <ModuleHeader
        title="Return on Investment Analysis"
        subtitle="Track and analyze the financial performance of your livestock investments"
        module="financial"
        submodule="roi"
        actionLabel="Add ROI Project"
        actionIcon={<Add />}
        onAction={() => handleOpenDialog()}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Stats Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
            ROI Overview
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Investment"
                  subtitle={formatCurrency(totalInvestment)}
                  module="financial"
                  uniqueId="roi-investment"
                  icon={<AttachMoney />}
                  height={160}
                  accentColor={theme.palette.primary.main}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Returns"
                  subtitle={formatCurrency(totalReturns)}
                  module="financial"
                  uniqueId="roi-returns"
                  icon={<TrendingUp />}
                  height={160}
                  accentColor={theme.palette.success.main}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Average ROI"
                  subtitle={`${averageROI.toFixed(1)}%`}
                  module="financial"
                  uniqueId="roi-average"
                  icon={<ShowChart />}
                  height={160}
                  accentColor={theme.palette.info.main}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Active Projects"
                  subtitle={`${activeProjects} projects`}
                  module="financial"
                  uniqueId="roi-active"
                  icon={<BarChart />}
                  height={160}
                  accentColor={theme.palette.warning.main}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* ROI Charts */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            ROI Analysis
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="ROI by Category"
                  subtitle="Return on investment across different categories"
                  module="financial"
                  uniqueId="roi-by-category"
                  height={400}
                  accentColor={theme.palette.primary.main}
                  content={
                    <Box sx={{ height: 330, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={mockROIByCategory}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, value }) => `${name}: ${value}%`}
                          >
                            {mockROIByCategory.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => [`${value}%`, 'ROI']} />
                          <Legend />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="ROI Trend Over Time"
                  subtitle="Average ROI performance over the years"
                  module="financial"
                  uniqueId="roi-trend"
                  height={400}
                  accentColor={theme.palette.secondary.main}
                  content={
                    <Box sx={{ height: 330, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={mockROIOverTime}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="year" />
                          <YAxis />
                          <Tooltip formatter={(value) => [`${value}%`, 'ROI']} />
                          <Legend />
                          <Line 
                            type="monotone" 
                            dataKey="roi" 
                            name="Average ROI" 
                            stroke={theme.palette.secondary.main} 
                            strokeWidth={2}
                            activeDot={{ r: 8 }} 
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </motion.div>
            </Grid>

            <Grid item xs={12}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Project ROI Comparison"
                  subtitle="ROI percentage across different projects"
                  module="financial"
                  uniqueId="roi-comparison"
                  height={400}
                  accentColor={theme.palette.success.main}
                  content={
                    <Box sx={{ height: 330, mt: 2 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart
                          data={mockROIProjects}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip formatter={(value, name) => [name === 'roi' ? `${value}%` : formatCurrency(value as number), name === 'roi' ? 'ROI' : name]} />
                          <Legend />
                          <Bar dataKey="roi" name="ROI %" fill={theme.palette.success.main} />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </Box>
                  }
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* ROI Projects Table */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 4, mb: 2 }}>
            <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mb: 0 }}>
              ROI Projects
            </Typography>
            <Box>
              <FormControl size="small" sx={{ minWidth: 150, mr: 2 }}>
                <InputLabel>Filter Status</InputLabel>
                <Select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  label="Filter Status"
                >
                  <MenuItem value="all">All Projects</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                  <MenuItem value="planned">Planned</MenuItem>
                </Select>
              </FormControl>
              <CustomButton 
                variant="outlined" 
                startIcon={<Add />}
                onClick={() => handleOpenDialog()}
              >
                Add Project
              </CustomButton>
            </Box>
          </Box>
          
          <motion.div variants={itemVariants}>
            <TableContainer component={Paper} sx={{ boxShadow: '0 4px 20px rgba(0,0,0,0.08)', borderRadius: '12px' }}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                  <TableRow>
                    <TableCell><strong>Project Name</strong></TableCell>
                    <TableCell><strong>Category</strong></TableCell>
                    <TableCell><strong>Initial Investment</strong></TableCell>
                    <TableCell><strong>Returns</strong></TableCell>
                    <TableCell><strong>Timeframe (months)</strong></TableCell>
                    <TableCell><strong>ROI</strong></TableCell>
                    <TableCell><strong>Status</strong></TableCell>
                    <TableCell><strong>Actions</strong></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredProjects.map((project) => (
                    <TableRow key={project.id} hover>
                      <TableCell>{project.name}</TableCell>
                      <TableCell>{project.category}</TableCell>
                      <TableCell>{formatCurrency(project.initialInvestment)}</TableCell>
                      <TableCell>{formatCurrency(project.returns)}</TableCell>
                      <TableCell>{project.timeframe}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', color: project.roi >= 50 ? 'success.main' : 'warning.main' }}>
                          {project.roi >= 50 ? <TrendingUp fontSize="small" sx={{ mr: 0.5 }} /> : <TrendingDown fontSize="small" sx={{ mr: 0.5 }} />}
                          {project.roi}%
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                          color={
                            project.status === 'active' ? 'success' :
                            project.status === 'planned' ? 'info' : 'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton size="small" onClick={() => handleOpenDialog(project)}>
                          <Edit fontSize="small" />
                        </IconButton>
                        <IconButton size="small">
                          <Delete fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </motion.div>
        </motion.div>
      </Box>

      {/* Add/Edit ROI Project Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedProject ? 'Edit ROI Project' : 'Add New ROI Project'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                label="Project Name"
                fullWidth
                margin="normal"
                defaultValue={selectedProject?.name || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Category</InputLabel>
                <Select
                  label="Category"
                  defaultValue={selectedProject?.category || ''}
                >
                  <MenuItem value="Breeding">Breeding</MenuItem>
                  <MenuItem value="Equipment">Equipment</MenuItem>
                  <MenuItem value="Land">Land</MenuItem>
                  <MenuItem value="Health">Health</MenuItem>
                  <MenuItem value="Marketing">Marketing</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Status</InputLabel>
                <Select
                  label="Status"
                  defaultValue={selectedProject?.status || 'planned'}
                >
                  <MenuItem value="planned">Planned</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Initial Investment (R)"
                fullWidth
                margin="normal"
                type="number"
                defaultValue={selectedProject?.initialInvestment || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Expected/Actual Returns (R)"
                fullWidth
                margin="normal"
                type="number"
                defaultValue={selectedProject?.returns || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Timeframe (months)"
                fullWidth
                margin="normal"
                type="number"
                defaultValue={selectedProject?.timeframe || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="ROI (%)"
                fullWidth
                margin="normal"
                type="number"
                defaultValue={selectedProject?.roi || ''}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Start Date"
                type="date"
                fullWidth
                margin="normal"
                defaultValue={selectedProject?.startDate || format(new Date(), 'yyyy-MM-dd')}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="End Date"
                type="date"
                fullWidth
                margin="normal"
                defaultValue={selectedProject?.endDate || format(new Date(new Date().setMonth(new Date().getMonth() + 12)), 'yyyy-MM-dd')}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Notes"
                fullWidth
                multiline
                rows={4}
                margin="normal"
                defaultValue={selectedProject?.notes || ''}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <CustomButton onClick={handleCloseDialog}>Cancel</CustomButton>
          <CustomButton onClick={handleSave} variant="contained" color="primary">
            {selectedProject ? 'Update' : 'Save'}
          </CustomButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ROI;
