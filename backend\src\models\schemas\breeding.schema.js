/**
 * Breeding Schema
 * 
 * Defines the MongoDB schema for the breeding collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Breeding Record Schema
const breedingRecordSchema = new Schema({
  female: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  male: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  breedingDate: {
    type: Date,
    required: true
  },
  method: {
    type: String,
    enum: ['natural', 'artificial insemination', 'embryo transfer', 'other'],
    default: 'natural'
  },
  location: {
    type: String,
    trim: true
  },
  notes: {
    type: String
  },
  status: {
    type: String,
    enum: ['pending', 'successful', 'unsuccessful', 'unknown'],
    default: 'pending'
  },
  expectedDueDate: {
    type: Date
  },
  confirmedPregnancy: {
    type: Boolean,
    default: false
  },
  pregnancyCheckDate: {
    type: Date
  },
  pregnancyCheckMethod: {
    type: String,
    enum: ['visual', 'ultrasound', 'blood test', 'other'],
    default: 'visual'
  },
  birthRecord: {
    type: Schema.Types.ObjectId,
    ref: 'BirthRecord'
  },
  performedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  cost: {
    type: Number,
    min: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Birth Record Schema
const birthRecordSchema = new Schema({
  breedingRecord: {
    type: Schema.Types.ObjectId,
    ref: 'BreedingRecord'
  },
  mother: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  father: {
    type: Schema.Types.ObjectId,
    ref: 'Animal'
  },
  birthDate: {
    type: Date,
    required: true
  },
  birthTime: {
    type: String // Format: HH:MM
  },
  location: {
    type: String,
    trim: true
  },
  assistanceRequired: {
    type: Boolean,
    default: false
  },
  assistanceDetails: {
    type: String
  },
  complications: {
    type: Boolean,
    default: false
  },
  complicationDetails: {
    type: String
  },
  offspring: [{
    animal: {
      type: Schema.Types.ObjectId,
      ref: 'Animal'
    },
    tagNumber: {
      type: String,
      trim: true
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'unknown'],
      default: 'unknown'
    },
    birthWeight: {
      value: {
        type: Number,
        min: 0
      },
      unit: {
        type: String,
        enum: ['kg', 'g', 'lb', 'oz'],
        default: 'kg'
      }
    },
    status: {
      type: String,
      enum: ['alive', 'stillborn', 'died shortly after birth', 'unknown'],
      default: 'alive'
    },
    notes: {
      type: String
    }
  }],
  totalOffspring: {
    type: Number,
    min: 0,
    default: 0
  },
  aliveOffspring: {
    type: Number,
    min: 0,
    default: 0
  },
  notes: {
    type: String
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    caption: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  recordedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Heat Detection Schema
const heatDetectionSchema = new Schema({
  animal: {
    type: Schema.Types.ObjectId,
    ref: 'Animal',
    required: true
  },
  detectionDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  signs: [{
    type: String,
    enum: ['mounting', 'standing heat', 'vaginal discharge', 'swelling', 'restlessness', 'decreased appetite', 'decreased milk production', 'other'],
    required: true
  }],
  intensity: {
    type: String,
    enum: ['strong', 'moderate', 'weak', 'unknown'],
    default: 'unknown'
  },
  notes: {
    type: String
  },
  action: {
    type: String,
    enum: ['none', 'scheduled for breeding', 'bred', 'other'],
    default: 'none'
  },
  breedingRecord: {
    type: Schema.Types.ObjectId,
    ref: 'BreedingRecord'
  },
  recordedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
breedingRecordSchema.index({ female: 1, breedingDate: -1 });
breedingRecordSchema.index({ male: 1 });
breedingRecordSchema.index({ status: 1 });
breedingRecordSchema.index({ expectedDueDate: 1 });

birthRecordSchema.index({ mother: 1, birthDate: -1 });
birthRecordSchema.index({ 'offspring.animal': 1 });

heatDetectionSchema.index({ animal: 1, detectionDate: -1 });
heatDetectionSchema.index({ action: 1 });

// Pre-save hook to update timestamps
breedingRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

birthRecordSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Calculate total and alive offspring
  if (this.offspring && this.offspring.length > 0) {
    this.totalOffspring = this.offspring.length;
    this.aliveOffspring = this.offspring.filter(o => o.status === 'alive').length;
  }
  
  next();
});

heatDetectionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const BreedingRecord = mongoose.model('BreedingRecord', breedingRecordSchema);
const BirthRecord = mongoose.model('BirthRecord', birthRecordSchema);
const HeatDetection = mongoose.model('HeatDetection', heatDetectionSchema);

module.exports = {
  BreedingRecord,
  BirthRecord,
  HeatDetection
};
