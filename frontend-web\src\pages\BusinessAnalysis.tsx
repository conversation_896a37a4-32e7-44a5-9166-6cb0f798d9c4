import React, { useState } from 'react';
import { Box, Typography, Grid, Container, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import AnalyticsCard from '../components/analytics/AnalyticsCard';
import CustomAnalyticsButton from '../components/analytics/CustomAnalyticsButton';
import { useBusinessAnalytics } from '../hooks/useBusinessAnalytics';
import { 
  BarChart, Bar, LineChart, Line, PieChart, Pie, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, 
  ResponsiveContainer, Cell 
} from 'recharts';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import InsightsIcon from '@mui/icons-material/Insights';
import AssessmentIcon from '@mui/icons-material/Assessment';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import PetsIcon from '@mui/icons-material/Pets';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import WarningIcon from '@mui/icons-material/Warning';
import LightbulbIcon from '@mui/icons-material/Lightbulb';

/**
 * Business Analysis Dashboard Component
 * This component provides comprehensive business analytics and insights
 * without relying on the problematic MUI Button component
 */
const BusinessAnalysis: React.FC = () => {
  const theme = useTheme();
  const { analyticsData, loading } = useBusinessAnalytics();
  const [activeTab, setActiveTab] = useState<string>('overview');

  // Define colors for charts
  const chartColors = [
    theme.palette.primary.main || '#3AA99F',
    theme.palette.secondary.main || '#38B2AC',
    theme.palette.info.main || '#3b82f6',
    theme.palette.success.main || '#10b981',
    theme.palette.warning.main || '#f59e0b',
    theme.palette.error.main || '#ef4444',
  ];

  // Define tabs
  const tabs = [
    { id: 'overview', label: 'Overview', icon: <InsightsIcon /> },
    { id: 'financial', label: 'Financial', icon: <MonetizationOnIcon /> },
    { id: 'animals', label: 'Animals', icon: <PetsIcon /> },
    { id: 'health', label: 'Health', icon: <LocalHospitalIcon /> },
    { id: 'feeding', label: 'Feeding', icon: <RestaurantIcon /> },
    { id: 'predictions', label: 'Predictions', icon: <AssessmentIcon /> },
    { id: 'insights', label: 'Insights', icon: <LightbulbIcon /> },
    { id: 'risks', label: 'Risks', icon: <WarningIcon /> }
  ];

  // Render metrics
  const renderMetrics = () => {
    const filteredMetrics = activeTab === 'overview' 
      ? analyticsData.metrics.slice(0, 6) 
      : analyticsData.metrics.filter(metric => activeTab === 'overview' || metric.category === activeTab);

    return (
      <Grid container spacing={3}>
        {filteredMetrics.map((metric) => (
          <Grid item xs={12} sm={6} md={4} key={metric.id}>
            <AnalyticsCard
              title={metric.name}
              icon={getIconForCategory(metric.category)}
              height={140}
              delay={0.1}
            >
              <Box sx={{ textAlign: 'center', mt: 1 }}>
                <Typography variant="h4" fontWeight="bold" color="primary">
                  {metric.value}
                  {metric.unit && <Typography component="span" variant="body1"> {metric.unit}</Typography>}
                </Typography>
                
                {metric.trend && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                    {metric.trend.isPositive ? (
                      <TrendingUpIcon color="success" fontSize="small" />
                    ) : (
                      <TrendingDownIcon color="error" fontSize="small" />
                    )}
                    <Typography 
                      variant="body2" 
                      color={metric.trend.isPositive ? 'success.main' : 'error.main'}
                      sx={{ ml: 0.5 }}
                    >
                      {metric.trend.value}% {metric.trend.isPositive ? 'increase' : 'decrease'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render KPIs
  const renderKPIs = () => {
    const filteredKPIs = activeTab === 'overview' 
      ? analyticsData.kpis.slice(0, 4) 
      : analyticsData.kpis.filter(kpi => activeTab === 'overview' || kpi.category === activeTab);

    return (
      <Grid container spacing={3}>
        {filteredKPIs.map((kpi) => (
          <Grid item xs={12} sm={6} md={6} lg={3} key={kpi.id}>
            <AnalyticsCard
              title={kpi.name}
              icon={getIconForCategory(kpi.category)}
              height={180}
              delay={0.2}
            >
              <Box sx={{ textAlign: 'center', mt: 1 }}>
                <Typography variant="h4" fontWeight="bold" color="primary">
                  {kpi.value}{kpi.unit}
                </Typography>
                
                <Box sx={{ mt: 2, mb: 1 }}>
                  <Box sx={{ 
                    width: '100%', 
                    height: 8, 
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    borderRadius: 4
                  }}>
                    <Box sx={{ 
                      width: `${(kpi.value / kpi.target) * 100}%`, 
                      height: '100%', 
                      backgroundColor: getStatusColor(kpi.status),
                      borderRadius: 4
                    }} />
                  </Box>
                </Box>
                
                <Typography variant="body2" color="text.secondary">
                  Target: {kpi.target}{kpi.unit}
                </Typography>
                
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                  {kpi.trend > 0 ? (
                    <TrendingUpIcon color="success" fontSize="small" />
                  ) : (
                    <TrendingDownIcon color="error" fontSize="small" />
                  )}
                  <Typography 
                    variant="body2" 
                    color={kpi.trend > 0 ? 'success.main' : 'error.main'}
                    sx={{ ml: 0.5 }}
                  >
                    {Math.abs(kpi.trend)}% {kpi.trend > 0 ? 'increase' : 'decrease'}
                  </Typography>
                </Box>
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render charts
  const renderCharts = () => {
    if (activeTab === 'overview' || activeTab === 'financial') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <AnalyticsCard
              title="Revenue & Expenses"
              subtitle="Last 6 months"
              icon={<MonetizationOnIcon />}
              height={400}
              delay={0.3}
            >
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analyticsData.revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" stroke={chartColors[0]} activeDot={{ r: 8 }} />
                  <Line type="monotone" dataKey="expenses" stroke={chartColors[1]} />
                  <Line type="monotone" dataKey="profit" stroke={chartColors[2]} />
                </LineChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <AnalyticsCard
              title="Expense Breakdown"
              subtitle="Current period"
              icon={<MonetizationOnIcon />}
              height={400}
              delay={0.4}
            >
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={analyticsData.expenseData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {analyticsData.expenseData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
        </Grid>
      );
    }

    if (activeTab === 'animals') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <AnalyticsCard
              title="Animal Distribution"
              subtitle="By species"
              icon={<PetsIcon />}
              height={400}
              delay={0.3}
            >
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={analyticsData.animalDistributionData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {analyticsData.animalDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
          <Grid item xs={12} md={6}>
            <AnalyticsCard
              title="Growth Rates"
              subtitle="By species (kg/day)"
              icon={<TrendingUpIcon />}
              height={400}
              delay={0.4}
            >
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analyticsData.growthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="cattle" stroke={chartColors[0]} />
                  <Line type="monotone" dataKey="sheep" stroke={chartColors[1]} />
                  <Line type="monotone" dataKey="goats" stroke={chartColors[2]} />
                </LineChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
        </Grid>
      );
    }

    if (activeTab === 'health') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <AnalyticsCard
              title="Health Activities"
              subtitle="Last 6 months"
              icon={<LocalHospitalIcon />}
              height={400}
              delay={0.3}
            >
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analyticsData.healthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="vaccinations" fill={chartColors[0]} />
                  <Bar dataKey="treatments" fill={chartColors[1]} />
                  <Bar dataKey="checkups" fill={chartColors[2]} />
                </BarChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
        </Grid>
      );
    }

    if (activeTab === 'feeding') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <AnalyticsCard
              title="Feed Consumption"
              subtitle="Last 6 months (kg)"
              icon={<RestaurantIcon />}
              height={400}
              delay={0.3}
            >
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analyticsData.feedData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="hay" fill={chartColors[0]} />
                  <Bar dataKey="grain" fill={chartColors[1]} />
                  <Bar dataKey="supplements" fill={chartColors[2]} />
                </BarChart>
              </ResponsiveContainer>
            </AnalyticsCard>
          </Grid>
        </Grid>
      );
    }

    return null;
  };

  // Render predictions
  const renderPredictions = () => {
    if (activeTab !== 'overview' && activeTab !== 'predictions') return null;

    const filteredPredictions = activeTab === 'overview' 
      ? analyticsData.predictions.slice(0, 2) 
      : analyticsData.predictions;

    return (
      <Grid container spacing={3}>
        {filteredPredictions.map((prediction) => (
          <Grid item xs={12} sm={6} md={activeTab === 'overview' ? 6 : 4} key={prediction.id}>
            <AnalyticsCard
              title={prediction.name}
              subtitle={`Prediction timeframe: ${prediction.timeframe}`}
              icon={<AssessmentIcon />}
              height={activeTab === 'overview' ? 200 : 300}
              delay={0.5}
            >
              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Current</Typography>
                    <Typography variant="h5" fontWeight="bold" color="primary">
                      {prediction.currentValue}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Predicted</Typography>
                    <Typography variant="h5" fontWeight="bold" color={prediction.predictedValue > prediction.currentValue ? 'success.main' : 'error.main'}>
                      {prediction.predictedValue}
                    </Typography>
                  </Grid>
                </Grid>
                
                <Box sx={{ mt: 2, mb: 1 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                    Confidence: {prediction.confidence}%
                  </Typography>
                  <Box sx={{ 
                    width: '100%', 
                    height: 8, 
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    borderRadius: 4
                  }}>
                    <Box sx={{ 
                      width: `${prediction.confidence}%`, 
                      height: '100%', 
                      backgroundColor: getConfidenceColor(prediction.confidence),
                      borderRadius: 4
                    }} />
                  </Box>
                </Box>
                
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  {prediction.predictedValue > prediction.currentValue ? 'Projected increase' : 'Projected decrease'} of{' '}
                  <Typography component="span" fontWeight="bold" color={prediction.predictedValue > prediction.currentValue ? 'success.main' : 'error.main'}>
                    {Math.abs(((prediction.predictedValue - prediction.currentValue) / prediction.currentValue) * 100).toFixed(1)}%
                  </Typography>
                </Typography>
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render insights
  const renderInsights = () => {
    if (activeTab !== 'overview' && activeTab !== 'insights') return null;

    const filteredInsights = activeTab === 'overview' 
      ? analyticsData.insights.slice(0, 1) 
      : analyticsData.insights;

    return (
      <Grid container spacing={3}>
        {filteredInsights.map((insight) => (
          <Grid item xs={12} md={activeTab === 'overview' ? 12 : 6} key={insight.id}>
            <AnalyticsCard
              title={insight.title}
              subtitle={`Impact: ${insight.impact.charAt(0).toUpperCase() + insight.impact.slice(1)}`}
              icon={<LightbulbIcon />}
              height={activeTab === 'overview' ? 180 : 250}
              delay={0.6}
              accentColor={getImpactColor(insight.impact)}
            >
              <Box sx={{ mt: 1 }}>
                <Typography variant="body1">
                  {insight.description}
                </Typography>
                
                {insight.actionable && insight.suggestedAction && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="body2" fontWeight="bold">
                      Suggested Action:
                    </Typography>
                    <Typography variant="body2">
                      {insight.suggestedAction}
                    </Typography>
                  </Box>
                )}
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render risks
  const renderRisks = () => {
    if (activeTab !== 'risks') return null;

    return (
      <Grid container spacing={3}>
        {analyticsData.risks.map((risk) => (
          <Grid item xs={12} md={6} key={risk.id}>
            <AnalyticsCard
              title={risk.name}
              subtitle={`Risk Score: ${(risk.riskScore * 100).toFixed(0)}%`}
              icon={<WarningIcon />}
              height={250}
              delay={0.3}
              accentColor={getRiskColor(risk.riskScore)}
            >
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Probability: {(risk.probability * 100).toFixed(0)}% | Impact: {(risk.impact * 100).toFixed(0)}%
                </Typography>
                
                <Typography variant="body1">
                  {risk.mitigationStrategy}
                </Typography>
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render opportunities
  const renderOpportunities = () => {
    if (activeTab !== 'overview' && activeTab !== 'insights') return null;
    if (activeTab === 'overview') return null;

    return (
      <Grid container spacing={3}>
        {analyticsData.opportunities.map((opportunity) => (
          <Grid item xs={12} md={6} key={opportunity.id}>
            <AnalyticsCard
              title={opportunity.name}
              subtitle={`Potential Value: R ${opportunity.potentialValue.toLocaleString()}`}
              icon={<LightbulbIcon />}
              height={250}
              delay={0.4}
              accentColor={theme.palette.success.main}
            >
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Feasibility: {(opportunity.feasibility * 100).toFixed(0)}% | Score: {opportunity.opportunityScore.toLocaleString()}
                </Typography>
                
                <Typography variant="body1">
                  {opportunity.implementationStrategy}
                </Typography>
              </Box>
            </AnalyticsCard>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Helper function to get icon for category
  const getIconForCategory = (category: string) => {
    switch (category) {
      case 'financial':
        return <MonetizationOnIcon />;
      case 'animals':
        return <PetsIcon />;
      case 'health':
        return <LocalHospitalIcon />;
      case 'breeding':
        return <PetsIcon />;
      case 'feeding':
        return <RestaurantIcon />;
      case 'operational':
        return <AssessmentIcon />;
      default:
        return <InsightsIcon />;
    }
  };

  // Helper function to get color for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on-track':
        return theme.palette.success.main;
      case 'at-risk':
        return theme.palette.warning.main;
      case 'behind':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  // Helper function to get color for confidence
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return theme.palette.success.main;
    if (confidence >= 60) return theme.palette.info.main;
    if (confidence >= 40) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // Helper function to get color for impact
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return theme.palette.success.main;
      case 'medium':
        return theme.palette.info.main;
      case 'low':
        return theme.palette.warning.main;
      default:
        return theme.palette.primary.main;
    }
  };

  // Helper function to get color for risk
  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 0.4) return theme.palette.error.main;
    if (riskScore >= 0.2) return theme.palette.warning.main;
    return theme.palette.info.main;
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="bold" gutterBottom>
          Business Analysis
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Comprehensive analytics and insights to drive business decisions
        </Typography>
      </Box>

      {/* Tabs */}
      <Box 
        sx={{ 
          display: 'flex', 
          flexWrap: 'wrap',
          gap: 1,
          mb: 4,
          pb: 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        {tabs.map((tab) => (
          <CustomAnalyticsButton
            key={tab.id}
            label={tab.label}
            icon={tab.icon}
            variant={activeTab === tab.id ? 'contained' : 'text'}
            color="primary"
            onClick={() => setActiveTab(tab.id)}
          />
        ))}
      </Box>

      {/* Content */}
      <Box sx={{ mb: 4 }}>
        {renderMetrics()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderKPIs()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderCharts()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderPredictions()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderInsights()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderRisks()}
      </Box>

      <Box sx={{ mb: 4 }}>
        {renderOpportunities()}
      </Box>
    </Container>
  );
};

export default BusinessAnalysis;
