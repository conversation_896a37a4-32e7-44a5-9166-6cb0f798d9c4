import React, { ReactNode } from 'react';
import { Box, useTheme } from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { getPageGradientBackground } from '../../utils/colorBlendingUtils';
import { getSelectableStyles } from '../../utils/selectionUtils';

interface ModulePageWrapperProps {
  children: ReactNode;
  module?: string;
  customPrimaryColor?: string;
  customSecondaryColor?: string;
}

/**
 * A wrapper component for module pages that applies consistent styling
 * including background gradients and ensures all content is selectable
 */
const ModulePageWrapper: React.FC<ModulePageWrapperProps> = ({
  children,
  module,
  customPrimaryColor,
  customSecondaryColor
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Get module color if module is provided
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return customPrimaryColor || themeColor.primary;
    }
  };

  // Use module colors if module is provided, otherwise use provided colors or theme colors
  const primaryColor = module ? getModuleColor() : (customPrimaryColor || themeColor.primary);
  const secondaryColor = module ? (customSecondaryColor || theme.palette.primary.dark) : (customSecondaryColor || themeColor.secondary);

  return (
    <Box
      sx={{
        minHeight: '100vh',
        width: '100%',
        ...getPageGradientBackground(primaryColor, secondaryColor),
        ...getSelectableStyles()
      }}
    >
      {children}
    </Box>
  );
};

export default ModulePageWrapper;
