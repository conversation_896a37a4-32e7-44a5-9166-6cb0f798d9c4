/**
 * Types for BKB Livestock Auctions integration
 */

export interface BkbAuction {
  id: string;
  title: string;
  location: string;
  date: Date;
  startTime: string;
  endTime: string;
  type: BkbAuctionType;
  category: BkbAuctionCategory;
  region?: string;
  province?: string;
  description?: string;
  url?: string;
}

export type BkbAuctionType = 
  | 'weekly' 
  | 'monthly' 
  | 'fortnightly' 
  | 'special' 
  | 'prime' 
  | 'other';

export type BkbAuctionCategory = 
  | 'livestock' 
  | 'fibre' 
  | 'irrigation' 
  | 'grazing' 
  | 'other';

export interface BkbAuctionCalendarDay {
  date: Date;
  auctions: BkbAuction[];
}

export interface BkbAuctionCalendarMonth {
  year: number;
  month: number; // 0-11
  days: BkbAuctionCalendarDay[];
}

export interface BkbAuctionFilter {
  types?: BkbAuctionType[];
  categories?: BkbAuctionCategory[];
  regions?: string[];
  provinces?: string[];
}
