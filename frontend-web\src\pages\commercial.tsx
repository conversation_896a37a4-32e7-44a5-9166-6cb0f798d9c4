import React from 'react';
import { Routes, Route } from 'react-router-dom';
import CommercialDashboard from './commercial/CommercialDashboard';
import Pricing from './commercial/Pricing';
import Marketplace from './commercial/Marketplace';
import Orders from './commercial/Orders';
import Suppliers from './commercial/Suppliers';
import SupplierDetail from './commercial/SupplierDetail';
import Auctions from './commercial/Auctions';

const Commercial: React.FC = () => {
  return (
    <Routes>
      <Route index element={<CommercialDashboard />} />
      <Route path="marketplace" element={<Marketplace />} />
      <Route path="orders" element={<Orders />} />
      <Route path="suppliers" element={<Suppliers />} />
      <Route path="suppliers/:id" element={<SupplierDetail />} />
      <Route path="pricing" element={<Pricing />} />
      <Route path="auctions" element={<Auctions />} />
    </Routes>
  );
};

export default Commercial;
