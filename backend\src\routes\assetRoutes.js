const express = require('express');
const router = express.Router();
const assetController = require('../controllers/assetController');

/**
 * Asset Management Routes
 */
module.exports = (useMockData = false) => {
  // Controller would be selected based on mock data flag
  const controller = useMockData
    ? require('../controllers/mockDataController')
    : assetController;

  // Define routes
  router.get('/', (req, res) => {
    res.status(200).json({ message: 'Asset Management API' });
  });

  // Animal Asset routes
  router.get('/animals', controller.getAnimalAssets);
  router.get('/animals/:id', controller.getAnimalAssetById);
  router.post('/animals', controller.createAnimalAsset);
  router.put('/animals/:id', controller.updateAnimalAsset);
  
  // Retirement routes
  router.post('/animals/:id/retire', controller.retireAnimal);
  router.get('/retirement/candidates', controller.getRetirementCandidates);
  router.get('/retirement/stats', controller.getRetirementStats);

  return router;
};
