export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
}

export const animalFields: FormField[] = [
  {
    name: 'tagNumber',
    label: 'Tag Number',
    type: 'text',
    required: true,
    placeholder: 'e.g., A001'
  },
  {
    name: 'name',
    label: 'Animal Name',
    type: 'text',
    placeholder: 'Optional name'
  },
  {
    name: 'species',
    label: 'Species',
    type: 'select',
    required: true,
    options: [
      { value: 'cattle', label: 'Cattle' },
      { value: 'sheep', label: 'Sheep' },
      { value: 'goat', label: 'Goat' },
      { value: 'pig', label: 'Pig' },
      { value: 'chicken', label: 'Chicken' }
    ]
  },
  {
    name: 'breed',
    label: 'Breed',
    type: 'text',
    required: true,
    placeholder: 'e.g., <PERSON>, Merino'
  },
  {
    name: 'gender',
    label: 'Gender',
    type: 'select',
    required: true,
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
    ]
  },
  {
    name: 'birthDate',
    label: 'Birth Date',
    type: 'date',
    required: true
  },
  {
    name: 'weight',
    label: 'Current Weight (kg)',
    type: 'number',
    placeholder: 'Weight in kilograms'
  },
  {
    name: 'motherTag',
    label: 'Mother Tag Number',
    type: 'text',
    placeholder: 'If known'
  },
  {
    name: 'fatherTag',
    label: 'Father Tag Number',
    type: 'text',
    placeholder: 'If known'
  },
  {
    name: 'notes',
    label: 'Additional Notes',
    type: 'textarea',
    rows: 3,
    placeholder: 'Any additional information'
  }
];

export const healthFields: FormField[] = [
  {
    name: 'animalTag',
    label: 'Animal Tag Number',
    type: 'text',
    required: true,
    placeholder: 'e.g., A001'
  },
  {
    name: 'recordType',
    label: 'Record Type',
    type: 'select',
    required: true,
    options: [
      { value: 'vaccination', label: 'Vaccination' },
      { value: 'treatment', label: 'Treatment' },
      { value: 'checkup', label: 'Health Checkup' },
      { value: 'injury', label: 'Injury' },
      { value: 'illness', label: 'Illness' }
    ]
  },
  {
    name: 'date',
    label: 'Date',
    type: 'date',
    required: true
  },
  {
    name: 'veterinarian',
    label: 'Veterinarian',
    type: 'text',
    placeholder: 'Vet name or clinic'
  },
  {
    name: 'medication',
    label: 'Medication/Vaccine',
    type: 'text',
    placeholder: 'Name of medication or vaccine'
  },
  {
    name: 'dosage',
    label: 'Dosage',
    type: 'text',
    placeholder: 'Amount administered'
  },
  {
    name: 'cost',
    label: 'Cost (R)',
    type: 'number',
    placeholder: 'Treatment cost'
  },
  {
    name: 'nextDueDate',
    label: 'Next Due Date',
    type: 'date',
    placeholder: 'For follow-up or booster'
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea',
    rows: 4,
    placeholder: 'Symptoms, observations, instructions'
  }
];

export const breedingFields: FormField[] = [
  {
    name: 'femaleTag',
    label: 'Female Tag Number',
    type: 'text',
    required: true,
    placeholder: 'e.g., F001'
  },
  {
    name: 'maleTag',
    label: 'Male Tag Number',
    type: 'text',
    required: true,
    placeholder: 'e.g., M001'
  },
  {
    name: 'breedingDate',
    label: 'Breeding Date',
    type: 'date',
    required: true
  },
  {
    name: 'breedingMethod',
    label: 'Breeding Method',
    type: 'select',
    required: true,
    options: [
      { value: 'natural', label: 'Natural Mating' },
      { value: 'ai', label: 'Artificial Insemination' },
      { value: 'et', label: 'Embryo Transfer' }
    ]
  },
  {
    name: 'expectedDueDate',
    label: 'Expected Due Date',
    type: 'date',
    placeholder: 'Calculated due date'
  },
  {
    name: 'pregnancyConfirmed',
    label: 'Pregnancy Status',
    type: 'select',
    options: [
      { value: 'pending', label: 'Pending Confirmation' },
      { value: 'confirmed', label: 'Confirmed Pregnant' },
      { value: 'not_pregnant', label: 'Not Pregnant' }
    ]
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea',
    rows: 3,
    placeholder: 'Additional breeding information'
  }
];

export const feedingFields: FormField[] = [
  {
    name: 'animalTag',
    label: 'Animal Tag Number',
    type: 'text',
    required: true,
    placeholder: 'e.g., A001 or leave blank for group feeding'
  },
  {
    name: 'feedType',
    label: 'Feed Type',
    type: 'select',
    required: true,
    options: [
      { value: 'hay', label: 'Hay' },
      { value: 'grain', label: 'Grain' },
      { value: 'pellets', label: 'Pellets' },
      { value: 'silage', label: 'Silage' },
      { value: 'pasture', label: 'Pasture' },
      { value: 'supplement', label: 'Supplement' }
    ]
  },
  {
    name: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    placeholder: 'Amount fed'
  },
  {
    name: 'unit',
    label: 'Unit',
    type: 'select',
    required: true,
    options: [
      { value: 'kg', label: 'Kilograms' },
      { value: 'lbs', label: 'Pounds' },
      { value: 'bales', label: 'Bales' },
      { value: 'bags', label: 'Bags' }
    ]
  },
  {
    name: 'feedingDate',
    label: 'Feeding Date',
    type: 'date',
    required: true
  },
  {
    name: 'cost',
    label: 'Cost (R)',
    type: 'number',
    placeholder: 'Feed cost'
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea',
    rows: 2,
    placeholder: 'Feeding observations'
  }
];

export const financialFields: FormField[] = [
  {
    name: 'type',
    label: 'Transaction Type',
    type: 'select',
    required: true,
    options: [
      { value: 'income', label: 'Income' },
      { value: 'expense', label: 'Expense' }
    ]
  },
  {
    name: 'category',
    label: 'Category',
    type: 'select',
    required: true,
    options: [
      { value: 'feed', label: 'Feed & Nutrition' },
      { value: 'veterinary', label: 'Veterinary Care' },
      { value: 'equipment', label: 'Equipment' },
      { value: 'labor', label: 'Labor' },
      { value: 'sales', label: 'Animal Sales' },
      { value: 'breeding', label: 'Breeding Services' },
      { value: 'utilities', label: 'Utilities' },
      { value: 'other', label: 'Other' }
    ]
  },
  {
    name: 'amount',
    label: 'Amount (R)',
    type: 'number',
    required: true,
    placeholder: 'Transaction amount'
  },
  {
    name: 'date',
    label: 'Date',
    type: 'date',
    required: true
  },
  {
    name: 'description',
    label: 'Description',
    type: 'text',
    required: true,
    placeholder: 'Brief description of transaction'
  },
  {
    name: 'animalTag',
    label: 'Related Animal Tag',
    type: 'text',
    placeholder: 'If transaction relates to specific animal'
  },
  {
    name: 'vendor',
    label: 'Vendor/Customer',
    type: 'text',
    placeholder: 'Who you paid or received money from'
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea',
    rows: 3,
    placeholder: 'Additional details'
  }
];

export const inventoryFields: FormField[] = [
  {
    name: 'itemName',
    label: 'Item Name',
    type: 'text',
    required: true,
    placeholder: 'Name of equipment or supply'
  },
  {
    name: 'category',
    label: 'Category',
    type: 'select',
    required: true,
    options: [
      { value: 'equipment', label: 'Equipment' },
      { value: 'feed', label: 'Feed & Supplies' },
      { value: 'medical', label: 'Medical Supplies' },
      { value: 'tools', label: 'Tools' },
      { value: 'infrastructure', label: 'Infrastructure' }
    ]
  },
  {
    name: 'quantity',
    label: 'Quantity',
    type: 'number',
    required: true,
    placeholder: 'Current quantity'
  },
  {
    name: 'unit',
    label: 'Unit',
    type: 'text',
    required: true,
    placeholder: 'e.g., pieces, kg, liters'
  },
  {
    name: 'purchaseDate',
    label: 'Purchase Date',
    type: 'date'
  },
  {
    name: 'purchasePrice',
    label: 'Purchase Price (R)',
    type: 'number',
    placeholder: 'Cost per unit'
  },
  {
    name: 'supplier',
    label: 'Supplier',
    type: 'text',
    placeholder: 'Where purchased from'
  },
  {
    name: 'location',
    label: 'Storage Location',
    type: 'text',
    placeholder: 'Where item is stored'
  },
  {
    name: 'notes',
    label: 'Notes',
    type: 'textarea',
    rows: 3,
    placeholder: 'Additional information'
  }
];
