/**
 * Centralized Database Configuration
 *
 * This file provides a unified interface for database connections,
 * supporting both MongoDB and fallback to mock data.
 */

const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../utils/logger');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Connection state
let client = null;
let db = null;
let isConnected = false;
let connectionError = null;

// Connection options
const connectionOptions = {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: process.env.MONGODB_SSL === 'true',
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'),
  socketTimeoutMS: parseInt(process.env.MONGODB_SOCKET_TIMEOUT || '45000'),
  maxPoolSize: parseInt(process.env.MONGODB_MAX_POOL_SIZE || '50'),
  minPoolSize: parseInt(process.env.MONGODB_MIN_POOL_SIZE || '5'),
  retryWrites: process.env.MONGODB_RETRY_WRITES === 'true',
  retryReads: process.env.MONGODB_RETRY_READS === 'true'
};

/**
 * Get MongoDB connection URI from environment variables
 * with fallback options
 */
function getConnectionUri() {
  // Primary connection string (preferred)
  const primaryUri = process.env.MONGODB_URI;

  // Fallback connection strings
  const atlasUri = process.env.MONGODB_ATLAS_URI;
  const sqlUri = process.env.MONGODB_SQL_URI;

  // Default connection string (last resort)
  const defaultUri = "mongodb://localhost:27017/ampd_livestock";

  // Log which connection string is being used (without showing credentials)
  const selectedUri = primaryUri || atlasUri || sqlUri || defaultUri;
  const redactedUri = selectedUri.replace(/:\/\/([^:]+):([^@]+)@/, '://$1:****@');
  logger.info(`Using MongoDB connection: ${redactedUri}`);

  // Return the first available connection string
  return selectedUri;
}

/**
 * Get database name from environment variables
 */
function getDatabaseName() {
  const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';
  logger.info(`Using database: ${dbName}`);
  return dbName;
}

/**
 * Connect to MongoDB with retry and fallback mechanism
 */
async function connectDB() {
  if (isConnected && db) {
    logger.info('Already connected to MongoDB');
    return { client, db };
  }

  // Reset connection state
  connectionError = null;

  // Get connection URI and database name
  const uri = getConnectionUri();
  const dbName = getDatabaseName();

  logger.info(`Attempting to connect to MongoDB: ${uri.substring(0, uri.indexOf('?') > 0 ? uri.indexOf('?') : 20)}...`);

  try {
    // Create a new client
    client = new MongoClient(uri, connectionOptions);

    // Set up connection timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Connection timeout')), parseInt(process.env.MONGODB_CONNECTION_TIMEOUT || '30000'));
    });

    // Connect to MongoDB
    const connectPromise = client.connect();

    // Race the connection against the timeout
    await Promise.race([connectPromise, timeoutPromise]);

    // Get database
    db = client.db(dbName);
    isConnected = true;

    // Ping the database to confirm connection
    await db.command({ ping: 1 });
    logger.info("Successfully connected to MongoDB!");

    return { client, db };
  } catch (error) {
    logger.error('Failed to connect to MongoDB:', error);
    connectionError = error;
    isConnected = false;
    db = null;

    // Close client if it exists
    if (client) {
      try {
        await client.close();
      } catch (closeError) {
        logger.error('Error closing MongoDB client:', closeError);
      }
      client = null;
    }

    throw error;
  }
}

/**
 * Get MongoDB database instance
 */
async function getDB() {
  if (!isConnected || !db) {
    try {
      await connectDB();
    } catch (error) {
      logger.error('Failed to get database connection:', error);
      throw error;
    }
  }

  return db;
}

/**
 * Get MongoDB collection
 * @param {string} collectionName - Name of the collection
 */
async function getCollection(collectionName) {
  try {
    const database = await getDB();
    return database.collection(collectionName);
  } catch (error) {
    logger.error(`Failed to get collection ${collectionName}:`, error);
    return null;
  }
}

/**
 * Disconnect from MongoDB
 */
async function disconnectDB() {
  if (client) {
    try {
      await client.close();
      isConnected = false;
      db = null;
      client = null;
      logger.info('Disconnected from MongoDB');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }
}

/**
 * Check if connected to MongoDB
 */
function isConnectedToDB() {
  return isConnected && db !== null;
}

/**
 * Get connection error if any
 */
function getConnectionError() {
  return connectionError;
}

module.exports = {
  connectDB,
  getDB,
  getCollection,
  disconnectDB,
  isConnectedToDB,
  getConnectionError
};
