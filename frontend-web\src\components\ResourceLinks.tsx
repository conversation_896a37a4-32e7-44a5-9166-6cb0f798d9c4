import React from 'react';
import { Box, Typography, Paper, Grid, Link, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import {
  Agriculture,
  AccountBalance,
  Description,
  BarChart,
  LocalShipping
} from '@mui/icons-material';
import { useLanguage } from '../contexts/LanguageContext';

interface ResourceLink {
  name: string;
  url: string;
  description: string;
  icon: React.ReactNode;
}

// Resource links will be created in the component to use translations

const ResourceLinks: React.FC = () => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // Create resource links with translations
  const resourceLinks: ResourceLink[] = [
    {
      name: translate('resources.landbank'),
      url: 'https://www.landbank.co.za/',
      description: translate('resources.landbank_desc'),
      icon: <AccountBalance />
    },
    {
      name: translate('resources.arc'),
      url: 'https://www.arc.agric.za/',
      description: translate('resources.arc_desc'),
      icon: <Agriculture />
    },
    {
      name: translate('resources.deeds'),
      url: 'http://www.deeds.gov.za/',
      description: translate('resources.deeds_desc'),
      icon: <Description />
    },
    {
      name: translate('resources.namc'),
      url: 'https://www.namc.co.za/',
      description: translate('resources.namc_desc'),
      icon: <BarChart />
    },
    {
      name: translate('resources.ppecb'),
      url: 'https://ppecb.com/',
      description: translate('resources.ppecb_desc'),
      icon: <LocalShipping />
    }
  ];

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        mb: 3,
        borderRadius: 2,
        backgroundColor: alpha(theme.palette.primary.main, 0.05),
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
      }}
    >
      <Typography
        variant="h6"
        sx={{
          mb: 3,
          color: theme.palette.primary.main,
          fontWeight: 'medium'
        }}
      >
        {translate('resources.title')}
      </Typography>

      <Grid container spacing={2}>
        {resourceLinks.map((link, index) => (
          <Grid item xs={12} sm={6} md={4} key={link.name}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                underline="none"
              >
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center',
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.background.paper, 0.7),
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      transform: 'translateY(-5px)',
                      boxShadow: `0 6px 20px ${alpha(theme.palette.primary.main, 0.15)}`
                    }
                  }}
                >
                  <Box
                    sx={{
                      color: theme.palette.primary.main,
                      mb: 1,
                      p: 1,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.primary.main, 0.1)
                    }}
                  >
                    {link.icon}
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 'bold',
                      color: theme.palette.text.primary
                    }}
                  >
                    {link.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      mt: 1
                    }}
                  >
                    {link.description}
                  </Typography>
                </Paper>
              </Link>
            </motion.div>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default ResourceLinks;
