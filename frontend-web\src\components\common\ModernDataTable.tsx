import React, { useState } from 'react';
import { CustomButton } from '../common';
import { Box, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, TableSortLabel, Toolbar, Typography, IconButton, Tooltip, TextField, InputAdornment, Chip, Menu, MenuItem, Checkbox, FormControlLabel, useTheme, alpha, Divider, CircularProgress } from '@mui/material';
import {
  Search,
  FilterList,
  MoreVert,
  Download,
  Print,
  Share,
  Delete,
  Edit,
  Visibility,
  Add,
  Refresh,
  ArrowUpward,
  ArrowDownward,
  Clear
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { ErrorBoundary } from '.';
import { getSafeElevation, getSafeAnimation } from '../../utils/themeDefaults';

export interface Column {
  id: string;
  label: string;
  align?: 'left' | 'right' | 'center';
  format?: (value: any) => React.ReactNode;
  sortable?: boolean;
  width?: string;
  hide?: boolean;
}

export interface Action {
  icon: React.ReactNode;
  label: string;
  onClick: (row: any) => void;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  disabled?: (row: any) => boolean;
  hide?: (row: any) => boolean;
  tooltip?: string;
}

export interface Filter {
  id: string;
  label: string;
  options: { value: string; label: string }[];
  defaultValue?: string;
}

export interface ModernDataTableProps {
  title?: string;
  subtitle?: string;
  data: any[];
  columns: Column[];
  actions?: Action[];
  onRowClick?: (row: any) => void;
  onAdd?: () => void;
  onRefresh?: () => void;
  onDownload?: () => void;
  onDelete?: (selected: any[]) => void;
  filters?: Filter[];
  searchable?: boolean;
  selectable?: boolean;
  pagination?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  rowsPerPageOptions?: number[];
  defaultRowsPerPage?: number;
  defaultSortBy?: string;
  defaultSortDirection?: 'asc' | 'desc';
  module?: string;
  dense?: boolean;
  maxHeight?: number | string;
  stickyHeader?: boolean;
  showColumnVisibilityControl?: boolean;
}

const ModernDataTable: React.FC<ModernDataTableProps> = ({
  title,
  subtitle,
  data,
  columns,
  actions,
  onRowClick,
  onAdd,
  onRefresh,
  onDownload,
  onDelete,
  filters,
  searchable = true,
  selectable = false,
  pagination = true,
  loading = false,
  emptyMessage,
  rowsPerPageOptions = [5, 10, 25, 50],
  defaultRowsPerPage = 10,
  defaultSortBy,
  defaultSortDirection = 'asc',
  module,
  dense = false,
  maxHeight,
  stickyHeader = false,
  showColumnVisibilityControl = false
}) => {
  const theme = useTheme();
  const { translate } = useLanguage();

  // State
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(defaultRowsPerPage);
  const [sortBy, setSortBy] = useState<string | undefined>(defaultSortBy);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>(defaultSortDirection);
  const [searchQuery, setSearchQuery] = useState('');
  const [selected, setSelected] = useState<any[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, string>>({});
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>(
    columns.reduce((acc, column) => ({ ...acc, [column.id]: !column.hide }), {})
  );
  const [columnMenuAnchorEl, setColumnMenuAnchorEl] = useState<null | HTMLElement>(null);

  // Get module color
  const getModuleColor = () => {
    switch (module) {
      case 'animals':
        return theme.palette.primary.main;
      case 'breeding':
        return theme.palette.secondary.main;
      case 'health':
        return theme.palette.success.main;
      case 'feeding':
        return theme.palette.warning.main;
      case 'financial':
        return theme.palette.info.main;
      case 'reports':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const moduleColor = getModuleColor();

  // Handlers
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSort = (columnId: string) => {
    const isAsc = sortBy === columnId && sortDirection === 'asc';
    setSortDirection(isAsc ? 'desc' : 'asc');
    setSortBy(columnId);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelected(filteredData);
      return;
    }
    setSelected([]);
  };

  const handleSelectClick = (event: React.MouseEvent, row: any) => {
    event.stopPropagation();

    const selectedIndex = selected.findIndex(item => item.id === row.id);
    let newSelected: any[] = [];

    if (selectedIndex === -1) {
      newSelected = [...selected, row];
    } else {
      newSelected = selected.filter((_, index) => index !== selectedIndex);
    }

    setSelected(newSelected);
  };

  const handleFilterChange = (filterId: string, value: string) => {
    setFilterValues(prev => ({
      ...prev,
      [filterId]: value
    }));
    setPage(0);
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleColumnMenuOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setColumnMenuAnchorEl(event.currentTarget);
  };

  const handleColumnMenuClose = () => {
    setColumnMenuAnchorEl(null);
  };

  const handleColumnVisibilityChange = (columnId: string) => {
    setColumnVisibility(prev => ({
      ...prev,
      [columnId]: !prev[columnId]
    }));
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setFilterValues({});
    setPage(0);
  };

  // Filter and sort data
  const filteredData = data.filter(row => {
    // Apply search filter
    if (searchQuery) {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = columns.some(column => {
        const value = row[column.id];
        if (value == null) return false;
        return String(value).toLowerCase().includes(searchLower);
      });
      if (!matchesSearch) return false;
    }

    // Apply column filters
    for (const [filterId, filterValue] of Object.entries(filterValues)) {
      if (filterValue && row[filterId] !== filterValue) {
        return false;
      }
    }

    return true;
  });

  // Sort data
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortBy) return 0;

    const aValue = a[sortBy];
    const bValue = b[sortBy];

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return sortDirection === 'asc' ? -1 : 1;
    if (bValue == null) return sortDirection === 'asc' ? 1 : -1;

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    return sortDirection === 'asc'
      ? (aValue > bValue ? 1 : -1)
      : (bValue > aValue ? 1 : -1);
  });

  // Paginate data
  const paginatedData = pagination
    ? sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
    : sortedData;

  // Check if row is selected
  const isSelected = (row: any) => selected.findIndex(item => item.id === row.id) !== -1;

  // Visible columns
  const visibleColumns = columns.filter(column => columnVisibility[column.id]);

  return (
    <ErrorBoundary>
      <Paper
        elevation={2}
        sx={{
        borderRadius: 3,
        overflow: 'hidden',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: getSafeAnimation(theme, 'medium'),
        '&:hover': {
          boxShadow: getSafeElevation(theme, 3),
          transform: 'translateY(-4px)',
        },
      }}
    >
      {/* Table Header */}
      <Toolbar
        sx={{
          p: 2,
          pl: 3,
          pr: 3,
          ...(selected.length > 0 && {
            bgcolor: alpha(moduleColor, 0.1),
          }),
        }}
      >
        {selected.length > 0 ? (
          <Typography
            sx={{ flex: '1 1 100%' }}
            color="inherit"
            variant="subtitle1"
            component="div"
          >
            {selected.length} {translate('table.selected')}
          </Typography>
        ) : (
          <Box sx={{ flex: '1 1 100%' }}>
            {title && (
              <Typography variant="h6" fontWeight="bold">
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
        )}

        {selected.length > 0 ? (
          <Tooltip title={translate('common.delete')}>
            <IconButton
              onClick={() => onDelete && onDelete(selected)}
              color="error"
            >
              <Delete />
            </IconButton>
          </Tooltip>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {searchable && (
              <TextField
                variant="outlined"
                size="small"
                placeholder={translate('common.search')}
                value={searchQuery}
                onChange={handleSearch}
                sx={{ mr: 1, minWidth: 200 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  ),
                  endAdornment: searchQuery ? (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => setSearchQuery('')}
                        edge="end"
                      >
                        <Clear fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ) : null
                }}
              />
            )}

            {filters && filters.length > 0 && (
              <Tooltip title={translate('common.filter')}>
                <IconButton onClick={handleMenuOpen} sx={{ mr: 1 }}>
                  <FilterList />
                </IconButton>
              </Tooltip>
            )}

            {showColumnVisibilityControl && (
              <Tooltip title={translate('table.columns')}>
                <IconButton onClick={handleColumnMenuOpen} sx={{ mr: 1 }}>
                  <Visibility />
                </IconButton>
              </Tooltip>
            )}

            {onRefresh && (
              <Tooltip title={translate('common.refresh')}>
                <IconButton onClick={onRefresh} sx={{ mr: 1 }}>
                  <Refresh />
                </IconButton>
              </Tooltip>
            )}

            {onDownload && (
              <Tooltip title={translate('common.download')}>
                <IconButton onClick={onDownload} sx={{ mr: 1 }}>
                  <Download />
                </IconButton>
              </Tooltip>
            )}

            {onAdd && (
              <CustomButton
                variant="contained"
                startIcon={<Add />}
                onClick={onAdd}
                sx={{
                  ml: 1,
                  backgroundColor: moduleColor,
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'light'
                      ? alpha(moduleColor, 0.8)
                      : alpha(moduleColor, 1.2)
                  }
                }}
              >
                {translate('common.add')}
              </CustomButton>
            )}
          </Box>
        )}
      </Toolbar>

      {/* Filter Chips */}
      {(Object.keys(filterValues).length > 0 || searchQuery) && (
        <Box sx={{ px: 3, py: 1, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {searchQuery && (
            <Chip
              label={`${translate('common.search')}: ${searchQuery}`}
              onDelete={() => setSearchQuery('')}
              size="small"
              color="primary"
              variant="outlined"
            />
          )}

          {Object.entries(filterValues).map(([filterId, value]) => {
            if (!value) return null;
            const filter = filters?.find(f => f.id === filterId);
            const option = filter?.options.find(o => o.value === value);

            return (
              <Chip
                key={filterId}
                label={`${filter?.label || filterId}: ${option?.label || value}`}
                onDelete={() => handleFilterChange(filterId, '')}
                size="small"
                color="primary"
                variant="outlined"
              />
            );
          })}

          <CustomButton
            size="small"
            onClick={handleClearFilters}
            startIcon={<Clear fontSize="small" />}
            sx={{ ml: 'auto' }}
          >
            {translate('common.clear_all')}
          </CustomButton>
        </Box>
      )}

      {/* Table */}
      <TableContainer
        sx={{
          flex: 1,
          maxHeight: maxHeight,
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 8,
            height: 8
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: alpha(theme.palette.primary.main, 0.2),
            borderRadius: 4
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: alpha(theme.palette.primary.main, 0.05)
          }
        }}
      >
        <Table
          stickyHeader={stickyHeader}
          size={dense ? 'small' : 'medium'}
          sx={{ minWidth: 650 }}
        >
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < filteredData.length}
                    checked={filteredData.length > 0 && selected.length === filteredData.length}
                    onChange={handleSelectAllClick}
                    inputProps={{ 'aria-label': 'select all' }}
                    sx={{
                      color: moduleColor,
                      '&.Mui-checked': {
                        color: moduleColor,
                      },
                    }}
                  />
                </TableCell>
              )}

              {visibleColumns.map(column => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  sortDirection={sortBy === column.id ? sortDirection : false}
                  sx={{
                    fontWeight: 'bold',
                    whiteSpace: 'nowrap',
                    width: column.width,
                    backgroundColor: alpha(moduleColor, 0.05)
                  }}
                >
                  {column.sortable !== false ? (
                    <TableSortLabel
                      active={sortBy === column.id}
                      direction={sortBy === column.id ? sortDirection : 'asc'}
                      onClick={() => handleSort(column.id)}
                      sx={{
                        '&.MuiTableSortLabel-active': {
                          color: moduleColor,
                        },
                        '& .MuiTableSortLabel-icon': {
                          color: `${moduleColor} !important`,
                        },
                      }}
                    >
                      {column.label}
                    </TableSortLabel>
                  ) : (
                    column.label
                  )}
                </TableCell>
              ))}

              {actions && actions.length > 0 && (
                <TableCell
                  align="right"
                  sx={{
                    fontWeight: 'bold',
                    backgroundColor: alpha(moduleColor, 0.05)
                  }}
                >
                  {translate('table.actions')}
                </TableCell>
              )}
            </TableRow>
          </TableHead>

          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell
                  colSpan={visibleColumns.length + (selectable ? 1 : 0) + (actions ? 1 : 0)}
                  align="center"
                  sx={{ py: 6 }}
                >
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <CircularProgress size={40} sx={{ mb: 2, color: moduleColor }} />
                    <Typography variant="body1" color="text.secondary">
                      {translate('common.loading')}
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ) : paginatedData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={visibleColumns.length + (selectable ? 1 : 0) + (actions ? 1 : 0)}
                  align="center"
                  sx={{ py: 6 }}
                >
                  <Typography variant="body1" color="text.secondary">
                    {emptyMessage || translate('table.no_data')}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              paginatedData.map((row, index) => {
                const isItemSelected = isSelected(row);

                return (
                  <TableRow
                    hover
                    onClick={onRowClick ? () => onRowClick(row) : undefined}
                    role="checkbox"
                    aria-checked={isItemSelected}
                    tabIndex={-1}
                    key={row.id || index}
                    selected={isItemSelected}
                    sx={{
                      cursor: onRowClick ? 'pointer' : 'default',
                      '&.Mui-selected': {
                        backgroundColor: alpha(moduleColor, 0.1),
                      },
                      '&.Mui-selected:hover': {
                        backgroundColor: alpha(moduleColor, 0.15),
                      },
                    }}
                  >
                    {selectable && (
                      <TableCell padding="checkbox">
                        <Checkbox
                          checked={isItemSelected}
                          onClick={(event) => handleSelectClick(event, row)}
                          sx={{
                            color: moduleColor,
                            '&.Mui-checked': {
                              color: moduleColor,
                            },
                          }}
                        />
                      </TableCell>
                    )}

                    {visibleColumns.map(column => (
                      <TableCell
                        key={column.id}
                        align={column.align || 'left'}
                      >
                        {column.format ? column.format(row[column.id]) : row[column.id]}
                      </TableCell>
                    ))}

                    {actions && actions.length > 0 && (
                      <TableCell align="right">
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                          {actions.map((action, actionIndex) => {
                            if (action.hide && action.hide(row)) return null;

                            return (
                              <Tooltip key={actionIndex} title={action.tooltip || action.label}>
                                <span>
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      action.onClick(row);
                                    }}
                                    disabled={action.disabled ? action.disabled(row) : false}
                                    color={action.color || 'default'}
                                    sx={{ ml: 1 }}
                                  >
                                    {action.icon}
                                  </IconButton>
                                </span>
                              </Tooltip>
                            );
                          })}
                        </Box>
                      </TableCell>
                    )}
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {pagination && (
        <TablePagination
          rowsPerPageOptions={rowsPerPageOptions}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage={translate('table.rows_per_page')}
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} ${translate('table.of')} ${count}`
          }
          sx={{
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
              fontSize: '0.875rem',
            },
          }}
        />
      )}

      {/* Filter Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 200,
            maxHeight: 400,
            overflow: 'auto'
          }
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1.5, fontWeight: 'bold' }}>
          {translate('common.filters')}
        </Typography>
        <Divider />

        {filters?.map((filter) => (
          <Box key={filter.id} sx={{ p: 2 }}>
            <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
              {filter.label}
            </Typography>

            {filter.options.map((option) => (
              <FormControlLabel
                key={option.value}
                control={
                  <Checkbox
                    checked={filterValues[filter.id] === option.value}
                    onChange={() => {
                      const newValue = filterValues[filter.id] === option.value ? '' : option.value;
                      handleFilterChange(filter.id, newValue);
                    }}
                    size="small"
                    sx={{
                      color: moduleColor,
                      '&.Mui-checked': {
                        color: moduleColor,
                      },
                    }}
                  />
                }
                label={
                  <Typography variant="body2">
                    {option.label}
                  </Typography>
                }
                sx={{ display: 'block', mb: 0.5 }}
              />
            ))}

            {filter !== filters[filters.length - 1] && (
              <Divider sx={{ mt: 1.5, mb: 0.5 }} />
            )}
          </Box>
        ))}
      </Menu>

      {/* Column Visibility Menu */}
      <Menu
        anchorEl={columnMenuAnchorEl}
        open={Boolean(columnMenuAnchorEl)}
        onClose={handleColumnMenuClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          elevation: 3,
          sx: {
            borderRadius: 2,
            minWidth: 200,
            maxHeight: 400,
            overflow: 'auto'
          }
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 2, py: 1.5, fontWeight: 'bold' }}>
          {translate('table.columns')}
        </Typography>
        <Divider />

        {columns.map((column) => (
          <MenuItem key={column.id} dense>
            <FormControlLabel
              control={
                <Checkbox
                  checked={columnVisibility[column.id]}
                  onChange={() => handleColumnVisibilityChange(column.id)}
                  size="small"
                  sx={{
                    color: moduleColor,
                    '&.Mui-checked': {
                      color: moduleColor,
                    },
                  }}
                />
              }
              label={
                <Typography variant="body2">
                  {column.label}
                </Typography>
              }
              sx={{ width: '100%' }}
            />
          </MenuItem>
        ))}
      </Menu>
    </Paper>
    </ErrorBoundary>
  );
};

export default ModernDataTable;
