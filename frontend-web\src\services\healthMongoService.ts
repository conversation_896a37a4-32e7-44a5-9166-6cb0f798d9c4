import { MongoDbService } from './mongoDbService';
import { HealthRecord } from '../types/health';

/**
 * Service for health-related operations
 */
class HealthMongoService extends MongoDbService<HealthRecord> {
  constructor() {
    super('health_records');
  }

  /**
   * Find health records by animal ID
   * @param animalId Animal ID
   */
  async findByAnimalId(animalId: string) {
    return this.findAll({ animalId });
  }

  /**
   * Find health records by type
   * @param recordType Record type (e.g., 'vaccination', 'treatment', 'checkup')
   */
  async findByType(recordType: string) {
    return this.findAll({ type: recordType });
  }

  /**
   * Find health records by date range
   * @param startDate Start date
   * @param endDate End date
   */
  async findByDateRange(startDate: Date, endDate: Date) {
    return this.findAll({
      date: { $gte: startDate, $lte: endDate }
    });
  }

  /**
   * Find upcoming vaccinations
   * @param daysAhead Number of days ahead to look
   */
  async findUpcomingVaccinations(daysAhead: number = 30) {
    const today = new Date();
    const futureDate = new Date();
    futureDate.setDate(today.getDate() + daysAhead);
    
    return this.findAll({
      type: 'vaccination',
      nextDueDate: { $gte: today, $lte: futureDate }
    });
  }

  /**
   * Find records by veterinarian
   * @param veterinarian Veterinarian name
   */
  async findByVeterinarian(veterinarian: string) {
    return this.findAll({ veterinarian });
  }
}

export const healthMongoService = new HealthMongoService();
