import React from 'react';
import ModuleSummary from '../components/ModuleSummary';
import { ROUTES } from '../constants/routes';

const Resources: React.FC = () => {
  const resourcesSubModules = [
    {
      name: 'Guidelines',
      path: `${ROUTES.RESOURCES}/guidelines`,
      icon: '📚',
      count: 45,
      status: 'Updated guides'
    },
    {
      name: 'Training',
      path: `${ROUTES.RESOURCES}/training`,
      icon: '🎓',
      count: 12,
      status: '3 new courses'
    },
    {
      name: 'Support',
      path: `${ROUTES.RESOURCES}/support`,
      icon: '🆘',
      count: 8,
      status: 'Active tickets'
    },
    {
      name: 'Documentation',
      path: `${ROUTES.RESOURCES}/documentation`,
      icon: '📄',
      count: 156,
      status: 'Available docs'
    },
    {
      name: 'Downloads',
      path: `${ROUTES.RESOURCES}/downloads`,
      icon: '⬇️',
      count: 25,
      status: 'Available files'
    }
  ];

  const resourcesStats = {
    total_resources: 245,
    active_users: 89,
    completion_rate: '78%',
    satisfaction: '4.5/5'
  };

  return (
    <ModuleSummary
      title="Resources"
      description="Training materials and support resources"
      subModules={resourcesSubModules}
      stats={resourcesStats}
    />
  );
};

export default Resources;
