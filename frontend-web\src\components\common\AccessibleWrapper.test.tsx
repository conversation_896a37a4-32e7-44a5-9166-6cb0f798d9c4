import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import AccessibleWrapper from './AccessibleWrapper';

describe('AccessibleWrapper Component', () => {
  test('renders children correctly', () => {
    render(
      <AccessibleWrapper>
        <div>Test Content</div>
      </AccessibleWrapper>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('applies correct ARIA attributes', () => {
    render(
      <AccessibleWrapper
        role="button"
        ariaLabel="Test Button"
        ariaDescription="This is a test button"
        ariaExpanded={true}
        ariaSelected={true}
        ariaRequired={true}
        ariaDisabled={false}
      >
        Test Content
      </AccessibleWrapper>
    );

    const wrapper = screen.getByRole('button');
    expect(wrapper).toHaveAttribute('aria-label', 'Test Button');
    expect(wrapper).toHaveAttribute('aria-description', 'This is a test button');
    expect(wrapper).toHaveAttribute('aria-expanded', 'true');
    expect(wrapper).toHaveAttribute('aria-selected', 'true');
    expect(wrapper).toHaveAttribute('aria-required', 'true');
    expect(wrapper).toHaveAttribute('aria-disabled', 'false');
  });

  test('handles keyboard events correctly', () => {
    const onEnterMock = jest.fn();
    const onSpaceMock = jest.fn();
    const onArrowUpMock = jest.fn();
    const onArrowDownMock = jest.fn();
    const onEscapeMock = jest.fn();

    render(
      <AccessibleWrapper
        role="button"
        onKeyAction={{
          onEnter: onEnterMock,
          onSpace: onSpaceMock,
          onArrowUp: onArrowUpMock,
          onArrowDown: onArrowDownMock,
          onEscape: onEscapeMock,
        }}
      >
        Test Content
      </AccessibleWrapper>
    );

    const wrapper = screen.getByRole('button');
    
    // Test Enter key
    fireEvent.keyDown(wrapper, { key: 'Enter' });
    expect(onEnterMock).toHaveBeenCalledTimes(1);
    
    // Test Space key
    fireEvent.keyDown(wrapper, { key: ' ' });
    expect(onSpaceMock).toHaveBeenCalledTimes(1);
    
    // Test Arrow Up key
    fireEvent.keyDown(wrapper, { key: 'ArrowUp' });
    expect(onArrowUpMock).toHaveBeenCalledTimes(1);
    
    // Test Arrow Down key
    fireEvent.keyDown(wrapper, { key: 'ArrowDown' });
    expect(onArrowDownMock).toHaveBeenCalledTimes(1);
    
    // Test Escape key
    fireEvent.keyDown(wrapper, { key: 'Escape' });
    expect(onEscapeMock).toHaveBeenCalledTimes(1);
  });

  test('applies custom styles correctly', () => {
    render(
      <AccessibleWrapper
        role="region"
        sx={{ backgroundColor: 'red', padding: '10px' }}
      >
        Test Content
      </AccessibleWrapper>
    );

    const wrapper = screen.getByRole('region');
    expect(wrapper).toHaveStyle('background-color: red');
    expect(wrapper).toHaveStyle('padding: 10px');
  });

  test('applies correct tabIndex', () => {
    render(
      <AccessibleWrapper
        role="button"
        tabIndex={2}
      >
        Test Content
      </AccessibleWrapper>
    );

    const wrapper = screen.getByRole('button');
    expect(wrapper).toHaveAttribute('tabindex', '2');
  });

  test('applies ARIA relationships correctly', () => {
    render(
      <AccessibleWrapper
        role="tab"
        ariaControls="panel-1"
        ariaLabelledBy="tab-1"
        ariaDescribedBy="description-1"
      >
        Test Tab
      </AccessibleWrapper>
    );

    const wrapper = screen.getByRole('tab');
    expect(wrapper).toHaveAttribute('aria-controls', 'panel-1');
    expect(wrapper).toHaveAttribute('aria-labelledby', 'tab-1');
    expect(wrapper).toHaveAttribute('aria-describedby', 'description-1');
  });
});
