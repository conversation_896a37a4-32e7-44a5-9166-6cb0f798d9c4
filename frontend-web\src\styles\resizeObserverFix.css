/**
 * CSS fixes for ResizeObserver loop errors
 * These styles help prevent ResizeObserver loop errors by limiting the frequency of resize events
 */

/* Apply to all elements that use ResizeObserver */
.recharts-responsive-container {
  /* Prevent rapid resize events */
  transition: width 0.1s ease-in-out, height 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: width, height !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart containers */
.recharts-wrapper {
  /* Prevent rapid resize events */
  transition: width 0.1s ease-in-out, height 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: width, height !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart surfaces */
.recharts-surface {
  /* Prevent rapid resize events */
  transition: width 0.1s ease-in-out, height 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: width, height !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart elements */
.recharts-layer {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart tooltips */
.recharts-tooltip-wrapper {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart legends */
.recharts-legend-wrapper {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart axes */
.recharts-cartesian-axis {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart grids */
.recharts-cartesian-grid {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart bars */
.recharts-bar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart lines */
.recharts-line {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart areas */
.recharts-area {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart pies */
.recharts-pie {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart radars */
.recharts-radar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart scatters */
.recharts-scatter {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart radial bars */
.recharts-radial-bar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart funnel */
.recharts-funnel {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart treemap */
.recharts-treemap {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart sankey */
.recharts-sankey {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart composed */
.recharts-composed {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart brush */
.recharts-brush {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference lines */
.recharts-reference-line {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference areas */
.recharts-reference-area {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference dots */
.recharts-reference-dot {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart error bars */
.recharts-errorBar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart text */
.recharts-text {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart labels */
.recharts-label {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart sectors */
.recharts-sector {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart curves */
.recharts-curve {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart polygons */
.recharts-polygon {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart dots */
.recharts-dot {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart rectangles */
.recharts-rectangle {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart paths */
.recharts-path {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart circles */
.recharts-circle {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart lines */
.recharts-line {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart areas */
.recharts-area {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart bars */
.recharts-bar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart pies */
.recharts-pie {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart radars */
.recharts-radar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart scatters */
.recharts-scatter {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart radial bars */
.recharts-radial-bar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart funnels */
.recharts-funnel {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart treemaps */
.recharts-treemap {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart sankeys */
.recharts-sankey {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart composed */
.recharts-composed {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart brushes */
.recharts-brush {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference lines */
.recharts-reference-line {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference areas */
.recharts-reference-area {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart reference dots */
.recharts-reference-dot {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart error bars */
.recharts-errorBar {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart texts */
.recharts-text {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart labels */
.recharts-label {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart sectors */
.recharts-sector {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart curves */
.recharts-curve {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart polygons */
.recharts-polygon {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart dots */
.recharts-dot {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart rectangles */
.recharts-rectangle {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart paths */
.recharts-path {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}

/* Apply to all chart circles */
.recharts-circle {
  /* Prevent rapid resize events */
  transition: transform 0.1s ease-in-out !important;
  /* Ensure proper rendering */
  will-change: transform !important;
  /* Prevent layout shifts */
  contain: layout !important;
}
