/**
 * Custom resolver for MongoDB modules
 * This file is used to resolve MongoDB modules that use Node.js-specific features
 */

class MongoDBResolver {
  constructor() {
    this.apply = this.apply.bind(this);
  }

  apply(resolver) {
    const target = resolver.ensureHook('resolve');
    
    resolver.getHook('resolve').tapAsync('MongoDBResolver', (request, resolveContext, callback) => {
      // Check if the request is for a MongoDB module that uses timers/promises
      if (request.request && (
        request.request.includes('mongodb/lib/cmap/auth/mongodb_oidc/callback_workflow') ||
        request.request.includes('mongodb/lib/cmap/auth/mongodb_oidc/machine_workflow')
      )) {
        // Resolve to our empty module instead
        const emptyModulePath = require.resolve('../polyfills/empty-module.js');
        const obj = {
          path: emptyModulePath,
          query: request.query,
          file: true,
          resolved: true
        };
        return callback(null, obj);
      }
      
      // Continue with the normal resolution process
      callback();
    });
  }
}

module.exports = MongoDBResolver;
