import { notificationService, NotificationType, NotificationPriority, BirthNotificationConfig } from './notificationService';
import { differenceInDays } from 'date-fns';

/**
 * Interface for a scheduled notification
 */
export interface ScheduledNotification {
  id: string;
  animalId: string;
  animalName: string;
  expectedDueDate: Date;
  notificationType: NotificationType;
  recipient: {
    name: string;
    phone?: string;
    email?: string;
  };
  message: string;
  priority: NotificationPriority;
  scheduledDate: Date;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  sentDate?: Date;
  error?: string;
}

/**
 * Service for scheduling and managing notifications
 */
export const notificationSchedulerService = {
  scheduledNotifications: [] as ScheduledNotification[],
  
  /**
   * Schedule notifications for an upcoming birth
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param expectedDueDate Expected due date
   * @param config Notification configuration
   * @returns Array of scheduled notification IDs
   */
  scheduleBirthNotifications: (
    animalId: string,
    animalName: string,
    expectedDueDate: Date,
    config: BirthNotificationConfig
  ): string[] => {
    if (!config.enabled) {
      return [];
    }
    
    const scheduledIds: string[] = [];
    const now = new Date();
    
    // For each notification day threshold
    for (const days of config.notifyDaysBefore) {
      // Calculate the notification date
      const notificationDate = new Date(expectedDueDate);
      notificationDate.setDate(notificationDate.getDate() - days);
      
      // Skip if the notification date is in the past
      if (notificationDate < now) {
        continue;
      }
      
      // Determine priority based on days
      let priority: NotificationPriority = 'low';
      if (config.escalationEnabled && config.escalationThresholds) {
        if (days <= config.escalationThresholds.critical) {
          priority = 'critical';
        } else if (days <= config.escalationThresholds.high) {
          priority = 'high';
        } else if (days <= config.escalationThresholds.medium) {
          priority = 'medium';
        }
      } else {
        // Default priority logic if escalation thresholds not provided
        if (days <= 2) {
          priority = 'critical';
        } else if (days <= 7) {
          priority = 'high';
        } else if (days <= 14) {
          priority = 'medium';
        }
      }
      
      // For each recipient
      for (const recipient of config.recipients) {
        // For each notification type
        for (const notificationType of recipient.notificationTypes) {
          // Skip if recipient doesn't have required contact info for this notification type
          if ((notificationType === 'sms' || notificationType === 'voice' || notificationType === 'whatsapp') && !recipient.phone) {
            continue;
          }
          if (notificationType === 'email' && !recipient.email) {
            continue;
          }
          
          // Create message based on notification type and days remaining
          let message = config.customMessage || 
            `BIRTH ALERT: ${animalName} (ID: ${animalId}) is expected to give birth in ${days} days.`;
          
          // Add urgency indicator based on priority
          if (priority === 'critical') {
            message = `URGENT: ${message} IMMEDIATE ATTENTION REQUIRED.`;
          } else if (priority === 'high') {
            message = `IMPORTANT: ${message} Please prepare accordingly.`;
          }
          
          // Create a scheduled notification
          const scheduledNotification: ScheduledNotification = {
            id: `notification-${Date.now()}-${Math.floor(Math.random() * 10000)}`,
            animalId,
            animalName,
            expectedDueDate,
            notificationType,
            recipient: {
              name: recipient.name,
              phone: recipient.phone,
              email: recipient.email
            },
            message,
            priority,
            scheduledDate: notificationDate,
            status: 'pending'
          };
          
          // Add to scheduled notifications
          notificationSchedulerService.scheduledNotifications.push(scheduledNotification);
          scheduledIds.push(scheduledNotification.id);
        }
      }
    }
    
    return scheduledIds;
  },
  
  /**
   * Get all scheduled notifications
   * @returns Array of scheduled notifications
   */
  getScheduledNotifications: (): ScheduledNotification[] => {
    return [...notificationSchedulerService.scheduledNotifications];
  },
  
  /**
   * Get scheduled notifications for a specific animal
   * @param animalId Animal ID
   * @returns Array of scheduled notifications for the animal
   */
  getScheduledNotificationsForAnimal: (animalId: string): ScheduledNotification[] => {
    return notificationSchedulerService.scheduledNotifications.filter(n => n.animalId === animalId);
  },
  
  /**
   * Cancel a scheduled notification
   * @param notificationId Notification ID
   * @returns True if cancelled successfully, false otherwise
   */
  cancelNotification: (notificationId: string): boolean => {
    const index = notificationSchedulerService.scheduledNotifications.findIndex(n => n.id === notificationId);
    if (index !== -1 && notificationSchedulerService.scheduledNotifications[index].status === 'pending') {
      notificationSchedulerService.scheduledNotifications[index].status = 'cancelled';
      return true;
    }
    return false;
  },
  
  /**
   * Cancel all scheduled notifications for an animal
   * @param animalId Animal ID
   * @returns Number of notifications cancelled
   */
  cancelNotificationsForAnimal: (animalId: string): number => {
    let cancelCount = 0;
    notificationSchedulerService.scheduledNotifications.forEach(notification => {
      if (notification.animalId === animalId && notification.status === 'pending') {
        notification.status = 'cancelled';
        cancelCount++;
      }
    });
    return cancelCount;
  },
  
  /**
   * Process due notifications
   * This would typically be called by a background process or cron job
   * @returns Array of processed notification results
   */
  processDueNotifications: async (): Promise<Array<{ notification: ScheduledNotification, success: boolean }>> => {
    const now = new Date();
    const results: Array<{ notification: ScheduledNotification, success: boolean }> = [];
    
    // Find notifications that are due
    const dueNotifications = notificationSchedulerService.scheduledNotifications.filter(
      n => n.status === 'pending' && n.scheduledDate <= now
    );
    
    // Process each due notification
    for (const notification of dueNotifications) {
      try {
        let result;
        
        switch (notification.notificationType) {
          case 'sms':
            if (notification.recipient.phone) {
              result = await notificationService.sendSMS(
                notification.recipient.phone,
                notification.message,
                notification.priority
              );
            }
            break;
          case 'voice':
            if (notification.recipient.phone) {
              result = await notificationService.sendVoiceCall(
                notification.recipient.phone,
                notification.message,
                notification.priority
              );
            }
            break;
          case 'email':
            if (notification.recipient.email) {
              result = await notificationService.sendEmail(
                notification.recipient.email,
                `Birth Alert for ${notification.animalName}`,
                notification.message,
                notification.priority
              );
            }
            break;
          case 'whatsapp':
            if (notification.recipient.phone) {
              result = await notificationService.sendWhatsApp(
                notification.recipient.phone,
                notification.message,
                notification.priority
              );
            }
            break;
          case 'app':
            result = await notificationService.sendAppNotification(
              notification.recipient.name,
              notification.message,
              notification.priority
            );
            break;
        }
        
        // Update notification status
        if (result && result.success) {
          notification.status = 'sent';
          notification.sentDate = new Date();
          results.push({ notification, success: true });
        } else {
          notification.status = 'failed';
          notification.error = result?.error || 'Unknown error';
          results.push({ notification, success: false });
        }
      } catch (error) {
        // Handle errors
        notification.status = 'failed';
        notification.error = error instanceof Error ? error.message : 'Unknown error';
        results.push({ notification, success: false });
      }
    }
    
    return results;
  },
  
  /**
   * Check if an animal needs notifications based on its due date
   * @param animalId Animal ID
   * @param animalName Animal name
   * @param expectedDueDate Expected due date
   * @param config Notification configuration
   * @returns Object with notification status and days remaining
   */
  checkNotificationStatus: (
    animalId: string,
    animalName: string,
    expectedDueDate: Date,
    config: BirthNotificationConfig
  ): { 
    needsNotification: boolean; 
    daysRemaining: number; 
    priority: NotificationPriority;
    nextNotificationDays?: number;
  } => {
    const now = new Date();
    const daysRemaining = differenceInDays(expectedDueDate, now);
    
    // Determine priority based on days remaining
    let priority: NotificationPriority = 'low';
    if (config.escalationEnabled && config.escalationThresholds) {
      if (daysRemaining <= config.escalationThresholds.critical) {
        priority = 'critical';
      } else if (daysRemaining <= config.escalationThresholds.high) {
        priority = 'high';
      } else if (daysRemaining <= config.escalationThresholds.medium) {
        priority = 'medium';
      }
    } else {
      // Default priority logic
      if (daysRemaining <= 2) {
        priority = 'critical';
      } else if (daysRemaining <= 7) {
        priority = 'high';
      } else if (daysRemaining <= 14) {
        priority = 'medium';
      }
    }
    
    // Check if notification is needed
    const needsNotification = config.enabled && config.notifyDaysBefore.includes(daysRemaining);
    
    // Find the next notification day
    let nextNotificationDays: number | undefined;
    if (!needsNotification) {
      const futureNotificationDays = config.notifyDaysBefore
        .filter(days => days < daysRemaining)
        .sort((a, b) => b - a); // Sort in descending order
      
      nextNotificationDays = futureNotificationDays[0];
    }
    
    return {
      needsNotification,
      daysRemaining,
      priority,
      nextNotificationDays
    };
  }
};

export default notificationSchedulerService;
