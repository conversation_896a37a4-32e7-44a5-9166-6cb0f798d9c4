const express = require('express');
const router = express.Router();
const { executeQuery } = require('../config/db');
const mongoAtlasSqlService = require('../services/mongoAtlasSqlService');
const logger = require('../utils/logger');

router.get('/metrics/:type', async (req, res) => {
  const { type } = req.params;
  const { timeRange } = req.query;
  
  try {
    let query = '';
    const today = new Date();
    let startDate = new Date();
    
    // Calculate date range
    switch(timeRange) {
      case 'day':
        startDate.setDate(today.getDate() - 1);
        break;
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 1);
        break;
      default:
        startDate.setDate(today.getDate() - 7); // Default to week
    }

    // Build query based on metric type
    switch(type) {
      case 'livestock':
        query = `
          SELECT 
            FORMAT(record_date, 'yyyy-MM-dd') as date,
            COUNT(*) as total,
            SUM(CASE WHEN health_status = 'healthy' THEN 1 ELSE 0 END) as healthy,
            SUM(CASE WHEN health_status = 'sick' THEN 1 ELSE 0 END) as sick,
            SUM(CASE WHEN breeding_status = 'pregnant' THEN 1 ELSE 0 END) as pregnant
          FROM Animals
          WHERE record_date BETWEEN @startDate AND @endDate
          GROUP BY FORMAT(record_date, 'yyyy-MM-dd')
          ORDER BY date;
        `;
        break;
      
      case 'financial':
        query = `
          SELECT 
            FORMAT(transaction_date, 'yyyy-MM-dd') as date,
            SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as revenue,
            SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as expenses,
            SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END) as profit
          FROM Transactions
          WHERE transaction_date BETWEEN @startDate AND @endDate
          GROUP BY FORMAT(transaction_date, 'yyyy-MM-dd')
          ORDER BY date;
        `;
        break;
      
      // Add more metric types as needed
    }

    const result = await executeQuery(query, {
      startDate: startDate.toISOString(),
      endDate: today.toISOString()
    });

    // Transform data for chart
    const labels = result.recordset.map(row => row.date);
    const datasets = [];
    
    // Remove date from recordset and create datasets
    const firstRow = result.recordset[0];
    if (firstRow) {
      Object.keys(firstRow).forEach(key => {
        if (key !== 'date') {
          datasets.push({
            label: key.charAt(0).toUpperCase() + key.slice(1),
            data: result.recordset.map(row => row[key]),
            color: getColorForMetric(key)
          });
        }
      });
    }

    res.json({
      labels,
      datasets
    });

  } catch (error) {
    logger.error('Error fetching metrics:', error);
    res.status(500).json({ error: 'Failed to fetch metrics' });
  }
});

// Helper function to assign consistent colors to metrics
function getColorForMetric(metric) {
  const colorMap = {
    total: '#2563eb',     // blue
    healthy: '#16a34a',   // green
    sick: '#dc2626',      // red
    pregnant: '#9333ea',  // purple
    revenue: '#16a34a',   // green
    expenses: '#dc2626',  // red
    profit: '#2563eb'     // blue
  };
  return colorMap[metric] || '#666666';
}

/**
 * @route GET /api/analytics/livestock-performance
 * @desc Get livestock performance analytics using MongoDB Atlas SQL
 * @access Private
 */
router.get('/livestock-performance', async (req, res) => {
  try {
    logger.info('Fetching livestock performance analytics');

    const analytics = await mongoAtlasSqlService.getLivestockPerformanceAnalytics();

    res.json({
      success: true,
      data: analytics,
      message: 'Livestock performance analytics retrieved successfully'
    });
  } catch (error) {
    logger.error('Error fetching livestock performance analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch livestock performance analytics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/financial
 * @desc Get financial analytics for a date range
 * @access Private
 */
router.get('/financial', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    // Default to last 12 months if no dates provided
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setFullYear(defaultStartDate.getFullYear() - 1);

    const start = startDate ? new Date(startDate) : defaultStartDate;
    const end = endDate ? new Date(endDate) : defaultEndDate;

    logger.info(`Fetching financial analytics from ${start.toISOString()} to ${end.toISOString()}`);

    const analytics = await mongoAtlasSqlService.getFinancialAnalytics(start, end);

    res.json({
      success: true,
      data: analytics,
      dateRange: { startDate: start, endDate: end },
      message: 'Financial analytics retrieved successfully'
    });
  } catch (error) {
    logger.error('Error fetching financial analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch financial analytics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/health-trends
 * @desc Get health trends analytics
 * @access Private
 */
router.get('/health-trends', async (req, res) => {
  try {
    logger.info('Fetching health trends analytics');

    const analytics = await mongoAtlasSqlService.getHealthTrendsAnalytics();

    res.json({
      success: true,
      data: analytics,
      message: 'Health trends analytics retrieved successfully'
    });
  } catch (error) {
    logger.error('Error fetching health trends analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch health trends analytics',
      error: error.message
    });
  }
});

/**
 * @route GET /api/analytics/breeding-efficiency
 * @desc Get breeding efficiency analytics
 * @access Private
 */
router.get('/breeding-efficiency', async (req, res) => {
  try {
    logger.info('Fetching breeding efficiency analytics');

    const analytics = await mongoAtlasSqlService.getBreedingEfficiencyAnalytics();

    res.json({
      success: true,
      data: analytics,
      message: 'Breeding efficiency analytics retrieved successfully'
    });
  } catch (error) {
    logger.error('Error fetching breeding efficiency analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch breeding efficiency analytics',
      error: error.message
    });
  }
});

module.exports = router;