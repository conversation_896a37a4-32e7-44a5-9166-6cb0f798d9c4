export interface Auction {
  id: string;
  title: string;
  date: Date;
  status: 'upcoming' | 'live' | 'closed';
  location: string;
  description: string;
  items: AuctionItem[];
  registeredBidders: number;
  minimumBid: number;
  currentHighestBid?: number;
  endTime: Date;
}

export interface AuctionItem {
  id: string;
  type: 'livestock' | 'equipment' | 'other';
  description: string;
  startingPrice: number;
  currentBid?: number;
  numberOfBids: number;
  images: string[];
  status: 'available' | 'sold' | 'withdrawn';
}

export const mockAuctions: Auction[] = [
  {
    id: 'AUC001',
    title: 'Spring Livestock Auction 2024',
    date: new Date('2024-03-15'),
    status: 'upcoming',
    location: 'Main Auction House',
    description: 'Annual spring livestock auction featuring premium cattle',
    items: [
      {
        id: 'AUCITEM001',
        type: 'livestock',
        description: 'Premium Angus Bull, 2 years old',
        startingPrice: 3000,
        numberOfBids: 0,
        images: ['bull1.jpg', 'bull2.jpg'],
        status: 'available'
      }
    ],
    registeredBidders: 45,
    minimumBid: 500,
    endTime: new Date('2024-03-15T18:00:00')
  }
];