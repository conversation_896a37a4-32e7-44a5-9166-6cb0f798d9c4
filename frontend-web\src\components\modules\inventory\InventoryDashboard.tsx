import React from 'react';
import { ROUTES } from '../../../constants/routes';
import ModuleDashboard, { ModuleMetric, QuickAction } from '../../ModuleDashboard';

const InventoryDashboard: React.FC = () => {
  const metrics: ModuleMetric[] = [
    { label: 'Feed Stock', value: '85%', trend: '-5%', status: 'neutral' },
    { label: 'Medicine Stock', value: '92%', trend: 'neutral', status: 'positive' },
    { label: 'Low Stock Items', value: '3', trend: '+1', status: 'warning' },
    { label: 'Stock Value', value: '$24,850', trend: '+2%', status: 'positive' }
  ];

  const quickActions: QuickAction[] = [
    { name: 'Stock Update', icon: '📦', path: `${ROUTES.INVENTORY}/update` },
    { name: 'Order Supplies', icon: '🛒', path: `${ROUTES.INVENTORY}/order` },
    { name: 'Stock Count', icon: '🔢', path: `${ROUTES.INVENTORY}/count` },
    { name: 'View Alerts', icon: '⚠️', path: `${ROUTES.INVENTORY}/alerts` }
  ];

  return (
    <ModuleDashboard
      title="Inventory Management"
      description="Track and manage farm supplies and equipment"
      metrics={metrics}
      quickActions={quickActions}
    />
  );
};

export default InventoryDashboard;