/**
 * MongoDB Data Population Script
 *
 * This script populates the MongoDB database with realistic data for the AMPD Livestock application.
 * It uses the mock data from the application and adds additional realistic data.
 */

require('dotenv').config();
const { MongoClient, ServerApiVersion } = require('mongodb');
const logger = require('../src/utils/logger');
const fs = require('fs');
const path = require('path');

// Import mock data
let mockAnimals = [];
let mockHealthRecords = [];
let mockVaccinations = [];
let mockBreedingRecords = [];
let mockCalvingRecords = [];
let mockFeedingRecords = [];
let mockFeedInventory = [];
let mockInventory = [];
let mockPastures = [];
let mockExpenses = [];
let mockUsers = [];
let mockReports = [];

// Load mock data with error handling
try {
  mockAnimals = require('../src/mocks/animalData').mockAnimals || [];
  logger.info(`Loaded ${mockAnimals.length} animals from mock data`);
} catch (error) {
  logger.error('Error loading animalData:', error);
}

try {
  const healthData = require('../src/mocks/healthData');
  mockHealthRecords = healthData.mockHealthRecords || [];
  mockVaccinations = healthData.mockVaccinations || [];
  logger.info(`Loaded ${mockHealthRecords.length} health records and ${mockVaccinations ? mockVaccinations.length : 0} vaccinations from mock data`);
} catch (error) {
  logger.error('Error loading healthData:', error);
}

try {
  const breedingData = require('../src/mocks/breedingData');
  mockBreedingRecords = breedingData.mockBreedingRecords || [];
  mockCalvingRecords = breedingData.mockCalvingRecords || [];
  logger.info(`Loaded ${mockBreedingRecords.length} breeding records and ${mockCalvingRecords.length} calving records from mock data`);
} catch (error) {
  logger.error('Error loading breedingData:', error);
}

try {
  const feedingData = require('../src/mocks/feedingData');
  mockFeedingRecords = feedingData.mockFeedingRecords || [];
  mockFeedInventory = feedingData.mockFeedInventory || [];
  logger.info(`Loaded ${mockFeedingRecords.length} feeding records and ${mockFeedInventory.length} feed inventory items from mock data`);
} catch (error) {
  logger.error('Error loading feedingData:', error);
}

try {
  mockInventory = require('../src/mocks/inventoryData').mockInventory || [];
  logger.info(`Loaded ${mockInventory.length} inventory items from mock data`);
} catch (error) {
  logger.error('Error loading inventoryData:', error);
}

try {
  mockPastures = require('../src/mocks/pastureMgmtData').mockPastures || [];
  logger.info(`Loaded ${mockPastures.length} pastures from mock data`);
} catch (error) {
  logger.error('Error loading pastureMgmtData:', error);
}

try {
  mockExpenses = require('../src/mocks/financialData').mockExpenses || [];
  logger.info(`Loaded ${mockExpenses.length} expenses from mock data`);
} catch (error) {
  logger.error('Error loading financialData:', error);
}

// Load reports data
try {
  // Create a path to the reports data script
  const reportsDataPath = path.join(__dirname, 'populate-reports-data.js');

  // Check if the file exists
  if (fs.existsSync(reportsDataPath)) {
    // Import the sample reports from the script
    const sampleReports = [
      {
        id: 'REP001',
        title: 'Monthly Livestock Status Report',
        type: 'livestock',
        module: 'animals',
        date: new Date('2024-02-15'),
        status: 'generated',
        format: 'pdf',
        size: '2.5 MB',
        author: 'System',
        description: 'Comprehensive overview of livestock status including health, breeding, and inventory',
        downloadUrl: '/reports/REP001.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2017/12/Nguni-cattle-herd.jpg',
        tags: ['livestock', 'monthly', 'status'],
        viewCount: 42,
        downloadCount: 15,
        isShared: true,
        parameters: {
          dateRange: { start: '2024-01-01', end: '2024-01-31' },
          includeHealthMetrics: true,
          includeBreedingMetrics: true
        }
      },
      {
        id: 'REP002',
        title: 'Q1 Financial Summary',
        type: 'financial',
        module: 'financial',
        date: new Date('2024-01-31'),
        status: 'generated',
        format: 'excel',
        size: '1.8 MB',
        author: 'System',
        description: 'Financial performance summary for Q1 including revenue, expenses, and profit analysis',
        downloadUrl: '/reports/REP002.xlsx',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2020/01/money-farm.jpg',
        tags: ['financial', 'quarterly', 'summary'],
        viewCount: 35,
        downloadCount: 20,
        isShared: true,
        parameters: {
          dateRange: { start: '2024-01-01', end: '2024-03-31' },
          includeRevenueBreakdown: true,
          includeExpenseBreakdown: true
        }
      },
      {
        id: 'REP003',
        title: 'Breeding Performance Analysis',
        type: 'breeding',
        module: 'breeding',
        date: new Date('2024-02-10'),
        status: 'generated',
        format: 'pdf',
        size: '3.2 MB',
        author: 'System',
        description: 'Analysis of breeding program performance including success rates, genetic improvements, and recommendations',
        downloadUrl: '/reports/REP003.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2018/10/cows-calves.jpg',
        tags: ['breeding', 'performance', 'analysis'],
        viewCount: 28,
        downloadCount: 12,
        isShared: false,
        parameters: {
          dateRange: { start: '2023-01-01', end: '2024-01-31' },
          includeGeneticAnalysis: true,
          includeRecommendations: true
        }
      },
      {
        id: 'REP004',
        title: 'Feed Consumption Trends',
        type: 'feeding',
        module: 'feed-management',
        date: new Date('2024-02-05'),
        status: 'generated',
        format: 'pdf',
        size: '2.1 MB',
        author: 'System',
        description: 'Analysis of feed consumption patterns, costs, and efficiency metrics',
        downloadUrl: '/reports/REP004.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/05/cattle-feeding.jpg',
        tags: ['feeding', 'consumption', 'trends'],
        viewCount: 19,
        downloadCount: 8,
        isShared: false,
        parameters: {
          dateRange: { start: '2023-07-01', end: '2024-01-31' },
          includeCostAnalysis: true,
          includeEfficiencyMetrics: true
        }
      },
      {
        id: 'REP005',
        title: 'Health Inspection Summary',
        type: 'health',
        module: 'health',
        date: new Date('2024-02-20'),
        status: 'generated',
        format: 'pdf',
        size: '1.5 MB',
        author: 'System',
        description: 'Summary of health inspections, vaccinations, and treatment outcomes',
        downloadUrl: '/reports/REP005.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2018/05/vet-cattle.jpg',
        tags: ['health', 'inspection', 'summary'],
        viewCount: 31,
        downloadCount: 14,
        isShared: true,
        parameters: {
          dateRange: { start: '2024-01-01', end: '2024-02-15' },
          includeVaccinationRecords: true,
          includeTreatmentOutcomes: true
        }
      },
      {
        id: 'REP006',
        title: 'Market Price Analysis',
        type: 'market',
        module: 'commercial',
        date: new Date('2024-02-25'),
        status: 'generated',
        format: 'excel',
        size: '2.3 MB',
        author: 'System',
        description: 'Analysis of market prices for livestock and agricultural products',
        downloadUrl: '/reports/REP006.xlsx',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/08/auction.jpg',
        tags: ['market', 'price', 'analysis'],
        viewCount: 27,
        downloadCount: 18,
        isShared: true,
        parameters: {
          dateRange: { start: '2023-01-01', end: '2024-02-15' },
          includePriceTrends: true,
          includeMarketForecasts: true
        }
      },
      {
        id: 'REP007',
        title: 'Compliance Audit Report',
        type: 'compliance',
        module: 'compliance',
        date: new Date('2024-01-15'),
        status: 'generated',
        format: 'pdf',
        size: '4.1 MB',
        author: 'System',
        description: 'Comprehensive audit of compliance with agricultural regulations and standards',
        downloadUrl: '/reports/REP007.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2020/03/compliance.jpg',
        tags: ['compliance', 'audit', 'regulations'],
        viewCount: 15,
        downloadCount: 10,
        isShared: false,
        parameters: {
          auditDate: '2024-01-10',
          includeRegulatoryCompliance: true,
          includeRecommendations: true
        }
      },
      {
        id: 'REP008',
        title: 'Custom Productivity Report',
        type: 'custom',
        module: 'reports',
        date: new Date('2024-02-28'),
        status: 'generated',
        format: 'pdf',
        size: '2.8 MB',
        author: 'Admin',
        description: 'Custom report on farm productivity metrics and efficiency',
        downloadUrl: '/reports/REP008.pdf',
        thumbnail: 'https://www.farmersweekly.co.za/wp-content/uploads/2019/12/farm-productivity.jpg',
        tags: ['custom', 'productivity', 'efficiency'],
        viewCount: 8,
        downloadCount: 3,
        isShared: false,
        parameters: {
          dateRange: { start: '2023-01-01', end: '2024-02-15' },
          customMetrics: ['labor_efficiency', 'land_productivity', 'resource_utilization'],
          includeRecommendations: true
        }
      }
    ];

    mockReports = sampleReports;
    logger.info(`Loaded ${mockReports.length} reports from sample data`);
  } else {
    logger.warn('Reports data script not found');
  }
} catch (error) {
  logger.error('Error loading reports data:', error);
}

// Create sample users if none exist
mockUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'admin123'
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 2,
    username: 'manager',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'manager123'
    firstName: 'Farm',
    lastName: 'Manager',
    role: 'manager',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: 3,
    username: 'worker',
    email: '<EMAIL>',
    password: '$2a$10$yfIJGdT1XzrXnGn2wQUUEuQEYWzQOQrD/1wd1eFthK5HMtL.FwNHO', // hashed 'worker123'
    firstName: 'Farm',
    lastName: 'Worker',
    role: 'worker',
    isActive: true,
    lastLogin: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'AMPD Live Stock';

// Create a MongoClient with a MongoClientOptions object to set the Stable API version
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  },
  ssl: true,
  tlsAllowInvalidCertificates: true, // For development only
  tlsAllowInvalidHostnames: true, // For development only
  useNewUrlParser: true,
  useUnifiedTopology: true,
  connectTimeoutMS: 30000, // 30 seconds timeout
  socketTimeoutMS: 45000, // 45 seconds timeout
});

// Enhance mock data with additional fields
function enhanceData(data, collectionName) {
  return data.map(item => {
    // Convert string dates to Date objects
    const enhancedItem = { ...item };

    // Add timestamps if they don't exist
    if (!enhancedItem.createdAt) {
      enhancedItem.createdAt = new Date();
    } else if (typeof enhancedItem.createdAt === 'string') {
      enhancedItem.createdAt = new Date(enhancedItem.createdAt);
    }

    if (!enhancedItem.updatedAt) {
      enhancedItem.updatedAt = new Date();
    } else if (typeof enhancedItem.updatedAt === 'string') {
      enhancedItem.updatedAt = new Date(enhancedItem.updatedAt);
    }

    // Convert date fields to Date objects
    if (enhancedItem.date && typeof enhancedItem.date === 'string') {
      enhancedItem.date = new Date(enhancedItem.date);
    }

    if (enhancedItem.dateOfBirth && typeof enhancedItem.dateOfBirth === 'string') {
      enhancedItem.dateOfBirth = new Date(enhancedItem.dateOfBirth);
    }

    if (enhancedItem.birthDate && typeof enhancedItem.birthDate === 'string') {
      enhancedItem.birthDate = new Date(enhancedItem.birthDate);
    }

    if (enhancedItem.purchaseDate && typeof enhancedItem.purchaseDate === 'string') {
      enhancedItem.purchaseDate = new Date(enhancedItem.purchaseDate);
    }

    if (enhancedItem.breedingDate && typeof enhancedItem.breedingDate === 'string') {
      enhancedItem.breedingDate = new Date(enhancedItem.breedingDate);
    }

    if (enhancedItem.calvingDate && typeof enhancedItem.calvingDate === 'string') {
      enhancedItem.calvingDate = new Date(enhancedItem.calvingDate);
    }

    if (enhancedItem.expirationDate && typeof enhancedItem.expirationDate === 'string') {
      enhancedItem.expirationDate = new Date(enhancedItem.expirationDate);
    }

    // Collection-specific enhancements
    if (collectionName === 'animals') {
      // Add RFID tag if it doesn't exist
      if (!enhancedItem.rfidTag) {
        enhancedItem.rfidTag = `RFID${enhancedItem.id.toString().padStart(5, '0')}`;
      }

      // Add image URL if it doesn't exist
      if (!enhancedItem.imageUrl) {
        const species = enhancedItem.species || enhancedItem.type || 'Cattle';
        enhancedItem.imageUrl = `/images/animals/${species.toLowerCase()}.jpg`;
      }
    }

    return enhancedItem;
  });
}

// Generate additional data for collections with insufficient mock data
function generateAdditionalData(collectionName, count) {
  const data = [];

  switch (collectionName) {
    case 'users':
      // Additional users are already created above
      return [];

    case 'animals':
      for (let i = mockAnimals.length + 1; i <= count; i++) {
        const species = ['Cattle', 'Sheep', 'Goat', 'Horse', 'Game'][Math.floor(Math.random() * 5)];
        const gender = ['Male', 'Female'][Math.floor(Math.random() * 2)];
        const status = ['Active', 'Sold', 'Deceased'][Math.floor(Math.random() * 3)];
        const healthStatus = ['Healthy', 'Sick', 'Injured', 'Pregnant'][Math.floor(Math.random() * 4)];

        data.push({
          id: i,
          tag: `A${i.toString().padStart(3, '0')}`,
          name: `Animal ${i}`,
          species,
          breed: `${species} Breed`,
          gender,
          dateOfBirth: new Date(2018 + Math.floor(Math.random() * 5), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          status,
          location: `Location ${Math.floor(Math.random() * 5) + 1}`,
          weight: Math.floor(Math.random() * 500) + 100,
          purchaseDate: new Date(2020 + Math.floor(Math.random() * 3), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          purchasePrice: Math.floor(Math.random() * 10000) + 5000,
          healthStatus,
          rfidTag: `RFID${i.toString().padStart(5, '0')}`,
          notes: `${gender} ${species} in ${status} status`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      return data;

    case 'health_records':
      for (let i = mockHealthRecords.length + 1; i <= count; i++) {
        const animalId = Math.floor(Math.random() * mockAnimals.length) + 1;
        const type = ['checkup', 'treatment', 'vaccination'][Math.floor(Math.random() * 3)];
        const status = ['completed', 'scheduled', 'cancelled'][Math.floor(Math.random() * 3)];

        data.push({
          id: `HR${i.toString().padStart(3, '0')}`,
          animalId,
          date: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          type,
          description: `${type} for animal #${animalId}`,
          performedBy: `Dr. ${['Smith', 'Johnson', 'Williams', 'Brown', 'Jones'][Math.floor(Math.random() * 5)]}`,
          cost: Math.floor(Math.random() * 200) + 50,
          notes: `Routine ${type} with no complications`,
          status,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      return data;

    case 'breeding_records':
      for (let i = mockBreedingRecords.length + 1; i <= count; i++) {
        const femaleId = Math.floor(Math.random() * mockAnimals.length) + 1;
        const maleId = Math.floor(Math.random() * mockAnimals.length) + 1;
        const breedingDate = new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
        const status = ['Confirmed', 'Pending', 'Failed'][Math.floor(Math.random() * 3)];

        data.push({
          id: i,
          femaleId,
          maleId,
          breedingDate,
          method: ['Natural', 'AI'][Math.floor(Math.random() * 2)],
          status,
          confirmationDate: status === 'Confirmed' ? new Date(breedingDate.getTime() + (Math.floor(Math.random() * 30) + 15) * 24 * 60 * 60 * 1000) : null,
          notes: `Breeding attempt between animals #${femaleId} and #${maleId}`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      return data;

    case 'feeding_records':
      for (let i = mockFeedingRecords.length + 1; i <= count; i++) {
        const feedType = ['Hay', 'Grain', 'Silage', 'Concentrate', 'Supplement'][Math.floor(Math.random() * 5)];
        const location = ['Pasture A', 'Barn 1', 'Barn 2', 'Feedlot', 'Pen 3'][Math.floor(Math.random() * 5)];
        const quantity = Math.floor(Math.random() * 500) + 100;

        data.push({
          id: i,
          date: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          feedType,
          quantity,
          unit: ['kg', 'lbs', 'bales'][Math.floor(Math.random() * 3)],
          location,
          animals: Array.from({ length: Math.floor(Math.random() * 5) + 1 }, () => Math.floor(Math.random() * mockAnimals.length) + 1),
          cost: Math.floor(Math.random() * 200) + 50,
          notes: `${feedType} feeding in ${location}`,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      return data;

    case 'inventory':
      for (let i = mockInventory.length + 1; i <= count; i++) {
        const category = ['Equipment', 'Supplies', 'Medicine', 'Feed', 'Tools'][Math.floor(Math.random() * 5)];
        const condition = ['New', 'Good', 'Fair', 'Poor'][Math.floor(Math.random() * 4)];

        data.push({
          id: i,
          category,
          name: `${category} Item ${i}`,
          description: `Description for ${category} item ${i}`,
          quantity: Math.floor(Math.random() * 100) + 1,
          location: ['Storage Room', 'Barn', 'Shed', 'Office', 'Medicine Cabinet'][Math.floor(Math.random() * 5)],
          purchaseDate: new Date(2022, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          purchasePrice: Math.floor(Math.random() * 1000) + 100,
          condition,
          lastMaintenanceDate: category === 'Equipment' ? new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1) : null,
          nextMaintenanceDate: category === 'Equipment' ? new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1) : null,
          expirationDate: category === 'Medicine' ? new Date(2025, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1) : null,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      return data;

    default:
      return [];
  }
}

// Main function to populate MongoDB
async function populateDatabase() {
  try {
    logger.info('Connecting to MongoDB...');
    await client.connect();
    logger.info('Connected to MongoDB');

    const db = client.db(dbName);

    // Define collections and their data
    const collections = [
      { name: 'users', data: mockUsers, minCount: 3 },
      { name: 'animals', data: mockAnimals, minCount: 50 },
      { name: 'health_records', data: mockHealthRecords, minCount: 100 },
      { name: 'vaccinations', data: mockVaccinations, minCount: 50 },
      { name: 'breeding_records', data: mockBreedingRecords, minCount: 30 },
      { name: 'calving_records', data: mockCalvingRecords, minCount: 20 },
      { name: 'feeding_records', data: mockFeedingRecords, minCount: 100 },
      { name: 'feed_inventory', data: mockFeedInventory, minCount: 20 },
      { name: 'inventory', data: mockInventory, minCount: 50 },
      { name: 'pastures', data: mockPastures, minCount: 10 },
      { name: 'expenses', data: mockExpenses, minCount: 50 },
      { name: 'reports', data: mockReports, minCount: 8 }
    ];

    // Process each collection
    for (const collection of collections) {
      logger.info(`Processing collection: ${collection.name}`);

      // Check if collection exists
      const collectionExists = await db.listCollections({ name: collection.name }).hasNext();
      if (collectionExists) {
        // Drop existing collection
        logger.info(`Dropping existing collection: ${collection.name}`);
        await db.collection(collection.name).drop();
      }

      // Create collection
      logger.info(`Creating collection: ${collection.name}`);
      await db.createCollection(collection.name);

      // Enhance existing data
      let enhancedData = enhanceData(collection.data, collection.name);

      // Generate additional data if needed
      if (enhancedData.length < collection.minCount) {
        logger.info(`Generating additional data for ${collection.name}: ${collection.minCount - enhancedData.length} records`);
        const additionalData = generateAdditionalData(collection.name, collection.minCount);
        enhancedData = [...enhancedData, ...additionalData];
      }

      // Insert data
      if (enhancedData.length > 0) {
        logger.info(`Inserting ${enhancedData.length} records into ${collection.name}`);
        await db.collection(collection.name).insertMany(enhancedData);
      } else {
        logger.warn(`No data to insert into ${collection.name}`);
      }
    }

    logger.info('Database population completed successfully');
  } catch (error) {
    logger.error('Error populating database:', error);
  } finally {
    await client.close();
    logger.info('MongoDB connection closed');
  }
}

// Run the population script
populateDatabase().catch(console.error);
