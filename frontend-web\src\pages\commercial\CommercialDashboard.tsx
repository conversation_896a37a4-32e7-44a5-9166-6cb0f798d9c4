import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, Box, IconButton, Avatar, LinearProgress, useTheme, Chip, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Dialog, DialogTitle, DialogContent, DialogActions, Alert, Snackbar,  } from '@mui/material';
import CustomButton from '../../components/common/CustomButton';
// Import all icons from our utility
import {
  Add,
  TrendingUp,
  Search,
  FilterList,
  MoreVert,
  Edit,
  Delete,
  CheckCircle,
  Warning,
  ShoppingCart,
  Store,
  Gavel,
  GetApp
} from '../../utils/iconImports';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, AreaChart, Area, CartesianGrid, Legend } from 'recharts';
import { EnhancedPieLabelRenderProps } from '../../types/recharts';
import { useCommercialData } from '../../hooks/useCommercialData';
import OrderModal from '../../components/commercial/OrderModal';
import SupplierModal from '../../components/commercial/SupplierModal';
import AuctionModal from '../../components/commercial/AuctionModal';
import { CommercialOrder, Supplier, Auction } from '../../types/commercial';
import { ModuleHeader, ModuleContainer, ModuleHeaderCard, StandardDashboard, ModernChart } from '../../components/common';

const CommercialDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const {
    orders,
    suppliers,
    auctions,
    stats,
    loading,
    error,
    addOrder,
    updateOrder,
    deleteOrder,
    addSupplier,
    updateSupplier,
    deleteSupplier,
    addAuction,
    updateAuction,
    deleteAuction,
    generateExcelReport
  } = useCommercialData();

  // State for modals
  const [orderModalOpen, setOrderModalOpen] = useState(false);
  const [supplierModalOpen, setSupplierModalOpen] = useState(false);
  const [auctionModalOpen, setAuctionModalOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<CommercialOrder | null>(null);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [selectedAuction, setSelectedAuction] = useState<Auction | null>(null);

  // State for alerts
  const [alert, setAlert] = useState<{open: boolean, message: string, severity: 'success' | 'error' | 'info' | 'warning'}>({
    open: false,
    message: '',
    severity: 'info'
  });

  // State for chart interactions
  const [selectedChart, setSelectedChart] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  // Handle modal actions
  const handleOrderModalOpen = (order: CommercialOrder | null = null) => {
    setSelectedOrder(order);
    setOrderModalOpen(true);
  };

  const handleSupplierModalOpen = (supplier: Supplier | null = null) => {
    setSelectedSupplier(supplier);
    setSupplierModalOpen(true);
  };

  const handleAuctionModalOpen = (auction: Auction | null = null) => {
    setSelectedAuction(auction);
    setAuctionModalOpen(true);
  };

  const [actionLoading, setActionLoading] = useState(false);

  const handleOrderSave = async (order: Omit<CommercialOrder, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      setActionLoading(true);
      if ('id' in order && order.id) {
        // For existing orders, we need to update
        const { id, ...orderData } = order as CommercialOrder;
        await updateOrder(id, orderData);
        setAlert({
          open: true,
          message: 'Order updated successfully',
          severity: 'success'
        });
      } else {
        // For new orders
        await addOrder(order);
        setAlert({
          open: true,
          message: 'Order added successfully',
          severity: 'success'
        });
      }
      setOrderModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error saving order',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleSupplierSave = async (supplier: Omit<Supplier, 'id'>) => {
    try {
      setActionLoading(true);
      if ('id' in supplier && supplier.id) {
        // For existing suppliers, we need to update
        const { id, ...supplierData } = supplier as Supplier;
        await updateSupplier(id, supplierData);
        setAlert({
          open: true,
          message: 'Supplier updated successfully',
          severity: 'success'
        });
      } else {
        // For new suppliers
        await addSupplier(supplier);
        setAlert({
          open: true,
          message: 'Supplier added successfully',
          severity: 'success'
        });
      }
      setSupplierModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error saving supplier',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAuctionSave = async (auction: Omit<Auction, 'id'>) => {
    try {
      setActionLoading(true);
      if ('id' in auction && auction.id) {
        // For existing auctions, we need to update
        const { id, ...auctionData } = auction as Auction;
        await updateAuction(id, auctionData);
        setAlert({
          open: true,
          message: 'Auction updated successfully',
          severity: 'success'
        });
      } else {
        // For new auctions
        await addAuction(auction);
        setAlert({
          open: true,
          message: 'Auction added successfully',
          severity: 'success'
        });
      }
      setAuctionModalOpen(false);
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error saving auction',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleOrderDelete = async (orderId: string) => {
    try {
      setActionLoading(true);
      await deleteOrder(orderId);
      setAlert({
        open: true,
        message: 'Order deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error deleting order',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleSupplierDelete = async (supplierId: string) => {
    try {
      setActionLoading(true);
      await deleteSupplier(supplierId);
      setAlert({
        open: true,
        message: 'Supplier deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error deleting supplier',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAuctionDelete = async (auctionId: string) => {
    try {
      setActionLoading(true);
      await deleteAuction(auctionId);
      setAlert({
        open: true,
        message: 'Auction deleted successfully',
        severity: 'success'
      });
    } catch (error) {
      setAlert({
        open: true,
        message: 'Error deleting auction',
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleGenerateReport = async (reportType: 'orders' | 'suppliers' | 'auctions') => {
    try {
      setActionLoading(true);
      const reportUrl = await generateExcelReport(reportType);
      setAlert({
        open: true,
        message: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully`,
        severity: 'success'
      });
      // In a real app, you would download the file here
      console.log(`Report URL: ${reportUrl}`);
    } catch (error) {
      setAlert({
        open: true,
        message: `Error generating ${reportType} report`,
        severity: 'error'
      });
    } finally {
      setActionLoading(false);
    }
  };

  const handleAlertClose = () => {
    setAlert({...alert, open: false});
  };

  // Prepare data for order status chart
  const orderStatusData = stats.ordersByStatus.map(item => ({
    name: item.status.charAt(0).toUpperCase() + item.status.slice(1),
    value: item.count
  }));

  // Prepare data for revenue chart
  const revenueData = stats.revenueByMonth;

  // Prepare data for top products chart
  const topProductsData = stats.topSellingProducts.map(product => ({
    name: product.productName,
    value: product.revenue
  }));

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <Typography variant="h6" color="textSecondary">Loading MayCaiphus commercial data...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={4}>
        <Alert severity="error">Error loading commercial data. Please try again later.</Alert>
      </Box>
    );
  }

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: 'Total Orders',
      value: stats.totalOrders,
      icon: <ShoppingCart />,
      color: theme.palette.primary.main,
      trend: {
        value: 8,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Monthly Revenue',
      value: `R${stats.monthlyRevenue.toLocaleString()}`,
      icon: <TrendingUp />,
      color: theme.palette.success.main,
      trend: {
        value: 12,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Active Suppliers',
      value: stats.activeSuppliers,
      icon: <Store />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: 'Pending Auctions',
      value: stats.pendingAuctions,
      icon: <Gavel />,
      color: theme.palette.info.main,
      trend: {
        value: 2,
        isPositive: false,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: 'New Order',
      icon: <Add />,
      onClick: () => handleOrderModalOpen(),
      color: 'primary'
    }
  ];

  return (
    <StandardDashboard
      title="Commercial Dashboard"
      subtitle="Manage MayCaiphus orders, suppliers, auctions, and marketplace activities"
      icon={<ShoppingCart />}
      stats={dashboardStats}
      actions={dashboardActions}
      isLoading={loading || actionLoading}
      loadingMessage="Loading MayCaiphus commercial data..."
      onRefresh={() => console.log('Refreshing commercial data')}
      module="commercial"
    >

      <Box sx={{ px: 3, pb: 5 }}>
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <ShoppingCart />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Total Orders
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.totalOrders}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={100}
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Monthly Revenue
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      R{stats.monthlyRevenue.toLocaleString()}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={80}
                    color="success"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                    <Store />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Active Suppliers
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.activeSuppliers}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={65}
                    color="warning"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
              <CardContent>
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <Gavel />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      Pending Auctions
                    </Typography>
                    <Typography variant="h4" fontWeight="bold">
                      {stats.pendingAuctions}
                    </Typography>
                  </Box>
                </Box>
                <Box mt={2}>
                  <LinearProgress
                    variant="determinate"
                    value={40}
                    color="info"
                    sx={{ height: 8, borderRadius: 4 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Charts Section */}
      <Grid container spacing={3} mb={4} sx={{ '& .recharts-responsive-container': { minHeight: { xs: 200, sm: 250, md: 300 } } }}>
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
          >
            <ModernChart
              title="Orders by Status"
              subtitle="Distribution of orders by their current status"
              data={orderStatusData}
              type="pie"
              dataKeys={['value']}
              height={350}
              accentColor={theme.palette.primary.main}
              allowChartTypeChange={true}
              module="commercial"
              tooltip="Shows the distribution of orders across different statuses"
              formatValue={(value) => `${value} orders`}
            />
          </motion.div>
        </Grid>

        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.6 }}
          >
            <ModernChart
              title="Top Selling Products"
              subtitle="Products with the highest revenue"
              data={topProductsData}
              type="bar"
              dataKeys={['value']}
              xAxisDataKey="name"
              height={350}
              accentColor={theme.palette.secondary.main}
              allowChartTypeChange={true}
              module="commercial"
              tooltip="Shows the top selling products by revenue"
              formatValue={(value) => `R${value.toLocaleString()}`}
              layout="vertical"
            />
          </motion.div>
        </Grid>

        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.7 }}
          >
            <ModernChart
              title="Monthly Revenue"
              subtitle="Revenue trends over time"
              data={revenueData}
              type="area"
              dataKeys={['revenue']}
              xAxisDataKey="month"
              height={350}
              accentColor={theme.palette.info.main}
              allowChartTypeChange={true}
              allowTimeRangeChange={true}
              module="commercial"
              tooltip="Shows revenue trends by month"
              formatValue={(value) => `R${value.toLocaleString()}`}
            />
          </motion.div>
        </Grid>
      </Grid>

      {/* Recent Orders */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', mb: 4, overflow: 'hidden' }}>
          <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              Recent Orders
            </Typography>
            <Box>
              <IconButton color="inherit" size="small" onClick={() => handleGenerateReport('orders')}>
                <GetApp />
              </IconButton>
              <IconButton color="inherit" size="small">
                <Search />
              </IconButton>
              <IconButton color="inherit" size="small">
                <FilterList />
              </IconButton>
            </Box>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Order #</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {orders.slice(0, 5).map((order) => (
                  <TableRow key={order.id} hover>
                    <TableCell>{order.orderNumber}</TableCell>
                    <TableCell>{order.customer}</TableCell>
                    <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                    <TableCell>R{order.totalAmount.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        color={
                          order.status === 'delivered' ? 'success' :
                          order.status === 'processing' ? 'primary' :
                          order.status === 'pending' ? 'warning' :
                          order.status === 'cancelled' ? 'error' : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" onClick={() => handleOrderModalOpen(order)}>
                        <Edit fontSize="small" />
                      </IconButton>
                      <IconButton size="small" onClick={() => handleOrderDelete(order.id)}>
                        <Delete fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <CustomButton
              variant="outlined"
              color="primary"
              onClick={() => navigate('/dashboard/commercial/orders')}
            >
              View All Orders
            </CustomButton>
          </Box>
        </Card>
      </motion.div>

      {/* Suppliers and Auctions */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
              <Box sx={{ p: 2, bgcolor: 'secondary.main', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" fontWeight="bold">
                  Active Suppliers
                </Typography>
                <Box>
                  <IconButton color="inherit" size="small" onClick={() => handleGenerateReport('suppliers')}>
                    <GetApp />
                  </IconButton>
                  <IconButton color="inherit" size="small" onClick={() => handleSupplierModalOpen()}>
                    <Add />
                  </IconButton>
                </Box>
              </Box>
              <TableContainer sx={{ maxHeight: 300 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Name</TableCell>
                      <TableCell>Contact</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {suppliers.slice(0, 5).map((supplier) => (
                      <TableRow key={supplier.id} hover>
                        <TableCell>{supplier.name}</TableCell>
                        <TableCell>{supplier.contactPerson}</TableCell>
                        <TableCell>
                          <Chip
                            label={supplier.status}
                            color={supplier.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton size="small" onClick={() => handleSupplierModalOpen(supplier)}>
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleSupplierDelete(supplier.id)}>
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <CustomButton
                  variant="outlined"
                  color="secondary"
                  onClick={() => navigate('/dashboard/commercial/suppliers')}
                >
                  View All Suppliers
                </CustomButton>
              </Box>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={6}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
          >
            <Card sx={{ borderRadius: '12px', boxShadow: '0 4px 20px rgba(0,0,0,0.08)', height: '100%' }}>
              <Box sx={{ p: 2, bgcolor: 'warning.main', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="h6" fontWeight="bold">
                  Upcoming Auctions
                </Typography>
                <Box>
                  <IconButton color="inherit" size="small" onClick={() => handleGenerateReport('auctions')}>
                    <GetApp />
                  </IconButton>
                  <IconButton color="inherit" size="small" onClick={() => handleAuctionModalOpen()}>
                    <Add />
                  </IconButton>
                </Box>
              </Box>
              <TableContainer sx={{ maxHeight: 300 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {auctions.slice(0, 5).map((auction) => (
                      <TableRow key={auction.id} hover>
                        <TableCell>{auction.title}</TableCell>
                        <TableCell>{new Date(auction.date).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Chip
                            label={auction.status}
                            color={
                              auction.status === 'active' ? 'success' :
                              auction.status === 'upcoming' ? 'primary' :
                              auction.status === 'completed' ? 'default' :
                              'error'
                            }
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton size="small" onClick={() => handleAuctionModalOpen(auction)}>
                            <Edit fontSize="small" />
                          </IconButton>
                          <IconButton size="small" onClick={() => handleAuctionDelete(auction.id)}>
                            <Delete fontSize="small" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box sx={{ p: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <CustomButton
                  variant="outlined"
                  color="warning"
                  onClick={() => navigate('/dashboard/commercial/auctions')}
                >
                  View All Auctions
                </CustomButton>
              </Box>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Modals */}
      <OrderModal
        open={orderModalOpen}
        onClose={() => setOrderModalOpen(false)}
        onSave={handleOrderSave}
        order={selectedOrder}
        loading={actionLoading}
      />

      <SupplierModal
        open={supplierModalOpen}
        onClose={() => setSupplierModalOpen(false)}
        onSave={handleSupplierSave}
        supplier={selectedSupplier}
        loading={actionLoading}
      />

      <AuctionModal
        open={auctionModalOpen}
        onClose={() => setAuctionModalOpen(false)}
        onSave={handleAuctionSave}
        auction={selectedAuction}
        loading={actionLoading}
      />

      {/* Alert Snackbar */}
      <Snackbar
        open={alert.open}
        autoHideDuration={6000}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleAlertClose} severity={alert.severity} sx={{ width: '100%' }}>
          {alert.message}
        </Alert>
      </Snackbar>
    </Box>
    </StandardDashboard>
  );
};

export default CommercialDashboard;
