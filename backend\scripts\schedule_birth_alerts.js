/**
 * Schedule Birth Alerts
 *
 * This script schedules birth alerts for upcoming births
 * based on the data from the breeding module
 */

const ruralNotificationService = require('../services/ruralNotificationService');

// Birth prediction data with Sotho, Tswana, and Afrikaans names
const birthPredictions = [
  {
    animalId: 'CTL001',
    animalName: 'Tshepiso',
    dueDate: new Date('2024-07-15'),
    daysRemaining: 45,
    riskLevel: 'medium',
    specialCare: [
      'Regular monitoring',
      'Balanced nutrition',
      'Ensure calm environment'
    ]
  },
  {
    animalId: 'CTL002',
    animalName: 'Naledi',
    dueDate: new Date('2024-06-18'),
    daysRemaining: 18,
    riskLevel: 'medium-high',
    specialCare: [
      'Monitor vital signs',
      'Supplemental nutrition',
      'Reduce stress'
    ]
  },
  {
    animalId: 'CTL003',
    animalName: 'Lerato',
    dueDate: new Date('2024-08-18'),
    daysRemaining: 79,
    riskLevel: 'low',
    specialCare: [
      'Regular checkups',
      'Standard care protocol'
    ]
  }
];

/**
 * Schedule birth alerts for all animals
 */
async function scheduleAlerts() {
  console.log('=== Scheduling Birth Alerts ===');
  console.log(`Current time: ${new Date().toLocaleString()}`);
  console.log(`Number of animals: ${birthPredictions.length}`);
  console.log('');

  for (const animal of birthPredictions) {
    console.log(`Processing ${animal.animalName} (${animal.animalId})`);
    console.log(`Due date: ${animal.dueDate.toLocaleDateString()}`);
    console.log(`Days remaining: ${animal.daysRemaining}`);
    console.log(`Risk level: ${animal.riskLevel}`);
    console.log('');

    // Determine notification types based on days remaining
    const notificationTypes = ['sms'];

    if (animal.daysRemaining <= 7) {
      notificationTypes.push('whatsapp');
    }

    if (animal.daysRemaining <= 3) {
      notificationTypes.push('voice');
    }

    // Determine priority based on days remaining
    let priority;
    if (animal.daysRemaining <= 1) {
      priority = 'critical';
    } else if (animal.daysRemaining <= 3) {
      priority = 'high';
    } else if (animal.daysRemaining <= 7) {
      priority = 'medium';
    } else {
      priority = 'low';
    }

    // Schedule the alert
    try {
      const results = await ruralNotificationService.sendBirthPredictionAlert({
        animalId: animal.animalId,
        animalName: animal.animalName,
        daysRemaining: animal.daysRemaining,
        expectedDueDate: animal.dueDate,
        riskLevel: animal.riskLevel,
        specialCare: animal.specialCare,
        notificationTypes,
        priority
      });

      console.log(`Alert scheduled for ${animal.animalName}`);
      console.log('Results:', results);
      console.log('');
    } catch (error) {
      console.error(`Error scheduling alert for ${animal.animalName}:`, error);
    }
  }

  console.log('=== All alerts scheduled ===');
}

// Run the scheduler
scheduleAlerts().catch(console.error);
