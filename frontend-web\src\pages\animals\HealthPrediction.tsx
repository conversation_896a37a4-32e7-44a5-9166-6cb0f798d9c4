import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Typography, Card, CardContent, Grid, Chip, CircularProgress, TextField, MenuItem, FormControl, InputLabel, Select, Divider, Paper, List, ListItem, ListItemIcon, ListItemText, alpha, useTheme } from '@mui/material';
import { 
  LocalHospital, 
  Pets, 
  Warning, 
  CheckCircle, 
  Info,
  TrendingUp,
  TrendingDown,
  Timeline,
  Science,
  HealthAndSafety,
  Medication,
  MonitorHeart,
  Thermostat,
  WaterDrop,
  Scale,
  Restaurant
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../../contexts/LanguageContext';
import { DataCard, ProgressIndicator, StatusChip, GlassCard } from '../../design/DesignSystem';

// Mock data and service functions
const mockAnimals = [
  { id: '1', tagNumber: 'CATTLE-2023-001', name: '<PERSON>', type: 'Cattle', breed: '<PERSON>uni', gender: 'Female', age: 3 },
  { id: '2', tagNumber: 'CATTLE-2023-002', name: '<PERSON>', type: 'Cattle', breed: 'Bonsmara', gender: 'Male', age: 4 },
  { id: '3', tagNumber: 'SHEEP-2023-001', name: 'Daisy', type: 'Sheep', breed: 'Dorper', gender: 'Female', age: 2 },
  { id: '4', tagNumber: 'GOAT-2023-001', name: 'Billy', type: 'Goat', breed: 'Boer', gender: 'Male', age: 1 },
  { id: '5', tagNumber: 'CATTLE-2023-003', name: 'Luna', type: 'Cattle', breed: 'Brahman', gender: 'Female', age: 5 }
];

// Mock health prediction data
interface HealthPredictionData {
  animalId: string;
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high';
  predictionConfidence: number;
  healthTrends: {
    weight: { trend: 'up' | 'down' | 'stable'; value: number };
    temperature: { trend: 'up' | 'down' | 'stable'; value: number };
    hydration: { trend: 'up' | 'down' | 'stable'; value: number };
    appetite: { trend: 'up' | 'down' | 'stable'; value: number };
  };
  riskFactors: Array<{
    factor: string;
    impact: number;
    description: string;
  }>;
  potentialIssues: Array<{
    issue: string;
    probability: number;
    symptoms: string[];
  }>;
  preventiveMeasures: Array<{
    measure: string;
    effectiveness: number;
    description: string;
  }>;
  recommendedActions: string[];
}

// Mock API function to fetch health prediction
const fetchHealthPrediction = (animalId: string): Promise<HealthPredictionData> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Generate mock data based on animal ID
      const riskScore = Math.floor(Math.random() * 100);
      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      
      if (riskScore > 70) {
        riskLevel = 'high';
      } else if (riskScore > 30) {
        riskLevel = 'medium';
      }
      
      resolve({
        animalId,
        riskScore,
        riskLevel,
        predictionConfidence: 75 + Math.floor(Math.random() * 20),
        healthTrends: {
          weight: { 
            trend: Math.random() > 0.5 ? 'up' : 'down', 
            value: Math.floor(Math.random() * 5) 
          },
          temperature: { 
            trend: Math.random() > 0.7 ? 'up' : 'stable', 
            value: Math.floor(Math.random() * 2) 
          },
          hydration: { 
            trend: Math.random() > 0.6 ? 'down' : 'stable', 
            value: Math.floor(Math.random() * 10) 
          },
          appetite: { 
            trend: Math.random() > 0.5 ? 'down' : 'up', 
            value: Math.floor(Math.random() * 15) 
          }
        },
        riskFactors: [
          {
            factor: 'Age',
            impact: Math.floor(Math.random() * 100),
            description: 'Animal age increases susceptibility to certain conditions'
          },
          {
            factor: 'Weight Fluctuation',
            impact: Math.floor(Math.random() * 100),
            description: 'Recent weight changes may indicate underlying issues'
          },
          {
            factor: 'Seasonal Factors',
            impact: Math.floor(Math.random() * 100),
            description: 'Current season increases risk of specific health issues'
          },
          {
            factor: 'Genetic Predisposition',
            impact: Math.floor(Math.random() * 100),
            description: 'Breed-specific health concerns'
          }
        ],
        potentialIssues: [
          {
            issue: 'Respiratory Infection',
            probability: Math.floor(Math.random() * 100),
            symptoms: ['Coughing', 'Nasal discharge', 'Labored breathing']
          },
          {
            issue: 'Digestive Issues',
            probability: Math.floor(Math.random() * 100),
            symptoms: ['Reduced appetite', 'Abnormal stool', 'Bloating']
          },
          {
            issue: 'Parasitic Infection',
            probability: Math.floor(Math.random() * 100),
            symptoms: ['Weight loss', 'Dull coat', 'Lethargy']
          }
        ],
        preventiveMeasures: [
          {
            measure: 'Vaccination',
            effectiveness: Math.floor(Math.random() * 100),
            description: 'Ensure vaccinations are up to date'
          },
          {
            measure: 'Dietary Adjustment',
            effectiveness: Math.floor(Math.random() * 100),
            description: 'Modify diet to address nutritional needs'
          },
          {
            measure: 'Environmental Management',
            effectiveness: Math.floor(Math.random() * 100),
            description: 'Improve housing conditions and reduce stress factors'
          },
          {
            measure: 'Regular Monitoring',
            effectiveness: Math.floor(Math.random() * 100),
            description: 'Increase frequency of health checks and observations'
          }
        ],
        recommendedActions: [
          'Schedule a veterinary check-up within the next 7 days',
          'Monitor temperature daily for the next 2 weeks',
          'Adjust feed composition to increase protein intake',
          'Ensure adequate access to clean water',
          'Isolate from other animals if symptoms worsen'
        ]
      });
    }, 1500);
  });
};

// Component for health prediction chart
interface HealthPredictionChartProps {
  data: HealthPredictionData;
}

const HealthPredictionChart: React.FC<HealthPredictionChartProps> = ({ data }) => {
  const theme = useTheme();
  
  // Get color based on risk level
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };
  
  // Get icon for trend
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp />;
      case 'down':
        return <TrendingDown />;
      default:
        return <Timeline />;
    }
  };
  
  // Get color for trend
  const getTrendColor = (trend: string, isPositive: boolean) => {
    if (trend === 'stable') return theme.palette.info.main;
    return (trend === 'up') === isPositive ? theme.palette.success.main : theme.palette.error.main;
  };
  
  const riskColor = getRiskColor(data.riskLevel);
  
  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Health Risk Assessment
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box sx={{ position: 'relative', width: 120, height: 120 }}>
            <CircularProgress
              variant="determinate"
              value={100}
              size={120}
              thickness={4}
              sx={{ color: alpha(theme.palette.grey[300], 0.3) }}
            />
            <CircularProgress
              variant="determinate"
              value={data.riskScore}
              size={120}
              thickness={4}
              sx={{ 
                color: riskColor,
                position: 'absolute',
                top: 0,
                left: 0
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                bottom: 0,
                right: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column'
              }}
            >
              <Typography variant="h4" fontWeight="bold">
                {data.riskScore}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Risk Score
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ ml: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Risk Level
            </Typography>
            <StatusChip
              label={data.riskLevel.toUpperCase()}
              status={data.riskLevel as 'success' | 'warning' | 'error'}
              sx={{ mb: 2 }}
            />
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Prediction Confidence
            </Typography>
            <Typography variant="body1" fontWeight="medium">
              {data.predictionConfidence}%
            </Typography>
          </Box>
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        <Typography variant="subtitle1" gutterBottom>
          Health Trends
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1
                }}
              >
                <Scale color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Weight
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: getTrendColor(data.healthTrends.weight.trend, true)
                }}
              >
                {getTrendIcon(data.healthTrends.weight.trend)}
                <Typography variant="body1" fontWeight="medium" sx={{ ml: 0.5 }}>
                  {data.healthTrends.weight.value}%
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1
                }}
              >
                <Thermostat color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Temperature
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: getTrendColor(data.healthTrends.temperature.trend, false)
                }}
              >
                {getTrendIcon(data.healthTrends.temperature.trend)}
                <Typography variant="body1" fontWeight="medium" sx={{ ml: 0.5 }}>
                  {data.healthTrends.temperature.value}%
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1
                }}
              >
                <WaterDrop color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Hydration
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: getTrendColor(data.healthTrends.hydration.trend, true)
                }}
              >
                {getTrendIcon(data.healthTrends.hydration.trend)}
                <Typography variant="body1" fontWeight="medium" sx={{ ml: 0.5 }}>
                  {data.healthTrends.hydration.value}%
                </Typography>
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 1
                }}
              >
                <Restaurant color="primary" sx={{ mr: 0.5 }} />
                <Typography variant="body2" color="text.secondary">
                  Appetite
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: getTrendColor(data.healthTrends.appetite.trend, true)
                }}
              >
                {getTrendIcon(data.healthTrends.appetite.trend)}
                <Typography variant="body1" fontWeight="medium" sx={{ ml: 0.5 }}>
                  {data.healthTrends.appetite.value}%
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

// Component for risk factors list
interface RiskFactorsListProps {
  data: HealthPredictionData;
}

const RiskFactorsList: React.FC<RiskFactorsListProps> = ({ data }) => {
  const theme = useTheme();
  
  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Risk Factors
        </Typography>
        
        <List>
          {data.riskFactors.map((factor, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon>
                <Box
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Typography variant="body2" fontWeight="bold" color="primary">
                    {factor.impact}%
                  </Typography>
                </Box>
              </ListItemIcon>
              <ListItemText
                primary={factor.factor}
                secondary={factor.description}
                primaryTypographyProps={{ fontWeight: 'medium' }}
              />
            </ListItem>
          ))}
        </List>
      </CardContent>
    </Card>
  );
};

// Component for potential issues
interface PotentialIssuesCardProps {
  data: HealthPredictionData;
}

const PotentialIssuesCard: React.FC<PotentialIssuesCardProps> = ({ data }) => {
  const theme = useTheme();
  
  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Potential Health Issues
        </Typography>
        
        {data.potentialIssues.map((issue, index) => (
          <Box key={index} sx={{ mb: 3, '&:last-child': { mb: 0 } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="subtitle1" fontWeight="medium">
                {issue.issue}
              </Typography>
              <Chip
                label={`${issue.probability}%`}
                size="small"
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  fontWeight: 'bold'
                }}
              />
            </Box>
            
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Symptoms to watch for:
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {issue.symptoms.map((symptom, i) => (
                <Chip
                  key={i}
                  label={symptom}
                  size="small"
                  sx={{
                    bgcolor: alpha(theme.palette.warning.main, 0.1),
                    color: theme.palette.warning.main
                  }}
                />
              ))}
            </Box>
            
            {index < data.potentialIssues.length - 1 && (
              <Divider sx={{ mt: 2 }} />
            )}
          </Box>
        ))}
      </CardContent>
    </Card>
  );
};

// Component for preventive measures
interface PreventiveMeasuresCardProps {
  data: HealthPredictionData;
}

const PreventiveMeasuresCard: React.FC<PreventiveMeasuresCardProps> = ({ data }) => {
  const theme = useTheme();
  
  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Preventive Measures
        </Typography>
        
        {data.preventiveMeasures.map((measure, index) => (
          <Box key={index} sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: '50%',
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  color: theme.palette.success.main,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 1
                }}
              >
                <CheckCircle fontSize="small" />
              </Box>
              <Typography variant="subtitle2">
                {measure.measure}
              </Typography>
              <Box sx={{ flexGrow: 1 }} />
              <Typography variant="body2" fontWeight="bold" color="success.main">
                {measure.effectiveness}% effective
              </Typography>
            </Box>
            
            <Typography variant="body2" color="text.secondary" sx={{ ml: 5 }}>
              {measure.description}
            </Typography>
            
            {index < data.preventiveMeasures.length - 1 && (
              <Divider sx={{ my: 2 }} />
            )}
          </Box>
        ))}
      </CardContent>
    </Card>
  );
};

// Component for recommended actions
interface RecommendedActionsCardProps {
  data: HealthPredictionData;
}

const RecommendedActionsCard: React.FC<RecommendedActionsCardProps> = ({ data }) => {
  const theme = useTheme();
  
  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Recommended Actions
        </Typography>
        
        <List>
          {data.recommendedActions.map((action, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemIcon>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.info.main, 0.1),
                    color: theme.palette.info.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {index + 1}
                </Box>
              </ListItemIcon>
              <ListItemText
                primary={action}
                primaryTypographyProps={{ variant: 'body1' }}
              />
            </ListItem>
          ))}
        </List>
        
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
          <CustomButton
            variant="contained"
            color="primary"
            startIcon={<LocalHospital />}
            sx={{
              borderRadius: 2,
              px: 3
            }}
          >
            Schedule Veterinary Visit
          </CustomButton>
        </Box>
      </CardContent>
    </Card>
  );
};

// Main Health Prediction Component
const HealthPrediction: React.FC = () => {
  const [selectedAnimal, setSelectedAnimal] = useState<string>('');
  const [predictionData, setPredictionData] = useState<HealthPredictionData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Handle animal selection
  const handleAnimalChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedAnimal(event.target.value as string);
    setPredictionData(null);
    setError(null);
  };
  
  // Generate prediction
  const generatePrediction = async () => {
    if (!selectedAnimal) {
      setError('Please select an animal first');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchHealthPrediction(selectedAnimal);
      setPredictionData(data);
    } catch (err) {
      setError('Failed to generate health prediction. Please try again.');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ maxWidth: 1200, mx: 'auto', p: 2 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            AI Health Prediction
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Use artificial intelligence to predict potential health issues and get preventive recommendations
          </Typography>
        </Box>
        
        {/* Selection and Controls */}
        <GlassCard sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, alignItems: 'center', gap: 2 }}>
            <FormControl fullWidth sx={{ maxWidth: { md: 400 } }}>
              <InputLabel id="animal-select-label">Select Animal</InputLabel>
              <Select
                labelId="animal-select-label"
                value={selectedAnimal}
                onChange={handleAnimalChange as any}
                label="Select Animal"
              >
                <MenuItem value="">
                  <em>Select an animal</em>
                </MenuItem>
                {mockAnimals.map((animal) => (
                  <MenuItem key={animal.id} value={animal.id}>
                    {animal.tagNumber} - {animal.name} ({animal.breed})
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            <CustomButton
              variant="contained"
              color="primary"
              startIcon={<Science />}
              onClick={generatePrediction}
              disabled={!selectedAnimal || isLoading}
              sx={{
                py: 1.5,
                px: 3,
                borderRadius: 2,
                minWidth: 200
              }}
            >
              {isLoading ? 'Analyzing...' : 'Generate Prediction'}
            </CustomButton>
          </Box>
          
          {error && (
            <Typography variant="body2" color="error" sx={{ mt: 2 }}>
              {error}
            </Typography>
          )}
        </GlassCard>
        
        {/* Loading State */}
        {isLoading && (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" sx={{ mt: 2 }}>
              Analyzing health data...
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Our AI is processing historical data and current health indicators
            </Typography>
          </Box>
        )}
        
        {/* Prediction Results */}
        {!isLoading && predictionData && (
          <>
            <Box sx={{ mb: 4 }}>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                Health Prediction Results
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Analysis for {mockAnimals.find(a => a.id === selectedAnimal)?.name} ({mockAnimals.find(a => a.id === selectedAnimal)?.tagNumber})
              </Typography>
            </Box>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <HealthPredictionChart data={predictionData} />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <RiskFactorsList data={predictionData} />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <PotentialIssuesCard data={predictionData} />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <PreventiveMeasuresCard data={predictionData} />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <RecommendedActionsCard data={predictionData} />
              </Grid>
            </Grid>
            
            <Box sx={{ mt: 4, textAlign: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                This prediction is based on AI analysis of historical health data, breed-specific factors, and current health indicators.
                Always consult with a veterinarian for professional medical advice.
              </Typography>
            </Box>
          </>
        )}
        
        {/* Empty State */}
        {!isLoading && !predictionData && !error && (
          <Box sx={{ textAlign: 'center', py: 8, opacity: 0.7 }}>
            <HealthAndSafety sx={{ fontSize: 80, color: alpha(theme.palette.primary.main, 0.3), mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              Select an animal and generate a prediction to see health insights
            </Typography>
          </Box>
        )}
      </Box>
    </motion.div>
  );
};

export default HealthPrediction;
