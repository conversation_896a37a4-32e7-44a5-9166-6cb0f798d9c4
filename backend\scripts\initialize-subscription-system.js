/**
 * Initialize Subscription System
 * 
 * This script sets up the subscription system with default plans and configurations
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Import subscription service
const SubscriptionService = require('../src/services/subscriptionService');

// MongoDB connection
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ampd_livestock';
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Initialize subscription system
const initializeSubscriptionSystem = async () => {
  try {
    console.log('🚀 Initializing AMPD Subscription System...\n');

    // Initialize default subscription plans
    console.log('📋 Creating default subscription plans...');
    await SubscriptionService.initializeDefaultPlans();
    console.log('✅ Default subscription plans created successfully\n');

    // Display created plans
    const plans = await SubscriptionService.getAvailablePlans();
    console.log('📊 Available Subscription Plans:');
    console.log('================================');
    
    plans.forEach(plan => {
      console.log(`\n🎯 ${plan.displayName}`);
      console.log(`   Price: R${plan.price.monthly}/month (R${plan.price.yearly}/year)`);
      console.log(`   Max Animals: ${plan.features.maxAnimals === -1 ? 'Unlimited' : plan.features.maxAnimals}`);
      console.log(`   Max Reports: ${plan.features.maxReports === -1 ? 'Unlimited' : plan.features.maxReports}`);
      console.log(`   Max Alerts: ${plan.features.maxAlerts === -1 ? 'Unlimited' : plan.features.maxAlerts}`);
      console.log(`   AI Agent: ${plan.features.aiAgentMode ? '✅' : '❌'}`);
      console.log(`   Advanced Analytics: ${plan.features.advancedAnalytics ? '✅' : '❌'}`);
      console.log(`   Financial Intelligence: ${plan.features.financialIntelligence ? '✅' : '❌'}`);
      console.log(`   Priority Support: ${plan.features.prioritySupport ? '✅' : '❌'}`);
      console.log(`   Data Export: ${plan.features.dataExport.formats.join(', ')}`);
    });

    console.log('\n🎉 Subscription system initialized successfully!');
    console.log('\n📝 Next Steps:');
    console.log('1. Start the backend server: npm start');
    console.log('2. Users will automatically get beta subscriptions when they register');
    console.log('3. Monitor conversion metrics in the admin dashboard');
    console.log('4. Configure payment processing (Stripe) for production');

  } catch (error) {
    console.error('❌ Error initializing subscription system:', error);
    process.exit(1);
  }
};

// Create sample beta user for testing
const createSampleBetaUser = async () => {
  try {
    console.log('\n👤 Creating sample beta user for testing...');
    
    // Import user model
    const User = require('../src/models/schemas/user.schema');
    
    // Check if sample user already exists
    const existingUser = await User.findOne({ username: 'beta_farmer' });
    if (existingUser) {
      console.log('✅ Sample beta user already exists');
      return existingUser;
    }

    // Create sample user
    const sampleUser = new User({
      username: 'beta_farmer',
      email: '<EMAIL>',
      password: 'password123', // Will be hashed by pre-save middleware
      firstName: 'Beta',
      lastName: 'Farmer',
      role: 'user',
      profile: {
        farmName: 'Sample Beta Farm',
        location: {
          province: 'gauteng',
          district: 'johannesburg',
          coordinates: {
            latitude: -26.2041,
            longitude: 28.0473
          }
        },
        farmSize: 'medium',
        livestockTypes: ['cattle', 'sheep'],
        contactInfo: {
          phone: '+27123456789'
        }
      }
    });

    await sampleUser.save();
    console.log('✅ Sample beta user created successfully');

    // Create beta subscription for the user
    console.log('📝 Creating beta subscription for sample user...');
    await SubscriptionService.createBetaSubscription(sampleUser._id);
    console.log('✅ Beta subscription created successfully');

    return sampleUser;
  } catch (error) {
    console.error('❌ Error creating sample beta user:', error);
  }
};

// Display system statistics
const displaySystemStats = async () => {
  try {
    console.log('\n📈 System Statistics:');
    console.log('====================');

    const { SubscriptionPlan, UserSubscription, BetaConversionMetrics } = require('../src/models/schemas/subscription.schema');
    
    const totalPlans = await SubscriptionPlan.countDocuments();
    const totalSubscriptions = await UserSubscription.countDocuments();
    const activeSubscriptions = await UserSubscription.countDocuments({ status: 'active' });
    const trialSubscriptions = await UserSubscription.countDocuments({ status: 'trial' });
    const totalBetaUsers = await BetaConversionMetrics.countDocuments();

    console.log(`📋 Total Plans: ${totalPlans}`);
    console.log(`👥 Total Subscriptions: ${totalSubscriptions}`);
    console.log(`✅ Active Subscriptions: ${activeSubscriptions}`);
    console.log(`🔄 Trial Subscriptions: ${trialSubscriptions}`);
    console.log(`🧪 Beta Users: ${totalBetaUsers}`);

    if (totalBetaUsers > 0) {
      const conversionRate = ((activeSubscriptions / totalBetaUsers) * 100).toFixed(2);
      console.log(`📊 Conversion Rate: ${conversionRate}%`);
    }

  } catch (error) {
    console.error('❌ Error getting system statistics:', error);
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await initializeSubscriptionSystem();
    await createSampleBetaUser();
    await displaySystemStats();
    
    console.log('\n🎯 Beta Strategy Implementation Complete!');
    console.log('=========================================');
    console.log('The AMPD Live Stock Management beta system is now ready.');
    console.log('Users can sign up for free beta accounts and experience');
    console.log('the conversion funnel designed to drive premium subscriptions.');
    
  } catch (error) {
    console.error('❌ Script execution failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n👋 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Handle script termination
process.on('SIGINT', async () => {
  console.log('\n⚠️  Script interrupted by user');
  await mongoose.disconnect();
  process.exit(0);
});

process.on('unhandledRejection', async (error) => {
  console.error('❌ Unhandled rejection:', error);
  await mongoose.disconnect();
  process.exit(1);
});

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  initializeSubscriptionSystem,
  createSampleBetaUser,
  displaySystemStats
};
