/**
 * This patch fixes the "Cannot read properties of undefined (reading 'dark')" error
 * in the MUI Button component by ensuring that the theme.palette.primary.dark property
 * is always available.
 */
import React from 'react';

export const applyMuiButtonPatch = () => {
  try {
    // Create a global patch for the MUI Button component
    const originalCreateElement = React.createElement;

    // @ts-ignore
    React.createElement = function(type: any, props: any, ...children: any[]) {
      // Check if this is a Button component
      if (type && type.name === 'Button' && props && props.theme && props.theme.palette) {
        // Ensure primary.dark exists
        if (props.theme.palette.primary && !props.theme.palette.primary.dark) {
          props.theme.palette.primary.dark = props.theme.palette.primary.main || '#2A8A82';
        }

        // Ensure secondary.dark exists
        if (props.theme.palette.secondary && !props.theme.palette.secondary.dark) {
          props.theme.palette.secondary.dark = props.theme.palette.secondary.main || '#2C8A84';
        }
      }

      return originalCreateElement(type, props, ...children);
    };

    console.log('MUI Button patch applied successfully');
  } catch (error) {
    console.error('Error applying <PERSON><PERSON> Button patch:', error);
  }
};
