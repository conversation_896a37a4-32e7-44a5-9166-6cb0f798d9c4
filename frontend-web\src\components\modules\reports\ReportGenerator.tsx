import React, { useState } from 'react';
import CustomButton from '../../common/CustomButton';
import { Box, Card, CardContent, FormControl, FormControlLabel, FormLabel, Grid, InputLabel, MenuItem, Radio, RadioGroup, Select, TextField, Typography, CircularProgress, Alert, Snackbar, useTheme, alpha } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';
import {
  PictureAsPdf,
  TableChart,
  Code,
  Download,
  BarChart,
  Timeline,
  LocalHospital,
  TrendingUp,
  AttachMoney
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { generateReport, ReportType, ReportFormat, TimePeriod } from '../../../services/reportService';
import { useAnimalData } from '../../../hooks/useAnimalData';
import { useFinancialData } from '../../../hooks/useFinancialData';

interface ReportGeneratorProps {
  defaultType?: ReportType;
}

const ReportGenerator: React.FC<ReportGeneratorProps> = ({ defaultType = 'analysis' }) => {
  const theme = useTheme();
  const { animals, loading: animalsLoading } = useAnimalData();
  const { transactions, stats, loading: financialLoading } = useFinancialData();

  // Form state
  const [reportType, setReportType] = useState<ReportType>(defaultType);
  const [reportFormat, setReportFormat] = useState<ReportFormat>('html');
  const [timePeriod, setTimePeriod] = useState<TimePeriod>('month');
  const [startDate, setStartDate] = useState<Dayjs | null>(dayjs().subtract(1, 'month'));
  const [endDate, setEndDate] = useState<Dayjs | null>(dayjs());
  const [includeCharts, setIncludeCharts] = useState<boolean>(true);
  const [includeSummary, setIncludeSummary] = useState<boolean>(true);

  // UI state
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const handleGenerateReport = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate dates for custom time period
      if (timePeriod === 'custom') {
        if (!startDate || !endDate) {
          throw new Error('Please select both start and end dates');
        }

        if (endDate.isBefore(startDate)) {
          throw new Error('End date must be after start date');
        }
      }

      // Prepare filters
      const filters: Record<string, any> = {
        includeCharts,
        includeSummary,
        currency: 'ZAR'
      };

      // Add specific data based on report type
      if (reportType === 'financial') {
        filters.transactions = transactions;
        filters.stats = stats;
      }

      // Generate the report
      await generateReport({
        type: reportType,
        format: reportFormat,
        timePeriod,
        startDate: startDate?.format('YYYY-MM-DD'),
        endDate: endDate?.format('YYYY-MM-DD'),
        filters
      });

      setSuccess(true);
    } catch (err) {
      console.error('Error generating report:', err);
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setLoading(false);
    }
  };

  const getReportTypeIcon = (type: ReportType) => {
    switch (type) {
      case 'analysis': return <BarChart />;
      case 'performance': return <Timeline />;
      case 'health': return <LocalHospital />;
      case 'market': return <TrendingUp />;
      case 'financial': return <AttachMoney />;
      case 'custom': return <BarChart />;
      default: return <BarChart />;
    }
  };

  const getReportFormatIcon = (format: ReportFormat) => {
    switch (format) {
      case 'pdf': return <PictureAsPdf />;
      case 'excel': return <TableChart />;
      case 'csv': return <TableChart />;
      case 'html': return <Code />;
      default: return <PictureAsPdf />;
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card
          sx={{
            mb: 4,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
          }}
        >
          <CardContent>
            <Typography variant="h5" gutterBottom>
              Generate Report
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Create custom reports with real-time data from your livestock management system.
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Grid container spacing={3}>
              {/* Report Type */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Report Type</InputLabel>
                  <Select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value as ReportType)}
                    label="Report Type"
                    disabled={loading}
                  >
                    <MenuItem value="analysis">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BarChart sx={{ mr: 1, color: 'primary.main' }} />
                        Analysis Report
                      </Box>
                    </MenuItem>
                    <MenuItem value="performance">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Timeline sx={{ mr: 1, color: 'primary.main' }} />
                        Performance Report
                      </Box>
                    </MenuItem>
                    <MenuItem value="health">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <LocalHospital sx={{ mr: 1, color: 'primary.main' }} />
                        Health Report
                      </Box>
                    </MenuItem>
                    <MenuItem value="market">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TrendingUp sx={{ mr: 1, color: 'primary.main' }} />
                        Market Report
                      </Box>
                    </MenuItem>
                    <MenuItem value="financial">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AttachMoney sx={{ mr: 1, color: 'primary.main' }} />
                        Financial Report
                      </Box>
                    </MenuItem>
                    <MenuItem value="financial-summary">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <AttachMoney sx={{ mr: 1, color: 'success.main' }} />
                        Financial Summary
                      </Box>
                    </MenuItem>
                    <MenuItem value="profit-loss">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TrendingUp sx={{ mr: 1, color: 'info.main' }} />
                        Profit & Loss Statement
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Report Format */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Report Format</InputLabel>
                  <Select
                    value={reportFormat}
                    onChange={(e) => setReportFormat(e.target.value as ReportFormat)}
                    label="Report Format"
                    disabled={loading}
                  >
                    <MenuItem value="html">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Code sx={{ mr: 1, color: 'primary.main' }} />
                        HTML
                      </Box>
                    </MenuItem>
                    <MenuItem value="pdf">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PictureAsPdf sx={{ mr: 1, color: 'primary.main' }} />
                        PDF
                      </Box>
                    </MenuItem>
                    <MenuItem value="excel">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TableChart sx={{ mr: 1, color: 'primary.main' }} />
                        Excel
                      </Box>
                    </MenuItem>
                    <MenuItem value="csv">
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <TableChart sx={{ mr: 1, color: 'primary.main' }} />
                        CSV
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Time Period */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Time Period</InputLabel>
                  <Select
                    value={timePeriod}
                    onChange={(e) => setTimePeriod(e.target.value as TimePeriod)}
                    label="Time Period"
                    disabled={loading}
                  >
                    <MenuItem value="week">Last Week</MenuItem>
                    <MenuItem value="month">Last Month</MenuItem>
                    <MenuItem value="quarter">Last Quarter</MenuItem>
                    <MenuItem value="year">Last Year</MenuItem>
                    <MenuItem value="custom">Custom Range</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Custom Date Range */}
              {timePeriod === 'custom' && (
                <>
                  <Grid item xs={12} md={6}>
                    <DatePicker
                      label="Start Date"
                      value={startDate}
                      onChange={(newValue) => setStartDate(newValue)}
                      disabled={loading}
                      slotProps={{ textField: { fullWidth: true } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <DatePicker
                      label="End Date"
                      value={endDate}
                      onChange={(newValue) => setEndDate(newValue)}
                      disabled={loading}
                      slotProps={{ textField: { fullWidth: true } }}
                    />
                  </Grid>
                </>
              )}

              {/* Options */}
              <Grid item xs={12}>
                <FormControl component="fieldset">
                  <FormLabel component="legend">Report Options</FormLabel>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                    <FormControlLabel
                      control={
                        <RadioGroup
                          row
                          value={includeCharts ? 'yes' : 'no'}
                          onChange={(e) => setIncludeCharts(e.target.value === 'yes')}
                        >
                          <FormControlLabel
                            value="yes"
                            control={<Radio />}
                            label="Include Charts"
                            disabled={loading}
                          />
                          <FormControlLabel
                            value="no"
                            control={<Radio />}
                            label="No Charts"
                            disabled={loading}
                          />
                        </RadioGroup>
                      }
                      label=""
                    />

                    <FormControlLabel
                      control={
                        <RadioGroup
                          row
                          value={includeSummary ? 'yes' : 'no'}
                          onChange={(e) => setIncludeSummary(e.target.value === 'yes')}
                        >
                          <FormControlLabel
                            value="yes"
                            control={<Radio />}
                            label="Include Summary"
                            disabled={loading}
                          />
                          <FormControlLabel
                            value="no"
                            control={<Radio />}
                            label="No Summary"
                            disabled={loading}
                          />
                        </RadioGroup>
                      }
                      label=""
                    />
                  </Box>
                </FormControl>
              </Grid>

              {/* Generate Button */}
              <Grid item xs={12}>
                <CustomButton
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Download />}
                  onClick={handleGenerateReport}
                  disabled={loading || animalsLoading || financialLoading}
                  fullWidth
                  sx={{ py: 1.5 }}
                >
                  {loading ? 'Generating Report...' : 'Generate & Download Report'}
                </CustomButton>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Preview Card */}
        <Card
          sx={{
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            overflow: 'hidden'
          }}
        >
          <Box
            sx={{
              p: 2,
              backgroundColor: alpha(theme.palette.primary.main, 0.05),
              borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}
          >
            <Typography variant="h6">
              Report Preview
            </Typography>
          </Box>
          <CardContent>
            <Box
              sx={{
                p: 3,
                border: `1px dashed ${alpha(theme.palette.primary.main, 0.3)}`,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.primary.main, 0.02),
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: 300
              }}
            >
              <Box sx={{ mb: 2 }}>
                {getReportTypeIcon(reportType)}
              </Box>
              <Typography variant="h6" gutterBottom>
                {reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                This report will include data for {reportType === 'financial' ? `${transactions.length} financial transactions` : `${animals.length} animals`} in {reportFormat.toUpperCase()} format.
                <br />
                Click "Generate & Download Report" to create and download your report.
              </Typography>
              <Box
                sx={{
                  mt: 3,
                  display: 'flex',
                  alignItems: 'center',
                  color: theme.palette.primary.main
                }}
              >
                {getReportFormatIcon(reportFormat)}
                <Typography variant="body2" sx={{ ml: 1 }}>
                  {reportFormat.toUpperCase()} Format
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </motion.div>

      {/* Success Notification */}
      <Snackbar
        open={success}
        autoHideDuration={6000}
        onClose={() => setSuccess(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSuccess(false)}
          severity="success"
          variant="filled"
        >
          Report generated and downloaded successfully!
        </Alert>
      </Snackbar>
    </LocalizationProvider>
  );
};

export default ReportGenerator;
