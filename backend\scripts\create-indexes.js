/**
 * Create MongoDB Indexes
 * 
 * This script creates indexes for MongoDB collections to optimize query performance.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const logger = require('../src/utils/logger');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const DB_NAME = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI, {
      dbName: DB_NAME
    });
    logger.info('Connected to MongoDB');
    return mongoose.connection;
  } catch (error) {
    logger.error('MongoDB connection error:', error);
    process.exit(1);
  }
}

// Create indexes for collections
async function createIndexes() {
  try {
    const db = mongoose.connection.db;
    
    // User indexes
    logger.info('Creating indexes for User collection...');
    await db.collection('users').createIndexes([
      { key: { username: 1 }, unique: true },
      { key: { email: 1 }, unique: true },
      { key: { role: 1 } }
    ]);
    
    // Animal indexes
    logger.info('Creating indexes for Animal collection...');
    await db.collection('animals').createIndexes([
      { key: { tagNumber: 1 }, unique: true },
      { key: { species: 1 } },
      { key: { breed: 1 } },
      { key: { gender: 1 } },
      { key: { status: 1 } },
      { key: { healthStatus: 1 } },
      { key: { location: 1 } },
      { key: { createdAt: -1 } }
    ]);
    
    // Health record indexes
    logger.info('Creating indexes for HealthRecord collection...');
    await db.collection('healthrecords').createIndexes([
      { key: { animal: 1 } },
      { key: { recordType: 1 } },
      { key: { date: -1 } },
      { key: { status: 1 } },
      { key: { performer: 1 } }
    ]);
    
    // Breeding record indexes
    logger.info('Creating indexes for BreedingRecord collection...');
    await db.collection('breedingrecords').createIndexes([
      { key: { femaleAnimal: 1 } },
      { key: { maleAnimal: 1 } },
      { key: { breedingDate: -1 } },
      { key: { status: 1 } },
      { key: { confirmedPregnant: 1 } }
    ]);
    
    // Birth record indexes
    logger.info('Creating indexes for BirthRecord collection...');
    await db.collection('birthrecords').createIndexes([
      { key: { mother: 1 } },
      { key: { father: 1 } },
      { key: { birthDate: -1 } },
      { key: { breedingRecord: 1 } }
    ]);
    
    // Transaction indexes
    logger.info('Creating indexes for Transaction collection...');
    await db.collection('transactions').createIndexes([
      { key: { transactionDate: -1 } },
      { key: { type: 1 } },
      { key: { category: 1 } },
      { key: { amount: 1 } },
      { key: { status: 1 } }
    ]);
    
    // Inventory item indexes
    logger.info('Creating indexes for InventoryItem collection...');
    await db.collection('inventoryitems').createIndexes([
      { key: { name: 1 } },
      { key: { category: 1 } },
      { key: { quantity: 1 } },
      { key: { status: 1 } }
    ]);
    
    // Inventory transaction indexes
    logger.info('Creating indexes for InventoryTransaction collection...');
    await db.collection('inventorytransactions').createIndexes([
      { key: { itemId: 1 } },
      { key: { transactionType: 1 } },
      { key: { date: -1 } }
    ]);
    
    // Business strategy indexes
    logger.info('Creating indexes for BusinessStrategy collection...');
    await db.collection('businessstrategies').createIndexes([
      { key: { name: 1 } },
      { key: { status: 1 } },
      { key: { startDate: -1 } },
      { key: { endDate: -1 } }
    ]);
    
    // Market analysis indexes
    logger.info('Creating indexes for MarketAnalysis collection...');
    await db.collection('marketanalyses').createIndexes([
      { key: { marketType: 1 } },
      { key: { date: -1 } }
    ]);
    
    // Predictive analysis indexes
    logger.info('Creating indexes for PredictiveAnalysis collection...');
    await db.collection('predictiveanalyses').createIndexes([
      { key: { analysisType: 1 } },
      { key: { date: -1 } }
    ]);
    
    // Business KPI indexes
    logger.info('Creating indexes for BusinessKpi collection...');
    await db.collection('businesskpis').createIndexes([
      { key: { date: -1 } },
      { key: { period: 1 } }
    ]);
    
    // Report indexes
    logger.info('Creating indexes for Report collection...');
    await db.collection('reports').createIndexes([
      { key: { name: 1 } },
      { key: { type: 1 } },
      { key: { createdAt: -1 } }
    ]);
    
    // Report result indexes
    logger.info('Creating indexes for ReportResult collection...');
    await db.collection('reportresults').createIndexes([
      { key: { reportId: 1 } },
      { key: { generatedAt: -1 } }
    ]);
    
    logger.info('All indexes created successfully');
  } catch (error) {
    logger.error('Error creating indexes:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    // Connect to MongoDB
    await connectDB();
    
    // Create indexes
    await createIndexes();
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    logger.error('Error:', error);
    
    // Disconnect from MongoDB
    try {
      await mongoose.disconnect();
      logger.info('Disconnected from MongoDB');
    } catch (disconnectError) {
      logger.error('Error disconnecting from MongoDB:', disconnectError);
    }
    
    process.exit(1);
  }
}

// Run the main function
main();
