import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Box, Typography, Grid, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, useTheme, alpha, IconButton, Divider, MenuItem, Select, FormControl, InputLabel, TextField } from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  BarChart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Download,
  Print,
  Share,
  FilterList,
  DateRange
} from '@mui/icons-material';
import {  ModuleHeader, AnimatedBackgroundCard , CustomButton } from '../../components/common';
import { useAlert } from '../../hooks/useAlert';
import { format } from 'date-fns';
import { generateReport as generateReportOld, generateReportFromAPI } from '../../utils/reportUtils';
import { generateReport, ReportParams } from '../../services/reportService';

// Mock data for financial reports
const financialMetrics = [
  {
    metric: 'Total Revenue',
    value: 'R 1,245,890',
    trend: 'up',
    change: '+12.5%',
    period: 'vs last month'
  },
  {
    metric: 'Total Expenses',
    value: 'R 876,450',
    trend: 'down',
    change: '-5.2%',
    period: 'vs last month'
  },
  {
    metric: 'Net Profit',
    value: 'R 369,440',
    trend: 'up',
    change: '+18.7%',
    period: 'vs last month'
  },
  {
    metric: 'Profit Margin',
    value: '29.7%',
    trend: 'up',
    change: '+2.3%',
    period: 'vs last month'
  }
];

const recentTransactions = [
  {
    id: 'TRX001',
    date: '2024-04-15',
    description: 'Livestock feed purchase',
    category: 'Feed',
    amount: -45600,
    paymentMethod: 'Bank Transfer'
  },
  {
    id: 'TRX002',
    date: '2024-04-12',
    description: 'Cattle sale - 5 heads',
    category: 'Sales',
    amount: 125000,
    paymentMethod: 'Bank Transfer'
  },
  {
    id: 'TRX003',
    date: '2024-04-10',
    description: 'Veterinary services',
    category: 'Health',
    amount: -12500,
    paymentMethod: 'Credit Card'
  },
  {
    id: 'TRX004',
    date: '2024-04-08',
    description: 'Farm equipment maintenance',
    category: 'Maintenance',
    amount: -8750,
    paymentMethod: 'Cash'
  },
  {
    id: 'TRX005',
    date: '2024-04-05',
    description: 'Milk sales - 1200L',
    category: 'Sales',
    amount: 36000,
    paymentMethod: 'Bank Transfer'
  }
];

const reportTypes = [
  {
    name: 'Profit & Loss Statement',
    description: 'Comprehensive overview of revenue and expenses',
    lastGenerated: '2024-04-01',
    icon: <BarChartIcon />
  },
  {
    name: 'Cash Flow Report',
    description: 'Track cash inflows and outflows over time',
    lastGenerated: '2024-04-05',
    icon: <TrendingUp />
  },
  {
    name: 'Expense Breakdown',
    description: 'Detailed analysis of all farm expenses by category',
    lastGenerated: '2024-03-31',
    icon: <PieChartIcon />
  },
  {
    name: 'Revenue Analysis',
    description: 'Breakdown of all income sources and trends',
    lastGenerated: '2024-03-28',
    icon: <BarChartIcon />
  }
];

const FinancialReports: React.FC = () => {
  const theme = useTheme();
  const { showAlert } = useAlert();
  const [dateRange, setDateRange] = useState('month');
  const [reportFormat, setReportFormat] = useState('pdf');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  const handleGenerateReport = async (reportName: string) => {
    showAlert({
      message: `Generating ${reportName} in ${reportFormat.toUpperCase()} format...`,
      severity: 'info'
    });

    try {
      // Determine report type based on the report name
      let reportType: 'financial' | 'market' | 'performance' = 'financial';

      if (reportName.includes('Profit & Loss')) {
        reportType = 'financial';
      } else if (reportName.includes('Cash Flow')) {
        reportType = 'financial';
      } else if (reportName.includes('Expense')) {
        reportType = 'financial';
      } else if (reportName.includes('Revenue')) {
        reportType = 'financial';
      } else if (reportName.includes('Market')) {
        reportType = 'market';
      } else if (reportName.includes('Performance')) {
        reportType = 'performance';
      }

      // Import mock data for demonstration
      const mockAnimals = await import('../../mocks/animalData').then(module => module.mockAnimals);
      const mockFinancialRecords = await import('../../mocks/financialData').then(module => module.mockFinancialRecords);

      // Generate the report using our detailed report generation function
      const params: ReportParams = {
        type: reportType,
        format: reportFormat as any,
        timePeriod: dateRange as any,
        filters: {
          includeFinancialRecords: true,
          includeAnimals: true,
          reportName: reportName
        }
      };

      await generateReport(params);

      showAlert({
        message: `${reportName} has been generated successfully!`,
        severity: 'success'
      });
    } catch (error) {
      console.error('Error generating report:', error);

      // Fall back to the old report generation method if the new one fails
      try {
        console.warn('Detailed report generation failed, falling back to basic report generation:', error);

        // Determine which data to use based on the report name
        let reportData: any[] = [];
        let reportType = 'financial';

        if (reportName.includes('Profit & Loss')) {
          reportData = financialMetrics;
          reportType = 'profit-loss';
        } else if (reportName.includes('Cash Flow')) {
          reportData = recentTransactions;
          reportType = 'cash-flow';
        } else if (reportName.includes('Expense')) {
          reportData = recentTransactions.filter(t => t.amount < 0);
          reportType = 'expenses';
        } else if (reportName.includes('Revenue')) {
          reportData = recentTransactions.filter(t => t.amount > 0);
          reportType = 'revenue';
        } else {
          // Default to all transactions
          reportData = recentTransactions;
        }

        generateReportOld(
          reportData,
          reportName.toLowerCase().replace(/\s+/g, '_'),
          reportFormat as 'csv' | 'excel' | 'pdf',
          reportName
        );

        showAlert({
          message: `${reportName} has been generated successfully!`,
          severity: 'success'
        });
      } catch (fallbackError) {
        console.error('Fallback report generation also failed:', fallbackError);
        showAlert({
          message: `Error generating report: ${error instanceof Error ? error.message : 'Unknown error'}`,
          severity: 'error'
        });
      }
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <Box>
      <ModuleHeader
        title="Financial Reports"
        subtitle="Comprehensive financial analysis and reporting tools"
        module="reports"
        submodule="financial"
        actionLabel="Generate Report"
        onAction={() => handleGenerateReport('Custom Financial Report')}
      />

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Key metrics */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Grid container spacing={3} mb={4}>
            {financialMetrics.map((metric, index) => (
              <Grid item xs={12} sm={6} md={3} key={metric.metric}>
                <motion.div variants={itemVariants}>
                  <AnimatedBackgroundCard
                    title={metric.metric}
                    subtitle={metric.value}
                    icon={metric.trend === 'up' ? <TrendingUp /> : <TrendingDown />}
                    accentColor={metric.trend === 'up' ? theme.palette.success.main : theme.palette.error.main}
                    secondaryColor={metric.trend === 'up' ? theme.palette.success.dark : theme.palette.error.dark}
                    delay={index * 0.1}
                    height={160}
                    overlay="gradient"
                    content={
                      <Box sx={{ mt: 1 }}>
                        <Typography variant="body2" fontWeight="medium" color={metric.trend === 'up' ? 'success.main' : 'error.main'}>
                          {metric.change} {metric.period}
                        </Typography>
                      </Box>
                    }
                  />
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Report Generation Tools */}
          <Card
            sx={{
              mb: 4,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.light, 0.1)})`
            }}
          >
            <CardContent>
              <Typography variant="h6" fontWeight="bold" gutterBottom>
                Report Generation Tools
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="date-range-label">Date Range</InputLabel>
                    <Select
                      labelId="date-range-label"
                      value={dateRange}
                      label="Date Range"
                      onChange={(e) => setDateRange(e.target.value)}
                    >
                      <MenuItem value="week">Last Week</MenuItem>
                      <MenuItem value="month">Last Month</MenuItem>
                      <MenuItem value="quarter">Last Quarter</MenuItem>
                      <MenuItem value="year">Last Year</MenuItem>
                      <MenuItem value="custom">Custom Range</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {dateRange === 'custom' && (
                  <>
                    <Grid item xs={12} md={3}>
                      <TextField
                        label="Start Date"
                        type="date"
                        size="small"
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                        defaultValue={format(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd')}
                      />
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <TextField
                        label="End Date"
                        type="date"
                        size="small"
                        fullWidth
                        InputLabelProps={{ shrink: true }}
                        defaultValue={format(new Date(), 'yyyy-MM-dd')}
                      />
                    </Grid>
                  </>
                )}

                <Grid item xs={12} md={dateRange === 'custom' ? 3 : 6}>
                  <FormControl fullWidth size="small">
                    <InputLabel id="format-label">Report Format</InputLabel>
                    <Select
                      labelId="format-label"
                      value={reportFormat}
                      label="Report Format"
                      onChange={(e) => setReportFormat(e.target.value)}
                    >
                      <MenuItem value="pdf">PDF Document</MenuItem>
                      <MenuItem value="xlsx">Excel Spreadsheet</MenuItem>
                      <MenuItem value="csv">CSV File</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={3}>
                  <CustomButton
                    variant="contained"
                    fullWidth
                    startIcon={<FilterList />}
                  >
                    Additional Filters
                  </CustomButton>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Available Reports */}
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Available Reports
          </Typography>

          <Grid container spacing={3} mb={4}>
            {reportTypes.map((report, index) => (
              <Grid item xs={12} sm={6} key={report.name}>
                <motion.div variants={itemVariants}>
                  <Card sx={{ height: '100%', boxShadow: '0 2px 10px rgba(0,0,0,0.08)' }}>
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Box
                          sx={{
                            p: 1.5,
                            borderRadius: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            color: theme.palette.primary.main,
                            mr: 2
                          }}
                        >
                          {report.icon}
                        </Box>
                        <Box>
                          <Typography variant="h6" fontWeight="bold">
                            {report.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Last generated: {report.lastGenerated}
                          </Typography>
                        </Box>
                      </Box>

                      <Typography variant="body2" paragraph>
                        {report.description}
                      </Typography>

                      <Box display="flex" justifyContent="flex-end" gap={1}>
                        <IconButton size="small" color="primary" title="Print Report">
                          <Print />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="primary"
                          title="Download Report"
                          onClick={() => handleGenerateReport(report.name)}
                        >
                          <Download />
                        </IconButton>
                        <IconButton size="small" color="primary" title="Share Report">
                          <Share />
                        </IconButton>
                        <CustomButton
                          variant="outlined"
                          size="small"
                          onClick={() => handleGenerateReport(report.name)}
                        >
                          Generate
                        </CustomButton>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Recent Transactions */}
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            Recent Financial Transactions
          </Typography>

          <Card
            sx={{
              mb: 4,
              borderRadius: 2,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden'
            }}
          >
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableRow>
                    <TableCell>Transaction ID</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Payment Method</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {recentTransactions.map((transaction) => (
                    <TableRow key={transaction.id} hover>
                      <TableCell>{transaction.id}</TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>
                        <Chip
                          label={transaction.category}
                          size="small"
                          sx={{
                            bgcolor: transaction.category === 'Sales'
                              ? alpha(theme.palette.success.main, 0.1)
                              : alpha(theme.palette.info.main, 0.1),
                            color: transaction.category === 'Sales'
                              ? theme.palette.success.main
                              : theme.palette.info.main
                          }}
                        />
                      </TableCell>
                      <TableCell align="right" sx={{
                        color: transaction.amount > 0
                          ? theme.palette.success.main
                          : theme.palette.error.main,
                        fontWeight: 'bold'
                      }}>
                        {formatCurrency(Math.abs(transaction.amount))}
                      </TableCell>
                      <TableCell>{transaction.paymentMethod}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        </motion.div>
      </Box>
    </Box>
  );
};

export default FinancialReports;
