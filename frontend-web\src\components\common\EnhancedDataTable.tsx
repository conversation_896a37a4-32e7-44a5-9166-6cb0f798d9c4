import React, { useState, useEffect } from 'react';
import { useResponsive } from '../../hooks';
import { Box, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Typography, IconButton, Chip, TextField, InputAdornment, Collapse, Tooltip, CircularProgress, useTheme, alpha } from '@mui/material';
import {
  Search,
  FilterList,
  KeyboardArrowDown,
  KeyboardArrowUp,
  MoreVert
 } from '../../utils/iconImports';
import { motion } from 'framer-motion';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  format?: (value: any, row?: any) => React.ReactNode;
  type?: 'text' | 'number' | 'date' | 'status' | 'actions';
  sortable?: boolean;
  hideOnMobile?: boolean;
}

interface Action {
  icon: React.ReactNode;
  label?: string;
  title?: string;
  onClick: (row: any) => void;
  color?: string;
}

interface EnhancedDataTableProps {
  title?: string;
  responsiveMode?: 'standard' | 'cards' | 'auto';
  subtitle?: string;
  columns: Column[];
  data: any[];
  keyField?: string;
  loading?: boolean;
  onRefresh?: () => void;
  onRowClick?: (row: any) => void;
  onView?: (row: any) => void;
  onEdit?: (row: any) => void;
  onDelete?: (row: any) => void;
  actions?: Action[];
  searchable?: boolean;
  searchFields?: string[];
  searchPlaceholder?: string;
  filterable?: boolean;
  filters?: React.ReactNode;
  pagination?: boolean;
  initialRowsPerPage?: number;
  rowsPerPageOptions?: number[];
  emptyMessage?: string;
  backgroundImage?: string;
  accentColor?: string;
  className?: string;
  showHeader?: boolean;
  expandableRows?: boolean;
  renderExpandedRow?: (row: any) => React.ReactNode;
}

const EnhancedDataTable: React.FC<EnhancedDataTableProps> = ({
  title,
  subtitle,
  columns,
  data,
  keyField = 'id',
  loading = false,
  onRefresh,
  onRowClick,
  onView,
  onEdit,
  onDelete,
  actions = [],
  searchable = true,
  searchFields = [],
  searchPlaceholder = 'Search...',
  filterable = false,
  filters,
  pagination = true,
  initialRowsPerPage = 10,
  rowsPerPageOptions = [5, 10, 25, 50],
  emptyMessage = 'No data available',
  backgroundImage,
  accentColor,
  className,
  showHeader = true,
  expandableRows = false,
  renderExpandedRow,
  responsiveMode = 'auto'
}) => {
  const theme = useTheme();
  const { isMobile } = useResponsive();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(initialRowsPerPage);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  const handleExpandRow = (rowKey: string) => {
    setExpandedRow(expandedRow === rowKey ? null : rowKey);
  };
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [displayMode, setDisplayMode] = useState<'table' | 'cards'>('table');

  // Determine display mode based on screen size and responsiveMode prop
  useEffect(() => {
    if (responsiveMode === 'standard') {
      setDisplayMode('table');
    } else if (responsiveMode === 'cards') {
      setDisplayMode('cards');
    } else {
      // Auto mode - use cards on mobile, table on desktop
      setDisplayMode(isMobile ? 'cards' : 'table');
    }
  }, [isMobile, responsiveMode]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleSort = (columnId: string) => {
    let direction: 'asc' | 'desc' = 'asc';

    if (sortConfig && sortConfig.key === columnId && sortConfig.direction === 'asc') {
      direction = 'desc';
    }

    setSortConfig({ key: columnId, direction });
  };

  const toggleExpandRow = (rowId: string) => {
    setExpandedRow(expandedRow === rowId ? null : rowId);
  };

  // Filter data based on search term
  const filteredData = data.filter(row => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return columns.some(column => {
      const value = row[column.id];
      if (value === null || value === undefined) return false;
      return String(value).toLowerCase().includes(searchLower);
    });
  });

  // Sort data if sortConfig is set
  const sortedData = React.useMemo(() => {
    if (!sortConfig) return filteredData;

    return [...filteredData].sort((a, b) => {
      if (a[sortConfig.key] === null) return 1;
      if (b[sortConfig.key] === null) return -1;

      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredData, sortConfig]);

  // Paginate data
  const paginatedData = sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  // Render status chip
  const renderStatusChip = (value: string) => {
    let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'default';

    const valueLower = String(value).toLowerCase();

    if (['active', 'available', 'good', 'healthy', 'completed', 'approved'].some(s => valueLower.includes(s))) {
      color = 'success';
    } else if (['pending', 'in progress', 'medium', 'warning', 'review'].some(s => valueLower.includes(s))) {
      color = 'warning';
    } else if (['inactive', 'unavailable', 'low', 'expired', 'rejected', 'error', 'sick', 'critical'].some(s => valueLower.includes(s))) {
      color = 'error';
    } else if (['draft', 'info', 'neutral'].some(s => valueLower.includes(s))) {
      color = 'info';
    }

    return (
      <Chip
        label={value}
        color={color}
        size="small"
        sx={{ fontWeight: 500 }}
      />
    );
  };

  // Render a card view for mobile devices
  const renderCardView = () => {
    const visibleColumns = columns.filter(col => !col.hideOnMobile);

    return (
      <Box sx={{ mt: 2 }}>
        {filteredData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row, index) => {
          const rowKey = row[keyField] || index;

          return (
            <motion.div
              key={rowKey}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  mb: 2,
                  borderRadius: '12px',
                  cursor: onRowClick ? 'pointer' : 'default',
                  '&:hover': onRowClick ? {
                    boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                    transform: 'translateY(-2px)',
                  } : {},
                  transition: 'all 0.2s ease',
                  border: `1px solid ${theme.palette.divider}`,
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '4px',
                    height: '100%',
                    backgroundColor: accentColor || theme.palette.primary.main,
                  }
                }}
                onClick={() => onRowClick && onRowClick(row)}
              >
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {row[visibleColumns[0]?.id] || 'Item'}
                  </Typography>

                  {/* Actions */}
                  {(onView || onEdit || onDelete || actions.length > 0) && (
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {onView && (
                        <IconButton size="small" onClick={(e) => { e.stopPropagation(); onView(row); }}>
                          <Search fontSize="small" />
                        </IconButton>
                      )}
                      {onEdit && (
                        <IconButton size="small" onClick={(e) => { e.stopPropagation(); onEdit(row); }}>
                          <Search fontSize="small" />
                        </IconButton>
                      )}
                      {onDelete && (
                        <IconButton size="small" onClick={(e) => { e.stopPropagation(); onDelete(row); }}>
                          <Search fontSize="small" />
                        </IconButton>
                      )}
                      {actions.map((action, actionIndex) => (
                        <IconButton
                          key={actionIndex}
                          size="small"
                          onClick={(e) => { e.stopPropagation(); action.onClick(row); }}
                        >
                          {action.icon}
                        </IconButton>
                      ))}
                    </Box>
                  )}
                </Box>

                {/* Card Content - Display other columns */}
                <Box sx={{ mt: 1 }}>
                  {visibleColumns.slice(1).map((column) => {
                    const value = row[column.id];
                    return (
                      <Box key={column.id} sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2" color="text.secondary">
                          {column.label}:
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {column.format ? column.format(value, row) : value}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>

                {/* Expandable content */}
                {expandableRows && renderExpandedRow && (
                  <Box sx={{ mt: 2 }}>
                    <Box
                      sx={{
                        p: 1,
                        borderTop: `1px solid ${theme.palette.divider}`,
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandRow(rowKey.toString());
                      }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        {expandedRow === rowKey.toString() ? 'Hide Details' : 'Show Details'}
                      </Typography>
                      {expandedRow === rowKey.toString() ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                    </Box>

                    <Collapse in={expandedRow === rowKey.toString()}>
                      <Box sx={{ p: 2, bgcolor: alpha(theme.palette.background.default, 0.5) }}>
                        {renderExpandedRow(row)}
                      </Box>
                    </Collapse>
                  </Box>
                )}
              </Paper>
            </motion.div>
          );
        })}
      </Box>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Paper
        sx={{
          width: '100%',
          overflow: 'hidden',
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          position: 'relative',
          backgroundImage: backgroundImage ? `url(${backgroundImage})` : 'none',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          '&::before': backgroundImage ? {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(5px)',
            zIndex: 0
          } : {}
        }}
      >
        <Box
          sx={{
            p: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            position: 'relative',
            zIndex: 1,
            backgroundColor: accentColor ? alpha(accentColor, 0.05) : 'transparent'
          }}
        >
          <Box>
            {title && (
              <Typography variant="h6" fontWeight="bold" color={accentColor || 'inherit'}>
                {title}
              </Typography>
            )}
            {subtitle && (
              <Typography variant="body2" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>

          <TextField
            placeholder={searchPlaceholder}
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearch}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search fontSize="small" color="action" />
                </InputAdornment>
              )
            }}
            sx={{
              width: { xs: '100%', sm: '250px' },
              backgroundColor: 'background.paper'
            }}
          />
        </Box>

        {displayMode === 'cards' ? (
        <Box sx={{ p: 2, position: 'relative', zIndex: 1 }}>
          {renderCardView()}
        </Box>
      ) : (
        <TableContainer sx={{ maxHeight: 440, position: 'relative', zIndex: 1 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {expandableRows && (
                  <TableCell padding="checkbox" />
                )}
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align || 'left'}
                    style={{ minWidth: column.minWidth }}
                    sx={{
                      fontWeight: 'bold',
                      backgroundColor: accentColor ? alpha(accentColor, 0.1) : alpha(theme.palette.primary.main, 0.1),
                      cursor: column.sortable ? 'pointer' : 'default'
                    }}
                    onClick={column.sortable ? () => handleSort(column.id) : undefined}
                  >
                    <Box display="flex" alignItems="center">
                      {column.label}
                      {sortConfig && sortConfig.key === column.id && (
                        <Box component="span" ml={0.5}>
                          {sortConfig.direction === 'asc' ? '↑' : '↓'}
                        </Box>
                      )}
                    </Box>
                  </TableCell>
                ))}
                {actions.length > 0 && (
                  <TableCell
                    align="right"
                    sx={{
                      fontWeight: 'bold',
                      backgroundColor: accentColor ? alpha(accentColor, 0.1) : alpha(theme.palette.primary.main, 0.1)
                    }}
                  >
                    Actions
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length + (actions.length > 0 ? 1 : 0) + (expandableRows ? 1 : 0)}
                    align="center"
                    sx={{ py: 3 }}
                  >
                    <CircularProgress size={40} color="primary" />
                    <Typography variant="body2" color="text.secondary" mt={2}>
                      Loading data...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedData.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={columns.length + (actions.length > 0 ? 1 : 0) + (expandableRows ? 1 : 0)}
                    align="center"
                    sx={{ py: 3 }}
                  >
                    <Typography variant="body1" color="text.secondary">
                      {emptyMessage}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedData.map((row) => {
                  const isExpanded = expandableRows && expandedRow === row[keyField];

                  return (
                    <React.Fragment key={row[keyField] || Math.random()}>
                      <TableRow
                        hover
                        onClick={onRowClick && !expandableRows ? () => onRowClick(row) : undefined}
                        sx={{
                          cursor: (onRowClick && !expandableRows) ? 'pointer' : 'default',
                          backgroundColor: isExpanded ? alpha(theme.palette.primary.main, 0.05) : 'inherit'
                        }}
                      >
                        {expandableRows && (
                          <TableCell padding="checkbox">
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleExpandRow(row[keyField]);
                              }}
                            >
                              {isExpanded ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
                            </IconButton>
                          </TableCell>
                        )}

                        {columns.map((column) => {
                          const value = row[column.id];
                          return (
                            <TableCell key={column.id} align={column.align || 'left'}>
                              {column.format ? column.format(value, row) :
                                column.type === 'status' ? renderStatusChip(value) : value}
                            </TableCell>
                          );
                        })}

                        {actions.length > 0 && (
                          <TableCell align="right">
                            <Box display="flex" justifyContent="flex-end">
                              {actions.map((action, index) => (
                                <Tooltip key={index} title={action.label}>
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      action.onClick(row);
                                    }}
                                    sx={{ color: action.color }}
                                  >
                                    {action.icon}
                                  </IconButton>
                                </Tooltip>
                              ))}
                            </Box>
                          </TableCell>
                        )}
                      </TableRow>

                      {expandableRows && isExpanded && renderExpandedRow && (
                        <TableRow>
                          <TableCell
                            colSpan={columns.length + (actions.length > 0 ? 1 : 0) + 1}
                            sx={{ py: 0, borderBottom: 0 }}
                          >
                            <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                              {renderExpandedRow(row)}
                            </Collapse>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredData.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{ position: 'relative', zIndex: 1 }}
        />
      </Paper>
    </motion.div>
  );
};

export default EnhancedDataTable;
