import { useCallback, useRef, KeyboardEvent } from 'react';

/**
 * Hook for managing keyboard navigation and accessibility features
 * @returns Object with accessibility utility functions
 */
export const useAccessibility = () => {
  const focusRef = useRef<HTMLElement | null>(null);

  /**
   * Handle keyboard navigation for interactive elements
   * @param event Keyboard event
   * @param options Configuration options
   */
  const handleKeyboardNavigation = useCallback((
    event: KeyboardEvent<HTMLElement>,
    options: {
      onEnter?: () => void;
      onSpace?: () => void;
      onArrowUp?: () => void;
      onArrowDown?: () => void;
      onArrowLeft?: () => void;
      onArrowRight?: () => void;
      onEscape?: () => void;
      onTab?: () => void;
    } = {}
  ) => {
    const { 
      onEnter, 
      onSpace, 
      onArrowUp, 
      onArrowDown, 
      onArrowLeft, 
      onArrowRight,
      onEscape,
      onTab
    } = options;

    switch (event.key) {
      case 'Enter':
        if (onEnter) {
          event.preventDefault();
          onEnter();
        }
        break;
      case ' ':
        if (onSpace) {
          event.preventDefault();
          onSpace();
        }
        break;
      case 'ArrowUp':
        if (onArrowUp) {
          event.preventDefault();
          onArrowUp();
        }
        break;
      case 'ArrowDown':
        if (onArrowDown) {
          event.preventDefault();
          onArrowDown();
        }
        break;
      case 'ArrowLeft':
        if (onArrowLeft) {
          event.preventDefault();
          onArrowLeft();
        }
        break;
      case 'ArrowRight':
        if (onArrowRight) {
          event.preventDefault();
          onArrowRight();
        }
        break;
      case 'Escape':
        if (onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
      case 'Tab':
        if (onTab) {
          // Don't prevent default for Tab to maintain normal tab navigation
          onTab();
        }
        break;
      default:
        break;
    }
  }, []);

  /**
   * Set focus to an element
   * @param element Element to focus
   */
  const setFocus = useCallback((element: HTMLElement | null) => {
    if (element) {
      element.focus();
      focusRef.current = element;
    }
  }, []);

  /**
   * Get ARIA attributes for different element types
   * @param role ARIA role
   * @param options Additional options
   * @returns Object with ARIA attributes
   */
  const getAriaAttributes = useCallback((
    role: string,
    options: {
      label?: string;
      description?: string;
      expanded?: boolean;
      selected?: boolean;
      checked?: boolean;
      disabled?: boolean;
      required?: boolean;
      hasPopup?: boolean;
      controls?: string;
      labelledBy?: string;
      describedBy?: string;
      level?: number;
    } = {}
  ) => {
    const {
      label,
      description,
      expanded,
      selected,
      checked,
      disabled,
      required,
      hasPopup,
      controls,
      labelledBy,
      describedBy,
      level
    } = options;

    const attributes: Record<string, string | boolean | number | undefined> = {
      role,
    };

    if (label) attributes['aria-label'] = label;
    if (description) attributes['aria-description'] = description;
    if (expanded !== undefined) attributes['aria-expanded'] = expanded;
    if (selected !== undefined) attributes['aria-selected'] = selected;
    if (checked !== undefined) attributes['aria-checked'] = checked;
    if (disabled !== undefined) attributes['aria-disabled'] = disabled;
    if (required !== undefined) attributes['aria-required'] = required;
    if (hasPopup !== undefined) attributes['aria-haspopup'] = hasPopup;
    if (controls) attributes['aria-controls'] = controls;
    if (labelledBy) attributes['aria-labelledby'] = labelledBy;
    if (describedBy) attributes['aria-describedby'] = describedBy;
    if (level) attributes['aria-level'] = level;

    return attributes;
  }, []);

  return {
    handleKeyboardNavigation,
    setFocus,
    getAriaAttributes,
    focusRef
  };
};

export default useAccessibility;
