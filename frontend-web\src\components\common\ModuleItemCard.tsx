import React from 'react';
import { CustomButton } from '../common';
import { Box, Typography, Chip, useTheme, alpha, CardProps } from '@mui/material';
import BlendedBackgroundCard from './BlendedBackgroundCard';
import { getCardBackground } from '../../utils/CardStyleUtils';

interface ModuleItemCardProps extends Omit<CardProps, 'children' | 'title' | 'content'> {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  module: string;
  backgroundImage?: string;
  icon?: React.ReactNode;
  primaryColor?: string;
  secondaryColor?: string;
  tags?: Array<{
    label: string;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  }>;
  status?: {
    label: string;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  };
  primaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  };
  content?: React.ReactNode;
  footer?: React.ReactNode;
  height?: number | string;
}

/**
 * A generic card component for displaying items in any module
 * Uses the BlendedBackgroundCard for consistent styling across modules
 */
const ModuleItemCard: React.FC<ModuleItemCardProps> = ({
  id,
  title,
  subtitle,
  description,
  module,
  backgroundImage,
  icon,
  primaryColor,
  secondaryColor,
  tags,
  status,
  primaryAction,
  secondaryAction,
  content,
  footer,
  height = 200,
  ...cardProps
}) => {
  const theme = useTheme();

  // Use provided background image or get one based on ID and module
  const cardBackground = backgroundImage || getCardBackground(id, module);

  // Use theme colors if not provided
  const cardPrimaryColor = primaryColor || theme.palette.primary.main;
  const cardSecondaryColor = secondaryColor || theme.palette.primary.dark;

  return (
    <BlendedBackgroundCard
      backgroundImage={cardBackground}
      primaryColor={cardPrimaryColor}
      secondaryColor={cardSecondaryColor}
      height={height}
      {...cardProps}
      title={
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              {title}
            </Typography>

            {subtitle && (
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                {subtitle}
              </Typography>
            )}

            {/* Tags */}
            {tags && tags.length > 0 && (
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                {tags.map((tag, index) => (
                  <Chip
                    key={index}
                    label={tag.label}
                    size="small"
                    color={tag.color || 'primary'}
                    sx={{
                      height: '20px',
                      fontSize: '0.7rem',
                      backgroundColor: alpha(theme.palette[tag.color || 'primary'].main, 0.2),
                      color: 'white',
                      borderColor: alpha(theme.palette[tag.color || 'primary'].main, 0.3),
                    }}
                  />
                ))}
              </Box>
            )}
          </Box>

          {/* Icon */}
          {icon && (
            <Box
              sx={{
                p: 1,
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                ml: 2
              }}
            >
              {icon}
            </Box>
          )}

          {/* Status Chip */}
          {status && (
            <Chip
              label={status.label}
              size="small"
              color={status.color || 'primary'}
              sx={{
                ml: icon ? 1 : 2,
                height: '24px',
                fontSize: '0.75rem',
                backgroundColor: alpha(theme.palette[status.color || 'primary'].main, 0.2),
                color: 'white',
                borderColor: alpha(theme.palette[status.color || 'primary'].main, 0.3),
              }}
            />
          )}
        </Box>
      }
      content={
        <Box sx={{ mt: 2 }}>
          {description && (
            <Typography variant="body2" sx={{ opacity: 0.9, mb: 2 }}>
              {description}
            </Typography>
          )}

          {content}
        </Box>
      }
      action={
        (primaryAction || secondaryAction) ? (
          <Box sx={{ display: 'flex', gap: 1 }}>
            {secondaryAction && (
              <CustomButton
                variant="outlined"
                size="small"
                startIcon={secondaryAction.icon}
                onClick={secondaryAction.onClick}
                sx={{
                  borderColor: 'rgba(255, 255, 255, 0.5)',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  }
                }}
              >
                {secondaryAction.label}
              </CustomButton>
            )}

            {primaryAction && (
              <CustomButton
                variant="contained"
                size="small"
                startIcon={primaryAction.icon}
                onClick={primaryAction.onClick}
                sx={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                  }
                }}
              >
                {primaryAction.label}
              </CustomButton>
            )}
          </Box>
        ) : undefined
      }
      footer={footer}
    />
  );
};

export default ModuleItemCard;
