import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, IconButton, TextField, MenuItem, Grid, Typography, Chip, Alert, Snackbar, CircularProgress,  } from '@mui/material';
import {
  Add,
  CalendarToday,
  Search,
  LocalHospital,
  Refresh,
  ArrowForward,
 } from '../../utils/iconImports';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { Box } from '@mui/material';
import HealthRecordModal from '../../components/health/HealthRecordModal';
import { useHealthRecords } from '../../hooks/useHealthRecords';
import { useSnackbar } from '../../contexts/SnackbarContext';
// Import HealthRecord type from types
import type { HealthRecord } from '../../types/health';

interface HealthRecordsProps {
  limit?: number;
  showViewAll?: boolean;
}

const HealthRecords: React.FC<HealthRecordsProps> = ({ limit, showViewAll = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<HealthRecord | null>(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { healthRecords, loading, error, fetchHealthData } = useHealthRecords();
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();

  // Refresh data when component mounts
  useEffect(() => {
    fetchHealthData();
  }, [fetchHealthData]);

  const handleRefresh = () => {
    fetchHealthData();
    showSnackbar('Health records refreshed', 'success');
  };

  const handleSaveRecord = () => {
    fetchHealthData();
  };

  let filteredRecords = healthRecords.filter((record) => {
    // Filter by type
    const typeMatch = filterType === 'all' || record.type === filterType;

    // Filter by search term
    const searchMatch = searchTerm === '' ||
      (record.animalId && record.animalId.toString().includes(searchTerm)) ||
      (record.description && record.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (record.performedBy && record.performedBy.toLowerCase().includes(searchTerm.toLowerCase()));

    return typeMatch && searchMatch;
  });

  // Apply limit if provided
  if (limit && limit > 0) {
    filteredRecords = filteredRecords.slice(0, limit);
  }

  const columns = [
    {
      field: 'animalTag',
      headerName: 'Animal Tag',
      width: 150,
      renderCell: (params: any) => (
        <Typography variant="body2" className="font-medium">
          #{params.value}
        </Typography>
      ),
    },
    {
      field: 'recordType',
      headerName: 'Record Type',
      width: 180,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={params.value === 'Vaccination' ? 'success' : 'primary'}
          size="small"
        />
      ),
    },
    {
      field: 'date',
      headerName: 'Date',
      width: 150,
      renderCell: (params: any) => (
        <div className="flex items-center space-x-2">
          <CalendarToday fontSize="small" />
          <span>{new Date(params.value).toLocaleDateString()}</span>
        </div>
      ),
    },
    {
      field: 'diagnosis',
      headerName: 'Diagnosis/Treatment',
      width: 250,
    },
    {
      field: 'veterinarian',
      headerName: 'Veterinarian',
      width: 180,
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 150,
      renderCell: (params: any) => (
        <Chip
          label={params.value}
          color={
            params.value === 'Completed'
              ? 'success'
              : params.value === 'Pending'
              ? 'warning'
              : 'error'
          }
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      renderCell: (params: any) => (
        <div className="flex space-x-2">
          <IconButton
            size="small"
            onClick={() => {
              setSelectedRecord(params.row);
              setIsModalOpen(true);
            }}
          >
            <LocalHospital fontSize="small" />
          </IconButton>
        </div>
      ),
    },
  ];

  const healthMetrics = [
    {
      title: 'Total Records',
      value: healthRecords.length,
      color: 'bg-blue-100 text-blue-800',
    },
    {
      title: 'Pending Treatments',
      value: healthRecords.filter((r: HealthRecord) => r.status === 'scheduled').length,
      color: 'bg-yellow-100 text-yellow-800',
    },
    {
      title: 'Critical Cases',
      value: healthRecords.filter((r: HealthRecord) => r.status === 'cancelled').length,
      color: 'bg-red-100 text-red-800',
    },
    {
      title: 'Completed',
      value: healthRecords.filter((r: HealthRecord) => r.status === 'completed').length,
      color: 'bg-green-100 text-green-800',
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="p-6 space-y-6"
    >
      {/* Error Alert */}
      {error && (
        <Alert severity="error" className="mb-4 w-full">
          {error}
        </Alert>
      )}

      {/* Header */}
      {!limit && (
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Health Records</h1>
            <p className="text-gray-600">
              Track and manage animal health records and treatments
            </p>
          </div>
          <div className="flex gap-2">
          <CustomButton
            variant="outlined"
            color="primary"
            startIcon={<Refresh />}
            onClick={handleRefresh}
          >
            Refresh
          </CustomButton>
          <CustomButton
            variant="contained"
            startIcon={<Add />}
            onClick={() => {
              setSelectedRecord(null);
              setIsModalOpen(true);
            }}
          >
            New Health Record
          </CustomButton>
        </div>
      </div>
      )}

      {/* Metrics Cards */}
      {!limit && (
        <Grid container spacing={3}>
          {healthMetrics.map((metric) => (
          <Grid item xs={12} sm={6} md={3} key={metric.title}>
            <Card className="p-4">
              <Typography variant="subtitle2" className="text-gray-600">
                {metric.title}
              </Typography>
              <Typography
                variant="h4"
                className={`mt-2 font-bold ${metric.color} p-2 rounded-lg`}
              >
                {metric.value}
              </Typography>
            </Card>
          </Grid>
          ))}
        </Grid>
      )}

      {/* Filters and Search */}
      {!limit && (
        <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow">
          <div className="flex space-x-4">
          <TextField
            select
            label="Filter by Type"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            size="small"
            className="w-48"
          >
            <MenuItem value="all">All Records</MenuItem>
            <MenuItem value="vaccination">Vaccinations</MenuItem>
            <MenuItem value="treatment">Treatments</MenuItem>
            <MenuItem value="checkup">Check-ups</MenuItem>
          </TextField>
          <TextField
            placeholder="Search records..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
            InputProps={{
              startAdornment: <Search color="action" />,
            }}
          />
          </div>
        </div>
      )}

      {/* Data Grid */}
      <Card>
        <DataGrid
          rows={filteredRecords}
          columns={columns}
          getRowId={(row) => row._id || row.id}
          initialState={{
            pagination: {
              paginationModel: { pageSize: limit || 10, page: 0 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          checkboxSelection
          disableRowSelectionOnClick
          autoHeight
          loading={loading}
          className="p-4"
          components={{
            LoadingOverlay: () => (
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                <CircularProgress />
              </div>
            ),
          }}
        />
      </Card>

      {/* View All Button */}
      {showViewAll && limit && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <CustomButton
            variant="outlined"
            color="primary"
            onClick={() => navigate('/health/records')}
            endIcon={<ArrowForward />}
          >
            View All Records
          </CustomButton>
        </Box>
      )}

      {/* Health Record Modal */}
      <HealthRecordModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        record={selectedRecord}
        onSave={handleSaveRecord}
      />
    </motion.div>
  );
};

export default HealthRecords;
