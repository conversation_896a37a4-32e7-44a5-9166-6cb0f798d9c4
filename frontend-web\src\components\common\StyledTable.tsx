import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  TableContainer,
  alpha,
  Box,
  Typography,
  useTheme
} from '@mui/material';
import { useThemeContext } from '../../contexts/ThemeContext';
import { isExcludedModule } from '../../utils/applyDesignPattern';

interface Column {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'right' | 'center';
  format?: (value: any, row?: any) => React.ReactNode;
}

interface StyledTableProps {
  columns: Column[];
  rows: any[];
  title?: string;
  subtitle?: string;
  keyField?: string;
  maxHeight?: number | string;
  onRowClick?: (row: any) => void;
  emptyMessage?: string;
  stickyHeader?: boolean;
  module?: string; // Added module prop to apply consistent styling
}

const StyledTable: React.FC<StyledTableProps> = ({
  columns,
  rows,
  title,
  subtitle,
  keyField = 'id',
  maxHeight,
  onRowClick,
  emptyMessage = 'No data available',
  stickyHeader = false,
  module = 'animals'
}) => {
  const theme = useTheme();
  const { currentColor, availableColors } = useThemeContext();
  const themeColor = availableColors[currentColor];

  // Apply metallic blue color scheme for non-excluded modules
  const isExcluded = isExcludedModule(module);
  const METALLIC_BLUE = {
    primary: '#4A6FA5',
    secondary: '#3A5A8C',
  };

  // Use metallic blue for non-excluded modules
  const moduleColor = isExcluded ? themeColor : {
    primary: METALLIC_BLUE.primary,
    secondary: METALLIC_BLUE.secondary
  };

  return (
    <Box>
      {(title || subtitle) && (
        <Box sx={{ mb: 2 }}>
          {title && (
            <Typography variant="h6" fontWeight="bold">
              {title}
            </Typography>
          )}
          {subtitle && (
            <Typography variant="body2" color="text.secondary">
              {subtitle}
            </Typography>
          )}
        </Box>
      )}

      <TableContainer
        component={Paper}
        sx={{
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          borderRadius: '12px',
          overflow: 'hidden',
          maxHeight
        }}
      >
        <Table stickyHeader={stickyHeader}>
          <TableHead sx={{ bgcolor: alpha(moduleColor.primary, 0.1) }}>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  align={column.align || 'left'}
                  style={{ minWidth: column.minWidth }}
                  sx={{
                    fontWeight: 'bold',
                    color: moduleColor.secondary,
                    whiteSpace: 'nowrap',
                    fontSize: '1rem' // Larger font for better accessibility
                  }}
                >
                  {column.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.length > 0 ? (
              rows.map((row) => (
                <TableRow
                  hover
                  key={row[keyField]}
                  onClick={onRowClick ? () => onRowClick(row) : undefined}
                  sx={{
                    cursor: onRowClick ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: alpha(moduleColor.primary, 0.05),
                    },
                    '& td': {
                      borderBottom: `1px solid ${alpha(moduleColor.primary, 0.1)}`,
                      fontSize: '0.95rem', // Slightly larger font for better readability
                      padding: '12px 16px' // More padding for better touch targets
                    }
                  }}
                >
                  {columns.map((column) => {
                    const value = row[column.id];
                    return (
                      <TableCell key={column.id} align={column.align || 'left'}>
                        {column.format ? column.format(value, row) : value}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{ py: 3 }}
                >
                  {emptyMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default StyledTable;
