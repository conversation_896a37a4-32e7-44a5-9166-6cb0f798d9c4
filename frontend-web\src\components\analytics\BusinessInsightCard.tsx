import React from 'react';
import { Box, Typography, useTheme, alpha, Chip } from '@mui/material';
import { motion } from 'framer-motion';
import { LightbulbOutlined } from '@mui/icons-material';
import BusinessAnalyticsCard from './BusinessAnalyticsCard';
import BusinessAnalyticsButton from './BusinessAnalyticsButton';

interface BusinessInsightProps {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  source: string;
  date: string;
  actionable: boolean;
  suggestedAction?: string;
  relatedMetrics?: string[];
}

interface BusinessInsightCardProps {
  insight: BusinessInsightProps;
  onAction?: (insight: BusinessInsightProps) => void;
  loading?: boolean;
  delay?: number;
  icon?: React.ReactNode;
}

/**
 * A card component for displaying business insights
 */
const BusinessInsightCard: React.FC<BusinessInsightCardProps> = ({
  insight,
  onAction,
  loading = false,
  delay = 0,
  icon
}) => {
  const theme = useTheme();

  const getImpactColor = (impact: 'high' | 'medium' | 'low') => {
    switch (impact) {
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const impactColor = getImpactColor(insight.impact);

  return (
    <BusinessAnalyticsCard
      title={insight.title}
      subtitle={`Impact: ${insight.impact.charAt(0).toUpperCase() + insight.impact.slice(1)}`}
      icon={icon || <LightbulbOutlined />}
      loading={loading}
      delay={delay}
      accentColor={impactColor}
      actionLabel={insight.actionable ? "Take Action" : undefined}
      onAction={insight.actionable ? () => onAction && onAction(insight) : undefined}
    >
      <Box sx={{ mt: 1 }}>
        <Typography variant="body1" sx={{ mb: 3 }}>
          {insight.description}
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Category
            </Typography>
            <Chip
              label={insight.category}
              size="small"
              sx={{
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                color: theme.palette.primary.main,
                fontWeight: 'medium'
              }}
            />
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Source
            </Typography>
            <Typography variant="body2">
              {insight.source}
            </Typography>
          </Box>
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Date
            </Typography>
            <Typography variant="body2">
              {insight.date}
            </Typography>
          </Box>
        </Box>

        {insight.actionable && insight.suggestedAction && (
          <Box
            sx={{
              mt: 2,
              p: 2,
              borderRadius: 2,
              backgroundColor: alpha(impactColor, 0.1),
              border: `1px solid ${alpha(impactColor, 0.2)}`
            }}
          >
            <Typography variant="body2" fontWeight="bold" gutterBottom>
              Suggested Action:
            </Typography>
            <Typography variant="body2">
              {insight.suggestedAction}
            </Typography>
          </Box>
        )}

        {insight.relatedMetrics && insight.relatedMetrics.length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Related Metrics
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {insight.relatedMetrics.map((metric, index) => (
                <Chip
                  key={index}
                  label={metric}
                  size="small"
                  sx={{
                    backgroundColor: alpha(theme.palette.secondary.main, 0.1),
                    color: theme.palette.secondary.main
                  }}
                />
              ))}
            </Box>
          </Box>
        )}
      </Box>
    </BusinessAnalyticsCard>
  );
};

export default BusinessInsightCard;
