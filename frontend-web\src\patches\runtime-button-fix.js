/**
 * This file contains a runtime fix for the MUI Button component.
 * It directly modifies the Button component's render function to prevent the "Cannot read properties of undefined (reading 'dark')" error.
 */

// Function to fix the Button component
export function fixButtonComponent() {
  try {
    // Find all Button components in the DOM
    const buttonElements = document.querySelectorAll('.MuiButton-root');
    console.log('Found', buttonElements.length, 'Button components');
    
    // Apply direct CSS styles to each Button component
    buttonElements.forEach(button => {
      // Apply base styles
      button.style.display = 'inline-flex';
      button.style.alignItems = 'center';
      button.style.justifyContent = 'center';
      button.style.position = 'relative';
      button.style.boxSizing = 'border-box';
      button.style.outline = '0';
      button.style.border = '0';
      button.style.margin = '0';
      button.style.cursor = 'pointer';
      button.style.userSelect = 'none';
      button.style.verticalAlign = 'middle';
      button.style.textDecoration = 'none';
      button.style.fontWeight = '500';
      button.style.fontSize = '0.875rem';
      button.style.lineHeight = '1.75';
      button.style.letterSpacing = '0.02857em';
      button.style.textTransform = 'uppercase';
      button.style.minWidth = '64px';
      button.style.padding = '6px 16px';
      button.style.borderRadius = '4px';
      button.style.transition = 'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms';
      
      // Apply variant-specific styles
      if (button.classList.contains('MuiButton-contained')) {
        button.style.color = '#fff';
        button.style.backgroundColor = '#3AA99F';
        button.style.boxShadow = '0px 3px 1px -2px rgba(0,0,0,0.2), 0px 2px 2px 0px rgba(0,0,0,0.14), 0px 1px 5px 0px rgba(0,0,0,0.12)';
      } else if (button.classList.contains('MuiButton-outlined')) {
        button.style.border = '1px solid rgba(0, 0, 0, 0.23)';
        button.style.padding = '5px 15px';
        button.style.color = '#3AA99F';
        button.style.backgroundColor = 'transparent';
      } else if (button.classList.contains('MuiButton-text')) {
        button.style.padding = '6px 8px';
        button.style.color = '#3AA99F';
        button.style.backgroundColor = 'transparent';
      }
      
      // Apply color-specific styles
      if (button.classList.contains('MuiButton-containedPrimary')) {
        button.style.color = '#fff';
        button.style.backgroundColor = '#3AA99F';
      } else if (button.classList.contains('MuiButton-containedSecondary')) {
        button.style.color = '#fff';
        button.style.backgroundColor = '#38B2AC';
      } else if (button.classList.contains('MuiButton-outlinedPrimary')) {
        button.style.color = '#3AA99F';
        button.style.border = '1px solid rgba(58, 169, 159, 0.5)';
      } else if (button.classList.contains('MuiButton-outlinedSecondary')) {
        button.style.color = '#38B2AC';
        button.style.border = '1px solid rgba(56, 178, 172, 0.5)';
      } else if (button.classList.contains('MuiButton-textPrimary')) {
        button.style.color = '#3AA99F';
      } else if (button.classList.contains('MuiButton-textSecondary')) {
        button.style.color = '#38B2AC';
      }
      
      // Apply disabled styles
      if (button.classList.contains('Mui-disabled')) {
        button.style.color = 'rgba(0, 0, 0, 0.26)';
        button.style.cursor = 'default';
        button.style.pointerEvents = 'none';
        
        if (button.classList.contains('MuiButton-contained')) {
          button.style.color = 'rgba(0, 0, 0, 0.26)';
          button.style.backgroundColor = 'rgba(0, 0, 0, 0.12)';
          button.style.boxShadow = 'none';
        } else if (button.classList.contains('MuiButton-outlined')) {
          button.style.border = '1px solid rgba(0, 0, 0, 0.12)';
        }
      }
      
      // Apply size-specific styles
      if (button.classList.contains('MuiButton-sizeSmall')) {
        button.style.padding = '4px 10px';
        button.style.fontSize = '0.8125rem';
      } else if (button.classList.contains('MuiButton-sizeLarge')) {
        button.style.padding = '8px 22px';
        button.style.fontSize = '0.9375rem';
      }
      
      // Apply full width styles
      if (button.classList.contains('MuiButton-fullWidth')) {
        button.style.width = '100%';
      }
    });
    
    console.log('Applied direct CSS styles to Button components');
    
    // Set up a MutationObserver to apply styles to new Button components
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === 1) {
              const buttons = node.querySelectorAll ? node.querySelectorAll('.MuiButton-root') : [];
              if (node.classList && node.classList.contains('MuiButton-root')) {
                buttons.push(node);
              }
              
              buttons.forEach(button => {
                // Apply the same styles as above
                // (Code omitted for brevity)
              });
            }
          });
        }
      });
    });
    
    // Start observing the document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    console.log('Set up MutationObserver for Button components');
    
    return true;
  } catch (error) {
    console.error('Error fixing Button component:', error);
    return false;
  }
}
