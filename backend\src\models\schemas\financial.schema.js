/**
 * Financial Schema
 * 
 * Defines the MongoDB schema for the financial collection
 */

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

// Transaction Schema
const transactionSchema = new Schema({
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  type: {
    type: String,
    enum: ['income', 'expense'],
    required: true
  },
  category: {
    type: String,
    required: true,
    trim: true
  },
  subcategory: {
    type: String,
    trim: true
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'USD',
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'check', 'credit card', 'debit card', 'bank transfer', 'mobile payment', 'other'],
    default: 'cash'
  },
  reference: {
    type: String,
    trim: true
  },
  relatedTo: {
    type: {
      type: String,
      enum: ['animal', 'breeding', 'health', 'feeding', 'equipment', 'facility', 'staff', 'other'],
      default: 'other'
    },
    id: {
      type: Schema.Types.ObjectId,
      refPath: 'relatedTo.type'
    },
    name: {
      type: String,
      trim: true
    }
  },
  attachments: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled', 'reconciled'],
    default: 'completed'
  },
  recordedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Budget Schema
const budgetSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  categories: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      enum: ['income', 'expense'],
      required: true
    },
    budgetedAmount: {
      type: Number,
      required: true,
      min: 0
    },
    actualAmount: {
      type: Number,
      default: 0,
      min: 0
    },
    notes: {
      type: String
    }
  }],
  totalBudgeted: {
    income: {
      type: Number,
      default: 0,
      min: 0
    },
    expense: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  totalActual: {
    income: {
      type: Number,
      default: 0,
      min: 0
    },
    expense: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'closed'],
    default: 'draft'
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Asset Schema
const assetSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  category: {
    type: String,
    enum: ['animal', 'equipment', 'vehicle', 'building', 'land', 'other'],
    default: 'other'
  },
  description: {
    type: String,
    trim: true
  },
  acquisitionDate: {
    type: Date,
    required: true
  },
  acquisitionCost: {
    type: Number,
    required: true,
    min: 0
  },
  currentValue: {
    type: Number,
    min: 0
  },
  lastValuationDate: {
    type: Date
  },
  depreciationMethod: {
    type: String,
    enum: ['straight-line', 'declining-balance', 'units-of-production', 'none'],
    default: 'none'
  },
  usefulLife: {
    type: Number,
    min: 0
  },
  salvageValue: {
    type: Number,
    min: 0
  },
  location: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'sold', 'disposed'],
    default: 'active'
  },
  relatedTo: {
    type: {
      type: String,
      enum: ['animal', 'breeding', 'health', 'feeding', 'equipment', 'facility', 'other'],
      default: 'other'
    },
    id: {
      type: Schema.Types.ObjectId,
      refPath: 'relatedTo.type'
    }
  },
  attachments: [{
    url: {
      type: String,
      required: true
    },
    name: {
      type: String,
      required: true
    },
    type: {
      type: String
    },
    date: {
      type: Date,
      default: Date.now
    }
  }],
  notes: {
    type: String
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Create indexes for common queries
transactionSchema.index({ date: -1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ category: 1 });
transactionSchema.index({ 'relatedTo.id': 1 });
transactionSchema.index({ status: 1 });

budgetSchema.index({ startDate: 1, endDate: 1 });
budgetSchema.index({ status: 1 });

assetSchema.index({ category: 1 });
assetSchema.index({ acquisitionDate: -1 });
assetSchema.index({ status: 1 });
assetSchema.index({ 'relatedTo.id': 1 });

// Pre-save hook to update timestamps
transactionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

budgetSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  // Calculate totals
  let totalIncomeBudgeted = 0;
  let totalExpenseBudgeted = 0;
  let totalIncomeActual = 0;
  let totalExpenseActual = 0;
  
  this.categories.forEach(category => {
    if (category.type === 'income') {
      totalIncomeBudgeted += category.budgetedAmount;
      totalIncomeActual += category.actualAmount;
    } else {
      totalExpenseBudgeted += category.budgetedAmount;
      totalExpenseActual += category.actualAmount;
    }
  });
  
  this.totalBudgeted.income = totalIncomeBudgeted;
  this.totalBudgeted.expense = totalExpenseBudgeted;
  this.totalActual.income = totalIncomeActual;
  this.totalActual.expense = totalExpenseActual;
  
  next();
});

assetSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Create the models
const Transaction = mongoose.model('Transaction', transactionSchema);
const Budget = mongoose.model('Budget', budgetSchema);
const Asset = mongoose.model('Asset', assetSchema);

module.exports = {
  Transaction,
  Budget,
  Asset
};
