import React from 'react';
import { Card, CardContent, CardHeader, Typography, Box, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import { isExcludedModule } from '../../utils/applyDesignPattern';

export interface EnhancedCardProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  action?: React.ReactNode;
  variant?: 'default' | 'outlined' | 'glass';
  accentColor?: string;
  delay?: number;
  module?: string; // Added module prop to apply consistent styling
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  children,
  title,
  subtitle,
  action,
  variant = 'default',
  accentColor,
  delay = 0,
  module = 'animals'
}) => {
  const theme = useTheme();

  // Apply metallic blue color scheme for non-excluded modules
  const isExcluded = isExcludedModule(module);
  const METALLIC_BLUE = {
    primary: '#4A6FA5',
    secondary: '#3A5A8C',
  };

  // Use metallic blue for non-excluded modules or the provided accent color
  const primaryColor = accentColor || (isExcluded ? theme.palette.primary.main : METALLIC_BLUE.primary);

  // Card styles based on variant
  let cardStyles = {};

  if (variant === 'outlined') {
    cardStyles = {
      border: `1px solid ${alpha(primaryColor, 0.2)}`,
      boxShadow: '0 4px 12px rgba(0,0,0,0.03)',
      borderRadius: '16px',
      height: '100%',
      backgroundColor: alpha(primaryColor, 0.05),
      transition: 'all 0.3s ease-in-out',
      '&:hover': {
        boxShadow: '0 8px 24px rgba(0,0,0,0.06)',
        transform: 'translateY(-2px)',
        borderColor: alpha(primaryColor, 0.3)
      },
      ...(accentColor && {
        borderTop: `4px solid ${accentColor}`
      })
    };
  } else if (variant === 'glass') {
    cardStyles = {
      background: alpha(primaryColor, 0.08),
      borderRadius: '16px',
      boxShadow: '0 6px 24px rgba(0,0,0,0.06)',
      height: '100%',
      transition: 'all 0.3s ease-in-out',
      backdropFilter: 'blur(8px)',
      '&:hover': {
        boxShadow: '0 10px 32px rgba(0,0,0,0.1)',
        transform: 'translateY(-3px)',
        background: alpha(primaryColor, 0.1)
      },
      ...(accentColor && {
        borderLeft: `4px solid ${accentColor}`
      })
    };
  } else {
    cardStyles = {
      boxShadow: '0 6px 24px rgba(0,0,0,0.06)',
      borderRadius: '16px',
      height: '100%',
      backgroundColor: alpha(primaryColor, 0.03),
      transition: 'all 0.3s ease-in-out',
      border: `1px solid ${alpha(primaryColor, 0.1)}`,
      '&:hover': {
        boxShadow: '0 10px 32px rgba(0,0,0,0.1)',
        transform: 'translateY(-3px)',
        borderColor: alpha(primaryColor, 0.2)
      },
      ...(accentColor && {
        borderBottom: `4px solid ${accentColor}`
      })
    };
  }

  // Header styles
  const headerStyles = {
    pb: 0,
    pt: 2.5,
    px: 3,
    '& .MuiCardHeader-title': {
      fontSize: '1.4rem',
      color: primaryColor,
      fontWeight: 'bold'
    },
    '& .MuiCardHeader-subheader': {
      fontSize: '1rem',
      marginTop: 0.5
    }
  };

  // Content styles
  const contentStyles = {
    height: !title ? '100%' : 'auto',
    p: 3,
    '&:last-child': {
      paddingBottom: 3
    },
    fontSize: '0.95rem' // Slightly larger font for better readability
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay }}
    >
      <Card sx={cardStyles}>
        {title && (
          <CardHeader
            title={<Typography variant="h6" fontWeight="bold">{title}</Typography>}
            subheader={subtitle && <Typography variant="body2" color="text.secondary">{subtitle}</Typography>}
            action={action}
            sx={headerStyles}
          />
        )}
        <CardContent sx={contentStyles}>
          <Box sx={{ height: '100%' }}>
            {children}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EnhancedCard;
